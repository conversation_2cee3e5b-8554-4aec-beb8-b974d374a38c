<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wifochina</groupId>
    <artifactId>ems-server</artifactId>
    <version>0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>ems-server</name>
    <description>大储能控制系统</description>

    <modules>
        <module>ems-common</module>
        <module>ems-site</module>
        <module>ems-cloud</module>
        <module>ems-income</module>
        <module>services</module>
        <module>services/realtime-service</module>
        <module>services/realtime-model</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.13</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <!-- 版 本 管 理 -->
    <properties>
        <!--        <java.version>11</java.version>-->
        <!--        <maven.compiler.source>11</maven.compiler.source>-->
        <!--        <maven.compiler.target>11</maven.compiler.target>-->
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <common.pool.version>2.11.1</common.pool.version>
        <fastjson.version>2.0.34</fastjson.version>
        <mybatis.plus.version>3.5.3.1</mybatis.plus.version>
        <mysql.version>8.0.28</mysql.version>
        <hutool.version>5.8.20</hutool.version>
        <jwt.version>0.9.1</jwt.version>
        <druid.version>1.2.20</druid.version>
        <velocity.version>2.3</velocity.version>
        <knife4j.version>2.0.4</knife4j.version>
        <guava.version>30.0-android</guava.version>
        <spring.boot.version>2.6.13</spring.boot.version>
        <!-- for jdk17 1.18.26 -->
        <!--        <lombok.version>1.18.34</lombok.version>-->
        <lombok.version>1.18.24</lombok.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <kotlin.version>1.9.0</kotlin.version>
        <chat-core-verison>8.0.0.5-SNAPSHOT</chat-core-verison>
        <alert.api.version>1.0.2-SNAPSHOT</alert.api.version>
        <aliyun.sdk.oss.version>3.16.0</aliyun.sdk.oss.version>
    </properties>

    <dependencies>
        <!--  boot  -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 参 数 验 证 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!-- 常 用 工 具 类 -->
        <!-- InfluxDB need kotlin-->
        <!--        <dependency>-->
        <!--            <groupId>org.jetbrains.kotlin</groupId>-->
        <!--            <artifactId>kotlin-stdlib</artifactId>-->
        <!--            <version>1.3.61</version>-->
        <!--        </dependency>-->
        <!-- jaxb依赖包 -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>
        <!-- 数 据 库 连 接 工 具 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
            <scope>runtime</scope>
        </dependency>
        <!-- 实 体 简 化 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <!--MapStruct start  需要在 lombok 后面引入 或者 maven plugin 指定编译顺序  -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <!-- 常 用 工 具 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${common.pool.version}</version>
        </dependency>
        <!-- 序 列 化 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <!--            <version>2.18.2</version>-->
        </dependency>
        <!-- TOKEN 处 理 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jwt.version}</version>
        </dependency>

        <!-- 数 据 库 常 用 工 具 -->
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>3.9.1</version>
        </dependency>
        <!-- 数 据 库 连 接 池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.mybatis</groupId>-->
        <!--            <artifactId>mybatis-typehandlers-jsr310</artifactId>-->
        <!--            <version>1.0.2</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <!-- 验 证 码 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <!-- sms -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-dysmsapi20170525</artifactId>
            <version>1.0.1</version>
        </dependency>
        <!-- prometheus-actuator -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <!-- 代 码 生 成 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.30</version>
        </dependency>
        <!--influx
        client-->
        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>influxdb-client-java</artifactId>
            <!--            <version>${influxdb-java.version}</version>-->
            <!--            <version>6.10.0</version>-->
        </dependency>
        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>flux-dsl</artifactId>
            <version>6.10.0</version>
        </dependency>
        <dependency>
            <artifactId>okhttp</artifactId>
            <groupId>com.squareup.okhttp3</groupId>
            <version>3.14.9</version>
        </dependency>

        <!--kotlin 协程 这个可以没有 如果不用kotlin协程的话-->
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
        </dependency>

        <!-- kotlin 日志打印  因为用不了lombok的 @sl4j注解了-->
        <dependency>
            <groupId>io.github.oshai</groupId>
            <artifactId>kotlin-logging-jvm</artifactId>
            <version>5.1.0</version>
            <exclusions>
                <!--这个里面有个低版本kotlin版本库 移出-->
                <exclusion>
                    <artifactId>kotlin-stdlib</artifactId>
                    <groupId>org.jetbrains.kotlin</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- 数 据 库 操 作 框 架 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>2.6.13</version>
            </dependency>

            <!-- 官方 Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2021.0.5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--            2025-03-14 15:54:54 add support future use spring cloud -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2021.0.5.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin.version}</version>
            </dependency>


            <dependency>
                <groupId>com.aliyun.lindorm</groupId>
                <artifactId>lindorm-tsdb-client</artifactId>
                <version>1.0.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>kotlin-stdlib-common</artifactId>
                        <groupId>org.jetbrains.kotlin</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-java</artifactId>
                <version>5.0.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kotlin-stdlib-common</artifactId>
                        <groupId>org.jetbrains.kotlin</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>okhttp</artifactId>
                        <groupId>com.squareup.okhttp3</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.weihengtech.chat</groupId>
                <artifactId>chat-core</artifactId>
                <version>${chat-core-verison}</version>
            </dependency>

            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>5.2.4</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.6.13</version>
            </dependency>

            <dependency>
                <groupId>com.weihengtech.alert</groupId>
                <artifactId>alert-api-client</artifactId>
                <version>${alert.api.version}</version>
                <classifier>plain</classifier>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.sdk.oss.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>

            <!--集成kotlin插件-->
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <configuration>
                    <jvmTarget>${java.version}</jvmTarget>
                    <compilerPlugins>
                        <!-- Or "jpa" for JPA support 我这里没加jpa支持 是因为我的jpa实体类都是java写的通过自动代码生成出来的 没有去改,所以不需要 ,否则需要 -->
                        <plugin>no-arg</plugin>
                        <plugin>spring</plugin>
                        <!--                        https://kotlinlang.org/docs/lombok.html 文档说明需要加 iot没加不知道为啥-->
                        <plugin>lombok</plugin>
                    </compilerPlugins>
                    <sourceDirs>
                        <sourceDir>src/main/kotlin</sourceDir>
                    </sourceDirs>
                    <pluginOptions>
                        <option>no-arg:annotation=com.wifochina.common.NoArg</option>
                        <!-- Call instance initializers in the synthetic constructor -->
                        <!-- <option>no-arg:invokeInitializers=true</option> -->
                    </pluginOptions>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                        <version>${lombok.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-lombok</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>

                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <!--                        <phase>process-sources</phase>-->
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>src/main/kotlin</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <!--                        <phase>process-test-sources</phase>-->
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>src/main/kotlin</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>kapt</id>
                        <goals>
                            <goal>kapt</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>src/main/kotlin</sourceDir>
                                <sourceDir>src/main/java</sourceDir>
                            </sourceDirs>
                            <annotationProcessorPaths>
                                <annotationProcessorPath>
                                    <groupId>org.projectlombok</groupId>
                                    <artifactId>lombok</artifactId>
                                    <version>${lombok.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>org.mapstruct</groupId>
                                    <artifactId>mapstruct-processor</artifactId>
                                    <version>${org.mapstruct.version}</version>
                                </annotationProcessorPath>
                            </annotationProcessorPaths>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>

                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <release>${java.version}</release>
                    <encoding>utf-8</encoding>

                    <annotationProcessorPaths>
<!--                        <path>-->
<!--                            <groupId>org.mapstruct</groupId>-->
<!--                            <artifactId>mapstruct-processor</artifactId>-->
<!--                            <version>${org.mapstruct.version}</version>-->
<!--                        </path>-->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <!-- 打包时跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>


        </plugins>
    </build>

</project>
