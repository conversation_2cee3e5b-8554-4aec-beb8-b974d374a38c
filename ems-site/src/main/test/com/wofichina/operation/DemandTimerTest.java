package com.wofichina.operation;

import com.wifochina.InfluxApplication;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.util.Pair;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-30 5:04 PM
 */
@SpringBootTest(classes = InfluxApplication.class)
@Slf4j
public class DemandTimerTest {

    @Resource private ProjectService projectService;

    @Resource private StrategyService strategyService;

    @Resource private GroupService groupService;

    @Resource private DemandService demandService;

    @Test
    public void calcDemand() {
        ProjectEntity projectEntity = projectService.getById("4f537620d37d40e19dd25be5ca6ad941");
        List<GroupEntity> groupEntities =
                groupService
                        .lambdaQuery()
                        .eq(GroupEntity::getProjectId, projectEntity.getId())
                        .eq(GroupEntity::getDemandController, true)
                        .list();
        // .eq(GroupEntity::getCalcEarningsController, true).eq(GroupEntity::getDemandIncome,
        // true).list();
        if (!groupEntities.isEmpty()) {
            for (GroupEntity groupEntity : groupEntities) {
                StrategyEntity strategyEntity =
                        strategyService
                                .lambdaQuery()
                                .eq(StrategyEntity::getGroupId, groupEntity.getId())
                                .eq(StrategyEntity::getWeekDay, 0)
                                .one();
                Long start = projectEntity.getCreateTime();
                for (int i = 0; ; i++) {
                    long time = start + (long) EmsConstants.ONE_DAY_SECOND * i;
                    if (time >= MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone())) {
                        break;
                    }
                    Map<Long, Pair<Double, Double>> controlMap =
                            demandService.getOriginAndActualDemand(
                                    "mean",
                                    time,
                                    time + EmsConstants.ONE_DAY_SECOND,
                                    projectEntity.getTimezone(),
                                    groupEntity);
                    demandService.saveOriginAndActualDemandToDb(
                            strategyEntity.getGridControlPower(), controlMap, groupEntity);
                    log.info(
                            "{} calc in {}",
                            groupEntity.getName(),
                            LocalDateTime.ofEpochSecond(
                                    time,
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())));
                }
            }
        }
    }
}
