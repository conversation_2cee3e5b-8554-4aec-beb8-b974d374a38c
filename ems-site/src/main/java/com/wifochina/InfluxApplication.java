package com.wifochina;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

/**
 * EnableCaching 仅仅在oauth2客户端基于数据的缓存用到，见DatabaseCachableClientDetailsService
 *
 * @since 2022/3/13 1:09
 * <AUTHOR>
 * @version 1.0
 */
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableFeignClients
public class InfluxApplication {

    static {
        System.setProperty("druid.mysql.usePingMethod", "false");
    }

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(InfluxApplication.class);
    }
}
