package com.wifochina.modules.user.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.handler.MessageSourceHandler;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.oauth.util.JwtHelper;
import com.wifochina.modules.oauth.util.JwtUserInfo;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.user.entity.LogEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.entity.UserProjectEntity;
import com.wifochina.modules.user.request.*;
import com.wifochina.modules.user.service.LogService;
import com.wifochina.modules.user.service.UserProjectService;
import com.wifochina.modules.user.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.NoSuchMessageException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Api(value = "user", tags = "17-用户管理")
@RestController
@RequestMapping("/user")
@Slf4j
@RequiredArgsConstructor
public class OperationSiteLogController {

    private final UserService userService;

    private final UserProjectService userProjectService;

    private final ProjectService projectService;

    private final LogService logService;

    private final JwtHelper jwtHelper;

    @GetMapping("/addLocalUser")
    @ApiOperation("添加本地用户")
    @Log(module = "USER_MANAGE", methods = "USER_MANAGE_ADD")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addLocalUser() {
        HttpServletRequest request =
                ((ServletRequestAttributes)
                        Objects.requireNonNull(RequestContextHolder.getRequestAttributes()))
                        .getRequest();
        String token = request.getHeader("Authorization");
        String authHeader = "bearer";
        // parse token
        if (token == null) {
            return Result.failure(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        } else if (token.startsWith(authHeader)) {
            token = token.substring(6).trim();
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getJwtFromToken(token);
        UserEntity userEntity = jwtUserInfo.getAuthUser().getUserEntity();
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        if (projectEntity == null) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ACCESS.value());
        }
        UserEntity temUser =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getUserName, userEntity.getUserName())
                        .one();
        if (temUser == null) {
            userService.save(userEntity);
        }
        UserProjectEntity temUserProject =
                userProjectService
                        .lambdaQuery()
                        .eq(UserProjectEntity::getProjectId, projectEntity.getId())
                        .eq(UserProjectEntity::getUserId, userEntity.getId())
                        .one();
        if (temUserProject == null) {
            UserProjectEntity userProjectEntity = new UserProjectEntity();
            userProjectEntity.setProjectId(WebUtils.projectId.get());
            userProjectEntity.setUserId(userEntity.getId());
            userProjectService.save(userProjectEntity);
        }
        return Result.success();
    }

    @PostMapping("/getOnePageLogs")
    @ApiOperation("查询一页操作记录")
    @PreAuthorize("hasAuthority('/user/log')")
    public Result<IPage<LogEntity>> getOnePageLogs(
            @RequestBody LogNamePageRequest logNamePageRequest) {
        IPage<LogEntity> resultPage = queryLogs(logNamePageRequest);
        if (resultPage != null && CollUtil.isNotEmpty(resultPage.getRecords())) {
            resultPage.getRecords().forEach(i -> i.setDetail(null));
        }
        return Result.success(resultPage);
    }

    @PostMapping("/getOnePageLogs/detail")
    @ApiOperation("查询一页操作记录(明细)")
    @PreAuthorize("hasAuthority('/user/log/detail')")
    public Result<IPage<LogEntity>> getOnePageLogsDetail(
            @RequestBody LogNamePageRequest logNamePageRequest) {
        IPage<LogEntity> list = queryLogs(logNamePageRequest);
        return Result.success(list);
    }

    private IPage<LogEntity> queryLogs(LogNamePageRequest logNamePageRequest) {
        // 查找分配给该项目的用户
        List<UserProjectEntity> userProjectEntities =
                userProjectService.list(
                        Wrappers.lambdaQuery(UserProjectEntity.class)
                                .eq(UserProjectEntity::getProjectId, WebUtils.projectId.get()));
        // 用户id
        Set<String> userIds =
                userProjectEntities.stream()
                        .map(UserProjectEntity::getUserId)
                        .collect(Collectors.toSet());
        // 当前登录用户
        UserEntity userEntity = Objects.requireNonNull(SecurityUtil.getPrincipal()).getUserEntity();
        // 查询all用户
        List<UserEntity> allUserList =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getAllProject, true)
                        .orderByDesc(UserEntity::getCreateTime)
                        .list();
        Set<String> allUserIds =
                allUserList.stream().map(UserEntity::getId).collect(Collectors.toSet());
        Set<String> queryUserIds;
        if (EmsConstants.WH_ADMIN_USERNAME.equals(userEntity.getUserName())) {
            Set<String> superUser = new HashSet<>();
            superUser.add("1");
            // 用户合并(查询所有项目的用户+分配给这个用户)
            superUser.addAll(allUserIds);
            superUser.addAll(userIds);
            queryUserIds = superUser;
        } else {
            // AD用户看ALL用户和其它用户
            if (EmsConstants.USER_ROLE_AD.equals(userEntity.getRole())) {
                // 用户合并(查询所有项目的用户+分配给这个用户)
                allUserIds.addAll(userIds);
                queryUserIds = allUserIds;
            } else {
                // admin或者client 只看admin和client
                queryUserIds = userIds;
            }
        }
        List<UserEntity> conditionUsers =
                userService
                        .lambdaQuery()
                        .ne(
                                EmsConstants.ADMIN_USERNAME.equals(userEntity.getRole())
                                        || EmsConstants.USER_ROLE_REGISTER.equals(
                                                userEntity.getRole())
                                        || EmsConstants.USER_ROLE_CLIENT.equals(
                                                userEntity.getRole()),
                                UserEntity::getRole,
                                EmsConstants.USER_ROLE_AD)
                        .like(
                                StringUtils.hasLength(logNamePageRequest.getName()),
                                UserEntity::getUserName,
                                logNamePageRequest.getName())
                        .or()
                        .like(
                                StringUtils.hasLength(logNamePageRequest.getName()),
                                UserEntity::getName,
                                logNamePageRequest.getName())
                        .list();
        Set<String> conditionUserIds =
                conditionUsers.stream().map(UserEntity::getId).collect(Collectors.toSet());
        if (conditionUserIds.isEmpty()) {
            return null;
        }
        conditionUserIds.retainAll(queryUserIds);
        Map<String, String> conditionNameMap =
                conditionUsers.stream()
                        .collect(
                                Collectors.toMap(
                                        UserEntity::getId,
                                        e ->
                                                !StringUtils.hasLength(e.getName())
                                                        ? e.getUserName()
                                                        : e.getName()));

        IPage<LogEntity> logPage =
                new Page<>(logNamePageRequest.getPageNum(), logNamePageRequest.getPageSize());
        IPage<LogEntity> list =
                logService.page(
                        logPage,
                        new QueryWrapper<LogEntity>()
                                .lambda()
                                .eq(LogEntity::getProjectId, WebUtils.projectId.get())
                                .ge(
                                        logNamePageRequest.getStart() != null,
                                        LogEntity::getRequestTime,
                                        logNamePageRequest.getStart())
                                .le(
                                        logNamePageRequest.getEnd() != null,
                                        LogEntity::getRequestTime,
                                        logNamePageRequest.getEnd())
                                .in(LogEntity::getUserId, conditionUserIds)
                                .orderByDesc(LogEntity::getRequestTime));
        list.getRecords()
                .forEach(
                        logEntity -> {
                            try {
                                logEntity.setModule(
                                        MessageSourceHandler.getMessage(logEntity.getModule()));
                                logEntity.setMethod(
                                        MessageSourceHandler.getMessage(logEntity.getMethod()));
                                logEntity.setName(conditionNameMap.get(logEntity.getUserId()));
                            } catch (NoSuchMessageException e) {
                                log.info(
                                        "[language error]----------------------------->"
                                                + e.getMessage());
                            }
                        });
        return list;
    }
}
