package com.wifochina.modules.remedies.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.page.Result;
import com.wifochina.modules.remedies.request.CheckDataRequest;
import com.wifochina.modules.remedies.timer.InfluxRemediesTimer;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @since 2023-08-25 4:16 PM
 */

@Api(tags = "24-补充数据")
@RequestMapping("/remedies")
@RestController
public class CheckDataController {

    @Resource
    private InfluxRemediesTimer influxRemediesTimer;

    @PostMapping("/checkDataRecover")
    @ApiOperation("恢复检查数据任务")
    public Result<Object> checkDataRecover(@RequestBody CheckDataRequest checkDataRequest) {
        influxRemediesTimer.checkDataRecover(checkDataRequest.getTime());
        return Result.success();
    }
}
