package com.wifochina.modules.remedies.timer;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.data.influxdb.client.InfluxClient;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.entity.EmsDataCountEntity;
import com.wifochina.modules.remedies.service.EmsDataCountService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023-08-07 11:11 AM
 */
@Component
@Slf4j
public class InfluxRemediesTimer {
    @Resource
    private InfluxClient influxClient;

    @Resource
    private ProjectService projectService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private EmsDataCountService emsDataCountService;

    @Value("${ems.standPointCount}")
    private String standPointCount;

    private static final Map<String, Long> CHECKD_PROJECCT_MAP = new HashMap<>(50);

    @Scheduled(cron = "0 */30 * * * ?")
    @Async("asyncServiceExecutor")
    public void checkData() {
        ProjectEntity projectEntity = projectService.lambdaQuery().one();
        long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        if (CHECKD_PROJECCT_MAP.get(projectEntity.getId()) != null && todayZero == CHECKD_PROJECCT_MAP.get(projectEntity.getId())) {
            return;
        }
        addOneProjectData(Integer.parseInt(standPointCount), todayZero, projectEntity);
        CHECKD_PROJECCT_MAP.put(projectEntity.getId(), todayZero);
    }

    private void addOneProjectData(int stand, long end, ProjectEntity projectEntity) {
        long start = end - EmsConstants.ONE_DAY_SECOND;
        EmsDataCountEntity emsDataCountEntityExist = emsDataCountService.lambdaQuery()
                .eq(EmsDataCountEntity::getTime, start).eq(EmsDataCountEntity::getProjectId, projectEntity.getId()).one();
        if (emsDataCountEntityExist == null) {
            return;
        }
        String realQueryString = " from(bucket: \"ems\")\n" + "        |> range(start: {start}, stop: {end} )\n"
                + "        |> filter(fn: (r) => r._measurement == \"T1_5s\")\n"
                + "        |> filter(fn: (r) => r[\"_field\"] == \"ems_history_output_energy\"  )\n"
                + "        |> count()\n" + "        |> group()\n" + "        |> sum()";
        Long deviceCount = deviceService.lambdaQuery().eq(DeviceEntity::getProjectId, projectEntity.getId())
                .eq(DeviceEntity::getUnreal, false).count();
        realQueryString =
                realQueryString.replace("{start}", String.valueOf(start)).replace("{end}", String.valueOf(end));
        List<FluxTable> tables = influxClient.queryApi.query(realQueryString);
        double dataCount = 0;
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                Double value = (Double) record.getValueByKey("_value");
                if (value != null) {
                    dataCount = value;
                }
            }
        }
        // insert data status
        if (stand * deviceCount > dataCount) {
            EmsDataCountEntity emsDataCountEntity = new EmsDataCountEntity();
            emsDataCountEntity.setDataCount((int) dataCount);
            emsDataCountEntity.setState(false);
            emsDataCountEntity.setTime(start);
            emsDataCountEntity.setProjectId(projectEntity.getId());
            emsDataCountEntity.setEmsCount(Math.toIntExact(deviceCount));
            try {
                emsDataCountService.save(emsDataCountEntity);
            } catch (Exception e) {
                log.error("already insert");
            }
        }
    }

    public void checkDataRecover(Long time) {
        int finalStand = Integer.parseInt(standPointCount);
        Optional.ofNullable(time).ifPresent(t -> {
            t = t + EmsConstants.ONE_DAY_SECOND;
            ProjectEntity projectEntity = projectService.lambdaQuery().one();
            Long finalT = t;
            Optional.ofNullable(projectEntity).ifPresent(p -> addOneProjectData(finalStand, finalT, p));
        });
    }
}
