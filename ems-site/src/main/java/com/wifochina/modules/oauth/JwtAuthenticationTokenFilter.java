package com.wifochina.modules.oauth;

import com.alibaba.fastjson.JSON;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.oauth.config.MySecurityConfig;
import com.wifochina.modules.oauth.service.impl.UserDetailsServiceImpl;
import com.wifochina.modules.oauth.util.JwtHelper;
import com.wifochina.modules.oauth.util.JwtUserInfo;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.entity.UserEntity;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 6/27/2022 11:17 AM
 */
@Component
@Slf4j
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter
        implements IJwtAuthenticationTokenFilter {

    private RedisTemplate<String, String> redisTemplate;
    private JwtHelper jwtHelper;
    private UserDetailsServiceImpl userDetailsServiceImpl;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setJwtHelper(JwtHelper jwtHelper) {
        this.jwtHelper = jwtHelper;
    }

    @Autowired
    @Lazy // 解决循环依赖
    public void setUserDetailsServiceImpl(UserDetailsServiceImpl userDetailsServiceImpl) {
        this.userDetailsServiceImpl = userDetailsServiceImpl;
    }

    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            @NotNull HttpServletResponse response,
            @NotNull FilterChain filterChain)
            throws ServletException, IOException {
        // get token
        String token = request.getHeader(EmsConstants.AUTHORIZATION_HEADER);
        String authHeader = EmsConstants.BEARER;
        // parse token
        if (token == null
                || MySecurityConfig.NO_AUTH_URLS.contains(request.getRequestURI().substring(4))) {
            filterChain.doFilter(request, response);
            return;
        } else if (token.startsWith(authHeader)) {
            token = token.substring(6).trim();
        }
        if (JwtHelper.illegalTokenSet.contains(token)) {
            sendErrorMessage(response);
            return;
        }
        // get user info from redis
        // TODO  需要取出来 userName 和roleId 做场站的用户查询跳转
        JwtUserInfo jwtUserInfo = jwtHelper.getJwtFromToken(token);
        if (jwtUserInfo == null) {
            sendErrorMessage(response);
            return;
        }
        String userId = jwtUserInfo.getUserId();
        AuthUser loginUser = jwtUserInfo.getAuthUser();
        if (loginUser == null) {
            // init
            loginUser = JSON.parseObject(redisTemplate.opsForValue().get(userId), AuthUser.class);
            if (loginUser == null) {
                UserEntity userEntity = userDetailsServiceImpl.getUserEntityByUserId(userId);
                if (userEntity == null) {
                    sendErrorMessage(response);
                    return;
                }
                loginUser = userDetailsServiceImpl.loadUserByUsername(userEntity.getUserName());
                redisTemplate.opsForValue().set(userId, JSON.toJSONString(loginUser));
            }
        }
        String projectId = request.getHeader(EmsConstants.PROJECT_ID_O);
        WebUtils.projectId.set(projectId);
        // store user info to the SecurityContext
        UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken =
                new UsernamePasswordAuthenticationToken(
                        loginUser, null, loginUser.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
        // go to next filter chain
        filterChain.doFilter(request, response);
    }

    private void sendErrorMessage(HttpServletResponse response) throws IOException {
        // 在这里定义你的异常处理逻辑，比如返回自定义的 HTTP 响应
        Result<Object> resultVo = Result.failure(ErrorResultCode.ILLEGAL_ACCESS.value());
        WebUtils.rendString(response, JSON.toJSONString(resultVo));
    }
}
