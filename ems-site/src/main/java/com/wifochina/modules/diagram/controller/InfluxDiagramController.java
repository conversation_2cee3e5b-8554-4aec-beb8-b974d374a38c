package com.wifochina.modules.diagram.controller;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.page.Result;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.BatteryRequest;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.oauth.util.WebUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DiagramController
 *
 * <AUTHOR>
 * @version 1.0
 * @date 4/18/2022 6:47 PM
 */
@RequestMapping("/diagram")
@RestController
@Api(tags = "16-历史曲线")
@RequiredArgsConstructor
public class InfluxDiagramController {

    private final DataService dataService;
    private final DeviceService deviceService;
    private final InfluxClientService influxClient;
    private final PointListHolder pointListHolder;

    /** 单体电池电压温度曲线 */
    @PostMapping("/getBatteryRate")
    @ApiOperation("单体电池电压温度曲线")
    @PreAuthorize("hasAuthority('/diagram/getBatteryRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getBatteryRate(
            @RequestBody BatteryRequest batteryRequest) {
        RequestWithDeviceId requestWithDeviceId = new RequestWithDeviceId();
        requestWithDeviceId.setDeviceId(batteryRequest.getDeviceId());
        requestWithDeviceId.setStartDate(batteryRequest.getStartDate());
        requestWithDeviceId.setEndDate(batteryRequest.getEndDate());

        int[] data = dataService.get(batteryRequest.getDeviceId());
        // the number of cluster selected
        int cluster = batteryRequest.getBms_cluster_count();
        // the number of stack selected
        int stack = batteryRequest.getBms_stack_per_cluster_count();
        // 每组电芯数量
        int cell = data[pointListHolder.getBmsCellNum(data[42])];
        // 每组电芯温度传感器数量
        int cellT = data[pointListHolder.getBmsCellTNum(data[42])];
        List<String> fields = new ArrayList<>(cell);
        for (int k = 0; k < cell; k++) {
            String column = "bms_cell_voltage_" + cluster + "_" + stack + "_" + k;
            fields.add(column);
        }
        for (int k = 0; k < cellT; k++) {
            String column = "bms_cell_temperature_" + cluster + "_" + stack + "_" + k;
            fields.add(column);
        }
        // Map<String, Map<String, List<ValueVO>>> socRateMap =
        // diagramService.getRate("forever", requestWithDeviceId, fields, null,
        // "T2_1m");
        //
        // influxClient.getEmsTable(WebUtils.projectId.get()),

        // TODO refactor 这里需要再看一下 并且 把这个 measurement 处理一下 , 没仔细看
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithDeviceId, holder);
        if (requestWithDeviceId.getPeriod() != null) {
            holder.setPeriod(Long.valueOf(requestWithDeviceId.getPeriod()));
        }
        Map<String, Map<String, List<ValueVO>>> socRateMap =
                EquipmentTimeSeriesUtils.rateQueryEngine
                        .getRateMap(
                                influxClient.getBucketForever(),
                                "T2_1m",
                                holder,
                                fields,
                                () ->
                                        deviceService.deviceIdsPrepare(
                                                WebUtils.projectId.get(),
                                                requestWithDeviceId.getDeviceId()))
                        .getMap();

        Map<String, List<ValueVO>> columnMap = socRateMap.get(batteryRequest.getDeviceId());
        Map<String, Map<String, List<ValueVO>>> result = new HashMap<>(columnMap.size());
        result.put("bms_cell_temperature", columnMap);
        return Result.success(result);
    }

    /** 单体电池电压温度曲线 */
    @PostMapping("/getBatteryRateWhoStation")
    @ApiOperation("整站电池电压温度曲线")
    @PreAuthorize("hasAuthority('/diagram/getBatteryRateWhoStation')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getBatteryRateWhoStation(
            @RequestBody BatteryRequest batteryRequest) {
        RequestWithDeviceId requestWithDeviceId = new RequestWithDeviceId();
        requestWithDeviceId.setDeviceId(batteryRequest.getDeviceId());
        requestWithDeviceId.setStartDate(batteryRequest.getStartDate());
        requestWithDeviceId.setEndDate(batteryRequest.getEndDate());

        // 这里控制一下 这个整站太小会查询不出来
        long period = 10;
        if (requestWithDeviceId.getPeriod() != null) {
            period = requestWithDeviceId.getPeriod();
        }
        Flux flux =
                Flux.from(influxClient.getBucketForever())
                        .range(requestWithDeviceId.getStartDate(), requestWithDeviceId.getEndDate())
                        .filter(Restrictions.measurement().equal("T2_1m"))
                        .filter(
                                Restrictions.tag("projectId")
                                        .equal(requestWithDeviceId.getProjectId()))
                        .aggregateWindow(period, ChronoUnit.MINUTES, "last")
                        .withCreateEmpty(false);
        String queryString = flux.toString();
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        Map<String, Map<String, List<ValueVO>>> map = new HashMap<>();
        Map<String, List<ValueVO>> dataMap = new HashMap<>();
        for (FluxTable fluxTable : tables) {
            for (FluxRecord record : fluxTable.getRecords()) {

                String field = (String) record.getValueByKey("_field");
                Double value = (Double) record.getValueByKey("_value");
                Instant time = (Instant) record.getValueByKey("_time");
                String deviceId = (String) record.getValueByKey("deviceId");
                String[] parts = field.split("_");
                int len = parts.length;
                if (len >= 3) {
                    // 获取最后三个
                    String cellCluster = parts[len - 3];
                    String cellStack = parts[len - 2];
                    String index = parts[len - 1];
                    assert index != null;
                    ValueVO valueVO = new ValueVO(time.getEpochSecond() - (period * 60) * 2, value);
                    if (field.contains("bms_cell_voltage")) {
                        String id =
                                "bms_cell_voltage_" + cellCluster + "_" + cellStack + "_" + index;
                        dataMap.computeIfAbsent(id, k -> new ArrayList<>()).add(valueVO);
                    } else if (field.contains("bms_cell_temperature")) {
                        String id =
                                "bms_cell_temperature_"
                                        + cellCluster
                                        + "_"
                                        + cellStack
                                        + "_"
                                        + index;
                        dataMap.computeIfAbsent(id, k -> new ArrayList<>()).add(valueVO);
                    }
                    map.put(deviceId, dataMap);
                } else {
                    System.out.println("字符串格式不正确");
                }

                //                assert time != null;
                //                // 这里 * 2 不清楚 因为forever 正常只需要 一倍的
                //                ValueVO valueVO = new ValueVO(time.getEpochSecond() - (period *
                // 60) * 2, value);
                //                String index = (String) record.getValueByKey("cellIndex");
                //                String cellStack = (String) record.getValueByKey("cellStack");
                //                String cellCluster = (String) record.getValueByKey("cellCluster");
                //                assert index != null;
                //                assert field != null;
                //                if (field.contains("bms_cell_voltage")) {
                //                    String id = "bms_cell_voltage_" + cellCluster + "_" +
                // cellStack + "_" + index;
                //                    dataMap.computeIfAbsent(id, k -> new
                // ArrayList<>()).add(valueVO);
                //                } else if (field.contains("bms_cell_temperature")) {
                //                    String id =
                //                            "bms_cell_temperature_" + cellCluster + "_" +
                // cellStack + "_" + index;
                //                    dataMap.computeIfAbsent(id, k -> new
                // ArrayList<>()).add(valueVO);
                //                }
                //                map.put(deviceId, dataMap);
            }
        }
        Map<String, List<ValueVO>> columnMap = map.get(batteryRequest.getDeviceId());
        Map<String, Map<String, List<ValueVO>>> result = new HashMap<>(columnMap.size());
        result.put("bms_cell_temperature", columnMap);
        //        return map;
        return Result.success(result);
    }
}
