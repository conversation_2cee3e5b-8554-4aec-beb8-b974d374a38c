package com.wifochina.modules.investor.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.modules.investor.entity.InvestorEntity;
import com.wifochina.modules.investor.entity.InvestorProjectEntity;
import com.wifochina.modules.user.service.UserService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SiteInvestorProjectImpl extends InvestorProjectServiceImpl {

    @Resource private UserService userService;

    @Override
    public List<String> queryInvestIdsByUserId(String userId) {
        return this.queryInvestorProjectByUserId(userId).stream()
                .map(InvestorProjectEntity::getInvestorId)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvestorEntity> queryInvestorsByUserId(String userId) {
        List<String> investorIds = queryInvestIdsByUserId(userId);
        if (investorIds.isEmpty()) {
            return List.of();
        } else {
            return investorService.lambdaQuery().in(InvestorEntity::getId, investorIds).list();
        }
    }

    @Override
    public List<InvestorProjectEntity> queryInvestorProjectByUserId(String userId) {
        List<String> projectIds = userService.getProjectIdsByUserId(userId);
        if (CollUtil.isNotEmpty(projectIds)) {
            return this.baseMapper.selectList(
                    Wrappers.lambdaQuery(InvestorProjectEntity.class)
                            .in(InvestorProjectEntity::getProjectId, projectIds)
                            .orderByDesc(InvestorProjectEntity::getCreateTime));
        } else {
            return List.of();
        }
    }
}
