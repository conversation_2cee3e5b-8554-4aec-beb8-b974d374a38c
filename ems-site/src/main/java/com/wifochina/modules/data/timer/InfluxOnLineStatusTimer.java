package com.wifochina.modules.data.timer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.group.vo.StatusVo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @since 2024-03-21 3:03 PM
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InfluxOnLineStatusTimer {

    private final ControllerService controllerService;

    private final RestTemplate restTemplate;

    private final RedisTemplate<String, String> redisTemplate;

    private final DeviceService deviceService;

    private final AmmeterService ammeterService;

    private final ControllableService controllableService;

    private final CameraService cameraService;

    private final EventMessageService eventMessageService;

    private static final Map<String, DeviceEntity> deviceEntityMap = new HashMap<>();

    private static final Map<String, AmmeterEntity> ammeterEntityMap = new HashMap<>();

    private static final Map<String, ControllableEntity> controllableEntityMap = new HashMap<>();

    private static final Map<String, CameraEntity> cameraEntityMap = new HashMap<>();

    @Value("${ems.collect.project.id}")
    private String projectId;

    @Async("taskScheduler")
    @Scheduled(initialDelay = 4000, fixedRateString = "${spring.task.online.fixedRate}")
    public void collectOnLineStatusPoint() {
        log.info("online check ---> 设备在线状态检查正在执行");
        List<ControllerEntity> controllerEntities =
                controllerService.list(
                        Wrappers.lambdaQuery(ControllerEntity.class)
                                .eq(ControllerEntity::getProjectId, projectId));
        // 没有任何控制器则直接退出
        if (controllerEntities == null || controllerEntities.isEmpty()) {
            return;
        }
        ControllerEntity controllerEntity = controllerEntities.get(0);
        String url =
                "http://"
                        + controllerEntity.getIp()
                        + ":"
                        + controllerEntity.getPort()
                        + "/api/v1/get_online";
        JSONObject jsonObject;
        try {
            jsonObject = restTemplate.postForObject(url, null, JSONObject.class);
        } catch (Exception e) {
            // 有异常证明数据采集不成功，则不处理
            log.error(
                    "collectOnLineStatusPoint--->设备在线状态数据采集 {}",
                    ErrorResultCode.FAIL_CONNECT_GOCONTROL.value() + " - " + e.getMessage());
            return;
        }
        if (jsonObject == null) {
            return;
        }
        List<StatusVo> emsList =
                jsonObject.getJSONArray("ems") == null
                        ? Collections.emptyList()
                        : jsonObject.getJSONArray("ems").toJavaList(StatusVo.class);
        List<StatusVo> meterList =
                jsonObject.getJSONArray("meter") == null
                        ? Collections.emptyList()
                        : jsonObject.getJSONArray("meter").toJavaList(StatusVo.class);
        List<StatusVo> controllableList =
                jsonObject.getJSONArray("controllable") == null
                        ? Collections.emptyList()
                        : jsonObject.getJSONArray("controllable").toJavaList(StatusVo.class);
        List<StatusVo> cameraList =
                jsonObject.getJSONArray("camera") == null
                        ? Collections.emptyList()
                        : jsonObject.getJSONArray("camera").toJavaList(StatusVo.class);
        List<StatusVo> statusVos = new ArrayList<>();
        statusVos.addAll(emsList);
        statusVos.addAll(meterList);
        statusVos.addAll(controllableList);
        statusVos.addAll(cameraList);
        if (statusVos.isEmpty()) {
            return;
        }
        String jsonStr =
                redisTemplate.opsForValue().get("online:" + controllerEntity.getProjectId());
        List<StatusVo> lastStatusVos = JSON.parseObject(jsonStr, new TypeReference<>() {});
        redisTemplate
                .opsForValue()
                .set(
                        "online:" + controllerEntity.getProjectId(),
                        JSON.toJSONString(statusVos),
                        20,
                        TimeUnit.SECONDS);
        if (lastStatusVos == null || lastStatusVos.isEmpty()) {
            return;
        }
        Map<String, Boolean> lastStatusVoMap =
                lastStatusVos.stream()
                        .collect(Collectors.toMap(StatusVo::getUuid, StatusVo::getOnline));
        saveEmsEvent(emsList, lastStatusVoMap);
        saveMeterEvent(meterList, lastStatusVoMap);
        saveSteerableEvent(controllableList, lastStatusVoMap);
        saveCameraEvent(cameraList, lastStatusVoMap);
    }

    private void saveCameraEvent(List<StatusVo> cameraList, Map<String, Boolean> lastStatusVoMap) {
        cameraList.forEach(
                camera -> {
                    if (!Objects.equals(
                            camera.getOnline(), lastStatusVoMap.get(camera.getUuid()))) {
                        log.info("online check ---> camera数据改变，uuid:{}", camera.getUuid());
                        CameraEntity cameraEntity = cameraEntityMap.get(camera.getUuid());
                        if (cameraEntity == null) {
                            cameraEntity = cameraService.getById(camera.getUuid());
                            cameraEntityMap.put(camera.getUuid(), cameraEntity);
                        }
                        EventMessageEntity eventMessageEntity = new EventMessageEntity();
                        eventMessageEntity.setDeviceId(camera.getUuid());
                        eventMessageEntity.setEquipName("CAMERA");
                        eventMessageEntity.setMaintain(false);
                        eventMessageEntity.setEquipType("CAMERA");
                        handOffLineEvent(eventMessageEntity, camera.getOnline());
                    }
                });
    }

    private void saveSteerableEvent(
            List<StatusVo> controllableList, Map<String, Boolean> lastStatusVoMap) {
        controllableList.forEach(
                controllable -> {
                    if (!Objects.equals(
                            controllable.getOnline(),
                            lastStatusVoMap.get(controllable.getUuid()))) {
                        log.info(
                                "online check ---> controllable数据改变，uuid:{}",
                                controllable.getUuid());
                        ControllableEntity controllableEntity =
                                controllableEntityMap.get(controllable.getUuid());
                        if (controllableEntity == null) {
                            controllableEntity =
                                    controllableService.getById(controllable.getUuid());
                            controllableEntityMap.put(controllable.getUuid(), controllableEntity);
                        }
                        EventMessageEntity eventMessageEntity = new EventMessageEntity();
                        eventMessageEntity.setDeviceId(controllable.getUuid());
                        eventMessageEntity.setEquipName("CONTROLLABLE");
                        eventMessageEntity.setMaintain(false);
                        eventMessageEntity.setEquipType("CONTROLLABLE");
                        handOffLineEvent(eventMessageEntity, controllable.getOnline());
                    }
                });
    }

    private void saveMeterEvent(List<StatusVo> meterList, Map<String, Boolean> lastStatusVoMap) {
        meterList.forEach(
                meter -> {
                    if (!Objects.equals(meter.getOnline(), lastStatusVoMap.get(meter.getUuid()))) {
                        log.info("online check --->  meter数据改变，uuid:{}", meter.getUuid());
                        AmmeterEntity ammeterEntity = ammeterEntityMap.get(meter.getUuid());
                        if (ammeterEntity == null) {
                            ammeterEntity = ammeterService.getById(meter.getUuid());
                            ammeterEntityMap.put(meter.getUuid(), ammeterEntity);
                        }
                        EventMessageEntity eventMessageEntity = new EventMessageEntity();
                        eventMessageEntity.setDeviceId(meter.getUuid());
                        eventMessageEntity.setEquipName("METER");
                        eventMessageEntity.setMaintain(false);
                        eventMessageEntity.setEquipType("METER");
                        handOffLineEvent(eventMessageEntity, meter.getOnline());
                    }
                });
    }

    private void saveEmsEvent(List<StatusVo> emsList, Map<String, Boolean> lastStatusVoMap) {
        emsList.forEach(
                ems -> {
                    if (!Objects.equals(ems.getOnline(), lastStatusVoMap.get(ems.getUuid()))) {
                        DeviceEntity deviceEntity = deviceEntityMap.get(ems.getUuid());
                        if (deviceEntity == null) {
                            deviceEntity = deviceService.getById(ems.getUuid());
                            deviceEntityMap.put(ems.getUuid(), deviceEntity);
                        }
                        EventMessageEntity eventMessageEntity = new EventMessageEntity();
                        eventMessageEntity.setDeviceId(ems.getUuid());
                        eventMessageEntity.setEquipName("EMS");
                        eventMessageEntity.setMaintain(deviceEntity.getMaintain());
                        eventMessageEntity.setEquipType("EMS");
                        handOffLineEvent(eventMessageEntity, ems.getOnline());
                        log.info("online check ---> ems数据改变，uuid:{}", ems.getUuid());
                    }
                });
    }

    public void handOffLineEvent(EventMessageEntity eventMessageEntity, Boolean oneLine) {
        if (Boolean.FALSE.equals(oneLine)) {
            eventMessageEntity.setEventOnOff("on");
            eventMessageEntity.setStatus(0);
        } else {
            eventMessageEntity.setEventOnOff("off");
        }
        eventMessageEntity.setEventDescription("离线");
        eventMessageEntity.setEventDescriptionEn("offline");
        // 事件编码=偏移量+bit码
        eventMessageEntity.setEventCode(0);
        eventMessageEntity.setEventType(EventLevelEnum.FAULT.getLevel());
        eventMessageEntity.setProjectId(projectId);
        // 保存事件数据
        eventMessageService.save(eventMessageEntity);
    }
}
