package com.wifochina.modules.data.influxdb.client;

import com.influxdb.client.*;
import com.wifochina.common.util.HashUtil;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.group.entity.GroupEntity;

import lombok.Data;

import okhttp3.OkHttpClient;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @since 4/6/2022 3:27 PM
 * <AUTHOR>
 */
@Component
@Data
@EnableConfigurationProperties
public class InfluxClient implements InitializingBean, EnvironmentAware, InfluxClientService {

    public WriteApiBlocking writeApi;

    public QueryApi queryApi;

    private Environment environment;

    public static final String BUCKET_REALTIME = "ems";

    public static final String BUCKET_FOREVER = "forever";

    public static final String BUCKET_MEAN = "mean";

    public static final String BUCKET_DEMAND = "control";

    // 支持一下 可控设备的
    public static final String TABLE_CONTROLLABLE = "T4_5s";
    public static final String TABLE_EMS = "T1_5s";
    public static final String TABLE_EMS_T0 = "T0_1m";

    public static final String TABLE_METER = "T3_5s";

    public static final String TABLE_POWER_1M = "power_1m";

    public static final String TABLE_DEMAND_1M_SLIDING = "T3_5s";

    public static final String TABLE_DEMAND_15M_FIXED = "T3_5s";

    public static final String TABLE_DEMAND_30M_FIXED = "T3_5s";
    public static final String TABLE_DEMAND = "T3_5s";

    public static final String KEY_DEVICE = "deviceId";

    public static final String KEY_METER = "ammeterId";
    public static final String TABLE_DEMAND_PREFIX = "control_";
    public static final String TABLE_CAPACITY = "capacity_1d";
    private String groupBucket;
    private static final String TABLE_EMS_GROUP = "group";

    @Override
    public void afterPropertiesSet() {
        String url = environment.getProperty("ems.influx.url");
        String token = environment.getProperty("ems.influx.token");
        String org = environment.getProperty("ems.influx.org");
        String bucket = environment.getProperty("ems.influx.bucket");
        this.groupBucket = environment.getProperty("ems.influx.bucketgroup");
        assert url != null;
        assert token != null;
        InfluxDBClientOptions influxDBClientOptions =
                InfluxDBClientOptions.builder()
                        .bucket(bucket)
                        .url(url)
                        .org(org)
                        .authenticateToken(token.toCharArray())
                        .okHttpClient(
                                new OkHttpClient.Builder()
                                        // .connectTimeout(5, TimeUnit.SECONDS)
                                        .readTimeout(120, TimeUnit.SECONDS))
                        .build();
        InfluxDBClient influxDbClient = InfluxDBClientFactory.create(influxDBClientOptions);
        writeApi = influxDbClient.getWriteApiBlocking();
        queryApi = influxDbClient.getQueryApi();
    }

    @Autowired
    public InfluxClient(Environment environment) {
        this.environment = environment;
    }

    @Override
    public String getBucketGroup() {
        return groupBucket;
    }

    @Override
    public String getBucketRealtime() {
        return BUCKET_REALTIME;
    }

    @Override
    public String getBucketForever() {
        return BUCKET_FOREVER;
    }

    @Override
    public String getBucketMean() {
        return BUCKET_MEAN;
    }

    @Override
    public String getBucketDemand() {
        return BUCKET_DEMAND;
    }

    @Override
    public String getTableEms() {
        return TABLE_EMS;
    }

    @Override
    public String getTableMeter() {
        return TABLE_METER;
    }

    @Override
    public String getTableDemand1mSliding() {
        return TABLE_DEMAND_1M_SLIDING;
    }

    @Override
    public String getTableDemand15mFixed() {
        return TABLE_DEMAND_15M_FIXED;
    }

    @Override
    public String getTableDemand30mFixed() {
        return TABLE_DEMAND_30M_FIXED;
    }

    @Override
    public String getControllableTable(String projectId) {
        return TABLE_CONTROLLABLE;
    }

    @Override
    public String getEmsTable(String projectId) {
        return TABLE_EMS;
    }

    public static void main(String[] args) {
        int s = HashUtil.elfHash("4f537620d37d40e19dd25be5ca6ad941");
        int s2 = HashUtil.elfHash("4f537620d37d40e19dd25be5ca6ad941");
        System.out.println(s);
        System.out.println(s2);
    }

    @Override
    public String getEmsGroupTable(String projectId) {
        return TABLE_EMS_GROUP + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public String getEmsT0Table(String projectId) {
        return TABLE_EMS_T0;
    }

    @Override
    public String getMeterTable(String projectId) {
        return TABLE_METER;
    }

    @Override
    public String getDemandTable(String projectId, GroupEntity systemGroup) {
        return TABLE_DEMAND;
    }

    @Override
    public String getCapacityTable(String projectId) {
        return TABLE_CAPACITY;
    }

    @Override
    public String getCellTable(String projectId) {
        return TABLE_METER;
    }

    @Override
    public String getDeviceKey() {
        return KEY_DEVICE;
    }

    @Override
    public String getMeterKey() {
        return KEY_METER;
    }

    @Override
    public String getControllableKey() {
        return "ctrlId";
    }

    @Override
    public String getOrg() {
        return environment.getProperty("ems.influx.org");
    }

    @Override
    public void setEnvironment(@NotNull Environment environment) {
        this.environment = environment;
    }
}
