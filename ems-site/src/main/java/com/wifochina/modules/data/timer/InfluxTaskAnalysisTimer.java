package com.wifochina.modules.data.timer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.entity.PointDataType;
import com.wifochina.modules.data.influxdb.client.InfluxClient;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.DeviceService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 * @since 3/29/2022 11:40 AM
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InfluxTaskAnalysisTimer {

    private final DeviceService deviceService;

    private final InfluxClient influxClient;

    private final PointListHolder pointListHolder;

    /** 动态采集数组数据 */
    public static Map<String, int[]> pointT0DataMap = new HashMap<>();

    /** 动态采集数组数据 */
    public static Map<String, int[]> pointT2DataMap = new HashMap<>();

    @Value("${ems.collect.project.id}")
    private String projectId;

    /** 根据采集级别，对采集到的数据进行分析，然后将其存储到时序数据库里,T0对应级别，启动后1.5秒立即执行一次 */
    @Async("taskScheduler")
    @Scheduled(initialDelay = 1500, fixedRateString = "${spring.task.t0.fixedRate}")
    public void analysisTaskLevelOne() {
        log.info("{} -->正在执行", " analysisTaskLevelOne");
        List<DeviceEntity> deviceList =
                deviceService.list(
                        Wrappers.lambdaQuery(DeviceEntity.class)
                                .eq(DeviceEntity::getProjectId, projectId)
                                .eq(DeviceEntity::getUnreal, false));
        List<Point> points = new ArrayList<>();
        long time = Instant.now().toEpochMilli();
        for (DeviceEntity device : deviceList) {
            // 从缓存pointDataMap取，采集完删除，可以防止重复录入
            int[] data = pointT0DataMap.get(device.getId());
            if (data == null || data.length == 0) {
                continue;
            }
            Point point =
                    saveData(
                            data,
                            device.getId(),
                            "T0_1m",
                            pointListHolder.getPointDefinitionLevelT0List(
                                    data[pointListHolder.getEmsTypeIndex()]),
                            time);
            if (point != null) {
                points.add(point);
            }
            // 单独处理ASCII数据
            Point point2 =
                    saveData(
                            data,
                            device.getId(),
                            "T0_1m_asc",
                            pointListHolder.getAsciiPointDataList(
                                    data[pointListHolder.getEmsTypeIndex()]),
                            time);
            if (point2 != null) {
                points.add(point2);
            }
        }
        if (!points.isEmpty()) {
            influxClient.writeApi.writePoints(points);
        }
    }

    /** 根据采集级别，对采集到的数据进行分析，然后将其存储到时序数据库里 */
    @Async("taskScheduler")
    @Scheduled(fixedRateString = "${spring.task.t2.fixedRate}")
    public void analysisTaskLevelThree() {
        log.info("{} -->正在执行", " AnalysisTaskLevelThree");
        List<DeviceEntity> deviceList =
                deviceService.list(
                        Wrappers.lambdaQuery(DeviceEntity.class)
                                .eq(DeviceEntity::getProjectId, projectId));
        List<Point> points = new ArrayList<>();
        long time = Instant.now().toEpochMilli();
        for (DeviceEntity device : deviceList) {
            // 数据采集缓存的数据
            int[] data = pointT2DataMap.get(device.getId());
            // 数据池中没有新采集的数据
            if (data == null || data.length == 0) {
                continue;
            }
            String tableName = "T2_1m";
            // T2任务级别的表名
            Point point = Point.measurement(tableName);
            // 设置tags值
            point.addTag("deviceId", device.getId());
            point.addTag("projectId", projectId);
            // 电池簇数量
            int cluster =
                    data[pointListHolder.getBmsClusterNum(data[pointListHolder.getEmsTypeIndex()])];
            // 每簇电池组数量
            int stack =
                    data[pointListHolder.getBmsStackNum(data[pointListHolder.getEmsTypeIndex()])];
            // 每组电芯数量
            int cell = data[pointListHolder.getBmsCellNum(data[pointListHolder.getEmsTypeIndex()])];
            // 每组电芯温度传感器数量
            int cellT =
                    data[pointListHolder.getBmsCellTNum(data[pointListHolder.getEmsTypeIndex()])];
            int initCellIndex =
                    pointListHolder.getCellInitIndex(data[pointListHolder.getEmsTypeIndex()]);
            for (int i = 0; i < cluster; i++) {
                for (int j = 0; j < stack; j++) {
                    for (int k = 0; k < cell; k++) {
                        String column = "bms_cell_voltage_" + i + "_" + j + "_" + k;
                        double value = data[initCellIndex + k + (j) * cell + cell * stack * i];
                        double d = Double.parseDouble(String.format("%.2f", value / 1000));
                        point.addField(column, d);
                    }
                }
            }
            int cellIndex = initCellIndex + cluster * stack * cell;
            for (int i = 0; i < cluster; i++) {
                for (int j = 0; j < stack; j++) {
                    for (int k = 0; k < cellT; k++) {
                        String column = "bms_cell_temperature_" + i + "_" + j + "_" + k;
                        double value = data[cellIndex + k + (j) * cellT + cellT * stack * i];
                        double d = Double.parseDouble(String.format("%.2f", value / 10));
                        point.addField(column, d);
                    }
                }
            }
            // 设置事件
            point.time(time, WritePrecision.MS);
            points.add(point);
        }
        // 录入数据
        if (!points.isEmpty()) {
            influxClient.writeApi.writePoints(points);
        }
    }

    /**
     * 通用的保存数据到influxdb
     *
     * @param uuid 设备uuid
     * @param tableName measurement name
     * @param list 点位list
     */
    public Point saveData(
            int[] data, String uuid, String tableName, List<PointDataEntity> list, long time) {
        // 数据池中没有新采集的数据
        if (data == null) {
            return null;
        }
        // TO任务级别的表名
        Point point = Point.measurement(tableName);
        // 设置tags值S
        point.addTag("deviceId", uuid);
        point.addTag("projectId", projectId);
        // 获取T0级别的任务
        for (PointDataEntity pointDataEntity : list) {
            try {
                // 该装备或者点位有多个
                if (pointDataEntity.getPointColumn().contains("{i}")) {

                    String offset = pointDataEntity.getPointOffset();
                    // 获取装备类型
                    String equipType =
                            offset.substring(offset.indexOf("+") + 1, offset.indexOf("*") - 1);

                    int countIndex =
                            pointListHolder
                                    .getEquipCountIndexMap(data[pointListHolder.getEmsTypeIndex()])
                                    .get(equipType.trim());
                    // 返回装备实际数量
                    int count = data[countIndex];
                    // 初始位置
                    int addressInit = pointDataEntity.getPointAddress();
                    // 计算出offset的起点位置
                    int offsetStart =
                            Integer.parseInt(offset.substring(0, offset.indexOf("+")).trim());
                    // 计算出offset的倍数
                    int multiple =
                            Integer.parseInt(offset.substring(offset.indexOf("*") + 1).trim());
                    // 循环遍历处理该设备下的所有装备
                    for (int i = 0; i < count; i++) {
                        int index = addressInit + offsetStart + i * multiple;
                        // 替换column作为influx的filed
                        String column =
                                pointDataEntity.getPointColumn().replace("{i}", String.valueOf(i));
                        addField(
                                point,
                                column,
                                data,
                                index,
                                pointDataEntity.getPointType(),
                                pointDataEntity.getPointMul());
                    }
                } else {
                    // 得到所有的field值
                    int index =
                            pointDataEntity.getPointAddress()
                                    + Integer.parseInt(pointDataEntity.getPointOffset());
                    String column = pointDataEntity.getPointColumn();
                    addField(
                            point,
                            column,
                            data,
                            index,
                            pointDataEntity.getPointType(),
                            pointDataEntity.getPointMul());
                }
            } catch (Exception e) {
                log.error(pointDataEntity.getPointColumn());
            }
        }
        point.time(time, WritePrecision.MS);
        return point;
    }

    /**
     * 根据不同类型，存入不同的数据类型点位，方便进行集合运算
     *
     * @param point 点位
     * @param column 字段
     * @param data 数据
     * @param index 点位索引
     * @param pointType 点位类型
     * @param mul 点位倍数
     */
    void addField(
            Point point, String column, int[] data, int index, String pointType, Integer mul) {
        if (PointDataType.getByDesc(pointType) == null) {
            log.error("有数据type为空，index---》" + index);
        }
        StringBuilder value = new StringBuilder();
        switch (Objects.requireNonNull(PointDataType.getByDesc(pointType))) {
            case ASCII:
                for (int i = 0; i < mul; i++) {
                    value.append((char) (data[index + i] >> 8));
                    value.append((char) (data[index + i] & 0xFF));
                }
                point.addField(column, value.toString().trim());
                break;
            case UINT16:
                double d1 = Double.parseDouble(String.format("%.2f", (double) data[index] / mul));
                point.addField(column, d1);
                break;
            case INT16:
                double d2 =
                        Double.parseDouble(
                                String.format("%.2f", ((double) (short) data[index]) / mul));
                point.addField(column, d2);
                break;
            case BIT:
                point.addField(column, data[index]);
                break;
            case INT32:
                int high = data[index];
                int low = data[index + 1];
                int temp = (high << 16) + low;
                double d3 = Double.parseDouble(String.format("%.2f", (double) temp / mul));
                point.addField(column, d3);
                break;
            case UINT32:
                int highU = data[index];
                int lowU = data[index + 1];
                long tempU = ((long) highU << 16) + lowU;
                double d4 = Double.parseDouble(String.format("%.2f", (double) tempU / mul));
                if (d4 == 0) {
                    if (!EmsConstants.EMS_HISTORY_OUTPUT_ENERGY.equals(column)
                            && !EmsConstants.EMS_HISTORY_INPUT_ENERGY.equals(column)) {
                        point.addField(column, d4);
                    }
                } else {
                    point.addField(column, d4);
                }
                break;
            default:
                point.addField(column, value.toString());
        }
    }
}
