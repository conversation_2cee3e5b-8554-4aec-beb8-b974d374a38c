package com.wifochina.modules.data.timer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.util.DemandControlEnum;
import com.wifochina.modules.data.entity.ElectricityUsage;
import com.wifochina.modules.data.entity.MeterContentData;
import com.wifochina.modules.data.entity.MeterData;
import com.wifochina.modules.data.influxdb.client.InfluxClient;
import com.wifochina.modules.data.listener.DataEventPublishService;
import com.wifochina.modules.data.listener.MeterStateChange;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.entity.GroupAmmeterEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.service.GroupAmmeterService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2022-03-03 15:37:04
 *     <p>致敬大师，彼岸无岸，当下即是
 */
@Slf4j
@Component
public class InfluxMeterTaskCollectDataTimer {

    @Resource private AmmeterService ammeterService;

    @Resource private DataEventPublishService dataEventPublishService;

    @Resource private ControllerService controllerService;

    @Resource private RestTemplate restTemplate;

    @Resource private GroupService groupService;

    @Resource private StrategyService strategyService;

    @Resource private GroupAmmeterService groupAmmeterService;

    @Resource private HideEventCodeService hideEventCodeService;

    @Resource private InfluxClient influxClient;

    public static Map<String, int[]> meterStatusDataMap = new HashMap<>();

    @Value("${ems.collect.project.id}")
    private String projectId;

    /** 定时采集电表数据 间隔时间在配置文件中 initialDelay设为4000ms，等待服务起好后执行 */
    @Async("taskScheduler")
    @Scheduled(initialDelay = 4000, fixedRateString = "${spring.task.meter.fixedRate}")
    public void collectMeterDataPoint() throws IllegalAccessException {
        log.info("collectMeterDataPoint---> 电表数据正在执行");
        List<AmmeterEntity> ammeterEntities = ammeterService.lambdaQuery().list();
        if (ammeterEntities.isEmpty()) {
            return;
        }
        List<ControllerEntity> controllerEntities =
                controllerService.list(
                        Wrappers.lambdaQuery(ControllerEntity.class)
                                .eq(ControllerEntity::getProjectId, projectId));
        // 没有任何控制器则直接退出
        if (controllerEntities == null || controllerEntities.isEmpty()) {
            return;
        }
        ControllerEntity controllerEntity = controllerEntities.get(0);
        String url =
                "http://"
                        + controllerEntity.getIp()
                        + ":"
                        + controllerEntity.getPort()
                        + "/api/v1/get_meter_information";
        JSONObject jsonObject;
        try {
            jsonObject = restTemplate.postForObject(url, null, JSONObject.class);
        } catch (Exception e) {
            // 有异常证明数据采集不成功，则不处理
            log.error(
                    "collectMeterDataPoint--->meter数据采集 {}",
                    ErrorResultCode.FAIL_CONNECT_GOCONTROL.value() + " - " + e.getMessage());
            return;
        }
        if (jsonObject == null) {
            return;
        }
        String arrayData = JSON.toJSONString(jsonObject.get("meters"));
        List<MeterData> list = JSON.parseArray(arrayData, MeterData.class);
        if (list == null) {
            return;
        }
        // 设置tags值
        GroupEntity systemGroupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, projectId)
                                .eq(GroupEntity::getWhetherSystem, true));
        StrategyEntity strategyEntity =
                strategyService.getOne(
                        Wrappers.lambdaQuery(StrategyEntity.class)
                                .eq(StrategyEntity::getGroupId, systemGroupEntity.getId())
                                .eq(StrategyEntity::getWeekDay, 0)
                                .eq(StrategyEntity::getProjectId, projectId));
        Map<String, Integer> meterTypesMap =
                ammeterEntities.stream()
                        .collect(Collectors.toMap(AmmeterEntity::getId, AmmeterEntity::getType));
        List<Point> points = new ArrayList<>();

        List<String> hideMeterEventCodes = hideEventCodeService.getHideMeterTypeEventCodes(projectId);

        for (MeterData meterData : list) {
            // 发布电表事件
            publishMeterEvent(meterData, hideMeterEventCodes);
            Point point =
                    saveMeterData(
                            systemGroupEntity,
                            strategyEntity,
                            meterData,
                            meterTypesMap,
                            Instant.now().toEpochMilli());
            if (point != null) {
                points.add(point);
            }
        }
        // 录入数据
        if (!points.isEmpty()) {
            influxClient.writeApi.writePoints(points);
        }
    }

    private Point saveMeterData(
            GroupEntity systemGroupEntity,
            StrategyEntity strategyEntity,
            MeterData meterData,
            Map<String, Integer> meterTypesMap,
            long time)
            throws IllegalAccessException {
        String tableName = "T3_5s";
        // T3任务级别的表名
        Point point = Point.measurement(tableName);
        MeterContentData meterContentData = meterData.getInformation();
        if (meterContentData == null || !meterContentData.getOnline()) {
            return null;
        }
        List<String> systemMeterIds =
                groupAmmeterService
                        .list(
                                Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                                        .eq(
                                                GroupAmmeterEntity::getGroupId,
                                                systemGroupEntity.getId()))
                        .stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .map(String::valueOf)
                        .collect(Collectors.toList());
        point.addTag("ammeterId", String.valueOf(meterData.getUuid()));
        point.addTag("type", String.valueOf(meterTypesMap.get(meterData.getUuid())));
        point.addTag("projectId", projectId);
        if (systemMeterIds.contains(String.valueOf(meterData.getUuid()))) {
            point.addTag("system", "1");
        } else {
            point.addTag("system", "0");
        }
        Field[] declaredFields = meterContentData.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            // 设置是否可以访问，如果不设置将报错
            declaredField.setAccessible(true);
            Class<?> type = declaredField.getType();
            String column = declaredField.getName();
            if (Objects.equals(type, List.class)) {
                List<?> data = (List<?>) declaredField.get(meterContentData);
                if (data == null) {
                    continue;
                }
                for (int j = 0; j < data.size(); j++) {
                    Object datum = data.get(j);
                    if (datum instanceof Double) {
                        point.addField(
                                column + "_" + j, Double.parseDouble(String.format("%.2f", datum)));
                    } else if (datum instanceof Boolean) {
                        point.addField(column + "_" + j, (Boolean) datum);
                    }
                }
            } else if (Objects.equals(type, Boolean.class)) {
                point.addField(column, (Boolean) declaredField.get(meterContentData));
            } else if (Objects.equals(type, Integer.class)) {
                point.addField(column, (Integer) declaredField.get(meterContentData));
            } else if (Objects.equals(type, Double.class)) {
                Double tmp = (Double) declaredField.get(meterContentData);
                if (tmp != null) {
                    double value = Double.parseDouble(String.format("%.2f", tmp));
                    if (value == 0) {
                        if (!"ac_history_positive_power_in_kwh".equals(column)
                                && !"ac_history_negative_power_in_kwh".equals(column)) {
                            point.addField(column, value);
                        }
                    } else {
                        point.addField(column, value);
                    }
                } else {
                    log.info("column {} tep: {}", column, tmp);
                    point.addField(column, 0);
                }
            } else if (Objects.equals(type, ElectricityUsage.class)) {
                ElectricityUsage electricityUsage =
                        (ElectricityUsage) declaredField.get(meterContentData);
                if (electricityUsage == null) {
                    continue;
                }
                if ("positiveElectricityUsage".equals(column)) {
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_1.field(),
                            electricityUsage.getPeak());
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_2.field(),
                            electricityUsage.getValley());
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_3.field(),
                            electricityUsage.getFlat());
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_4.field(),
                            electricityUsage.getPeak2());
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_5.field(),
                            electricityUsage.getValley2());
                } else {
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_1.field(),
                            electricityUsage.getPeak());
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_2.field(),
                            electricityUsage.getValley());
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_3.field(),
                            electricityUsage.getFlat());
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_4.field(),
                            electricityUsage.getPeak2());
                    point.addField(
                            MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_5.field(),
                            electricityUsage.getValley2());
                }
            }
            // 设置时间
            point.time(time, WritePrecision.MS);
            if (DemandControlEnum.enableDemandControl(systemGroupEntity.getDemandControl())) {
                //            if (systemGroupEntity.getDemandController()) {
                // 录入系统需量
                if (strategyEntity != null) {
                    point.addField("control_power", strategyEntity.getControlPower());
                }
                // 录入系统需量
                if (strategyEntity != null) {
                    point.addField("demand_power", strategyEntity.getDemandPower());
                }
            }
        }
        return point;
    }

    private void publishMeterEvent(MeterData meterData, List<String> hideMeterEventCodes) {
        String meterId = meterData.getUuid();
        AmmeterEntity ammeterEntity = ammeterService.getById(meterId);
        if (ammeterEntity == null) {
            return;
        }
        if (Boolean.FALSE.equals(ammeterEntity.getControllable())) {
            return;
        }
        List<Integer> auxList = meterData.getInformation().getAux_digital();
        if (auxList == null || auxList.isEmpty()) {
            return;
        }
        int[] data = auxList.stream().mapToInt(i -> i).toArray();
        int[] lastData = meterStatusDataMap.get(meterId);
        meterStatusDataMap.put(meterData.getUuid(), data);
        if (lastData == null || data == null) {
            return;
        }
        for (int i = 0; i < data.length; i++) {
            String diff = Integer.toBinaryString(lastData[i] ^ data[i]);
            if ("0".equals(diff)) {
                continue;
            }
            log.info("原始数据：{}，新采集数据{}", lastData[i], data[i]);
            MeterStateChange meterStateChange = new MeterStateChange();
            meterStateChange.setMeterId(meterId);
            meterStateChange.setMeterName(ammeterEntity.getName());
            meterStateChange.setBinaryString(diff);
            meterStateChange.setMeterType(ammeterEntity.getVendor());
            meterStateChange.setValue(data[i]);
            meterStateChange.setLastValue(lastData[i]);
            meterStateChange.setIndex(i);
            meterStateChange.setMaintain(ammeterEntity.getMaintain());
            meterStateChange.setProjectId(ammeterEntity.getProjectId());
            meterStateChange.setHideEventCodes(hideMeterEventCodes);
            dataEventPublishService.publishStateChangeEvent(meterStateChange);
        }
    }
}
