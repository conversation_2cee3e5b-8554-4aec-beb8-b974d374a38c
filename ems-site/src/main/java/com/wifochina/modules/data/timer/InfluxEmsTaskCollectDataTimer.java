package com.wifochina.modules.data.timer;

import com.alibaba.fastjson.JSONObject;
import com.influxdb.client.write.Point;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.TypeCodeIndexEnum;
import com.wifochina.common.util.ConversionUtil;
import com.wifochina.modules.data.entity.EmsDataCache;
import com.wifochina.modules.data.entity.OffsetTypeEnum;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.influxdb.client.InfluxClient;
import com.wifochina.modules.data.listener.DataEventPublishService;
import com.wifochina.modules.data.listener.StateChange;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.service.DeviceService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2022-03-03 15:37:04
 *     <p>致敬大师，彼岸无岸，当下即是
 */
@Slf4j
@Component
public class InfluxEmsTaskCollectDataTimer {

    @Resource private DeviceService deviceService;

    @Resource private DataEventPublishService dataEventPublishService;

    @Resource private ControllerService controllerService;

    @Resource private RestTemplate restTemplate;

    @Resource private InfluxClient influxClient;

    @Resource private PointListHolder pointListHolder;

    @Resource private InfluxTaskAnalysisTimer influxTaskAnalysisTimer;

    @Resource private HideEventCodeService hideEventCodeService;

    /** 动态采集数组数据，key为设备id */
    public static Map<String, int[]> pointDataMap = new HashMap<>();

    @Value("${ems.collect.project.id}")
    private String projectId;

    /** 定时采集设备数据 间隔时间在配置文件中 initialDelay设为3000ms，等待服务起好后执行 */
    @Async("taskScheduler")
    @Scheduled(initialDelay = 3000, fixedRateString = "${spring.task.ems.fixedRate}")
    public void collectDeviceDataPoint() {
        log.info("collectDeviceDataPoint---> ems采集正在执行");
        List<Point> points = new ArrayList<>();
        // 实时数据
        try {
            ControllerEntity controllerEntity =
                    controllerService
                            .lambdaQuery()
                            .eq(ControllerEntity::getProjectId, projectId)
                            .one();
            String url =
                    "http://" + controllerEntity.getIp().trim() + ":" + controllerEntity.getPort();
            JSONObject jsonObject =
                    restTemplate.postForObject(
                            url + "/api/v1/get_all_ems_modbus", null, JSONObject.class);
            assert jsonObject != null;
            List<DeviceEntity> deviceList =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getUnreal, false)
                            .eq(DeviceEntity::getProjectId, projectId)
                            .list();
            long now = Instant.now().getEpochSecond();
            long time = Instant.now().toEpochMilli();
            List<String> hideEventCodeList =
                    hideEventCodeService.getHideEmsTypeEventCodes(projectId);
            for (DeviceEntity deviceEntity : deviceList) {
                List<Integer> dataList =
                        ConversionUtil.objToList(
                                jsonObject.getJSONObject(deviceEntity.getId()).get("data"),
                                Integer.class);
                int[] data = dataList.stream().mapToInt(Integer::valueOf).toArray();
                EmsDataCache emsDataCacheTemp = new EmsDataCache();
                emsDataCacheTemp.setData(data);
                emsDataCacheTemp.setUpdateTime(now);
                DataService.EMS_DATA_CACHE_MAP.put(deviceEntity.getId(), emsDataCacheTemp);
                if (data == null || data.length == 0) {
                    if (InfluxTaskAnalysisTimer.pointT0DataMap != null
                            && InfluxTaskAnalysisTimer.pointT0DataMap.get(deviceEntity.getId())
                                    != null) {
                        InfluxTaskAnalysisTimer.pointT0DataMap.remove(deviceEntity.getId());
                    }
                    if (InfluxTaskAnalysisTimer.pointT2DataMap != null
                            && InfluxTaskAnalysisTimer.pointT2DataMap.get(deviceEntity.getId())
                                    != null) {
                        InfluxTaskAnalysisTimer.pointT2DataMap.clear();
                    }
                    continue;
                }
                // 存储T0的数据
                Point point =
                        influxTaskAnalysisTimer.saveData(
                                data,
                                deviceEntity.getId(),
                                "T1_5s",
                                pointListHolder.getPointDefinitionLevelT1List(
                                        data[pointListHolder.getEmsTypeIndex()]),
                                time);
                points.add(point);
                // 最近一次的data
                int[] lastData = pointDataMap.get(deviceEntity.getId());
                if (lastData != null) {
                    publishEmsEvent(deviceEntity, data, lastData, hideEventCodeList);
                }
                // 更新缓存数据，方便下次对比以及作为实时运行监控数据和数据采集的来源
                pointDataMap.put(deviceEntity.getId(), data);
                // 向数据解析记录点位任务里面丢入数据，放置3个map是因为录入一次后，数据应清空，防止重复录入相同数据
                InfluxTaskAnalysisTimer.pointT0DataMap.put(deviceEntity.getId(), data);
                InfluxTaskAnalysisTimer.pointT2DataMap.put(deviceEntity.getId(), data);
            }
        } catch (Exception e) {
            // 有异常证明数据采集不成功，则不处理
            log.error(
                    "collectDeviceDataPoint--->ems数据采集 {}",
                    ErrorResultCode.FAIL_CONNECT_GOCONTROL.value() + " - " + e.getMessage());
        } finally {
            if (!points.isEmpty()) {
                influxClient.writeApi.writePoints(points);
            }
        }
    }

    private void publishEmsEvent(
            DeviceEntity deviceEntity, int[] data, int[] lastData, List<String> hideEventCodeList) {

        List<PointDataEntity> pointDataEventList =
                pointListHolder.getPointDataEventList(data[pointListHolder.getEmsTypeIndex()]);
        // 循环事件点位监控数据，如果发生前后状态的变化，再进行事件通知
        for (PointDataEntity pointDataEntity : pointDataEventList) {
            String offset = pointDataEntity.getPointOffset();
            // 如果有 * 号，则点位需要计算
            if (offset.contains("*")) {
                // 暂时不记录cluster变化的情况，以后需要再注销代码放出来。同时需要在事件数据库添加
                if (offset.contains(OffsetTypeEnum.CLUSTER.getCode())) {
                    continue;
                }
                // 获取装备类型 offset值示例（100+AIRi*10）-》AIR
                String equipType =
                        offset.substring(offset.indexOf("+") + 1, offset.indexOf("*") - 1);
                int countIndex =
                        pointListHolder
                                .getEquipCountIndexMap(data[pointListHolder.getEmsTypeIndex()])
                                .get(equipType.trim());
                // 返回装备实际数量
                int count = data[countIndex];
                // 初始位置
                int addressInit = pointDataEntity.getPointAddress();
                // 计算出offset的起点位置
                int offsetStart = Integer.parseInt(offset.substring(0, offset.indexOf("+")).trim());
                // 计算出offset的倍数
                int multiple = Integer.parseInt(offset.substring(offset.indexOf("*") + 1).trim());
                // 循环遍历处理该设备下的所有装备
                for (int i = 0; i < count; i++) {
                    int address = addressInit + offsetStart + i * multiple;
                    String diff = Integer.toBinaryString(lastData[address] ^ data[address]);
                    if (!"0".equals(diff)) {
                        log.info("原始数据：{}，新采集数据{}", lastData[address], data[address]);
                        StateChange stateChange =
                                getStateChange(
                                        deviceEntity,
                                        lastData,
                                        data,
                                        pointDataEntity,
                                        equipType,
                                        i,
                                        address,
                                        diff);
                        // 2025-05-14 14:21:44 added  hide event codes
                        stateChange.setHideEventCodes(hideEventCodeList);
                        dataEventPublishService.publishStateChangeEvent(stateChange);
                    }
                }
            } else {
                // 系统状态
                int address =
                        pointDataEntity.getPointAddress()
                                + Integer.parseInt(pointDataEntity.getPointOffset().trim());
                String diff = Integer.toBinaryString(lastData[address] ^ data[address]);
                if (!"0".equals(diff)) {
                    StateChange stateChange =
                            getStateChange(
                                    deviceEntity, lastData, data, pointDataEntity, address, diff);
                    dataEventPublishService.publishStateChangeEvent(stateChange);
                }
            }
        }
    }

    private StateChange getStateChange(
            DeviceEntity deviceEntity,
            int[] lastData,
            int[] data,
            PointDataEntity pointDataEntity,
            int address,
            String diff) {
        StateChange stateChange = new StateChange();
        stateChange.setBinaryString(diff);
        stateChange.setValue(data[address]);
        stateChange.setDeviceId(deviceEntity.getId());
        stateChange.setDeviceName(deviceEntity.getName());
        stateChange.setMaintain(deviceEntity.getMaintain());
        stateChange.setAddress(address);
        stateChange.setLastValue(lastData[address]);
        setTypeCode(data, pointDataEntity, stateChange, -1);
        stateChange.setColumn(pointDataEntity.getPointColumn());
        if (pointDataEntity.getPointColumn().startsWith(TypeCodeIndexEnum.BMS.getCode())) {
            stateChange.setEquipName(OffsetTypeEnum.BMS.getCode());
        } else {
            stateChange.setEquipName("EMS");
        }
        return stateChange;
    }

    private void setTypeCode(
            int[] data, PointDataEntity pointDataEntity, StateChange stateChange, int index) {
        int typeCode =
                data[
                        pointListHolder.getTypeCodeIndex(
                                data[pointListHolder.getEmsTypeIndex()],
                                pointDataEntity.getPointColumn(),
                                index)];
        stateChange.setTypeCode(typeCode);
    }

    private StateChange getStateChange(
            DeviceEntity deviceEntity,
            int[] lastData,
            int[] data,
            PointDataEntity pointDataEntity,
            String equipType,
            int index,
            int address,
            String diff) {
        StateChange stateChange = new StateChange();
        stateChange.setBinaryString(diff);
        stateChange.setValue(data[address]);
        stateChange.setLastValue(lastData[address]);
        stateChange.setDeviceId(deviceEntity.getId());
        stateChange.setDeviceName(deviceEntity.getName());
        stateChange.setMaintain(deviceEntity.getMaintain());
        stateChange.setEquipName(equipType + index);
        stateChange.setAddress(address);
        stateChange.setEquipIndex(index);
        // 0代表system
        setTypeCode(data, pointDataEntity, stateChange, index);
        stateChange.setColumn(pointDataEntity.getPointColumn());
        return stateChange;
    }
}
