package com.wifochina.modules.data.timer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.data.entity.SteerableContentData;
import com.wifochina.modules.data.entity.SteerableData;
import com.wifochina.modules.data.influxdb.client.InfluxClient;
import com.wifochina.modules.data.listener.ControllableStateChange;
import com.wifochina.modules.data.listener.DataEventPublishService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.ControllableService;
import com.wifochina.modules.group.service.ControllerService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;

import javax.annotation.Resource;

/**
 * @since 2024-03-20 4:11 PM
 * <AUTHOR>
 */
@Slf4j
@Component
public class InfluxControllableCollectDataTime {
    @Resource private ControllableService controllableService;
    @Resource private ControllerService controllerService;
    @Resource private InfluxClient influxClient;
    @Resource private DataEventPublishService dataEventPublishService;

    @Resource private HideEventCodeService hideEventCodeService;

    @Value("${ems.collect.project.id}")
    private String projectId;

    private static final List<String> NOT_RECORD_FIELDS =
            List.of(
                    "stateEvent",
                    "alarmEvent",
                    "faultEvent",
                    "stateEventEN",
                    "alarmEventEN",
                    "faultEventEN");

    private static final Set<String> CUSTOM_PREFIX_FIELDS =
            Set.of(
                    "phase_a_temperature",
                    "phase_b_temperature",
                    "phase_c_temperature",
                    "circuit_d_temperature");
    private final Map<String, SteerableContentData> steerableContentDataMap = new HashMap<>();
    @Resource private RestTemplate restTemplate;

    /** 定时采集电表数据 间隔时间在配置文件中 initialDelay设为4000ms，等待服务起好后执行 */
    @Async("taskScheduler")
    @Scheduled(initialDelay = 4000, fixedRateString = "${spring.task.controllable.fixedRate}")
    public void collectControllableDataPoint() throws IllegalAccessException {
        log.info("collectControllableDataPoint---> 可控设备数据正在执行");
        List<ControllableEntity> controllableEntities = controllableService.lambdaQuery().list();
        if (controllableEntities.isEmpty()) {
            return;
        }
        List<ControllerEntity> controllerEntities =
                controllerService.list(
                        Wrappers.lambdaQuery(ControllerEntity.class)
                                .eq(ControllerEntity::getProjectId, projectId));
        // 没有任何控制器则直接退出
        if (controllerEntities == null || controllerEntities.isEmpty()) {
            return;
        }
        ControllerEntity controllerEntity = controllerEntities.get(0);
        String url =
                "http://"
                        + controllerEntity.getIp()
                        + ":"
                        + controllerEntity.getPort()
                        + "/api/v1/get_controllable_information";
        JSONObject jsonObject;
        try {
            jsonObject = restTemplate.postForObject(url, null, JSONObject.class);
        } catch (Exception e) {
            // 有异常证明数据采集不成功，则不处理
            log.error(
                    "collectControllableDataPoint--->可控设备数据采集 {}",
                    ErrorResultCode.FAIL_CONNECT_GOCONTROL.value() + " - " + e.getMessage());
            return;
        }
        if (jsonObject == null) {
            return;
        }
        String arrayData = JSON.toJSONString(jsonObject.get("controllables"));
        List<SteerableData> list = JSON.parseArray(arrayData, SteerableData.class);
        if (list == null) {
            return;
        }
        List<Point> points = new ArrayList<>();

        // 2025-05-14 15:28:29 added search controllable event codes from t_event_code not
        // t_meter_event_code
        List<String> hideEmsTypeEventCodes =
                hideEventCodeService.getHideEmsTypeEventCodes(projectId);
        for (SteerableData steerableData : list) {
            // 发布可控事件
            publishControllableEvent(steerableData, hideEmsTypeEventCodes);
            steerableContentDataMap.put(steerableData.getUuid(), steerableData.getInformation());
            Point point = saveControllableData(steerableData, Instant.now().toEpochMilli());
            if (point != null) {
                points.add(point);
            }
        }
        // 录入数据
        if (!points.isEmpty()) {
            influxClient.writeApi.writePoints(points);
        }
    }

    private Point saveControllableData(SteerableData steerableData, long time)
            throws IllegalAccessException {
        String tableName = "T4_5s";
        // T3任务级别的表名
        Point point = Point.measurement(tableName);
        SteerableContentData contentData = steerableData.getInformation();
        if (contentData == null || !contentData.getOnline()) {
            return null;
        }
        point.addTag("controllableId", String.valueOf(steerableData.getUuid()));
        point.addTag("projectId", projectId);
        Field[] declaredFields = contentData.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            // 设置是否可以访问，如果不设置将报错
            declaredField.setAccessible(true);
            Class<?> type = declaredField.getType();
            String column = declaredField.getName();
            if (Objects.equals(type, List.class)) {
                List<?> data = (List<?>) declaredField.get(contentData);
                if (data == null) {
                    continue;
                }
                for (int j = 0; j < data.size(); j++) {
                    Object datum = data.get(j);
                    if (datum instanceof Double) {
                        point.addField(
                                column + "_" + j, Double.parseDouble(String.format("%.2f", datum)));
                    } else if (datum instanceof Boolean) {
                        point.addField(column + "_" + j, (Boolean) datum);
                    }
                }
            } else if (Objects.equals(type, Boolean.class)) {
                point.addField(column, (Boolean) declaredField.get(contentData));
            } else if (Objects.equals(type, Integer.class)) {
                point.addField(column, (Integer) declaredField.get(contentData));
            } else if (Objects.equals(type, Double.class)) {
                Double tmp = (Double) declaredField.get(contentData);
                double value = Double.parseDouble(String.format("%.2f", tmp));
                point.addField(column, value);
            } else if (type.isArray()) {
                Object array = declaredField.get(contentData);
                if (NOT_RECORD_FIELDS.contains(column) || array == null) {
                    continue;
                }
                Class<?> componentType = type.getComponentType();
                if (componentType == Double.class) {
                    Double[] dataArray = (Double[]) array;
                    for (int j = 0; j < dataArray.length; j++) {
                        double value = dataArray[j];
                        point.addField(column + "_" + j, String.format("%.2f", value));
                    }
                } else if (componentType == Boolean.class) {
                    Boolean[] booleanArray = (Boolean[]) array;
                    for (int j = 0; j < booleanArray.length; j++) {
                        point.addField(column + "_" + j, booleanArray[j]);
                    }
                } else if (componentType == Integer.class) {
                    Integer[] intArray = (Integer[]) array;
                    for (int j = 0; j < intArray.length; j++) {
                        point.addField(column + "_" + j, intArray[j]);
                    }
                }
            } else if (type == Map.class) {
                @SuppressWarnings("unchecked")
                Map<String, Double> data = (Map<String, Double>) declaredField.get(contentData);
                if (data != null) {
                    for (Map.Entry<String, Double> entry : data.entrySet()) {
                        String key = entry.getKey();
                        double value = entry.getValue();
                        if (CUSTOM_PREFIX_FIELDS.contains(key)) {
                            // 你得问 go 为什么 这里接口返回不加custom_
                            // .............................................................
                            point.addField(
                                    "custom_" + column + "_" + key, String.format("%.2f", value));
                        } else {

                            point.addField(column + "_" + key, String.format("%.2f", value));
                        }
                    }
                }
            }
            // 设置时间
            point.time(time, WritePrecision.MS);
        }
        return point;
    }

    private void publishControllableEvent(
            SteerableData steerableData, List<String> hideEmsEventCodes) {
        String controllableId = steerableData.getUuid();
        SteerableContentData steerableContentData = steerableData.getInformation();
        SteerableContentData lastSteerableContentData = steerableContentDataMap.get(controllableId);
        ControllableEntity controllableEntity = controllableService.getById(controllableId);
        if (controllableEntity == null
                || steerableContentData == null
                || lastSteerableContentData == null) {
            return;
        }
        ControllableStateChange controllableStateChangeTemp = new ControllableStateChange();
        controllableStateChangeTemp.setControllableId(controllableId);
        controllableStateChangeTemp.setControllableName(controllableEntity.getName());
        controllableStateChangeTemp.setVendor(controllableEntity.getVendor());
        controllableStateChangeTemp.setMaintain(controllableEntity.getMaintain());
        controllableStateChangeTemp.setProjectId(projectId);
        controllableStateChangeTemp.setTypeCode(steerableContentData.getDeviceTypeCode());
        Optional.ofNullable(steerableContentData.getStateCode())
                .ifPresent(
                        stateCode -> {
                            Integer[] lastStateCode = lastSteerableContentData.getStateCode();
                            pushEvent(
                                    stateCode,
                                    lastStateCode,
                                    controllableStateChangeTemp,
                                    EventLevelEnum.STATE,
                                    hideEmsEventCodes);
                        });
        Optional.ofNullable(steerableContentData.getAlarmCode())
                .ifPresent(
                        stateCode -> {
                            Integer[] lastStateCode = lastSteerableContentData.getAlarmCode();
                            pushEvent(
                                    stateCode,
                                    lastStateCode,
                                    controllableStateChangeTemp,
                                    EventLevelEnum.ALARM,
                                    hideEmsEventCodes);
                        });
        Optional.ofNullable(steerableContentData.getFaultCode())
                .ifPresent(
                        stateCode -> {
                            Integer[] lastStateCode = lastSteerableContentData.getFaultCode();
                            pushEvent(
                                    stateCode,
                                    lastStateCode,
                                    controllableStateChangeTemp,
                                    EventLevelEnum.FAULT,
                                    hideEmsEventCodes);
                        });
    }

    private void pushEvent(
            Integer[] stateCode,
            Integer[] lastStateCode,
            ControllableStateChange controllableStateChangeTemp,
            EventLevelEnum eventLevelEnum,
            List<String> hideEmsEventCodes) {
        for (int i = 0; i < stateCode.length; i++) {
            Integer state = stateCode[i];
            Integer lastState = lastStateCode[i];
            ControllableStateChange controllableStateChange = new ControllableStateChange();
            BeanUtils.copyProperties(controllableStateChangeTemp, controllableStateChange);
            String diff = Integer.toBinaryString(state ^ lastState);
            if ("0".equals(diff)) {
                continue;
            }
            log.info("原始数据：{}，新采集数据{}", lastState, state);
            controllableStateChange.setBinaryString(diff);
            controllableStateChange.setValue(state);
            controllableStateChange.setLastValue(lastState);
            controllableStateChange.setIndex(i);
            controllableStateChange.setLevel(eventLevelEnum.getLevel());
            controllableStateChange.setHideEventCodes(hideEmsEventCodes);
            dataEventPublishService.publishStateChangeEvent(controllableStateChange);
        }
    }
}
