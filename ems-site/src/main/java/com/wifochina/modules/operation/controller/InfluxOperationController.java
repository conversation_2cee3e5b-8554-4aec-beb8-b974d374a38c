package com.wifochina.modules.operation.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.page.Result;
import com.wifochina.modules.income.caculate.pvwind.IPvWindIncomeCalculateService;
import com.wifochina.modules.income.query.pvwind.RenewableIncomeQueryService;
import com.wifochina.modules.operation.request.GeneratePvWindRequest;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-11-01 8:08 PM
 */
@Slf4j
@RestController
@RequestMapping("/operation")
@Api(tags = "10-运营收益")
@Deprecated
public class InfluxOperationController {

    @Resource private ProjectService projectService;
    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource private RenewableIncomeQueryService renewableIncomeQueryService;
    @Resource private IPvWindIncomeCalculateService IPvWindIncomeCalculateService;

    @Deprecated
    @PostMapping("/generatePvWindRecords")
    @ApiOperation("生成pvwind历史记录")
    @PreAuthorize("hasAuthority('/operation/getProfit')")
    public Result<Object> generatePvWindRecords(@RequestBody GeneratePvWindRequest request) {
        List<ProjectEntity> list = new ArrayList<>();
        if (request.getRange() == null
                && request.getSpec() == null
                && request.getProjectId() == null) {
            // 如果都没有 那就直接从 项目的初始开始 并且是所有项目
            list =
                    projectService.list(
                            Wrappers.lambdaQuery(ProjectEntity.class)
                                    .eq(ProjectEntity::getWhetherDelete, false));
        }

        if (request.getProjectId() != null) {
            // 查询特定的项目 处理
            list =
                    projectService.list(
                            Wrappers.lambdaQuery(ProjectEntity.class)
                                    .eq(ProjectEntity::getWhetherDelete, false)
                                    .eq(ProjectEntity::getId, request.getProjectId()));
        }

        log.info("{} -->正在执行", Thread.currentThread().getName() + " generatePvWindRecords");
        // 遍历每一个想 然后去循环 从项目初始化开始执行 需要判断是否已经执行过
        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                continue;
            }
            ZoneId zoneId = ZoneId.of(projectEntity.getTimezone());
            List<LocalDate> dateList = new ArrayList<>();
            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            // 将字符串解析为LocalDate对象
            // 这里把 特定的 放在前面
            if (request.getSpec() != null) {
                LocalDate specDate1 = LocalDate.parse(request.getSpec().getSpecDate(), formatter);
                dateList.add(specDate1);
            } else if (request.getRange() != null) {
                LocalDate from = LocalDate.parse(request.getRange().getFrom(), formatter);
                LocalDate end = LocalDate.parse(request.getRange().getEnd(), formatter);
                // 处理 时间段范围内的
                long between = ChronoUnit.DAYS.between(from, end);
                for (int i = 0; i < between; i++) {
                    LocalDate date = from.plusDays(i);
                    dateList.add(date);
                }
            } else {
                // 哪一天的数据
                // 获取到项目的 初始化时间
                Long createTime = projectEntity.getCreateTime();
                Instant instant = Instant.ofEpochSecond(createTime);
                ZonedDateTime zonedDateTime = instant.atZone(zoneId);
                LocalDate startTime = zonedDateTime.toLocalDate();
                LocalDate now = LocalDate.now(zoneId);
                long between = ChronoUnit.DAYS.between(startTime, now);
                for (int i = 0; i < between; i++) {
                    LocalDate date = startTime.plusDays(i);
                    dateList.add(date);
                }
            }
            log.info("generatePvWindRecords 初始化job 的开始时间是  startTime:{}", dateList.get(0));
            for (int i = 0; i < dateList.size(); i++) {
                LocalDate date = dateList.get(i);
                int finalI = i;
                threadPoolTaskExecutor.submit(
                        () -> {
                            long oneDayZero = date.atStartOfDay(zoneId).toEpochSecond();
                            RenewableProfitEntity p =
                                    renewableIncomeQueryService.getOne(
                                            new LambdaQueryWrapper<RenewableProfitEntity>()
                                                    .eq(RenewableProfitEntity::getTime, oneDayZero)
                                                    .eq(
                                                            RenewableProfitEntity::getRenewableType,
                                                            MeterTypeEnum.PV.name())
                                                    .eq(
                                                            RenewableProfitEntity::getProjectId,
                                                            projectEntity.getId()));
                            if (p == null) {
                                // 以初始化时间作为起始时间 开始进行缓存 需要判断是否已经缓存过
                                RenewableProfitEntity pvProfitEntity =
                                        IPvWindIncomeCalculateService.calculatePvWindProfit(
                                                date,
                                                MeterTypeEnum.PV.name(),
                                                zoneId,
                                                projectEntity.getId());
                                if (pvProfitEntity != null) {
                                    renewableIncomeQueryService.saveOrUpdate(pvProfitEntity);
                                }
                            }

                            RenewableProfitEntity w =
                                    renewableIncomeQueryService.getOne(
                                            new LambdaQueryWrapper<RenewableProfitEntity>()
                                                    .eq(RenewableProfitEntity::getTime, oneDayZero)
                                                    .eq(
                                                            RenewableProfitEntity::getRenewableType,
                                                            MeterTypeEnum.WIND.name())
                                                    .eq(
                                                            RenewableProfitEntity::getProjectId,
                                                            projectEntity.getId()));
                            if (w == null) {
                                RenewableProfitEntity windProfitEntity =
                                        IPvWindIncomeCalculateService.calculatePvWindProfit(
                                                date,
                                                MeterTypeEnum.WIND.name(),
                                                zoneId,
                                                projectEntity.getId());
                                renewableIncomeQueryService.saveOrUpdate(windProfitEntity);
                                if (windProfitEntity != null) {
                                    renewableIncomeQueryService.saveOrUpdate(windProfitEntity);
                                }
                            }

                            log.info(
                                    "projectName: {} finish date:{} pvOrWind Profit 第{}个日期 总共:{}",
                                    projectEntity.getProjectName(),
                                    date,
                                    finalI + 1,
                                    dateList.size());
                        });
            }
        }
        return Result.success();
    }
}
