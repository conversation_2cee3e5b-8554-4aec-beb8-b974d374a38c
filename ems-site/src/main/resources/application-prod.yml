spring:
  datasource:
    #url: jdbc:p6spy:${DATABASE_HOST:mysql://**************:3306}/${DATABASE_NAME:ems}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull
    url: jdbc:${DATABASE_HOST}/${DATABASE_NAME:cloud}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useLegacyDatetimeCode=false&serverTimezone=UTC
    # url: jdbc:p6spy:${DATABASE_HOST:mysql://**************:3306}/${DATABASE_NAME:cloud}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useLegacyDatetimeCode=false&serverTimezone=UTC
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
    type: com.alibaba.druid.pool.DruidDataSource
    #driver-class-name : com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    #password: ${REDIS_PASSWORD:wq2018!}
    database: ${REDIS_DATABASE:8}
    timeout: 10000
    jedis:
      pool:
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 1
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1

ems:
  gateway: ${GAREWAY_URL:http://**************:9091}
  adUrl: ${AD_URL:https://vpn.weiheng-tech.com:21170}/auth/ldap
  version: ${EMS_VERSION:v2.1.7}
  influx:
    #influxdb服务器的地址
    url: ${TSDB_URL}
    token: ${TSDB_TOKEN}
    org: wifo
    bucket: ems
    bucketgroup: ems_group_fvcc

  collect:
    data:
      url: ${DATA_URL:http://{ip}:{port}/api/modbus?start={start}&count={count}&type={type}}
      #一次采集数据最大值，默认10000
      count: 65535
    project:
      id: ${PROJECT_ID:4f537620d37d40e19dd25be5ca6ad941}
