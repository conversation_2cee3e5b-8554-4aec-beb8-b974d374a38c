spring:
  datasource:
    url: jdbc:${DATABASE_HOST:mysql://**************:3306}/${DATABASE_NAME:cloud}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull
    username: ${DATABASE_USERNAME:root}
    password: ${DATABASE_PASSWORD:123@abcd}
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: ${REDIS_HOST:**************}
    port: ${REDIS_PORT:6379}
    #password: ${REDIS_PASSWORD:wq2018!}
    database: ${REDIS_DATABASE:8}
    timeout: 10000
    jedis:
      pool:
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 1
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1

ems:
  gateway: ${GAREWAY_URL:http://**************:9091}
  adUrl: ${AD_URL:http://ldap-login.dev.weiheng-tech.com}/auth/ldap
  version: ${EMS_VERSION:v2.1.6}
  influx:
    #influxdb服务器的地址
    url: ${TSDB_URL:http://**************:8086}
    token: ${TSDB_TOKEN:sLbiSBACzk058f1TFlJJeYKbQQmLjlsozyn9IGCOJfmb9RtA-XYx1VrrVNjnKWgBR6vX65Y7yNasnkaQWcbh6g==}
    org: wifo
    bucket: ems
    bucketgroup: ems_group_fvcc

  collect:
    data:
      url: ${DATA_URL:http://{ip}:{port}/api/modbus?start={start}&count={count}&type={type}}
      #一次采集数据最大值，默认10000
      count: 65535
    project:
      id: ${PROJECT_ID:4f537620d37d40e19dd25be5ca6ad941}

logging:
  level:
    com.wifochina: ${LOG_LEVEL:info}
