<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.wifochina</groupId>
        <artifactId>ems-server</artifactId>
        <version>0.1-SNAPSHOT</version>
    </parent>

    <artifactId>ems-site</artifactId>
    <version>0.1-SNAPSHOT</version>
    <name>ems-site</name>
    <description>ems site project</description>

    <properties>
        <java.version>11</java.version>
    </properties>

    <dependencies>
        <!-- WEB 框 架 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wifochina</groupId>
            <artifactId>ems-common</artifactId>
            <version>0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wifochina</groupId>
            <artifactId>ems-income</artifactId>
            <version>0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>influxdb-client-java</artifactId>
            <!--            <version>${influxdb-java.version}</version>-->
            <!--            <version>6.10.0</version>-->
        </dependency>
        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>flux-dsl</artifactId>
            <version>6.10.0</version>
        </dependency>
        <dependency>
            <artifactId>okhttp</artifactId>
            <groupId>com.squareup.okhttp3</groupId>
            <version>3.14.9</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>

</project>
