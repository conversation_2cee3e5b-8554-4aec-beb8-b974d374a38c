package com.wifochina.modules.datacalibration.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.electric.ElectricAdapterChooser;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.income.ElectricProfitService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.request.DataCalibrationCommon;
import com.wifochina.modules.remedies.request.DataCalibrationEms;
import com.wifochina.modules.remedies.request.DataCalibrationRenewable;
import com.wifochina.modules.remedies.service.DataCalibrationService;
import com.wifochina.modules.renewable.RenewableAdapterChooser;
import com.wifochina.modules.renewable.RenewableAdapterService;
import com.wifochina.modules.renewable.entity.RenewableDynamicProfitEntity;
import com.wifochina.modules.renewable.entity.RenewablePeriod;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.renewable.request.RenewableProfitOneRequest;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created on 2024/6/20 10:59.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class DataCalibrationServiceImpl implements DataCalibrationService {

    private final ProjectService projectService;
    private final ElectricPriceService electricPriceService;
    private final RenewableAdapterChooser chooser;
    private final ElectricAdapterChooser electricAdapterChooser;
    private final ElectricProfitService electricProfitService;

    @Override
    public void emsDataCalibration(String projectId, DataCalibrationEms dataCalibrationEms) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        dataCalibrationEms.setProjectId(projectId);
        // 把 date转换成 对应 时间戳
        LocalDate time =
                LocalDate.parse(dataCalibrationEms.getDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        long timeZero = time.atStartOfDay(ZoneId.of(projectEntity.getTimezone())).toEpochSecond();
        dataCalibrationEms.setTime(timeZero);
        // 根据这个日期 找到 找到 那个匹配到的 电价配置策略
        ElectricPriceEntity electricPriceEntity =
                electricPriceService.getElectricPriceMatch(projectEntity.getId(), timeZero);
        if (electricPriceEntity != null) {
            try {
                // 这里保持chooser 目前可能只有 国内的尖峰平谷 会有这个 for DataCalibration (数据校准) 操作
                electricAdapterChooser
                        .choose(projectEntity.getElectricPriceType())
                        .saveElectricForDataCalibration(
                                new ElectricAdapterService.ElectricAdapterContext() {
                                    @Override
                                    public String groupId() {
                                        return dataCalibrationEms.getGroupId();
                                    }

                                    @Override
                                    public long timePoint() {
                                        return timeZero;
                                    }

                                    @Override
                                    public DataCalibrationEms dataCalibration() {
                                        return dataCalibrationEms;
                                    }

                                    @Override
                                    public List<ElectricPriceEntity> periodPriceList() {
                                        return electricPriceEntity.getPeriodPriceList();
                                    }

                                    @Override
                                    public ProjectEntity project() {
                                        return projectEntity;
                                    }
                                });
            } catch (Exception e) {
                log.error("项目 {} 保存电池收益收益异常 {}", projectEntity.getProjectName(), e.getMessage());
            }
        }
    }

    @Override
    public ElectricProfitVO getEmsDataCalibration(String projectId, DataCalibrationCommon common) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 把 date转换成 对应 时间戳
        LocalDate time = LocalDate.parse(common.getDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        long timeZero = time.atStartOfDay(ZoneId.of(projectEntity.getTimezone())).toEpochSecond();
        ProfitRequest profitRequest = new ProfitRequest();
        profitRequest.setStartDate(timeZero);
        profitRequest.setEndDate(timeZero + 1);
        profitRequest.setDeviceId(EmsConstants.ALL);
        profitRequest.setGroupId(common.getGroupId());
        profitRequest.setProjectId(projectId);
        return electricProfitService.getElectricTotalProfit(profitRequest);
        //        return operationProfitService.getBatteryProfit(profitRequest, projectId, null);
    }

    /**
     * pv wind 的数据校准接口
     *
     * <p>把前端传的字符串日期 转换成 对应时区的 时间戳
     *
     * <p>根据这个日期 找到对应的 电价配置策略
     *
     * <p>根据chooser 去执行 calculateSaveForDataCalibration 根据提交的电量信息 去和价格做计算 得到PvWind对应的缓存数据 存入数据库,
     * 目前没有实现动态电价的
     *
     * @param data : 数据校准 pv wind 的数据 data
     */
    @Override
    public void renewableDataCalibration(String projectId, DataCalibrationRenewable data) {
        CalculateTypeEnum calculateTypeEnum = CalculateTypeEnum.valueOf(data.getRenewableType());
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 把 date转换成 对应 时间戳
        LocalDate time = LocalDate.parse(data.getDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        long timeZero = time.atStartOfDay(ZoneId.of(projectEntity.getTimezone())).toEpochSecond();
        // 根据这个日期 找到 找到 那个匹配到的 电价配置策略
        ElectricPriceEntity electricPriceEntity =
                electricPriceService.getElectricPriceMatch(projectEntity.getId(), timeZero);
        if (electricPriceEntity != null) {
            try {
                // 这里保持chooser 目前可能只有 国内的尖峰平谷 会有这个 for DataCalibration (数据校准) 操作
                chooser.choose(projectEntity.getElectricPriceType())
                        .calculateSaveForDataCalibration(
                                time,
                                calculateTypeEnum.name(),
                                new RenewableAdapterService
                                        .RenewableDataCalibrationAdapterContext() {
                                    @Override
                                    public DataCalibrationRenewable dataCalibration() {
                                        return data;
                                    }

                                    @Override
                                    public ElectricPriceEntity electricPriceParent() {
                                        return electricPriceEntity;
                                    }

                                    @Override
                                    public ProjectEntity project() {
                                        return projectEntity;
                                    }
                                });
            } catch (Exception e) {
                log.error("项目 {} 保存昨日PV风电收益异常 {}", projectEntity.getProjectName(), e.getMessage());
            }
        }
    }

    /**
     * 用于 查询缓存中的 数据 根据指定的日期和type : CalculateTypeEnum
     *
     * @param date : 指定的日期
     * @param type : CalculateTypeEnum...
     * @return : List<T> list 是因为为了以后扩展动态电价的 , 动态电价 可能需要返回多条时段对应的数据
     */
    @Override
    public <T extends RenewablePeriod> List<T> getRenewableData(String date, String type) {
        LocalDate time = LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE);
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        long timeZero = time.atStartOfDay(ZoneId.of(projectEntity.getTimezone())).toEpochSecond();
        CalculateTypeEnum calculateTypeEnum = CalculateTypeEnum.valueOf(type);
        List<T> result = new ArrayList<>();

        // 使用chooser 为了以后支持 海外的
        chooser.choose(projectEntity.getElectricPriceType())
                .queryPvOrWindData(
                        new RenewableProfitOneRequest()
                                // 支持 pv or wind
                                .setType(calculateTypeEnum.name())
                                .setDataCalibrationFlag(false)
                                .setProjectId(WebUtils.projectId.get())
                                .setTime(timeZero),
                        new RenewableAdapterService.QueryRenewableResult() {
                            @Override
                            public void peakValleysPeriodPostProcessor(
                                    List<RenewableProfitEntity> t) {
                                if (CollectionUtil.isNotEmpty(t)) {
                                    // 1.3.7 支持 回显 缓存的 尖峰平谷的 pv wind 数据
                                    RenewableProfitEntity renewableProfitEntity = t.get(0);
                                    result.add((T) renewableProfitEntity);
                                }
                            }

                            @Override
                            public void dynamicPeriodPostProcessor(
                                    List<RenewableDynamicProfitEntity> t) {
                                // TODO  还未实现 海外的 固定电价和实时电价 口子先留着
                                result.addAll((Collection<? extends T>) t);
                            }
                        });

        return result;
    }
}
