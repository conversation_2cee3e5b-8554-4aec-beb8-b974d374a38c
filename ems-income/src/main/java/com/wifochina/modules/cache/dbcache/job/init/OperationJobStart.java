package com.wifochina.modules.cache.dbcache.job.init;

import com.wifochina.modules.operation.job.OperationMonthReportJob;
import com.wifochina.modules.operation.job.OperationYearReportJob;
import com.wifochina.modules.operation.service.impl.OperationMonthQuartzServiceImpl;
import com.wifochina.modules.operation.service.impl.OperationYearQuartzServiceImpl;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-15 8:29 PM
 */
@Component
public class OperationJobStart {
    @Resource private ProjectService projectService;

    @Resource private OperationMonthQuartzServiceImpl operationMonthQuartzServiceImpl;

    @Resource private OperationYearQuartzServiceImpl operationYearQuartzServiceImpl;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        List<ProjectEntity> projectEntities =
                projectService.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list();
        List<String> timezoneList =
                projectEntities.stream()
                        .map(ProjectEntity::getTimezone)
                        .distinct()
                        .collect(Collectors.toList());
        for (String timezone : timezoneList) {
            operationMonthQuartzServiceImpl.addOperationJob(
                    timezone, OperationMonthReportJob.class);
            operationYearQuartzServiceImpl.addOperationJob(timezone, OperationYearReportJob.class);
        }
    }
}
