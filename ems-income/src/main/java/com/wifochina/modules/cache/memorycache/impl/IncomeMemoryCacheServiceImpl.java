package com.wifochina.modules.cache.memorycache.impl;

import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.collect.ElectricCollectVo;
import com.wifochina.modules.income.AbstractProfitService;
import com.wifochina.modules.income.ElectricProfitService;
import com.wifochina.modules.income.RenewableProfitService;
import com.wifochina.modules.income.cache.memorycache.IIncomeMemoryCacheService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.income.timer.TaskOperationTimer;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.project.entity.ProjectEntity;

import com.wifochina.modules.report.entity.DayReportEntity;
import com.wifochina.modules.report.service.DayReportCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.stream.DoubleStream;

/**
 * <AUTHOR>
 * @since 2023-03-27 4:22 PM
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IncomeMemoryCacheServiceImpl implements IIncomeMemoryCacheService {

    private final AbstractProfitService abstractProfitService;
    private final RenewableProfitService renewableProfitService;
    private final ElectricProfitService electricProfitService;
    private final DayReportCacheService dayReportCacheService;

    @Override
    public void updateElectricProfitVO(
            String projectId, String projectName, Long projectInitTime, String timeZoneCode) {
        ProfitRequest profitRequest = new ProfitRequest();
        profitRequest.setStartDate(projectInitTime);
        profitRequest.setGroupId(EmsConstants.ALL);
        profitRequest.setDeviceId(EmsConstants.ALL);
        long todayZero = MyTimeUtil.getTodayZeroTime(timeZoneCode);
        profitRequest.setEndDate(todayZero);
        ElectricProfitVO electricProfitVO = new ElectricProfitVO();
        electricProfitVO.init();
        if (projectInitTime < todayZero) {
            log.info(
                    "updateElectricProfitVO#---> {}",
                    "收益计算正在执行 " + projectId + " : " + projectName);

            profitRequest.setProjectId(projectId);
            electricProfitVO = electricProfitService.getElectricTotalProfit(profitRequest);
            //                    operationProfitService.getBatteryProfit(profitRequest, projectId,
            // null);
            if (electricProfitVO.getTotalChargeQuantity() != null
                    && electricProfitVO.getTotalChargeQuantity() != 0) {
                electricProfitVO.setLastUpdateTime(todayZero);
            }
            log.info(
                    "updateElectricProfitVO#---> 【收益已经完成】- {} | electric total:{}",
                    projectId + " : " + projectName,
                    electricProfitVO.getTotalBenefit());
        }
        TaskOperationTimer.totalElectricMap.put(projectId, electricProfitVO);
        ElectricProfitVO yesElectricProfitVO = new ElectricProfitVO();
        yesElectricProfitVO.init();
        if (projectInitTime <= todayZero - MyTimeUtil.ONE_DAY_SECONDS) {
            profitRequest.setStartDate(todayZero - MyTimeUtil.ONE_DAY_SECONDS);
            profitRequest.setEndDate(todayZero);
            profitRequest.setProjectId(projectId);
            yesElectricProfitVO = electricProfitService.getElectricTotalProfit(profitRequest);
            log.info(
                    "updateElectricProfitVO#---> 昨天收益 - {} | yesterday electric:{}",
                    projectId + " : " + projectName,
                    yesElectricProfitVO.getTotalBenefit());
        }
        yesElectricProfitVO.setLastUpdateTime(todayZero);
        TaskOperationTimer.yesElectricMap.put(projectId, yesElectricProfitVO);

        // 2023-10-09 计算 月度的收益 存入map
        ElectricProfitVO monthElectricProfitVO = new ElectricProfitVO();
        monthElectricProfitVO.init();
        // 2023-10-01:00:00:00 -> 今天零点
        if (projectInitTime < todayZero) {
            if (projectInitTime > MyTimeUtil.getCurrentMonthZeroTime(timeZoneCode)) {
                profitRequest.setStartDate(projectInitTime);
            } else {
                profitRequest.setStartDate(MyTimeUtil.getCurrentMonthZeroTime(timeZoneCode));
            }
            profitRequest.setEndDate(todayZero);
            profitRequest.setProjectId(projectId);
            monthElectricProfitVO = electricProfitService.getElectricTotalProfit(profitRequest);
            monthElectricProfitVO.setLastUpdateTime(todayZero);
        } else {
            // 上面已经.init了
        }
        // 月度收益 保存到 monthElectricMap 中
        TaskOperationTimer.monthElectricMap.put(projectId, monthElectricProfitVO);
    }

    @Override
    public void updatePvVo(
            String projectId, String projectName, Long projectInitTime, String timeZoneCode) {
        BenefitRequest benefitRequest = new BenefitRequest();
        long oneDaySecond = 86400;
        benefitRequest.setStartDate(projectInitTime);
        long todayZero = MyTimeUtil.getTodayZeroTime(timeZoneCode);
        benefitRequest.setEndDate(todayZero - 1);
        ProfitVO profitVO = new ProfitVO();
        if (projectInitTime < todayZero) {
            log.debug(
                    "TaskOperation#updatePvVo---> {}",
                    "pv update正在执行 " + projectId + " : " + projectName);
            profitVO =
                    renewableProfitService.getRenewableProfitVo(
                            benefitRequest, projectId, CalculateTypeEnum.PV);
            //            profitVO = abstractProfitService.getPvProfitVO(benefitRequest, projectId);
            log.info(
                    "TaskOperation#updatePvVo---> pv update 完成- {} | pv total: {}",
                    projectId + " : " + projectName,
                    profitVO.getPv_discharge_quantity());
        } else {
            profitVO.setPv_discharge_quantity("0");
            profitVO.setPv_discharge_benefit("0");
        }
        TaskOperationTimer.totalPvMap.put(projectId, profitVO);
        benefitRequest.setStartDate(todayZero - oneDaySecond);
        // 昨天的59时间
        benefitRequest.setEndDate(todayZero - 1);
        ProfitVO profitYesVO = new ProfitVO();
        if (projectInitTime <= todayZero - oneDaySecond) {
            log.debug("getPvProfitVO benefitRequest {}, projectId:{}", benefitRequest, projectId);
            profitYesVO =
                    renewableProfitService.getRenewableProfitVo(
                            benefitRequest, projectId, CalculateTypeEnum.PV);

        } else {
            profitYesVO.setPv_discharge_quantity("0");
            profitYesVO.setPv_discharge_benefit("0");
        }
        log.debug("profitYesVo: {}", profitYesVO);
        TaskOperationTimer.yesPvMap.put(projectId, profitYesVO);

        // 查询 当前月份
        benefitRequest.setEndDate(todayZero);
        ProfitVO profitMonthVO = new ProfitVO();
        // 初始化时间小于 当前零点时间 并且 今天不是月份第一天 这个限制去掉了
        if (projectInitTime < todayZero) {
            if (projectInitTime > MyTimeUtil.getCurrentMonthZeroTime(timeZoneCode)) {
                benefitRequest.setStartDate(projectInitTime);
            } else {
                benefitRequest.setStartDate(MyTimeUtil.getCurrentMonthZeroTime(timeZoneCode));
            }
            profitMonthVO =
                    renewableProfitService.getRenewableProfitVo(
                            benefitRequest, projectId, CalculateTypeEnum.PV);

        } else {
            profitMonthVO.setPv_discharge_quantity("0");
            profitMonthVO.setPv_discharge_benefit("0");
        }
        TaskOperationTimer.monthPvMap.put(projectId, profitMonthVO);
    }

    @Override
    @Async("asyncServiceExecutor")
    public void updateWindVo(
            String projectId, String projectName, Long projectInitTime, String timeZoneCode) {
        BenefitRequest benefitRequest = new BenefitRequest();
        long oneDaySecond = 86400;
        benefitRequest.setStartDate(projectInitTime);
        long todayZero = MyTimeUtil.getTodayZeroTime(timeZoneCode);
        benefitRequest.setEndDate(todayZero);
        ProfitVO profitVO = new ProfitVO();
        if (projectInitTime < todayZero) {
            log.info(
                    "TaskOperation#updateWindVo---> {}",
                    "wind update正在执行 " + projectId + " : " + projectName);
            profitVO =
                    renewableProfitService.getRenewableProfitVo(
                            benefitRequest, projectId, CalculateTypeEnum.WIND);
            //            profitVO = abstractProfitService.getWindProfitVO(benefitRequest,
            // projectId);
            log.info(
                    "TaskOperation#updateWindVo---> wind update正在完成- {} | wind total: {}",
                    Thread.currentThread().getName() + " " + projectId + " : " + projectName,
                    profitVO.getWind_discharge_quantity());
        } else {
            profitVO.setWind_discharge_quantity("0");
            profitVO.setWind_discharge_benefit("0");
        }
        TaskOperationTimer.totalWindMap.put(projectId, profitVO);
        benefitRequest.setStartDate(todayZero - oneDaySecond);
        ProfitVO profitYesVO = new ProfitVO();
        if (projectInitTime < todayZero - oneDaySecond) {
            profitYesVO =
                    renewableProfitService.getRenewableProfitVo(
                            benefitRequest, projectId, CalculateTypeEnum.WIND);
            //            profitYesVO = abstractProfitService.getWindProfitVO(benefitRequest,
            // projectId);
        } else {
            profitYesVO.setWind_discharge_quantity("0");
            profitYesVO.setWind_discharge_benefit("0");
        }
        TaskOperationTimer.yesWindMap.put(projectId, profitYesVO);

        benefitRequest.setStartDate(MyTimeUtil.getCurrentMonthZeroTime(timeZoneCode));
        ProfitVO profitMonthVO = new ProfitVO();
        if (projectInitTime < todayZero) {
            if (projectInitTime > MyTimeUtil.getCurrentMonthZeroTime(timeZoneCode)) {
                benefitRequest.setStartDate(projectInitTime);
            } else {
                benefitRequest.setStartDate(MyTimeUtil.getCurrentMonthZeroTime(timeZoneCode));
            }
            profitMonthVO =
                    renewableProfitService.getRenewableProfitVo(
                            benefitRequest, projectId, CalculateTypeEnum.WIND);
            // profitMonthVO = abstractProfitService.getWindProfitVO(benefitRequest, projectId);
        } else {
            profitMonthVO.setWind_discharge_quantity("0");
            profitMonthVO.setWind_discharge_benefit("0");
        }
        TaskOperationTimer.monthWindMap.put(projectId, profitMonthVO);
    }

    @Override
    public void updateDemandVoAsync(ProjectEntity projectEntity) {
        String projectId = projectEntity.getId();
        Long projectInitTime = projectEntity.getCreateTime();
        BenefitRequest benefitRequest = new BenefitRequest();
        benefitRequest.setStartDate(projectInitTime);
        benefitRequest.setEndDate(Instant.now().getEpochSecond());
        ProfitVO profitDemandVO =
                abstractProfitService.getDemandProfitVO(benefitRequest, projectId);
        TaskOperationTimer.demandControlMap.put(projectId, profitDemandVO);

        // month demandControl
        BenefitRequest monthBenefitRequest = new BenefitRequest();
        monthBenefitRequest.setStartDate(
                MyTimeUtil.getCurrentMonthZeroTime(projectEntity.getTimezone()));
        monthBenefitRequest.setEndDate(Instant.now().getEpochSecond());
        ProfitVO monthProfitDemandVO =
                abstractProfitService.getDemandProfitVO(monthBenefitRequest, projectId);
        TaskOperationTimer.monthDemandControlMap.put(projectId, monthProfitDemandVO);
    }

    /**
     * 缓存一下今日的 just 充放电 不和 收益挂钩的
     *
     * @param projectEntity : projectEntity
     */
    @Override
    public void updateTodayJustElectricVo(ProjectEntity projectEntity) {
        List<DayReportEntity> todayEmsDayReports =
                dayReportCacheService.findTodayEmsDayReports(
                        projectEntity,
                        new RangeRequest()
                                .setStartDate(
                                        MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone()))
                                .setEndDate(Instant.now().getEpochSecond()));
        double todayDischarge =
                todayEmsDayReports.stream().mapToDouble(DayReportEntity::getOutData).sum();
        double todayCharge =
                todayEmsDayReports.stream().mapToDouble(DayReportEntity::getInData).sum();
        TaskOperationTimer.todayElectricCollectMap.put(
                projectEntity.getId(), new ElectricCollectVo(todayDischarge, todayCharge));
    }
}
