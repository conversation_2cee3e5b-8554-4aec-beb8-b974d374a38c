package com.wifochina.modules.cache.dbcache.job;

import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.renewable.RenewableAdapterChooser;
import com.wifochina.modules.renewable.RenewableAdapterService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @since 2024-04-08 18:49:02 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class YestRenewableProfitJob extends QuartzJobBean {

    private final ProjectService projectService;
    private final ElectricPriceService electricPriceService;
    private final RenewableAdapterChooser chooser;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        String timezone = (String) context.getJobDetail().getJobDataMap().get("timezone");
        List<ProjectEntity> projectEntities = projectService.getProjectsByTimeZone(timezone);
        for (ProjectEntity projectEntity : projectEntities) {
            String projectId = projectEntity.getId();
            long yesterdayZero =
                    MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone())
                            - EmsConstants.ONE_DAY_SECOND;
            // 找到 那个匹配到的 电价配置策略
            ElectricPriceEntity electricPriceEntity =
                    electricPriceService.getElectricPriceMatch(projectId, yesterdayZero);
            if (electricPriceEntity != null) {
                try {
                    chooser.choose(projectEntity.getElectricPriceType())
                            .saveRenewable(
                                    new RenewableAdapterService.RenewableAdapterContext() {
                                        @Override
                                        public ElectricPriceEntity electricPriceParent() {
                                            return electricPriceEntity;
                                        }

                                        @Override
                                        public ProjectEntity project() {
                                            return projectEntity;
                                        }
                                    });
                } catch (Exception e) {
                    log.error(
                            "项目 {} 保存昨日PV风电收益异常 {}",
                            projectEntity.getProjectName(),
                            e.getMessage());
                }
            }
        }
    }
}
