package com.wifochina.modules.cache.dbcache.electric.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.util.*;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.ElectricService;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.group.entity.IdSearchSupport;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.caculate.electric.IElectricIncomeCalculateService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 尖峰平谷 时段的 电收益 serviceImpl <br>
 * Created on 2024/5/24 11:59.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class ElectricPeakValleysPeriodServiceImpl
        implements ElectricAdapterService<ElectricEntity> {

    private final ElectricService electricService;
    private final DeviceService deviceService;
    private final AmmeterService ammeterService;
    private final IElectricIncomeCalculateService electricIncomeCalculateService;
    private final GroupService groupService;

    public ElectricEntity getElectricEntity(
            ZonedDateTime timePointZonedDateTime, String projectId, String deviceId) {
        ElectricEntity electricEntity = new ElectricEntity();
        electricEntity.setTime(timePointZonedDateTime.toEpochSecond());
        electricEntity.setProjectId(projectId);
        LocalDateTime localDateTime = timePointZonedDateTime.toLocalDateTime();
        electricEntity.setYear(localDateTime.getYear());
        electricEntity.setMonth(localDateTime.getMonthValue());
        electricEntity.setDay(localDateTime.getDayOfMonth());
        electricEntity.setDeviceId(deviceId);
        electricEntity.setCalibrationType(ElectricCalibrationTypeEnum.ELECTRIC_ORIGINAL.getCode());
        return electricEntity;
    }

    /**
     * 通用的 记录保存 ,支持 device 和 meter 的
     *
     * @param context : ElectricAdapterContext
     * @param timeContext : TimeContext
     * @param idSearchSupports : 设备ids
     * @param sql : sql
     */
    private void recordCommonSave(
            ElectricAdapterContext context,
            TimeContext timeContext,
            List<? extends IdSearchSupport> idSearchSupports,
            String sql) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<ElectricPriceEntity> periodList = context.periodPriceList();
        String projectId = context.project().getId();
        if (!idSearchSupports.isEmpty()) {
            idSearchSupports.forEach(
                    idEntity -> {
                        log.info(
                                "peakValleys recordCommonSave timePoint:{} id:{} type:{}",
                                timeContext.getTimePointDate().format(formatter),
                                idEntity.id(),
                                idEntity.type());
                        // 这里是 尖峰平谷的 有对应字段存储 这些时段 所以是一个这个记录对象
                        ElectricEntity electricEntity =
                                getElectricEntity(
                                        timeContext.getTimePointDate(), projectId, idEntity.id());
                        for (ElectricPriceEntity priceEntity : periodList) {
                            String queryString = sql;
                            // queryString = queryString.replace("{deviceId}", idEntity.id());
                            queryString =
                                    queryString.replace(
                                            IdSearchUtils.PLACEHOLDER,
                                            IdSearchUtils.createIdSearchSqlForTimeSeries(
                                                    List.of(idEntity)));
                            // 根据不同的 时序service去执行 可以走lindorm 和 influx
                            electricIncomeCalculateService.calculateTimeChargeDiff(
                                    timeContext,
                                    priceEntity,
                                    context.project(),
                                    queryString,
                                    (outDiff, inDiff) ->
                                            calculatePeriod(
                                                    priceEntity, electricEntity, outDiff, inDiff));
                        }
                        calculateTotal(electricEntity);
                        // 这一段为了支持 重跑数据 能做update
                        ElectricEntity old =
                                electricService.getOne(
                                        new LambdaQueryWrapper<ElectricEntity>()
                                                .eq(
                                                        ElectricEntity::getProjectId,
                                                        electricEntity.getProjectId())
                                                .eq(ElectricEntity::getDeviceId, idEntity.id())
                                                .eq(
                                                        ElectricEntity::getTime,
                                                        electricEntity.getTime()));
                        if (old != null) {
                            electricEntity.setId(old.getId());
                        }
                        electricService.saveOrUpdate(electricEntity);
                    });
        }
    }

    //
    @Override
    public ElectricProfitVO queryElectricTotalProfit(
            ElectricRequest electricRequest, QueryElectricResult queryElectricResult) {
        return queryElectricResult.peakValleysPeriodPostProcessor(
                electricService.queryElectricTotalProfit(electricRequest));
    }

    @Override
    public void queryElectricEveryDayProfit(
            ElectricRequest electricRequest, QueryElectricsResult queryElectricsResult) {
        queryElectricsResult.peakValleysPeriodPostProcessor(
                electricService.queryElectricEveryDayProfit(electricRequest));
    }

    @Override
    public void deviceElectricSave(ElectricAdapterContext context, TimeContext timeContext) {
        recordCommonSave(
                context,
                timeContext,
                deviceService.findIncomeDevices(context.project().getId()),
                electricIncomeCalculateService.getElectricEmsSql(context.project()));
    }

    @Override
    public void electricSaveForDataCalibration(
            ElectricAdapterContext context, TimeContext timeContext) {
        // 先查记录有没有
        ElectricEntity electricEntity =
                electricService.queryElectricByGroupId(context.groupId(), context.timePoint());
        boolean isGroupEarningCondition =
                groupService.groupEarningCondition(context.project().getId(), null);
        // 删除原始的数据
        deleteOldCache(context, electricEntity, isGroupEarningCondition);
        ElectricEntity electric =
                getElectricEntity(
                        timeContext.getTimePointDate(),
                        context.project().getId(),
                        context.groupId());

        BeanUtils.copyProperties(context.dataCalibration(), electric);
        List<ElectricPriceEntity> periodPriceList =
                context.periodPriceList().stream()
                        .collect(
                                // value就是ElectricPriceEntity对象本身
                                // 选择去重依据的key，这里是period
                                Collectors.collectingAndThen(
                                        Collectors.toMap(
                                                ElectricPriceEntity::getPeriod,
                                                Function.identity(),
                                                // 如果有重复，保留第一个出现的实体
                                                (entity1, entity2) -> entity1),
                                        map -> new ArrayList<>(map.values())));

        for (ElectricPriceEntity priceEntity : periodPriceList) {
            calculatePeriod(priceEntity, electric, 0d, 0d);
        }
        calculateTotal(electric);
        electric.setId(electricEntity == null ? null : electricEntity.getId());
        electric.setDeviceId(context.groupId());
        electric.setCalibrationType(ElectricCalibrationTypeEnum.ELECTRIC_GROUP.getCode());
        String userId = (SecurityUtil.getUserId() == null ? "system" : SecurityUtil.getUserId());
        electric.setUserId(userId);
        electricService.saveOrUpdate(electric);
    }

    private void deleteOldCache(
            ElectricAdapterContext context,
            ElectricEntity electricEntity,
            boolean isGroupEarningCondition) {
        if (electricEntity != null) {
            List<String> deviceIds =
                    groupService.getIncomeDeviceAmmeterIdsForGroup(
                            context.project().getId(),
                            isGroupEarningCondition ? context.groupId() : EmsConstants.ALL);
            if (!deviceIds.isEmpty()) {
                List<ElectricEntity> electricEntities =
                        electricService
                                .lambdaQuery()
                                .eq(ElectricEntity::getTime, context.timePoint())
                                .in(ElectricEntity::getDeviceId, deviceIds)
                                .list();
                if (electricEntities != null && !electricEntities.isEmpty()) {
                    electricEntities.forEach(
                            electric ->
                                    electric.setCalibrationType(
                                            ElectricCalibrationTypeEnum.ELECTRIC_DELETE.getCode()));
                    electricService.saveOrUpdateBatch(electricEntities);
                }
            }
        }
    }

    @Override
    public void ammeterElectricSave(ElectricAdapterContext context, TimeContext timeContext) {
        recordCommonSave(
                context,
                timeContext,
                // 国内 开收益标签 or 开抄表的 电表 都会记录这个 前日缓存数据
                ammeterService.findIncomeOrReadingAmmeter(context.project().getId()),
                electricIncomeCalculateService.getElectricMeterSql(context.project()));
    }

    @Override
    public void calculatePeriod(
            ElectricPriceEntity price,
            ElectricEntity electricEntity,
            Double outDiff,
            Double inDiff) {
        Integer type = price.getPeriod();
        switch (type) {
                // 电价时段（谷1 平2 峰3 尖4）
            case 1:
                // 谷充电成本
                electricEntity.setVallyChargeQuantity(
                        electricEntity.getVallyChargeQuantity() + inDiff);
                double vallyChargeCost =
                        price.getBuyPrice() * electricEntity.getVallyChargeQuantity();
                electricEntity.setVallyChargeCost(vallyChargeCost);
                // 谷放电收益
                electricEntity.setVallyDischargeQuantity(
                        electricEntity.getVallyDischargeQuantity() + outDiff);
                double vallyDischargeBenefit =
                        price.getSellPrice() * electricEntity.getVallyDischargeQuantity();
                electricEntity.setVallyDischargeBenefit(vallyDischargeBenefit);
                electricEntity.setVallyBuyPrice(price.getBuyPrice());
                electricEntity.setVallySellPrice(price.getSellPrice());
                break;
            case 2:
                // 平充电成本
                electricEntity.setFlatChargeQuantity(
                        electricEntity.getFlatChargeQuantity() + inDiff);
                double flatChargeCost =
                        price.getBuyPrice() * electricEntity.getFlatChargeQuantity();
                electricEntity.setFlatChargeCost(flatChargeCost);
                // 平放电收益
                electricEntity.setFlatDischargeQuantity(
                        electricEntity.getFlatDischargeQuantity() + outDiff);
                double flatDischargeBenefit =
                        price.getSellPrice() * electricEntity.getFlatDischargeQuantity();
                electricEntity.setFlatDischargeBenefit(flatDischargeBenefit);
                electricEntity.setFlatBuyPrice(price.getBuyPrice());
                electricEntity.setFlatSellPrice(price.getSellPrice());
                break;
            case 3:
                // 峰充电成本
                electricEntity.setPeakChargeQuantity(
                        electricEntity.getPeakChargeQuantity() + inDiff);
                double peakChargeCost =
                        price.getBuyPrice() * electricEntity.getPeakChargeQuantity();
                electricEntity.setPeakChargeCost(peakChargeCost);
                // 峰放电收益
                electricEntity.setPeakDischargeQuantity(
                        electricEntity.getPeakDischargeQuantity() + outDiff);
                double peakDischargeBenefit =
                        price.getSellPrice() * electricEntity.getPeakDischargeQuantity();
                electricEntity.setPeakDischargeBenefit(peakDischargeBenefit);
                electricEntity.setPeakBuyPrice(price.getBuyPrice());
                electricEntity.setPeakSellPrice(price.getSellPrice());
                break;
            case 4:
                // 尖充电成本
                electricEntity.setTipChargeQuantity(electricEntity.getTipChargeQuantity() + inDiff);
                double tipChargeCost = price.getBuyPrice() * electricEntity.getTipChargeQuantity();
                electricEntity.setTipChargeCost(tipChargeCost);
                // 尖放电收益
                electricEntity.setTipDischargeQuantity(
                        electricEntity.getTipDischargeQuantity() + outDiff);
                double tipDischargeBenefit =
                        price.getSellPrice() * electricEntity.getTipDischargeQuantity();
                electricEntity.setTipDischargeBenefit(tipDischargeBenefit);
                electricEntity.setTipBuyPrice(price.getBuyPrice());
                electricEntity.setTipSellPrice(price.getSellPrice());
                break;
            case 5:
                // 深谷充电成本
                electricEntity.setDeepVallyChargeQuantity(
                        electricEntity.getDeepVallyChargeQuantity() + inDiff);
                double deepVallyChargeCost =
                        price.getBuyPrice() * electricEntity.getDeepVallyChargeQuantity();
                electricEntity.setDeepVallyChargeCost(deepVallyChargeCost);
                // 深谷放电收益
                electricEntity.setDeepVallyDischargeQuantity(
                        electricEntity.getDeepVallyDischargeQuantity() + outDiff);
                double deepVallyDischargeBenefit =
                        price.getSellPrice() * electricEntity.getDeepVallyDischargeQuantity();
                electricEntity.setDeepVallyDischargeBenefit(deepVallyDischargeBenefit);
                electricEntity.setDeepVallyBuyPrice(price.getBuyPrice());
                electricEntity.setDeepVallySellPrice(price.getSellPrice());
                break;
            default:
        }
    }

    @Override
    public void calculatePeriodWithElectricProfitVo(
            ElectricPriceEntity price,
            ElectricProfitVO electricProfitVO,
            Double outDiff,
            Double inDiff) {
        Integer type = price.getPeriod();
        switch (type) {
                // 电价时段（谷1 平2 峰3 尖4）
            case 1:
                // 充电成本
                electricProfitVO.setVallyChargeQuantity(
                        electricProfitVO.getVallyChargeQuantity() + inDiff);
                double vallyChargeCost =
                        price.getBuyPrice() * electricProfitVO.getVallyChargeQuantity();
                electricProfitVO.setVallyChargeCost(vallyChargeCost);
                // 放电收益
                electricProfitVO.setVallyDischargeQuantity(
                        electricProfitVO.getVallyDischargeQuantity() + outDiff);
                double vallyDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getVallyDischargeQuantity();
                electricProfitVO.setVallyDischargeBenefit(vallyDischargeBenefit);
                break;
                // 电价时段（谷1 平2 峰3 尖4）
            case 2:
                // 充电成本
                electricProfitVO.setFlatChargeQuantity(
                        electricProfitVO.getFlatChargeQuantity() + inDiff);
                double flatChargeCost =
                        price.getBuyPrice() * electricProfitVO.getFlatChargeQuantity();
                electricProfitVO.setFlatChargeCost(flatChargeCost);
                // 放电收益
                electricProfitVO.setFlatDischargeQuantity(
                        electricProfitVO.getFlatDischargeQuantity() + outDiff);
                double flatDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getFlatDischargeQuantity();
                electricProfitVO.setFlatDischargeBenefit(flatDischargeBenefit);
                break;
                // 电价时段（谷1 平2 峰3 尖4）
            case 3:
                // 充电成本
                electricProfitVO.setPeakChargeQuantity(
                        electricProfitVO.getPeakChargeQuantity() + inDiff);
                double peakChargeCost =
                        price.getBuyPrice() * electricProfitVO.getPeakChargeQuantity();
                electricProfitVO.setPeakChargeCost(peakChargeCost);
                // 放电收益
                electricProfitVO.setPeakDischargeQuantity(
                        electricProfitVO.getPeakDischargeQuantity() + outDiff);
                double peakDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getPeakDischargeQuantity();
                electricProfitVO.setPeakDischargeBenefit(peakDischargeBenefit);
                break;
                // 电价时段（谷1 平2 峰3 尖4）
            case 4:
                // 充电成本
                electricProfitVO.setTipChargeQuantity(
                        electricProfitVO.getTipChargeQuantity() + inDiff);
                double tipChargeCost =
                        price.getBuyPrice() * electricProfitVO.getTipChargeQuantity();
                electricProfitVO.setTipChargeCost(tipChargeCost);
                // 放电收益
                electricProfitVO.setTipDischargeQuantity(
                        electricProfitVO.getTipDischargeQuantity() + outDiff);
                double tipDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getTipDischargeQuantity();
                electricProfitVO.setTipDischargeBenefit(tipDischargeBenefit);
                break;
            case 5:
                // 充电成本
                electricProfitVO.setDeepVallyChargeQuantity(
                        electricProfitVO.getDeepVallyChargeQuantity() + inDiff);
                double deepVallyChargeCost =
                        price.getBuyPrice() * electricProfitVO.getDeepVallyChargeQuantity();
                electricProfitVO.setDeepVallyChargeCost(deepVallyChargeCost);
                // 放电收益
                electricProfitVO.setDeepVallyDischargeQuantity(
                        electricProfitVO.getDeepVallyDischargeQuantity() + outDiff);
                double deepVallyDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getDeepVallyDischargeQuantity();
                electricProfitVO.setDeepVallyDischargeBenefit(deepVallyDischargeBenefit);
                break;
            default:
        }
    }

    @Override
    public Double getTotalProfitByDayAndProjectId(String projectId, int year, int month, int day) {
        return electricService.getTotalProfitByDayAndProjectId(projectId, year, month, day);
    }

    public void calculateTotal(ElectricEntity electricEntity) {
        double totalChargeCost =
                electricEntity.getFlatChargeCost()
                        + electricEntity.getVallyChargeCost()
                        + electricEntity.getPeakChargeCost()
                        + electricEntity.getTipChargeCost()
                        + electricEntity.getDeepVallyChargeCost();
        electricEntity.setTotalChargeCost(totalChargeCost);
        double totalChargeQuantity =
                electricEntity.getFlatChargeQuantity()
                        + electricEntity.getVallyChargeQuantity()
                        + electricEntity.getPeakChargeQuantity()
                        + electricEntity.getTipChargeQuantity()
                        + electricEntity.getDeepVallyChargeQuantity();
        electricEntity.setTotalChargeQuantity(totalChargeQuantity);
        // 总放电收益
        double totalDischargeQuantity =
                electricEntity.getFlatDischargeQuantity()
                        + electricEntity.getVallyDischargeQuantity()
                        + electricEntity.getPeakDischargeQuantity()
                        + electricEntity.getTipDischargeQuantity()
                        + electricEntity.getDeepVallyDischargeQuantity();
        electricEntity.setTotalDischargeQuantity(totalDischargeQuantity);
        double totalDischargeBenefit =
                electricEntity.getFlatDischargeBenefit()
                        + electricEntity.getVallyDischargeBenefit()
                        + electricEntity.getPeakDischargeBenefit()
                        + electricEntity.getTipDischargeBenefit()
                        + electricEntity.getDeepVallyDischargeBenefit();
        electricEntity.setTotalDischargeBenefit(totalDischargeBenefit);
        // 总收益
        double totalBenefit = totalDischargeBenefit - totalChargeCost;
        electricEntity.setTotalBenefit(totalBenefit);
    }

    @Override
    public Set<String> type() {
        return Set.of(ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD.getValue());
    }
}
