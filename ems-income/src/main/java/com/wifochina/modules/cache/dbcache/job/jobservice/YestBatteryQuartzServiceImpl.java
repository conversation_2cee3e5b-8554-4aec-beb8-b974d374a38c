package com.wifochina.modules.cache.dbcache.job.jobservice;

import com.wifochina.modules.cache.dbcache.job.YestBatteryProfitJob;
import com.wifochina.modules.income.job.CustomTimezoneQuartzService;

import lombok.extern.slf4j.Slf4j;

import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

import javax.annotation.Resource;

/**
 * @since 2024-03-14 3:59 PM
 * <AUTHOR>
 */
@Component("yestBatteryQuartzServiceImpl")
@Slf4j
public class YestBatteryQuartzServiceImpl implements CustomTimezoneQuartzService {

    @Resource protected Scheduler scheduler;

    public static final String JOB_GROUP = "yest-battery-profit-group";

    /**
     * 测试方法 快速启动job
     *
     * @param timezone : timezone
     */
    public void startNowTest(String timezone) {
        JobDetail jobDetail = getJobDetail(timezone);
        Trigger trigger =
                TriggerBuilder.newTrigger()
                        .withIdentity("triggerName", "groupbattery")
                        .startNow()
                        .build();
        try {
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 如果要开启测试 注释方法内容 打开startNowTest()方法
     *
     * @param timezone : timezone
     */
    @Override
    public void addJob(String timezone) {
        // startNowTest(timezone);
        // 构建JobDetail (表示一个具体的可执行的调度程序，Job是这个可执行调度程序所要执行的内容)
        JobDetail jobDetail = getJobDetail(timezone);
        // 构建出发去Trigger （调度参数的配置，代表何时出发该任务)
        Trigger trigger = getTrigger(timezone, getJobCrontab());
        if (trigger != null) {
            try {
                scheduler.scheduleJob(jobDetail, trigger);
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
            } catch (SchedulerException ignored) {
                log.error("");
            }
        }
    }

    public JobDetail getJobDetail(String timezone) {
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = String.format("yest-battery-profit-job:%s", timezone);
        jobData.put("timezone", timezone);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        return JobBuilder.newJob(YestBatteryProfitJob.class)
                .withIdentity(jobName, JOB_GROUP)
                .usingJobData(jobDataMap)
                .storeDurably()
                .build();
    }

    public Trigger getTrigger(String timezone, String jobCron) {
        String triggerName = String.format("yest-battery-profit-trigger:%s", timezone);
        return TriggerBuilder.newTrigger()
                .withIdentity(triggerName, JOB_GROUP)
                // .startAt(DateBuilder.futureDate(1, DateBuilder.IntervalUnit.SECOND))
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(jobCron)
                                .inTimeZone(TimeZone.getTimeZone(timezone)))
                .build();
    }

    public String getJobCrontab() {
        return "0 15 0 * * ?";
    }
}
