package com.wifochina.modules.cache.dbcache.pvwind.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.onoff.OnOffComponent;
import com.wifochina.common.price.RenewableModelPriceFormulaComponent;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.cache.dbcache.pvwind.RenewableDynamicProfitService;
import com.wifochina.modules.income.caculate.pvwind.RenewableTimeSeriesService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.renewable.AbstractRenewableAdapterService;
import com.wifochina.modules.renewable.RenewablePowerDatas;
import com.wifochina.modules.renewable.RenewableUtils;
import com.wifochina.modules.renewable.context.CalcOnOffFlagContext;
import com.wifochina.modules.renewable.context.CalcRenewableProfitCoreContext;
import com.wifochina.modules.renewable.entity.RenewableDynamicProfitEntity;
import com.wifochina.modules.renewable.request.RenewableProfitOneRequest;
import com.wifochina.modules.renewable.request.RenewableProfitRequest;
import com.wifochina.modules.renewable.vo.RenewableProfitVo;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.sql.rowset.serial.SerialException;

/**
 * Created on 2024/4/12 13:47.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RenewableDynamicPeriodServiceImpl
        extends AbstractRenewableAdapterService<RenewableDynamicProfitEntity> {

    private final RenewableDynamicProfitService renewableDynamicProfitService;
    private final RenewableModelPriceFormulaComponent pvWindModelFormulaComponent;
    private final ElectricPriceService electricPriceService;

    public RenewableDynamicPeriodServiceImpl(
            RenewableTimeSeriesService renewableTimeSeriesService,
            OnOffComponent onOffComponent,
            RenewableDynamicProfitService renewableDynamicProfitService,
            RenewableModelPriceFormulaComponent pvWindModelFormulaComponent,
            ElectricPriceService electricPriceService) {
        super(renewableTimeSeriesService, onOffComponent);
        this.renewableDynamicProfitService = renewableDynamicProfitService;
        this.pvWindModelFormulaComponent = pvWindModelFormulaComponent;
        this.electricPriceService = electricPriceService;
    }

    @Override
    public void calculatePeriod(
            ProjectEntity projectEntity,
            ElectricPriceEntity superPrice,
            ElectricPriceEntity price,
            RenewableDynamicProfitEntity pvWindDynamicProfitEntity,
            String pvOrWindType,
            RenewablePowerDatas renewablePowerDatas) {

        Double power = renewablePowerDatas.power();
        Double onlinePower = renewablePowerDatas.onlinePower();
        Double dcdcPower = renewablePowerDatas.dcdcPower();
        Double selfPower = renewablePowerDatas.selfPower();
        setInit(superPrice);
        // power += dcdcPower;
        // 自用电 =  pv - online
        //        double selfPower = power - onlinePower;
        //        selfPower = selfPower < 0 ? 0 : selfPower;
        //        power = power < 0 ? 0 : power;

        //  pv 收益公式=(国家补助+各个时段电价)*各个时段的自用电量+（国家补助+脱硫标杆电价）*上网电量
        double benefit;
        double fullInternetBenefit;
        double onLineBenefit;
        double agreementBenefit;
        initPrice(superPrice, price);
        CalculateTypeEnum calculateTypeEnum = CalculateTypeEnum.valueOf(pvOrWindType);

        RenewableModelPriceFormulaComponent.RenewableFormulaBenefit renewableFormulaBenefit =
                // 计算 各种模式下的收益
                pvWindModelFormulaComponent.collectFormula(
                        calculateTypeEnum, superPrice, price, projectEntity, renewablePowerDatas);
        benefit = renewableFormulaBenefit.getPeriodModelBenefit();
        fullInternetBenefit = renewableFormulaBenefit.getFullInternetModelBenefit();
        agreementBenefit = renewableFormulaBenefit.getAgreementModelBenefit();
        onLineBenefit = renewableFormulaBenefit.getOnlineBenefit();
        if (benefit < 0) {
            benefit = 0;
        }
        // 总pv 电量
        pvWindDynamicProfitEntity.setTotalDischargeQuantity(power);
        // pv自用电量
        pvWindDynamicProfitEntity.setDischargeQuantity(selfPower);
        pvWindDynamicProfitEntity.setDischargeQuantityDcdc(dcdcPower);
        pvWindDynamicProfitEntity.setDischargeQuantityOnline(onlinePower);
        // 时段的收益
        pvWindDynamicProfitEntity.setDischargeBenefit(benefit);
        // 上网的收益
        // 这个和时段收益 组成的是 自发自用收益
        pvWindDynamicProfitEntity.setTotalOnlineBenefit(onLineBenefit);
        // 全额上网模式的
        pvWindDynamicProfitEntity.setTotalFullInternetAccessBenefit(fullInternetBenefit);
        // 协议模式的
        pvWindDynamicProfitEntity.setAgreementBenefit(agreementBenefit);
    }

    @Override
    public void queryPvOrWindData(RenewableProfitOneRequest request, QueryRenewableResult result) {
        // TODO 尚未实现  对于查询 要支持一下 如果是 数据校准了 那么有限查询出 数据校准的, 可以看 尖峰平谷那边的实现
        result.dynamicPeriodPostProcessor(List.of());
    }

    @Override
    public void queryCalibration(
            String projectId, String type, Long start, Long end, QueryRenewableResult result) {
        // TODO 尚未实现  对于查询
        result.dynamicPeriodPostProcessor(List.of());
    }

    private void setInit(ElectricPriceEntity superPrice) {
        if (superPrice.getPvPrice() == null) {
            superPrice.setPvPrice(0.0);
        }
        if (superPrice.getPvDfPrice() == null) {
            superPrice.setPvDfPrice(0.0);
        }
        if (superPrice.getPvSelfPrice() == null) {
            superPrice.setPvSelfPrice(0.0);
        }
        if (superPrice.getPvSubsidyPrice() == null) {
            superPrice.setPvSubsidyPrice(0.0);
        }

        if (superPrice.getWindPrice() == null) {
            superPrice.setWindPrice(0.0);
        }
        if (superPrice.getWindDfPrice() == null) {
            superPrice.setWindDfPrice(0.0);
        }
        if (superPrice.getWindSelfPrice() == null) {
            superPrice.setWindSelfPrice(0.0);
        }
        if (superPrice.getWindSubsidyPrice() == null) {
            superPrice.setWindSubsidyPrice(0.0);
        }
    }

    /**
     * 计算和保存 先调用 获取方法 再保存
     *
     * @param time : time
     * @param calculateType : calculateType
     * @param context : context
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculateSave(
            LocalDate time, String calculateType, RenewableAdapterContext context) {
        List<RenewableDynamicProfitEntity> results =
                calculateRenewableProfit(time, calculateType, context);
        if (CollectionUtil.isNotEmpty(results)) {
            // 把每个时段 作为一条记录 进行保存
            //  TODO 如果要支持 重跑数据 这里需要 支持一下 更新操作, 需要把以前的 id 找到 才可以 需求是目前不需要支持 所以这里不改了 可以参考
            // PvWindPeakValleysPeriodServiceImpl 的calculateSave
            renewableDynamicProfitService.remove(
                    new LambdaQueryWrapper<RenewableDynamicProfitEntity>()
                            .eq(
                                    RenewableDynamicProfitEntity::getProjectId,
                                    context.project().getId())
                            .eq(RenewableDynamicProfitEntity::getRenewableType, calculateType)
                            .eq(RenewableDynamicProfitEntity::getYear, time.getYear())
                            .eq(RenewableDynamicProfitEntity::getMonth, time.getMonthValue())
                            .eq(RenewableDynamicProfitEntity::getDay, time.getDayOfMonth()));
            renewableDynamicProfitService.saveBatch(results);
        }
    }

    @Override
    public void calculateSaveForDataCalibration(
            LocalDate time, String calculateType, RenewableDataCalibrationAdapterContext context)
            throws SerialException {
        // TODO do not impl temp
        throw new SerialException("pvwind dynamic period not impl for dataCalibration");
    }

    /**
     * 计算 pv or wind 的收益 缓存
     *
     * @param time : 计算的是哪一天的
     * @param calculateType : @see CalculateTypeEnum
     * @param context : context
     * @return : List<PvWindDynamicProfitEntity>
     */
    @Override
    public List<RenewableDynamicProfitEntity> calculateRenewableProfit(
            LocalDate time, String calculateType, RenewableAdapterContext context) {
        List<RenewableDynamicProfitEntity> results = new ArrayList<>();
        ProjectEntity project = context.project();
        CalcOnOffFlagContext calcOnOffFlagContext = getCalcOnOffFlagContext(project, calculateType);
        for (ElectricPriceEntity price : context.electricPriceParent().getPeriodPriceList()) {
            // 动态时段 需要每一个时段 一条记录
            RenewableDynamicProfitEntity pvWindDynamicEntity =
                    getPvWindDynamicEntity(time, calculateType, price, project);
            calcRenewablePeriodProfitCore(
                    new CalcRenewableProfitCoreContext() {
                        @Override
                        public LocalDate time() {
                            return time;
                        }

                        @Override
                        public ProjectEntity project() {
                            return project;
                        }

                        @Override
                        public CalculateTypeEnum calculateType() {
                            return CalculateTypeEnum.valueOf(calculateType);
                        }

                        @Override
                        public CalcOnOffFlagContext calcOnOffFlagContext() {
                            return calcOnOffFlagContext;
                        }
                    },
                    price,
                    context.electricPriceParent(),
                    pvWindDynamicEntity);
            results.add(pvWindDynamicEntity);
        }
        return results;
    }

    @Override
    public List<RenewableDynamicProfitEntity> calculateRenewableProfitForDataCalibration(
            LocalDate time, String calculateType, RenewableDataCalibrationAdapterContext context) {
        return null;
    }

    @Override
    public RenewableProfitVo calculateRenewableProfitVo(
            BenefitRequest benefitRequest, String calculateType, ProjectEntity project) {
        long yesterdayEndTime = MyTimeUtil.getYesterdayEndTime(project.getTimezone());
        boolean todaySearch = benefitRequest.getEndDate() > yesterdayEndTime;
        if (todaySearch) {
            // 如果是包括了查询今天时间 则 把 请求时间放到 今天的前一天23:59:59, 这个也可以没有,
            // 因为带着今天去查询数据库也查询不到今天的缓存 为了更好理解吧
            benefitRequest.setEndDate(yesterdayEndTime);
        }
        RenewableProfitVo pvProfitVo = new RenewableProfitVo();
        // 直接查询缓存中的数据
        RenewableProfitRequest request =
                new RenewableProfitRequest(
                        benefitRequest.getStartDate(),
                        benefitRequest.getEndDate(),
                        project.getId(),
                        calculateType);
        RenewableDynamicProfitEntity beforePvWindDynamicProfit =
                renewableDynamicProfitService.queryPvWindDynamicTotalProfit(request);
        // 如果需要查询今天
        if (todaySearch) {
            long todayZero = MyTimeUtil.getTodayZeroTime(project.getTimezone());
            // 找到 那个匹配到的 电价配置策略
            ElectricPriceEntity electricPriceParent =
                    electricPriceService.getElectricPriceMatch(project.getId(), todayZero);
            List<RenewableDynamicProfitEntity> results = new ArrayList<>();
            if (electricPriceParent != null) {
                // 大致意思是 : 如果今天查询出数据 把今天的和今天前的数据合并
                results =
                        calculateRenewableProfit(
                                LocalDate.now(ZoneId.of(project.getTimezone())),
                                calculateType,
                                new RenewableAdapterContext() {
                                    @Override
                                    public ElectricPriceEntity electricPriceParent() {
                                        return electricPriceParent;
                                    }

                                    @Override
                                    public ProjectEntity project() {
                                        return project;
                                    }
                                });
            }
            if (beforePvWindDynamicProfit != null) {
                if ((CollectionUtil.isNotEmpty(results))) {
                    // 计算 把缓存的和今日的 加起来
                    RenewableDynamicProfitEntity finalBeforePvWindDynamicProfit1 =
                            beforePvWindDynamicProfit;
                    results.forEach(
                            pvWindDynamicProfitEntity -> {
                                // 每个时段的数据
                                RenewableUtils.renewableProfitFieldAdd(
                                        finalBeforePvWindDynamicProfit1, pvWindDynamicProfitEntity);
                            });
                    // 这里copyProperties copy一些 比如 projectId  day month year等属性吧..
                    BeanUtil.copyProperties(beforePvWindDynamicProfit, pvProfitVo);
                    transformToPvWindProfitVo(beforePvWindDynamicProfit, pvProfitVo);
                }
            } else if (CollectionUtil.isNotEmpty(results)) {
                // 构造一个 空的
                beforePvWindDynamicProfit = new RenewableDynamicProfitEntity();
                RenewableDynamicProfitEntity finalBeforePvWindDynamicProfit =
                        beforePvWindDynamicProfit;
                results.forEach(
                        pvWindDynamicProfitEntity -> {
                            // 每个时段的数据
                            RenewableUtils.renewableProfitFieldAdd(
                                    finalBeforePvWindDynamicProfit, pvWindDynamicProfitEntity);
                        });
                BeanUtil.copyProperties(beforePvWindDynamicProfit, pvProfitVo);
                transformToPvWindProfitVo(beforePvWindDynamicProfit, pvProfitVo);
            }
        } else {
            // 不查询今天的 则直接 把before的数据 copy到 pvProfitVo
            if (beforePvWindDynamicProfit != null) {
                BeanUtil.copyProperties(beforePvWindDynamicProfit, pvProfitVo);
                transformToPvWindProfitVo(beforePvWindDynamicProfit, pvProfitVo);
            }
        }

        return pvProfitVo;
    }

    private void transformToPvWindProfitVo(
            RenewableDynamicProfitEntity pvWindDynamicProfitEntity, RenewableProfitVo pvProfitVo) {
        pvProfitVo.setTotalDischargeSelfQuantity(pvWindDynamicProfitEntity.getDischargeQuantity());
        pvProfitVo.setTotalDischargeSelfBenefit(pvWindDynamicProfitEntity.getDischargeBenefit());
        pvProfitVo.setTotalFullInternetAccessBenefit(
                pvWindDynamicProfitEntity.getTotalFullInternetAccessBenefit());
        pvProfitVo.setTotalOnlineQuantity(pvWindDynamicProfitEntity.getDischargeQuantityOnline());
        pvProfitVo.setTotalOnlineBenefit(pvWindDynamicProfitEntity.getTotalOnlineBenefit());
        // 注意 这个 字段是 agreementBenefit
        pvProfitVo.setTotalAgreementBenefit(pvWindDynamicProfitEntity.getAgreementBenefit());
        pvProfitVo.setTotalDischargeQuantity(pvWindDynamicProfitEntity.getTotalDischargeQuantity());
    }

    private void calPvWindCacheAndToday(
            RenewableDynamicProfitEntity beforePvWindDynamicProfit,
            RenewableDynamicProfitEntity todayPvWindDynamicProfitEntity) {

        for (Field field : beforePvWindDynamicProfit.getClass().getDeclaredFields()) {
            if (field.getType() == double.class) {
                try {
                    if (field.getName().contains("Price")) {
                        continue;
                    }
                    field.setAccessible(true);
                    double value = field.getDouble(todayPvWindDynamicProfitEntity);
                    double value2 = field.getDouble(beforePvWindDynamicProfit);
                    field.set(beforePvWindDynamicProfit, value + value2);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    private RenewableDynamicProfitEntity getPvWindDynamicEntity(
            LocalDate time,
            String calculateType,
            ElectricPriceEntity price,
            ProjectEntity projectEntity) {
        LocalTime startTime = price.getStartTime();
        LocalTime endTime = price.getEndTime();
        // 把每个时段当做一条 记录
        long periodStartTime =
                startTime
                        .atDate(time)
                        .atZone(ZoneId.of(projectEntity.getTimezone()))
                        .toEpochSecond();
        long periodEndTime =
                endTime.atDate(time).atZone(ZoneId.of(projectEntity.getTimezone())).toEpochSecond();
        RenewableDynamicProfitEntity pvWindDynamicProfitEntity = new RenewableDynamicProfitEntity();
        pvWindDynamicProfitEntity.setPeriodStartTime(periodStartTime);
        pvWindDynamicProfitEntity.setPeriodEndTime(periodEndTime);
        pvWindDynamicProfitEntity.setProjectId(projectEntity.getId());
        pvWindDynamicProfitEntity.setYear(time.getYear());
        pvWindDynamicProfitEntity.setMonth(time.getMonthValue());
        pvWindDynamicProfitEntity.setDay(time.getDayOfMonth());
        // 冗余存储 价格
        // redundancyPrice(calculateType, pvWindDynamicProfitEntity, parentPrice, price);
        pvWindDynamicProfitEntity.setRenewableType(calculateType);
        return pvWindDynamicProfitEntity;
    }

    @Override
    public Set<String> type() {
        return Set.of(
                ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.getValue(),
                ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.getValue());
    }
}
