package com.wifochina.modules.cache.dbcache.pvwind;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.renewable.entity.RenewableDynamicProfitEntity;
import com.wifochina.modules.renewable.request.RenewableProfitRequest;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
public interface RenewableDynamicProfitService extends IService<RenewableDynamicProfitEntity> {
    RenewableDynamicProfitEntity queryPvWindDynamicTotalProfit(RenewableProfitRequest request);
}
