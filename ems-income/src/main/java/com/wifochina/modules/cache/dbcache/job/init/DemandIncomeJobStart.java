package com.wifochina.modules.cache.dbcache.job.init;

import com.wifochina.modules.cache.dbcache.job.jobservice.DemandIncomeQuartzServiceImpl;
import com.wifochina.modules.project.service.ProjectService;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * @since 2024-03-14 2:59 PM
 * <AUTHOR>
 */
@Component
@Order(100)
public class DemandIncomeJobStart implements ApplicationRunner {

    @Resource private ProjectService projectService;

    @Resource private DemandIncomeQuartzServiceImpl demandIncomeQuartzService;

    @Override
    public void run(ApplicationArguments args) {
        List<String> timezoneList = projectService.getAllTimeZone();
        // for (String timezone : timezoneList) {
        // yestBatteryQuartzService.addJob(timezone);
        // incomeQuartzServiceImpl.addJob(timezone);
        // }
        //        demandIncomeQuartzService.startNowTest("Asia/Shanghai");
    }
}
