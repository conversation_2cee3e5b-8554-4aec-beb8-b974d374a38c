package com.wifochina.modules.cache.dbcache.job;

import cn.hutool.extra.spring.SpringUtil;

import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmLevelEnum;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.LastWeekCalculator;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.alarm.service.AlarmHandler;
import com.wifochina.modules.electric.ElectricAdapterChooser;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.entity.ElectricPeriod;
import com.wifochina.modules.electric.timesharing.TimeSharingCacheService;
import com.wifochina.modules.metrics.ScheduleJobMetrics;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * @since 2024-03-14 2:02 PM
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class YestBatteryProfitJob extends QuartzJobBean {

    private final ElectricAdapterChooser chooser;

    private final TimeSharingCacheService timeSharingCacheService;

    private final ProjectService projectService;

    private ScheduleJobMetrics scheduleJobMetrics;

    private final AlarmHandler alarmHandler;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        String timezone = (String) context.getJobDetail().getJobDataMap().get("timezone");
        List<ProjectEntity> projectEntities = projectService.getProjectsByTimeZone(timezone);
        for (ProjectEntity projectEntity : projectEntities) {
            try {
                log.info("yestBatteryProfitJob start");
                String electricType = saveOneDayElectric(projectEntity);
                log.info("yestBatteryProfitJob end");
                calculateDeviationCoefficient(projectEntity, electricType);
                scheduleJobMetrics.recordSuccess(projectEntity.getId(), projectEntity.getProjectName(), "yestBatteryProfitJob");
            } catch (Exception e) {
                log.error("项目 {} 保存昨日电量收益异常 {}", projectEntity.getProjectName(), e.getMessage());
                scheduleJobMetrics.recordFailure(projectEntity.getId(), projectEntity.getProjectName(), "yestBatteryProfitJob", e.getMessage());
            }
        }
    }

    /** 计算偏差系数并告警 */
    private void calculateDeviationCoefficient(ProjectEntity projectEntity, String electricType) {
        // 查询当前项目告警配置
        AlarmCacheDTO alarmCache = alarmHandler.cacheAlarmInfo(projectEntity.getId());
        AlarmConfigEntity profitAlarmConfig =
                Optional.ofNullable(alarmCache)
                        .map(AlarmCacheDTO::getAlarmConfigList)
                        .map(List::stream)
                        .map(

                                i ->
                                        i.filter(
                                                j ->
                                                        AlarmContentEnum.PROJECT_EARNING.getCode()
                                                                == j.getAlarmContent()))
                        .flatMap(Stream::findFirst)
                        .orElse(null);
        if (profitAlarmConfig == null) {
            return;
        }
        // 计算当天和上周同天的收益偏差系数
        LocalDate localDate =
                Instant.now()
                        .atZone(ZoneId.of(projectEntity.getTimezone()))
                        .plusDays(-1)
                        .toLocalDate();
        int year = localDate.getYear();
        int month = localDate.getMonthValue();
        int day = localDate.getDayOfMonth();
        ElectricAdapterService<? extends ElectricPeriod> eleAdapter = chooser.choose(electricType);
        Double curProfit =
                eleAdapter.getTotalProfitByDayAndProjectId(projectEntity.getId(), year, month, day);
        int[] timeArr = LastWeekCalculator.lastWeekSameDay(year, month, day);
        Double lastWeekProfit =
                eleAdapter.getTotalProfitByDayAndProjectId(
                        projectEntity.getId(), timeArr[0], timeArr[1], timeArr[2]);
        if (lastWeekProfit != null && curProfit != null) {
            double deviationCoefficient = curProfit / lastWeekProfit;
            log.info("项目 {} 偏差系数 {}", projectEntity.getProjectName(), deviationCoefficient);
            // 超过偏差系数，发送告警
            if (deviationCoefficient > profitAlarmConfig.getProfitDeviationCoefficient()) {
                alarmHandler.handleAlarm(
                        projectEntity.getId(),
                        AlarmContentEnum.PROJECT_EARNING,
                        AlarmLevelEnum.warning);
            }
        }
    }

    public String saveOneDayElectric(ProjectEntity projectEntity) {
        String projectId = projectEntity.getId();
        long yesterdayZero =
                MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone())
                        - EmsConstants.ONE_DAY_SECOND;
        ElectricPriceService electricPriceService = SpringUtil.getBean(ElectricPriceService.class);
        // 找到 那个匹配到的 电价配置策略
        ElectricPriceEntity electricPriceEntity =
                electricPriceService.getElectricPriceMatch(projectId, yesterdayZero);
        if (electricPriceEntity != null) {
            // 通过 chooser去选择 一个 执行 适配尖峰平谷 or 新的 自定义时段
            chooser.choose(projectEntity.getElectricPriceType())
                    .saveElectric(
                            new ElectricAdapterService.ElectricAdapterContext() {
                                @Override
                                public long timePoint() {
                                    return yesterdayZero;
                                }

                                @Override
                                public List<ElectricPriceEntity> periodPriceList() {
                                    return electricPriceEntity.getPeriodPriceList();
                                }

                                @Override
                                public ProjectEntity project() {
                                    return projectEntity;
                                }
                            });

            // 1.3.7 add 分时缓存;
            timeSharingCacheService.timeSharingCacheBattery(
                    yesterdayZero, projectEntity, electricPriceEntity);
        } else {
            log.error(
                    "can`t find electricPrice by projectId:{} , yesterdayZero: {}",
                    projectId,
                    yesterdayZero);
        }
        return projectEntity.getElectricPriceType();
    }
}
