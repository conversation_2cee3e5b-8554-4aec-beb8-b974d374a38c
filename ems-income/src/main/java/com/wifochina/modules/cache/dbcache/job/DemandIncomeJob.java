package com.wifochina.modules.cache.dbcache.job;

import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.demand.service.GroupDemandMonthIncomeService;
import com.wifochina.modules.metrics.ScheduleJobMetrics;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * @since 2024-09-13 16:36:44
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class DemandIncomeJob extends QuartzJobBean {

    private final ProjectService projectService;
    private final GroupDemandMonthIncomeService groupDemandMonthIncomeService;
    private final ScheduleJobMetrics scheduleJobMetrics;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        // 必须要有这个
        String initProjectId =
                (String) context.getJobDetail().getJobDataMap().get(EmsConstants.PROJECT_ID);
        Long initMonthStart =
                context.getJobDetail().getJobDataMap().get(EmsConstants.START_DATE) == null
                        ? null
                        : Long.valueOf(
                                ((Integer)
                                        context.getJobDetail()
                                                .getJobDataMap()
                                                .get(EmsConstants.START_DATE)));
        Long initMonthEnd =
                context.getJobDetail().getJobDataMap().get(EmsConstants.END_DATE) == null
                        ? null
                        : Long.valueOf(
                                (Integer)
                                        context.getJobDetail()
                                                .getJobDataMap()
                                                .get(EmsConstants.END_DATE));
        // 必须要有这个projectId
        if (!StringUtil.isEmpty(initProjectId)) {
            ProjectEntity projectEntity = projectService.getById(initProjectId);
            try{
                // 上面是获取 到 是否是根据特定参数来执行 用于手动执行接口
                RangeRequest rangeRequest =
                        new RangeRequest().setStartDate(initMonthStart).setEndDate(initMonthEnd);
                groupDemandMonthIncomeService.saveGroupDemandMonthIncome(projectEntity, rangeRequest);
            } catch (Exception e) {
                log.error("DemandIncomeJob error : {}", e.getMessage());
                scheduleJobMetrics.recordFailure(initProjectId, projectEntity.getProjectName(),
                        this.getClass().getSimpleName(), e.getMessage());
            }

        }
    }
}
