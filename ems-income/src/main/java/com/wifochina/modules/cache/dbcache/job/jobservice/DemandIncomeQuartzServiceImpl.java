package com.wifochina.modules.cache.dbcache.job.jobservice;

import com.wifochina.modules.cache.dbcache.job.DemandIncomeJob;
import com.wifochina.modules.income.job.CustomProjectQuartzService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.extern.slf4j.Slf4j;

import org.quartz.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

import javax.annotation.Resource;

/**
 * @since 2024-03-14 3:59 PM
 * <AUTHOR>
 */
@Component("demandIncomeQuartzServiceImpl")
@Slf4j
public class DemandIncomeQuartzServiceImpl implements CustomProjectQuartzService {

    @Resource protected Scheduler scheduler;
    @Resource protected ProjectService projectService;

    public static final String JOB_GROUP = "demand-income-group";
    public static final String JOB_NAME_PLACEHOLDER = "demand-income-profit-job:%s";
    public static final String JOB_TRIGGER_PLACEHOLDER = "demand-income-profit-trigger:%s";

    /**
     * 测试方法 快速启动job
     *
     * @param projectId : projectId
     */
    public void startNowTest(String projectId, Map<String, Object> initParamsMap) {
        JobDetail jobDetail = getJobDetail(projectId, true);
        JobDataMap jobDataMap = jobDetail.getJobDataMap();
        // 将initParamsMap中的值放入JobDataMap
        jobDataMap.putAll(initParamsMap);
        String triggerName = String.format(JOB_TRIGGER_PLACEHOLDER, projectId);

        Trigger trigger =
                TriggerBuilder.newTrigger()
                        .withIdentity("test_" + triggerName, "test_" + JOB_GROUP)
                        .startNow()
                        .build();
        try {
            String jobName = String.format("test_" + JOB_NAME_PLACEHOLDER, projectId);
            JobKey jobKey = new JobKey(jobName, "test_" + JOB_GROUP);
            if (!scheduler.checkExists(jobKey)) {
                // 创建并调度Job
                scheduler.scheduleJob(jobDetail, trigger);
            } else {
                // Job已经存在，可以选择更新Job或跳过
                JobDetail existingJobDetail = scheduler.getJobDetail(jobKey);
                JobDataMap existingJobDataMap = existingJobDetail.getJobDataMap();
                // 更新已有Job的JobDataMap中的参数
                existingJobDataMap.putAll(initParamsMap);

                // 重新添加或更新Job到调度器 true表示覆盖已有Job
                scheduler.addJob(existingJobDetail, true);
                // 触发现有的Job
                scheduler.triggerJob(jobKey);
                System.out.println("Job already exists");
            }
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 如果要开启测试 注释方法内容 打开startNowTest()方法
     *
     * @param projectId : projectId
     */
    @Override
    public void addJob(String projectId) {
        // startNowTest(timezone);
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 构建JobDetail (表示一个具体的可执行的调度程序，Job是这个可执行调度程序所要执行的内容)
        JobDetail jobDetail = getJobDetail(projectEntity.getId(), false);
        // 构建出发去Trigger （调度参数的配置，代表何时出发该任务)
        Trigger trigger = getTrigger(projectEntity.getId(), projectEntity.getTimezone());
        if (trigger != null) {
            try {
                scheduler.scheduleJob(jobDetail, trigger);
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
            } catch (SchedulerException ignored) {
                log.error("");
            }
        }
    }

    public JobDetail getJobDetail(String projectId, boolean testFlag) {
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = String.format(JOB_NAME_PLACEHOLDER, projectId);
        jobData.put("projectId", projectId);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        return JobBuilder.newJob(DemandIncomeJob.class)
                .withIdentity(
                        testFlag ? "test_" + jobName : jobName,
                        testFlag ? "test_" + JOB_GROUP : JOB_GROUP)
                .usingJobData(jobDataMap)
                .storeDurably()
                .build();
    }

    public Trigger getTrigger(String projectId, String timezone) {
        String triggerName = String.format(JOB_TRIGGER_PLACEHOLDER, projectId);
        return TriggerBuilder.newTrigger()
                .withIdentity(triggerName, JOB_GROUP)
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(getJobCrontab())
                                .inTimeZone(TimeZone.getTimeZone(timezone)))
                .build();
    }

    public String getJobCrontab() {
        // 每个月的1日的凌晨3.30 执行 统计上个月的 用于计算 需量收益 的 缓存数据 包括需量算收益的power 等
        return "0 30 3 1 * ?";
    }
}
