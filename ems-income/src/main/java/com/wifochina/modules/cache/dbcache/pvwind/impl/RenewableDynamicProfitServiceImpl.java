package com.wifochina.modules.cache.dbcache.pvwind.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.cache.dbcache.pvwind.RenewableDynamicProfitService;
import com.wifochina.modules.renewable.entity.RenewableDynamicProfitEntity;
import com.wifochina.modules.renewable.mapper.RenewableDynamicProfitMapper;
import com.wifochina.modules.renewable.request.RenewableProfitRequest;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
@AllArgsConstructor
@Slf4j
public class RenewableDynamicProfitServiceImpl
        extends ServiceImpl<RenewableDynamicProfitMapper, RenewableDynamicProfitEntity>
        implements RenewableDynamicProfitService {

    @Override
    public RenewableDynamicProfitEntity queryPvWindDynamicTotalProfit(
            RenewableProfitRequest request) {
        return baseMapper.queryRenewableDynamicTotalProfit(request);
    }
}
