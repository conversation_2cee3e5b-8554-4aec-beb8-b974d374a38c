package com.wifochina.modules.cache.dbcache.job.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.cache.dbcache.job.jobservice.DemandIncomeQuartzServiceImpl;
import com.wifochina.modules.demand.service.impl.DemandQuartzServiceImpl;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.annotation.Resource;

/**
 * Created on 2024/10/11 10:06.
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@AllArgsConstructor
@Api(tags = "job-手动执行job")
public class ManualExecuteJobController {

    @Resource protected Scheduler scheduler;
    @Resource protected DemandIncomeQuartzServiceImpl demandIncomeQuartzService;
    @Resource protected DemandQuartzServiceImpl demandQuartzService;

    @PostMapping("executeDemandIncome")
    @ApiOperation("手动执行需量收益缓存job")
    public Result<Void> executeDemandIncome(@RequestBody Map<String, Object> maps) {
        String projectId = (String) maps.get("projectId");
        demandIncomeQuartzService.startNowTest(projectId, maps);
        return Result.success();
    }

    @PostMapping("executeDemandCalJob")
    @ApiOperation("手动执行需量计算job")
    public Result<Void> executeDemandCalJob(@RequestBody Map<String, Object> maps) {
        String projectId = (String) maps.get("projectId");
        demandQuartzService.startNowTest(projectId, maps);
        return Result.success();
    }
}
