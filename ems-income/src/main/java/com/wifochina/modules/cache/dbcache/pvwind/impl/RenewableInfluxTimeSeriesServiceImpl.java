package com.wifochina.modules.cache.dbcache.pvwind.impl;

import com.wifochina.common.constants.*;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.onoff.OnOffComponent;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.common.search.AbstractEquipmentIdsQuerySupplier;
import com.wifochina.modules.common.search.AbstractEquipmentTypesQuerySupplier;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.common.search.TimeSeriesQuerySource;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.income.caculate.pvwind.RenewableTimeSeriesService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created on 2024/6/17 11:53. <br>
 * 以后只会有这一个实现了 influx 版本的实现 以前有lindorm 已经被移除了
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class RenewableInfluxTimeSeriesServiceImpl implements RenewableTimeSeriesService {

    private final OnOffComponent onOffComponent;
    private final InfluxClientService influxClient;

    /**
     * 1.4.2 新增余热发电的支持的时候 重构了该方法的实现 使用
     * EquipmentTimeSeriesUtils.differenceQueryEngine.getRangeDifferenceEquipmentsMap, 查询一个时间段内的 差值
     * 支持PV/WIND/WASTER/ 以及 Gird->对应上网电量 和 DCDC
     *
     * <p>if else 就是支持 meter 和 ems的 ,不然其实差不多
     *
     * @param calculateType : @see CalculateTypeEnums
     * @param time : 当前Date时间
     * @param projectEntity : 项目
     * @param price : 电价 时段的 的price对象
     * @return : 该时段下的 power电量
     */
    @Override
    public double getPower(
            String calculateType,
            LocalDate time,
            ProjectEntity projectEntity,
            ElectricPriceEntity price) {
        double power = 0.0;
        Pair<Long, Long> periodStartEndPair =
                MyTimeUtil.getPeriodStartEndPair(
                        price.getStartTime(),
                        price.getEndTime(),
                        time,
                        projectEntity.getTimezone());
        long startTimestamp = periodStartEndPair.getFirst();
        // + 1 看getPeriodStartEndPair 方法解释,  + 60是因为 influxdb [) 这种 多1分钟的数据才能把时间对上
        long endTimestamp = periodStartEndPair.getSecond() + 1 + 60;
        if (calculateType.equals(CalculateTypeEnum.PV.name())
                || calculateType.equals(CalculateTypeEnum.WIND.name())
                || calculateType.equals(CalculateTypeEnum.WASTER.name())
                || calculateType.equals(CalculateTypeEnum.ONLINE_POWER.name())) {
            String meterType = getMeterTypeFromCalculateType(calculateType);
            Map<String, Double> rangeDifferenceEquipmentsMap =
                    EquipmentTimeSeriesUtils.differenceQueryEngine.getRangeDifferenceEquipmentsMap(
                            new TimeSeriesQuerySource()
                                    .setBucket(influxClient.getBucketForever())
                                    .setMeasurement(
                                            influxClient.getMeterTable(projectEntity.getId()))
                                    .setProject(projectEntity),
                            new FluxRateCommonHolder()
                                    .setStartDate(startTimestamp)
                                    .setEndDate(endTimestamp),
                            MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                            // 这里是根据 type 设备类型去查询, 也可以根据 ids去查询 实现类不同罢了, 原本重构前是 type去查询 所以这里保持一致
                            new AbstractEquipmentTypesQuerySupplier() {
                                @Override
                                public List<String> supplier() {
                                    return List.of(meterType);
                                }
                            });
            power =
                    rangeDifferenceEquipmentsMap.values().stream()
                            .mapToDouble(Double::doubleValue)
                            .sum();
            log.debug(
                    "calculateType:{} , startTime {} ,endTime:{} diff:{}",
                    calculateType,
                    startTimestamp,
                    endTimestamp,
                    power);
        } else {
            List<String> deviceIds = new ArrayList<>();
            boolean dcdcAndProfitOnOff =
                    onOffComponent.dcdcAndProfitOnOff(projectEntity.getId(), deviceIds::addAll);
            if (dcdcAndProfitOnOff && !deviceIds.isEmpty()) {
                Map<String, Double> rangeDifferenceEquipmentsMap =
                        EquipmentTimeSeriesUtils.differenceQueryEngine
                                .getRangeDifferenceEquipmentsMap(
                                        new TimeSeriesQuerySource()
                                                .setBucket(influxClient.getBucketForever())
                                                .setMeasurement(
                                                        influxClient.getEmsTable(
                                                                projectEntity.getId()))
                                                .setProject(projectEntity),
                                        new FluxRateCommonHolder()
                                                .setStartDate(startTimestamp)
                                                .setEndDate(endTimestamp),
                                        EmsFieldEnum.DCDC_METER_HISTORY_ENERGY_POS.field(),
                                        // 这里是根据 type 设备类型去查询, 也可以根据 ids去查询 实现类不同罢了, 原本重构前是 type去查询
                                        // 所以这里保持一致
                                        new AbstractEquipmentIdsQuerySupplier() {
                                            @Override
                                            public List<String> supplier() {
                                                return deviceIds;
                                            }
                                        });
                power =
                        rangeDifferenceEquipmentsMap.values().stream()
                                .mapToDouble(Double::doubleValue)
                                .sum();
                log.debug(
                        "calculateType:{} , startTime {} ,endTime:{} diff:{}",
                        calculateType,
                        startTimestamp,
                        endTimestamp,
                        power);
            }
        }
        return power;
    }

    private String getMeterTypeFromCalculateType(String calculateType) {
        String meterType = null;
        switch (CalculateTypeEnum.valueOf(calculateType)) {
            case PV:
                meterType = MeterTypeEnum.PV.meterType().toString();
                break;
            case WIND:
                meterType = MeterTypeEnum.WIND.meterType().toString();
                break;

            case WASTER:
                meterType = MeterTypeEnum.WASTER.meterType().toString();
                break;
            case ONLINE_POWER:
                meterType = MeterTypeEnum.GRID.meterType().toString();
        }
        if (meterType == null) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        return meterType;
    }

    //                // 获取 dcdc 的 power
    //                sql =
    //                        fillTempStampForSql(
    //                                getDcdcPowerSql(projectEntity.getId(), deviceIds),
    //                                time,
    //                                projectEntity,
    //                                price);
    //    // 时间 传进来了
    //    // 将query进行兑换
    //            log.debug("YestPvWindProfitJob#calculateTimeProfit---> {} ", sql + " ;");
    //    Double maxValue;
    //    Double minValue;
    //    double dcdcValue = 0.0;
    //    double totalPower = 0.0;
    //    List<FluxTable> tables = influxClient.getQueryApi().query(sql);
    //            for (FluxTable table : tables) {
    //        for (FluxRecord record : table.getRecords()) {
    //            if (calculateType.equals(CalculateTypeEnum.DCDC_POWER.name())) {
    //                // dcdc
    //                Double dcdc = (Double) record.getValueByKey("_value");
    //                if (dcdc == null) {
    //                    dcdc = 0.0;
    //                }
    //                dcdcValue += dcdc;
    //            } else {
    //                minValue = (Double) record.getValueByKey("firstValue");
    //                maxValue = (Double) record.getValueByKey("lastValue");
    //                if (minValue == null) {
    //                    minValue = 0.0;
    //                }
    //                if (maxValue == null) {
    //                    maxValue = 0.0;
    //                }
    //                LocalTime startTime = price.getStartTime();
    //                LocalTime endTime = price.getEndTime();
    //                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
    //                String formattedStartTime = startTime.format(formatter);
    //                String formattedEndTime = endTime.format(formatter);
    //                String timeStr = formattedStartTime + "-" + formattedEndTime;
    //                log.debug(
    //                        "time {} , maxValue:{}, minValue:{}, diff:{}",
    //                        timeStr,
    //                        maxValue,
    //                        minValue,
    //                        maxValue - minValue);
    //                totalPower += maxValue - minValue;
    //            }
    //        }
    //    }
    //            if (!calculateType.equals(CalculateTypeEnum.DCDC_POWER.name())) {
    //        // 如果不是dcdc 是 pv 或者gird 则是 最大值-最小值
    //        return totalPower;
    //    } else {
    //        return dcdcValue;
    //    }

    //
    //    @Override
    //    public String getDcdcPowerSql(String projectId, List<String> deviceIds) {
    //        Restrictions fieldRestriction = null;
    //        if (deviceIds != null && !deviceIds.isEmpty()) {
    //            List<Restrictions> deviceRestrictions = new ArrayList<>();
    //            for (String deviceId : deviceIds) {
    //                Restrictions deviceRestriction = Restrictions.tag("deviceId").equal(deviceId);
    //                deviceRestrictions.add(deviceRestriction);
    //            }
    //            fieldRestriction = Restrictions.or(deviceRestrictions.toArray(new
    // Restrictions[0]));
    //        }
    //        List<Restrictions> restrictionsList = new ArrayList<>();
    //        restrictionsList.add(fieldRestriction);
    //        // return Flux.from("forever")
    //        // 这里以前是写死的 现在是支持 场站的influx 和 云版本的 influx的 bucket 获取方式
    //        return Flux.from(influxClient.getBucketForever())
    //                .filter(
    //                        Restrictions.and(
    //                                Restrictions.measurement()
    //                                        .equal(influxClient.getEmsTable(projectId)),
    //                                Restrictions.tag("projectId").equal(projectId),
    //                                Restrictions.field()
    //
    // .equal(EmsFieldEnum.DCDC_METER_HISTORY_ENERGY_POS.field())))
    //                .filter(Restrictions.and(restrictionsList.toArray(new Restrictions[0])))
    //                .difference()
    //                .group()
    //                .sum()
    //                .toString();
    //    }
    //
    //    @Override
    //    public String getOnLinePowerSql(String sql) {
    //        return sql.replace("{type}", MeterTypeEnum.GRID.meterType().toString());
    //    }
    //
    //    @Override
    //    public String fillTempStampForSql(
    //            String sql, LocalDate time, ProjectEntity projectEntity, ElectricPriceEntity
    // price) {
    //        Pair<Long, Long> periodStartEndPair =
    //                MyTimeUtil.getPeriodStartEndPair(
    //                        price.getStartTime(),
    //                        price.getEndTime(),
    //                        time,
    //                        projectEntity.getTimezone());
    //        long startTimestamp = periodStartEndPair.getFirst();
    //        long endTimestamp = periodStartEndPair.getSecond();
    //        // 这里有个+1 秒的操作
    //        endTimestamp = endTimestamp + 1 + 60;
    //        // 将query进行兑换
    //        // String tempStartString = sql;
    //        // 这里使用 正则去把 还没有fill 时间戳的 这部分 换成带了时间戳的
    //        // String patternString = "from\\(bucket:\"forever\"\\)";
    //        String patternString = "from\\(bucket:\"" + influxClient.getBucketForever() + "\"\\)";
    //        Pattern pattern = Pattern.compile(patternString);
    //        // 这里使用 正则把 range 时间拼接上
    //        String replacement =
    //                Flux.from(influxClient.getBucketForever())
    //                        .range(startTimestamp, endTimestamp)
    //                        .toString();
    //        Matcher matcher = pattern.matcher(sql);
    //        return matcher.replaceAll(replacement);
    //    }
    //
    //    @Override
    //    public String getPvWindPowerSql(String baseSql, boolean pv) {
    //        String pvOrWindFluxSql;
    //        if (pv) {
    //            // pv
    //            pvOrWindFluxSql = baseSql.replace("{type}",
    // MeterTypeEnum.PV.meterType().toString());
    //        } else {
    //            // 风电
    //            pvOrWindFluxSql = baseSql.replace("{type}",
    // MeterTypeEnum.WIND.meterType().toString());
    //        }
    //        return pvOrWindFluxSql;
    //    }
    //
    //    @NotNull
    //    private String getBaseSql(String projectId) {
    //        FilterFlux filter =
    //                Flux.from(influxClient.getBucketForever())
    //                        // .range(startTimestamp, endTimestamp)
    //                        .filter(
    //                                Restrictions.measurement()
    //                                        .equal(influxClient.getMeterTable(projectId)))
    //                        .filter(
    //                                Restrictions.and(
    //                                        Restrictions.field()
    //                                                .equal(
    //                                                        MeterFieldEnum
    //
    // .AC_HISTORY_POSITIVE_POWER_IN_KWH
    //                                                                .field()),
    //                                        Restrictions.tag("projectId").equal(projectId),
    //                                        Restrictions.tag("type").equal("{type}")));
    //        // 这里要根据 是场站的 还是云版本的 来变化一下 ammeterId 或者是 meterId
    //        Flux firstMap;
    //        Flux lastMap;
    //        List<String> valueKey = List.of("firstValue", "lastValue");
    //        String basemapStr =
    //                "({_time: r._time, "
    //                        + EmsConstants.SAMPLE_PLACEHOLDER
    //                        + ": r._value, "
    //                        + influxClient.getMeterKey()
    //                        + ": r."
    //                        + influxClient.getMeterKey()
    //                        + "})";
    //
    //        firstMap =
    //                filter.first()
    //                        .map(basemapStr.replace(EmsConstants.SAMPLE_PLACEHOLDER,
    // valueKey.get(0)));
    //        lastMap =
    //                filter.last()
    //                        .map(basemapStr.replace(EmsConstants.SAMPLE_PLACEHOLDER,
    // valueKey.get(1)));
    //        JoinFlux withTable =
    //                Flux.join().withTable("firstTable", firstMap).withTable("lastTable", lastMap);
    //        String sql = withTable.withOn(influxClient.getMeterKey()).toString();
    //        return sql;
    //    }
    //
    //    public String getFluxSql(
    //            LocalDate time, String projectId, ElectricPriceEntity price, ZoneId zoneId) {
    //        LocalTime startTime = price.getStartTime();
    //        LocalTime endTime = price.getEndTime();
    //        if (endTime.getSecond() == 0 && endTime.getMinute() == 0 && endTime.getHour() == 0) {
    //            endTime = LocalTime.of(23, 59, 59);
    //        }
    //        // 获取到
    //        // 将起始时间和结束时间转换为 ZonedDateTime 对象，并指定时区
    //        ZonedDateTime startDateTime = ZonedDateTime.of(time, startTime, zoneId);
    //        ZonedDateTime endDateTime = ZonedDateTime.of(time, endTime, zoneId);
    //
    //        // 将起始时间和结束时间转换为 ZonedDateTime 对象，并指定时区
    //        // 获取起始时间和结束时间的时间戳（单位：秒）
    //        long startTimestamp = startDateTime.toInstant().getEpochSecond();
    //        long endTimestamp = endDateTime.toInstant().getEpochSecond();
    //        endTimestamp += 1;
    //
    //        FilterFlux filter =
    //                Flux.from("forever")
    //                        .range(startTimestamp, endTimestamp)
    //                        .filter(Restrictions.measurement().equal("T3_5s"))
    //                        .filter(
    //                                Restrictions.and(
    //                                        Restrictions.field()
    //                                                .equal("ac_history_positive_power_in_kwh"),
    //                                        Restrictions.tag("projectId").equal(projectId),
    //                                        Restrictions.tag("type").equal("{type}")));
    //        Flux firstMap =
    //                filter.first()
    //                        .map("({_time: r._time, firstValue: r._value, ammeterId:
    // r.ammeterId})");
    //        Flux lastMap =
    //                filter.last()
    //                        .map("({_time: r._time, lastValue: r._value, ammeterId:
    // r.ammeterId})");
    //        return Flux.join()
    //                .withTable("firstTable", firstMap)
    //                .withTable("lastTable", lastMap)
    //                .withOn("ammeterId")
    //                .toString();
    //    }
}
