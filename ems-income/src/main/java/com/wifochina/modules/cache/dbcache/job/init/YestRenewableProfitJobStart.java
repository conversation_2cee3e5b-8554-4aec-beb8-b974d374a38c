package com.wifochina.modules.cache.dbcache.job.init;

import com.wifochina.modules.cache.dbcache.job.jobservice.YestRenewableQuartzServiceImpl;
import com.wifochina.modules.project.service.ProjectService;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * @since 2024-03-14 2:59 PM
 * <AUTHOR>
 */
@Component
public class YestRenewableProfitJobStart implements ApplicationRunner {

    @Resource private ProjectService projectService;

    @Resource private YestRenewableQuartzServiceImpl yestRenewableQuartzService;

    @Override
    public void run(ApplicationArguments args) {
        List<String> timezoneList = projectService.getAllTimeZone();
        for (String timezone : timezoneList) {
            yestRenewableQuartzService.addJob(timezone);
        }
    }
}
