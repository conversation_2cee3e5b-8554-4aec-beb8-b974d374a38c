package com.wifochina.modules.cache.dbcache.pvwind.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.income.query.pvwind.RenewableIncomeQueryService;
import com.wifochina.modules.renewable.RenewableUtils;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.renewable.mapper.RenewableProfitMapper;
import com.wifochina.modules.renewable.request.RenewableProfitOneRequest;
import com.wifochina.modules.renewable.request.RenewableProfitRequest;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
@AllArgsConstructor
@Slf4j
public class RenewableIncomeQueryServiceImpl
        extends ServiceImpl<RenewableProfitMapper, RenewableProfitEntity>
        implements RenewableIncomeQueryService {

    /**
     * 支持 数据校准 逻辑: 就是把查询的时间段内的 数据校准的记录找到 然后 查询这个时间段内的非这些数据校准的数据的sum,然后把数据校准的数据+上去
     *
     * @param renewableProfitRequest : 请求
     * @return :PvWindProfitEntity
     */
    @Override
    public RenewableProfitEntity queryRenewableTotalProfit(
            RenewableProfitRequest renewableProfitRequest) {
        Long start = renewableProfitRequest.getStart();
        Long end = renewableProfitRequest.getEnd();
        // 第一步 把这个时间段内的 存在数据校准的 日期找到

        List<RenewableProfitEntity> dataCalibrations =
                baseMapper.selectList(
                        new LambdaQueryWrapper<RenewableProfitEntity>()
                                .eq(
                                        RenewableProfitEntity::getProjectId,
                                        renewableProfitRequest.getProjectId())
                                .eq(
                                        RenewableProfitEntity::getRenewableType,
                                        renewableProfitRequest.getType())
                                .eq(RenewableProfitEntity::isDataCalibrationFlag, true)
                                .ge(RenewableProfitEntity::getTime, start)
                                .lt(RenewableProfitEntity::getTime, end));
        // 需要排除的日期
        List<Long> times =
                dataCalibrations.stream()
                        .map(RenewableProfitEntity::getTime)
                        .collect(Collectors.toList());
        renewableProfitRequest.setDataCalibrationTimes(times);
        // 需要缓存里的查询出来 但是要排除了 已经数据校准的 日期的 以数据校准的数据为主
        RenewableProfitEntity renewableProfitEntity =
                baseMapper.queryRenewableTotalProfit(renewableProfitRequest);

        if (renewableProfitEntity == null) {
            // 有可能 查询不出来 缓存
            renewableProfitEntity = new RenewableProfitEntity();
        }
        if (CollectionUtil.isNotEmpty(dataCalibrations)) {
            RenewableProfitEntity finalRenewableProfitEntity = renewableProfitEntity;
            dataCalibrations.forEach(
                    dataCalibration ->
                            // 使用反射把数据加一下 把 dataCalibration 的对应属性添加到 pvWindProfitEntity 上
                            RenewableUtils.renewableProfitFieldAdd(
                                    finalRenewableProfitEntity, dataCalibration));
        }
        // 把这一段的 数据校准数据拿到
        return renewableProfitEntity;
    }

    @Override
    public RenewableProfitEntity queryRenewableData(RenewableProfitOneRequest request) {
        // 先按照 取数据校准后的如果没 则去查询缓存的
        RenewableProfitEntity dataCalibrationRenewableProfitEntity =
                baseMapper.selectOne(
                        new LambdaQueryWrapper<RenewableProfitEntity>()
                                .eq(RenewableProfitEntity::getProjectId, request.getProjectId())
                                .eq(RenewableProfitEntity::getRenewableType, request.getType())
                                .eq(RenewableProfitEntity::getTime, request.getTime())
                                .eq(RenewableProfitEntity::isDataCalibrationFlag, true));
        if (dataCalibrationRenewableProfitEntity != null) {
            return dataCalibrationRenewableProfitEntity;
        }
        // 查询某一天的 一条记录 这个项目的这个时间点的 这个类型的
        return baseMapper.selectOne(
                new LambdaQueryWrapper<RenewableProfitEntity>()
                        .eq(RenewableProfitEntity::getProjectId, request.getProjectId())
                        .eq(RenewableProfitEntity::getRenewableType, request.getType())
                        .eq(RenewableProfitEntity::getTime, request.getTime())
                        .eq(RenewableProfitEntity::isDataCalibrationFlag, false));
    }
}
