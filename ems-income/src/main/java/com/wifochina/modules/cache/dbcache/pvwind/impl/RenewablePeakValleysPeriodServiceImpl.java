package com.wifochina.modules.cache.dbcache.pvwind.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.onoff.OnOffComponent;
import com.wifochina.common.price.RenewableModelPriceFormulaComponent;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.income.caculate.pvwind.RenewableTimeSeriesService;
import com.wifochina.modules.income.query.pvwind.RenewableIncomeQueryService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.remedies.request.DataCalibrationRenewable;
import com.wifochina.modules.renewable.AbstractRenewableAdapterService;
import com.wifochina.modules.renewable.RenewablePowerDatas;
import com.wifochina.modules.renewable.RenewableUtils;
import com.wifochina.modules.renewable.context.CalcOnOffFlagContext;
import com.wifochina.modules.renewable.context.CalcRenewableProfitCoreContext;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.renewable.request.RenewableProfitOneRequest;
import com.wifochina.modules.renewable.request.RenewableProfitRequest;
import com.wifochina.modules.renewable.vo.RenewableProfitVo;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created on 2024/4/12 13:47.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RenewablePeakValleysPeriodServiceImpl
        extends AbstractRenewableAdapterService<RenewableProfitEntity> {

    private final RenewableIncomeQueryService renewableIncomeQueryService;
    private final RenewableModelPriceFormulaComponent pvWindModelFormulaComponent;
    private final ElectricPriceService electricPriceService;

    public RenewablePeakValleysPeriodServiceImpl(
            RenewableTimeSeriesService renewableTimeSeriesService,
            OnOffComponent onOffComponent,
            RenewableIncomeQueryService renewableIncomeQueryService,
            RenewableModelPriceFormulaComponent pvWindModelFormulaComponent,
            ElectricPriceService electricPriceService) {
        super(renewableTimeSeriesService, onOffComponent);
        this.renewableIncomeQueryService = renewableIncomeQueryService;
        this.pvWindModelFormulaComponent = pvWindModelFormulaComponent;
        this.electricPriceService = electricPriceService;
    }

    private RenewableProfitEntity getPvWindEntity(
            LocalDate time,
            String calculateType,
            ProjectEntity projectEntity,
            boolean dataCalibrationFlag) {
        ZonedDateTime zonedDateTime = time.atStartOfDay(ZoneId.of(projectEntity.getTimezone()));
        long oneDayZero = zonedDateTime.toEpochSecond();
        RenewableProfitEntity renewableProfitEntity = new RenewableProfitEntity();
        renewableProfitEntity.setTime(oneDayZero);
        renewableProfitEntity.setProjectId(projectEntity.getId());
        renewableProfitEntity.setYear(time.getYear());
        renewableProfitEntity.setMonth(time.getMonthValue());
        renewableProfitEntity.setDay(time.getDayOfMonth());
        renewableProfitEntity.setRenewableType(calculateType);

        // 1.3.7 新增的 标记这个记录是否是 数据校准的记录
        renewableProfitEntity.setDataCalibrationFlag(dataCalibrationFlag);
        return renewableProfitEntity;
    }

    public void calculatePvWindTotal(RenewableProfitEntity renewableProfitEntity) {
        // 总放电收益
        double totalDischargeQuantity =
                renewableProfitEntity.getFlatDischargeQuantity()
                        + renewableProfitEntity.getVallyDischargeQuantity()
                        + renewableProfitEntity.getPeakDischargeQuantity()
                        + renewableProfitEntity.getTipDischargeQuantity()
                        + renewableProfitEntity.getDeepVallyDischargeQuantity();
        renewableProfitEntity.setTotalDischargeSelfQuantity(totalDischargeQuantity);

        double totalDischargeBenefit =
                renewableProfitEntity.getFlatDischargeBenefit()
                        + renewableProfitEntity.getVallyDischargeBenefit()
                        + renewableProfitEntity.getPeakDischargeBenefit()
                        + renewableProfitEntity.getTipDischargeBenefit()
                        + renewableProfitEntity.getDeepVallyDischargeBenefit();
        renewableProfitEntity.setTotalDischargeSelfBenefit(totalDischargeBenefit);
        // 还有一个 totalAgreementBenefit 我没有把每个尖峰平谷段内的 协议部分保存 感觉不需要
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculateSave(
            LocalDate time, String calculateType, RenewableAdapterContext context) {
        List<RenewableProfitEntity> results =
                calculateRenewableProfit(time, calculateType, context);
        if (CollectionUtil.isNotEmpty(results)) {
            // 这里可能需要根据
            RenewableProfitEntity renewableProfitEntity = results.get(0);
            // 需要去查询 old 一个 id 然后才能执行成功 saveOrUpdate 否则id 是空 ,因为重跑数据的时候 需要update
            RenewableProfitEntity old =
                    renewableIncomeQueryService.getOne(
                            new LambdaQueryWrapper<RenewableProfitEntity>()
                                    .eq(RenewableProfitEntity::isDataCalibrationFlag, false)
                                    .eq(
                                            RenewableProfitEntity::getProjectId,
                                            renewableProfitEntity.getProjectId())
                                    .eq(
                                            RenewableProfitEntity::getRenewableType,
                                            renewableProfitEntity.getRenewableType())
                                    .eq(
                                            RenewableProfitEntity::getTime,
                                            renewableProfitEntity.getTime()));
            if (old != null) {
                renewableProfitEntity.setId(old.getId());
            }
            renewableIncomeQueryService.saveOrUpdate(renewableProfitEntity);
        }
    }

    /**
     * 计算并且保存 给 数据校准 使用的
     *
     * @param time
     * @param calculateType
     */
    @Override
    public void calculateSaveForDataCalibration(
            LocalDate time, String calculateType, RenewableDataCalibrationAdapterContext context) {
        List<RenewableProfitEntity> results =
                calculateRenewableProfitForDataCalibration(time, calculateType, context);
        if (CollectionUtil.isNotEmpty(results)) {
            RenewableProfitEntity renewableProfitEntity = results.get(0);
            String userId =
                    (SecurityUtil.getUserId() == null ? "system" : SecurityUtil.getUserId());
            renewableProfitEntity.setUserId(userId);
            RenewableProfitEntity old =
                    renewableIncomeQueryService.getOne(
                            new LambdaQueryWrapper<RenewableProfitEntity>()
                                    .eq(
                                            RenewableProfitEntity::getProjectId,
                                            renewableProfitEntity.getProjectId())
                                    .eq(
                                            RenewableProfitEntity::getRenewableType,
                                            renewableProfitEntity.getRenewableType())
                                    .eq(RenewableProfitEntity::isDataCalibrationFlag, true)
                                    .eq(
                                            RenewableProfitEntity::getTime,
                                            renewableProfitEntity.getTime()));
            if (old != null) {
                renewableProfitEntity.setId(old.getId());
            }
            renewableIncomeQueryService.saveOrUpdate(renewableProfitEntity);
        }
    }

    @Override
    public List<RenewableProfitEntity> calculateRenewableProfit(
            LocalDate time, String calculateType, RenewableAdapterContext context) {
        ProjectEntity project = context.project();
        RenewableProfitEntity renewableProfitEntity =
                getPvWindEntity(time, calculateType, project, false);
        // 得到 开关context
        CalcOnOffFlagContext calcOnOffFlagContext = getCalcOnOffFlagContext(project, calculateType);
        // 这里是 尖峰平谷的国内的 可再生能源 是一天只有一条记录, 尖峰平谷的数据都在 这个对象的 各个属性上 和 国外动态电价不一样, 但是其实, 可以统一
        // 就是把尖峰平谷改成和国外的一样
        for (ElectricPriceEntity price : context.electricPriceParent().getPeriodPriceList()) {
            // 这个是核心方法 是计算每个段的 根据每个段的price去计算收益
            calcRenewablePeriodProfitCore(
                    new CalcRenewableProfitCoreContext() {
                        @Override
                        public LocalDate time() {
                            return time;
                        }

                        @Override
                        public ProjectEntity project() {
                            return project;
                        }

                        @Override
                        public CalculateTypeEnum calculateType() {
                            return CalculateTypeEnum.valueOf(calculateType);
                        }

                        @Override
                        public CalcOnOffFlagContext calcOnOffFlagContext() {
                            return calcOnOffFlagContext;
                        }
                    },
                    price,
                    // 这里是super price 因为有的模式计算比如全额上网只有super才有 历史遗留问题吧
                    context.electricPriceParent(),
                    renewableProfitEntity);
        }
        // 做一个合计的计算
        calculatePvWindTotal(renewableProfitEntity);
        return List.of(renewableProfitEntity);
    }

    //  暂时留着 这个是 calcRenewablePeriodProfitCore 方法的实现
    //            double pvPower = 0.0;
    //            double onLinePower = 0.0;
    //            double dcdcPower = 0.0;
    //            if (pvFlag) {
    //                // 计算 pv 的发电量
    //                pvPower =
    //                        pvWindTimeSeriesService.getPower(
    //                                calculateType, time, context.project(), price);
    //                totalPower = totalPower + pvPower;
    //                // 计算 pv 的 上网的电量
    //                onLinePower =
    //                        pvWindTimeSeriesService.getPower(
    //                                CalculateTypeEnum.ONLINE_POWER.name(),
    //                                time,
    //                                context.project(),
    //                                price);
    //            }
    //            if (dcdcFlag) {
    //                // 计算dcdc的 power
    //                dcdcPower =
    //                        pvWindTimeSeriesService.getPower(
    //                                CalculateTypeEnum.DCDC_POWER.name(),
    //                                time,
    //                                context.project(),
    //                                price);
    //            }
    //            PowerDatas powerDatas = getPowerDatas(pvPower, onLinePower, dcdcPower);
    //            // 根据 3个 power计算出来
    //            calculatePeriod(
    //                    project,
    //                    context.electricPriceParent(),
    //                    price,
    //                    renewableProfitEntity,
    //                    calculateType,
    //                    powerDatas);

    @Override
    public List<RenewableProfitEntity> calculateRenewableProfitForDataCalibration(
            LocalDate time, String calculateType, RenewableDataCalibrationAdapterContext context) {
        double benefit;
        double fullInternetBenefit;
        double onLineBenefit;
        double agreementBenefit;
        ElectricPriceEntity superPrice = context.electricPriceParent();
        ProjectEntity project = context.project();
        RenewableProfitEntity renewableProfitEntity =
                getPvWindEntity(time, calculateType, project, true);
        DataCalibrationRenewable dataCalibrationRenewable = context.dataCalibration();
        // 这里是尖峰平谷的 pv 方式 是一天只有一条记录
        // 这里根据type作为key 因为会有相同的type 时段不同, 比如尖峰段10-14 那可能会有2条记录 分别是 价格相同但是 10-12 12-14
        // 这样, 如果这样数据校准
        // 会循环多了 这里只让每个type 保持一条ElectricPriceEntity
        Map<Integer, ElectricPriceEntity> typeElectricPriceMap =
                context.electricPriceParent().getPeriodPriceList().stream()
                        .collect(
                                Collectors.toMap(
                                        ElectricPriceEntity::getPeriod,
                                        v -> v,
                                        (existing, replacement) -> existing));
        double onlinePower =
                dataCalibrationRenewable.getOnlineEnergy() == null
                        ? 0.0
                        : dataCalibrationRenewable.getOnlineEnergy();
        // 如果前段填写的是这个
        CalculateTypeEnum calculateTypeEnum = CalculateTypeEnum.valueOf(calculateType);
        double totalSelfPower =
                dataCalibrationRenewable.getTotalSelfEnergy() == null
                        ? 0.0
                        : dataCalibrationRenewable.getTotalSelfEnergy();
        fullInternetBenefit =
                pvWindModelFormulaComponent.fullInternetFormula(
                        calculateTypeEnum,
                        superPrice,
                        new RenewablePowerDatas() {
                            @Override
                            public Double power() {
                                // 全额上网 前端只会传一个 馈网电量
                                return onlinePower;
                            }
                        });

        agreementBenefit =
                pvWindModelFormulaComponent.agreementModelFormula(
                        calculateTypeEnum,
                        superPrice,
                        project,
                        new RenewablePowerDatas() {
                            @Override
                            public Double selfPower() {
                                // 协议上网模式 前端只会有一个 总的自用电 和 一个馈网电量 2个提交来
                                return totalSelfPower;
                            }
                        });
        onLineBenefit =
                pvWindModelFormulaComponent.onlineFormula(
                        calculateTypeEnum,
                        superPrice,
                        project,
                        new RenewablePowerDatas() {
                            @Override
                            public Double onlinePower() {
                                return onlinePower;
                            }
                        });
        // 全额上网收益
        renewableProfitEntity.setTotalFullInternetAccessBenefit(fullInternetBenefit);
        // 馈网
        renewableProfitEntity.setTotalOnlineQuantity(onlinePower);
        renewableProfitEntity.setTotalOnlineBenefit(onLineBenefit);
        // 1.3.7 add 协议电价模式的收益
        renewableProfitEntity.setTotalAgreementBenefit(agreementBenefit);
        double totalFrontSelfPower = 0.0;
        for (Map.Entry<Integer, ElectricPriceEntity> entry : typeElectricPriceMap.entrySet()) {
            Integer period = entry.getKey();
            ElectricPriceEntity price = entry.getValue();
            initPrice(superPrice, price);
            totalFrontSelfPower += dataCalibrationRenewable.getEnergy(period);
            // 这里需要根据 period 去拿dataCalibrationPvWind 里面的属性
            // 根据 3个 power计算出来
            benefit =
                    pvWindModelFormulaComponent.periodModelFormula(
                            calculateTypeEnum,
                            superPrice,
                            price,
                            new RenewablePowerDatas() {
                                @Override
                                public Double selfPower() {
                                    return dataCalibrationRenewable.getEnergy(period);
                                }
                            });

            if (benefit < 0) {
                benefit = 0;
            }
            switchSetPeakValleysData(
                    price,
                    renewableProfitEntity,
                    period,
                    benefit,
                    dataCalibrationRenewable.getEnergy(period),
                    0.0,
                    0.0);
        }
        boolean isFlullInternet = false;
        if (totalFrontSelfPower == 0.0 && onlinePower > 0.0) {
            // 如果前端没有传尖峰平谷 并且只传了一个 onlinePower 那就是全额上网 把这个online 放到 totalDischargeQuantity,
            // 因为全额上网的时候 用的就是这个字段Gird 是总得馈网电表 包括了pv和wind 所以不是用 totlaOnlineQuantity字段了
            isFlullInternet = true;
        }
        // 做一个合计的计算
        // 这里如果是 这个值有的话就应该是 是一个 协议模式, 协议模式 因为前端是直接把 自用电 勇 total传上来 所以
        if (dataCalibrationRenewable.getTotalSelfEnergy() > 0.0) {
            renewableProfitEntity.setTotalDischargeQuantity(totalSelfPower);
            renewableProfitEntity.setTotalDischargeSelfBenefit(agreementBenefit);
            renewableProfitEntity.setTotalDischargeSelfQuantity(totalSelfPower);
        } else {
            // 如果不是协议的 那么就是 全额或者 时段, 时段就会按照前端每个时段 之和
            if (isFlullInternet) {
                renewableProfitEntity.setTotalDischargeQuantity(onlinePower);
            } else {
                renewableProfitEntity.setTotalDischargeQuantity(totalFrontSelfPower);
            }
            calculatePvWindTotal(renewableProfitEntity);
        }
        return List.of(renewableProfitEntity);
    }

    private static void calculateOnLineAndFullInternetForDataCalibration(
            String calculateType,
            RenewableDataCalibrationAdapterContext context,
            Double totalSelfPower,
            Double onlinePower,
            RenewableProfitEntity renewableProfitEntity) {
        ElectricPriceEntity superPrice = context.electricPriceParent();
        double fullInternetBenefit = 0.0;
        double onLineBenefit = 0.0;
        if (calculateType.equals(CalculateTypeEnum.PV.name())) {
            // totalSelfPower + onlinePower 就等于 总的 pvPower
            // 全额收益就是 pvPrice * pvPower (但是没有直接的 pvPower 有所有的自用电 + 一个总计的馈网 就是总计的pvPower)
            fullInternetBenefit = superPrice.getPvPrice() * (totalSelfPower + onlinePower);

            onLineBenefit =
                    ((superPrice.getPvSubsidyPrice() + superPrice.getPvDfPrice()) * onlinePower);
        } else if (calculateType.equals(CalculateTypeEnum.WIND.name())) {

            fullInternetBenefit = superPrice.getWindPrice() * (totalSelfPower + onlinePower);
            onLineBenefit =
                    ((superPrice.getWindSubsidyPrice() + superPrice.getWindDfPrice())
                            * onlinePower);
        }
        renewableProfitEntity.setTotalOnlineBenefit(
                renewableProfitEntity.getTotalOnlineBenefit() + onLineBenefit);
        renewableProfitEntity.setTotalFullInternetAccessBenefit(
                renewableProfitEntity.getTotalFullInternetAccessBenefit() + fullInternetBenefit);
        renewableProfitEntity.setTotalOnlineQuantity(
                renewableProfitEntity.getTotalOnlineQuantity() + onlinePower);
    }

    @Override
    public RenewableProfitVo calculateRenewableProfitVo(
            BenefitRequest benefitRequest, String calculateType, ProjectEntity project) {
        long yesterdayEndTime = MyTimeUtil.getYesterdayEndTime(project.getTimezone());
        boolean todaySearch = benefitRequest.getEndDate() > yesterdayEndTime;
        //        if (todaySearch) {
        //            // 如果是包括了查询今天时间 则 把 请求时间放到 今天的前一天23:59:59, 这个也可以没有,
        //            // 因为带着今天去查询数据库也查询不到今天的缓存 为了更好理解吧
        //            benefitRequest.setEndDate(yesterdayEndTime);
        //        }
        RenewableProfitVo renewableProfitVo = new RenewableProfitVo();
        // 直接查询缓存中的数据
        RenewableProfitRequest request =
                new RenewableProfitRequest(
                        benefitRequest.getStartDate(),
                        benefitRequest.getEndDate(),
                        project.getId(),
                        calculateType);
        RenewableProfitEntity beforePvWindProfit =
                renewableIncomeQueryService.queryRenewableTotalProfit(request);
        // 如果需要查询今天
        if (todaySearch) {
            long todayZero = MyTimeUtil.getTodayZeroTime(project.getTimezone());
            // 找到 那个匹配到的 电价配置策略
            ElectricPriceEntity electricPriceParent =
                    electricPriceService.getElectricPriceMatch(project.getId(), todayZero);
            List<RenewableProfitEntity> results = new ArrayList<>();
            if (electricPriceParent != null) {
                // 大致意思是 : 如果今天查询出数据 把今天的和今天前的数据合并
                results =
                        calculateRenewableProfit(
                                LocalDate.now(ZoneId.of(project.getTimezone())),
                                calculateType,
                                new RenewableAdapterContext() {
                                    @Override
                                    public ElectricPriceEntity electricPriceParent() {
                                        return electricPriceParent;
                                    }

                                    @Override
                                    public ProjectEntity project() {
                                        return project;
                                    }
                                });
            }
            if (beforePvWindProfit != null) {
                if ((CollectionUtil.isNotEmpty(results))) {
                    // 计算 把缓存的和今日的 加起来
                    RenewableProfitEntity todayPvWindProfit = results.get(0);
                    RenewableUtils.renewableProfitFieldAdd(beforePvWindProfit, todayPvWindProfit);
                    // calPvWindCacheAndToday(beforePvWindProfit, todayPvWindProfit);
                    BeanUtil.copyProperties(beforePvWindProfit, renewableProfitVo);
                }
            } else if (CollectionUtil.isNotEmpty(results)) {
                RenewableProfitEntity todayPvWindProfit = results.get(0);
                BeanUtil.copyProperties(todayPvWindProfit, renewableProfitVo);
            }
        } else {
            // 不查询今天的 则直接 把before的数据 copy到 renewableProfitVo
            if (beforePvWindProfit != null) {
                BeanUtil.copyProperties(beforePvWindProfit, renewableProfitVo);
            }
        }

        return renewableProfitVo;
    }

    private void calPvWindCacheAndToday(
            RenewableProfitEntity renewableProfitEntity,
            RenewableProfitEntity todayRenewableProfitEntity) {
        for (Field field : renewableProfitEntity.getClass().getDeclaredFields()) {
            if (field.getType() == double.class) {
                try {
                    if (field.getName().contains("Price")) {
                        continue;
                    }
                    field.setAccessible(true);
                    double value = field.getDouble(todayRenewableProfitEntity);
                    double value2 = field.getDouble(renewableProfitEntity);
                    field.set(renewableProfitEntity, value + value2);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    @Override
    public void calculatePeriod(
            ProjectEntity projectEntity,
            ElectricPriceEntity superPrice,
            ElectricPriceEntity price,
            RenewableProfitEntity renewableProfitEntity,
            String pvOrWindType,
            RenewablePowerDatas renewablePowerDatas) {

        Integer type = price.getPeriod();
        Double power = renewablePowerDatas.power();
        Double onlinePower = renewablePowerDatas.onlinePower();
        Double dcdcPower = renewablePowerDatas.dcdcPower();
        Double selfPower = renewablePowerDatas.selfPower();

        // pv 收益公式=(国家补助+各个时段电价)*各个时段的自用电量+（国家补助+脱硫标杆电价）*上网电量
        double benefit;
        double fullInternetBenefit;
        double onLineBenefit;
        double agreementBenefit;
        initPrice(superPrice, price);

        CalculateTypeEnum calculateTypeEnum = CalculateTypeEnum.valueOf(pvOrWindType);
        RenewableModelPriceFormulaComponent.RenewableFormulaBenefit renewableFormulaBenefit =
                // 计算 各种模式下的收益
                pvWindModelFormulaComponent.collectFormula(
                        calculateTypeEnum, superPrice, price, projectEntity, renewablePowerDatas);
        benefit = renewableFormulaBenefit.getPeriodModelBenefit();
        fullInternetBenefit = renewableFormulaBenefit.getFullInternetModelBenefit();
        agreementBenefit = renewableFormulaBenefit.getAgreementModelBenefit();
        onLineBenefit = renewableFormulaBenefit.getOnlineBenefit();
        // 总pv 电量
        // 记录一下: 全额上网模式 电量用的是这个power 取这个 totalDischargeQuantity
        // 时段模式 : 和下面的 协议模式一样
        // 协议模式 : 自用电量 分到了每个尖峰平谷里面 但是有一个总的 字段是 totalDischargeSelfQuantity, 和 一个 馈网电量
        // totalOnlineQuantity
        renewableProfitEntity.setTotalDischargeQuantity(
                renewableProfitEntity.getTotalDischargeQuantity() + power);
        renewableProfitEntity.setTotalOnlineBenefit(
                renewableProfitEntity.getTotalOnlineBenefit() + onLineBenefit);
        renewableProfitEntity.setTotalFullInternetAccessBenefit(
                renewableProfitEntity.getTotalFullInternetAccessBenefit() + fullInternetBenefit);
        renewableProfitEntity.setTotalOnlineQuantity(
                renewableProfitEntity.getTotalOnlineQuantity() + onlinePower);
        // 1.3.7 add 协议电价模式的收益
        renewableProfitEntity.setTotalAgreementBenefit(
                renewableProfitEntity.getTotalAgreementBenefit() + agreementBenefit);
        if (benefit < 0) {
            benefit = 0;
        }
        // 根据 switch 设置 尖峰平谷 对应的属性
        switchSetPeakValleysData(
                price, renewableProfitEntity, type, benefit, selfPower, onlinePower, dcdcPower);
    }

    private static void switchSetPeakValleysData(
            ElectricPriceEntity price,
            RenewableProfitEntity renewableProfitEntity,
            Integer type,
            double benefit,
            Double selfPower,
            Double onlinePower,
            Double dcdcPower) {
        switch (type) {
                // 电价时段（谷1 平2 峰3 尖4）
            case 1:
                // (pv - online ) * (price + 国家补助)
                renewableProfitEntity.setVallySellPrice(price.getSellPrice());
                // 谷放电收益
                renewableProfitEntity.setVallyDischargeBenefit(
                        renewableProfitEntity.getVallyDischargeBenefit() + benefit);
                // 谷放电自用量
                renewableProfitEntity.setVallyDischargeQuantity(
                        renewableProfitEntity.getVallyDischargeQuantity() + selfPower);
                // 上网电量
                renewableProfitEntity.setVallyDischargeQuantityOnline(
                        renewableProfitEntity.getVallyDischargeQuantityOnline() + onlinePower);
                // dcdc
                renewableProfitEntity.setVallyDischargeQuantityDcdc(
                        renewableProfitEntity.getVallyDischargeQuantityDcdc() + dcdcPower);
                break;
            case 2:
                renewableProfitEntity.setFlatSellPrice(price.getSellPrice());
                renewableProfitEntity.setFlatDischargeBenefit(
                        renewableProfitEntity.getFlatDischargeBenefit() + benefit);

                // 平端电收益
                renewableProfitEntity.setFlatDischargeQuantity(
                        renewableProfitEntity.getFlatDischargeQuantity() + selfPower);
                // 上网电量
                renewableProfitEntity.setFlatDischargeQuantityOnline(
                        renewableProfitEntity.getFlatDischargeQuantityOnline() + onlinePower);
                // dcdc
                renewableProfitEntity.setFlatDischargeQuantityDcdc(
                        renewableProfitEntity.getFlatDischargeQuantityDcdc() + dcdcPower);

                break;
            case 3:
                renewableProfitEntity.setPeakSellPrice(price.getSellPrice());
                renewableProfitEntity.setPeakDischargeBenefit(
                        renewableProfitEntity.getPeakDischargeBenefit() + benefit);

                // 峰放电收益
                renewableProfitEntity.setPeakDischargeQuantity(
                        renewableProfitEntity.getPeakDischargeQuantity() + selfPower);
                // 上网电量
                renewableProfitEntity.setPeakDischargeQuantityOnline(
                        renewableProfitEntity.getPeakDischargeQuantityOnline() + onlinePower);
                // dcdc
                renewableProfitEntity.setPeakDischargeQuantityDcdc(
                        renewableProfitEntity.getPeakDischargeQuantityDcdc() + dcdcPower);
                break;
            case 4:
                renewableProfitEntity.setTipSellPrice(price.getSellPrice());
                renewableProfitEntity.setTipDischargeBenefit(
                        renewableProfitEntity.getTipDischargeBenefit() + benefit);

                // 尖峰放电收益
                renewableProfitEntity.setTipDischargeQuantity(
                        renewableProfitEntity.getTipDischargeQuantity() + selfPower);
                renewableProfitEntity.setTipDischargeQuantityOnline(
                        renewableProfitEntity.getTipDischargeQuantityOnline() + onlinePower);
                // dcdc
                renewableProfitEntity.setTipDischargeQuantityDcdc(
                        renewableProfitEntity.getTipDischargeQuantityDcdc() + dcdcPower);
                break;
            case 5:
                renewableProfitEntity.setDeepVallySellPrice(price.getSellPrice());
                renewableProfitEntity.setDeepVallyDischargeBenefit(
                        renewableProfitEntity.getDeepVallyDischargeBenefit() + benefit);

                // 深谷放电收益
                renewableProfitEntity.setDeepVallyDischargeQuantity(
                        renewableProfitEntity.getDeepVallyDischargeQuantity() + selfPower);
                renewableProfitEntity.setDeepVallyDischargeQuantityOnline(
                        renewableProfitEntity.getDeepVallyDischargeQuantityOnline() + onlinePower);
                // dcdc
                renewableProfitEntity.setDeepVallyDischargeQuantityDcdc(
                        renewableProfitEntity.getDeepVallyDischargeQuantityDcdc() + dcdcPower);
                break;
            default:
        }
    }

    @Override
    public void queryPvOrWindData(RenewableProfitOneRequest request, QueryRenewableResult result) {
        RenewableProfitEntity renewableProfitEntity =
                renewableIncomeQueryService.queryRenewableData(request);
        if (renewableProfitEntity == null) {
            renewableProfitEntity = new RenewableProfitEntity();
        }
        result.peakValleysPeriodPostProcessor(List.of(renewableProfitEntity));
    }

    @Override
    public void queryCalibration(
            String projectId, String type, Long start, Long end, QueryRenewableResult result) {
        List<RenewableProfitEntity> list =
                renewableIncomeQueryService
                        .lambdaQuery()
                        .eq(RenewableProfitEntity::getRenewableType, type)
                        .eq(RenewableProfitEntity::getProjectId, projectId)
                        .ge(RenewableProfitEntity::getTime, start)
                        .le(RenewableProfitEntity::getTime, end)
                        .eq(RenewableProfitEntity::isDataCalibrationFlag, true)
                        .list();
        result.peakValleysPeriodPostProcessor(list);
    }

    @Override
    public Set<String> type() {
        return Set.of(ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD.getValue());
    }
}
