package com.wifochina.modules.cache.dbcache.electric.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.electric.ElectricService;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.mapper.ElectricMapper;
import com.wifochina.modules.electric.request.ElectricPriceRequest;
import com.wifochina.modules.electric.request.ElectricRequest;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ElectricServiceImpl extends ServiceImpl<ElectricMapper, ElectricEntity>
        implements ElectricService {

    @Override
    public List<ElectricEntity> queryElectricSumGroupByDeviceId(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricSumGroupByDeviceId(electricRequest);
    }

    @Override
    public List<ElectricEntity> queryElectricMonthGroupByDeviceId(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricMonthGroupByDeviceId(electricRequest);
    }

    @Override
    public List<ElectricEntity> queryElectricYear(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricYear(electricRequest);
    }

    @Override
    public ElectricEntity queryElectricTotalProfit(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricTotalProfit(electricRequest);
    }

    @Override
    public List<ElectricEntity> queryElectricEveryDayProfit(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricEveryDayProfit(electricRequest);
    }

    @Override
    public List<ElectricEntity> queryElectricPrice(ElectricPriceRequest electricPriceRequest) {
        return this.baseMapper.queryElectricPrice(electricPriceRequest);
    }

    /** 查询每月储能收益 */
    @Override
    public List<ElectricEntity> queryElectricProfitByMonth(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricProfitByMonth(electricRequest);
    }

    /** 查询每日储能收益 */
    @Override
    public List<ElectricEntity> queryElectricProfitByDay(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricProfitByDay(electricRequest);
    }

    @Override
    public List<ElectricEntity> queryElectricQuantityByDay(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricQuantityByDay(electricRequest);
    }

    @Override
    public List<ElectricEntity> queryElectricQuantityByMonth(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricQuantityByMonth(electricRequest);
    }

    @Override
    public ElectricEntity queryElectricByGroupId(String groupId, long time) {
        return this.baseMapper.selectOne(
                Wrappers.lambdaQuery(ElectricEntity.class)
                        .eq(ElectricEntity::getDeviceId, groupId)
                        .eq(ElectricEntity::getTime, time));
    }

    @Override
    public Double getTotalProfitByDayAndProjectId(String projectId, int year, int month, int day) {
        return this.baseMapper.getTotalProfitByDayAndProjectId(projectId, year, month, day);
    }
}
