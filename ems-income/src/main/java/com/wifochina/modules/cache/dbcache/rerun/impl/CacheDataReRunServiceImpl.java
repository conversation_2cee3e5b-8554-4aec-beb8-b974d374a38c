package com.wifochina.modules.cache.dbcache.rerun.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.wifochina.common.constants.ReRunTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.ApplicationHolder;
import com.wifochina.common.util.ElectricCalibrationTypeEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.electric.ElectricAdapterChooser;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.ElectricService;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.timesharing.TimeSharingCacheService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.controller.rerun.service.CacheDataReRunService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.request.CacheDataReRunRequest;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.request.DataCalibrationEms;
import com.wifochina.modules.remedies.request.DataCalibrationRenewable;
import com.wifochina.modules.remedies.service.DataCalibrationService;
import com.wifochina.modules.renewable.RenewableAdapterChooser;
import com.wifochina.modules.renewable.RenewableAdapterService;
import com.wifochina.modules.renewable.entity.RenewableDynamicProfitEntity;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.report.service.DayReportCacheService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * Created on 2024/6/24 19:28.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class CacheDataReRunServiceImpl implements CacheDataReRunService {

    private final ProjectService projectService;
    private final ElectricPriceService electricPriceService;
    private final RenewableAdapterChooser renewableAdapterChooser;
    private final ElectricAdapterChooser batteryChooser;
    private final TimeSharingCacheService timeSharingCacheService;
    private final GroupService groupService;
    private final StrategyService strategyService;
    private final DemandService demandService;
    private final ElectricService electricService;
    private final DataCalibrationService dataCalibrationService;
    private final DayReportCacheService dayReportCacheService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    static class PvWindDataTemp {
        Map<Long, List<RenewableProfitEntity>> pvWindProfitMap = new HashMap<>();
    }

    /**
     * 重跑 PV OR WIND 收益
     *
     * @param request : request
     */
    @Override
    public void reRunRenewable(CacheDataReRunRequest request) {
        List<String> projectIds = request.getProjectIds();
        if (projectIds.isEmpty()) {
            // 跑所有项目
            projectIds =
                    projectService.getAllProjectByServiceDeployStatus().stream()
                            .map(ProjectEntity::getId)
                            .collect(Collectors.toList());
        }
        ReRunTypeEnum reRunTypeEnum = ReRunTypeEnum.valueOf(request.getType());
        List<LocalDate> dateRanges =
                MyTimeUtil.getRanges(request.getRange().getFrom(), request.getRange().getEnd());
        CountDownLatch countDownLatch = new CountDownLatch(projectIds.size());
        projectIds.forEach(
                projectId -> {
                    threadPoolTaskExecutor.submit(
                            () -> {
                                // 测试后 改成 threadPoolExecutor
                                ProjectEntity projectEntity = projectService.getById(projectId);

                                long start =
                                        dateRanges
                                                .get(0)
                                                .atStartOfDay(
                                                        ZoneId.of(projectEntity.getTimezone()))
                                                .toEpochSecond();
                                long end =
                                        dateRanges
                                                .get(dateRanges.size() - 1)
                                                .atStartOfDay(
                                                        ZoneId.of(projectEntity.getTimezone()))
                                                .toEpochSecond();
                                long todayZero =
                                        LocalDate.now()
                                                .atStartOfDay(
                                                        ZoneId.of(projectEntity.getTimezone()))
                                                .toEpochSecond();
                                if (start == todayZero) {
                                    return;
                                }
                                if (end == todayZero) {
                                    end = todayZero - EmsConstants.ONE_DAY_SECOND;
                                }
                                // 查询出时间段所有的补值
                                Map<Long, List<RenewableProfitEntity>> map = new HashMap<>();
                                PvWindDataTemp temp = new PvWindDataTemp();
                                renewableAdapterChooser
                                        .choose(projectEntity.getElectricPriceType())
                                        .queryCalibration(
                                                projectId,
                                                reRunTypeEnum.name(),
                                                start,
                                                end,
                                                new RenewableAdapterService.QueryRenewableResult() {
                                                    @Override
                                                    public void peakValleysPeriodPostProcessor(
                                                            List<RenewableProfitEntity> t) {

                                                        temp.pvWindProfitMap =
                                                                t.stream()
                                                                        .collect(
                                                                                Collectors
                                                                                        .groupingBy(
                                                                                                RenewableProfitEntity
                                                                                                        ::getTime));
                                                    }

                                                    @Override
                                                    public void dynamicPeriodPostProcessor(
                                                            List<RenewableDynamicProfitEntity> t) {
                                                        // TODO ..1.3.7 暂时不支持 海外的 冲跑数据 这里也挺麻烦
                                                        // 后面再说来不及了
                                                    }
                                                });
                                DateTimeFormatter formatter =
                                        DateTimeFormatter.ofPattern("yyyy-MM-dd");
                                dateRanges.forEach(
                                        date -> {
                                            long timePoint =
                                                    date.atStartOfDay(
                                                                    ZoneId.of(
                                                                            projectEntity
                                                                                    .getTimezone()))
                                                            .toEpochSecond();
                                            log.info(
                                                    "dynamic reRunPVOrWind timePoint:{} reRunType:{}",
                                                    date.format(formatter),
                                                    reRunTypeEnum.name());
                                            ElectricPriceEntity electricPriceEntity =
                                                    electricPriceService.getElectricPriceMatch(
                                                            projectId, timePoint);
                                            if (electricPriceEntity != null) {
                                                // 处理补时的，如果是分组算的，当做新补值的，让补值再算一下
                                                List<RenewableProfitEntity>
                                                        renewableProfitEntities =
                                                                temp.pvWindProfitMap.get(timePoint);
                                                if (CollectionUtil.isNotEmpty(
                                                        renewableProfitEntities)) {
                                                    for (RenewableProfitEntity
                                                            renewableProfitEntity :
                                                                    renewableProfitEntities) {
                                                        DataCalibrationRenewable
                                                                dataCalibrationRenewable =
                                                                        new DataCalibrationRenewable();
                                                        setEnergy(
                                                                dataCalibrationRenewable,
                                                                renewableProfitEntity);
                                                        dataCalibrationRenewable.setDate(
                                                                date.toString());
                                                        dataCalibrationRenewable.setRenewableType(
                                                                reRunTypeEnum.name());
                                                        dataCalibrationService
                                                                .renewableDataCalibration(
                                                                        projectId,
                                                                        dataCalibrationRenewable);
                                                    }
                                                }
                                                try {
                                                    // 这里其实if 去掉 就能支持 国外的重跑数据
                                                    //                                        if
                                                    //                                        if
                                                    // (ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD
                                                    //
                                                    //  .name()
                                                    //
                                                    // .equals(projectEntity.getElectricPriceType())) {
                                                    // 必须要是尖峰平谷 国内的 才能重跑数据
                                                    renewableAdapterChooser
                                                            .choose(
                                                                    projectEntity
                                                                            .getElectricPriceType())
                                                            .calculateSave(
                                                                    date,
                                                                    reRunTypeEnum.name(),
                                                                    new RenewableAdapterService
                                                                            .RenewableAdapterContext() {
                                                                        @Override
                                                                        public ElectricPriceEntity
                                                                                electricPriceParent() {
                                                                            return electricPriceEntity;
                                                                        }

                                                                        @Override
                                                                        public ProjectEntity
                                                                                project() {
                                                                            return projectEntity;
                                                                        }
                                                                    });
                                                    //                                        }
                                                } catch (Exception e) {
                                                    log.error(
                                                            "项目 {} 保存 {} PV风电收益异常 {}",
                                                            projectEntity.getProjectName(),
                                                            date,
                                                            e.getMessage());
                                                }
                                            } else {
                                                log.error(
                                                        "项目 {} 找不到 日期{} 对应的 电价配置, 无法重跑 {} 数据",
                                                        projectEntity.getProjectName(),
                                                        date,
                                                        reRunTypeEnum.name());
                                            }
                                        });
                                countDownLatch.countDown();
                                log.info("还有{}个项目在执行中", countDownLatch.getCount());
                            });
                });
        try {
            countDownLatch.await();
            log.info("全部执行完毕");
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private void setEnergy(
            DataCalibrationRenewable dataCalibrationRenewable,
            RenewableProfitEntity renewableProfitEntity) {
        // TODO 这里不是很确定啊..
        dataCalibrationRenewable.setPeakSelfEnergy(
                renewableProfitEntity.getPeakDischargeQuantity());
        dataCalibrationRenewable.setFlatSelfEnergy(
                renewableProfitEntity.getFlatDischargeQuantity());
        dataCalibrationRenewable.setTipSelfEnergy(renewableProfitEntity.getTipDischargeQuantity());
        dataCalibrationRenewable.setVallySelfEnergy(
                renewableProfitEntity.getVallyDischargeQuantity());
        dataCalibrationRenewable.setDeepVallySelfEnergy(
                renewableProfitEntity.getDeepVallyDischargeQuantity());
        dataCalibrationRenewable.setTotalSelfEnergy(
                renewableProfitEntity.getTotalDischargeSelfQuantity());
        dataCalibrationRenewable.setOnlineEnergy(renewableProfitEntity.getTotalOnlineQuantity());
    }

    /**
     * 重跑 电池收益
     *
     * @param request : request
     */
    @Override
    public void reRunBattery(CacheDataReRunRequest request) {
        List<String> projectIds = request.getProjectIds();
        if (projectIds.isEmpty()) {
            // 跑所有项目
            projectIds =
                    projectService.getAllProjectByServiceDeployStatus().stream()
                            .map(ProjectEntity::getId)
                            .collect(Collectors.toList());
        }
        ReRunTypeEnum reRunTypeEnum = ReRunTypeEnum.valueOf(request.getType());
        List<LocalDate> dateRanges =
                MyTimeUtil.getRanges(request.getRange().getFrom(), request.getRange().getEnd());
        if (dateRanges.isEmpty()) {
            log.error("没有符合条件的日期");
        }

        for (String projectId : projectIds) {
            // 测试后 改成 threadPoolExecutor
            threadPoolTaskExecutor.submit(
                    () -> {
                        ProjectEntity projectEntity = projectService.getById(projectId);
                        long start =
                                dateRanges
                                        .get(0)
                                        .atStartOfDay(ZoneId.of(projectEntity.getTimezone()))
                                        .toEpochSecond();
                        long end =
                                dateRanges
                                        .get(dateRanges.size() - 1)
                                        .atStartOfDay(ZoneId.of(projectEntity.getTimezone()))
                                        .toEpochSecond();
                        long todayZero =
                                LocalDate.now()
                                        .atStartOfDay(ZoneId.of(projectEntity.getTimezone()))
                                        .toEpochSecond();
                        if (start == todayZero) {
                            return;
                        }
                        if (end == todayZero) {
                            end = todayZero - EmsConstants.ONE_DAY_SECOND;
                        }
                        // 查询出时间段所有的补值
                        // TODO 这里如果是海外 这里跑不了
                        List<ElectricEntity> groupEntities =
                                electricService
                                        .lambdaQuery()
                                        .eq(ElectricEntity::getProjectId, projectId)
                                        .ge(ElectricEntity::getTime, start)
                                        .le(ElectricEntity::getTime, end)
                                        .eq(
                                                ElectricEntity::getCalibrationType,
                                                ElectricCalibrationTypeEnum.ELECTRIC_GROUP
                                                        .getCode())
                                        .list();
                        // 按照天来转换为 list
                        Map<Long, List<ElectricEntity>> calibrationElectricMap =
                                groupEntities.stream()
                                        .collect(Collectors.groupingBy(ElectricEntity::getTime));
                        for (LocalDate date : dateRanges) {
                            long timePoint =
                                    date.atStartOfDay(ZoneId.of(projectEntity.getTimezone()))
                                            .toEpochSecond();
                            ElectricPriceEntity electricPriceEntity =
                                    electricPriceService.getElectricPriceMatch(
                                            projectId, timePoint);
                            if (electricPriceEntity != null) {
                                // 处理补时的，如果是分组算的，当做新补值的，让补值再算一下
                                List<ElectricEntity> calibrationElectricEntities =
                                        calibrationElectricMap.get(timePoint);
                                if (CollectionUtil.isNotEmpty(calibrationElectricEntities)) {
                                    for (ElectricEntity calibrationElectricEntity :
                                            calibrationElectricEntities) {
                                        DataCalibrationEms dataCalibrationEms =
                                                new DataCalibrationEms();
                                        BeanUtils.copyProperties(
                                                calibrationElectricEntity, dataCalibrationEms);
                                        dataCalibrationEms.setGroupId(
                                                calibrationElectricEntity.getDeviceId());
                                        dataCalibrationEms.setDate(date.toString());
                                        dataCalibrationService.emsDataCalibration(
                                                projectId, dataCalibrationEms);
                                    }
                                }
                                // 通过 chooser去选择 一个 执行 适配尖峰平谷 or 新的 自定义时段
                                try {

                                    batteryChooser
                                            .choose(projectEntity.getElectricPriceType())
                                            // 确认一下 是否能插入成功 saveOrUpdate
                                            .saveElectric(
                                                    new ElectricAdapterService
                                                            .ElectricAdapterContext() {
                                                        @Override
                                                        public long timePoint() {
                                                            return timePoint;
                                                        }

                                                        @Override
                                                        public List<ElectricPriceEntity>
                                                                periodPriceList() {
                                                            return electricPriceEntity
                                                                    .getPeriodPriceList();
                                                        }

                                                        @Override
                                                        public ProjectEntity project() {
                                                            return projectEntity;
                                                        }
                                                    });
                                    timeSharingCacheService.timeSharingCacheBattery(
                                            timePoint, projectEntity, electricPriceEntity);

                                } catch (Exception e) {

                                    log.error(
                                            "项目 {}  日期{} , 无法重跑 {} 数据",
                                            projectEntity.getProjectName(),
                                            date,
                                            reRunTypeEnum.name());
                                }

                            } else {
                                log.error(
                                        "项目 {} 找不到 日期{} 对应的 电价配置, 无法重跑 {} 数据",
                                        projectEntity.getProjectName(),
                                        date,
                                        reRunTypeEnum.name());
                            }
                        }
                    });
        }
    }

    @Override
    public void reRunDemand(CacheDataReRunRequest request) {
        List<String> projectIds = request.getProjectIds();
        List<LocalDate> dateRanges =
                MyTimeUtil.getRanges(request.getRange().getFrom(), request.getRange().getEnd());
        projectIds.forEach(
                projectId -> {
                    threadPoolTaskExecutor.submit(
                            () -> {
                                ProjectEntity projectEntity = projectService.getById(projectId);
                                List<GroupEntity> groupEntities =
                                        groupService.queryEnableDemandControl(projectId);
                                if (!groupEntities.isEmpty()) {
                                    for (GroupEntity groupEntity : groupEntities) {
                                        StrategyEntity strategyEntity =
                                                strategyService
                                                        .lambdaQuery()
                                                        .eq(
                                                                StrategyEntity::getGroupId,
                                                                groupEntity.getId())
                                                        .eq(StrategyEntity::getWeekDay, 0)
                                                        .one();
                                        dateRanges.forEach(
                                                date -> {
                                                    long time =
                                                            date.atStartOfDay(
                                                                            ZoneId.of(
                                                                                    projectEntity
                                                                                            .getTimezone()))
                                                                    .toEpochSecond();
                                                    try {
                                                        Map<Long, OaDemandData> controlMap =
                                                                demandService
                                                                        .getOriginAndActualDemand(
                                                                                "mean",
                                                                                time,
                                                                                time
                                                                                        + EmsConstants
                                                                                                .ONE_DAY_SECOND,
                                                                                projectEntity
                                                                                        .getTimezone(),
                                                                                groupEntity);
                                                        demandService
                                                                .saveOriginAndActualDemandToInfluxDb(
                                                                        strategyEntity
                                                                                .getGridControlPower(),
                                                                        controlMap,
                                                                        groupEntity);
                                                        log.info(
                                                                "{} calc in {}",
                                                                groupEntity.getName(),
                                                                LocalDateTime.ofEpochSecond(
                                                                        time,
                                                                        0,
                                                                        MyTimeUtil
                                                                                .getZoneOffsetFromZoneCode(
                                                                                        projectEntity
                                                                                                .getTimezone())));
                                                    } catch (Exception e) {
                                                        log.error(
                                                                "{} calc in {} error : {}",
                                                                groupEntity.getName(),
                                                                LocalDateTime.ofEpochSecond(
                                                                        time,
                                                                        0,
                                                                        MyTimeUtil
                                                                                .getZoneOffsetFromZoneCode(
                                                                                        projectEntity
                                                                                                .getTimezone())),
                                                                e.getMessage());
                                                    }
                                                });
                                    }
                                }
                            });
                });
    }

    /**
     * 重跑 需量的月度缓存 //正常只需要执行一次 在项目启动上线的时候 让其方法支持可以根据传参来执行 否则全量执行 ,全量执行为了1.4.0上线补全前面的所有的 月度需量缓存数据
     *
     * @param request : request
     */
    @Override
    public void reRunDemandMonthCache(CacheDataReRunRequest request) {

        //
    }

    @Override
    public void reRunDayReport(CacheDataReRunRequest request) {
        List<String> projectIds = request.getProjectIds();
        List<LocalDate> dateRanges =
                MyTimeUtil.getRanges(request.getRange().getFrom(), request.getRange().getEnd());
        projectIds.forEach(
                projectId -> {
                    threadPoolTaskExecutor.submit(
                            () -> {
                                ProjectEntity projectEntity = projectService.getById(projectId);
                                dateRanges.forEach(
                                        date -> {
                                            long time =
                                                    date.atStartOfDay(
                                                                    ZoneId.of(
                                                                            projectEntity
                                                                                    .getTimezone()))
                                                            .toEpochSecond();
                                            try {
                                                dayReportCacheService.saveDayReport(
                                                        projectEntity,
                                                        new RangeRequest()
                                                                .setStartDate(time)
                                                                .setEndDate(
                                                                        time
                                                                                + EmsConstants
                                                                                        .ONE_DAY_SECOND
                                                                                - 1));

                                                log.info(
                                                        "reRunDayReport  project:{} projectName:{} , date:{} success",
                                                        projectEntity.getId(),
                                                        projectEntity.getProjectName(),
                                                        date);
                                            } catch (Exception e) {
                                                log.error(
                                                        "reRunDayReport error : {}",
                                                        e.getMessage());
                                            }
                                        });
                            });
                });
    }
}
