package com.wifochina.modules.caculate.electric.impl;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.IdSearchUtils;
import com.wifochina.common.util.TimeContext;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.income.caculate.electric.IElectricIncomeCalculateService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

/**
 * Created on 2024/6/13 09:47. <br>
 * 电相关的 Influx 时序数据库 Service Impl
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class ElectricIncomeCalculateServiceImpl implements IElectricIncomeCalculateService {
    @Resource private InfluxClientService influxClient;

    @Override
    public void calculateTimeChargeDiff(
            TimeContext timeContext,
            ElectricPriceEntity price,
            ProjectEntity projectEntity,
            String queryString,
            CalculateResult calculateResult) {
        if (queryString == null) {
            calculateResult.result(0.0, 0.0);
            return;
        }
        // 通过正则 去解析 sql 找到 columns
        List<String> columns = getFieldsWithPattern(queryString);
        String outField = columns.get(0);
        String inField = columns.get(1);

        Pair<Long, Long> periodStartEndPair =
                MyTimeUtil.getPeriodStartEndPair(
                        price.getStartTime(),
                        price.getEndTime(),
                        timeContext.getTimePointDate().toEpochSecond(),
                        projectEntity.getTimezone());

        // 替换 string里面的 start 和 end的时间戳 core code
        String tempString = getAndReplaceTime(queryString, periodStartEndPair);
        log.debug("period:{},  sql : {}", price.getPeriod(), tempString);
        List<FluxTable> tables = influxClient.getQueryApi().query(tempString);
        double inValue = 0.0;
        double outValue = 0.0;
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                if (inField.equals(record.getField())) {
                    // 注意这个 是 +=
                    inValue += record.getValue() == null ? 0.0 : (Double) record.getValue();
                }
                if (outField.equals(record.getField())) {
                    outValue += record.getValue() == null ? 0.0 : (Double) record.getValue();
                }
            }
        }
        calculateResult.result(outValue, inValue);
    }

    private static @NotNull String getAndReplaceTime(
            String queryString, Pair<Long, Long> periodStartEndPair) {
        long periodStartTime = periodStartEndPair.getFirst();
        // forever 库 是一分钟一个点 influxdb 是 [) 的方式 也就是说 如果你传一个 00:00 01:00 那么最后一个 只能到59
        // 那么就会少一个
        // 01:00的整点数据 , 这里的+1 因为前面已经把这个 endPair变成了 00:23:59 了 所以+1 + 60 到 01:01
        long periodEndTime = periodStartEndPair.getSecond() + 1 + 60;
        //        long periodEndTime = periodStartEndPair.getSecond() + 60;
        // 电价时段（谷1 平2 峰3 尖4 深谷5）
        // 将query进行兑换
        String tempString = queryString;
        tempString =
                tempString
                        .replace("{start}", String.valueOf(periodStartTime))
                        .replace("{end}", String.valueOf(periodEndTime));
        return tempString;
    }

    /**
     * 通过 正则 去解析 一下 要查询的 字段
     *
     * @param queryString : 查询sql
     * @return : 要查询的字段列表
     */
    @NotNull
    private static List<String> getFieldsWithPattern(String queryString) {
        // Define the regex pattern to find fill(column: "column_name")
        // Pattern pattern = Pattern.compile("fill\\(column: \"(.*?)\"");
        Pattern pattern = Pattern.compile("r\\[\"_field\"] == \"(.*?)\"");

        // Create a matcher for the pattern
        Matcher matcher = pattern.matcher(queryString);
        // Create a list to store the column names
        List<String> columns = new ArrayList<>();
        // Find all matches and add the column names to the list
        while (matcher.find()) {
            columns.add(matcher.group(1));
        }
        return columns;
    }

    private String getBaseSql(ProjectEntity projectEntity) {
        String projectId = projectEntity.getId();
        return "import \"date\"\n"
                + "import \"timezone\"\n"
                + "option location = timezone.fixed(offset: "
                + MyTimeUtil.getOffsetSecondsFromZoneCode(projectEntity.getTimezone())
                + "s)\n"
                + "from(bucket: \""
                + influxClient.getBucketForever()
                + "\")\n"
                + "  |> range(start: {start}, stop: {end})\n"
                + "  |> filter(fn: (r) => r[\"_measurement\"] == \"{Table}\")\n"
                + "|> filter(fn: (r) => r.projectId == \""
                + projectId
                + "\")\n";
    }

    @Override
    public String getElectricEmsSql(ProjectEntity projectEntity) {
        String queryString = getBaseSql(projectEntity);
        queryString = queryString + "  |> filter(fn: (r) =>  " + IdSearchUtils.PLACEHOLDER + " )\n";
        queryString =
                queryString
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"{OutColumn}\" or r[\"_field\"]"
                        + " == \"{InColumn}\" )\n";
        queryString = queryString + "  |> difference()\n" + "  |> sum()\n";
        queryString =
                queryString.replace("{Table}", influxClient.getEmsTable(projectEntity.getId()));
        queryString = queryString.replace("{OutColumn}", "ems_history_output_energy");
        queryString = queryString.replace("{InColumn}", "ems_history_input_energy");
        return queryString;
    }

    @Override
    public String getElectricMeterSql(ProjectEntity projectEntity) {
        String queryString = getBaseSql(projectEntity);
        queryString = queryString + "  |> filter(fn: (r) =>  " + IdSearchUtils.PLACEHOLDER + " )\n";
        queryString =
                queryString
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"{OutColumn}\" or r[\"_field\"]"
                        + " == \"{InColumn}\" )\n";
        queryString = queryString + "  |> difference()\n" + "  |> sum()\n";
        // queryString = queryString.replace("{Table}", "T3_5s");
        queryString =
                queryString.replace("{Table}", influxClient.getMeterTable(projectEntity.getId()));
        queryString = queryString.replace("{OutColumn}", "ac_history_positive_power_in_kwh");
        queryString = queryString.replace("{InColumn}", "ac_history_negative_power_in_kwh");
        return queryString;
    }
}
