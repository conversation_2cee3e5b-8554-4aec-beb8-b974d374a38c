package com.wifochina.modules.caculate.pvwind.impl;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.FilterFlux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.onoff.OnOffComponent;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.income.caculate.pvwind.IPvWindIncomeCalculateService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Created on 2024/4/12 13:47.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@Deprecated
public class PvWindIncomeCalculateServiceImpl implements IPvWindIncomeCalculateService {

    private final ElectricPriceService electricPriceService;
    private final OnOffComponent onOffComponent;
    private final InfluxClientService influxClient;

    @Override
    public RenewableProfitEntity calculatePvWindProfit(
            LocalDate time, String pvOrWindType, ZoneId zoneId, String projectId) {
        ZonedDateTime zonedDateTime = time.atStartOfDay(zoneId);
        long oneDayZero = zonedDateTime.toEpochSecond();
        ElectricPriceEntity electricPriceEntity =
                electricPriceService.getElectricPriceMatch(projectId, oneDayZero);

        if (electricPriceEntity == null) {
            log.error("projectId {} cant find electricPrice not calculate pvWindProfit", projectId);
            return null;
        }
        boolean pv = MeterTypeEnum.PV.name().equals(pvOrWindType);
        // 风电的时候 好像默认就是 开启了pv 不懂
        boolean pvFlag;
        // 意思就是 如果是计算pv 那么 pvFlag 需要进行 判断开关是否开了 , 如果是计算wind则 直接认为pvFlag = true 参考以前的代码这样写的
        if (pv) {
            // pv的时候 要看是否开启pv了
            pvFlag =
                    onOffComponent.renewableProfitOnOff(
                            projectId, CalculateTypeEnum.valueOf(pvOrWindType));
        } else {
            pvFlag = true;
        }
        List<String> deviceIds = new ArrayList<>();
        boolean dcdcAndProfitOnOff;
        if (pv) {
            dcdcAndProfitOnOff = onOffComponent.dcdcAndProfitOnOff(projectId, deviceIds::addAll);
        } else {
            // 风电 不去管 dcdc
            dcdcAndProfitOnOff = false;
        }
        RenewableProfitEntity renewableProfitEntity = getPvWindEntity(time, oneDayZero, projectId);
        Optional.ofNullable(electricPriceEntity.getPeriodPriceList())
                .ifPresent(
                        periodList -> {
                            // 记录一下 是pv 的收益还是 风电的收益
                            renewableProfitEntity.setRenewableType(pvOrWindType);
                            for (ElectricPriceEntity entity : periodList) {
                                double pvOrWindPower = 0.0;
                                double onLinePower = 0.0;
                                double dcdcPower = 0.0;
                                // 开启了pv收益的采取计算
                                if (pvFlag) {
                                    String sql = getFluxSql(time, projectId, entity, zoneId);
                                    String pvOrWindFluxSql = getPvOrWindFluxSql(pv, sql);
                                    pvOrWindPower =
                                            calculateTimeProfit(pvOrWindType, pvOrWindFluxSql);

                                    String pvOrWindOnlineFluxSql = getPvOrWindOnLineFluxSql(sql);
                                    // 计算 pv 的 上网的电量
                                    onLinePower =
                                            calculateTimeProfit(
                                                    MeterTypeEnum.GRID.meterType().toString(),
                                                    pvOrWindOnlineFluxSql);
                                }
                                // 开启了dcdc 和 dcdc收益的 才去计算
                                if (dcdcAndProfitOnOff) {
                                    if (!deviceIds.isEmpty()) {
                                        String dcdcFluxSql =
                                                getDcdcFluxSql(
                                                        time, projectId, entity, zoneId, deviceIds);
                                        dcdcPower =
                                                calculateTimeProfit(
                                                        CalculateTypeEnum.DCDC_POWER.name(),
                                                        dcdcFluxSql);
                                    }
                                }
                                // core 真实计算收益
                                calculateAndSetTotal(
                                        electricPriceEntity,
                                        entity,
                                        renewableProfitEntity,
                                        entity.getPeriod(),
                                        pvOrWindType,
                                        pvOrWindPower,
                                        onLinePower,
                                        dcdcPower);
                            }
                            calculatePvWindTotal(renewableProfitEntity);
                        });

        return renewableProfitEntity;
    }

    private static String getPvOrWindFluxSql(boolean pv, String sql) {
        String pvOrWindFluxSql;
        if (pv) {
            // pv
            pvOrWindFluxSql = sql.replace("{type}", MeterTypeEnum.PV.meterType().toString());
        } else {
            // 风电
            pvOrWindFluxSql = sql.replace("{type}", MeterTypeEnum.WIND.meterType().toString());
        }
        return pvOrWindFluxSql;
    }

    private static String getPvOrWindOnLineFluxSql(String sql) {
        String pvOrWindOnlineFluxSql;
        pvOrWindOnlineFluxSql = sql.replace("{type}", MeterTypeEnum.GRID.meterType().toString());
        return pvOrWindOnlineFluxSql;
    }

    public String getFluxSql(
            LocalDate time, String projectId, ElectricPriceEntity price, ZoneId zoneId) {
        LocalTime startTime = price.getStartTime();
        LocalTime endTime = price.getEndTime();
        if (endTime.getSecond() == 0 && endTime.getMinute() == 0 && endTime.getHour() == 0) {
            endTime = LocalTime.of(23, 59, 59);
        }
        // 获取到
        // 将起始时间和结束时间转换为 ZonedDateTime 对象，并指定时区
        ZonedDateTime startDateTime = ZonedDateTime.of(time, startTime, zoneId);
        ZonedDateTime endDateTime = ZonedDateTime.of(time, endTime, zoneId);

        // 将起始时间和结束时间转换为 ZonedDateTime 对象，并指定时区
        // 获取起始时间和结束时间的时间戳（单位：秒）
        long startTimestamp = startDateTime.toInstant().getEpochSecond();
        long endTimestamp = endDateTime.toInstant().getEpochSecond();
        endTimestamp += 1;

        FilterFlux filter =
                Flux.from("forever")
                        .range(startTimestamp, endTimestamp)
                        .filter(Restrictions.measurement().equal("T3_5s"))
                        .filter(
                                Restrictions.and(
                                        Restrictions.field()
                                                .equal("ac_history_positive_power_in_kwh"),
                                        Restrictions.tag("projectId").equal(projectId),
                                        Restrictions.tag("type").equal("{type}")));
        Flux firstMap =
                filter.first()
                        .map("({_time: r._time, firstValue: r._value, ammeterId: r.ammeterId})");
        Flux lastMap =
                filter.last()
                        .map("({_time: r._time, lastValue: r._value, ammeterId: r.ammeterId})");
        return Flux.join()
                .withTable("firstTable", firstMap)
                .withTable("lastTable", lastMap)
                .withOn("ammeterId")
                .toString();
    }

    private String getDcdcFluxSql(
            LocalDate time,
            String projectId,
            ElectricPriceEntity price,
            ZoneId zoneId,
            List<String> deviceIds) {
        LocalTime startTime = price.getStartTime();
        LocalTime endTime = price.getEndTime();
        if (endTime.getSecond() == 0 && endTime.getMinute() == 0 && endTime.getHour() == 0) {
            endTime = LocalTime.of(23, 59, 59);
        }

        ZonedDateTime startDateTime = ZonedDateTime.of(time, startTime, zoneId);
        ZonedDateTime endDateTime = ZonedDateTime.of(time, endTime, zoneId);
        // 获取起始时间和结束时间的时间戳（单位：秒）
        long startTimestamp = startDateTime.toInstant().getEpochSecond();
        long endTimestamp = endDateTime.toInstant().getEpochSecond();
        endTimestamp += 1;

        Restrictions fieldRestriction = null;
        if (deviceIds != null && !deviceIds.isEmpty()) {
            List<Restrictions> deviceRestrictions = new ArrayList<>();
            for (String deviceId : deviceIds) {
                Restrictions deviceRestriction = Restrictions.tag("deviceId").equal(deviceId);
                deviceRestrictions.add(deviceRestriction);
            }
            fieldRestriction = Restrictions.or(deviceRestrictions.toArray(new Restrictions[0]));
        }
        List<Restrictions> restrictionsList = new ArrayList<>();
        restrictionsList.add(fieldRestriction);

        return Flux.from("forever")
                .range(startTimestamp, endTimestamp)
                .filter(
                        Restrictions.and(
                                Restrictions.measurement().equal("T1_5s"),
                                Restrictions.tag("projectId").equal(projectId),
                                Restrictions.field().equal("dcdc_meter_history_energy_pos")))
                .filter(Restrictions.and(restrictionsList.toArray(new Restrictions[0])))
                .difference()
                .group()
                .sum()
                .toString();
    }

    public void calculateAndSetTotal(
            ElectricPriceEntity superPrice,
            ElectricPriceEntity price,
            RenewableProfitEntity renewableProfitEntity,
            Integer type,
            String pvOrWindType,
            double pvPower,
            double onLinePower,
            double dcdcPower) {
        pvPower += dcdcPower;
        // 自用电 =  pv - online
        double selfPower = pvPower - onLinePower;
        selfPower = selfPower < 0 ? 0 : selfPower;

        double fullInternetBenefit = 0.0;
        double benefit = 0.0;
        double onLineBenefit = 0.0;
        initPrice(price);
        if (pvOrWindType.equals(MeterTypeEnum.PV.name())) {
            fullInternetBenefit = superPrice.getPvPrice() * pvPower;
            //  pv 收益公式=(国家补助+各个时段电价)*各个时段的自用电量+（国家补助+脱硫标杆电价）*上网电量
            if (price.getSellPrice() == null) {
                price.setSellPrice(0.0);
            }
            benefit = (selfPower * (price.getSellPrice() + superPrice.getPvSubsidyPrice()));

            onLineBenefit =
                    ((superPrice.getPvSubsidyPrice() + superPrice.getPvDfPrice()) * onLinePower);
        } else if (pvOrWindType.equals(MeterTypeEnum.WIND.name())) {
            fullInternetBenefit = superPrice.getWindPrice() * pvPower;
            if (price.getSellPrice() == null) {
                price.setSellPrice(0.0);
            }
            benefit = (selfPower * (price.getSellPrice() + superPrice.getWindSubsidyPrice()));

            onLineBenefit =
                    ((superPrice.getWindSubsidyPrice() + superPrice.getWindDfPrice())
                            * onLinePower);
        }
        renewableProfitEntity.setTotalDischargeQuantity(
                renewableProfitEntity.getTotalDischargeQuantity() + pvPower);
        // 馈网的收益
        renewableProfitEntity.setTotalOnlineBenefit(
                renewableProfitEntity.getTotalOnlineBenefit() + onLineBenefit);
        // 全额上网的 收益
        renewableProfitEntity.setTotalFullInternetAccessBenefit(
                renewableProfitEntity.getTotalFullInternetAccessBenefit() + fullInternetBenefit);
        // 总馈网的电量
        renewableProfitEntity.setTotalOnlineQuantity(
                renewableProfitEntity.getTotalOnlineQuantity() + onLinePower);
        if (benefit < 0) {
            benefit = 0;
        }
        switch (type) {
                // 电价时段（谷1 平2 峰3 尖4）
            case 1:
                // (pv - online ) * (price + 国家补助)
                renewableProfitEntity.setVallySellPrice(price.getSellPrice());
                renewableProfitEntity.setVallyDischargeBenefit(
                        renewableProfitEntity.getVallyDischargeBenefit() + benefit);

                // 谷放电收益
                renewableProfitEntity.setVallyDischargeQuantity(
                        renewableProfitEntity.getVallyDischargeQuantity() + selfPower);
                // 上网电量
                renewableProfitEntity.setVallyDischargeQuantityOnline(
                        renewableProfitEntity.getVallyDischargeQuantityOnline() + onLinePower);
                // dcdc
                renewableProfitEntity.setVallyDischargeQuantityDcdc(
                        renewableProfitEntity.getVallyDischargeQuantityDcdc() + dcdcPower);
                break;
            case 2:
                renewableProfitEntity.setFlatSellPrice(price.getSellPrice());
                renewableProfitEntity.setFlatDischargeBenefit(
                        renewableProfitEntity.getFlatDischargeBenefit() + benefit);

                // 平端电收益
                renewableProfitEntity.setFlatDischargeQuantity(
                        renewableProfitEntity.getFlatDischargeQuantity() + selfPower);
                // 上网电量
                renewableProfitEntity.setFlatDischargeQuantityOnline(
                        renewableProfitEntity.getFlatDischargeQuantityOnline() + onLinePower);
                // dcdc
                renewableProfitEntity.setFlatDischargeQuantityDcdc(
                        renewableProfitEntity.getFlatDischargeQuantityDcdc() + dcdcPower);

                break;
            case 3:
                renewableProfitEntity.setPeakSellPrice(price.getSellPrice());
                renewableProfitEntity.setPeakDischargeBenefit(
                        renewableProfitEntity.getPeakDischargeBenefit() + benefit);

                // 峰放电收益
                renewableProfitEntity.setPeakDischargeQuantity(
                        renewableProfitEntity.getPeakDischargeQuantity() + selfPower);
                // 上网电量
                renewableProfitEntity.setPeakDischargeQuantityOnline(
                        renewableProfitEntity.getPeakDischargeQuantityOnline() + onLinePower);
                // dcdc
                renewableProfitEntity.setPeakDischargeQuantityDcdc(
                        renewableProfitEntity.getPeakDischargeQuantityDcdc() + dcdcPower);
                break;
            case 4:
                renewableProfitEntity.setTipSellPrice(price.getSellPrice());
                renewableProfitEntity.setTipDischargeBenefit(
                        renewableProfitEntity.getTipDischargeBenefit() + benefit);

                // 尖峰放电收益
                renewableProfitEntity.setTipDischargeQuantity(
                        renewableProfitEntity.getTipDischargeQuantity() + selfPower);
                renewableProfitEntity.setTipDischargeQuantityOnline(
                        renewableProfitEntity.getTipDischargeQuantityOnline() + onLinePower);
                // dcdc
                renewableProfitEntity.setTipDischargeQuantityDcdc(
                        renewableProfitEntity.getTipDischargeQuantityDcdc() + dcdcPower);
                break;
            case 5:
                renewableProfitEntity.setDeepVallySellPrice(price.getSellPrice());
                renewableProfitEntity.setDeepVallyDischargeBenefit(
                        renewableProfitEntity.getDeepVallyDischargeBenefit() + benefit);

                // 深谷放电收益
                renewableProfitEntity.setDeepVallyDischargeQuantity(
                        renewableProfitEntity.getDeepVallyDischargeQuantity() + selfPower);
                renewableProfitEntity.setDeepVallyDischargeQuantityOnline(
                        renewableProfitEntity.getDeepVallyDischargeQuantityOnline() + onLinePower);
                // dcdc
                renewableProfitEntity.setDeepVallyDischargeQuantityDcdc(
                        renewableProfitEntity.getDeepVallyDischargeQuantityDcdc() + dcdcPower);
                break;
            default:
        }
    }

    private RenewableProfitEntity getPvWindEntity(
            LocalDate time, long oneDayZero, String projectId) {
        RenewableProfitEntity renewableProfitEntity = new RenewableProfitEntity();
        renewableProfitEntity.setTime(oneDayZero);
        renewableProfitEntity.setProjectId(projectId);
        renewableProfitEntity.setYear(time.getYear());
        renewableProfitEntity.setMonth(time.getMonthValue());
        renewableProfitEntity.setDay(time.getDayOfMonth());
        return renewableProfitEntity;
    }

    private static void initPrice(ElectricPriceEntity price) {
        if (price.getSellPrice() == null) {
            price.setSellPrice(0.0);
        }
        if (price.getPvPrice() == null) {
            price.setPvPrice(0.0);
        }
        if (price.getPvDfPrice() == null) {
            price.setPvDfPrice(0.0);
        }
        if (price.getPvSelfPrice() == null) {
            price.setPvSelfPrice(0.0);
        }
        if (price.getPvSubsidyPrice() == null) {
            price.setPvSubsidyPrice(0.0);
        }
        if (price.getWindPrice() == null) {
            price.setWindPrice(0.0);
        }
        if (price.getWindDfPrice() == null) {
            price.setWindDfPrice(0.0);
        }

        if (price.getWindSelfPrice() == null) {
            price.setWindSelfPrice(0.0);
        }

        if (price.getWindSubsidyPrice() == null) {
            price.setWindSubsidyPrice(0.0);
        }
    }

    public double calculateTimeProfit(String calculateType, String queryString) {

        // 将query进行兑换
        log.debug("YestPvWindProfitJob#calculateTimeProfit---> {} ", queryString + " ;");
        Double maxValue;
        Double minValue;
        Double dcdcValue = 0.0;
        double totalPower = 0.0;
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                if (calculateType.equals(CalculateTypeEnum.DCDC_POWER.name())) {
                    // dcdc
                    dcdcValue = (Double) record.getValueByKey("_value");
                    if (dcdcValue == null) {
                        dcdcValue = 0.0;
                    }
                } else {
                    minValue = (Double) record.getValueByKey("firstValue");
                    maxValue = (Double) record.getValueByKey("lastValue");
                    if (minValue == null) {
                        minValue = 0.0;
                    }
                    if (maxValue == null) {
                        maxValue = 0.0;
                    }
                    totalPower += maxValue - minValue;
                }
            }
        }
        if (!calculateType.equals(CalculateTypeEnum.DCDC_POWER.name())) {
            // 如果不是dcdc 是 pv 或者gird 则是 最大值-最小值
            return totalPower;
        } else {
            return dcdcValue;
        }
    }

    public void calculatePvWindTotal(RenewableProfitEntity renewableProfitEntity) {
        // 自用电的电量 总的电量
        double totalDischargeSelfQuantity =
                renewableProfitEntity.getFlatDischargeQuantity()
                        + renewableProfitEntity.getVallyDischargeQuantity()
                        + renewableProfitEntity.getPeakDischargeQuantity()
                        + renewableProfitEntity.getTipDischargeQuantity()
                        + renewableProfitEntity.getDeepVallyDischargeQuantity();
        renewableProfitEntity.setTotalDischargeSelfQuantity(totalDischargeSelfQuantity);

        // 自用电的收益 总的收益
        double totalDischargeSelfBenefit =
                renewableProfitEntity.getFlatDischargeBenefit()
                        + renewableProfitEntity.getVallyDischargeBenefit()
                        + renewableProfitEntity.getPeakDischargeBenefit()
                        + renewableProfitEntity.getTipDischargeBenefit()
                        + renewableProfitEntity.getDeepVallyDischargeBenefit();
        renewableProfitEntity.setTotalDischargeSelfBenefit(totalDischargeSelfBenefit);
    }
}
