package com.wifochina.modules.query;

import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.*;
import com.wifochina.modules.electric.ElectricAdapterChooser;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.ElectricDynamicPeriodService;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.EquipmentSearchService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.ElectricProfitService;
import com.wifochina.modules.income.caculate.electric.IElectricIncomeCalculateService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.operation.util.OperationUtil;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * Created on 2024/9/9 09:48.
 *
 * <AUTHOR> <<<<<<< Updated upstream
 */
@Service
@Slf4j
@AllArgsConstructor
public class ElectricProfitServiceImpl implements ElectricProfitService {
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final ProjectService projectService;
    private final EquipmentSearchService equipmentSearchService;
    private final GroupService groupService;
    private final ElectricAdapterChooser electricAdapterChooser;
    private final IElectricIncomeCalculateService electricIncomeCalculateService;
    private final DeviceService deviceService;
    private final AmmeterService ammeterService;
    private final ElectricPriceService electricPriceService;
    private final CacheUtils cacheUtils;
    private final ElectricDynamicPeriodService electricDynamicPeriodService;

    @Override
    public ElectricProfitVO getTodayRealTimeElectricProfitVo(
            ProfitRequest profitRequest, boolean groupController) {
        String projectId = profitRequest.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        long endDate = Instant.now().getEpochSecond();
        long startDate = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        ServiceAssert.isTrue(endDate >= startDate, ErrorResultCode.START_GE_END_TIME.value());
        List<DeviceEntity> deviceEntities;
        List<AmmeterEntity> ammeterEntities;
        if (groupController) {
            deviceEntities =
                    deviceService.getDeviceByGid(
                            profitRequest.getProjectId(), profitRequest.getGroupId());
            ammeterEntities =
                    ammeterService.getAmmeterByGid(
                            profitRequest.getProjectId(), profitRequest.getGroupId());
        } else {
            deviceEntities = deviceService.findIncomeDevices(projectId);
            ammeterEntities = ammeterService.findIncomeAmmeter(projectId);
        }
        String queryEmsString = null;
        String queryMeterString = null;
        if (!deviceEntities.isEmpty()) {
            queryEmsString = electricIncomeCalculateService.getElectricEmsSql(projectEntity);
            queryEmsString =
                    queryEmsString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createIdSearchSqlForTimeSeries(deviceEntities));
        }
        if (!ammeterEntities.isEmpty()) {
            queryMeterString = electricIncomeCalculateService.getElectricMeterSql(projectEntity);
            queryMeterString =
                    queryMeterString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createIdSearchSqlForTimeSeries(ammeterEntities));
        }
        return getTodayElectricCore(projectId, startDate, queryEmsString, queryMeterString);
    }

    @Override
    public Map<String, Object> getElectricProfitCollect(ProfitRequest profitRequest) {
        String projectId = profitRequest.getProjectId();
        Map<String, Object> benefitMap = new HashMap<>(4);
        CountDownLatch countDownLatch = new CountDownLatch(4);
        threadPoolTaskExecutor.submit(
                () -> {
                    benefitMap.put("total", getElectricTotalProfit(profitRequest));
                    countDownLatch.countDown();
                });
        threadPoolTaskExecutor.submit(
                () -> {
                    benefitMap.put("cost", getElectricPeriodProfit(profitRequest));
                    countDownLatch.countDown();
                });
        // 1.3.7 国内才有的 开启了分时缓存的 把分时缓存的 数据提供给前端
        threadPoolTaskExecutor.submit(
                () -> {
                    benefitMap.put("timesharing", getTimeSharingProfit(profitRequest, projectId));
                    countDownLatch.countDown();
                });

        // 1.3.7 当 实时电价or 固定电价 的时候 需要把每个段的 详细数据返回 供前端导出
        threadPoolTaskExecutor.submit(
                () -> {
                    benefitMap.put(
                            "periods", getDynamicPeriodDetailProfit(profitRequest, projectId));
                    countDownLatch.countDown();
                });
        try {
            countDownLatch.await(2, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return benefitMap;
    }

    @Override
    public ElectricProfitVO getTodayElectricProfitVo(
            ProfitRequest profitRequest, boolean groupController) {
        String projectId = profitRequest.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        long endDate = Instant.now().getEpochSecond();
        long startDate = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        ServiceAssert.isTrue(endDate >= startDate, ErrorResultCode.START_GE_END_TIME.value());
        List<DeviceEntity> deviceEntities;
        List<AmmeterEntity> ammeterEntities;
        if (groupController) {
            deviceEntities =
                    deviceService.getDeviceByGid(
                            profitRequest.getProjectId(), profitRequest.getGroupId());
            ammeterEntities =
                    ammeterService.getAmmeterByGid(
                            profitRequest.getProjectId(), profitRequest.getGroupId());
        } else {
            deviceEntities = deviceService.findIncomeDevices(projectId);
            ammeterEntities = ammeterService.findIncomeAmmeter(projectId);
        }
        String queryEmsString = null;
        String queryMeterString = null;
        if (!deviceEntities.isEmpty()) {
            queryEmsString = electricIncomeCalculateService.getElectricEmsSql(projectEntity);
            queryEmsString =
                    queryEmsString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createIdSearchSqlForTimeSeries(deviceEntities));
        }
        if (!ammeterEntities.isEmpty()) {
            queryMeterString = electricIncomeCalculateService.getElectricMeterSql(projectEntity);
            queryMeterString =
                    queryMeterString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createIdSearchSqlForTimeSeries(ammeterEntities));
        }
        return getTodayElectricCore(projectId, startDate, queryEmsString, queryMeterString);
    }

    private ElectricProfitVO getTodayElectricCore(
            String projectId, long startDate, String queryEmsSql, String queryMeterSql) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 获取系统当前的配置价格
        ElectricPriceEntity matchPriceEntity =
                electricPriceService.getElectricPriceMatch(projectId, startDate);
        if (matchPriceEntity == null) {
            log.error(
                    "projectId {} cant find electricPrice not calculate getTodayElectricPrice",
                    projectId);
            return new ElectricProfitVO().init();
            //            return null;
        }
        ElectricProfitVO electricProfitVO = new ElectricProfitVO();
        electricProfitVO.init();
        Lock lock = new ReentrantLock();
        // 获取时间段价格
        List<ElectricPriceEntity> list = matchPriceEntity.getPeriodPriceList();
        TimeContext timeContext = TimeContext.getContext(projectEntity.getTimezone(), startDate);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        for (ElectricPriceEntity price : list) {
            threadPoolTaskExecutor.submit(
                    () -> {
                        try {
                            todayElectricProfit(
                                    queryEmsSql,
                                    price,
                                    timeContext,
                                    projectEntity,
                                    lock,
                                    electricProfitVO);
                            todayElectricProfit(
                                    queryMeterSql,
                                    price,
                                    timeContext,
                                    projectEntity,
                                    lock,
                                    electricProfitVO);
                        } finally {
                            countDownLatch.countDown();
                        }
                    });
        }
        boolean jobResult;
        try {
            jobResult = countDownLatch.await(3, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error(
                    "getTodayBatteryProfit---> {}",
                    projectEntity.getProjectName()
                            + "- calculate electric earning await produce exception");
            return electricProfitVO;
        }
        if (!jobResult) {
            log.error(
                    "getTodayBatteryProfit---> {}",
                    projectEntity.getProjectName() + "- calculate electric earning await failed");
            return electricProfitVO;
        }
        // 如果是 尖峰平谷的 需要去 合计一下
        if (projectEntity
                .getElectricPriceType()
                .equals(ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD.name())) {
            setElectricProfitVoTotal(electricProfitVO);
        } else if (projectEntity
                        .getElectricPriceType()
                        .equals(ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.name())
                || projectEntity
                        .getElectricPriceType()
                        .equals(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name())) {
            setElectricProfitVoTotalForDynamic(electricProfitVO);
        }
        return electricProfitVO;
    }

    private void todayElectricProfit(
            String queryEmsString,
            ElectricPriceEntity price,
            TimeContext timeContext,
            ProjectEntity projectEntity,
            Lock lock,
            ElectricProfitVO electricProfitVO) {
        // 后期要把这个timeContext 优化一下 目前influxdb 里面是按照 时段的时间去查询的 不需要那么多东西了 ,目前看了下 只用到了 oneDayZero
        // 但是lindorm那边还是按照日期 然后去减去或者加上时段的时间 去算的 懒得改了 后面和lindorm 说拜拜了
        electricIncomeCalculateService.calculateTimeChargeDiff(
                timeContext,
                price,
                projectEntity,
                queryEmsString,
                (outDiff, inDiff) -> {
                    // 如果是的动态时段 对total相关的属性 多个时段线程 会有安全问题 这里锁一下, 如果是尖峰平谷没问题
                    // 因为是各个不同的字段
                    lock.lock();
                    try {
                        electricAdapterChooser
                                .choose(projectEntity.getElectricPriceType())
                                .calculatePeriodWithElectricProfitVo(
                                        price, electricProfitVO, outDiff, inDiff);
                    } finally {
                        lock.unlock();
                    }
                });
    }

    // TODO refactor
    protected static void setElectricProfitVoTotal(ElectricProfitVO electricProfitVO) {
        // 总充电成本
        double totalChargeCost =
                electricProfitVO.getFlatChargeCost()
                        + electricProfitVO.getVallyChargeCost()
                        + electricProfitVO.getPeakChargeCost()
                        + electricProfitVO.getTipChargeCost()
                        + electricProfitVO.getDeepVallyChargeCost();
        electricProfitVO.setTotalChargeCost(totalChargeCost);
        double totalChargeQuantity =
                electricProfitVO.getFlatChargeQuantity()
                        + electricProfitVO.getVallyChargeQuantity()
                        + electricProfitVO.getPeakChargeQuantity()
                        + electricProfitVO.getTipChargeQuantity()
                        + electricProfitVO.getDeepVallyChargeQuantity();
        electricProfitVO.setTotalChargeQuantity(totalChargeQuantity);
        // 总放电收益
        double totalDischargeQuantity =
                electricProfitVO.getFlatDischargeQuantity()
                        + electricProfitVO.getVallyDischargeQuantity()
                        + electricProfitVO.getPeakDischargeQuantity()
                        + electricProfitVO.getTipDischargeQuantity()
                        + electricProfitVO.getDeepVallyDischargeQuantity();
        electricProfitVO.setTotalDischargeQuantity(totalDischargeQuantity);
        double totalDischargeBenefit =
                electricProfitVO.getFlatDischargeBenefit()
                        + electricProfitVO.getVallyDischargeBenefit()
                        + electricProfitVO.getPeakDischargeBenefit()
                        + electricProfitVO.getTipDischargeBenefit()
                        + electricProfitVO.getDeepVallyDischargeBenefit();
        electricProfitVO.setTotalDischargeBenefit(totalDischargeBenefit);
        // 总收益
        electricProfitVO.setTotalBenefit(totalDischargeBenefit - totalChargeCost);
    }

    // TODO refactor
    private void setElectricProfitVoTotalForDynamic(ElectricProfitVO electricProfitVO) {
        // 总收益
        electricProfitVO.setTotalBenefit(
                electricProfitVO.getTotalDischargeBenefit()
                        - electricProfitVO.getTotalChargeCost());
    }

    // TODO refactor
    /** 查询 分时缓存的 */
    private List<ElectricDynamicPeriodEntity> getTimeSharingProfit(
            ProfitRequest profitRequest, String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);

        List<ElectricDynamicPeriodEntity> results = new ArrayList<>();
        // 分时缓存 . 开启了分时换成的开关 并且 是中国的 , 并且 当前查询的时间段 是 单日的
        if (Boolean.TRUE.equals(projectEntity.getTimeSharingCache())
                && cacheUtils.isChina(projectEntity.getCountry())) {
            // 判断是 查询的单日的
            if (profitRequest.getStartDate() + EmsConstants.ONE_DAY_SECOND
                    == profitRequest.getEndDate() + 1) {
                // 判断是 查询的单日的
                results = getDynamicDetailEntityList(profitRequest, projectId, projectEntity);
            }
        }
        return results;
    }

    // TODO refactor
    /** 查询 数据库的 动态分段的 entity 列表 此方法支持 分时缓存条件下来查询 也支持 固定电价or实时电价下前端导出需要的数据 */
    private List<ElectricDynamicPeriodEntity> getDynamicDetailEntityList(
            ProfitRequest profitRequest, String projectId, ProjectEntity projectEntity) {
        List<ElectricDynamicPeriodEntity> timeSharingProfits;
        EquipmentSearchService.SearchContext context =
                new EquipmentSearchService.SearchContext()
                        .setProject(projectEntity)
                        .setSearchGroupId(profitRequest.getGroupId());
        boolean groupController = equipmentSearchService.groupController(context);
        List<String> deviceIds =
                equipmentSearchService.equipmentIdsSupportGroup(context, groupController);
        ElectricRequest timeSharingRequest =
                new ElectricRequest()
                        .setProjectId(projectId)
                        // 这个 deviceIds 是 设备和电表的合计 也是上面支持了 分组的
                        .setDeviceIdList(deviceIds)
                        .setStart(profitRequest.getStartDate())
                        // +1 是因为前端传的是 23:59:59  查询里面 是 < endTIme
                        .setEnd(profitRequest.getEndDate() + 1);
        timeSharingProfits =
                ((ElectricDynamicPeriodService)
                                electricAdapterChooser.choose(
                                        ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name()))
                        .queryElectricTimeSharingProfit(timeSharingRequest);
        return timeSharingProfits;
    }

    /**
     * // 1.3.7 当 实时电价or 固定电价 的时候 需要把每个段的 详细数据返回 供前端导出
     *
     * @param profitRequest : request
     * @param projectId: projectId
     * @return : list
     */
    private List<ElectricDynamicPeriodEntity> getDynamicPeriodDetailProfit(
            ProfitRequest profitRequest, String projectId) {
        List<String> types =
                List.of(
                        ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.name(),
                        ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name());
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<ElectricDynamicPeriodEntity> results = new ArrayList<>();
        // 固定电价 or 实时电价 把详情返回
        if (projectEntity.getElectricPriceType() != null
                && types.contains(projectEntity.getElectricPriceType())) {
            // 真正查询 list 列表
            results = getDynamicDetailEntityList(profitRequest, projectId, projectEntity);
            long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            if (profitRequest.getEndDate() > todayZero) {
                // 查询今天的 部分
                // 处理一下 今天的 时段, 这个时段 只是获取到 列表 不进行数据库的缓存
                fillTodayDynamicPeriodDetails(projectEntity, results);
            }
        }
        return results;
    }

    private void fillTodayDynamicPeriodDetails(
            ProjectEntity projectEntity, List<ElectricDynamicPeriodEntity> results) {
        String projectId = projectEntity.getId();
        long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        // 找到 那个匹配到的 电价配置策略
        ElectricPriceEntity electricPriceEntity =
                electricPriceService.getElectricPriceMatch(projectId, todayZero);
        // 查询今天的则要 处理一下
        List<DeviceEntity> incomeDevices = deviceService.findIncomeDevices(projectId);
        List<AmmeterEntity> incomeAmmeters = ammeterService.findIncomeAmmeter(projectId);
        TimeContext timeContext = TimeContext.getContext(projectEntity.getTimezone(), todayZero);
        if (electricPriceEntity != null) {
            ElectricAdapterService.ElectricAdapterContext context =
                    new ElectricAdapterService.ElectricAdapterContext() {
                        @Override
                        public List<ElectricPriceEntity> periodPriceList() {
                            return electricPriceEntity.getPeriodPriceList();
                        }

                        @Override
                        public long timePoint() {
                            return todayZero;
                        }

                        @Override
                        public ProjectEntity project() {
                            return projectEntity;
                        }
                    };
            Map<Long, ElectricDynamicPeriodEntity> electricDynamicPeriodEntityMap = new HashMap<>();
            incomeDevices.forEach(
                    device -> {
                        List<ElectricDynamicPeriodEntity> toDayResults = new ArrayList<>();
                        electricDynamicPeriodService.commonPeriodList(
                                context,
                                timeContext,
                                toDayResults,
                                electricIncomeCalculateService.getElectricEmsSql(context.project()),
                                device);
                        toDayResults.forEach(
                                electricDynamicPeriodEntity -> {
                                    ElectricDynamicPeriodEntity exist =
                                            electricDynamicPeriodEntityMap.get(
                                                    electricDynamicPeriodEntity
                                                            .getPeriodStartTime());
                                    if (exist != null) {
                                        // 如果存在了 把数值加进去
                                        setSum(exist, electricDynamicPeriodEntity);
                                    } else {
                                        electricDynamicPeriodEntityMap.put(
                                                electricDynamicPeriodEntity.getPeriodStartTime(),
                                                electricDynamicPeriodEntity);
                                    }
                                });
                    });
            incomeAmmeters.forEach(
                    ammeter -> {
                        List<ElectricDynamicPeriodEntity> toDayResults = new ArrayList<>();
                        electricDynamicPeriodService.commonPeriodList(
                                context,
                                timeContext,
                                toDayResults,
                                electricIncomeCalculateService.getElectricMeterSql(
                                        context.project()),
                                ammeter);
                        toDayResults.forEach(
                                electricDynamicPeriodEntity -> {
                                    ElectricDynamicPeriodEntity exist =
                                            electricDynamicPeriodEntityMap.get(
                                                    electricDynamicPeriodEntity
                                                            .getPeriodStartTime());
                                    if (exist != null) {
                                        // 如果存在了 把数值加进去
                                        setSum(exist, electricDynamicPeriodEntity);
                                    } else {
                                        electricDynamicPeriodEntityMap.put(
                                                electricDynamicPeriodEntity.getPeriodStartTime(),
                                                electricDynamicPeriodEntity);
                                    }
                                });
                    });
            List<ElectricDynamicPeriodEntity> toDayResults =
                    new ArrayList<>(electricDynamicPeriodEntityMap.values());
            results.addAll(toDayResults);
        }
    }

    // TODO refactor
    private void setSum(
            ElectricDynamicPeriodEntity exist,
            ElectricDynamicPeriodEntity electricDynamicPeriodEntity) {
        exist.setChargeQuantity(
                exist.getChargeQuantity() + electricDynamicPeriodEntity.getChargeQuantity());
        exist.setChargeCost(exist.getChargeCost() + electricDynamicPeriodEntity.getChargeCost());
        exist.setDischargeQuantity(
                exist.getDischargeQuantity() + electricDynamicPeriodEntity.getDischargeQuantity());
        exist.setDischargeBenefit(
                exist.getDischargeBenefit() + electricDynamicPeriodEntity.getDischargeBenefit());
        exist.setTotalBenefit(
                exist.getTotalBenefit() + electricDynamicPeriodEntity.getTotalBenefit());
    }

    @Override
    public ElectricProfitVO getElectricTotalProfit(ProfitRequest profitRequest) {
        String projectId = profitRequest.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        // AOP 设置的
        EquipmentSearchService.DeviceIdsAndControl deviceIdsAndControl =
                equipmentSearchService.getDeviceIdsAndControl(
                        new EquipmentSearchService.SearchContext()
                                .setProject(projectEntity)
                                .setSearchGroupId(profitRequest.getGroupId()));
        List<String> deviceIds = deviceIdsAndControl.getDeviceIds();
        boolean groupController = deviceIdsAndControl.isGroupController();
        profitRequest.setDeviceIdsAndControl(deviceIdsAndControl);
        // 增加补值的分组 id
        deviceIds.addAll(
                groupService.queryGroup(projectId).stream()
                        .map(GroupEntity::getId)
                        .collect(Collectors.toList()));
        ElectricProfitVO electricProfitVO = new ElectricProfitVO();
        // 可能有测试项目 下面electricProfitVO的属性还是null 导致 addRemedies 方法里面报错, 所以需要初始化
        electricProfitVO.init();
        if (!deviceIds.isEmpty()) {
            ElectricRequest electricRequest = new ElectricRequest();
            electricRequest.setProjectId(projectId);
            electricRequest.setStart(profitRequest.getStartDate());
            electricRequest.setEnd(profitRequest.getEndDate());
            electricRequest.setDeviceIdList(deviceIds);
            // 查询 ElectricProfitVo 对象 支持海外
            electricProfitVO = searchElectricProfit(projectEntity, electricRequest);
        }
        long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        if (profitRequest.getEndDate() > todayZero) {
            // 新增的支持 分组查询今日的储能收益
            ElectricProfitVO todayElectricProfitVO =
                    this.getTodayElectricProfitVo(profitRequest, groupController);
            // 如果是 尖峰平谷字段
            if (ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD
                    .name()
                    .equals(projectEntity.getElectricPriceType())) {
                // 添加今日的收益
                OperationUtil.addRemedies(electricProfitVO, todayElectricProfitVO);
            } else {
                // 如果是 动态的 则已经都算好了total的 不需尖峰平谷 字段去 累计
                // 1.3.7  但是这里是把今天的加上去  周涛said 目前 动态的和固定电价都不需要 补值 ,
                OperationUtil.addRemediesForDynamic(electricProfitVO, todayElectricProfitVO);
            }
        }
        return electricProfitVO;
    }

    @Override
    public Map<Long, ElectricProfitVO> getElectricPeriodProfit(ProfitRequest profitRequest) {
        String projectId = profitRequest.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        EquipmentSearchService.DeviceIdsAndControl deviceIdsAndControl =
                equipmentSearchService.getDeviceIdsAndControl(
                        new EquipmentSearchService.SearchContext()
                                .setProject(projectEntity)
                                .setSearchGroupId(profitRequest.getGroupId()));
        List<String> deviceIds = deviceIdsAndControl.getDeviceIds();
        boolean groupController = deviceIdsAndControl.isGroupController();
        Map<Long, ElectricProfitVO> map = new HashMap<>();
        // 增加补值的分组 id
        deviceIds.addAll(
                groupService.queryGroup(projectId).stream()
                        .map(GroupEntity::getId)
                        .collect(Collectors.toList()));
        if (!deviceIds.isEmpty()) {
            ElectricRequest electricRequest = new ElectricRequest();
            electricRequest.setProjectId(projectId);
            electricRequest.setStart(profitRequest.getStartDate());
            electricRequest.setEnd(profitRequest.getEndDate());
            electricRequest.setDeviceIdList(deviceIds);
            // 1.3.7 add 超过31天 则按照月份统计
            boolean differenceMoreThan31Days =
                    MyTimeUtil.isDifferenceMoreThan31Days(
                            electricRequest.getStart(), electricRequest.getEnd());
            electricRequest.setTimeRangeWithin31Day(differenceMoreThan31Days);
            electricAdapterChooser
                    .choose(projectEntity.getElectricPriceType())
                    .queryElectricEveryDayProfit(
                            electricRequest,
                            new ElectricAdapterService.QueryElectricsResult() {
                                @Override
                                public void peakValleysPeriodPostProcessor(List<ElectricEntity> t) {
                                    for (ElectricEntity electricEntity : t) {
                                        ElectricProfitVO electricProfitVO =
                                                OperationUtil.getPeakValleysPeriodElectricProfitVo(
                                                        Optional.ofNullable(electricEntity)
                                                                .orElse(new ElectricEntity()));
                                        assert electricEntity != null;
                                        if (!differenceMoreThan31Days) {
                                            map.put(electricEntity.getTime(), electricProfitVO);
                                        } else {
                                            // 如果找过了3天 则这个不会有time,  需要根据year month 构建一个 月份的 key
                                            long monthKey =
                                                    MyTimeUtil.getEveryMonthKey(
                                                            projectEntity.getTimezone(),
                                                            electricEntity.getYear(),
                                                            electricEntity.getMonth());
                                            map.put(monthKey, electricProfitVO);
                                        }
                                    }
                                }

                                @Override
                                public void dynamicPeriodPostProcessor(
                                        List<ElectricDynamicPeriodEntity> t) {

                                    for (ElectricDynamicPeriodEntity electricDynamicPeriodEntity :
                                            t) {
                                        ElectricProfitVO electricProfitVO =
                                                OperationUtil.getDynamicPeriodElectricProfitVo(
                                                        Optional.ofNullable(
                                                                        electricDynamicPeriodEntity)
                                                                .orElse(
                                                                        new ElectricDynamicPeriodEntity()));
                                        assert electricDynamicPeriodEntity != null;
                                        if (!differenceMoreThan31Days) {
                                            // 根据 年月日去 确定 key 时间戳 因为这个查询是把时段按照天都合并了  不会有具体天的时间戳 key
                                            long dayKey =
                                                    MyTimeUtil.getEveryDayKey(
                                                            projectEntity.getTimezone(),
                                                            electricDynamicPeriodEntity.getYear(),
                                                            electricDynamicPeriodEntity.getMonth(),
                                                            electricDynamicPeriodEntity.getDay());
                                            map.put(dayKey, electricProfitVO);
                                        } else {
                                            // 根据 月份进行分组
                                            long monthKey =
                                                    MyTimeUtil.getEveryMonthKey(
                                                            projectEntity.getTimezone(),
                                                            electricDynamicPeriodEntity.getYear(),
                                                            electricDynamicPeriodEntity.getMonth());
                                            map.put(monthKey, electricProfitVO);
                                        }
                                    }
                                }
                            });
            long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            if (profitRequest.getEndDate() > todayZero) {
                ElectricProfitVO todayElectricProfitVo =
                        getTodayElectricProfitVo(profitRequest, groupController);
                map.put(todayZero, todayElectricProfitVo);
            }
        }
        return map;
    }

    /** 查询 ElectricProfitVO */
    private ElectricProfitVO searchElectricProfit(
            ProjectEntity projectEntity, ElectricRequest electricRequest) {
        return electricAdapterChooser
                .choose(projectEntity.getElectricPriceType())
                .queryElectricTotalProfit(
                        electricRequest,
                        new ElectricAdapterService.QueryElectricResult() {
                            @Override
                            public ElectricProfitVO peakValleysPeriodPostProcessor(
                                    ElectricEntity t) {
                                return OperationUtil.getPeakValleysPeriodElectricProfitVo(
                                        Optional.ofNullable(t).orElse(new ElectricEntity()));
                            }

                            @Override
                            public ElectricProfitVO dynamicPeriodPostProcessor(
                                    ElectricDynamicPeriodEntity t) {
                                // 目前海外的没有 补值操作, 并且 只是只有 总冲总放等..
                                return OperationUtil.getDynamicPeriodElectricProfitVo(
                                        Optional.ofNullable(t)
                                                .orElse(new ElectricDynamicPeriodEntity()));
                            }
                        });
    }
}
