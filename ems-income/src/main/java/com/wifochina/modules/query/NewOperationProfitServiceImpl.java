package com.wifochina.modules.query;

import com.alibaba.fastjson.JSON;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.TimePointEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.ElectricProfitService;
import com.wifochina.modules.income.NewOperationProfitService;
import com.wifochina.modules.income.RenewableProfitService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.income.timer.TaskOperationTimer;
import com.wifochina.modules.income.vo.OperationProfitVo;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.VO.GroupElectricProfit;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.operation.service.impl.NewInfluxOperationProfitServiceImpl;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.Nullable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<<<<<< Updated upstream
 */
@Service
@Slf4j
@AllArgsConstructor
public class NewOperationProfitServiceImpl implements NewOperationProfitService {

    private final GroupService groupService;
    private final ElectricProfitService electricProfitService;
    private final NewInfluxOperationProfitServiceImpl operationProfitService;
    private final RenewableProfitService renewableProfitService;
    private final ProjectService projectService;
    private final RedisTemplate<String, String> redisTemplate;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final InfluxClientService influxClientService;
    private static final int MAP_DEFAULT_SIZE = 16;

    /**
     * @param todayWhetherFromCache: true /false 是否今天的数据也走缓存, true的话就是从像其他的一样 从对应的map中获取 如
     *     newTodayProfitMap, false的话是 三页收益查询 这种查询的实时的 并且不会像大屏那样频繁
     * @param projectId : projectId
     * @param timePointEnums : timePointEnums
     * @return : key :timePointEnum.getValue() Object: 收益
     *     <p>先获取到TodayFuture 然后 再创建其他的future 把todayFuture传递进去 最后汇总 resultMap里
     */
    @Override
    public Map<String, Object> getTotalOperationProfit(
            boolean todayWhetherFromCache, String projectId, TimePointEnum... timePointEnums) {
        Map<String, Object> resultMap = new HashMap<>();
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 获取today的future
        CompletableFuture<OperationProfitVo> todayFuture =
                getTodayFuture(todayWhetherFromCache, projectId, timePointEnums, projectEntity);
        List<TimePointEnum> otherTimePointEnums =
                Arrays.stream(timePointEnums)
                        .filter(
                                timePointEnum ->
                                        !timePointEnum
                                                .getValue()
                                                .equals(TimePointEnum.TODAY.getValue()))
                        .collect(Collectors.toList());
        Map<String, CompletableFuture<OperationProfitVo>> otherFutureMaps =
                new ConcurrentHashMap<>();
        // 获取到 其他TimePoint 的future
        for (TimePointEnum timePointEnum : otherTimePointEnums) {
            String key = "newProfit:" + timePointEnum.getValue() + ":" + projectId;
            OperationProfitVo cacheObject =
                    JSON.parseObject(redisTemplate.opsForValue().get(key), OperationProfitVo.class);
            if (cacheObject == null) {
                CompletableFuture<OperationProfitVo> otherFuture =
                        CompletableFuture.supplyAsync(
                                () ->
                                        getAllTotalProfitMap(
                                                todayWhetherFromCache,
                                                todayFuture,
                                                projectEntity,
                                                timePointEnum),
                                threadPoolTaskExecutor);
                otherFutureMaps.put(timePointEnum.getValue(), otherFuture);
            } else {
                resultMap.put(timePointEnum.getValue(), cacheObject);
            }
        }
        CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(otherFutureMaps.values().toArray(new CompletableFuture[0]));
        // 等待future
        allFutures.join();
        otherFutureMaps.forEach(
                (timePoint, otherFuture) -> {
                    try {
                        String key = "newProfit:" + timePoint + ":" + projectId;
                        OperationProfitVo operationProfitVo = otherFuture.get(1L, TimeUnit.MINUTES);
                        redisTemplate
                                .opsForValue()
                                .set(
                                        key,
                                        JSON.toJSONString(operationProfitVo),
                                        10,
                                        TimeUnit.SECONDS);
                        resultMap.put(timePoint, operationProfitVo);
                    } catch (InterruptedException | ExecutionException | TimeoutException e) {
                        throw new RuntimeException(e);
                    }
                });
        if (todayFuture != null) {
            try {
                OperationProfitVo todayOperationProfitVo = todayFuture.get(1L, TimeUnit.MINUTES);
                resultMap.put(TimePointEnum.TODAY.getValue(), todayOperationProfitVo);
                // 特殊处理一下 如果这个今日的数据从 cache缓存中拿到的 那就不要塞入redis了 不然一直无法得到最新的
                // 这个fromCache 是在getTodayFuture
                if (!todayOperationProfitVo.isFromCache()) {
                    String key = "newProfit:" + TimePointEnum.TODAY.getValue() + ":" + projectId;
                    redisTemplate
                            .opsForValue()
                            .set(
                                    key,
                                    JSON.toJSONString(todayOperationProfitVo),
                                    10,
                                    TimeUnit.SECONDS);
                }
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                throw new RuntimeException(e);
            }
        }
        return resultMap;
    }

    private @Nullable CompletableFuture<OperationProfitVo> getTodayFuture(
            boolean todayWhetherFromCache,
            String projectId,
            TimePointEnum[] timePointEnums,
            ProjectEntity projectEntity) {
        boolean needSearchToday = true;
        CompletableFuture<OperationProfitVo> todayFuture;
        if (timePointEnums.length == 1
                && timePointEnums[0].getValue().equals(TimePointEnum.YESTERDAY.getValue())) {
            // 只有一个单独的查询 昨日的不需要查询今日 其他的都需要查询今日
            needSearchToday = false;
        }
        if (needSearchToday) {
            // 如果这次查询里 有今天的 先查询今天的
            String key = "newProfit:" + TimePointEnum.TODAY.getValue() + ":" + projectId;
            OperationProfitVo todayCacheObject =
                    JSON.parseObject(redisTemplate.opsForValue().get(key), OperationProfitVo.class);
            if (todayCacheObject == null) {
                todayFuture =
                        CompletableFuture.supplyAsync(
                                () ->
                                        getAllTotalProfitMap(
                                                todayWhetherFromCache,
                                                null,
                                                projectEntity,
                                                TimePointEnum.TODAY),
                                threadPoolTaskExecutor);
            } else {
                // 注意这里要 把缓存中放到这个future里面 供后面的用
                todayCacheObject.setFromCache(true);
                todayFuture = CompletableFuture.supplyAsync(() -> todayCacheObject);
            }
        } else {
            // 这种情况就是 不需要查询今天的 目前也就是只查询 yesterday的数据
            todayFuture = null;
        }
        return todayFuture;
    }

    protected OperationProfitVo getAllTotalProfitMap(
            boolean todayWhetherFromCache,
            CompletableFuture<OperationProfitVo> todayOperationProfitFuture,
            ProjectEntity projectEntity,
            TimePointEnum... timePointEnums) {
        Map<String, Boolean> timePointEnumMap =
                Arrays.stream(timePointEnums)
                        .collect(Collectors.toMap(TimePointEnum::getValue, timePointEnum -> true));
        OperationProfitVo todayOperationProfit;
        // 这里是查询今天的 电的收益, 不要进行分组, 需要的是系统的今日的电收益
        if (timePointEnumMap.get(TimePointEnum.TODAY.getValue()) != null) {
            // 获取今天的收益 这里的处理是为了大屏那边
            if (todayWhetherFromCache) {
                // 注意这个 newTodayProfitMap 是不包括今日的需量收益的, 只有电 pv wind waster 之和
                if (TaskOperationTimer.newTodayProfitMap.get(projectEntity.getId()) == null) {
                    // 这里看日志有问题 所以这里返回一个 new 对象  防止调用地方 抛错, 这种情况可能map还没缓存或者什么情况
                    todayOperationProfit = new OperationProfitVo();
                } else {
                    todayOperationProfit =
                            TaskOperationTimer.newTodayProfitMap.get(projectEntity.getId());
                }
            } else {
                // 不走缓存实时去查询
                todayOperationProfit = this.getTodayOperationProfit(projectEntity);
            }
            return todayOperationProfit;
        } else if (timePointEnumMap.get(TimePointEnum.YESTERDAY.getValue()) != null) {
            return getOperationProfit(
                    TimePointEnum.YESTERDAY, todayOperationProfitFuture, projectEntity);
        } else if (timePointEnumMap.get(TimePointEnum.MONTH.getValue()) != null) {
            // 如果不存在 今天的则需要去计算一下 今天的 用于累计的时候 加上去
            return getOperationProfit(
                    TimePointEnum.MONTH, todayOperationProfitFuture, projectEntity);
        } else if (timePointEnumMap.get(TimePointEnum.TOTAL.getValue()) != null) {
            // 计算 累计 需要实时今天的数据
            return getOperationProfit(
                    TimePointEnum.TOTAL, todayOperationProfitFuture, projectEntity);
        } else {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }

    /**
     * TODO 暂时 真的没精力 重构了 乱七八糟的的 坑太多
     *
     * @param projectEntity
     * @return
     */
    @Override
    public OperationProfitVo getTodayOperationProfit(ProjectEntity projectEntity) {
        long now = Instant.now().getEpochSecond();
        // 查询到系统分组
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectEntity.getId());
        OperationProfitVo operationProfitVo = new OperationProfitVo();
        // 2023-10-09 之前调用getTotalBatteryProfit , 改为直接调用 getBatteryProfit
        // 之前调用getTotalBatteryProfit  已经被我删了
        CompletableFuture<ElectricProfitVO> electricFuture =
                CompletableFuture.supplyAsync(
                        () ->
                                electricProfitService.getTodayRealTimeElectricProfitVo(
                                        new ProfitRequest().setProjectId(projectEntity.getId()),
                                        false),
                        threadPoolTaskExecutor);
        // 查询今天的 0点到 now当前的时间
        BenefitRequest benefitRequest =
                new BenefitRequest()
                        .setStartDate(MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone()))
                        .setEndDate(now);
        // pv wind waster futures 异步的future , 正常来说3个future 但是如果对应开关没开 则直接没有对应的future
        Map<String, CompletableFuture<ProfitVO>> renewableFutureMaps =
                // 获取到 pv wind的 future 对应的maps
                this.getRenewableFutureMaps(benefitRequest, projectEntity, systemGroupEntity);
        CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(
                        renewableFutureMaps.values().toArray(new CompletableFuture[0]));
        // 让 电  pv  wind 3个查询 同步进行 提高查询效率
        allFutures.join();
        try {
            double totalOperationBenefit = 0.0;
            for (Map.Entry<String, CompletableFuture<ProfitVO>> entry :
                    renewableFutureMaps.entrySet()) {
                String pvOrWind = entry.getKey();
                CompletableFuture<ProfitVO> future = entry.getValue();
                if (pvOrWind.equals(CalculateTypeEnum.PV.name())) {
                    ProfitVO todayPvProfitVo = future.get();
                    if (todayPvProfitVo != null && todayPvProfitVo.getPvProfitVo() != null) {
                        // 注意这里变成了 从PvWindProfitVo里面获取了 也就是从
                        // todayPvProfitVo.getPvProfitVo(); 获取 但是我在
                        // getPvProfitVO 最后也放进去了所以还是可以和以前一样保持一致的获取 直接从 todayProfitVo.直接获取
                        operationProfitVo.setPvDischargeQuantity(
                                todayPvProfitVo.getPv_discharge_quantity());
                        operationProfitVo.setPvDischargeBenefit(
                                todayPvProfitVo.getPv_discharge_benefit());
                        totalOperationBenefit +=
                                Double.parseDouble(todayPvProfitVo.getPv_discharge_benefit());
                    }
                }
                if (pvOrWind.equals(CalculateTypeEnum.WIND.name())) {
                    ProfitVO todayWindProfitVo = future.get();
                    if (todayWindProfitVo != null && todayWindProfitVo.getWindProfitVo() != null) {
                        operationProfitVo.setWindDischargeQuantity(
                                todayWindProfitVo.getWind_discharge_quantity());
                        operationProfitVo.setWindDischargeBenefit(
                                todayWindProfitVo.getWind_discharge_benefit());
                        totalOperationBenefit +=
                                Double.parseDouble(todayWindProfitVo.getWind_discharge_benefit());
                    }
                }
                if (pvOrWind.equals(CalculateTypeEnum.WASTER.name())) {
                    ProfitVO todayWasterProfitVo = future.get();
                    if (todayWasterProfitVo != null
                            && todayWasterProfitVo.getWasterProfitVo() != null) {
                        operationProfitVo.setWasterDischargeQuantity(
                                todayWasterProfitVo.getWaster_discharge_quantity());
                        operationProfitVo.setWasterDischargeBenefit(
                                todayWasterProfitVo.getWaster_discharge_benefit());
                        totalOperationBenefit +=
                                Double.parseDouble(
                                        todayWasterProfitVo.getWaster_discharge_benefit());
                    }
                }
            }
            // 这个会比较慢放在最后取
            ElectricProfitVO todayElectricProfitVo = electricFuture.get(1, TimeUnit.MINUTES);
            // 电运营收益 放收益-充成本
            double todayElectricOperationBenefit =
                    todayElectricProfitVo.getTotalDischargeBenefit()
                            - todayElectricProfitVo.getTotalChargeCost();
            totalOperationBenefit += todayElectricOperationBenefit;
            // TODO refactor mapstruct
            operationProfitVo.setTotalBenefit(String.valueOf(totalOperationBenefit));
            operationProfitVo.setElectricBenefit(String.valueOf(todayElectricOperationBenefit));
            operationProfitVo.setTotalChargeQuantity(
                    String.valueOf(todayElectricProfitVo.getTotalChargeQuantity()));
            operationProfitVo.setTotalChargeCost(
                    String.valueOf(todayElectricProfitVo.getTotalChargeCost()));
            operationProfitVo.setTotalDischargeQuantity(
                    String.valueOf(todayElectricProfitVo.getTotalDischargeQuantity()));
            operationProfitVo.setTotalDischargeBenefit(
                    String.valueOf(todayElectricProfitVo.getTotalDischargeBenefit()));
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new RuntimeException(e);
        }

        return operationProfitVo;
    }

    @Override
    public Map<String, CompletableFuture<ProfitVO>> getRenewableFutureMaps(
            BenefitRequest benefitRequest,
            ProjectEntity projectEntity,
            GroupEntity systemGroupEntity) {
        Map<String, CompletableFuture<ProfitVO>> renewableFutureMaps = new HashMap<>();
        if (systemGroupEntity != null
                && systemGroupEntity.getCalcEarningsController() != null
                && systemGroupEntity.getCalcEarningsController()) {
            // 2023-11-17 10:24:55 add 根据pv收益开关去 判断是否要把 pv收益计算进去
            // 2024-11-01 14:44:22add 或者根据dcdc收益开关  判断是否要计算 pv收益
            if (systemGroupEntity.getPvProfitController()
                    || systemGroupEntity.getDcdcProfitController()) {
                CompletableFuture<ProfitVO> pvFuture =
                        CompletableFuture.supplyAsync(
                                () ->
                                        renewableProfitService.getRenewableProfitVo(
                                                benefitRequest,
                                                projectEntity.getId(),
                                                CalculateTypeEnum.PV),
                                threadPoolTaskExecutor);
                renewableFutureMaps.put(CalculateTypeEnum.PV.name(), pvFuture);
            }
            // 2023-11-17 10:24:55 add 根据wind收益开关去 判断是否要把 wind收益计算进去
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())
                    && Boolean.TRUE.equals(systemGroupEntity.getWindEarningsController())) {
                CompletableFuture<ProfitVO> windFuture =
                        CompletableFuture.supplyAsync(
                                () ->
                                        renewableProfitService.getRenewableProfitVo(
                                                benefitRequest,
                                                projectEntity.getId(),
                                                CalculateTypeEnum.WIND),
                                threadPoolTaskExecutor);
                renewableFutureMaps.put(CalculateTypeEnum.WIND.name(), windFuture);
            }
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration())
                    && Boolean.TRUE.equals(systemGroupEntity.getWasterEarningsController())) {
                CompletableFuture<ProfitVO> wasterFuture =
                        CompletableFuture.supplyAsync(
                                () ->
                                        renewableProfitService.getRenewableProfitVo(
                                                benefitRequest,
                                                projectEntity.getId(),
                                                CalculateTypeEnum.WASTER),
                                threadPoolTaskExecutor);
                renewableFutureMaps.put(CalculateTypeEnum.WASTER.name(), wasterFuture);
            }
        }
        return renewableFutureMaps;
    }

    //    private @NotNull Map<String, CompletableFuture<ProfitVO>> getRenewableFutureMaps(
    //            ProjectEntity projectEntity, GroupEntity systemGroupEntity, long now) {
    //
    //        Map<String, CompletableFuture<ProfitVO>> renewableFutureMaps = new HashMap<>();
    //        if (systemGroupEntity != null
    //                && systemGroupEntity.getCalcEarningsController() != null
    //                && systemGroupEntity.getCalcEarningsController()) {
    //            // 2023-11-17 10:24:55 add 根据pv收益开关去 判断是否要把 pv收益计算进去
    //            // 2024-11-01 14:44:22add 或者根据dcdc收益开关  判断是否要计算 pv收益
    //            if (systemGroupEntity.getPvProfitController()
    //                    || systemGroupEntity.getDcdcProfitController()) {
    //                BenefitRequest benefitRequest = new BenefitRequest();
    //                benefitRequest.setStartDate(
    //                        MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone()));
    //                benefitRequest.setEndDate(now);
    //                CompletableFuture<ProfitVO> pvFuture =
    //                        CompletableFuture.supplyAsync(
    //                                () ->
    //                                        renewableProfitService.getRenewableProfitVo(
    //                                                benefitRequest,
    //                                                projectEntity.getId(),
    //                                                CalculateTypeEnum.PV),
    //                                //
    //                                // operationProfitService.getPvProfitVO(
    //                                //
    // benefitRequest,
    //                                // projectEntity.getId()),
    //                                threadPoolTaskExecutor);
    //                renewableFutureMaps.put(CalculateTypeEnum.PV.name(), pvFuture);
    //            }
    //            // 2023-11-17 10:24:55 add 根据wind收益开关去 判断是否要把 wind收益计算进去
    //            if (systemGroupEntity.getEnableWindPowerGeneration()) {
    //                BenefitRequest benefitRequest = new BenefitRequest();
    //                benefitRequest.setStartDate(
    //                        MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone()));
    //                benefitRequest.setEndDate(now);
    //                CompletableFuture<ProfitVO> windFuture =
    //                        CompletableFuture.supplyAsync(
    //                                () ->
    //                                        renewableProfitService.getRenewableProfitVo(
    //                                                benefitRequest,
    //                                                projectEntity.getId(),
    //                                                CalculateTypeEnum.WIND),
    //
    //                                //
    //                                // operationProfitService.getWindProfitVO(
    //                                //
    // benefitRequest,
    //                                // projectEntity.getId()),
    //                                threadPoolTaskExecutor);
    //                renewableFutureMaps.put(CalculateTypeEnum.WIND.name(), windFuture);
    //            }
    //            if (systemGroupEntity.getEnableWastePowerGeneration()) {
    //                BenefitRequest benefitRequest = new BenefitRequest();
    //                benefitRequest.setStartDate(
    //                        MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone()));
    //                benefitRequest.setEndDate(now);
    //                CompletableFuture<ProfitVO> wasterFuture =
    //                        CompletableFuture.supplyAsync(
    //                                () ->
    //                                        renewableProfitService.getRenewableProfitVo(
    //                                                benefitRequest,
    //                                                projectEntity.getId(),
    //                                                CalculateTypeEnum.WASTER),
    //                                threadPoolTaskExecutor);
    //                renewableFutureMaps.put(CalculateTypeEnum.WASTER.name(), wasterFuture);
    //            }
    //        }
    //        return renewableFutureMaps;
    //    }

    @Override
    public GroupElectricProfit getGroupProfitVo(
            BenefitRequest benefitRequest, ProjectEntity projectEntity, GroupEntity group) {
        ProfitRequest profitRequest = new ProfitRequest();
        profitRequest.setStartDate(benefitRequest.getStartDate());
        profitRequest.setEndDate(benefitRequest.getEndDate());
        // 查询这个单个分组的所有的 设备的收益
        profitRequest.setDeviceId(EmsConstants.ALL);
        // core 计算单个分组的收益
        profitRequest.setGroupId(group.getId());
        profitRequest.setProjectId(projectEntity.getId());
        ElectricProfitVO groupElectricProfitVoAll =
                electricProfitService.getElectricTotalProfit(profitRequest);
        Double groupTotalBenefit = groupElectricProfitVoAll.getTotalBenefit();
        return new GroupElectricProfit()
                .setGroupName(group.getName())
                .setGroupElectricBenefit(groupTotalBenefit);
    }

    @Override
    public ProfitVO getProfitVO(
            BenefitRequest benefitRequest, String projectId, boolean calGroupFlag)
            throws InterruptedException {
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 这里 因为有一些方法里面 改了这个 原始的benefitRequest 的参数 所以这里copy几个

        // 查询到系统分组
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectEntity.getId());
        if (systemGroupEntity == null) {
            throw new ServiceException(ErrorResultCode.SYSTEM_GROUP_NOT_EXIST.value());
        }
        ProfitVO profitVO = new ProfitVO();
        Map<String, GroupElectricProfit> groupElectricProfitMap = new ConcurrentHashMap<>();
        // calGroupFlag 为了标注有些Task任务调用该方法的时候 不需要去计算分组收益 , 哪怕开启了分组收益开关
        boolean groupEarningsFlag =
                groupService.groupEarningCondition(projectEntity.getId(), systemGroupEntity);
        CountDownLatch countDownLatch;
        if (calGroupFlag && systemGroupEntity.getCalcEarningsController() && groupEarningsFlag) {
            // 2024-03-20 10:43:45 前提是 非系统分组里面 有绑定了开启了收益的设备或者电表的分组列表
            List<GroupEntity> groupsNotSystems =
                    groupService.queryGroupsNotSystemAndBindIncomeDeviceOrMeter(
                            WebUtils.projectId.get());
            // core 计算每个分数的收益
            countDownLatch = new CountDownLatch(groupsNotSystems.size());
            for (GroupEntity groupsNotSystem : groupsNotSystems) {
                threadPoolTaskExecutor.submit(
                        () -> {
                            // 可以改成 CompletableFuture 保持统一 但是懒得改了!
                            GroupElectricProfit groupElectricProfit =
                                    NewOperationProfitServiceImpl.this.getGroupProfitVo(
                                            benefitRequest, projectEntity, groupsNotSystem);
                            groupElectricProfitMap.put(
                                    groupsNotSystem.getId(), groupElectricProfit);
                            countDownLatch.countDown();
                        });
            }
        } else {
            countDownLatch = null;
        }
        // 下面这段还是保留原始的 意思就是查询所有的 设备的
        calProfitNotGroup(benefitRequest, projectEntity, profitVO, systemGroupEntity);
        // 提高速度 去获取到 分组 计算的相关数据
        if (countDownLatch != null) {
            countDownLatch.await();
            profitVO.setGroupElectricProfitMap(groupElectricProfitMap);
        }
        return profitVO;
    }

    private void calProfitNotGroup(
            BenefitRequest benefitRequest,
            ProjectEntity projectEntity,
            ProfitVO profitVO,
            GroupEntity systemGroupEntity) {
        // 历史遗留问题...唉 电查询使用 的这个 ProfitRequest 无语
        ProfitRequest electricProfitRequest = new ProfitRequest();
        electricProfitRequest.setStartDate(benefitRequest.getStartDate());
        electricProfitRequest.setEndDate(benefitRequest.getEndDate());
        electricProfitRequest.setDeviceId(EmsConstants.ALL);
        electricProfitRequest.setProjectId(projectEntity.getId());
        electricProfitRequest.setGroupId(EmsConstants.ALL);
        Map<String, CompletableFuture<ProfitVO>> renewableFutureMaps =
                this.getRenewableFutureMaps(benefitRequest, projectEntity, systemGroupEntity);
        // TODO  remove 暂时优化一部分吧 ..
        //        CompletableFuture<ProfitVO> pvFuture =
        //                CompletableFuture.supplyAsync(
        //                        () ->
        //                                renewableProfitService.getRenewableProfitVo(
        //                                        benefitRequest,
        //                                        projectEntity.getId(),
        //                                        CalculateTypeEnum.PV),
        //                        threadPoolTaskExecutor);
        //        CompletableFuture<ProfitVO> windFuture =
        //                CompletableFuture.supplyAsync(
        //                        () ->
        //                                renewableProfitService.getRenewableProfitVo(
        //                                        benefitRequest,
        //                                        projectEntity.getId(),
        //                                        CalculateTypeEnum.WIND),
        //                        threadPoolTaskExecutor);
        //
        //        CompletableFuture<ProfitVO> wasterFuture =
        //                CompletableFuture.supplyAsync(
        //                        () ->
        //                                renewableProfitService.getRenewableProfitVo(
        //                                        benefitRequest,
        //                                        projectEntity.getId(),
        //                                        CalculateTypeEnum.WASTER),
        //                        threadPoolTaskExecutor);

        CompletableFuture<ProfitVO> demandFuture =
                CompletableFuture.supplyAsync(
                        () ->
                                operationProfitService.getDemandProfitVO(
                                        benefitRequest, projectEntity.getId()),
                        threadPoolTaskExecutor);

        CompletableFuture<ElectricProfitVO> electricFuture =
                CompletableFuture.supplyAsync(
                        () -> electricProfitService.getElectricTotalProfit(electricProfitRequest));

        List<CompletableFuture<?>> allFuturesList = new ArrayList<>(renewableFutureMaps.values());
        allFuturesList.add(demandFuture);
        allFuturesList.add(electricFuture);
        CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(allFuturesList.toArray(new CompletableFuture[0]));
        //        CompletableFuture<Void> allFutures =
        //                CompletableFuture.allOf(
        //                        pvFuture, windFuture, wasterFuture, demandFuture, electricFuture);
        // 把electricProfitVoAll设置到 profitVo上面
        allFutures.join();
        try {
            double totalBenefit;
            for (Map.Entry<String, CompletableFuture<ProfitVO>> entry :
                    renewableFutureMaps.entrySet()) {
                String renewableType = entry.getKey();
                CompletableFuture<ProfitVO> future = entry.getValue();
                ProfitVO futureProfitVo = future.get();
                if (renewableType.equals(CalculateTypeEnum.PV.name())) {
                    if (futureProfitVo != null && futureProfitVo.getPvProfitVo() != null) {
                        profitVO.setPv_discharge_quantity(
                                futureProfitVo.getPv_discharge_quantity());
                        profitVO.setPv_discharge_benefit(futureProfitVo.getPv_discharge_benefit());
                        profitVO.setPvProfitVo(futureProfitVo.getPvProfitVo());
                    }
                }
                if (renewableType.equals(CalculateTypeEnum.WIND.name())) {
                    if (futureProfitVo != null && futureProfitVo.getWindProfitVo() != null) {
                        profitVO.setWind_discharge_quantity(
                                futureProfitVo.getWind_discharge_quantity());
                        profitVO.setWind_discharge_benefit(
                                futureProfitVo.getWind_discharge_benefit());
                        profitVO.setWindProfitVo(futureProfitVo.getWindProfitVo());
                    }
                }
                if (renewableType.equals(CalculateTypeEnum.WASTER.name())) {
                    if (futureProfitVo != null && futureProfitVo.getWasterProfitVo() != null) {
                        profitVO.setWaster_discharge_quantity(
                                futureProfitVo.getWaster_discharge_quantity());
                        profitVO.setWaster_discharge_benefit(
                                futureProfitVo.getWaster_discharge_benefit());
                        profitVO.setWasterProfitVo(futureProfitVo.getWasterProfitVo());
                    }
                }
            }
            ProfitVO demandProfitVo = demandFuture.get();
            ElectricProfitVO electricProfitVO = electricFuture.get();

            // TODO remove
            //            ProfitVO pvProfitVo = pvFuture.get();
            //            ProfitVO windProfitVo = windFuture.get();
            //            ProfitVO wasterProfitVo = wasterFuture.get();
            //            ProfitVO demandProfitVo = demandFuture.get();
            //            ElectricProfitVO electricProfitVO = electricFuture.get();
            //            String pvDischargeBenefit = pvProfitVo.getPv_discharge_benefit();
            //            String windDischargeBenefit = windProfitVo.getWind_discharge_benefit();
            //            String wasterDischargeBenefit =
            // wasterProfitVo.getWaster_discharge_benefit();
            //            profitVO.setPv_discharge_quantity(pvProfitVo.getPv_discharge_quantity());
            //
            // profitVO.setWind_discharge_quantity(windProfitVo.getWind_discharge_quantity());
            //
            // profitVO.setWaster_discharge_quantity(wasterProfitVo.getWaster_discharge_quantity());
            //
            // profitVO.setWaster_discharge_benefit(wasterProfitVo.getWaster_discharge_benefit());
            //            profitVO.setPv_discharge_benefit(pvDischargeBenefit);
            //            profitVO.setWind_discharge_benefit(windDischargeBenefit);
            //            profitVO.setPvProfitVo(pvProfitVo.getPvProfitVo());
            //            profitVO.setWindProfitVo(windProfitVo.getWindProfitVo());
            //            profitVO.setWasterProfitVo(wasterProfitVo.getWasterProfitVo());
            profitVO.setDemand_control_power(demandProfitVo.getDemand_control_power());
            profitVO.setDemand_control_benefit(demandProfitVo.getDemand_control_benefit());
            profitVO.setDemandProfitMap(demandProfitVo.getDemandProfitMap());
            // 设置一些详细的电的尖峰平谷等参数到 profitVo
            setProfitVoFromElectricProfitVo(profitVO, electricProfitVO);
            // 第二步如果有需量收益 把需量的加上去
            totalBenefit =
                    electricProfitVO.getTotalBenefit()
                            + Double.parseDouble(
                                    demandProfitVo.getDemand_control_benefit() == null
                                            ? "0"
                                            : demandProfitVo.getDemand_control_benefit());
            // TODO remove
            // 2023-11-17 11:07:57 add 前端让后端去统计一下 总收益,
            // 故此 新增总收益 = 电池收益 + 风电收益 + 需量收益 + 光伏收益(根据光伏收益开关判断是否加入)
            // 第三步 如果有pv wind 收益 加上去
            //            if (systemGroupEntity.getPvProfitController()
            //                    || systemGroupEntity.getDcdcProfitController()) {
            //                totalBenefit +=
            //                        pvDischargeBenefit == null ? 0.0 :
            // Double.parseDouble(pvDischargeBenefit);
            //            }
            //            if (systemGroupEntity.getWindEarningsController()) {
            //                totalBenefit +=
            //                        windDischargeBenefit == null
            //                                ? 0.0
            //                                : Double.parseDouble(windDischargeBenefit);
            //            }
            //            // 1.4.2 余热发电
            //            if (systemGroupEntity.getWasterEarningsController()) {
            //                totalBenefit +=
            //                        wasterDischargeBenefit == null
            //                                ? 0.0
            //                                : Double.parseDouble(wasterDischargeBenefit);
            //            }
            // 这里 profitVO 对应的属性肯定会有 无非是 0 还是有值 , 开关也不需要判断, 因为 获取renewable收益都有开关
            totalBenefit +=
                    Double.parseDouble(profitVO.getPv_discharge_benefit())
                            + Double.parseDouble(profitVO.getWind_discharge_benefit())
                            + Double.parseDouble(profitVO.getWaster_discharge_benefit());
            profitVO.setTotal_benefit_extend(String.valueOf(totalBenefit));
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private static void setProfitVoFromElectricProfitVo(
            ProfitVO profitVO, ElectricProfitVO electricProfitVoAll) {
        profitVO.setElectric_benefit(String.valueOf(electricProfitVoAll.getTotalBenefit()));
        profitVO.setFlat_charge_cost(String.valueOf(electricProfitVoAll.getFlatChargeCost()));
        profitVO.setFlat_charge_quantity(
                String.valueOf(electricProfitVoAll.getFlatChargeQuantity()));
        profitVO.setFlat_discharge_benefit(
                String.valueOf(electricProfitVoAll.getFlatDischargeBenefit()));
        profitVO.setFlat_discharge_quantity(
                String.valueOf(electricProfitVoAll.getFlatDischargeQuantity()));
        profitVO.setPeak_charge_cost(String.valueOf(electricProfitVoAll.getPeakChargeCost()));
        profitVO.setPeak_charge_quantity(
                String.valueOf(electricProfitVoAll.getPeakChargeQuantity()));
        profitVO.setPeak_discharge_benefit(
                String.valueOf(electricProfitVoAll.getPeakDischargeBenefit()));
        profitVO.setPeak_discharge_quantity(
                String.valueOf(electricProfitVoAll.getPeakDischargeQuantity()));
        profitVO.setVally_charge_cost(String.valueOf(electricProfitVoAll.getVallyChargeCost()));
        profitVO.setVally_charge_quantity(
                String.valueOf(electricProfitVoAll.getVallyChargeQuantity()));
        profitVO.setVally_discharge_benefit(
                String.valueOf(electricProfitVoAll.getVallyDischargeBenefit()));
        profitVO.setVally_discharge_quantity(
                String.valueOf(electricProfitVoAll.getVallyDischargeQuantity()));
        profitVO.setDeep_vally_charge_cost(
                String.valueOf(electricProfitVoAll.getDeepVallyChargeCost()));
        profitVO.setDeep_vally_charge_quantity(
                String.valueOf(electricProfitVoAll.getDeepVallyChargeQuantity()));
        profitVO.setDeep_vally_discharge_benefit(
                String.valueOf(electricProfitVoAll.getDeepVallyDischargeBenefit()));
        profitVO.setDeep_vally_discharge_quantity(
                String.valueOf(electricProfitVoAll.getDeepVallyDischargeQuantity()));
        profitVO.setTip_charge_cost(String.valueOf(electricProfitVoAll.getTipChargeCost()));
        profitVO.setTip_charge_quantity(String.valueOf(electricProfitVoAll.getTipChargeQuantity()));
        profitVO.setTip_discharge_benefit(
                String.valueOf(electricProfitVoAll.getTipDischargeBenefit()));
        profitVO.setTip_discharge_quantity(
                String.valueOf(electricProfitVoAll.getTipDischargeQuantity()));
        profitVO.setTotal_charge_cost(String.valueOf(electricProfitVoAll.getTotalChargeCost()));
        profitVO.setTotal_charge_quantity(
                String.valueOf(electricProfitVoAll.getTotalChargeQuantity()));
        profitVO.setTotal_discharge_benefit(
                String.valueOf(electricProfitVoAll.getTotalDischargeBenefit()));
        profitVO.setTotal_discharge_quantity(
                String.valueOf(electricProfitVoAll.getTotalDischargeQuantity()));
    }

    private OperationProfitVo getOperationProfit(
            TimePointEnum timePointEnum,
            CompletableFuture<OperationProfitVo> todayOperationProfitFuture,
            ProjectEntity projectEntity) {
        String projectId = projectEntity.getId();
        OperationProfitVo operationProfitVo = new OperationProfitVo();
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectId);
        // 取到原始缓存数据
        OperationOriginalData originalData = getOperationOriginData(timePointEnum, projectId);
        ProfitVO pvProfitVo = originalData.getPvProfitVo();
        ProfitVO windProfitVo = originalData.getWindProfitVo();
        ProfitVO wasterProfitVo = originalData.getWasterProfitVo();
        ProfitVO demandProfit = originalData.getDemandProfit();
        ElectricProfitVO electricProfitVO = originalData.getElectricProfitVO();

        // 电池的 运营收益 放-成本
        double electricOperationBenefit =
                electricProfitVO.getTotalDischargeBenefit() - electricProfitVO.getTotalChargeCost();
        // 这个today的 getTotalBenefit 也就是today的getTotalDischargeBenefit -
        // 把电的运营收益加上去
        double totalOperationBenefit = electricOperationBenefit;
        // 需量 有就有 没有就是0
        totalOperationBenefit += Double.parseDouble(demandProfit.getDemand_control_benefit());

        // TODO refactor mapstruct
        operationProfitVo.setDemandControlBenefit(demandProfit.getDemand_control_benefit());
        operationProfitVo.setDemandControlPower(demandProfit.getDemand_control_power());

        // 昨日缓存 就不需要加上今日的了
        if (timePointEnum.getValue().equals(TimePointEnum.YESTERDAY.getValue())) {
            // 2023-11-17 10:24:55 add 根据pv收益开关去 判断是否要把 pv收益计算进去
            if (systemGroupEntity != null) {
                if (systemGroupEntity.getPvProfitController()) {
                    // 感觉需要判断一下 是全额上网方式还是 自发自用方式
                    totalOperationBenefit +=
                            Double.parseDouble(pvProfitVo.getPv_discharge_benefit());
                }
                if (systemGroupEntity.getWindEarningsController()) {
                    totalOperationBenefit +=
                            Double.parseDouble(windProfitVo.getWind_discharge_benefit());
                }
                if (systemGroupEntity.getWasterEarningsController()) {
                    totalOperationBenefit +=
                            Double.parseDouble(wasterProfitVo.getWaster_discharge_benefit());
                }
            }
            // TODO refactor code
            // 如果是昨天的 只需要从缓存总读取不需要加上今天的数据
            operationProfitVo.setElectricBenefit(String.valueOf(electricOperationBenefit));
            operationProfitVo.setTotalChargeQuantity(
                    String.valueOf(electricProfitVO.getTotalChargeQuantity()));
            operationProfitVo.setTotalChargeCost(
                    String.valueOf(electricProfitVO.getTotalChargeCost()));
            operationProfitVo.setTotalDischargeQuantity(
                    String.valueOf(electricProfitVO.getTotalDischargeQuantity()));
            operationProfitVo.setTotalDischargeBenefit(
                    String.valueOf(electricProfitVO.getTotalDischargeBenefit()));
            operationProfitVo.setTotalBenefit(String.valueOf(totalOperationBenefit));
            operationProfitVo.setPvDischargeBenefit(pvProfitVo.getPv_discharge_benefit());
            operationProfitVo.setPvDischargeQuantity(pvProfitVo.getPv_discharge_quantity());
            operationProfitVo.setWindDischargeBenefit(windProfitVo.getWind_discharge_benefit());
            operationProfitVo.setWindDischargeQuantity(windProfitVo.getWind_discharge_quantity());
            operationProfitVo.setWasterDischargeBenefit(
                    wasterProfitVo.getWaster_discharge_benefit());
            operationProfitVo.setWasterDischargeQuantity(
                    wasterProfitVo.getWaster_discharge_quantity());
        }
        // 总计和当月收益 需要加上今日
        if (timePointEnum.getValue().equals(TimePointEnum.MONTH.getValue())
                || timePointEnum.getValue().equals(TimePointEnum.TOTAL.getValue())) {
            OperationProfitVo todayOperationProfitVo;
            try {
                todayOperationProfitVo = todayOperationProfitFuture.get(1, TimeUnit.MINUTES);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                throw new RuntimeException(e);
            }
            if (todayOperationProfitVo == null) {
                // 正常不应该走到这里
                todayOperationProfitVo = this.getTodayOperationProfit(projectEntity);
                log.error("It shouldn't have come here this so slow in method getOperationProfit");
            }
            // 2023-11-17 10:24:55 add 根据pv收益开关去 判断是否要把 pv收益计算进去
            if (systemGroupEntity != null) {
                if (systemGroupEntity.getPvProfitController()) {
                    // 感觉需要判断一下 是全额上网方式还是 自发自用方式
                    double pv =
                            Double.parseDouble(pvProfitVo.getPv_discharge_benefit())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getPvDischargeBenefit());
                    totalOperationBenefit += pv;
                }
                if (systemGroupEntity.getWindEarningsController()) {
                    double wind =
                            Double.parseDouble(windProfitVo.getWind_discharge_benefit())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getWindDischargeBenefit());
                    totalOperationBenefit += wind;
                }
                if (systemGroupEntity.getWasterEarningsController()) {
                    double waster =
                            Double.parseDouble(wasterProfitVo.getWaster_discharge_benefit())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getWasterDischargeBenefit());
                    totalOperationBenefit += waster;
                }
            }
            // TODO refactor code
            double todayElectricOperation =
                    Double.parseDouble(todayOperationProfitVo.getTotalDischargeBenefit())
                            - Double.parseDouble(todayOperationProfitVo.getTotalChargeCost());
            operationProfitVo.setElectricBenefit(
                    String.valueOf(
                            // 缓存的+today的
                            electricOperationBenefit + todayElectricOperation));

            // 需要加上今天的数据
            operationProfitVo.setTotalChargeQuantity(
                    String.valueOf(
                            electricProfitVO.getTotalChargeQuantity()
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getTotalChargeQuantity())));
            operationProfitVo.setTotalChargeCost(
                    String.valueOf(
                            electricProfitVO.getTotalChargeCost()
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getTotalChargeCost())));
            operationProfitVo.setTotalDischargeQuantity(
                    String.valueOf(
                            electricProfitVO.getTotalDischargeQuantity()
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getTotalDischargeQuantity())));
            operationProfitVo.setTotalDischargeBenefit(
                    String.valueOf(
                            electricProfitVO.getTotalDischargeBenefit()
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getTotalDischargeBenefit())));
            operationProfitVo.setTotalBenefit(
                    String.valueOf(totalOperationBenefit + todayElectricOperation));

            operationProfitVo.setPvDischargeBenefit(
                    String.valueOf(
                            Double.parseDouble(pvProfitVo.getPv_discharge_benefit())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getPvDischargeBenefit())));
            operationProfitVo.setPvDischargeQuantity(
                    String.valueOf(
                            Double.parseDouble(pvProfitVo.getPv_discharge_quantity())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getPvDischargeQuantity())));
            operationProfitVo.setWindDischargeBenefit(
                    String.valueOf(
                            Double.parseDouble(windProfitVo.getWind_discharge_benefit())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getWindDischargeBenefit())));

            operationProfitVo.setWindDischargeQuantity(
                    String.valueOf(
                            Double.parseDouble(windProfitVo.getWind_discharge_quantity())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getWindDischargeQuantity())));
            // 1.4.2 add 余热发电
            operationProfitVo.setWasterDischargeBenefit(
                    String.valueOf(
                            Double.parseDouble(wasterProfitVo.getWaster_discharge_benefit())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getWasterDischargeBenefit())));
            operationProfitVo.setWasterDischargeQuantity(
                    String.valueOf(
                            Double.parseDouble(wasterProfitVo.getWaster_discharge_quantity())
                                    + Double.parseDouble(
                                            todayOperationProfitVo.getWasterDischargeQuantity())));
        }
        return operationProfitVo;
    }

    @Data
    @Accessors(chain = true)
    static class OperationOriginalData {
        ElectricProfitVO electricProfitVO;
        ProfitVO pvProfitVo;
        ProfitVO windProfitVo;
        ProfitVO wasterProfitVo;
        ProfitVO demandProfit;
    }

    /** 降低需量功率查询 */
    @Override
    public Map<String, Map<Long, Double>> getDemandPower(
            Long startDate, Long endDate, String projectId) {
        Map<String, Map<Long, Double>> map = new HashMap<>(MAP_DEFAULT_SIZE);
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<String> groupIds =
                groupService.queryEnableDemandIncome(projectId).stream()
                        .map(GroupEntity::getId)
                        .collect(Collectors.toList());
        if (groupIds.isEmpty()) {
            return map;
        }
        Map<String, Map<Long, Double>> meterMap = new HashMap<>(groupIds.size());
        Map<String, Map<Long, Double>> demandMap = new HashMap<>(groupIds.size());
        List<Restrictions> deviceRestrictions = new ArrayList<>(groupIds.size());
        for (String groupId : groupIds) {
            deviceRestrictions.add(Restrictions.tag("groupId").equal(groupId));
            meterMap.put(groupId, new HashMap<>(MAP_DEFAULT_SIZE));
            demandMap.put(groupId, new HashMap<>(MAP_DEFAULT_SIZE));
            map.put(groupId, new HashMap<>(MAP_DEFAULT_SIZE));
        }
        Restrictions groupTagFilter =
                Restrictions.or(deviceRestrictions.toArray(new Restrictions[0]));
        Flux flux =
                Flux.from(influxClientService.getBucketDemand())
                        .withLocationFixed(
                                MyTimeUtil.getOffsetSecondsFromZoneCode(projectEntity.getTimezone())
                                        + "s")
                        .range(startDate, endDate)
                        .filter(
                                Restrictions.measurement()
                                        .equal(influxClientService.getDemandTable(projectId, null)))
                        .filter(Restrictions.tag("projectId").equal(projectId))
                        .filter(groupTagFilter)
                        .aggregateWindow(1L, ChronoUnit.MONTHS, "max");
        // 将query进行兑换
        String queryString = flux.toString();
        log.debug(queryString);
        List<FluxTable> tables = influxClientService.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                Long time = Objects.requireNonNull(record.getTime()).getEpochSecond();
                Double value = (Double) record.getValueByKey("_value");
                String groupId = (String) record.getValueByKey("groupId");
                if ("original_Demand".equals(record.getField())) {
                    demandMap.get(groupId).put(time, value);
                }
                if ("meter_power".equals(record.getField())) {
                    meterMap.get(groupId).put(time, value);
                }
            }
        }
        if (meterMap.isEmpty()) {
            return map;
        }
        for (String groupId : groupIds) {
            for (Long time : meterMap.get(groupIds.get(0)).keySet()) {
                Double demandPower = demandMap.get(groupId).get(time);
                Double meterPower = meterMap.get(groupId).get(time);
                if (demandPower != null && meterPower != null) {
                    double controlPower = demandPower - meterPower;
                    if (map.get(groupId) != null) {
                        map.get(groupId).put(time, controlPower < 0 ? 0 : controlPower);
                    }
                }
            }
        }
        return map;
    }

    /**
     * 根据 不同的 TimePoint 去不同的 cache 里面取
     *
     * @param timePointEnum : timePointEnum
     * @param projectId : projectId
     * @return : OperationOriginalData
     */
    private static OperationOriginalData getOperationOriginData(
            TimePointEnum timePointEnum, String projectId) {
        ElectricProfitVO electricProfitVO = new ElectricProfitVO().init();
        ProfitVO pvProfitVo = new ProfitVO();
        ProfitVO windProfitVo = new ProfitVO();
        ProfitVO demandProfitVo = new ProfitVO();
        ProfitVO wasterProfitVo = new ProfitVO();
        if (timePointEnum.getValue().equals(TimePointEnum.YESTERDAY.getValue())) {
            electricProfitVO =
                    TaskOperationTimer.yesElectricMap.get(projectId) == null
                            ? new ElectricProfitVO().init()
                            : TaskOperationTimer.yesElectricMap.get(projectId);
            pvProfitVo =
                    TaskOperationTimer.yesPvMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.yesPvMap.get(projectId);
            windProfitVo =
                    TaskOperationTimer.yesWindMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.yesWindMap.get(projectId);
            wasterProfitVo =
                    TaskOperationTimer.yesWasterMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.yesWasterMap.get(projectId);
            // 昨日的 是没有需量的
        } else if (timePointEnum.getValue().equals(TimePointEnum.MONTH.getValue())) {
            electricProfitVO =
                    TaskOperationTimer.monthElectricMap.get(projectId) == null
                            ? new ElectricProfitVO().init()
                            : TaskOperationTimer.monthElectricMap.get(projectId);
            pvProfitVo =
                    TaskOperationTimer.monthPvMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.monthPvMap.get(projectId);
            windProfitVo =
                    TaskOperationTimer.monthWindMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.monthWindMap.get(projectId);
            wasterProfitVo =
                    TaskOperationTimer.monthWasterMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.monthWasterMap.get(projectId);

            demandProfitVo =
                    TaskOperationTimer.monthDemandControlMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.monthDemandControlMap.get(projectId);

        } else if (timePointEnum.getValue().equals(TimePointEnum.TOTAL.getValue())) {
            electricProfitVO =
                    TaskOperationTimer.totalElectricMap.get(projectId) == null
                            ? new ElectricProfitVO().init()
                            : TaskOperationTimer.totalElectricMap.get(projectId);
            pvProfitVo =
                    TaskOperationTimer.totalPvMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.totalPvMap.get(projectId);
            windProfitVo =
                    TaskOperationTimer.totalWindMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.totalWindMap.get(projectId);
            wasterProfitVo =
                    TaskOperationTimer.totalWasterMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.totalWasterMap.get(projectId);
            demandProfitVo =
                    TaskOperationTimer.demandControlMap.get(projectId) == null
                            ? new ProfitVO()
                            : TaskOperationTimer.demandControlMap.get(projectId);
        }
        return new OperationOriginalData()
                .setElectricProfitVO(electricProfitVO)
                .setPvProfitVo(pvProfitVo)
                .setWindProfitVo(windProfitVo)
                .setWasterProfitVo(wasterProfitVo)
                .setDemandProfit(demandProfitVo);
    }
}
