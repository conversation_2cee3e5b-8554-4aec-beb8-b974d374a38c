package com.wifochina.modules.query;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.constants.RenewableModelEnum;
import com.wifochina.common.onoff.OnOffComponent;
import com.wifochina.common.price.RenewableModelPriceFormulaComponent;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.RenewableProfitService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.renewable.RenewableAdapterChooser;
import com.wifochina.modules.renewable.vo.RenewableProfitVo;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * Created on 2024/9/10 11:25.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class RenewableProfitServiceImpl implements RenewableProfitService {

    private final GroupService groupService;
    private final DeviceService deviceService;
    private final ProjectService projectService;
    private final ElectricPriceService electricPriceService;
    private final RenewableAdapterChooser chooser;
    private final RenewableModelPriceFormulaComponent renewableModelPriceFormulaComponent;

    private final OnOffComponent onOffComponent;

    @Override
    public ProfitVO getRenewableProfitVo(
            BenefitRequest benefitRequest, String projectId, CalculateTypeEnum calculateTypeEnum) {
        ProfitVO profitVO = new ProfitVO();
        boolean renewableOnOffFlag =
                onOffComponent.renewableProfitOnOff(projectId, calculateTypeEnum);
        // 只有pv 并且 pv相关开关没开, 才去判断一下 dcdc相关开关
        if (CalculateTypeEnum.PV.name().equals(calculateTypeEnum.name()) && !renewableOnOffFlag) {
            // 得到是否开启dcdc dcdc开关如果开启 也可以去查询
            renewableOnOffFlag =
                    onOffComponent.dcdcAndProfitOnOff(projectId, new ArrayList<>()::addAll);
        }
        // 其他的一律按照 主可再生能源 开关判断, 比如 wind 和 余热发电 都 没有对应的 dcdc
        if (!renewableOnOffFlag) {
            return profitVO;
        }
        // 1. 把这部分 给去掉 , 只有查询 today今日的才去查询今日的电价相关
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 查询 可再生能源相关  如果查询的时间范围带今日的话 里面包括了 去实时计算今日的,
        RenewableProfitVo renewableProfitVo =
                chooser.choose(projectEntity.getElectricPriceType())
                        .calculateRenewableProfitVo(
                                benefitRequest, calculateTypeEnum.name(), projectEntity);

        RenewableModelEnum model = getModelFromCalculateType(projectEntity, calculateTypeEnum);
        if (model != null) {
            // 把收益汇聚一下 根据 不同的 model 去取不同的 收益字段放入 profitVO的 setXxx_discharge_benefit字段
            renewableModelPriceFormulaComponent.modelBenefitCollect(
                    calculateTypeEnum,
                    model,
                    renewableProfitVo,
                    new RenewableModelPriceFormulaComponent.ModelBenefitCollectPostProcessor() {
                        @Override
                        public void renewableBenefitPostProcessor(double benefit) {
                            fillRenewableBenefit(profitVO, calculateTypeEnum, benefit);
                        }
                    });
        }
        fillRenewableSubProfitVo(profitVO, calculateTypeEnum, renewableProfitVo);
        return profitVO;
    }

    private RenewableModelEnum getModelFromCalculateType(
            ProjectEntity project, CalculateTypeEnum calculateTypeEnum) {
        GroupEntity groupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getWhetherSystem, 1)
                                .eq(GroupEntity::getProjectId, project.getId()));
        RenewableModelEnum model = null;
        switch (calculateTypeEnum) {
            case PV:
                if (groupEntity == null
                        || groupEntity.getPhotovoltaicController() == null
                        || !groupEntity.getPvProfitController()) {
                    break;
                }
                if (groupEntity.getPhotovoltaicModel() != null) {
                    model = RenewableModelEnum.getModel(groupEntity.getPhotovoltaicModel());
                }
                break;
            case WIND:
                if (groupEntity == null
                        || groupEntity.getWindEarningsController() == null
                        || !groupEntity.getEnableWindPowerGeneration()) {
                    break;
                }
                if (groupEntity.getWindPowerModel() != null) {
                    model = RenewableModelEnum.getModel(groupEntity.getWindPowerModel());
                }
                break;
            case WASTER:
                if (groupEntity == null
                        || groupEntity.getEnableWastePowerGeneration() == null
                        || !groupEntity.getWasterEarningsController()) {
                    break;
                }
                if (groupEntity.getWasterPowerModel() != null) {
                    model = RenewableModelEnum.getModel(groupEntity.getWasterPowerModel());
                }
                break;
        }
        return model;
    }

    /**
     * 该方法主要是把 子对象 设置 到profitVO对应的参数上, 并且把电量设置到 对层profitVO 对象对应属性
     *
     * <p>// 这里不设置 收益是因为 收益会根据model 取一个字段 在 renewableModelPriceFormulaComponent.modelBenefitCollect
     * * 回调已经设置了
     *
     * @param profitVO
     * @param calculateTypeEnum
     * @param renewableProfitVo
     */
    private void fillRenewableSubProfitVo(
            ProfitVO profitVO,
            CalculateTypeEnum calculateTypeEnum,
            RenewableProfitVo renewableProfitVo) {
        switch (calculateTypeEnum) {
            case PV:
                // 注意这里是历史遗留问题 把
                profitVO.setPv_discharge_quantity(
                        String.valueOf(renewableProfitVo.getTotalDischargeQuantity()));
                // 设置 sub 子的对象
                profitVO.setPvProfitVo(renewableProfitVo);
                break;
            case WIND:
                profitVO.setWind_discharge_quantity(
                        String.valueOf(renewableProfitVo.getTotalDischargeQuantity()));
                profitVO.setWindProfitVo(renewableProfitVo);
                break;
            case WASTER:
                profitVO.setWaster_discharge_quantity(
                        String.valueOf(renewableProfitVo.getTotalDischargeQuantity()));
                profitVO.setWasterProfitVo(renewableProfitVo);
        }
    }

    private void fillRenewableBenefit(
            ProfitVO profitVO, CalculateTypeEnum calculateTypeEnum, double benefit) {
        switch (calculateTypeEnum) {
            case PV:
                profitVO.setPv_discharge_benefit(String.valueOf(benefit));
                break;
            case WIND:
                profitVO.setWind_discharge_benefit(String.valueOf(benefit));
                break;
            case WASTER:
                profitVO.setWaster_discharge_benefit(String.valueOf(benefit));
        }
    }

    @Override
    public ProfitVO getTodayRenewableProfitVo(
            ProfitRequest profitRequest, CalculateTypeEnum calculateTypeEnum) {
        return null;
    }
}
