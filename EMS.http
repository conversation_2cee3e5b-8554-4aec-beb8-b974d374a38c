### GET request with dynamic variables
GET https://vpn.weiheng-tech.com:21170/auth/ldap?username='jacob.sun'&password='1234'

###

###
POST http://192.168.130.58:23456/auth/ldap
Content-Type: application/json

{
  "username": "jacob.sun",
  "password": "123123"
}

###

###
POST http://ldap-login.dev.weiheng-tech.com/auth/ldap
Content-Type: application/json

{
  "username": "jacob.sun",
  "password": "12344"
}

###

###
GET http://192.168.130.49:9091/api/v1/get_ems_modbus
Accept: application/json
ProjectId: f4477a16be694375b2e9778d2862c217

###

###
POST https://pangu-cloud-gateway.weiheng-tech.com/api/v1/get_online
Content-Type: application/json
ProjectId: 8721f6f53b954e75a38ed08a0c288cc1

{}

###

###
POST https://pangu-api.weiheng-tech.com/api/oauth/token
Content-Type: application/json
Authorization:

{
  "code": "",
  "password": "jdf123456",
  "phone": "",
  "type": 0,
  "username": "jdfapi"
}

###
###
POST http://pangu-cloud.dev.weiheng-tech.com/weather/strategy/history
Content-Type: application/json
Authorization: H4sIAAAAAAAAAA3IyRKCIAAA0C+qIRKqo3tAZpC0eHGULElpccyyr693fOVAqyJUOtZ0K79kstZ0MS4H+j3tyT/Jh9zAmDNAOAxqvSMPEaqZJTs36XkiKc6slOP7rUFn9UKsJ9usyfsm857BQhgvbQfgCC/3I9eGRTeDxTz2xBt1psAWYCrGtDb5ii6zgx7ZreMq6IMnh0KZyKm7yorQ9F2B/FPaZfgwu1bv1fTOKnbE182y5RIjqa9oNZrb6cRA9xL9AKsoRU3QAAAA

{
  "start": 1692633600,
  "end": 1693324799,
  "projectId": "f4477a16be694375b2e9778d2862c217",
  "capacity": 613,
  "p_charge_max": 200,
  "p_discharge_max": 200,
  "p_delta_max": 200,
  "p_control_max": 410,
  "soc_min": 0.1
}

###
POST http://pangu-api.weiheng-tech.com/weather/strategy/history
Content-Type: application/json
Authorization: H4sIAAAAAAAAABXIyXKCMAAA0C/CAUZBjpikEmxClTVcHPY9joMsyde3fcdXCbfNr0Xnda4fSqzRzrUOlXBlGeP/nIhEKtGZTnu3Z0HappBJL8bCg7YkAdO84DFSiSXp8Iyn6FgAbPz9iUKkkx6diKMeWHsyNRCLfWapstsg4eoOSI7gBdhvg89ZSE1lbeBYfUU6Lz1FRTcbDKhesqnLl3mXjrCUlXv7K2sW0E8o+q6DYh83WT2f1xQ788ZXa8guH3N90JCpiXOvP9MStvfaZNBgGtyOvthw3LfS5uWbI3g2zxMdbzx4JbOofqrGL01r+AXcBsG9EAEAAA==

{
  "start": 1692633600,
  "end": 1693324799,
  "projectId": "f4477a16be694375b2e9778d2862c217",
  "capacity": 613,
  "p_charge_max": 200,
  "p_discharge_max": 200,
  "p_delta_max": 200,
  "p_control_max": 410,
  "soc_min": 0.1
}



### 创建bucket ems_forever  ems_mean ems_demand
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/buckets
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "name": "ems_demand",
  "retentionRules": [],
  "ruleType": null,
  "readableRetention": "forever",
  "orgID": "weiheng",
  "type": "user",
  "schemaType": "implicit"
}

### 创建task  ems_meter_forever
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/tasks
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "description": "",
  "flux": "import \"influxdata/influxdb/tasks\"\n\noption task = {\n    name: \"ems_meter_forever\",\n    every: 5m,\n    offset: 1m,\n}\n\nfrom(bucket: \"ems_cloud\")\n    |> range(start: tasks.lastSuccess(orTime: -30m))\n    |> filter(fn: (r) => r._measurement =~ /^ems100@*/ or r._measurement =~ /^meter@*/)\n    |> aggregateWindow(every: 1m, fn: last)\n    |> to(bucket: \"ems_forever\")",
  "orgID": "weiheng",
  "status": "active"
}

### 创建task  non_ems_meter_forever
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/tasks
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "description": "",
  "flux": "import \"influxdata/influxdb/tasks\"\n\noption task = {\n    name: \"non_ems_meter_forever\",\n    every: 5m,\n    offset: 1m,\n}\n\nfrom(bucket: \"ems_cloud\")\n    |> range(start: tasks.lastSuccess(orTime: -30m))\n    |> filter(fn: (r) => r._measurement !~ /^ems100@*/ and r._measurement !~ /^meter@*/)\n    |> aggregateWindow(every: 5m, fn: last)\n    |> to(bucket: \"ems_forever\")",
  "orgID": "weiheng",
  "status": "active"
}

### 创建task  功率平均task
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/tasks
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "description": "",
  "flux": "import \"influxdata/influxdb/tasks\"\n\noption task = {\n    name: \"power_mean_1m\",\n    every: 5m,\n    offset: 1m,\n}\n\nfrom(bucket: \"ems_cloud\")\n    |> range(start: tasks.lastSuccess(orTime: -30m))\n    |> filter(fn: (r) => r._measurement =~ /^ems100@*/ or r._measurement =~ /^meter@*/)\n    |> filter(fn: (r) => r[\"_field\"] == \"ems_ac_active_power_pos\" or r[\"_field\"] == \"ems_ac_reactive_power\" or r[\"_field\"] == \"ac_active_power\" or r[\"_field\"] == \"ac_reactive_power\" or r[\"_field\"] == \"ems_ac_active_power_neg\" or r[\"_field\"] == \"ems_ac_active_power\" or r[\"_field\"] == \"ac_current\" or r[\"_field\"] == \"ac_currents_0\" or r[\"_field\"] == \"ac_currents_1\" or r[\"_field\"] == \"ac_currents_2\" or r[\"_field\"] == \"ac_active_powers_0\" or r[\"_field\"] == \"ac_active_powers_1\" or r[\"_field\"] == \"ac_active_powers_2\" or r[\"_field\"] == \"ac_reactive_powers_0\" or r[\"_field\"] == \"ac_reactive_powers_1\" or r[\"_field\"] == \"ac_reactive_powers_2\" or r[\"_field\"] == \"dc_voltage\" or r[\"_field\"] == \"dc_power\" or r[\"_field\"] == \"frequency\" or r[\"_field\"] == \"ac_voltage\" or r[\"_field\"] == \"ac_voltages_0\" or r[\"_field\"] == \"ac_voltages_1\" or r[\"_field\"] == \"ac_voltages_2\")\n    |> aggregateWindow(every: 1m, fn: mean)\n    |> to(bucket: \"ems_mean\")",
  "orgID": "weiheng",
  "status": "active"
}