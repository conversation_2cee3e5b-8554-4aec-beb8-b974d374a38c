ersion: "3.5"
services:
  redis:
    image: redis:5.0.3
    container_name: redis
    privileged: true
    volumes:
      - /docker/redis/conf/redis.conf:/etc/redis/redis.conf
      - /docker/redis/data:/data
    environment:
      - /etc/redis/redis.conf
    ports:
      - 6379:6379
  influxdb:
    image: influxdb:2.1.1
    container_name: influxdb
    privileged: true
    volumes:
      - /docker/influxdb/data:/var/lib/influxdb2
    ports:
      - 8086:8086
  ems:
    image: openjdk:11
    container_name: ems
    privileged: true
    depends_on:
      - redis
      - influxdb
    env_file:
      - /docker/bin/ems.env
    volumes:
      - /docker/dockerFile/ems-server/ems-server-2.jar:/opt/ems-server-2.jar
    entrypoint: ["java","-jar","/opt/ems-server-2.jar"]
    expose:
      - 8808
    ports:
      - 8808:8808
  analysis:
    image: openjdk:11
    container_name: analysis
    privileged: true
    depends_on:
      - redis
      - influxdb
    env_file:
      - /docker/bin/ems.env
    volumes:
      - /docker/dockerFile/data-analysis/data-analysis.jar:/opt/data-analysis.jar
    entrypoint: ["java","-jar","/opt/data-analysis.jar"]
    expose:
      - 8809
    ports:
      - 8809:8809