version: "3.5"
services:
  redis:
    image: redis:5.0.3
    restart: always
    container_name: redis
    privileged: true
    volumes:
      - /docker/redis/conf/redis.conf:/etc/redis/redis.conf
      - /docker/redis/data:/data
    environment:
      - /etc/redis/redis.conf
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
  influxdb:
    image: influxdb:2.1.1
    restart: always
    container_name: influxdb
    privileged: true
    volumes:
      - /docker/influxdb/data:/var/lib/influxdb2
    ports:
      - 8086:8086
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
  mysql:
    image: mysql:5.7
    restart: always
    container_name: mysql
    privileged: true
    environment:
      - MYSQL_ROOT_PASSWORD="123@abcd"
    volumes:
      - /docker/mysql/conf/my.cnf:/etc/mysql/my.cnf
      - /docker/mysql/data:/var/lib/mysql
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
  ems:
    image: registry.cn-shanghai.aliyuncs.com/weiheng-tech/panguback:latest
    restart: always
    container_name: ems
    privileged: true
    depends_on:
      - redis
      - influxdb
      - mysql
    env_file:
      - ./ems.env
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
    expose:
      - 8808
  front:
    image: registry.cn-shanghai.aliyuncs.com/weiheng-tech/pangufront:latest
    restart: always
    container_name: front
    privileged: true
    depends_on:
      - ems
    volumes:
      - /docker/nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf
    ports:
      - 80:80
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
  adminer:
    image: adminer
    container_name: adminer
    ports:
      - 3300:8080
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
