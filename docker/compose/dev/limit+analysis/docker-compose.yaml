version: "3.5"
services:
  redis:
    image: redis:5.0.3
    container_name: redis
    privileged: true
    volumes:
      - /docker/redis/conf/redis.conf:/etc/redis/redis.conf
      - /docker/redis/data:/data
    environment:
      - /etc/redis/redis.conf
    ports:
      - 6379:6379
  influxdb:
    image: influxdb:2.1.1
    container_name: influxdb
    privileged: true
    volumes:
      - /docker/influxdb/data:/var/lib/influxdb2
    ports:
      - 8086:8086
  mysql:
    image: mysql:5.7
    container_name: mysql
    privileged: true
    environment:
      - MYSQL_ROOT_PASSWORD="123@abcd"
    volumes:
      - /docker/mysql/conf/my.cnf:/etc/mysql/my.cnf
      - /docker/mysql/data:/var/lib/mysql
    ports:
      - 3306:3306
  ems:
    image: openjdk:11
    container_name: ems
    privileged: true
    depends_on:
      - redis
      - influxdb
      - mysql
    env_file:
      - /docker/bin/ems.env
    volumes:
      - /docker/dockerFile/ems-server/ems-server-2.jar:/opt/ems-server-2.jar
    entrypoint: ["java","-jar","/opt/ems-server-2.jar"]
    expose:
      - 8808
    ports:
      - 8808:8808
  data-analysis:
    image: openjdk:11
    container_name: data-analysis
    privileged: true
    depends_on:
      - redis
      - influxdb
      - mysql
    env_file:
      - /docker/bin/ems.env
    volumes:
      - /docker/dockerFile/data-analysis/data-analysis.jar:/opt/data-analysis.jar
    entrypoint: ["java","-jar","/opt/data-analysis.jar"]
    expose:
      - 8809
    ports:
      - 8809:8809
  limit-interface:
    image: openjdk:11
    container_name: limit-interface
    privileged: true
    depends_on:
      - redis
      - influxdb
      - mysql
    env_file:
      - /docker/bin/ems.env
    volumes:
      - /docker/dockerFile/limit-interface/ems-limit-interface.jar:/opt/ems-limit-interface.jar
    entrypoint: ["java","-jar","/opt/ems-limit-interface.jar"]
    expose:
      - 8807
    ports:
      - 8807:8807
  nginx:
    image: nginx:1.21.5
    container_name: nginx
    privileged: true
    depends_on:
      - ems
    volumes:
      -  /docker/nginx/conf.d:/etc/nginx/conf.d
      -  /docker/dockerFile/front/dist/:/usr/share/nginx/html/
    ports:
      - 80:80
  adminer:
    image: adminer:4.8.1
    container_name: adminer
    ports:
      - 3300:8080

