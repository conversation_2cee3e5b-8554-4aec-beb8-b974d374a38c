#from java
FROM java:8
#autor
MAINTAINER weiheng-tech
#docker volume
#VOLUME /docker/dockerFile/ems-server:/opt 
#add jar from jekins
#ADD /root/.jenkins/workspace/ems2.0/target/ems-server-0.1-SNAPSHOT.jar ems-server-2.jar
ADD ems-limit-interface.jar /opt/ems-limit-interface.jar
#run jar
RUN bash -c 'touch /opt/ems-limit-interface.jar'
ENTRYPOINT ["java","-jar","/opt/ems-limit-interface.jar"]
#port
EXPOSE 8807
