package com.wifochina.modules.jobinit.jobservice

import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.demand.service.impl.DemandQuartzServiceImpl
import com.wifochina.modules.project.entity.ProjectEntity
import io.github.oshai.kotlinlogging.KotlinLogging
import org.checkerframework.checker.units.qual.m
import org.quartz.CronScheduleBuilder
import org.quartz.CronTrigger
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDataMap
import org.quartz.JobDetail
import org.quartz.ObjectAlreadyExistsException
import org.quartz.Scheduler
import org.quartz.SchedulerException
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.stereotype.Component
import java.lang.reflect.ParameterizedType
import java.util.TimeZone

private val log = KotlinLogging.logger { }

/**
 * Created on 2024/10/31 11:20.
 * <AUTHOR>
 */
@Component
abstract class AbstractDefaultQuartzJobService<T : Job>(
    val scheduler: Scheduler
) : CustomQuartzJobService {

    //如果是要测试 立即执行 对应类里重写 return true即可
    fun runNowForTest(): Boolean {
        return false
    }

    // 通过反射获取 T 的 Class 对象
    @Suppress("UNCHECKED_CAST")
    private fun getJobClass(): Class<T> {
        return (javaClass.genericSuperclass as ParameterizedType).actualTypeArguments[0] as Class<T>
    }

    override fun addTimeZoneJob(timezone: String, runNowTest: Boolean) {
        val jobType = jobType()
        var context = context()
        if (context.runNowTest) {
            context = JobContext("test_${context.jobName}", "test_${context.jobGroupName}", context.runNowTest)
        }
        when (jobType) {
            JobType.TIME_ZONE_JOB -> {
                jobTrigger(
                    JobInnerContext(null, timezone, context.runNowTest), "${context.jobName}-trigger:${timezone}"
                )?.apply {
                    scheduleJob(
                        ScheduleContext(null, timezone),
                        jobDetail("${context.jobName}-job:${timezone}", context.runNowTest) { paramsMap ->
                            paramsMap[EmsConstants.TIME_ZONE_KEY] = timezone
                        },
                        this,
                        JobType.TIME_ZONE_JOB
                    )
                }
            }

            else -> {}
        }
    }

    //直接调用通用的 这个 具体实现哪个子类自己定
    override fun addJob(project: ProjectEntity, runNowTest: Boolean) {
        val jobType = jobType()
        val context = context()
        when (jobType) {
            JobType.PROJECT_JOB -> {
                jobTrigger(
                    JobInnerContext(project, project.timezone, context.runNowTest),
                    "${context.jobName}-trigger:${project.id}"
                )?.apply {
                    scheduleJob(
                        ScheduleContext(project, project.timezone),
                        jobDetail("${context.jobName}-job:${project.id}", false) { paramsMap ->
                            paramsMap[EmsConstants.PROJECT_ID] = project.id
                        },
                        this,
                        JobType.PROJECT_JOB
                    )
                }
            }

            else -> {}
        }

    }

    private fun scheduleJob(
        scheduleContext: ScheduleContext, jobDetail: JobDetail, jobTrigger: Trigger?, jobType: JobType
    ) {
        jobTrigger?.let {
            try {
                scheduler.scheduleJob(jobDetail, jobTrigger)
                if (!scheduler.isStarted) {
                    scheduler.start()
                }
                var cronExpression: String? = null
                if (jobTrigger is CronTrigger) {
                    cronExpression = jobTrigger.cronExpression
                }
                when (jobType) {
                    JobType.PROJECT_JOB -> log.info { "添加${getJobClass()} 成功 projectId:${scheduleContext.project!!.id}, projectName:${scheduleContext.project.projectName}, trigger cron:$cronExpression " }
                    JobType.TIME_ZONE_JOB -> {
                        log.info { "添加${getJobClass()} 成功 timezone:${scheduleContext.timezone},  trigger cron:$cronExpression " }
                    }
                }
            } catch (existsException: ObjectAlreadyExistsException) {
                log.warn { "job exists: ${existsException.message}" }
            } catch (schedulerException: SchedulerException) {
                log.error { "job scheduler jobName ${jobDetail.key} error: ${schedulerException.message}" }
            }

        }
    }


    fun jobDetail(
        wrapJobName: String, testFlag: Boolean, paramsMap: (MutableMap<String, String>) -> Unit
    ): JobDetail {
        val jobData: MutableMap<String, String> = HashMap()
        paramsMap(jobData)
        val jobGroupName = context().jobGroupName
        val jobDataMap = JobDataMap()
        jobDataMap.putAll(jobData)
        return JobBuilder.newJob(getJobClass()).withIdentity(
            if (testFlag) "test_${wrapJobName}" else {
                wrapJobName
            }, if (testFlag) "test_$jobGroupName" else jobGroupName
        ).usingJobData(jobDataMap).storeDurably().build()
    }

    fun jobTrigger(jobInnerContext: JobInnerContext, warpTriggerName: String?): Trigger? {
        cron(jobInnerContext)?.let { cronExpression ->
            val jobGroupName = context().jobGroupName
            return if (jobInnerContext.runNowTest) {
                TriggerBuilder.newTrigger().withIdentity(warpTriggerName, jobGroupName).startNow().build()
            } else {
                TriggerBuilder.newTrigger().withIdentity(
                    warpTriggerName, jobGroupName
                ).withSchedule(
                    CronScheduleBuilder.cronSchedule(cronExpression)
                        .inTimeZone(TimeZone.getTimeZone(jobInnerContext.timezone))
                ).build()
            }
        } ?: run {
            return null
        }
    }

    abstract fun cron(jobInnerContext: JobInnerContext): String?
    abstract fun context(): JobContext
    abstract fun jobType(): JobType;

    data class JobContext(
        val jobName: String, val jobGroupName: String, val runNowTest: Boolean
    )

    data class JobInnerContext(val project: ProjectEntity?, val timezone: String, val runNowTest: Boolean)
    data class ScheduleContext(val project: ProjectEntity?, val timezone: String)
}