package com.wifochina.modules.jobinit

import com.wifochina.modules.jobinit.jobservice.AbstractDefaultQuartzJobService
import com.wifochina.modules.jobinit.jobservice.AbstractTimeZoneDefaultQuartzJobService
import com.wifochina.modules.jobinit.jobservice.CustomQuartzJobService
import com.wifochina.modules.jobinit.jobservice.JobType
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger { }

/**
 * 准备用这个统一 job 的初始化
 * 所有job都实现 AbstractDefaultQuartzJobService
 *  TODO 完善reJob 和 删除job 等功能Jk
 * Created on 2024/10/31 10:35.
 * <AUTHOR>
 */
@Component
class JobInitStart(
    val projectService: ProjectService, private val customQuartzJobServices: List<CustomQuartzJobService>
) {

    @EventListener(ApplicationReadyEvent::class)
    fun init() {

        val customProjectQuartzJobServices =
            customQuartzJobServices.filterIsInstance<AbstractDefaultQuartzJobService<*>>()
                .filter { it.jobType() == JobType.PROJECT_JOB }

        val customTimeZoneQuartzJobServices =
            customQuartzJobServices.filterIsInstance<AbstractDefaultQuartzJobService<*>>()
                .filter { it.jobType() == JobType.TIME_ZONE_JOB }
        //初始化job
        val projects = projectService.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list()
//        projects.forEach { project ->
//            if (project.projectModel == 1) {
//                return@forEach
//            }
//            //开始初始化job
//            customProjectQuartzJobServices.forEach {
//                it.addJob(project, it.runNowForTest())
//            }
//        }

        //对于 时区一个job的
        projects.filter { it.timezone.isNotEmpty() }.map { it.timezone }.distinct().forEach { timezone ->
            customTimeZoneQuartzJobServices.forEach {
                it as AbstractTimeZoneDefaultQuartzJobService<*>
                it.timezoneProjects(timezone)
                it.addTimeZoneJob(timezone, it.runNowForTest())
            }
        }
    }

    val foo = { x: Int -> x + 1 }

}