package com.wifochina.modules.jobinit.jobservice.impl

import com.wifochina.common.util.DemandControlAdjustModelEnum
import com.wifochina.modules.demand.job.DemandCalcMeterJob
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.jobinit.jobservice.AbstractDefaultQuartzJobService
import com.wifochina.modules.jobinit.jobservice.JobType
import org.quartz.Scheduler
import org.springframework.stereotype.Service

/**
 * Created on 2024/10/31 11:18.
 * <AUTHOR>
 */
@Service
class DemandCalcMeterJobService(
    override val scheduler: Scheduler, val groupService: GroupService
) : AbstractDefaultQuartzJobService<DemandCalcMeterJob>(scheduler) {


    //如果不满足业务 则直接返回null 就不会有job 触发
    override fun cron(jobInnerContext: JobInnerContext): String? {
        //如果没有 分组是 auto_meter 就不触发任务
        groupService.queryEnableDemandControl(jobInnerContext.project!!.id)
            .firstOrNull { it.demandControlAdjustModel.equals(DemandControlAdjustModelEnum.auto_meter.name) }?.let {
                //拿到 滑差时间
                val slipTime = it.slipTime
                return if (slipTime == 1) {
                    "0 * * * * ?" // 每分钟执行
                } else {
                    // 1 6 11 16 这样执行 如果改成 0 */1 * * * ? 则是整点 这里不改了以前就是
                    "0 0/$slipTime * * * ?" // 每几分钟执行
                }
            } ?: run {
            return null
        }
    }

    override fun context(): JobContext {
        return JobContext("demand-calc-meter", "demand-group", runNowForTest())
    }

    override fun jobType(): JobType {
        return JobType.PROJECT_JOB
    }

}