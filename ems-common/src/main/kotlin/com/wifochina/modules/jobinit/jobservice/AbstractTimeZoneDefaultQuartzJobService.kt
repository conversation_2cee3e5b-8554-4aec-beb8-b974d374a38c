package com.wifochina.modules.jobinit.jobservice

import com.wifochina.modules.project.entity.ProjectEntity
import org.quartz.Job
import org.quartz.Scheduler

/**
 * Created on 2025/3/18 14:58.
 * <AUTHOR>
 */
abstract class AbstractTimeZoneDefaultQuartzJobService<T : Job>(
    override val scheduler: Scheduler
) : AbstractDefaultQuartzJobService<T>(scheduler) {

    abstract fun timezoneProjects(timezone: String)
}