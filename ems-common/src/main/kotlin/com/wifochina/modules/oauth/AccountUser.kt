package com.wifochina.modules.oauth

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * Created on 2024/11/5 11:19.
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class AccountUser(
    val id: String?,
    val panguId: String?,
    val email: String?,
    val type: Int,
    val name: String?,
    val nickName: String?,
    val phone: String?,
    val phoneLocationCode: String?,
    val countryOrRegionName: String?,
    val countryOrRegionCode: String?,
    val region: String?,
    val address: String?,
    val lastSignInTime: Long?,
    val status: Int?,
    val source: Int?,
    val currentRole: Role?,
    val roleList: List<Role>?,
    val token: Token?,
    val isManager: String?,
    val ct: Long?
)

data class Role(
    val userId: String?,
    val appID: String?,
    val authStat: Int?,
    val roleId: String?,
    val roleCategory: String?,
    val roleName: String?,
    val allResourceStat: Int?,
    val policyAllowObjs: List<PolicyAllowObj>?,
    val policyAllowed: List<PolicyAllowObj>?
)

data class PolicyAllowObj(
    val id: String,
    val type: Int,
    val name: String,
    val picMark: String,
    val routers: String,
    val urlMarks: String,
    val orderBy: Int,
    val children: List<PolicyAllowObj>?
)

data class Token(
    val sessionId: String, val expiredAt: Long,
    val remainAttempts: Int?
)


