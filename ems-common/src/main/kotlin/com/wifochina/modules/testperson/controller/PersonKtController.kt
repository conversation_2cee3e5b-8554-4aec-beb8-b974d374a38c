package com.wifochina.modules.testperson.controller

import com.wifochina.modules.testperson.converter.PersonEntity
import com.wifochina.modules.testperson.converter.PersonKtConverter
import com.wifochina.modules.testperson.search.ProductTypeSearch
import io.github.oshai.kotlinlogging.KotlinLogging
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

private val log = KotlinLogging.logger {}

/**
 * Created on 2024/10/29 15:58.
 * <AUTHOR>
 */
@Api(tags = ["test-kotlin-integration"])
@RestController
@RequestMapping("/productType")
class PersonKtController {
    /**
     * 带条件查询的 分页接口
     *
     * @param productTypeSearch : 查询条件
     * @param pageVo : 分页条件
     * @return : ResultVo<Page></Page><ProductTypeVo>>
    </ProductTypeVo> */
    @GetMapping("/listByCondition")
    @ApiOperation(value = "根据条件查询 (分页)产品")
    fun listByCondition(
        productTypeSearch: ProductTypeSearch
    ): Result<String?> {
        log.info { "hello test kotlin ${productTypeSearch.productTypeName}" }
        val personEntity = PersonEntity("johnny", 23)
        val personVo = PersonKtConverter.INSTANCE.domain2Vo(personEntity)
        log.info { "personVo $personVo" }
        return Result.success(productTypeSearch.productTypeName);
    }
}