package com.wifochina.modules.testperson.search

import com.wifochina.common.NoArg
import com.wifochina.modules.BaseEntity
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.io.Serializable

/**
 *
 *
 * <pre>
 * ProductTypeSearch 查询对象
</pre> *
 *
 * <AUTHOR>
 * @date 2023-11-20 13:35:39
 */
@ApiModel(value = "", description = "")
@NoArg
data class ProductTypeSearch(
    @ApiModelProperty(value = "产品分类名称")
    var productTypeName: String? = null,
    @ApiModelProperty(value = "父id")
    var parentId: Long? = null,
    @ApiModelProperty(value = "层级")
    var level: Int?
) : BaseEntity(), Serializable
