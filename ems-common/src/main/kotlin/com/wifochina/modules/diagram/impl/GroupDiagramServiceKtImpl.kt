package com.wifochina.modules.diagram.impl

import com.wifochina.common.influx.DivisionConverter
import com.wifochina.common.influx.FluxAdapter
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.client.InfluxClientService
import com.wifochina.modules.common.search.PeriodUtils
import com.wifochina.modules.diagram.GroupDiagramServiceKt
import com.wifochina.modules.diagram.VO.ValueVO
import com.wifochina.modules.diagram.request.RequestWithGroupId
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.time.temporal.ChronoUnit


private val log = KotlinLogging.logger { }

@Service
class GroupDiagramServiceKtImpl(
    val divisionConverter: DivisionConverter,
    val influxClientService: InfluxClientService
) : GroupDiagramServiceKt {
    override fun getGroupRate(
        requestWithGroupId: RequestWithGroupId, bucket: String, fieds: List<String>
    ): Map<String, List<ValueVO>>? {

        val period = PeriodUtils.period(requestWithGroupId.startDate, requestWithGroupId.endDate, null, bucket)

        val toMap = FluxAdapter.query(
            FluxAdapter.builder().projectId(requestWithGroupId.projectId).from(influxClientService.bucketGroup)
                .range(requestWithGroupId.startDate, requestWithGroupId.endDate)
                .measurement(influxClientService.getEmsGroupTable(requestWithGroupId.projectId))
                .groupId(requestWithGroupId.groupId).fields(fieds).toFlux()
                .aggregateWindow(period, ChronoUnit.MINUTES, EmsConstants.INFLUX_MEAN_FUNC).toString()
        )
            //这里可能要提出去 目前使用的 几个group的属性 都需要 / 1000
            .unitConverter(divisionConverter)
            .handleResult().toMap()
        log.info { toMap }
        if (toMap.isEmpty()) {
            return null
        }
        return toMap
    }
}