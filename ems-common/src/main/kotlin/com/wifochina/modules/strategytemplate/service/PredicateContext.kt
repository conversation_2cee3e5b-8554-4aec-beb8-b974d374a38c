package com.wifochina.modules.strategytemplate.service

import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemControlEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity

/**
 * Created on 2025/4/29 15:37.
 * <AUTHOR>
 */
data class PredicateContext(
    val project: ProjectEntity,
    var group: GroupEntity?,
    val date: Long?,
    val templatePreviewContext: TemplatePreviewContext? = null
)