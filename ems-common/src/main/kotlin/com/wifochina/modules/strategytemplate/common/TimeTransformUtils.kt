package com.wifochina.modules.strategytemplate.common

import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

/**
 * Created on 2025/5/30 11:04.
 * <AUTHOR>
 */
class TimeTransformUtils {
    companion object {
        fun transformTimeUnix(localTime: LocalTime, localDate: LocalDate, zoneId: ZoneId): Long {
            return localTime.atDate(localDate).atZone(zoneId)
                .toEpochSecond()
        }

    }



}