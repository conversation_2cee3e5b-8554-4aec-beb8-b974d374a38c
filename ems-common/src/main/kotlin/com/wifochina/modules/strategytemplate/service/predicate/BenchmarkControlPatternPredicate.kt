package com.wifochina.modules.strategytemplate.service.predicate

import com.wifochina.modules.strategytemplate.enums.ConditionEnums
import com.wifochina.modules.strategytemplate.enums.ControlPatternLabelEnums
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }

/**
 * 定值 不关心 冲放 靠设置者
 * Created on 2025/5/8 15:41.
 * <AUTHOR>
 */
@Order(1)
@Service
class BenchmarkControlPatternPredicate : ControlPatternPredicate {
    override fun patternPredicate(context: ControlPatternPredicate.ControlPatternPredicateContext): PredicateResult {
        val result = PredicateResult()
        val control = context.control
        val strategy = context.strategyContext.strategy
        val strategyPeriodElectricPrices = context.strategyContext.strategyPeriodElectricPrices

        return if (control.priceBenchmarkController == true) {
            val priceBenchmark = control.priceBenchmark ?: run {
                log.info { "open priceBenchmarkController but not  set priceBenchmark is null for strategyEntity ${strategy.id}, control:$control" }
                return result.apply { skip = true }
            }
            result.apply {
                matchResult = control.priceBenchmarkCondition?.let { condition ->
                    val predicate: (Double, Double) -> Boolean = when (ConditionEnums.valueOf(condition)) {
                        ConditionEnums.EQUAL -> { a, b -> a == b }
                        ConditionEnums.NOT_EQUAL -> { a, b -> a != b }
                        ConditionEnums.GT -> { a, b -> a > b }
                        ConditionEnums.GTE -> { a, b -> a >= b }
                        ConditionEnums.LT -> { a, b -> a < b }
                        ConditionEnums.LTE -> { a, b -> a <= b }
                    }
                    strategyPeriodElectricPrices.filter { price -> predicate(price.average, priceBenchmark) }
                } ?: listOf()
                log.info { "price benchmark match prices ${strategy.startTime}-${strategy.endTime}: $matchResult" }
            }
        } else {
            result
        }
    }

    override fun patternLabel(): ControlPatternLabelEnums {
        return ControlPatternLabelEnums.BENCHMARK_VALUE
    }


}