package com.wifochina.modules.strategytemplate.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.wifochina.modules.BaseEntity
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 * 为了实现产品所谓的月度回显策略 而记录绑定关系
 * 毫无用处
 * Created on 2025/4/23 10:44.
 * <AUTHOR>
 */

@TableName("t_strategy_month_template_bind")
@ApiModel(value = "StrategyMonthTemplateBindEntity对象", description = "StrategyMonthTemplateBindEntity对象")
class StrategyMonthTemplateBindEntity : BaseEntity() {

    companion object {
        private const val serialVersionUID: Long = 1L
    }

    @TableId(value = "id", type = IdType.AUTO)
    var id: Int? = null

    @ApiModelProperty(value = "月份")
    var month: Int? = null

    @ApiModelProperty(value = "模版id")
    var templateId: Long? = null

    @ApiModelProperty(value = "项目Id")
    var projectId: String? = null

    @ApiModelProperty(value = "分组Id")
    var groupId: String? = null

}