package com.wifochina.modules.strategytemplate.service.predicate

import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity
import com.wifochina.modules.strategytemplate.enums.ControlPatternLabelEnums

/**
 * Created on 2025/5/8 14:47.
 * <AUTHOR>
 */
interface ControlPatternPredicate {

    fun patternPredicate(context: ControlPatternPredicateContext): PredicateResult
    fun patternLabel(): ControlPatternLabelEnums

    data class ControlPatternPredicateContext(
        var strategyContext: StrategyPredicateContext,
        var control: StrategyControlEntity,
    )


}