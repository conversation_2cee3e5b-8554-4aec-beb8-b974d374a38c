package com.wifochina.modules.strategytemplate.service.predicate

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.wifochina.modules.group.request.go.YearlyStrategy
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategytemplate.common.StrategyTransform
import com.wifochina.modules.strategytemplate.enums.ModelStrategyEnums
import com.wifochina.modules.strategytemplate.service.PredicateContext
import com.wifochina.modules.strategytemplate.service.PredicateModel
import com.wifochina.modules.strategytemplate.service.StrategyServiceKt
import org.springframework.stereotype.Component

/**
 * 简单的 predicate 只是把时间转换一下
 * Created on 2025/4/29 14:34.
 * <AUTHOR>
 */

@Component
class SimplePredicate(
    val strategyServiceKt: StrategyServiceKt,
    val strategyTransform: StrategyTransform
) : PredicateModel {
    override fun model(): ModelStrategyEnums {
        return ModelStrategyEnums.SIMPLE
    }

    override fun predicate(context: PredicateContext): Map<String, List<YearlyStrategy>> {
        //如果是国内的直接查询
        //先实现 国内的
        val list = strategyServiceKt.list(
            LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getProjectId, context.project.id)
                .eq(StrategyEntity::getGroupId, context.group!!.id).isNull(StrategyEntity::getWeekDay)
        )
        val strategyDateMap = list.groupBy { it.strategyDate }
        val yearlyStrategyMap = sortDates(list.map { it.strategyDate }).associateWith {
            val strategyEntities = strategyDateMap[it]!!
            //得到每天的 yearlyStrategyMap
            strategyTransform.transform(strategyEntities) {
                this.project = context.project
                this.group = context.group
            }
        }
        return yearlyStrategyMap
    }

    private fun sortDates(dates: List<String>): List<String> {
        return dates.sortedWith(
            compareBy(
                { it.substring(0, 2).toInt() }, // 月份
                { it.substring(3, 5).toInt() }  // 日
            ))
    }
}