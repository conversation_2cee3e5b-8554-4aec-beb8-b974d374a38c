package com.wifochina.modules.strategytemplate.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.fasterxml.jackson.annotation.JsonFormat
import com.wifochina.modules.BaseEntity
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.LocalTime

/**
 *
 * new strategy entity
 * Created on 2025/4/15 16:04.
 * <AUTHOR>
 */
//@TableName("strategy_details_entity")
@ApiModel(value = "StrategyDetailsEntity对象", description = "StrategyDetailsEntity对象")
class StrategyDetailsEntity : BaseEntity() {

    @TableId(value = "id", type = IdType.AUTO)
    var id: Int? = null

    @ApiModelProperty(value = "策略类型  0充电 1放电 2自发自用")
    var type: Int? = null

    @ApiModelProperty(value = "策略日期 如果是模版就为空")
    var date: String? = null

    @ApiModelProperty(value = "templateId 如果不为空则属于template定义的策略Detail")
    var templateId: Int? = null

    @ApiModelProperty(value = "功率")
    var power: Int? = null

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    var startTime: LocalTime? = null

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    var endTime: LocalTime? = null

    @ApiModelProperty(value = "动态电价策略类型 1最大差价 2电价定值 3 定时充放 4全自动模式")
    var strategyType: Int? = null
}