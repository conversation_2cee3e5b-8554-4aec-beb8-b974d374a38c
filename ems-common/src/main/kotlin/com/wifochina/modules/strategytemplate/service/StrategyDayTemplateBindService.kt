package com.wifochina.modules.strategytemplate.service

import com.baomidou.mybatisplus.extension.service.IService
import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.request.StrategyImportWithDaysRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateBindRequest
import com.wifochina.modules.strategytemplate.vo.StrategyDayTemplateBindInfoVo

/**
 * Created on 2025/4/24 10:32.
 * <AUTHOR>
 */
interface StrategyDayTemplateBindService : IService<StrategyDayTemplateBindEntity> {
    fun getBindInfo(strategyTemplateBindRequest: StrategyTemplateBindRequest): List<StrategyDayTemplateBindInfoVo>

    fun removeByMonth(projectId: String, groupId: String, month: Int)
    fun removeByDays(projectId: String, groupId: String, days: List<Int>)
    fun importStrategyWithDays(request: StrategyImportWithDaysRequest)


}