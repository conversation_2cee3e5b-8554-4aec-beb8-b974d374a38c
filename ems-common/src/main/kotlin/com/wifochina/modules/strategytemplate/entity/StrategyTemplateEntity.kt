package com.wifochina.modules.strategytemplate.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.fasterxml.jackson.annotation.JsonFormat
import com.wifochina.modules.BaseEntity
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.LocalTime

/**
 * Created on 2025/4/15 15:55.
 * <AUTHOR>
 */
@TableName("t_strategy_template")
@ApiModel(value = "StrategyTemplateEntity对象", description = "StrategyTemplateEntity对象")
open class StrategyTemplateEntity : BaseEntity() {
    companion object {
        private const val serialVersionUID: Long = 1L
    }

    @TableId(value = "id", type = IdType.AUTO)
    var id: Long? = null

    @ApiModelProperty(value = "模版名称")
    var templateName: String? = null

    @ApiModelProperty(value = "项目Id")
    var projectId: String? = null

    @ApiModelProperty(value = "海外用的 模式(自定义重放模式or电价全自动模式or...other)")
    var strategyType: Int? = null

}