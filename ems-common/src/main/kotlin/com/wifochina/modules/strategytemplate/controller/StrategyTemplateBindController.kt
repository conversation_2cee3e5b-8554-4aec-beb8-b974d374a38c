package com.wifochina.modules.strategytemplate.controller

import com.wifochina.common.log.Log
import com.wifochina.common.page.Result
import com.wifochina.modules.log.OperationType
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.strategytemplate.request.StrategyImportWithDaysRequest
import com.wifochina.modules.strategytemplate.request.StrategyImportWithMonthRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateBindRequest
import com.wifochina.modules.strategytemplate.service.StrategyDayTemplateBindService
import com.wifochina.modules.strategytemplate.service.StrategyMonthTemplateBindService
import com.wifochina.modules.strategytemplate.vo.StrategyDayTemplateBindInfoVo
import com.wifochina.modules.strategytemplate.vo.StrategyMonthTemplateBindInfoVo
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Created on 2025/4/24 10:33.
 * <AUTHOR>
 */
@RequestMapping("/strategyTemplateBind")
@RestController
@Api(tags = ["策略配置按日月bind记录"])
class StrategyTemplateBindController(
    val strategyDayTemplateBindService: StrategyDayTemplateBindService,
    val strategyMonthTemplateBindService: StrategyMonthTemplateBindService
) {

    @PostMapping("/getDayBindInfo")
    @ApiOperation("策略day模版绑定记录")
    fun getDayBindInfo(@RequestBody strategyTemplateBindRequest: StrategyTemplateBindRequest): Result<List<StrategyDayTemplateBindInfoVo>> {
        return Result.success(strategyDayTemplateBindService.getBindInfo(strategyTemplateBindRequest))
    }

    @PostMapping("/getMonthBindInfo")
    @ApiOperation("策略month模版绑定记录")
    fun getMonthBindInfo(@RequestBody strategyTemplateBindRequest: StrategyTemplateBindRequest): Result<List<StrategyMonthTemplateBindInfoVo>> {
        return Result.success(strategyMonthTemplateBindService.getBindInfo(strategyTemplateBindRequest))
    }

    /**
     * 导入策略 按照月 */
    @PostMapping("/importStrategyWithMonth")
    @ApiOperation("策略导入按月")
    @Log(module = "STRATEGY_TEMPLATE", methods = "STRATEGY_TEMPLATE_IMPORT_MONTH", type = OperationType.ADD)
    fun importStrategyWithMonth(@RequestBody request: StrategyImportWithMonthRequest): Result<Unit> {
        request.projectId = WebUtils.projectId.get()
        request.bindInfos.forEach {
            it.projectId = request.projectId
            it.groupId = request.groupId
        }
        strategyMonthTemplateBindService.importStrategyWithMonth(request)
        return Result.success()
    }

    @PostMapping("/importStrategyWithDays")
    @ApiOperation("策略导入按月")
    @Log(module = "STRATEGY_TEMPLATE", methods = "STRATEGY_TEMPLATE_IMPORT_DAYS", type = OperationType.ADD)
    fun importStrategyWithDays(@RequestBody request: StrategyImportWithDaysRequest): Result<Unit> {
        request.projectId = WebUtils.projectId.get()
        request.bindInfos.forEach {
            it.projectId = request.projectId
            it.groupId = request.groupId
            it.month = request.month
        }
        strategyDayTemplateBindService.importStrategyWithDays(request)
        return Result.success()
    }


}