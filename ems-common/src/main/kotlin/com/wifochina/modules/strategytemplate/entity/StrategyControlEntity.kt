package com.wifochina.modules.strategytemplate.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.wifochina.modules.BaseEntity
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 * Created on 2025/4/21 15:33.
 * <AUTHOR>
 */
@TableName("t_strategy_control")
@ApiModel(value = "StrategyControlEntity对象", description = "StrategyControlEntity对象")
open class StrategyControlEntity : BaseEntity() {
    companion object {
        private const val serialVersionUID: Long = 1L
    }

    @TableId(value = "id", type = IdType.AUTO)
    var id: Long? = null

    var projectId: String? = null
    var groupId: String? = null

    // 1.4.3 版本开始 兼容了 模版的详情  所以这里添加了 模版id 用于绑定到模版的详细策略 , 有这个id的就属于模版的
    //    @ApiModelProperty(value = "电价模版id 可以为空")
    //    private Integer templateId;
    @ApiModelProperty(value = "策略日期 如果是模版就为空, 用于替代week的, 形式如2025-03-02")
    var strategyDate: String? = null

    //--------差额------
    @ApiModelProperty(value = "电价差额/kWh 开关")
    var priceDifferenceController: Boolean? = false

    @ApiModelProperty(value = "电价差额/kWh")
    var priceDifference: Double? = null


    //--------定值------
    @ApiModelProperty(value = "电价定值 开关")
    var priceBenchmarkController: Boolean? = false

    @ApiModelProperty(value = "电价定值条件")
    var priceBenchmarkCondition: String? = null

    @ApiModelProperty(value = "电价定值")
    var priceBenchmark: Double? = null


    //--------最值------
    @ApiModelProperty(value = "电价最值开关")
    var priceBaseValueController: Boolean? = null

    @ApiModelProperty(value = "电价最值时间范围")
    var priceBaseValueRangeCondition: String? = null

    @ApiModelProperty(value = "电价最值段数")
    var priceBaseValueSectionCount: Int? = null


    //--------连续时长------
    @ApiModelProperty(value = "连续时长开关")
    var priceContinueDurationController: Boolean? = null

    @ApiModelProperty(value = "连续时长条件")
    var priceContinueDurationCondition: String? = null

    //    @ApiModelProperty(value = "连续小时数")
    @ApiModelProperty(value = "连续时间段数")
    var priceContinueDurationSegment: Int? = null


}