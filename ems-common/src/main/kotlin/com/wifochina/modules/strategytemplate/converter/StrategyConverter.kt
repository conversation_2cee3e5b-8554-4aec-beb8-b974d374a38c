package com.wifochina.modules.strategytemplate.converter

import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity
import com.wifochina.modules.strategytemplate.request.StrategyTemplateItemRequest
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

/**
 * Created on 2024/10/29 16:23.
 * <AUTHOR>
 */
@Mapper
interface StrategyConverter {

    fun copy(strategyEntity: StrategyEntity): StrategyEntity
    fun strategyTemplateRequestItem2Entity(strategyTemplateItemRequest: StrategyTemplateItemRequest): StrategyEntity

    fun strategyTemplateItem2Entity(strategyTemplateItemEntity: StrategyTemplateItemEntity): StrategyEntity

    companion object {
        val INSTANCE: StrategyConverter = Mappers.getMapper(StrategyConverter::class.java)
    }
}