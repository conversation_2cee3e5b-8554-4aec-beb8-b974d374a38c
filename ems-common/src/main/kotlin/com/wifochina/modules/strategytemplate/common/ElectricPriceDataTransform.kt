package com.wifochina.modules.strategytemplate.common

import com.wifochina.common.constants.ElectricPriceSpanEnum
import com.wifochina.common.time.MyTimeUtil
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.electric.vo.ElectricPriceSystemData
import com.wifochina.modules.group.request.go.YearlyStrategy
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalQueries.localDate

/**
 * transform 用来转换 比如 实时电价对象Data ->  YearlyStrategy
 * Created on 2025/5/9 11:11.
 * <AUTHOR>
 */
@Service
class ElectricPriceDataTransform : TransformYearlyStrategy<ElectricPriceSystemData.Data> {

    override fun transform(
        data: List<ElectricPriceSystemData.Data>,
        context: TransformYearlyStrategy.TransformContext.() -> Unit
    ): List<YearlyStrategy> {
        val ctx = TransformYearlyStrategy.TransformContext().apply(context)
        ctx.strategy.startTime
        ctx.strategy.endTime
        val zero = MyTimeUtil.getZonedStartOfDayEpoch(ctx.strategyDateStr, ctx.project.timezone)
        val zoneId = ZoneId.of(ctx.project.timezone)

        val instant = Instant.ofEpochSecond(data[0].startTimeUnix.toLong())
        val localDate = instant.atZone(zoneId).toLocalDate()
        val strategyStartUnix = ctx.strategy.startTime
            .atDate(localDate)
            .atZone(zoneId)
            .toEpochSecond()
        val strategyEndUnix = ctx.strategy.endTime
            .atDate(localDate)
            .atZone(zoneId)
            .toEpochSecond()
        return data.map { priceData ->
            var minEndFixedFlag = false
            if (zero == priceData.startTimeUnix.toLong()) {
                minEndFixedFlag = true
            }
            val yearlyStrategy = YearlyStrategy()
            // 1.4.4 优化了一下 关于 临界点的 开始时间选取 最大的那个
            val finalStart = maxOf(strategyStartUnix, priceData.startTimeUnix.toLong())
            yearlyStrategy.start_minute = MyTimeUtil.getMinutesInDayNew(
                finalStart, ctx.project.timezone,
                minEndFixedFlag
            )

            val endTimeUnix: Int =
                (priceData.startTimeUnix + ElectricPriceSpanEnum.getValue(ctx.project.electricPriceSpan))
            // 1.4.4 优化了一下 关于 临界点的 结束时间选取 最小的那个 更精准
            val finalEnd = maxOf(strategyEndUnix, endTimeUnix.toLong())
            yearlyStrategy.end_minute = MyTimeUtil.getMinutesInDayNew(
                finalEnd, ctx.project.timezone, minEndFixedFlag
            )
            yearlyStrategy.function = ctx.strategy.type
            yearlyStrategy.power = ctx.strategy.power
            if (ctx.group?.enableStopSoc == true) {
                yearlyStrategy.soc = ctx.strategy.soc?.toInt()
            }
            yearlyStrategy
        }.sortedBy { yearlyStrategy -> yearlyStrategy.start_minute }

    }
}