package com.wifochina.modules.strategytemplate.common

import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.go.YearlyStrategy
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.strategy.entity.StrategyEntity

/**
 * Created on 2025/5/9 11:07.
 * <AUTHOR>
 */
interface TransformYearlyStrategy<T : YearlyStrategyTransformAble> {
    fun transform(data: List<T>, context: TransformContext.() -> Unit): List<YearlyStrategy>

    class TransformContext {
        lateinit var project: ProjectEntity
        var group: GroupEntity? = null
        lateinit var strategy: StrategyEntity
        lateinit var strategyDateStr: String
    }

}