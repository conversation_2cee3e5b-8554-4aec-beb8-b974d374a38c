package com.wifochina.modules.strategytemplate.converter

import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity
import com.wifochina.modules.strategytemplate.request.StrategyTemplateRequest
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateVo
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

/**
 * Created on 2024/10/29 16:23.
 * <AUTHOR>
 */
@Mapper
interface StrategyTemplateConverter {
    fun request2Entity(templateRequest: StrategyTemplateRequest): StrategyTemplateEntity

    fun entity2Vo(strategyTemplateEntity: StrategyTemplateEntity): StrategyTemplateVo

    companion object {
        val INSTANCE: StrategyTemplateConverter = Mappers.getMapper(StrategyTemplateConverter::class.java)
    }
}