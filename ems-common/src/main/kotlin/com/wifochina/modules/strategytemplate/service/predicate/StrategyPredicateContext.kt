package com.wifochina.modules.strategytemplate.service.predicate

import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.electric.vo.ElectricPriceSystemData
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategytemplate.service.PredicateContext
import java.time.LocalDate
import java.time.ZoneId

/**
 * Created on 2025/5/9 11:25.
 * <AUTHOR>
 */
data class StrategyPredicateContext(
    var predicateContext: PredicateContext,
    var strategy: StrategyEntity,
    var allDayElectricPrices: List<ElectricPriceSystemData.Data>,
) {
    var strategyStartTimeUnix: Long = 0
    var strategyEndTimeUnix: Long = 0
    lateinit var strategyPeriodElectricPrices: List<ElectricPriceSystemData.Data>

    fun calStrategyPeriodElectricPrices(
        strategyStartTimeUnix: Long, strategyEndTimeUnix: Long
    ): StrategyPredicateContext {

        strategyPeriodElectricPrices = allDayElectricPrices.filter {
            val priceEndTime = it.startTimeUnix.toLong() + it.intervalSeconds
            it.startTimeUnix < strategyEndTimeUnix && priceEndTime > strategyStartTimeUnix
        }
        this.strategyStartTimeUnix = strategyStartTimeUnix
        this.strategyEndTimeUnix = strategyEndTimeUnix
        return this
    }
}
