package com.wifochina.modules.strategytemplate.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.wifochina.modules.strategytemplate.converter.StrategyControlConverter
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateItemControlConverter
import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity
import com.wifochina.modules.strategytemplate.mapper.StrategyControlMapper
import com.wifochina.modules.strategytemplate.mapper.StrategyTemplateItemControlMapper
import com.wifochina.modules.strategytemplate.service.StrategyControlService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemControlService
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateItemControlVo
import org.springframework.stereotype.Service

/**
 * Created on 2025/4/21 17:01.
 * <AUTHOR>
 */

@Service
class StrategyControlServiceImpl : ServiceImpl<StrategyControlMapper, StrategyControlEntity>(),
    StrategyControlService {


    override fun selectControlByIds(ids: List<Long>): Map<Long, StrategyControlEntity> {
        return takeIf { ids.isNotEmpty() }
            ?.let {
                this.listByIds(ids)
                    .associateBy {
                        it.id!!
                    }
            } ?: mapOf()
    }
}