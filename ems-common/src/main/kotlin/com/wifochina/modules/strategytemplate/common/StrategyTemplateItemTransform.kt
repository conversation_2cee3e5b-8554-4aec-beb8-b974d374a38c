package com.wifochina.modules.strategytemplate.common

import com.wifochina.modules.group.request.go.YearlyStrategy
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity
import org.springframework.stereotype.Service

/**
 * Created on 2025/5/9 11:12.
 * <AUTHOR>
 */
@Service
class StrategyTemplateItemTransform : TransformYearlyStrategy<StrategyTemplateItemEntity> {
    override fun transform(
        data: List<StrategyTemplateItemEntity>,
        context: TransformYearlyStrategy.TransformContext.() -> Unit
    ): List<YearlyStrategy> {
        val ctx = TransformYearlyStrategy.TransformContext().apply(context)
        return data.map { strategy ->
            val yearlyStrategy = YearlyStrategy()
            yearlyStrategy.start_minute = (strategy.startTime!!.hour * 60 + strategy.startTime!!.minute)
            if (strategy.endTime!!.hour == 0 && strategy.endTime!!.minute == 0) {
                yearlyStrategy.end_minute = 1440
            } else {
                yearlyStrategy.end_minute = strategy.endTime!!.hour * 60 + strategy.endTime!!.minute
            }
            yearlyStrategy.function = strategy.type
            yearlyStrategy.power = strategy.power!!.toInt()
            if (ctx.group?.enableStopSoc == true) {
                yearlyStrategy.soc = strategy.soc?.toInt()
            }
            yearlyStrategy
        }.sortedBy { yearlyStrategy -> yearlyStrategy.start_minute }
    }
}