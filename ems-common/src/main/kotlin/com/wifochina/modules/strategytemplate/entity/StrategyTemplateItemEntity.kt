package com.wifochina.modules.strategytemplate.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.fasterxml.jackson.annotation.JsonFormat
import com.wifochina.modules.BaseEntity
import com.wifochina.modules.strategytemplate.common.YearlyStrategyTransformAble
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

fun LocalTime.transformTimeUnix(localDate: LocalDate, zoneId: ZoneId): Long {
    return this.atDate(localDate)
        .atZone(zoneId)
        .toEpochSecond()
}

/**
 * Created on 2025/4/18 14:43.
 * <AUTHOR>
 */
@TableName("t_strategy_template_item")
@ApiModel(value = "StrategyTemplateItemEntity对象", description = "StrategyTemplateItemEntity对象")
open class StrategyTemplateItemEntity : BaseEntity(), YearlyStrategyTransformAble {
    companion object {
        private const val serialVersionUID: Long = 1L
    }

    @TableId(value = "id", type = IdType.AUTO)
    var id: Long? = null

    @ApiModelProperty(value = "项目Id")
    var projectId: String? = null

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    var startTime: LocalTime? = null

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    var endTime: LocalTime? = null

    @ApiModelProperty(value = "模版Id")
    var templateId: Long? = null

    @ApiModelProperty(value = "power")
    var power: Double? = null

    @ApiModelProperty(value = "soc")
    var soc: Double? = null

    @ApiModelProperty(value = "type")
    var type: Int? = null

    var itemControlId: Long? = null


}