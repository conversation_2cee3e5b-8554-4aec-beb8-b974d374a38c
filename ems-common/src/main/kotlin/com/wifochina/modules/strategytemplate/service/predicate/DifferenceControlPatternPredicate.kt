package com.wifochina.modules.strategytemplate.service.predicate

import com.wifochina.modules.strategytemplate.enums.ControlPatternLabelEnums
import com.wifochina.modules.strategytemplate.enums.StrategyChargeDischargeTypeEnums
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Service


private val log = KotlinLogging.logger { }

/** * Created on 2025/5/8 15:13.
 * <AUTHOR>
 */
@Order(1)
@Service
class DifferenceControlPatternPredicate : ControlPatternPredicate {
    override fun patternPredicate(context: ControlPatternPredicate.ControlPatternPredicateContext): PredicateResult {

        val result = PredicateResult()
        val control = context.control
        val strategy = context.strategyContext.strategy
        val strategyPeriodElectricPrices = context.strategyContext.strategyPeriodElectricPrices
        return if (control.priceDifferenceController == true) {
            //如果开启了 差值开关
            //得到定价差值
            val priceDifference = control.priceDifference ?: run {
                log.info { "open priceDifferenceController but not  set priceDifference is null for strategyEntity ${strategy.id}, control:$control" }
                return result.apply { skip = true }
            }
            result.apply {
                matchResult = when (StrategyChargeDischargeTypeEnums.valueOfByType(strategy.type)) {
                    StrategyChargeDischargeTypeEnums.CHARGE -> {
                        val minValue = strategyPeriodElectricPrices.minOfOrNull { it.average } ?: Double.NaN
                        strategyPeriodElectricPrices.filter {
                            it.average in minValue..minValue + priceDifference
                        }
                    }

                    StrategyChargeDischargeTypeEnums.DISCHARGE -> {
                        val maxValue = strategyPeriodElectricPrices.maxOfOrNull { it.average } ?: Double.NaN
                        strategyPeriodElectricPrices.filter {
                            it.average in maxValue - priceDifference..maxValue
                        }
                    }
                }
                log.info { "price difference match prices ${strategy.startTime}-${strategy.endTime}: $matchResult" }
            }
        } else {
            result
        }

    }

    override fun patternLabel(): ControlPatternLabelEnums {
        return ControlPatternLabelEnums.DIFFERENCE_VALUE
    }

}