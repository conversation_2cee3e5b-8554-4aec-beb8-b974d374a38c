package com.wifochina.modules.strategytemplate.service.impl

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.mdc.MdcKotlinContext
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategy.service.StrategyService
import com.wifochina.modules.strategytemplate.common.StrategyCommonUtils
import com.wifochina.modules.strategytemplate.converter.StrategyDayTemplateBindConverter
import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyMonthTemplateBindEntity
import com.wifochina.modules.strategytemplate.mapper.StrategyDayTemplateBindMapper
import com.wifochina.modules.strategytemplate.request.StrategyImportWithDaysRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateBindRequest
import com.wifochina.modules.strategytemplate.service.StrategyDayTemplateBindService
import com.wifochina.modules.strategytemplate.service.StrategyServiceKt
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateService
import com.wifochina.modules.strategytemplate.vo.StrategyDayTemplateBindInfoVo
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * Created on 2025/4/24 10:32.
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = [Exception::class])
class StrategyDayTemplateBindServiceImpl(
    val strategyService: StrategyService,
    val strategyServiceKt: StrategyServiceKt,
    val strategyTemplateService: StrategyTemplateService,
    val strategyTemplateItemService: StrategyTemplateItemService,
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
) : ServiceImpl<StrategyDayTemplateBindMapper, StrategyDayTemplateBindEntity>(), StrategyDayTemplateBindService {

    override fun getBindInfo(strategyTemplateBindRequest: StrategyTemplateBindRequest): List<StrategyDayTemplateBindInfoVo> {
        takeIf { strategyTemplateBindRequest.month != null }?.let {
            // 根据月份去 得到具体月份的 day 的 记录
            val result = mutableListOf<StrategyDayTemplateBindInfoVo>()
            return this.list(
                KtQueryWrapper(StrategyDayTemplateBindEntity::class.java).eq(
                    StrategyDayTemplateBindEntity::projectId, strategyTemplateBindRequest.projectId
                ).eq(StrategyDayTemplateBindEntity::groupId, strategyTemplateBindRequest.groupId)
                    .eq(StrategyDayTemplateBindEntity::month, strategyTemplateBindRequest.month)
            ).let { dayTemplateBindInfos ->
                val dayBindInfoMap = dayTemplateBindInfos.associateBy { dayBindInfo -> dayBindInfo.day!! }
                val templateMap = strategyTemplateService.templateMap(dayBindInfoMap.values.map { it.templateId!! })
                //得到一个已经存在或者不存在的列表
                StrategyCommonUtils.monthDays[strategyTemplateBindRequest.month]?.let { days ->
                    for (day in 1..days) {
                        val bindInfo =
                            StrategyDayTemplateBindInfoVo(month = strategyTemplateBindRequest.month, day = day)
                        bindInfo.templateId = dayBindInfoMap[day]?.templateId
                        bindInfo.templateName =
                            if (dayBindInfoMap[day]?.templateId != null) templateMap[bindInfo.templateId]?.templateName else null
                        bindInfo.id = dayBindInfoMap[day]?.id
                        bindInfo.projectId = strategyTemplateBindRequest.projectId
                        bindInfo.groupId = strategyTemplateBindRequest.groupId
                        result.add(bindInfo)
                    }
                }
                result
            }
        } ?: run {
            throw ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value())
        }


    }

    override fun removeByMonth(projectId: String, groupId: String, month: Int) {
        this.remove(
            KtQueryWrapper(StrategyDayTemplateBindEntity::class.java).eq(
                StrategyDayTemplateBindEntity::projectId, projectId
            ).eq(StrategyMonthTemplateBindEntity::groupId, groupId).eq(StrategyMonthTemplateBindEntity::month, month)
        )
    }

    override fun removeByDays(projectId: String, groupId: String, days: List<Int>) {
        if (days.isNotEmpty()) {
            this.remove(
                KtQueryWrapper(StrategyDayTemplateBindEntity::class.java).eq(
                    StrategyDayTemplateBindEntity::projectId, projectId
                ).eq(StrategyMonthTemplateBindEntity::groupId, groupId).`in`(StrategyDayTemplateBindEntity::day, days)
            )
        }

    }

    override fun importStrategyWithDays(request: StrategyImportWithDaysRequest) {
        if (request.bindInfos.isNotEmpty()) {
            val templateIds = request.bindInfos.filter { it.templateId != null }.map { it.templateId!! }.distinct()
            val templateItemsMap = strategyTemplateItemService.selectItemByTemplateIds(
                request.projectId!!, templateIds
            )
            val templateMap = strategyTemplateService.templateMap(templateIds)
            //先删除 old 绑定的
            this.removeByMonth(request.projectId!!, request.groupId, request.month)
            //插入
            val everyMonthDaysBindInfo = mutableListOf<StrategyDayTemplateBindEntity>()
            val monthStrategyEntities = mutableListOf<StrategyEntity>()
            //依靠和前端的约定 这个接口每次前端都会把这个月的所有的天数传过来
            val noStrategyDateStrList = mutableListOf<String>()
            val noStrategyDayList = mutableListOf<Int>()

            runBlocking {
                withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                    request.bindInfos.forEach { bindInfoRequest ->
                        launch {
                            bindInfoRequest.templateId?.let {
                                if (templateItemsMap[bindInfoRequest.templateId] == null) {
//                                    return@forEach
                                    return@let
                                }
                                val dayBindInfo = StrategyDayTemplateBindConverter.INSTANCE.vo2Entity(
                                    bindInfoRequest
                                )
                                everyMonthDaysBindInfo.add(
                                    dayBindInfo
                                )
                                templateItemsMap[bindInfoRequest.templateId]?.let {
                                    val dayStrategyEntities = strategyServiceKt.rebuildDayStrategyWithItem(
                                        dayBindInfo, it, templateMap[bindInfoRequest.templateId]!!
                                    )
                                    //处理 real StrategyEntity
                                    monthStrategyEntities.addAll(dayStrategyEntities)
                                }
                            } ?: run {
                                //无策略
                                noStrategyDateStrList.add(bindInfoRequest.getStrategyDateStr())
                                noStrategyDayList.add(bindInfoRequest.day!!)
                            }

                            strategyService.remove(
                                LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getProjectId, request.projectId)
                                    .eq(StrategyEntity::getGroupId, request.groupId)
                                    .`in`(StrategyEntity::getStrategyDate, listOf(bindInfoRequest.getStrategyDateStr()))
                            )
                        }
                    }
                }

            }

            //无策略的要删掉
            strategyServiceKt.removeByStrategyDates(
                request.projectId!!, request.groupId, noStrategyDateStrList
            )
            strategyService.saveBatch(monthStrategyEntities)
            this.saveBatch(everyMonthDaysBindInfo)
        }
    }

}