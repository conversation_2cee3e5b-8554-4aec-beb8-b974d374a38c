package com.wifochina.modules.strategytemplate.converter

import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemControlEntity
import com.wifochina.modules.strategytemplate.request.StrategyItemControlRequest
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateItemControlVo
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

/**
 * Created on 2025/4/22 10:05.
 * <AUTHOR>
 */
@Mapper
interface StrategyTemplateItemControlConverter {

    fun request2Entity(strategyItemControlRequest: StrategyItemControlRequest): StrategyTemplateItemControlEntity

    fun entity2Vo(strategyTemplateItemControlEntity: StrategyTemplateItemControlEntity): StrategyTemplateItemControlVo

    companion object {
        val INSTANCE: StrategyTemplateItemControlConverter = Mappers.getMapper(StrategyTemplateItemControlConverter::class.java)
    }
}