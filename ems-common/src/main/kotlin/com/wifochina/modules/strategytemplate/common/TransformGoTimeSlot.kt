package com.wifochina.modules.strategytemplate.common

import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.go.GoTimeSlot
import com.wifochina.modules.project.entity.ProjectEntity

/**
 * Created on 2025/5/9 11:07.
 * <AUTHOR>
 */
interface TransformGoTimeSlot<T : GoTimeSlotTransformAble> {
    fun transform(data: List<T>, context: TransformContext.() -> Unit): List<GoTimeSlot>

    class TransformContext {
        lateinit var project: ProjectEntity
        var group: GroupEntity? = null
    }

}