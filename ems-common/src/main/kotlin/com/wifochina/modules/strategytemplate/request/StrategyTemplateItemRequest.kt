package com.wifochina.modules.strategytemplate.request

import com.wifochina.common.NoArg
import lombok.ToString
import java.time.LocalTime

/**
 * Created on 2025/4/18 14:09.
 * <AUTHOR>
 */
@NoArg
@ToString
data class StrategyTemplateItemRequest(
    val id: Long?,
    val type: Int,
    val power: Double,
    val soc: Double,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val templateId: Long,
    var projectId: String?,
    // 关于策略控制的request 对象
    val itemControl: StrategyItemControlRequest?
)