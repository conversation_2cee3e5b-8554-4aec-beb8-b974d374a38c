package com.wifochina.modules.strategytemplate.converter

import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemControlEntity
import com.wifochina.modules.strategytemplate.request.StrategyControlRequest
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateItemControlVo
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

/**
 * Created on 2025/4/22 10:05.
 * <AUTHOR>
 */
@Mapper
interface StrategyControlConverter {

    fun copy(strategyControlEntity: StrategyControlEntity): StrategyControlEntity
    fun request2Entity(strategyControlRequest: StrategyControlRequest): StrategyControlEntity

    fun itemControlVoToStrategyControl(itemControlVo: StrategyTemplateItemControlVo): StrategyControlEntity
    fun itemControlToStrategyControl(itemControlEntity: StrategyTemplateItemControlEntity): StrategyControlEntity

    companion object {
        val INSTANCE: StrategyControlConverter = Mappers.getMapper(StrategyControlConverter::class.java)
    }
}