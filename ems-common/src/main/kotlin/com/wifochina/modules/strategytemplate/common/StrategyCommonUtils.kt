package com.wifochina.modules.strategytemplate.common

import com.wifochina.common.time.MyTimeUtil
import com.wifochina.modules.project.entity.ProjectEntity
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * Created on 2025/4/24 10:42.
 * <AUTHOR>
 */
class StrategyCommonUtils {

    companion object {
        val monthDays = (1..12).associateWith { month ->
            when (month) {
                2 -> 29 // 二月固定为 29 天
                4, 6, 9, 11 -> 30
                else -> 31
            }
        }

        fun monthDaysResolve(month: Int, resolve: (month: Int, day: Int, strategyDateStr: String) -> Unit) {
            monthDays[month]?.let { days ->
                for (day in 1..days) {
                    val monthStr = month.toString().padStart(2, '0')
                    val dayStr = day.toString().padStart(2, '0')
                    val strategyDateStr = "$monthStr-$dayStr"
                    resolve(month, day, strategyDateStr)
                }
            }
        }

        fun getStrategyDateStr(project: ProjectEntity, dateZero: Long): String {
            val dateTime = Instant.ofEpochSecond(dateZero).atZone(ZoneId.of(project.timezone))
            val strategyDateStr = dateTime.format(DateTimeFormatter.ofPattern("MM-dd"))
            return strategyDateStr
        }
    }


}