package com.wifochina.modules.strategytemplate.service.dependencypredicate

import com.wifochina.modules.electric.vo.ElectricPriceSystemData
import com.wifochina.modules.strategytemplate.service.predicate.ControlPatternPredicate
import com.wifochina.modules.strategytemplate.service.predicate.PredicateResult

/**
 * Created on 2025/5/8 16:46.
 * <AUTHOR>
 */
interface ControlDependencyPatternPredicate {

    fun dependencyPatternPredicate(context: DependencyPredicateContext): PredicateResult

    data class DependencyPredicateContext(
        var controlPatternPredicateContext: ControlPatternPredicate.ControlPatternPredicateContext,
        var dependencyResultPrices: List<ElectricPriceSystemData.Data>,
    )


}