package com.wifochina.modules.strategytemplate.service.impl

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.wifochina.common.mdc.MdcKotlinContext
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategy.service.StrategyService
import com.wifochina.modules.strategytemplate.common.StrategyCommonUtils.Companion.monthDaysResolve
import com.wifochina.modules.strategytemplate.converter.StrategyControlConverter
import com.wifochina.modules.strategytemplate.converter.StrategyMonthTemplateBindConverter
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateItemConverter
import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity
import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyMonthTemplateBindEntity
import com.wifochina.modules.strategytemplate.mapper.StrategyMonthTemplateBindMapper
import com.wifochina.modules.strategytemplate.request.StrategyImportWithMonthRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateBindRequest
import com.wifochina.modules.strategytemplate.service.StrategyControlService
import com.wifochina.modules.strategytemplate.service.StrategyDayTemplateBindService
import com.wifochina.modules.strategytemplate.service.StrategyMonthTemplateBindService
import com.wifochina.modules.strategytemplate.service.StrategyServiceKt
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemControlService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateService
import com.wifochina.modules.strategytemplate.vo.StrategyMonthTemplateBindInfoVo
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * Created on 2025/4/23 14:45.
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = [Exception::class])
class StrategyMonthTemplateBindServiceImpl(
    val strategyService: StrategyService,
    val strategyServiceKt: StrategyServiceKt,
    val strategyTemplateService: StrategyTemplateService,
    val strategyTemplateItemService: StrategyTemplateItemService,
    val strategyDayTemplateBindService: StrategyDayTemplateBindService,
    val strategyTemplateItemControlService: StrategyTemplateItemControlService,
    val strategyControlService: StrategyControlService,
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
) : ServiceImpl<StrategyMonthTemplateBindMapper, StrategyMonthTemplateBindEntity>(), StrategyMonthTemplateBindService {
    override fun importStrategyWithMonth(request: StrategyImportWithMonthRequest) {
        if (request.bindInfos.isNotEmpty()) {
            val months = request.bindInfos.map { it.month!! }.toList()
            if (months.isNotEmpty()) {
                this.remove(
                    KtQueryWrapper(StrategyMonthTemplateBindEntity::class.java).eq(
                        StrategyMonthTemplateBindEntity::projectId, request.projectId
                    ).eq(StrategyMonthTemplateBindEntity::groupId, request.groupId)
                        .`in`(StrategyMonthTemplateBindEntity::month, months)
                )
                //重新新增 模版id不为空的
                this.saveBatch(request.bindInfos.filter { it.templateId != null }.map {
                    StrategyMonthTemplateBindConverter.INSTANCE.vo2Entity(it)
                })

                val templateMap = strategyTemplateService.templateMap(request.bindInfos.mapNotNull { it.templateId })
                //操作策略
                //生成策略 ,根据模版
                runBlocking {
                    withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                        request.bindInfos.forEach { bindInfoRequest ->
                            launch {
                                takeIf { bindInfoRequest.templateId != null }?.let {
                                    //查询这个模版的 item
                                    val templateItemsMap = strategyTemplateItemService.selectItemByTemplateIds(
                                        request.projectId!!, listOf(bindInfoRequest.templateId!!)
                                    )
                                    if (templateItemsMap[bindInfoRequest.templateId] == null) {
                                        //这个模版下无item
//                                        return@forEach
                                        return@let
                                    }
                                    val everyMonthDaysBindInfo = mutableListOf<StrategyDayTemplateBindEntity>()

                                    val monthStrategyEntities = mutableListOf<StrategyEntity>()
                                    val strategyDateStrList = mutableListOf<String>()

                                    val itemControlIds =
                                        templateItemsMap[bindInfoRequest.templateId]!!.filter { it.itemControlId != null }
                                            .map { it.itemControlId!! }
                                    val itemControlMaps =
                                        strategyTemplateItemControlService.selectControlByIds(itemControlIds)

                                    monthDaysResolve(bindInfoRequest.month!!) { _, day, strategyDateStr ->
                                        strategyDateStrList.add(strategyDateStr)
                                    }
                                    strategyControlService.remove(
                                        KtQueryWrapper(StrategyControlEntity::class.java).eq(
                                            StrategyControlEntity::projectId, request.projectId
                                        ).eq(StrategyControlEntity::groupId, request.groupId)
                                            .`in`(StrategyControlEntity::strategyDate, strategyDateStrList)
                                    )
                                    strategyDateStrList.clear()
                                    // 月份的具体天数处理
                                    monthDaysResolve(bindInfoRequest.month!!) { _, day, strategyDateStr ->
                                        strategyDateStrList.add(strategyDateStr)

                                        //根据itemControlIds 去查询
                                        val dayStrategyEntities =
                                            templateItemsMap[bindInfoRequest.templateId]!!.map { itemEntity ->
                                                val strategyControl =
                                                    itemControlMaps[itemEntity.itemControlId]?.let { itemControl ->
                                                        val strategyControl =
                                                            StrategyControlConverter.INSTANCE.itemControlVoToStrategyControl(
                                                                itemControl
                                                            )
                                                        strategyControl.projectId = request.projectId
                                                        strategyControl.groupId = request.groupId
                                                        strategyControl.strategyDate = strategyDateStr
                                                        strategyControl.id = null
                                                        strategyControlService.save(strategyControl)
                                                        strategyControl
                                                    }
                                                StrategyTemplateItemConverter.INSTANCE.entity2StrategyEntity(itemEntity)
                                                    .apply {
                                                        groupId = request.groupId
                                                        strategyDate = strategyDateStr
                                                        // 充放电类型 来自模版 只有海外有
                                                        strategyType =
                                                            templateMap[bindInfoRequest.templateId]!!.strategyType
                                                        //临时设置一下
                                                        strategyControlId = strategyControl?.id
                                                    }
                                            }

                                        monthStrategyEntities.addAll(dayStrategyEntities)
                                        everyMonthDaysBindInfo.add(StrategyMonthTemplateBindConverter.INSTANCE.vo2DayBindEntity(
                                            bindInfoRequest
                                        ).also {
                                            it.day = day
                                        })
                                    }

                                    //直接删掉这个月的 所有day的 策略
                                    strategyService.remove(
                                        LambdaQueryWrapper<StrategyEntity>().eq(
                                            StrategyEntity::getProjectId,
                                            request.projectId
                                        )
                                            .eq(StrategyEntity::getGroupId, request.groupId)
                                            .`in`(StrategyEntity::getStrategyDate, strategyDateStrList)
                                    )
                                    //重新保存 月的每天的策略
                                    strategyService.saveBatch(monthStrategyEntities)

                                    //删除old  days binds 根据month
                                    strategyDayTemplateBindService.removeByMonth(
                                        request.projectId!!, request.groupId, bindInfoRequest.month!!
                                    )
                                    //保存该月的每天绑定的模版bind 汇总插入
                                    strategyDayTemplateBindService.saveBatch(everyMonthDaysBindInfo)

                                } ?: run {
                                    //无策略
                                    val strategyDateStrList = mutableListOf<String>()
                                    monthDaysResolve(bindInfoRequest.month!!) { _, _, strategyDateStr ->
                                        strategyDateStrList.add(strategyDateStr)
                                    }
                                    strategyControlService.remove(
                                        KtQueryWrapper(StrategyControlEntity::class.java).eq(
                                            StrategyControlEntity::projectId, request.projectId
                                        ).eq(StrategyControlEntity::groupId, request.groupId)
                                            .`in`(StrategyControlEntity::strategyDate, strategyDateStrList)
                                    )
                                    strategyServiceKt.removeByStrategyDates(
                                        request.projectId!!, request.groupId, strategyDateStrList
                                    )
                                    // 无策略 处理 days 的bind
                                    strategyDayTemplateBindService.removeByMonth(
                                        request.projectId!!, request.groupId, bindInfoRequest.month!!
                                    )
//                                    return@forEach
                                    return@run
                                }
                            }
                        }
                    }
                }

            }


        }
    }

    override fun getBindInfo(strategyTemplateBindRequest: StrategyTemplateBindRequest): List<StrategyMonthTemplateBindInfoVo> {
        // 根据月份去 得到具体月份的 day 的 记录
        val result = mutableListOf<StrategyMonthTemplateBindInfoVo>()
        return this.list(
            KtQueryWrapper(StrategyMonthTemplateBindEntity::class.java).eq(
                StrategyMonthTemplateBindEntity::projectId, strategyTemplateBindRequest.projectId
            ).eq(StrategyMonthTemplateBindEntity::groupId, strategyTemplateBindRequest.groupId)
        ).let { monthTemplateBindInfos ->
            val monthBindInfoMap = monthTemplateBindInfos.associateBy { monthBindInfo -> monthBindInfo.month!! }
            val templateMap = strategyTemplateService.templateMap(monthBindInfoMap.values.map { it.templateId!! })
            //得到一个已经存在或者不存在的列表
            for (month in 1..12) {
                val bindInfo = StrategyMonthTemplateBindInfoVo(month)
                bindInfo.templateId = monthBindInfoMap[month]?.templateId
                bindInfo.templateName =
                    if (monthBindInfoMap[month]?.templateId != null) templateMap[bindInfo.templateId]?.templateName else null
                bindInfo.id = monthBindInfoMap[month]?.id
                bindInfo.projectId = strategyTemplateBindRequest.projectId
                bindInfo.groupId = strategyTemplateBindRequest.groupId
                result.add(bindInfo)
            }
            result
        }
    }


}