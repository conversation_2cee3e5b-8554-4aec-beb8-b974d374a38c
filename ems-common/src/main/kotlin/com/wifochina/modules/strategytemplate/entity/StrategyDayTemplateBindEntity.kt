package com.wifochina.modules.strategytemplate.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.wifochina.modules.BaseEntity
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.checkerframework.checker.units.qual.g

/**
 * Created on 2025/4/24 10:30.
 * <AUTHOR>
 */

@TableName("t_strategy_day_template_bind")
@ApiModel(value = "StrategyDayTemplateBindEntity对象", description = "StrategyDayTemplateBindEntity对象")
class StrategyDayTemplateBindEntity : BaseEntity() {

    companion object {
        private const val serialVersionUID: Long = 1L
    }

    @TableId(value = "id", type = IdType.AUTO)
    var id: Int? = null

    @ApiModelProperty(value = "月份")
    var month: Int? = null

    @ApiModelProperty(value = "day")
    var day: Int? = null

    @ApiModelProperty(value = "模版id")
    var templateId: Long? = null

    @ApiModelProperty(value = "项目Id")
    var projectId: String? = null

    @ApiModelProperty(value = "分组Id")
    var groupId: String? = null

    fun getStrategyDateStr(): String {
        val monthStr = this.month.toString().padStart(2, '0')
        val dayStr = this.day.toString().padStart(2, '0')
        val strategyDateStr = "$monthStr-$dayStr"
        return strategyDateStr
    }
}