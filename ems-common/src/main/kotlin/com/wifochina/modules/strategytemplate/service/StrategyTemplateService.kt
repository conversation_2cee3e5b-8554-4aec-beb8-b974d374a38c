package com.wifochina.modules.strategytemplate.service

import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.service.IService
import com.wifochina.common.page.PageBean
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity
import com.wifochina.modules.strategytemplate.request.StrategyTemplateCopyRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateRequest
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateVo

/**
 * Created on 2025/4/17 18:21.
 * <AUTHOR>
 */
interface StrategyTemplateService : IService<StrategyTemplateEntity> {

    fun addTemplate(strategyTemplateRequest: StrategyTemplateRequest)
    fun updateTemplate(strategyTemplateRequest: StrategyTemplateRequest)

    fun templateMap(ids: List<Long>): Map<Long, StrategyTemplateEntity>
    fun getTemplates(projectId: String): List<StrategyTemplateVo>
    fun getPage(projectId: String?, pageBean: PageBean): IPage<StrategyTemplateVo>
    fun deleteTemplate(id: Long)
    fun copyTemplate(strategyTemplateCopyRequest: StrategyTemplateCopyRequest)
}
