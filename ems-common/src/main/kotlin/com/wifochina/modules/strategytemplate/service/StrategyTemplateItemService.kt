package com.wifochina.modules.strategytemplate.service

import com.baomidou.mybatisplus.extension.service.IService
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity
import com.wifochina.modules.strategytemplate.request.StrategyTemplateItemRequest

/**
 * Created on 2025/4/18 15:11.
 * <AUTHOR>
 */
interface StrategyTemplateItemService : IService<StrategyTemplateItemEntity> {
    fun addItem(templateId: Long, request: StrategyTemplateItemRequest)
    fun updateItem(request: StrategyTemplateItemRequest)
    fun deleteItem(request: StrategyTemplateItemRequest)

    fun selectItemByTemplateIds(projectId: String, templateIds: List<Long>): Map<Long, List<StrategyTemplateItemEntity>>
}