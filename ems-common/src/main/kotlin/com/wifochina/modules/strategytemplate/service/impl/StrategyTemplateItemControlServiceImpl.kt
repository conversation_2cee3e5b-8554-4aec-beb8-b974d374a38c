package com.wifochina.modules.strategytemplate.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateItemControlConverter
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemControlEntity
import com.wifochina.modules.strategytemplate.mapper.StrategyTemplateItemControlMapper
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemControlService
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateItemControlVo
import org.springframework.stereotype.Service

/**
 * Created on 2025/4/21 17:01.
 * <AUTHOR>
 */

@Service
class StrategyTemplateItemControlServiceImpl : ServiceImpl<StrategyTemplateItemControlMapper, StrategyTemplateItemControlEntity>(),
    StrategyTemplateItemControlService {


    override fun selectControlByIds(ids: List<Long>): Map<Long, StrategyTemplateItemControlVo> {
        return takeIf { ids.isNotEmpty() }
            ?.let {
//                this.list(
//                    KtQueryWrapper(StrategyItemControlEntity::class.java)
//                        .`in`(StrategyItemControlEntity::strategyItemId, ids)
                this.listByIds(ids)
                    .map {
                        StrategyTemplateItemControlConverter.INSTANCE.entity2Vo(it)
                    }.associateBy {
                        it.id!!
                    }
            } ?: mapOf()
    }
}