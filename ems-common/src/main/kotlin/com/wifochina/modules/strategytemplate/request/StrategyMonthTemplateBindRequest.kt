package com.wifochina.modules.strategytemplate.request

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.wifochina.common.NoArg
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 * Created on 2025/4/23 15:33.
 * <AUTHOR>
 */
@NoArg
data class StrategyMonthTemplateBindRequest(
    @ApiModelProperty(value = "id") var id: Int? = null,

    @ApiModelProperty(value = "月份") var month: Int? = null,

    @ApiModelProperty(value = "模版id") var templateId: Long? = null,

    @ApiModelProperty(value = "项目Id") var projectId: String? = null,

    @ApiModelProperty(value = "分组Id") var groupId: String? = null,
)