package com.wifochina.modules.strategytemplate.converter

import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyMonthTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity
import com.wifochina.modules.strategytemplate.request.StrategyMonthTemplateBindRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateRequest
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

/**
 * Created on 2025/4/23 15:41.
 * <AUTHOR>
 */
@Mapper
interface StrategyMonthTemplateBindConverter {

    fun copy(strategyMonthTemplateBindEntity: StrategyMonthTemplateBindEntity): StrategyMonthTemplateBindEntity
    fun vo2Entity(strategyMonthTemplateBindRequest: StrategyMonthTemplateBindRequest): StrategyMonthTemplateBindEntity

    fun vo2DayBindEntity(strategyMonthTemplateBindRequest: StrategyMonthTemplateBindRequest): StrategyDayTemplateBindEntity

    companion object {
        val INSTANCE: StrategyMonthTemplateBindConverter =
            Mappers.getMapper(StrategyMonthTemplateBindConverter::class.java)
    }
}