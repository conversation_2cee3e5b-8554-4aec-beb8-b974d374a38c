package com.wifochina.modules.strategytemplate.service.predicate

import com.wifochina.common.constants.ElectricPriceTypeEnum
import com.wifochina.modules.group.request.go.YearlyStrategy
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.strategytemplate.enums.ModelStrategyEnums
import com.wifochina.modules.strategytemplate.service.ModelStrategy
import com.wifochina.modules.strategytemplate.service.PredicateContext
import com.wifochina.modules.strategytemplate.service.PredicateModel
import com.wifochina.modules.strategytemplate.service.TemplatePreviewContext
import org.springframework.stereotype.Component

/**
 * Created on 2025/4/29 10:06.
 * <AUTHOR>
 */
@Component
class DefaultCustomModelStrategy(
    private val predicateModels: List<PredicateModel>
) : ModelStrategy {
    override fun executeModelPredicate(context: PredicateContext): Map<String, List<YearlyStrategy>> {
        //根据 xxx 选择 是 使用 simple 还是 control
        return getModel(context).predicate(context)
    }

    fun getModel(context: PredicateContext): PredicateModel {
        return context.templatePreviewContext?.let {
            predicateModels.filter { it.model() == ModelStrategyEnums.TEMPLATE_PREVIEW_CONTROL }[0]
        } ?: run {
            takeIf { context.project.electricPriceType.equals(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.value) }?.let {
                run {
                    predicateModels.filter { it.model() == ModelStrategyEnums.CONTROL }[0]
                }
            } ?: run {
                predicateModels.filter { it.model() == ModelStrategyEnums.SIMPLE }[0]
            }
        }
    }
}