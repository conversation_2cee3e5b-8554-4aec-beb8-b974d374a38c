package com.wifochina.modules.strategytemplate.service.predicate

import com.wifochina.modules.strategytemplate.enums.BaseValueRangeConditionEnums
import com.wifochina.modules.strategytemplate.enums.ControlPatternLabelEnums
import com.wifochina.modules.strategytemplate.enums.StrategyChargeDischargeTypeEnums
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }

/**
 * 最值predicate
 * Created on 2025/5/8 14:53.
 * <AUTHOR>
 */
@Order(0)
@Service
class BaseValueControlPatternPredicate : ControlPatternPredicate {

    override fun patternPredicate(context: ControlPatternPredicate.ControlPatternPredicateContext): PredicateResult {
        val result = PredicateResult()
        val control = context.control
        val strategy = context.strategyContext.strategy
        val type = strategy.type
        val allDayElectricPrices = context.strategyContext.allDayElectricPrices
        val strategyPeriodElectricPrices = context.strategyContext.strategyPeriodElectricPrices
        return if (control.priceBaseValueController == true) {
            val sectionCount = control.priceBaseValueSectionCount ?: run {
                log.info { "open priceBaseValueController but not  set baseValueSectionCount is null for strategyEntity ${strategy.id}, control:$control" }
                return result.apply { skip = true }
            }
            control.priceBaseValueRangeCondition?.let { rangeCondition ->
                when (BaseValueRangeConditionEnums.valueOf(rangeCondition)) {
                    BaseValueRangeConditionEnums.STRATEGY_PERIOD_RANGE -> {
                        //策略简单直接
                        result.apply {
                            matchResult = when (StrategyChargeDischargeTypeEnums.valueOfByType(type)) {
                                StrategyChargeDischargeTypeEnums.CHARGE -> strategyPeriodElectricPrices.sortedBy { it.average }
                                    .take(sectionCount)

                                StrategyChargeDischargeTypeEnums.DISCHARGE -> strategyPeriodElectricPrices.sortedByDescending { it.average }
                                    .take(sectionCount)
                            }
                            log.info { "price baseValue strategy_period_range  match prices : $matchResult" }
                        }
                    }

                    BaseValueRangeConditionEnums.ALL_DAY_RANGE -> {
                        //如果是all day 则快速判断一下
                        val takeSelectionFromAllDay = when (StrategyChargeDischargeTypeEnums.valueOfByType(type)) {
                            StrategyChargeDischargeTypeEnums.CHARGE -> allDayElectricPrices.sortedBy { it.average }
                                .take(sectionCount)

                            StrategyChargeDischargeTypeEnums.DISCHARGE -> allDayElectricPrices.sortedByDescending { it.average }
                                .take(sectionCount)
                        }
                        val none =
                            takeSelectionFromAllDay.none { it.startTimeUnix.toLong() in context.strategyContext.strategyStartTimeUnix..context.strategyContext.strategyEndTimeUnix }
                        if (none) {
                            //如果全天策略 满足的段数都不在策略段内 直接表示这个策略无法找到最终的充电时段 , 这个strategyEntity 不生效 可以快速返回
                            return result.apply { skip = true }
                        }
                        //满足段内的返回
                        result.apply {
                            matchResult =
                                takeSelectionFromAllDay.filter { it.startTimeUnix.toLong() in context.strategyContext.strategyStartTimeUnix..context.strategyContext.strategyEndTimeUnix }
                            log.info { "price baseValue all_day_range  match prices ${strategy.startTime}-${strategy.endTime} : $matchResult" }
                        }

                    }
                }
            } ?: result
        } else {
            result
        }

    }

    override fun patternLabel(): ControlPatternLabelEnums {
        return ControlPatternLabelEnums.BASE_VALUE
    }


}