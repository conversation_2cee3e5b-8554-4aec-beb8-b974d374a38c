package com.wifochina.modules.strategytemplate.converter

import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyMonthTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity
import com.wifochina.modules.strategytemplate.request.StrategyDaysTemplateBindRequest
import com.wifochina.modules.strategytemplate.request.StrategyMonthTemplateBindRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateRequest
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

/**
 * Created on 2025/4/23 15:41.
 * <AUTHOR>
 */
@Mapper
interface StrategyDayTemplateBindConverter {

    fun vo2Entity(
        strategyDaysTemplateBindRequest: StrategyDaysTemplateBindRequest
    ): StrategyDayTemplateBindEntity


    fun copy(strategyDayTemplateBindEntity: StrategyDayTemplateBindEntity): StrategyDayTemplateBindEntity

    companion object {
        val INSTANCE: StrategyDayTemplateBindConverter =
            Mappers.getMapper(StrategyDayTemplateBindConverter::class.java)
    }
}