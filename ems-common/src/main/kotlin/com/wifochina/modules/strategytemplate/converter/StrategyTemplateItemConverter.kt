package com.wifochina.modules.strategytemplate.converter

import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity
import com.wifochina.modules.strategytemplate.request.StrategyTemplateItemRequest
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateItemVo
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.factory.Mappers

/**
 * Created on 2024/10/29 16:23.
 * <AUTHOR>
 */
@Mapper
interface StrategyTemplateItemConverter {
    fun request2Entity(strategyTemplateItemRequest: StrategyTemplateItemRequest): StrategyTemplateItemEntity
    fun entity2Vo(strategyTemplateItemEntity: StrategyTemplateItemEntity): StrategyTemplateItemVo

    @Mappings(
        Mapping(source = "startTime", target = "startTime"),
        Mapping(source = "endTime", target = "endTime"),
        Mapping(source = "power", target = "power"),
        Mapping(source = "soc", target = "soc"),
        Mapping(source = "type", target = "type"),
        Mapping(target = "id", ignore = true)
    )
    fun entity2StrategyEntity(strategyTemplateItemEntity: StrategyTemplateItemEntity): StrategyEntity

    companion object {
        val INSTANCE: StrategyTemplateItemConverter = Mappers.getMapper(StrategyTemplateItemConverter::class.java)
    }
}