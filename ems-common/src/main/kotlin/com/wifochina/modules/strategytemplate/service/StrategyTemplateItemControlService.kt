package com.wifochina.modules.strategytemplate.service

import com.baomidou.mybatisplus.extension.service.IService
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemControlEntity
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateItemControlVo

/**
 * Created on 2025/4/21 17:00.
 * <AUTHOR>
 */
interface StrategyTemplateItemControlService : IService<StrategyTemplateItemControlEntity> {
    fun selectControlByIds(ids: List<Long>): Map<Long, StrategyTemplateItemControlVo>
}