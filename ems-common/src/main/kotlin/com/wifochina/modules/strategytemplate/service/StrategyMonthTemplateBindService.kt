package com.wifochina.modules.strategytemplate.service

import com.baomidou.mybatisplus.extension.service.IService
import com.wifochina.modules.strategytemplate.entity.StrategyMonthTemplateBindEntity
import com.wifochina.modules.strategytemplate.request.StrategyImportWithMonthRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateBindRequest
import com.wifochina.modules.strategytemplate.vo.StrategyMonthTemplateBindInfoVo

/**
 * Created on 2025/4/23 14:45.
 * <AUTHOR>
 */
interface StrategyMonthTemplateBindService : IService<StrategyMonthTemplateBindEntity> {
    fun importStrategyWithMonth(request: StrategyImportWithMonthRequest)

    fun getBindInfo(strategyTemplateBindRequest: StrategyTemplateBindRequest): List<StrategyMonthTemplateBindInfoVo>
}