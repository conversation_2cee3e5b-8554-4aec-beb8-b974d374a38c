package com.wifochina.modules.strategytemplate.request

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.wifochina.common.NoArg
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 * Created on 2025/4/23 15:33.
 * <AUTHOR>
 */
@NoArg
data class StrategyDaysTemplateBindRequest(
    @ApiModelProperty(value = "id") var id: Int? = null,

    @ApiModelProperty(value = "月份") var month: Int? = null,

    @ApiModelProperty(value = "day") var day: Int? = null,

    @ApiModelProperty(value = "模版id") var templateId: Long? = null,

    @ApiModelProperty(value = "项目Id") var projectId: String? = null,

    @ApiModelProperty(value = "分组Id") var groupId: String? = null,


    ) {
    fun getStrategyDateStr(): String {
        val monthStr = this.month.toString().padStart(2, '0')
        val dayStr = this.day.toString().padStart(2, '0')
        val strategyDateStr = "$monthStr-$dayStr"
        return strategyDateStr
    }
}