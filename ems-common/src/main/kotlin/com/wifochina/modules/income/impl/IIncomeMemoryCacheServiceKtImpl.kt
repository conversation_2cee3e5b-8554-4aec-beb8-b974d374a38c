package com.wifochina.modules.income.impl

import com.wifochina.common.constants.CalculateTypeEnum
import com.wifochina.common.constants.TimePointEnum
import com.wifochina.common.enums.MemoryCacheEnum
import com.wifochina.common.time.MyTimeUtil
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.income.IIncomeMemoryCacheServiceKt
import com.wifochina.modules.income.RenewableProfitService
import com.wifochina.modules.income.timer.TaskOperationTimer
import com.wifochina.modules.income.vo.IncomeElectricVo
import com.wifochina.modules.operation.VO.ElectricProfitVO
import com.wifochina.modules.operation.VO.ProfitVO
import com.wifochina.modules.operation.request.BenefitRequest
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.vo.ProjectVo
import io.github.oshai.kotlinlogging.KotlinLogging
import lombok.extern.slf4j.Slf4j
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }

@Service
@Slf4j
class IIncomeMemoryCacheServiceKtImpl(
    val renewableProfitService: RenewableProfitService
) : IIncomeMemoryCacheServiceKt {
    /**
     * 更新 内存中的缓存, 主要更新的是 可再生能源相关的 内存缓存,包括 昨日 合计 和 月份收益
     * 支持更新CalculateTypeEnum 中的 PV,WIND和WASTER
     */
    override fun updateRenewableIncomeMemoryCache(project: ProjectEntity, calculateTypeEnum: CalculateTypeEnum) {
        val projectInitTime = MyTimeUtil.getOneDayZeroTime(
            project.createTime, project.timezone
        )
        val todayZero = MyTimeUtil.getTodayZeroTime(project.timezone)
        //下面每一个都生成一个对应查询区间的 BenefitRequest 不共用一个 防止多线程产生影响
        takeIf { projectInitTime < todayZero }?.let {
            updateRenewableIncomeMemoryCacheCore(
                MemoryCacheEnum.TOTAL_CACHE,
                project,
                BenefitRequest.getTimeProjectInitToYestEnd(project),
                calculateTypeEnum
            )
            updateRenewableIncomeMemoryCacheCore(
                MemoryCacheEnum.MONTH_CACHE, project, BenefitRequest.getTimeMonth(project), calculateTypeEnum
            )
        }
        takeIf { projectInitTime <= todayZero - EmsConstants.ONE_DAY_SECOND }?.let {
            updateRenewableIncomeMemoryCacheCore(
                MemoryCacheEnum.YESTERDAY_CACHE, project, BenefitRequest.getTimeYest(project), calculateTypeEnum
            )
        }
    }

    /**
     * 从内存缓存去获取 一个项目的 电量缓存数据获取项目的缓存数据  内存级别的
     *
     */
    override fun getElectricCache(
        projectId: String,
        memoryCacheEnum: MemoryCacheEnum
    ): ElectricProfitVO? {

        return when (memoryCacheEnum) {
            MemoryCacheEnum.TOTAL_CACHE -> {
                takeIf { TaskOperationTimer.totalElectricMap[projectId] != null }?.let {
                    TaskOperationTimer.totalElectricMap[projectId]
                } ?: run {
                    ElectricProfitVO().init()
                }
            }

            MemoryCacheEnum.YESTERDAY_CACHE -> {
                takeIf { TaskOperationTimer.yesElectricMap[projectId] != null }?.let {
                    TaskOperationTimer.yesElectricMap[projectId]
                } ?: run {
                    ElectricProfitVO().init()
                }
            }

            MemoryCacheEnum.MONTH_CACHE -> {
                takeIf { TaskOperationTimer.monthElectricMap[projectId] != null }?.let {
                    TaskOperationTimer.monthElectricMap[projectId]
                } ?: run {
                    ElectricProfitVO().init()
                }
            }
        }
    }

    private fun updateRenewableIncomeMemoryCacheCore(
        memoryCacheEnum: MemoryCacheEnum,
        project: ProjectEntity,
        benefitRequest: BenefitRequest,
        calculateTypeEnum: CalculateTypeEnum
    ) {
        log.debug {
            "IIncomeMemoryCacheServiceKtImpl#updateRenewableIncomeMemoryCache--->  update 正在执行 ${project.id}: ${project.projectName}"
        }
        val profitVo = renewableProfitService.getRenewableProfitVo(
            benefitRequest, project.id, calculateTypeEnum
        ) ?: run { ProfitVO() }
        log.info {
            "IIncomeMemoryCacheServiceKtImpl#updateRenewableIncomeMemoryCache--->  update 正在执行-  ${project.id}: ${project.projectName}  | profitVo: $profitVo"
        }
        when (memoryCacheEnum) {
            MemoryCacheEnum.TOTAL_CACHE -> {
                when (calculateTypeEnum) {
                    CalculateTypeEnum.PV -> {
                        TaskOperationTimer.totalPvMap[project.id] = profitVo
                    }

                    CalculateTypeEnum.WIND -> {
                        TaskOperationTimer.totalWindMap[project.id] = profitVo
                    }

                    CalculateTypeEnum.WASTER -> {
                        TaskOperationTimer.totalWasterMap[project.id] = profitVo
                    }

                    else -> {}
                }
            }

            MemoryCacheEnum.YESTERDAY_CACHE -> {
                when (calculateTypeEnum) {
                    CalculateTypeEnum.PV -> {
                        TaskOperationTimer.yesPvMap[project.id] = profitVo
                    }

                    CalculateTypeEnum.WIND -> {
                        TaskOperationTimer.yesWindMap[project.id] = profitVo
                    }

                    CalculateTypeEnum.WASTER -> {
                        TaskOperationTimer.yesWasterMap[project.id] = profitVo
                    }

                    else -> {}
                }

            }

            MemoryCacheEnum.MONTH_CACHE -> {
                when (calculateTypeEnum) {
                    CalculateTypeEnum.PV -> {
                        TaskOperationTimer.monthPvMap[project.id] = profitVo
                    }

                    CalculateTypeEnum.WIND -> {
                        TaskOperationTimer.monthWindMap[project.id] = profitVo
                    }

                    CalculateTypeEnum.WASTER -> {
                        TaskOperationTimer.monthWasterMap[project.id] = profitVo
                    }

                    else -> {}
                }
            }
        }
    }
}