package com.wifochina.modules.income

import com.wifochina.common.constants.CalculateTypeEnum
import com.wifochina.common.constants.TimePointEnum
import com.wifochina.common.enums.MemoryCacheEnum
import com.wifochina.modules.income.vo.IncomeElectricVo
import com.wifochina.modules.operation.VO.ElectricProfitVO
import com.wifochina.modules.project.entity.ProjectEntity

/**
 * Created on 2025/2/14 11:17.
 * <AUTHOR>
 */
interface IIncomeMemoryCacheServiceKt {


    fun updateRenewableIncomeMemoryCache(
        project: ProjectEntity, calculateTypeEnum: CalculateTypeEnum
    )

    fun getElectricCache(projectId: String, memoryCacheEnum: MemoryCacheEnum): ElectricProfitVO?

}