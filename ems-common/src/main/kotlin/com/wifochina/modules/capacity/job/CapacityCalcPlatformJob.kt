package com.wifochina.modules.capacity.job

import com.wifochina.common.mdc.MdcKotlinContext
import com.wifochina.common.util.CapacityControlAdjustModelEnum
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.capacity.job.init.CapacityCalcPlatformJobService
import com.wifochina.modules.capacity.service.adjustmodel.CapacityControlAdjustModelChooser
import com.wifochina.modules.capacity.service.adjustmodel.CapacityControlAdjustModelService
import com.wifochina.modules.demand.job.DemandCalcHolder
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.project.entity.ProjectEntity
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.quartz.JobExecutionContext
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.scheduling.quartz.QuartzJobBean
import org.springframework.util.StopWatch
import java.time.LocalDateTime
import java.time.ZoneId

private val log = KotlinLogging.logger { }

/**
 * Created on 2025/1/9 10:38.
 * <AUTHOR>
 */
class CapacityCalcPlatformJob(
    val groupService: GroupService,
    val capacityControlAdjustModelChooser: CapacityControlAdjustModelChooser,
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
) : QuartzJobBean() {

    override fun executeInternal(context: JobExecutionContext) {
        val timezone = context.jobDetail.jobDataMap[EmsConstants.TIME_ZONE_KEY].toString();
//        if (timezone != "Asia/Shanghai") {
//            return
//        }
        CapacityCalcPlatformJobService.holder[timezone]?.takeIf { it.isNotEmpty() }?.let { calcProject ->
            val startDateTime = LocalDateTime.now(ZoneId.of(timezone))
//            val filterProjects = calcProject.filter { it.id.equals("4f537620d37d40e19dd25be5ca6ad941") }.toList()
            try {
                runBlocking {
                    withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                        calcProject.forEach { project ->
                            launch {
                                // 查询开启了容量控制的 分组的列表
                                val capacityGroups = groupService.queryCapacityGroup(project.id)
                                log.info {
                                    "CapacityCalcPlatformJob 开始执行 时间: $startDateTime, 项目Id:${project.id} , 项目名称:${project.projectName} , 容量分组id列表:${
                                        capacityGroups.joinToString { "${it.id}-${it.name}," }
                                    }"
                                }
                                val stopwatch = StopWatch()
                                stopwatch.start()
                                //core
//                    capacityGroups = capacityGroups.filter { it.id.equals("f3835b1d89af43b79e8e7f0f7ce56cf4") }.toList()
                                for (groupEntity in capacityGroups) {
                                    capacityControlAdjustModelChooser // 根据当前控制需量调整模式 来选择策略触发
                                        .chooseAdjustModel(CapacityControlAdjustModelEnum.manual.name)
                                        .adjustExecute(object :
                                                           CapacityControlAdjustModelService.HandlerAdjustModelHolder {
                                            override fun group(): GroupEntity {
                                                return groupEntity
                                            }

                                            override fun project(): ProjectEntity {
                                                return project
                                            }
                                        })
                                }
                                val endTime = LocalDateTime.now(ZoneId.of(project.timezone))
                                stopwatch.stop()
                                log.info {
                                    "CapacityCalcPlatformJob 执行完毕 时间: $endTime, 项目Id:${project.id} , 项目名称:${project.projectName} , 耗时:${stopwatch.totalTimeSeconds}s "
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                log.error { "....job错误 ${e.message}" }
            }
        }
    }
}