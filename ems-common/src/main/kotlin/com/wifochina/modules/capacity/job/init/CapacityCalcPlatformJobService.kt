package com.wifochina.modules.capacity.job.init

import cn.hutool.core.lang.hash.Hash
import com.wifochina.modules.capacity.job.CapacityCalcPlatformJob
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.jobinit.jobservice.AbstractTimeZoneDefaultQuartzJobService
import com.wifochina.modules.jobinit.jobservice.JobType
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import org.quartz.Scheduler
import org.springframework.stereotype.Service
import java.util.Collections
import java.util.function.Consumer

/**
 * Created on 2025/3/17 15:42.
 * <AUTHOR>
 */
@Service
class CapacityCalcPlatformJobService(
    override val scheduler: Scheduler, val groupService: GroupService, val projectService: ProjectService
) : AbstractTimeZoneDefaultQuartzJobService<CapacityCalcPlatformJob>(scheduler) {
    companion object {
        val holder: MutableMap<String, MutableSet<ProjectEntity>> = mutableMapOf()
    }

    override fun timezoneProjects(timezone: String) {
        // 查询 projectList 符合要求的项目列表 然后
        val projectEntities: List<ProjectEntity> = projectService.getProjectsByTimeZone(timezone)
        projectEntities.forEach(Consumer<ProjectEntity> { project: ProjectEntity ->
            val groupEntities = groupService.queryCapacityGroup(project.id)
            if (groupEntities.isNotEmpty()) {
                holder.computeIfAbsent(timezone) { Collections.synchronizedSet(mutableSetOf()) }.add(project)
            }
        })
    }

    override fun runNowForTest(): Boolean {
        return false
    }

    override fun cron(jobInnerContext: JobInnerContext): String? {
        return "0 0/1 * * * ?";
    }

    override fun context(): JobContext {
        return JobContext("high-freq-capacity-job", "new-capacity-group", runNowForTest())
    }

    override fun jobType(): JobType {
        return JobType.TIME_ZONE_JOB
    }
}