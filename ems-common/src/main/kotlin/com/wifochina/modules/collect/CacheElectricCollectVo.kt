package com.wifochina.modules.collect

/**
 * 想构建一个 缓存 电量的 汇总结构vo
 * 希望统一系统内的 所有对外的 电量的汇总 结构对象 ,目前知道的有 今日,昨日,月,汇总 几种
 * Created on 2025/2/28 15:15.
 * <AUTHOR>
 */
data class CacheElectricCollectVo(
    val todayElectricCollect: ElectricCollectVo? = ElectricCollectVo(),
    val yesterdayElectricCollect: ElectricCollectVo? = ElectricCollectVo(),
    val monthElectricCollect: ElectricCollectVo? = ElectricCollectVo(),
    val totalElectricCollect: ElectricCollectVo? = ElectricCollectVo(),
)


/**
 * 电量汇总 vo
 * 只有 冲放2个属性
 */
data class ElectricCollectVo(
    var discharge: Double = 0.0,
    var charge: Double = 0.0
)