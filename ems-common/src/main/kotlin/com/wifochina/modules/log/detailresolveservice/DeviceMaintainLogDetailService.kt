package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.operation.request.CacheDataReRunRequest
import com.wifochina.modules.system.request.DeviceMaintainRequest
import com.wifochina.modules.user.entity.UserEntity
import com.wifochina.modules.user.request.UserRegisterRequest
import com.wifochina.modules.user.service.LogService
import com.wifochina.modules.user.service.UserService
import com.wifochina.modules.version.entity.VersionEntity
import com.wifochina.modules.version.request.VersionRequest
import com.wifochina.modules.version.service.VersionService
import org.springframework.stereotype.Service
import kotlin.reflect.jvm.internal.impl.load.kotlin.JvmType

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class DeviceMaintainLogDetailService(
    logService: LogService
) : AbstractLogDetailService<DeviceMaintainRequest, JvmType.Object>(logService, null) {
    override fun detailLog(logDetailContext: LogDetailContext<DeviceMaintainRequest>) {
        detailResolve(logDetailContext)
    }

}