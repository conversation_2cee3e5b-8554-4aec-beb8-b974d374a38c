package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.AmmeterRequest
import com.wifochina.modules.group.request.GroupRequest
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.notice.entity.IncomeDivideIntoEntity
import com.wifochina.modules.notice.request.IncomeDivideIntoRequest
import com.wifochina.modules.notice.service.IncomeDivideIntoService
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class IncomeDivideIntoLogDetailService(
    incomeDivideIntoService: IncomeDivideIntoService, logService: LogService
) : AbstractLogDetailService<IncomeDivideIntoRequest, IncomeDivideIntoEntity>(logService, incomeDivideIntoService) {
    override fun detailLog(logDetailContext: LogDetailContext<IncomeDivideIntoRequest>) {
        detailResolve(logDetailContext)
    }

}