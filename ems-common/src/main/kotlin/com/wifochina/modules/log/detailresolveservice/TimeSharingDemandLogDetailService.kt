package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity
import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity
import com.wifochina.modules.strategy.request.TimeSharingBackFlowLimitRequest
import com.wifochina.modules.strategy.request.TimeSharingDemandRequest
import com.wifochina.modules.strategy.service.TimeSharingDemandService
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class TimeSharingDemandLogDetailService(
    timeSharingDemandService: TimeSharingDemandService,
    logService: LogService
    // 这个补值 没有对应的service 是操作的 electric 可以null 因为不涉及 update 和 delete 就可以
) : AbstractLogDetailService<TimeSharingDemandRequest, TimeSharingDemandEntity>(logService, timeSharingDemandService) {
    override fun detailLog(logDetailContext: LogDetailContext<TimeSharingDemandRequest>) {
        detailResolve(logDetailContext)
    }

}