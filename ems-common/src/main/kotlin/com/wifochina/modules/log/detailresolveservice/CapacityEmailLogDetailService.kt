package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.capacity.entity.CapacityEmailEntity
import com.wifochina.modules.capacity.request.CapacityEmailRequest
import com.wifochina.modules.capacity.service.CapacityEmailService
import com.wifochina.modules.demand.entity.DemandEmailEntity
import com.wifochina.modules.demand.request.DemandEmailRequest
import com.wifochina.modules.demand.service.DemandEmailService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class CapacityEmailLogDetailService(
    capacityEmailService: CapacityEmailService, logService: LogService
) : AbstractLogDetailService<CapacityEmailRequest, CapacityEmailEntity>(logService, capacityEmailService) {
    override fun detailLog(logDetailContext: LogDetailContext<CapacityEmailRequest>) {
        detailResolve(logDetailContext)
    }

}