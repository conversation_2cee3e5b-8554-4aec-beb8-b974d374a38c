package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.ControllableEntity
import com.wifochina.modules.group.entity.ControllerEntity
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.AmmeterRequest
import com.wifochina.modules.group.request.ControllableRequest
import com.wifochina.modules.group.request.ControllerRequest
import com.wifochina.modules.group.request.GroupRequest
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.ControllableService
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class ControllableLogDetailService(
    controllableService: ControllableService, logService: LogService
) : AbstractLogDetailService<ControllableRequest, ControllableEntity>(logService, controllableService) {
    override fun detailLog(logDetailContext: LogDetailContext<ControllableRequest>) {
        detailResolve(logDetailContext)
    }

}