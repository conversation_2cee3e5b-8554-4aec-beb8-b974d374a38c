package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.AmmeterRequest
import com.wifochina.modules.group.request.GroupRequest
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.request.ProjectManageRequest
import com.wifochina.modules.project.service.ProjectService
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class ProjectManageLogDetailService(
    projectService: ProjectService , logService: LogService
) : AbstractLogDetailService<ProjectManageRequest, ProjectEntity>(logService, projectService) {
    override fun detailLog(logDetailContext: LogDetailContext<ProjectManageRequest>) {
        detailResolve(logDetailContext)
    }

}