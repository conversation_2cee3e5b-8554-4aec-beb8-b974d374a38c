package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.user.entity.UserEntity
import com.wifochina.modules.user.request.UserRegisterRequest
import com.wifochina.modules.user.service.LogService
import com.wifochina.modules.user.service.UserService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class UserLogDetailService(
    userService: UserService,
    logService: LogService
    // 这个补值 没有对应的service 是操作的 electric 可以null 因为不涉及 update 和 delete 就可以
) : AbstractLogDetailService<UserRegisterRequest, UserEntity>(logService, userService) {
    override fun detailLog(logDetailContext: LogDetailContext<UserRegisterRequest>) {
        detailResolve(logDetailContext)
    }

}