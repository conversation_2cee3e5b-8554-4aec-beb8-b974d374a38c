package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.project.entity.ProjectUrlEntity
import com.wifochina.modules.project.request.ProjectUrlRequest
import com.wifochina.modules.project.service.ProjectUrlService
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class ProjectUrlLogDetailService(
    urlService: ProjectUrlService, logService: LogService
) : AbstractLogDetailService<ProjectUrlRequest, ProjectUrlEntity>(logService, urlService) {
    override fun detailLog(logDetailContext: LogDetailContext<ProjectUrlRequest>) {
        detailResolve(logDetailContext)
    }

}