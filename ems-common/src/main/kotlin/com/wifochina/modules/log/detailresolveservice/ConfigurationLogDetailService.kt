package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.configuration.entity.ConfigurationEntity
import com.wifochina.modules.configuration.request.ConfigurationRequest
import com.wifochina.modules.configuration.service.ConfigurationService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */
@Service
class ConfigurationLogDetailService(
    configurationService: ConfigurationService, logService: LogService
) : AbstractLogDetailService<ConfigurationRequest, ConfigurationEntity>(logService, configurationService) {

    override fun detailLog(logDetailContext: LogDetailContext<ConfigurationRequest>) {
        detailResolve(logDetailContext)
    }

}