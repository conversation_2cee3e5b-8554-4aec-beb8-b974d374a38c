package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.electric.entity.ElectricEntity
import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.AmmeterRequest
import com.wifochina.modules.group.request.GroupRequest
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.remedies.request.DataCalibrationEms
import com.wifochina.modules.remedies.request.DataCalibrationRenewable
import com.wifochina.modules.remedies.service.DataCalibrationService
import com.wifochina.modules.renewable.entity.RenewableProfitEntity
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class DataCalibrationRenewableLogDetailService(
    logService: LogService
    // 这个补值 没有对应的service 是操作的 renewable 可以null 因为不涉及 update 和 delete 就可以
) : AbstractLogDetailService<DataCalibrationRenewable, RenewableProfitEntity>(logService, null) {
    override fun detailLog(logDetailContext: LogDetailContext<DataCalibrationRenewable>) {
        detailResolve(logDetailContext)
    }

}