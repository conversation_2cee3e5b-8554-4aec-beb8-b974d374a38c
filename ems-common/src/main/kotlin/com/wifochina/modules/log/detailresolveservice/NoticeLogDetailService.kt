package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.AmmeterRequest
import com.wifochina.modules.group.request.GroupRequest
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.notice.entity.NoticeEntity
import com.wifochina.modules.notice.request.NoticeRequest
import com.wifochina.modules.notice.service.NoticeService
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class NoticeLogDetailService(
    noticeService: NoticeService, logService: LogService
) : AbstractLogDetailService<NoticeRequest, NoticeEntity>(logService, noticeService) {
    override fun detailLog(logDetailContext: LogDetailContext<NoticeRequest>) {
        detailResolve(logDetailContext)
    }

}