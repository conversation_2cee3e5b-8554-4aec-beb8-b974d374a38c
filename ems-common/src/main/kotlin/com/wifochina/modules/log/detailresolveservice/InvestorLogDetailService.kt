package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.investor.entity.InvestorEntity
import com.wifochina.modules.investor.request.InvestorRequest
import com.wifochina.modules.investor.service.InvestorService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class InvestorLogDetailService(
    investorService: InvestorService, logService: LogService
) : AbstractLogDetailService<InvestorRequest, InvestorEntity>(logService, investorService) {
    override fun detailLog(logDetailContext: LogDetailContext<InvestorRequest>) {
        detailResolve(logDetailContext)
    }

}