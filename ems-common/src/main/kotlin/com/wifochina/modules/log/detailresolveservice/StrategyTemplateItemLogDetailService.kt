package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.configuration.entity.ConfigurationEntity
import com.wifochina.modules.configuration.request.ConfigurationRequest
import com.wifochina.modules.configuration.service.ConfigurationService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity
import com.wifochina.modules.strategytemplate.request.StrategyTemplateItemRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateRequest
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateService
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */
@Service
class StrategyTemplateItemLogDetailService(
    strategyTemplateItemService: StrategyTemplateItemService, logService: LogService
) : AbstractLogDetailService<StrategyTemplateItemRequest, StrategyTemplateItemEntity>(
    logService,
    strategyTemplateItemService
) {

    override fun detailLog(logDetailContext: LogDetailContext<StrategyTemplateItemRequest>) {
        detailResolve(logDetailContext)
    }

}