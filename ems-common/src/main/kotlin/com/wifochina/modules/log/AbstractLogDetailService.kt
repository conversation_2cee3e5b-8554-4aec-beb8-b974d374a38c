package com.wifochina.modules.log

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.JSONObject
import com.alibaba.fastjson2.filter.SimplePropertyPreFilter
import com.baomidou.mybatisplus.extension.service.IService
import com.wifochina.common.log.LogFieldIgnore
import com.wifochina.modules.user.entity.LogEntity
import com.wifochina.modules.user.service.LogService
import io.github.oshai.kotlinlogging.KotlinLogging
import io.swagger.annotations.ApiModelProperty
import org.springframework.stereotype.Service
import java.io.Serializable
import java.lang.reflect.Field
import kotlin.math.abs
import kotlin.reflect.KProperty1
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.full.declaredMembers
import kotlin.reflect.jvm.isAccessible

/**
 * Created on 2025/3/4 15:00.
 * <AUTHOR>
 */
private val log = KotlinLogging.logger { }

@Service
abstract class AbstractLogDetailService<T, ET>(
    val logService: LogService?, val iService: IService<ET>?
) : LogDetailService<T> {

    companion object {
        const val LOG_DETAIL_ADD_FORMAT: String = "%s 设置为 %s"
        const val LOG_DETAIL_UPDATE_FORMAT: String = "%s %s 已改为 %s"
        const val EPSILON_DOUBLE = 1e-8
        const val EPSILON_FLOAT = 1e-6f

    }

    fun detailResolve(logDetailContext: LogDetailContext<T>) {
        val type = logDetailContext.type()
        when (type) {
            OperationType.ADD -> {
                addDetailResolve(logDetailContext)
            }

            OperationType.UPDATE -> {
                updateDetailResolve(logDetailContext)
            }

            OperationType.DEL -> {
                deleteDetailResolve(logDetailContext)
            }

            OperationType.ADD_SIMPLE, OperationType.UPDATE_SIMPLE, OperationType.DEL_SIMPLE -> {
                addDetailSimpleRequestResolve(logDetailContext)
            }

            else -> {}
        }

    }

    private fun deleteDetailResolve(logDetailContext: LogDetailContext<T>) {
        val requestT = logDetailContext.requestT()
        if (requestT is String) {
            //删除的情况 目前看 都是传递的 id
            iService!!.getById(requestT.toString())?.let { et ->
                logDetailContext.logEntity().detail = "删除: " + com.alibaba.fastjson.JSON.toJSONString(et)
            }
        } else {
            getRequestTId(logDetailContext)?.let {
                //删除的情况 目前看 也有传对象的
                iService!!.getById(it)?.let { et ->
                    logDetailContext.logEntity().detail = "删除: " + com.alibaba.fastjson.JSON.toJSONString(et)
                }
            }
        }
    }

    private fun addDetailResolve(logDetailContext: LogDetailContext<T>) {
        logParamsNewChange(
            logDetailContext.requestT(), logDetailContext.logEntity()
        )
    }

    private fun addDetailSimpleRequestResolve(logDetailContext: LogDetailContext<T>) {
        val requestT = logDetailContext.requestT()
        requestT?.let {
            val ignoreFields = requestT!!::class.declaredMemberProperties.filter { property ->
                property.annotations.any { it is LogFieldIgnore }
            }.map { it.name }.toList()
            val filter = SimplePropertyPreFilter()
            filter.excludes.addAll(ignoreFields)
            logDetailContext.logEntity().detail = JSON.toJSONString(requestT, filter)
        }
    }

    private fun getRequestTId(logDetailContext: LogDetailContext<T>): Serializable? {
        val requestT = logDetailContext.requestT()
        // 请求对象不为空 并且 得到请求对象里的 id属性 根据这个id属性 去查询数据里更新前的原始数据eT, 调用logParamsChange 进行比较 放入logEntity 的detail属性里
        return takeIf { requestT != null }?.let {
            requestT!!::class.declaredMembers.filterIsInstance<KProperty1<T, *>>() // 仅查找属性
                //TODO 需要一个个确认 是否都有id 或者 是否有的不叫id
                .firstOrNull { it.name == "id" }?.apply { isAccessible = true }?.get(requestT) as? Serializable
        }
    }

    /**
     * 找到 update request里面的 id -> 查询db 得到old数据
     * 然后 记录 old entity 和 new request  update之间的变更 作为detail 字段存储在logEntity
     */
    private fun updateDetailResolve(logDetailContext: LogDetailContext<T>) {
        val requestT = logDetailContext.requestT()
        // 请求对象不为空 并且 得到请求对象里的 id属性 根据这个id属性 去查询数据里更新前的原始数据eT, 调用logParamsChange 进行比较 放入logEntity 的detail属性里
        getRequestTId(logDetailContext)?.let { id ->
            //拿更新前的数据库原始数据
            iService!!.getById(id)?.let { et ->
                logParamsChange(
                    requestT, JSON.parseObject(JSON.toJSONString(et)), logDetailContext.logEntity()
                )
            }
        }
    }

    private fun logParamsNewChange(arg: Any?, logEntity: LogEntity) {
        val keys: MutableList<String> = java.util.ArrayList()
        if (arg != null) {
            // 获取参数的类类型
            val clazz: Class<*> = arg.javaClass
            // 获取类的所有字段
            val fields = clazz.declaredFields
            // 对每个字段进行比较
            for (field in fields) {
                field.isAccessible = true // 设置字段可访问
                try {
                    if ("serialVersionUID" == field.name) {
                        continue
                    }
                    if (field.getAnnotation(LogFieldIgnore::class.java) != null) continue
                    // 获取字段的值
                    val log = getLogFiled(arg, field)
                    keys.add(log)
                } catch (ignored: IllegalAccessException) {
                }
            }
        }
        // 将方法的参数值也记录到日志中
        logEntity.detail = JSON.toJSONString(keys)
    }

    @Throws(IllegalAccessException::class)
    private fun getLogFiled(arg: Any, field: Field): String {
        val newValue = field[arg]
        // 获取字段的注解（如果有的话）
        val apiModelProperty = field.getAnnotation(ApiModelProperty::class.java)
        // 获取字段的描述（如果注解存在）
        val description = apiModelProperty?.value ?: field.name
        return String.format(LOG_DETAIL_ADD_FORMAT, description, newValue)
    }

    /**
     * 勇子写的 直接拿过来用
     */
    private fun logParamsChange(arg: Any?, jsonObject: JSONObject?, logEntity: LogEntity) {
        val keys: MutableList<String> = ArrayList()
        if (arg != null && jsonObject != null) {
            // 获取参数的类类型
            val clazz: Class<*> = arg.javaClass
            // 获取类的所有字段
            val fields = clazz.declaredFields
            // 对每个字段进行比较
            for (field in fields) {
                field.isAccessible = true // 设置字段可访问
                try {
                    if ("serialVersionUID" == field.name) {
                        continue
                    }
                    if (field.getAnnotation(LogFieldIgnore::class.java) != null) continue
                    // 获取字段的值
                    val newValue = field[arg]
                    // 获取字段的注解（如果有的话）
                    val apiModelProperty = field.getAnnotation(ApiModelProperty::class.java)
                    // 获取字段的描述（如果注解存在）
                    val description = apiModelProperty?.value ?: field.name
                    // 获取字段的旧值
                    val oldValue = jsonObject[field.name]

                    if (newValue is Float) {
                        handleFloat(field, newValue, oldValue, description, keys)
                    } else if (newValue is Double) {
                        handleDouble(field, newValue, oldValue, description, keys)
                    } else if (newValue == null && oldValue != null) {
                        //这种属于 把以前的值给去除了
                        val log = String.format(
                            LOG_DETAIL_UPDATE_FORMAT, description, oldValue, null
                        )
                        keys.add(log)
                    } else if (newValue != null && newValue != oldValue) {
                        //这种属于 把更改
                        val log = String.format(
                            LOG_DETAIL_UPDATE_FORMAT, description, oldValue, newValue
                        )
                        keys.add(log)
                    }
                } catch (ignored: IllegalAccessException) {
                }
            }
        }
        if (keys.isNotEmpty()) {
            // 将方法的参数值也记录到日志中
            logEntity.detail = JSON.toJSONString(keys)
        }
    }

    private fun handleDouble(
        field: Field, newValue: Double, oldValue: Any?, description: String, keys: MutableList<String>
    ) {
        // 比较 double 类型时使用的误差范围
        val newDoubleValue = newValue
        if (oldValue == null) {
            val log = String.format(
                LOG_DETAIL_UPDATE_FORMAT, description, null, newDoubleValue
            )
            keys.add(log)
            return
        }
        val oldDoubleValue = oldValue.toString().toDouble()
        // 比较两个 double 类型的值是否相等，使用误差范围来判断
        if (abs(newDoubleValue - oldDoubleValue) > EPSILON_DOUBLE) {
            val log = String.format(
                LOG_DETAIL_UPDATE_FORMAT, description, oldDoubleValue, newDoubleValue
            )
            keys.add(log)
        }
    }


    private fun handleFloat(
        field: Field, newValue: Float, oldValue: Any?, description: String, keys: MutableList<String>
    ) {
        // 比较 float 类型时使用的误差范围
        val newFloatValue = newValue
        if (oldValue == null) {
            val log = String.format(
                LOG_DETAIL_UPDATE_FORMAT, description, null, newFloatValue
            )
            keys.add(log)
            return
        }
        val oldFloatValue = oldValue.toString().toFloat()
        // 比较两个 float 类型的值是否相等，使用误差范围来判断
        if (abs((newFloatValue - oldFloatValue).toDouble()) > EPSILON_FLOAT) {
            val log = String.format(
                LOG_DETAIL_UPDATE_FORMAT, description, oldFloatValue, newFloatValue
            )
            keys.add(log)
        }
    }


}