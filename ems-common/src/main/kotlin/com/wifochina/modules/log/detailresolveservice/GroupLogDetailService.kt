package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.GroupRequest
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class GroupLogDetailService(
    groupService: GroupService,
    logService: LogService
) : AbstractLogDetailService<GroupRequest, GroupEntity>(logService, groupService) {
    override fun detailLog(logDetailContext: LogDetailContext<GroupRequest>) {
        detailResolve(logDetailContext)
    }

}