package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.demand.entity.DemandEmailEntity
import com.wifochina.modules.demand.request.DemandEmailRequest
import com.wifochina.modules.demand.service.DemandEmailService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class DemandEmailLogDetailService(
    demandEmailService: DemandEmailService, logService: LogService
) : AbstractLogDetailService<DemandEmailRequest, DemandEmailEntity>(logService, demandEmailService) {
    override fun detailLog(logDetailContext: LogDetailContext<DemandEmailRequest>) {
        detailResolve(logDetailContext)
    }

}