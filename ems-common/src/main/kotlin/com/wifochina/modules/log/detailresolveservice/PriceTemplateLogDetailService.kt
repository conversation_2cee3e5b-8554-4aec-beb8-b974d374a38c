package com.wifochina.modules.log.detailresolveservice

import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.AmmeterRequest
import com.wifochina.modules.group.request.GroupRequest
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.log.AbstractLogDetailService
import com.wifochina.modules.log.LogDetailContext
import com.wifochina.modules.pricetemplate.entity.PriceTemplateEntity
import com.wifochina.modules.pricetemplate.request.PriceTemplateRequest
import com.wifochina.modules.pricetemplate.service.PriceTemplateService
import com.wifochina.modules.pricetemplate.service.impl.PriceTemplateServiceImpl
import com.wifochina.modules.user.service.LogService
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/4 14:26.
 * <AUTHOR>
 */


@Service
class PriceTemplateLogDetailService(
    priceTemplateService: PriceTemplateService, logService: LogService
) : AbstractLogDetailService<PriceTemplateRequest, PriceTemplateEntity>(logService, priceTemplateService) {
    override fun detailLog(logDetailContext: LogDetailContext<PriceTemplateRequest>) {
        detailResolve(logDetailContext)
    }

}