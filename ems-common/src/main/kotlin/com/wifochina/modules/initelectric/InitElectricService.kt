package com.wifochina.modules.initelectric

import com.wifochina.common.constants.EmsFieldEnum
import com.wifochina.common.constants.MeterFieldEnum
import com.wifochina.common.influx.FluxAdapter
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.client.InfluxClientService
import com.wifochina.modules.diagram.VO.ValueVO
import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.DeviceEntity
import com.wifochina.modules.group.entity.IdSearchSupport
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.DeviceService
import com.wifochina.modules.project.entity.ProjectEntity
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneId

/**
 *from(bucket: "ems_forever")
 *   |> range(start: 2024-09-19T00:00:00.000Z, stop: 2024-09-20T00:00:00.000Z	)
 *   |> filter(fn: (r) => r["_measurement"] == "ems100@22960")
 *   |> filter(fn: (r) => r["_field"] == "ems_history_output_energy")
 *   |> filter(fn: (r) => r["deviceId"] == "5e97c2efb0f34845b6d674d349a49262")
 *   |> filter(fn: (r) => r["projectId"] == "ae9bfb9d62d94407b5cabbbb64c102a0")
 *   |>first()
 * //   |> sort(columns: ["_time"], desc: false)
 * //   |> limit(n: 100)
 * Created on 2025/5/15 15:06.
 * <AUTHOR>
 */

private val log = KotlinLogging.logger { }

@Service
class InitElectricService(
    val influxClientService: InfluxClientService, val deviceService: DeviceService, val ammeterService: AmmeterService
) {

    fun reduceInit(total: Double, type: String, idSearchSupport: IdSearchSupport): Double {
        var result: Double = total
        return when (idSearchSupport.type()) {
            EmsConstants.DEVICE -> {
                ((idSearchSupport) as DeviceEntity).let {
                    if (type == EmsConstants.IN) {
                        result = total - if (it.projectRunInitInData == null) 0.0 else it.projectRunInitInData
                    }
                    if (type == EmsConstants.OUT) {
                        result = total - if (it.projectRunInitOutData == null) 0.0 else it.projectRunInitOutData
                    }
                    result
                }
            }

            EmsConstants.AMMETER -> {
                ((idSearchSupport) as AmmeterEntity).let {
                    if (type == EmsConstants.IN) {
                        result = total - if (it.projectRunInitInData == null) 0.0 else it.projectRunInitInData
                    }
                    if (type == EmsConstants.OUT) {
                        result = total - if (it.projectRunInitOutData == null) 0.0 else it.projectRunInitOutData
                    }
                    result
                }
            }

            else -> {
                result
            }
        }
    }

    fun updateProjectAll(projectEntity: ProjectEntity) {
        val devices = deviceService.getDevicesByPid(projectEntity.id);
        val meters = ammeterService.findAllMeters(projectEntity.id);
        devices.forEach {
            getInitElectric(projectEntity, it)?.let { data ->
                it.projectRunInitInData = if (data.inData.value == null) 0.0 else data.inData.value
                it.projectRunInitOutData = if (data.outData.value == null) 0.0 else data.outData.value
                deviceService.updateById(it)
            }
        }
        meters.forEach {
            getInitElectric(projectEntity, it)?.let { data ->
                it.projectRunInitInData = if (data.inData.value == null) 0.0 else data.inData.value
                it.projectRunInitOutData = if (data.outData.value == null) 0.0 else data.outData.value
                ammeterService.updateById(it)
            }
        }

    }

    fun getInitElectric(projectEntity: ProjectEntity, idSearchSupport: IdSearchSupport): RunProjectInitData? {
        return when (idSearchSupport.type()) {
            EmsConstants.DEVICE -> {
                (idSearchSupport as DeviceEntity).id
            }

            EmsConstants.AMMETER -> {
                (idSearchSupport as AmmeterEntity).id
            }

            else -> {
                null
            }

        }?.let { equipmentId ->
            val projectRunZeroTime =
                Instant.ofEpochSecond(projectEntity.createTime).atZone(ZoneId.of(projectEntity.timezone)).toLocalDate()
                    .atStartOfDay(ZoneId.of(projectEntity.timezone)).toEpochSecond()
            val map = FluxAdapter.query(
                FluxAdapter.builder().projectId(projectEntity.id).timeZoneSet(false)
                    .from(influxClientService.bucketForever).range(
                        projectRunZeroTime, projectRunZeroTime + EmsConstants.ONE_DAY_SECOND
                    ).measurement(
                        if (idSearchSupport.type().equals(EmsConstants.DEVICE)) {
                            influxClientService.getEmsTable(projectEntity.id)
                        } else {
                            influxClientService.getMeterTable(projectEntity.id)
                        }
                    ).equipmentIds(
                        listOf(equipmentId)
                    ).fields(
                        if (idSearchSupport.type().equals(EmsConstants.DEVICE)) {
                            listOf(
                                EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field(),
                                EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field()
                            )
                        } else {
                            listOf(
                                MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                                MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field()
                            )
                        }
                    ).toFlux().first().toString()
            ).handleResult().toMapEachFieldOnlyEquipmentOnlyValue()
            //查询得到结果
            val result = when (idSearchSupport.type()) {
                EmsConstants.DEVICE -> {
                    RunProjectInitData(
                        equipmentId,
                        idSearchSupport.type(),
                        map[EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field()] ?: ValueVO(),
                        map[EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field()] ?: ValueVO()
                    )

                }

                EmsConstants.AMMETER -> {
                    RunProjectInitData(
                        equipmentId,
                        idSearchSupport.type(),
                        map[MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field()] ?: ValueVO(),
                        map[MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field()] ?: ValueVO()
                    )
                }

                else -> {
                    null
                }
            }
            log.info {
                "initEnergy ${idSearchSupport.type()} projectName:${projectEntity.projectName} " + "equipmentId:${idSearchSupport.id()} has init inData :${result?.inData} outData:${result?.outData} "
            }
            result

        }
    }

    class RunProjectInitData(
        val equipmentId: String, val equipmentType: String, val inData: ValueVO, val outData: ValueVO
    )
}