package com.wifochina.modules.demand.service.impl

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.influxdb.query.dsl.functions.restriction.Restrictions
import com.wifochina.common.config.SystemConfiguration
import com.wifochina.common.constants.EmsFieldEnum
import com.wifochina.common.constants.MeterFieldEnum
import com.wifochina.common.influx.FluxAdapter
import com.wifochina.common.request.RangeRequest
import com.wifochina.common.time.MyTimeUtil
import com.wifochina.common.util.DemandCalcModeEnum
import com.wifochina.common.util.EmsConstants
import com.wifochina.common.util.EmsUtil
import com.wifochina.common.util.StringUtil
import com.wifochina.common.utils.toFormattedDate
import com.wifochina.common.utils.toMillis
import com.wifochina.modules.client.InfluxClientService
import com.wifochina.modules.demand.entity.DemandLastRecordEntity
import com.wifochina.modules.demand.mapper.DemandLastRecordMapper
import com.wifochina.modules.demand.service.DemandModelStrategy.OaDemandCalParams
import com.wifochina.modules.demand.service.NewDemandServiceKt
import com.wifochina.modules.demand.vo.OaDemandData
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.GroupAmmeterService
import com.wifochina.modules.group.service.GroupDeviceService
import com.wifochina.modules.project.entity.ProjectEntity
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import kotlin.coroutines.CoroutineContext
import kotlin.math.abs

private val log = KotlinLogging.logger { }

/**
 * 新的 需量Service kt 版本
 * Created on 2025/1/3 13:49.
 * <AUTHOR>
 */
@Component
class NewDemandServiceKtImpl(
    val ammeterService: AmmeterService,
    val groupAmmeterService: GroupAmmeterService,
    val groupDeviceService: GroupDeviceService,
    val influxClientService: InfluxClientService,
    val demandLastRecordMapper: DemandLastRecordMapper,
    val demandModelStrategyTypeChooser: DemandModelStrategyTypeChooser,
    val systemConfiguration: SystemConfiguration,
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
    val coroutineContext: CoroutineContext,
) : NewDemandServiceKt {

    fun queryLastDemandCalTime(demandCalContext: NewDemandServiceKt.DemandCalContext, equipments: List<String>): Long? {
        val demandLastRecordEntity: DemandLastRecordEntity? = demandLastRecordMapper.selectOne(
            LambdaQueryWrapper<DemandLastRecordEntity>().eq(
                DemandLastRecordEntity::getProjectId, demandCalContext.project!!.id
            ).eq(DemandLastRecordEntity::getGroupId, demandCalContext.group!!.id)
        )
        //如果刚上线或者什么情况没有了这个数据, 这个感觉不需要 异常情况只需要手动在上面那个表里插入即可
        //如果没有 则 查询一下 3小时内的最新的time点..
        return demandLastRecordEntity?.time ?: run {
            //如果没有查询到 比如刚上线的情况
            //如果没有 则 查询一下 3小时内的最新的time点..
            // 当前时间 - 3 小时
            val preTIme = ZonedDateTime.now(ZoneId.of(demandCalContext.project!!.timezone))
                .minusHours(systemConfiguration.demand.demandLastPreHours.toLong())
            val query = FluxAdapter.query(
                FluxAdapter.builder().project(demandCalContext.project).from(demandCalContext.bucket)
                    .range(preTIme.toEpochSecond(), Instant.now().epochSecond)
                    .measurement(influxClientService.getMeterTable(demandCalContext.project!!.id))
                    .equipmentIds(equipments).fields(listOf(MeterFieldEnum.AC_ACTIVE_POWER.field())).toFlux().sort(
                        listOf(EmsConstants.INFLUX_TIME), true
                    ).limit(1).toString()
            )
            val onlyOneValue = query.handleResult().onlyOneValue()
            //先插入一条 为了处理刚上线的情况
            onlyOneValue.time?.let {
                val lastNewDemand = DemandLastRecordEntity()
                lastNewDemand.time = onlyOneValue.time
                lastNewDemand.projectId = demandCalContext.project!!.id
                lastNewDemand.groupId = demandCalContext.group!!.id
                demandLastRecordMapper.insert(lastNewDemand)
            }
            onlyOneValue.time
        }

    }

    /**
     * 窗口offset 计算
     * 具体步骤
     * 假设：
     *
     * start = 08:32（以分钟为单位是 32 分钟）。
     * 滑动窗口基准是 15 分钟（即窗口对齐点是 0、15、30、45 分钟）。
     * 计算过程：
     *
     * 计算当前分钟数：
     *
     * makefile
     * 复制代码
     * current_minutes = (小时数 * 60) + 分钟数
     * current_minutes = (8 * 60) + 32 = 512
     * 找到离当前分钟最近的基准点：
     *
     * scss
     * 复制代码
     * 基准点 = (current_minutes // 15) * 15 = (512 // 15) * 15 = 510
     * 计算偏移量：
     *
     * makefile
     * 复制代码
     * offset = current_minutes - 基准点
     * offset = 512 - 510 = 2
     * 结果：
     *
     * offset = -2m，表示窗口需要向前偏移 2 分钟。
     *
     *
     * 举例
     * 假设：
     *
     * range(start: 08:32, stop: 08:48)
     * aggregateWindow(every: 15m, fn: mean)
     * 窗口划分：
     *
     * 默认对齐点是 0、15、30、45 分钟。
     * 即使你的 start 是 08:32，窗口依然会从 08:30 对齐：
     * 第一个窗口：08:30 -> 08:45
     * 第二个窗口：08:45 -> 09:00
     */
    @Deprecated("old calculateOffset")
    fun calculateOffset(zoneId: ZoneId, timestamp: Long, period: Int): String {
        // 将时间戳转换为 ZonedDateTime（假设为 UTC 时间）
        val dateTime = Instant.ofEpochSecond(timestamp).atZone(zoneId)
        val hours = dateTime.hour
        val minutes = dateTime.minute

        // 转换为分钟数
        val currentMinutes = hours * 60 + minutes

        // 计算 15 分钟窗口的基准点
        val base = period
        val nearestBase = (currentMinutes / base) * base

        // 偏移量
        val offset = currentMinutes - nearestBase
        return "${if (offset >= 0) "" else "-"}${abs(offset)}m"
    }

    /**
     * 窗口offset 计算
     * 具体步骤
     * 假设：
     *
     * start = 08:32（以分钟为单位是 32 分钟）。
     * 滑动窗口基准是 15 分钟（即窗口对齐点是 0、15、30、45 分钟）。
     * 计算过程：
     *
     * 计算当前分钟数：
     *
     * makefile
     * 复制代码
     * current_minutes = (小时数 * 60) + 分钟数
     * current_minutes = (8 * 60) + 32 = 512
     * 找到离当前分钟最近的基准点：
     *
     * scss
     * 复制代码
     * 基准点 = (current_minutes // 15) * 15 = (512 // 15) * 15 = 510
     * 计算偏移量：
     *
     * makefile
     * 复制代码
     * offset = current_minutes - 基准点
     * offset = 512 - 510 = 2
     * 结果：
     *
     * offset = 2m，表示窗口需要向后偏移 2 分钟。
     *
     *
     * 举例
     * 假设：
     *
     * range(start: 08:32, stop: 08:48)
     * aggregateWindow(every: 15m, fn: mean)
     * 窗口划分：
     *
     * 默认对齐点是 0、15、30、45 分钟。
     * 即使你的 start 是 08:32，窗口依然会从 08:30 对齐：
     * 第一个窗口：08:30 -> 08:45
     * 第二个窗口：08:45 -> 09:00
     *
     *
     * 如果start 是 08:44 stop: 08:59 那么窗口会 按照 08:30 对齐
     * 第一个窗口：08:30 -> 08:45
     * 第二个窗口：08:46 -> 08:59  这部分因为不够15分钟 所以都不对 给的是结果数据 比如  08:47 就是 08:46 和 08:47的平均 ,  08:48就是 08:46 47 48的平均 依次类推
     * // 所以这里offset要偏移14 这样想哈 要计算8:44这个点的前15分钟平均, 那么 它的窗口应该要 现在会被分为 30 - 45 , 44会被计算30->44的 不对,  所以把窗口 offset 14 让窗口从 44->44+15 开始
     * 那么只会有一个窗口 08:44 -> 08:59 了  一个15分钟间隔平均 即计算的也是正确
     */
    fun calculateOffsetNew(zoneId: ZoneId, timestamp: Long, period: Int): Long {
        // 将时间戳转换为 ZonedDateTime（假设为 UTC 时间）
        val dateTime = Instant.ofEpochSecond(timestamp).atZone(zoneId)
        val hours = dateTime.hour
        val minutes = dateTime.minute

        // 转换为分钟数
        val currentMinutes = hours * 60 + minutes

        // 计算 15 分钟窗口的基准点
        val base = period
        val nearestBase = (currentMinutes / base) * base

        // 偏移量
        val offset = currentMinutes - nearestBase
        return offset.toLong()
    }


    /**
     * 使用 window 窗口 实现 滑动窗口的效果
     *from(bucket:"ems_cloud_dev")
     * 	|> range(start:1736487900, stop:1736490600)
     * 	|> filter(fn: (r) => r["_measurement"] == "meter@28689")
     * 	|> filter(fn: (r) => r["projectId"] == "4f537620d37d40e19dd25be5ca6ad941")
     * 	|> filter(fn: (r) => (r["meterId"] == "5b2fe5975295437a88aea4d3142341a3"))
     * 	|> filter(fn: (r) => (r["_field"] == "ac_active_power"))
     * 	|> window(every:1m, period: 15m, offset: 0m)
     *  |> mean()
     * 	|> map(fn: (r) => ({r with duration: int(v: r._stop) - int(v: r._start),_time: r._stop}))
     * 	|> filter(fn: (r) => r["duration"] == 900000000000)
     *
     *
     * 	解释一下offset window会固定从period点进行窗口划分 如果不offset, 则比如 0-15 15-30 30-45这样的窗口, 那么如果我们要计算 16这个点的数据(其实要的是01-16这个平均)
     * 	那么会传递start = 01 end = 16 , 那么窗口会划分为 00->15 16-> 16 得出2个数据 , 16这个数据是 16这1分钟的平均 不是前15分钟平均 大概就是offset的意义
     *
     * 	map 是用来过滤不满足时间窗口是15分钟的, 其实现在支持批量计算多个 滑动窗口分钟的平均值, 会range 大范围, 这时候 比如尾部数据不够period的时候 如果没有过滤,那么最后的数据是不对的
     */
    fun queryDemandDataWithSlideWindow(
        rangeRequest: RangeRequest,
        measurement: String,
        field: String,
        equipments: List<String>,
        demandCalContext: NewDemandServiceKt.DemandCalContext
    ): Map<String, Map<Long, Double>> {
        val group = demandCalContext.group!!
        val project = demandCalContext.project!!
        //这里 不管是 固定还是滑差 都把 周期返回即可,  滑差的话 返回周期 交给influxdb去算这个周期内的平均即可
        val windowPeriod =
            (if (DemandCalcModeEnum.isSliding(group.demandCalcModel)) group.demandPeriod else group.demandPeriod).toLong()
        // 查询需量数据
        val equipmentDemandMap = FluxAdapter.query(
            FluxAdapter.builder().project(demandCalContext.project).from(influxClientService.bucketRealtime)
                .range(rangeRequest.startDate, rangeRequest.endDate).measurement(measurement).fields(listOf(field))
                .equipmentIds(equipments).toFlux().window(
                    1,
                    ChronoUnit.MINUTES,
                    windowPeriod,
                    ChronoUnit.MINUTES,
                    calculateOffsetNew(ZoneId.of(project.timezone), rangeRequest.startDate, group.demandPeriod),
                    ChronoUnit.MINUTES
                ).mean().map("({r with duration: int(v: r._stop) - int(v: r._start),_time: r._stop})")
                .filter(Restrictions.column("duration").equal(group.demandPeriod * 60 * 1000000000L)).toString()
        ).handleResult().toMapEachEquipment()
        log.info {
            "demand slideWindow 项目Id:${project.id} 分组Id:${group.id} ${
                (rangeRequest.startDate + group.demandPeriod * 60).toMillis()
                    .toFormattedDate(zoneId = ZoneId.of(project.timezone))
            } measurement:${measurement} map: $equipmentDemandMap"
        }
        val mapValues = equipmentDemandMap.mapValues { entry ->
            entry.value.associate { it.time to it.value }
        }
        return mapValues
    }

    /**
     * 查询rangeRequest范围内的 需量数据 支持 固定和滑差计算,只需要传递好rangeRequest
     * 注意有一个 withOffset  需要使用 calculateOffset 才能准确的计算出 滑差15/or分钟平均的 值不然是从 整点进行窗口
     */
    @Deprecated("old method ")
    fun queryDemandData(
        rangeRequest: RangeRequest,
        measurement: String,
        field: String,
        equipments: List<String>,
        demandCalContext: NewDemandServiceKt.DemandCalContext
    ): Map<String, Map<Long, Double>> {
        val group = demandCalContext.group!!
        val project = demandCalContext.project!!
        //这里 不管是 固定还是滑差 都把 周期返回即可,  滑差的话 返回周期 交给influxdb去算这个周期内的平均即可
        val windowPeriod =
            (if (DemandCalcModeEnum.isSliding(group.demandCalcModel)) group.demandPeriod else group.demandPeriod).toLong()
        // 查询需量数据
        val equipmentDemandMap = FluxAdapter.query(
            FluxAdapter.builder().project(demandCalContext.project).from(influxClientService.bucketRealtime)
                .range(rangeRequest.startDate, rangeRequest.endDate).measurement(measurement).fields(listOf(field))
                .equipmentIds(equipments).toFlux().aggregateWindow(
                    windowPeriod, ChronoUnit.MINUTES, EmsConstants.INFLUX_MEAN_FUNC
                ).withOffset(
                    calculateOffset(
                        ZoneId.of(project.timezone), rangeRequest.startDate, group.demandPeriod
                    )
                ).toString()
        ).handleResult().onlyOneMap(equipments)
        log.info {
            "queryDemandData 项目Id:${project.id} 分组Id:${group.id} ${
                rangeRequest.startDate.toMillis().toFormattedDate(zoneId = ZoneId.of(project.timezone))
            } measurement:${measurement} map: $equipmentDemandMap"
        }
        return equipmentDemandMap.toMap().mapValues { (_, valueVo) ->
            mapOf(valueVo.time to valueVo.value)
        }
    }


    /**
     * 对标以前的getOriginAndActualDemand 这个方法 类似的实现 结构和 返回结构
     */
    override fun platformDemandCal(
        rangeRequest: RangeRequest, contextBuilder: NewDemandServiceKt.DemandCalContext.() -> Unit
    ): Map<Long, OaDemandData>? {
        val demandCalContext = object : NewDemandServiceKt.DemandCalContext {
            override var bucket: String? = null
            override var group: GroupEntity? = null
            override var project: ProjectEntity? = null
            override fun checkParams(): Boolean {
                return !(StringUtil.isEmpty(bucket) || group == null || project == null)
            }
        }
        demandCalContext.contextBuilder()
        return takeIf { demandCalContext.checkParams() }?.let {
            val group = demandCalContext.group
            val project = demandCalContext.project!!
            val result = mutableMapOf<Long, OaDemandData>()
            group!!.demandPeriod?.let {
                EmsUtil.getGridMeterIdsByGroupId(
                    group.id, ammeterService, groupAmmeterService
                ).takeIf { it.isNotEmpty() }?.let { meterList ->
                    val deviceList = EmsUtil.getDeviceIdsByGroupId(group.id, groupDeviceService)
                    //原始逻辑有 setDataMap方法 计算电表和EMS的数据
                    val queryLastDemandCalTime = queryLastDemandCalTime(demandCalContext, meterList)
                    log.debug { "项目Id:${project.id} 项目名称:${project.projectName} queryLastDemandCalTime $queryLastDemandCalTime" }
                    //TEST
                    val now = Instant.now().epochSecond;
                    queryLastDemandCalTime?.let {
                        //如果是滑差1分钟的比较好弄,就是每分钟1个点, 如果是固定的按照 15或者周期 只去算特定的 周期的点
                        val interval = when (group.demandCalcModel) {
                            DemandCalcModeEnum.SLIDING.model -> group.slipTime
                            DemandCalcModeEnum.FIXED.model -> group.demandPeriod
                            else -> group.demandPeriod
                        }
                        // 这个是应该要去计算多少次 根据最新计算成功的 到目前的
                        val calLoops = MyTimeUtil.generateMinuteList(
                            queryLastDemandCalTime, now, interval, ZoneId.of(project.timezone)
                        )
                        log.debug { "项目Id:${project.id} 项目名称:${project.projectName} interval:$interval calLoops $calLoops" }
                        // 这个180提出去 可以配置, 控制一下
                        val minLoop =
                            calLoops.takeLast(systemConfiguration.demand.demandExtrusionMaxTake).minOfOrNull { it }
                        val maxLoop =
                            calLoops.takeLast(systemConfiguration.demand.demandExtrusionMaxTake).maxOfOrNull { it }
                        // 这里的意思就是 我需要去计算 哪些点 找到最大最小的点
                        minLoop?.run {
                            // 用的是window 窗口, 那么第一个点 minLoop其实是我想算的 第一个平均点,
                            // 所以要把 range 范围根据 period 扩展到 能够算出第一个点的平均的地方
                            val rangeStart = this - group.demandPeriod * 60
                            // 封装range request
                            val loopRangeRequest = RangeRequest().setStartDate(rangeStart).setEndDate(maxLoop)
                            demandRawData(
                                loopRangeRequest, demandCalContext, meterList, deviceList
                            )?.let { (meterDataMap, deviceDataMap) ->
                                val modelCalResultMap =
                                    //不再有代码计算 滑差了交给了 influxdb.所以这里直接固定model的策略即可
                                    demandModelStrategyTypeChooser.chooseStrategy(DemandCalcModeEnum.FIXED.model)
                                        .modelCal(
                                            OaDemandCalParams().setMeterDataMap(meterDataMap)
                                                .setDeviceDataMap(deviceDataMap).setDeviceList(deviceList)
                                                .setMeterList(meterList)
                                                .setInterval(systemConfiguration.demand.demandJobCalcInterval.toLong())
                                        )
                                result.putAll(
                                    modelCalResultMap
                                )
                            }
                        }
                        log.debug { "项目Id:${project.id} 项目名称:${project.projectName} result $result" }
                        result.forEach { (key, value) ->
                            log.info {
                                "项目Id:${project.id} 分组Id:${group.id}${
                                    key.toMillis().toFormattedDate(zoneId = ZoneId.of(project.timezone))
                                }, $value"
                            }
                        }
                        result.takeIf { it.isNotEmpty() }?.entries?.maxByOrNull { it.key }?.let {
                            // result里面最后的一个 的time 插入数据库
                            demandLastRecordMapper.selectOne(
                                LambdaQueryWrapper<DemandLastRecordEntity>().eq(
                                    DemandLastRecordEntity::getProjectId, project.id
                                ).eq(DemandLastRecordEntity::getGroupId, group.id)
                            )?.let { demandLastRecord ->
                                demandLastRecord.time = it.key
                                demandLastRecordMapper.updateById(demandLastRecord)
                            }
                        }
                    }
                }
                    ?: log.warn { "projectId ${project.id} groupId:${group.id} 查询不到 queryLastDemandCalTime 无法计算, 检查数据上报是否异常,或者断线" }
            } ?: run {
                log.error { "projectId: ${project.id}  groupId:${group.id} 没有找到demandPeriod配置 无法执行需量job平台计算" }
                null
            }
            result
        }
    }

    private fun demandRawData(
        rangeRequest: RangeRequest,
        demandCalContext: NewDemandServiceKt.DemandCalContext,
        meterList: List<String>,
        deviceList: MutableList<String>
    ): Pair<Map<String, Map<Long, Double>>, Map<String, Map<Long, Double>>>? {
        // 封装range request
        val meterDataMap = queryDemandDataWithSlideWindow(
            rangeRequest,
            influxClientService.getMeterTable(demandCalContext.project!!.id),
            MeterFieldEnum.AC_ACTIVE_POWER.field(),
            meterList,
            demandCalContext
        )
        val commonTimeList = meterDataMap.values.flatMap { map -> map.map { it.key } } // 将所有 time 值提取出来
            .distinct() // 去重
            .sorted() // 排序
        if (commonTimeList.isEmpty()) {
            // 为了减少不必要的查询. 如果没有电表的 就不需要再查询ems的数据了
            return null
        }
        val deviceDataMap = queryDemandDataWithSlideWindow(
            rangeRequest,
            influxClientService.getEmsTable(demandCalContext.project!!.id),
            EmsFieldEnum.EMS_AC_ACTIVE_POWER.field(),
            deviceList,
            demandCalContext
        )
        return Pair(meterDataMap, deviceDataMap)
    }
}
//TODO 老的 实现  有一定的性能问题, 是把每分钟的数据 去查询一次 暂时先留着 新改的稳定后删除
//                    calLoops.takeLast(systemConfiguration.demand.demandExtrusionMaxTake).sorted()
//                        .forEach { loopEnd ->
//                            val loopStart: Long = loopEnd - (group.demandPeriod * 60)
//                            val loopRangeRequest = RangeRequest().setStartDate(loopStart).setEndDate(loopEnd)
//                            val meterDataMap = queryDemandData(
//                                loopRangeRequest,
//                                influxClientService.getMeterTable(demandCalContext.project!!.id),
//                                MeterFieldEnum.AC_ACTIVE_POWER.field(),
//                                meterList,
//                                demandCalContext
//                            )
//                            val deviceDataMap = queryDemandData(
//                                loopRangeRequest,
//                                influxClientService.getEmsTable(demandCalContext.project!!.id),
//                                EmsFieldEnum.EMS_AC_ACTIVE_POWER.field(),
//                                deviceList,
//                                demandCalContext
//                            )
//                            val modelCalResultMap =
//                                //不再有代码计算 滑差了交给了 influxdb.所以这里直接固定model的策略即可
//                                demandModelStrategyTypeChooser.chooseStrategy(DemandCalcModeEnum.FIXED.model)
//                                    .modelCal(
//                                        OaDemandCalParams().setMeterDataMap(meterDataMap)
//                                            .setDeviceDataMap(deviceDataMap).setDeviceList(deviceList)
//                                            .setMeterList(meterList)
//                                            .setInterval(systemConfiguration.demand.demandJobCalcInterval.toLong())
//                                    )
//                            result.putAll(modelCalResultMap