package com.wifochina.modules.demand.service

import com.wifochina.common.request.RangeRequest
import com.wifochina.modules.demand.vo.OaDemandData
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.project.entity.ProjectEntity

/**
 * Created on 2025/1/3 13:42.
 * <AUTHOR>
 */
interface NewDemandServiceKt {

    interface DemandCalContext {
        var project: ProjectEntity?
        var group: GroupEntity?
        var bucket: String?
        fun checkParams(): Boolean
    }

    /**
     * 平台需量的计算方法
     */
    fun platformDemandCal(
        rangeRequest: RangeRequest, contextBuilder: DemandCalContext.() -> Unit
    ): Map<Long, OaDemandData>?
}