package com.wifochina.modules.demand.service.strategy

import com.wifochina.common.util.DemandCalcModeEnum
import com.wifochina.modules.demand.service.DemandModelStrategy
import com.wifochina.modules.demand.vo.OaDemandData
import org.springframework.stereotype.Component

/**
 * 使用  kt 重写的 并且进行了一点细微调整的 新的 固定策略的 需量计算方式
 * Created on 2025/1/6 10:05.
 * <AUTHOR>
 */
@Component
class DemandModelFixedStrategy : DemandModelStrategy {
    override fun modelCal(oaDemandCalParams: DemandModelStrategy.OaDemandCalParams): MutableMap<Long, OaDemandData> {
        val meterDataMap = oaDemandCalParams.meterDataMap
        val deviceDataMap = oaDemandCalParams.deviceDataMap
        val commonTimeList = meterDataMap.values.flatMap { map -> map.keys }.distinct().sorted().toList()

        return let {
            val resultMap = mutableMapOf<Long, OaDemandData>()
            commonTimeList.forEach { time ->
                var deviceDiffValue = 0.0
                var meterDiffValue = 0.0
                var dataValid = true
                oaDemandCalParams.deviceList.forEach deviceLoop@{ deviceId ->
                    val deviceData = deviceDataMap[deviceId]
                    if (deviceData == null || deviceData[time] == null) {
                        dataValid = false
                        return@deviceLoop
                    }
                    deviceData[time]?.let { data ->
                        deviceDiffValue += data
                    }
                }
                oaDemandCalParams.meterList.forEach meterLoop@{ meterId ->
                    val meterData = meterDataMap[meterId]
                    if (meterData == null || meterData[time] == null) {
                        dataValid = false
                        return@meterLoop
                    }
                    meterData[time]?.let { data ->
                        meterDiffValue += data
                    }
                }
                if (dataValid) {
                    //实际需量 就是关口电表的
                    val actualDemand = -meterDiffValue / 1000
                    //原需量 关口电表 + 设备的
                    val originDemand = actualDemand + deviceDiffValue
                    resultMap[time] = OaDemandData().setOrigin(originDemand).setActual(actualDemand).setTime(time)
                }
            }
            resultMap
        }
    }

    override fun type(): Int {
        return DemandCalcModeEnum.FIXED.model
    }
}