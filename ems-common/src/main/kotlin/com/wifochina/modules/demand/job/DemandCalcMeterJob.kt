package com.wifochina.modules.demand.job

import com.wifochina.common.util.DemandControlAdjustModelEnum
import com.wifochina.common.util.EmsConstants
import com.wifochina.common.util.StringUtil
import com.wifochina.modules.demand.service.adjustmodel.DemandControlAdjustModelChooser
import com.wifochina.modules.demand.service.adjustmodel.DemandControlAdjustModelService.HandlerAdjustModelHolder
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.quartz.JobExecutionContext
import org.springframework.scheduling.quartz.QuartzJobBean
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.ZoneId

private val log = KotlinLogging.logger { }

/**
 * Created on 2024/10/30 11:21.
 * <AUTHOR>
 */

@Component
class DemandCalcMeterJob(
    val projectService: ProjectService,
    val groupService: GroupService,
    val demandAdjustModelChooser: DemandControlAdjustModelChooser
) : QuartzJobBean() {
    override fun executeInternal(context: JobExecutionContext) {
        val projectId = context.jobDetail.jobDataMap[EmsConstants.PROJECT_ID] as String
        val groupId = context.jobDetail.jobDataMap[EmsConstants.GROUP_ID] as String?
        projectService.getById(projectId)?.let { projectEntity ->
            //得到 自动电表的分组
            var autoMeterGroups = groupService.queryEnableDemandControl(projectId)
                .filter { it -> it.demandControlAdjustModel.equals(DemandControlAdjustModelEnum.auto_meter.name) }

            //如果手动执行传递了某个分组的Id 则只执行这个分组的
            if (!StringUtil.isEmpty(groupId)) {
                autoMeterGroups = autoMeterGroups.filter { it.id.equals(groupId) }
            }
            log.info {
                "DemandCalcMeterJob 开始执行 时间: ${LocalDateTime.now(ZoneId.of(projectEntity.timezone))}," +
                        " 项目Id: ${projectEntity.id}, 项目名称: ${projectEntity.projectName}, 电表需量分组列表: ${
                            autoMeterGroups.map { it.id }.joinToString(",")
                        }"
            }
            autoMeterGroups.forEach { groupEntity ->
                demandAdjustModelChooser.chooseAdjustModel(groupEntity.demandControlAdjustModel)
                    .adjustExecute(object : HandlerAdjustModelHolder {
                        override fun group(): GroupEntity {
                            return groupEntity
                        }

                        override fun project(): ProjectEntity {
                            return projectEntity
                        }
                    })
            }

        } ?: run {
            log.error { "can`t find project by id: $projectId" }
        }

    }
}