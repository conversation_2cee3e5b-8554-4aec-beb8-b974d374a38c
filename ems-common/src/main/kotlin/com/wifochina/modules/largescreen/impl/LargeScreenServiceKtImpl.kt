package com.wifochina.modules.largescreen.impl

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.wifochina.common.time.MyTimeUtil
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.largescreen.LargeScreenServiceKt
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity
import com.wifochina.modules.strategy.service.TimeSharingBackFlowLimitService
import com.wifochina.modules.strategy.service.StrategyService
import com.wifochina.modules.strategy.service.TimeSharingDemandService
import com.wifochina.modules.strategytemplate.common.StrategyCommonUtils
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalTime
import java.time.ZoneId

private val log = KotlinLogging.logger { }

/**
 * Created on 2025/5/31 16:30.
 * <AUTHOR>
 */
@Service
class LargeScreenServiceKtImpl(
    val groupService: GroupService,
    val strategyService: StrategyService,
    val projectService: ProjectService,
    val timeSharingBackFlowLimitService: TimeSharingBackFlowLimitService,
    val timeSharingDemandService: TimeSharingDemandService
) : LargeScreenServiceKt {
    override fun getStrategyByGroupId(groupId: String): Map<String, StrategyEntity?> {
        val project: ProjectEntity = projectService.getById(WebUtils.projectId.get())
        // 带时区的 localTime
        val currentTime = LocalTime.now(ZoneId.of(project.timezone))
        // 获取今天是星期几
        val strategyDateStr = StrategyCommonUtils.getStrategyDateStr(
            project, MyTimeUtil.getTodayZeroTime(project.timezone)
        )
        return groupService.getById(groupId)?.let {
            val externalController: Boolean = it.getExternalController()
            if (externalController) {
                // 外部控制
                log.debug {
                    "当前系统 projectId: ${project.id} 运行策略由外部控制"
                }
                return mapOf("0" to null, strategyDateStr to null)
            }
            val systemStrategy = strategyService.getOuterStrategyByGroupId(it.id)

            val strategies = strategyService.list(
                LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getProjectId, project.id)
                    .eq(StrategyEntity::getGroupId, groupId).eq(StrategyEntity::getStrategyDate, strategyDateStr)
            )
            var strategy: StrategyEntity? = null
            try {
                strategy = strategies.firstOrNull {
                    MyTimeUtil.isWithinTimeRange(currentTime, it.startTime, it.endTime)
                }
                if (strategy != null) {
                    log.debug {
                        "find strategy startTime:${strategy.startTime}, endTime: ${strategy.endTime}"
                    }
                } else {
                    log.debug {
                        "not find timeStrategyEntity now: ${currentTime}, list:${strategies}"
                    }
                }
            } catch (e: NoSuchElementException) {
                log.warn { "can't find such period currentTime:$currentTime " }
            }
            //1.4.4 backflow
            var backFlowLimit: TimeSharingBackFlowLimitEntity?

            if (it.timeSharingBackFlowLimitController == true) {
                //分时防逆流
                val backFlowLimits = timeSharingBackFlowLimitService.listBy(project.id, groupId)
                backFlowLimit = backFlowLimits.firstOrNull { backFlowLimit ->
                    MyTimeUtil.isWithinTimeRange(currentTime, backFlowLimit.startTime, backFlowLimit.endTime)
                }
                if (backFlowLimit != null) {
                    log.debug {
                        "find backFlowLimit:${backFlowLimit.startTime}, endTime: ${backFlowLimit.endTime}"
                    }
                    //如果没有找到策略 这里需要显示吗 应该也不会
                    strategy?.timeSharingBackFlowLimitEntityList = listOf(backFlowLimit)
                }
            }

            var timeSharingDemand: TimeSharingDemandEntity?
            if (it.timeSharingDemandController == true) {
                //分时防逆流
                val timeSharingDemands = timeSharingDemandService.listPivotBy(project.id, groupId)
                timeSharingDemand = timeSharingDemands.firstOrNull { timeSharingDemand ->
                    MyTimeUtil.isWithinTimeRange(currentTime, timeSharingDemand.startTime, timeSharingDemand.endTime)
                }
                if (timeSharingDemand != null) {
                    log.debug {
                        "find timeSharingDemand:${timeSharingDemand.startTime}, endTime: ${timeSharingDemand.endTime}"
                    }
                    //如果没有找到策略 这里需要显示吗 应该也不会
                    strategy?.timeSharingDemandEntityList = listOf(timeSharingDemand)
                }
            }
            mapOf("0" to systemStrategy, strategyDateStr to strategy)
        } ?: run {
            mapOf()
        }
    }
}