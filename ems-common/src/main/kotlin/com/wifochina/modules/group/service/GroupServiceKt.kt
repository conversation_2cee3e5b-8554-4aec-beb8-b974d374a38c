package com.wifochina.modules.group.service

import com.wifochina.modules.group.entity.GroupEntity

/**
 * Created on 2024/10/30 09:47.
 * <AUTHOR>
 */
interface GroupServiceKt {

    /**
     * 同步更新 demand的 params 到 每个分组上去
     */
    fun syncUpdateDemandParams(demandCalcModel: Int, demandPeriod: Int, splitTime: Int)

    fun controlAgcAndAvc(groupEntity: GroupEntity)

    fun systemGroup(projectId:String): GroupEntity?
}