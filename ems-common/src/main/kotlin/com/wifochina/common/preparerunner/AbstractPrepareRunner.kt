package com.wifochina.common.preparerunner

import com.wifochina.common.mdc.MdcKotlinContext
import com.wifochina.modules.project.entity.ProjectEntity
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component
import org.springframework.util.StopWatch
import java.util.concurrent.CountDownLatch

/**
 * 希望统一所有的 关于 上线前的 prepare 准备cache 的工作
 * Created on 2025/2/27 09:44.
 * <AUTHOR>
 */

private val log = KotlinLogging.logger { }

@Component
abstract class AbstractPrepareRunner(
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor
) : PrepareRunner {
    //调用这个方法 必须要丢入线程池 否则会阻塞主线程, 因为内部使用runBlocking
    override fun prepareRun() {
        takeIf { async() }?.let {
            //异步执行
            val loopProjects = loopProjectIds()
            val latch = CountDownLatch(loopProjects.size)
            runBlocking {
                withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                    loopProjects.forEach { projectEntity ->
                        launch {
                            val stopWatch = StopWatch()
                            stopWatch.start()
                            try {
                                run(projectEntity)
                            } finally {
                                stopWatch.stop()
                                latch.countDown()
                                log.info {
                                    "项目: ${projectEntity.projectName} PrePare ${this@AbstractPrepareRunner::class.simpleName} 执行完毕, " + "耗时: ${stopWatch.totalTimeSeconds}s 还有 ${latch.count}个项目在运行中"
                                }
                            }
                        }
                    }
                }
            }
            latch.await()
            log.info { "所有的${this::class.simpleName} runner 都执行完毕" }
        } ?: run {
            //同步执行
            loopProjectIds().forEach {
                run(it)
            }
        }

    }


    abstract fun loopProjectIds(): List<ProjectEntity>
    abstract fun run(projectEntity: ProjectEntity)

    abstract fun async(): Boolean
}