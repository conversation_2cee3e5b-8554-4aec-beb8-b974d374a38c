package com.wifochina.common.preparerunner.impl

import com.wifochina.common.preparerunner.AbstractPrepareRunner
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.DeviceService
import com.wifochina.modules.initelectric.InitElectricService
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger { }

/**
 * Created on 2025/5/15 14:44.
 * <AUTHOR>
 */
@Component
class InitEnergyPrepareRunner(
    threadPoolTaskExecutor: ThreadPoolTaskExecutor,
    val projectService: ProjectService,
    val initElectricService: InitElectricService,
    val deviceService: DeviceService,
    val ammeterService: AmmeterService,
) : AbstractPrepareRunner(threadPoolTaskExecutor) {


    override fun loopProjectIds(): List<ProjectEntity> {
        return projectService.allProjectByServiceDeployStatus
    }

    override fun run(projectEntity: ProjectEntity) {
        // 设备的投运时间的第一条数据 历史电量的
        // 1.得到项目的投运时间
        // 2.遍历这个项目的设备 device 和 meter
        // 3.查询influxdb 这个投运时间当天的数据的first()  值
        // 4.记录到 设备或者电表的新字段里
        // -> 5. 首页把这个给-掉
        // -> 6. 设备更新或者电表更新的时候 再去查询一次 这个这个方法
        val devices = deviceService.getDevicesByPid(projectEntity.id)
        val meters = ammeterService.findAllMeters(projectEntity.id)
        devices.forEach { device ->
            val result = initElectricService.getInitElectric(projectEntity, device);
            result?.let {
                device.projectRunInitInData = if (it.inData.value == null) 0.0 else it.inData.value
                device.projectRunInitOutData = if (it.outData.value == null) 0.0 else it.outData.value
                deviceService.updateById(device)
            }
        }
        meters.forEach { meter ->
            val result = initElectricService.getInitElectric(projectEntity, meter);
            result?.let {
                meter.projectRunInitInData = if (it.inData.value == null) 0.0 else it.inData.value
                meter.projectRunInitOutData = if (it.outData.value == null) 0.0 else it.outData.value
                ammeterService.updateById(meter)
            }
        }
    }

    override fun async(): Boolean {
        return false;
    }
}