package com.wifochina.common.preparerunner.impl

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.wifochina.common.preparerunner.AbstractPrepareRunner
import com.wifochina.common.request.RangeRequest
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.DeviceEntity
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.DeviceService
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import com.wifochina.modules.project.service.impl.ProjectServiceImpl
import com.wifochina.modules.report.entity.DayReportEntity
import com.wifochina.modules.report.service.DayReportCacheService
import com.wifochina.modules.report.service.DayReportService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.ZoneId


private val log = KotlinLogging.logger { }

/**
 * 打云版本 包的时候 记得把这个 注释了 不要上线的时候跑, 直接上线前给数据跑好
 */
@Component
class DayReportPrepareRunner(
    threadPoolTaskExecutor: ThreadPoolTaskExecutor,
    val projectService: ProjectService,
    val dayReportCacheService: DayReportCacheService,
    val dayReportService: DayReportService,
    val deviceService: DeviceService,
    val ammeterService: AmmeterService,
) : AbstractPrepareRunner(threadPoolTaskExecutor) {
    override fun loopProjectIds(): List<ProjectEntity> {
        //哪些项目要跑 runner // 云版本只跑 云版本的数据  场站只跑 场站的项目
        val list = projectService.allProjectByServiceDeployStatus
        return list
    }

    override fun run(projectEntity: ProjectEntity) {
        val ammeterEntities: List<AmmeterEntity> = ammeterService.findAllMeters(projectEntity.id)
        val deviceEntities: List<DeviceEntity> = deviceService.getDevicesByPid(projectEntity.id)
        if (ammeterEntities.isEmpty() && deviceEntities.isEmpty()) {
            log.warn {
                "saveDayReport 项目:${projectEntity.projectName} 无设备或者电表 无法运行 saveDayReport"
            }
            return
        }

        //1.得到 项目的开始时间->到当前日期的前一天时间的  时间日期零点列表
        var midnightTimestamps = getMidnightTimestamps(
            ZoneId.of(projectEntity.timezone),
            Instant.ofEpochSecond(projectEntity.createTime).atZone(ZoneId.of(projectEntity.timezone)).withHour(0)
                .withMinute(0).withSecond(0).withNano(0).plusDays(1).toEpochSecond(),
            Instant.now().epochSecond
        )
        dayReportService.getOne(
            LambdaQueryWrapper<DayReportEntity>().eq(DayReportEntity::getProjectId, projectEntity.id)
                .orderByDesc(DayReportEntity::getTime).last("LIMIT 1")
        )?.let { latestDayReport ->
            //如果以前有记录 就按照这个最新的记录time开始跑后面的数据
            val latestTimeIndexOf = midnightTimestamps.indexOf(latestDayReport.time)
            if (latestTimeIndexOf != -1) {
                //只切片 已经存在的最新数据->到昨天的时间
                midnightTimestamps =
                    midnightTimestamps.slice(IntRange(latestTimeIndexOf + 1, midnightTimestamps.size - 1))
            } else {
                log.error { "为找到time: ${latestDayReport.time} 时间在 midnightTimestamps $midnightTimestamps " }
                midnightTimestamps = listOf()
            }
        }
        if (midnightTimestamps.isEmpty()) {
            log.info { "dayReport prepare not need to run ${projectEntity.projectName}" }
            return
        }
        log.info {
            "dayReport prepare rangeTime from: ${midnightTimestamps[0]} to ${
                midnightTimestamps[midnightTimestamps.size - 1]
            }"
        }
        midnightTimestamps.forEach { dayStartTime ->
            try {
                dayReportCacheService.saveDayReport(
                    projectEntity,
                    RangeRequest().setStartDate(dayStartTime).setEndDate(dayStartTime + EmsConstants.ONE_DAY_SECOND)
                )
            } catch (e: Exception) {
                log.error { "saveDayReport error : $e" }
            }
        }
    }

    override fun async(): Boolean {
        return true
    }

    fun getMidnightTimestamps(zoneId: ZoneId, startTimestamp: Long, endTimestamp: Long): List<Long> {
        val startDate = Instant.ofEpochSecond(startTimestamp).atZone(zoneId).toLocalDate() // 获取开始日期

        val endDate = Instant.ofEpochSecond(endTimestamp).atZone(zoneId).minusDays(1).toLocalDate() // 获取结束日期

        return generateSequence(startDate) { date ->
            date.plusDays(1).takeIf { it <= endDate } // 每次加一天，直到超过结束日期
        }.map { date ->
            date.atStartOfDay(zoneId).toEpochSecond() // 转换为零点时间戳
        }.toList()
    }

}