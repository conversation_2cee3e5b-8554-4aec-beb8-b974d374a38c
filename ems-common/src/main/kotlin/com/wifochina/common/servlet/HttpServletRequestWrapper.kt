package com.wifochina.common.servlet

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletRequestWrapper

/**
 * Created on 2024/11/20 16:35.
 * <AUTHOR>
 */
class HttpServletRequestWrapper(httpServletRequest: HttpServletRequest) :
    HttpServletRequestWrapper(httpServletRequest) {
    private val customHeaders = mutableMapOf<String, String>()


    fun addHeader(name: String, value: String) {
        customHeaders[name] = value
    }

    override fun getHeader(name: String?): String? {
        val headerValue = customHeaders[name]
        if (headerValue != null) {
            return headerValue
        }
        return super.getHeader(name)
    }
}