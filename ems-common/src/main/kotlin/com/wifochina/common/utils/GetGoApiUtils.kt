package com.wifochina.common.utils

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.modules.group.entity.ControllerEntity
import com.wifochina.modules.group.service.ControllerService
import com.wifochina.modules.oauth.util.WebUtils.projectId
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/5 11:13.
 * <AUTHOR>
 */

private val log = KotlinLogging.logger { }

@Service
class GetGoApiUtils(
    val projectService: ProjectService, val controllerService: ControllerService
) {

    @Value("\${ems.gateway}")
    private val gatewayUrl: String? = null

    companion object {
        const val PROTOCOL_PREFIX = "http://"
    }

    fun getGoApi(projectId: String): String {
        return projectService.getById(projectId)?.let {
            getGoApi(it)
        } ?: run {
            //抛错
            throw ServiceException(
                ErrorResultCode.CUSTOM_MSG_ERROR.value(), "can`t find project by projectId $projectId"
            )
        }
    }

    fun getGoApi(projectEntity: ProjectEntity): String {
        return if (projectEntity.projectModel == 1) {
            controllerService.getOne(
                LambdaQueryWrapper<ControllerEntity>().eq(ControllerEntity::getProjectId, projectEntity.id)
            )?.let {
                "${PROTOCOL_PREFIX}${it.ip.trim()}:${it.port}"
            } ?: run {
                log.error { "getGoApi can`t find controller by projectId ${projectEntity.id}" }
                throw ServiceException("")
            }
        } else {
            gatewayUrl ?: run {
                throw ServiceException(
                    ErrorResultCode.CUSTOM_MSG_ERROR.value(),
                    "goGetApi can`t find gatewayUrl config by projectId ${projectEntity.id}"
                )
            }
        }
    }
}