package com.wifochina.common.utils

import com.wifochina.common.config.LoggingInterceptor
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder

private val log = KotlinLogging.logger { }

/**
 * Created on 2024/11/5 10:37.
 * <AUTHOR>
 */
@Component
class RestTemplateUtils(
    val restTemplate: RestTemplate
) {

    /**
     * 发送带泛型的 POST / GET 等 请求
     *
     * @param url           请求的 URL
     * @param method 请求方法类型
     * @param bodyOrParamsMap  请求体/ParamsMap
     * @param headersMap    请求头
     * @param parameterizedTypeReference  响应类型
     * @param <T>           响应类型泛型
     * @return              响应对象
    </T> */
    fun <T> sendRequestNew(
        url: String,
        method: HttpMethod,
        bodyOrParamsMap: Map<String, Any>?,
        headersMap: Map<String, String>,
        parameterizedTypeReference: ParameterizedTypeReference<T>,
        logFlag: Boolean?
    ): T? {
        logFlag?.let {
            if (!LoggingInterceptor.LOG_URL.contains(url)) LoggingInterceptor.LOG_URL.add(url)
        }
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        // 设置自定义请求头
        headersMap.forEach(headers::set)
        //支持了GET 请求 把参数 build 到 url后面
        val response = if (method.matches(HttpMethod.GET.name)) {
            val multiValueMap = LinkedMultiValueMap<String, String>()
            bodyOrParamsMap?.forEach { (key, value) ->
                value.let {
                    multiValueMap.add(key, value.toString())
                }
            }
            val urlWrapper = UriComponentsBuilder.fromHttpUrl(url).queryParams(multiValueMap).build().toUriString()
            restTemplate.exchange(
                urlWrapper, HttpMethod.GET, HttpEntity(null, headers), parameterizedTypeReference
            )
        } else {
            restTemplate.exchange(
                url, method, HttpEntity(bodyOrParamsMap, headers), parameterizedTypeReference
            )
        }
        return response.body
    }

    /**
     * 发送不带泛型的 POST 请求
     *
     * @param url           请求的 URL
     * @param requestBody   请求体
     * @param responseType  响应类型
     * @param headersMap    请求头
     * @param <T>           响应类型泛型
     * @return              响应对象
    </T> */
    //TODO 待删除 用sendRequestNew
    fun <T> sendRequest(
        url: String,
        method: HttpMethod,
        requestBody: Map<String, Any>?,
        responseType: Class<T>,
        headersMap: Map<String, String>,
        logFlag: Boolean?
    ): T? {
        logFlag?.let {
            if (!LoggingInterceptor.LOG_URL.contains(url)) LoggingInterceptor.LOG_URL.add(url)
        }
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        // 设置自定义请求头
        headersMap.forEach(headers::set)
        val response = restTemplate.exchange(
            url, method, HttpEntity(requestBody, headers), responseType
        )

        return response.body
    }

    /**
     * 发送带泛型的 POST 请求
     *
     * @param url           请求的 URL
     * @param requestBody   请求体
     * @param responseType  响应类型
     * @param headersMap    请求头
     * @param <T>           响应类型泛型
     * @return              响应对象
    </T> */
    fun <T> get(
        url: String,
        requestParams: Map<String, Any>,
        responseType: ParameterizedTypeReference<T>,
        headersMap: Map<String, String>,
        logFlag: Boolean?
    ): T? {
        logFlag?.let {
            if (it) {
                if (!LoggingInterceptor.LOG_URL.contains(url)) LoggingInterceptor.LOG_URL.add(url)
            }
        }
        val headers = HttpHeaders()
        // 设置自定义请求头
        headersMap.forEach(headers::set)
        val multiValueMap = LinkedMultiValueMap<String, String>()
        requestParams.forEach { (key, value) ->
            value?.let {
                multiValueMap.add(key, value.toString())
            }
        }
        val urlWrapper = UriComponentsBuilder.fromHttpUrl(url).queryParams(multiValueMap).build().toUriString()
        val response = restTemplate.exchange(
            urlWrapper, HttpMethod.GET, HttpEntity(null, headers), responseType
        )
        return response.body
    }

    /**
     * 发送带泛型的 POST 请求
     *
     * @param url           请求的 URL
     * @param requestBody   请求体
     * @param responseType  响应类型
     * @param headersMap    请求头
     * @param <T>           响应类型泛型
     * @return              响应对象
    </T> */
    fun <T> post(
        url: String,
        requestBody: Map<String, Any>,
        responseType: ParameterizedTypeReference<T>,
        headersMap: Map<String, String>,
        logFlag: Boolean?
    ): T? {
        logFlag?.let {
            if (!LoggingInterceptor.LOG_URL.contains(url)) LoggingInterceptor.LOG_URL.add(url)
        }
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        // 设置自定义请求头
        headersMap.forEach(headers::set)
        val response = restTemplate.exchange(
            url, HttpMethod.POST, HttpEntity(requestBody, headers), responseType
        )

        if (url.contains("login")) {
            log.error { "what ????? ---------- response body  : $response" }
        }
        return response.body
    }

//    fun <T> get(
//        url: String,
//        headersMap: Map<String, String>,
//        responseType: ParameterizedTypeReference<T>,
//    ): T? {
//        val headers = HttpHeaders()
//        headersMap.forEach(headers::set)
//        val objectHttpEntity = HttpEntity<Any?>(null, headers)
//        val response = restTemplate.exchange(
//            url, HttpMethod.GET, objectHttpEntity, responseType
//        )
//        return response.body
//    }

}