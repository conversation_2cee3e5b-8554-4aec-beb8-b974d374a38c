package com.wifochina.common.utils

import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

/**
 * Created on 2024/11/11 10:57.
 * <AUTHOR>
 */
class ExtendKt {

}

// 扩展函数将秒级时间戳转为毫秒级
fun Long.toMillis(): Long = if (this < 1_000_000_000_000L) this * 1000 else this

// 扩展函数将时间戳格式化为日期

fun Long.toFormattedDate(
    pattern: String = "yyyy-MM-dd HH:mm:ss", zoneId: ZoneId = ZoneId.systemDefault()
): String {
    val formatter = DateTimeFormatter.ofPattern(pattern).withZone(zoneId)
    return formatter.format(Instant.ofEpochMilli(this))
}

fun Any.dataClassToMap(): Map<String, Any?> {
    val properties = mutableMapOf<String, Any?>()

    // 递归获取当前类及其父类的所有属性
    var clazz: Class<*>? = this::class.java
    while (clazz != null) {
        clazz.kotlin.memberProperties.forEach { property ->
            property.isAccessible = true  // 设置为可访问
            properties[property.name] = property.call(this)
        }
        clazz = clazz.superclass // 继续向父类查找
    }
    return properties
//    return this::class.memberProperties.associate { it.name to it.call(this) }
}

fun Any.dataClassToMutableMap(): MutableMap<String, Any> {
    val map = this::class.java.declaredFields.associate { field ->
        field.isAccessible = true  // 设置为可访问
        field.name to field.get(this) // 获取字段名和字段值
    }
    val toMutableMap = map.toMutableMap()
    return toMutableMap
}