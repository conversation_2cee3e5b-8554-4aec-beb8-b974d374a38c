package com.wifochina.common.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

/**
 * Created on 2023/11/20 10:16.
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "system")
data class SystemConfiguration(
    val accountSystem: AccountSystemConfiguration,
    val demand: DemandConfiguration,
    val ai: AiConfiguration
//    val platform: PlatformConfiguration,
)


