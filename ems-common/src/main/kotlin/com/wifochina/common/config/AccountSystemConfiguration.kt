package com.wifochina.common.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

/**
 * Created on 2023/11/21 14:25.
 * <AUTHOR>
 */

@Configuration
@ConfigurationProperties(prefix = "account-system")
data class AccountSystemConfiguration(
    var auth: AccountSystemAuthConfiguration?
) {
    lateinit var accountUrl: String
    lateinit var appId: String
    lateinit var salt: String
    lateinit var dataCenter: String
    lateinit var adminAccount: String
    lateinit var adminPassword: String
    var adminAccountType: Int = 1
    lateinit var loginRoleId: String
}