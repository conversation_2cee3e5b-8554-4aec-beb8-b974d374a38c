package com.wifochina.common.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

/**
 * Created on 2023/11/21 14:25.
 * <AUTHOR>
 */

@Configuration
@ConfigurationProperties(prefix = "demand")
data class DemandConfiguration(
    var demandLastPreHours: Int = 3, var demandExtrusionMaxTake: Int = 180,
    val demandJobCalcInterval: Int = 1
)