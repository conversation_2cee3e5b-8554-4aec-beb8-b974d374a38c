package com.wifochina.common.mdc

import kotlinx.coroutines.asCoroutineDispatcher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import kotlin.coroutines.CoroutineContext

/**
 * Created on 2025/1/8 19:46.
 * <AUTHOR>
 */
@Configuration
class MdcKotlinCoroutineContextConfig {
    // 创建协程上下文的工厂方法
    fun createCoroutineContext(threadPoolTaskExecutor: ThreadPoolTaskExecutor): CoroutineContext {
        return threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()
    }

    @Bean
    fun coroutineContext(threadPoolTaskExecutor: ThreadPoolTaskExecutor): CoroutineContext {
        return createCoroutineContext(threadPoolTaskExecutor)
    }

}