
ALTER TABLE t_group ADD `enable_waste_power_generation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否余热发电';
ALTER TABLE t_group ADD `group_earnings_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启分组收益开关';

ALTER TABLE t_strategy ADD `off_gird_soc`  DOUBLE DEFAULT 5.0  COMMENT '离网soc下限';
ALTER TABLE t_strategy_history ADD `off_gird_soc`  DOUBLE DEFAULT 5.0  COMMENT '离网soc下限';


INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
    (712,	7,	'/monitor/steerable',	'可控设备监控');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
    (1002,	1,	'/homePage/hookup',	'接线图密码');

ALTER TABLE t_user ADD `lock_count` int DEFAULT '0' COMMENT '是否账号锁定中';
update t_user set lock_count = 0;

ALTER TABLE t_user ADD `country_id` int DEFAULT NULL COMMENT '国家id';

ALTER TABLE t_group add `ems_strategies` text COMMENT 'soc上限';

CREATE TABLE `t_email_log` (
                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
                               `group_id` varchar(128) DEFAULT NULL COMMENT '分组id',
                               `project_id` varchar(128) NOT NULL COMMENT '项目id',
                               `email` varchar(2500) NOT NULL COMMENT '收件人',
                               `subject` varchar(500) NOT NULL COMMENT '主题',
                               `content` text NOT NULL,
                               `type` int(1) NOT NULL COMMENT '1需量超了 2月初需量设置错误  3收益月报 4收益年报',
                               `retry` int(1) DEFAULT NULL COMMENT '补发',
                               `success` tinyint(1) NOT NULL COMMENT 'true发送成功 false发送失败',
                               `time` int(10) unsigned NOT NULL COMMENT '发生时间',
                               `fix_time` int(10) unsigned DEFAULT NULL COMMENT '修复时间',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;