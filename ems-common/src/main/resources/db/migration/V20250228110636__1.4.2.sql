ALTER TABLE t_log
    add `params` text COMMENT '参数';
ALTER TABLE t_log
    add `detail` text COMMENT '细节';
ALTER TABLE t_log
    ADD `client` varchar(20) NOT NULL DEFAULT 'PC' COMMENT '客户端';
ALTER TABLE t_log
    ADD `trace_id` varchar(256) NULL COMMENT '追踪标识id';

ALTER TABLE t_electric_price
    ADD `waster_self_price` double NULL  COMMENT '余热发电自用价格';
ALTER TABLE t_electric_price
    ADD `waster_df_price` double NULL  COMMENT '余热发电脱硫标杆价格';
ALTER TABLE t_electric_price
    ADD `waster_subsidy_price` double NULL  COMMENT '余热发电国家补贴价格';
ALTER TABLE t_electric_price
    ADD `waster_price` double NULL  COMMENT '余热发电发电价格';

ALTER TABLE t_price_template
    ADD `waster_self_price` double NULL  COMMENT '余热发电自用价格';
ALTER TABLE t_price_template
    ADD `waster_df_price` double NULL  COMMENT '余热发电脱硫标杆价格';
ALTER TABLE t_price_template
    ADD `waster_subsidy_price` double NULL  COMMENT '余热发电国家补贴价格';
ALTER TABLE t_price_template
    ADD `waster_price` double NULL  COMMENT '余热发电发电价格';


ALTER TABLE t_pv_wind_profit CHANGE COLUMN pv_or_wind_type renewable_type VARCHAR (128);
RENAME
TABLE t_pv_wind_profit TO t_renewable_profit;

ALTER TABLE t_pv_wind_dynamic_profit CHANGE COLUMN pv_or_wind_type renewable_type VARCHAR (128);
RENAME
TABLE t_pv_wind_dynamic_profit TO t_renewable_dynamic_profit;

ALTER TABLE t_group
    ADD `capacity_alarm_threshold` double NOT NULL DEFAULT '100' COMMENT '容量告警阈值(超出多少就告警)';
ALTER TABLE t_group
    ADD `capacity_remind_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT '容量提醒开关(false关闭)(true打开）';

-- 添加电表协议字段
alter table t_ammeter
    add protocol varchar(20) default 'TCP/IP' comment '协议';
alter table t_ammeter
    add device varchar(20) default null comment '设备名称';
alter table t_ammeter
    add baud_rate int default null comment '波特率';
alter table t_ammeter
    add data_bits int default null comment '数据位';
alter table t_ammeter
    add stop_bits int default null comment '停止位';
alter table t_ammeter
    add parity varchar(1) default null comment '奇偶校验';

CREATE TABLE `t_capacity_alarm_record`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `group_id`       varchar(128) NOT NULL COMMENT '分组id',
    `project_id`     varchar(128) NOT NULL COMMENT '项目id',
    `description`    varchar(256) DEFAULT NULL COMMENT '描述',
    `description_en` varchar(256) DEFAULT NULL COMMENT '英文描述',
    `type`           int(1) NOT NULL COMMENT '1 容量高级超了 0消失记录',
    `time`           int(10) unsigned NOT NULL COMMENT '时间',
    `alarm_value` double unsigned NOT NULL,
    `control_value` double unsigned NOT NULL,
    `create_by`      varchar(20)  DEFAULT NULL COMMENT '创建者',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE t_chat_session
(
    id          BIGINT PRIMARY KEY COMMENT '主键ID',
    chat_id     VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '对话ID',
    user_id     VARCHAR(64)  NOT NULL DEFAULT '' COMMENT '用户ID',
    title       VARCHAR(255) NOT NULL DEFAULT '' COMMENT '会话标题',
    create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户会话表';

CREATE TABLE t_chat_session_message
(
    id                BIGINT PRIMARY KEY COMMENT '主键ID',
    session_id        BIGINT    NOT NULL DEFAULT 0 COMMENT '会话ID',
    type              INT       NOT NULL DEFAULT 0 COMMENT '角色',
    parent_id         BIGINT    NOT NULL COMMENT '父级消息Id',
    reasoning_time    DECIMAL NULL COMMENT '思考时间',
    reasoning_content TEXT NULL COMMENT '思考内容',
    content           TEXT      NOT NULL COMMENT '消息内容',
    create_time       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话消息历史';


ALTER TABLE t_group
    ADD `mg_soc_balance` tinyint(1)  DEFAULT '0' COMMENT  '微网储能SOC被动均衡, 打开后，并网储能功率会根据离网储能的SOC和功率自动调节，达到所有储能SOC的平衡状态';

ALTER TABLE t_group
    ADD `mg_soc_balance_off_grid_power_limit_ratio` double DEFAULT 0.8 COMMENT  '微网储能SOC被动均衡功率比例 默认0.8';


INSERT INTO `t_authority` (`pid`, `ap_key`, `name`)
VALUES (16, '/manage/user/log/detail', '操作明细');

INSERT INTO `t_authority` (`pid`, `ap_key`, `name`)
VALUES (11, '/user/log/detail', '操作明细');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (10111, 10, '/diagram/getBatteryRateWhoStation', '整站导出');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (28, null, '/capacity', '容量管理');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (10222, 28, '/capacity/alarm/query', '容量告警记录查询');


-- 新增操作明细权限
INSERT INTO `t_authority` (`pid`, `ap_key`, `name`)
VALUES (16, '/manage/user/log/detail', '操作明细');

INSERT INTO `t_authority` (`pid`, `ap_key`, `name`)
VALUES (11, '/user/log/detail', '操作明细');

-- 1.4.2 - 后续版本新增的 补齐
-- 电站控制策略增加并离网soc上限
ALTER TABLE t_strategy
    ADD `off_grid_soc_high_limit` double DEFAULT 100 COMMENT  '离网soc上限';

ALTER TABLE t_strategy
    ADD `charge_soc_high_limit` double DEFAULT 100 COMMENT  '并网soc上限';

ALTER TABLE t_group
    ADD `home_page_reactive_power_controller` tinyint(1)  NOT NULL DEFAULT '0' COMMENT '首页显示无功功率开关(默认关闭)';
ALTER TABLE t_group
    ADD `home_page_zero_power_controller` tinyint(1) NOT NULL DEFAULT '1' COMMENT '首页整站/单机零值开关(默认开启)'


