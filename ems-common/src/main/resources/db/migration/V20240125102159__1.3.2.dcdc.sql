ALTER TABLE t_device ADD `has_sts` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有STS';
ALTER TABLE t_device ADD `has_dcdc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有dcdc';
ALTER TABLE t_device ADD `pv_output_history_init` double COMMENT 'pv表初始放电量初始值';
ALTER TABLE t_device ADD `pv_input_history_init` double COMMENT 'pv表初始充电量初始值';
ALTER TABLE t_group ADD `show_battery_electricity` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示电池电量';
ALTER TABLE t_group ADD `dcdc_profit_controller` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否开启dcdc收益开关';

update t_group set `show_battery_electricity` = 1;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
    (79,	7,	'/monitor/dcdcStatus',  'DCDC运行监控');