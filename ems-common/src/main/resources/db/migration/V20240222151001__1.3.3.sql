CREATE TABLE `t_demand` (
                            `id` int(11) NOT NULL COMMENT 'id',
                            `group_id` varchar(128) NOT NULL COMMENT '分组id',
                            `project_id` varchar(128) NOT NULL COMMENT '项目id',
                            `demand` double unsigned NOT NULL COMMENT '需量值',
                            `time` int(10) unsigned NOT NULL COMMENT '发生时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `t_demand_log` (
                                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                `group_id` varchar(128) NOT NULL COMMENT '分组id',
                                `project_id` varchar(128) NOT NULL COMMENT '项目id',
                                `description` varchar(128) NOT NULL COMMENT '描述',
                                `description_en` varchar(128) DEFAULT NULL COMMENT '英文描述',
                                `type` int(1) NOT NULL COMMENT '1 需量超了 2设置控制需量 3设置月初需量 4月初需量恢复',
                                `time` int(10) unsigned NOT NULL COMMENT '发生时间',
                                `actual_demand` double unsigned NOT NULL,
                                `create_by` varchar(20) DEFAULT NULL COMMENT '创建者',
                                `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                `update_by` varchar(20) DEFAULT NULL COMMENT '修改者',
                                `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;;

ALTER TABLE t_group ADD `demand_income` tinyint(1) NOT NULL DEFAULT '1' COMMENT '需量收益(false关闭)(true打开）';
ALTER TABLE t_group ADD `demand_calc_model` int(10)  NOT NULL DEFAULT '1' COMMENT '需量计算模式:1固定时段15分钟;2固定时段30分钟;3一分钟滑窗';
ALTER TABLE t_group ADD `demand_alarm_threshold` double NOT NULL DEFAULT '100' COMMENT '需量告警阈值(超出多少就告警)';
ALTER TABLE t_group ADD `capacity_controller` tinyint(1) NOT NULL DEFAULT '1' COMMENT '容量控制开关(false关闭)(true打开）';
ALTER TABLE t_strategy ADD `month_control_power` double NULL COMMENT '月初需量';
ALTER TABLE t_strategy ADD `grid_control_power` double NULL COMMENT '需量控制功率';
ALTER TABLE t_strategy_history ADD `month_control_power` double NULL COMMENT '月初需量';
ALTER TABLE t_strategy_history ADD `grid_control_power` double NULL COMMENT '需量控制功率';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
         (127,	12,	'/system/alarm',	'声控报警'),
         (25,	NULL,	'/demand',	'需量通知'),
         (251,	25,	'/demand/email/add',	'增加通知'),
         (252,	25,	'/demand/email/edit',	'修改通知'),
         (253,	25,	'/demand/email/delete',	'删除通知'),
         (254,	25,	'/demand/email/query',	'查询通知'),
         (255,	25,	'/demand/job/manage',	'需量任务管理'),
         (256,	25,	'/demand/log/query',	'查询日志'),
         (207,	10,	'/diagram/getVoltageDiffRate',	'最大电压差'),
         (208,	10,	'/diagram/getTemperatureDiffRate',	'最大温度差');

CREATE TABLE `t_demand_email` (
                                  `id` varchar(128) NOT NULL COMMENT '通知识符uuid',
                                  `email` varchar(32) DEFAULT NULL COMMENT '邮箱',
                                  `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                                  `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
                                  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                  `update_by` varchar(128) DEFAULT NULL COMMENT '修改者',
                                  `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE t_project ADD `whether_alarm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否报警';
ALTER TABLE t_project ADD `order` int NOT NULL DEFAULT '9' COMMENT '项目排序';
ALTER TABLE t_group ALTER COLUMN demand_controller SET DEFAULT '0';