ALTER TABLE t_project_ext
    ADD COLUMN hide_all_ems_kernel_code BOOLEAN NOT NULL DEFAULT 0 COMMENT '隐藏所有 ems 核心code';
ALTER TABLE t_project_ext
    ADD COLUMN hide_all_ems_sub_device_code BOOLEAN NOT NULL DEFAULT 0 COMMENT '隐藏所有 ems 子设备 code';
ALTER TABLE t_project_ext
    ADD COLUMN hide_all_meter_code BOOLEAN NOT NULL DEFAULT 0 COMMENT '隐藏 meter code';

update t_hide_event_code
set type = '1'
where event_code like '65534_%'
   or event_code like '65535_%';
update t_hide_event_code
set type = '2'
where type = 'EMS';
update t_hide_event_code
set type = '3'
where type = 'METER';

ALTER TABLE t_group
    ADD `agc_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'agc开关';

ALTER TABLE t_group
    ADD `avc_controller` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'avc开关';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (60012, 10, '/diagram/getAgcRate', 'agc曲线'),
       (60013, 10, '/diagram/getAvcRate', 'avc曲线');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (60014, 14, '/manage/project/operation', '管理的项目收益');

ALTER TABLE t_device
    ADD COLUMN `is_host` TINYINT(1) DEFAULT 0 COMMENT '是否主机0否1是';


CREATE TABLE `t_demand_last_record` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `time` bigint(20) NOT NULL,
                                        `project_id` varchar(255) NOT NULL,
                                        `group_id` varchar(255) NOT NULL,
                                        `create_by` varchar(255) DEFAULT NULL,
                                        `create_time` bigint(20) DEFAULT NULL,
                                        `update_by` varchar(255) DEFAULT NULL,
                                        `update_time` bigint(20) DEFAULT NULL,
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `unique_project_group` (`project_id`,`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (58, 5, '/operation/getPrice', '查询电价 ');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (59, 5, '/operation/savePrice', '保存电价');