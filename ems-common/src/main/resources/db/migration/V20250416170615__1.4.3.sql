-- 项目配置表
CREATE TABLE IF NOT EXISTS t_configuration (
  id INT AUTO_INCREMENT PRIMARY KEY,
  project_id VARCHAR(128) not null COMMENT '项目id',
  name VARCHAR(255) COMMENT '配置名称',
  type INT COMMENT '类型',
  password VARCHAR(255) COMMENT '密码',
  config_url VARCHAR(255) COMMENT '配置路径',
  generation_type INT COMMENT '生成类型',
  graph_script MEDIUMTEXT COMMENT '绘图脚本',
  is_released tinyint DEFAULT 0 COMMENT '是否发布',
  index_order INT COMMENT '排序',
  create_time timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置表';



CREATE TABLE IF NOT EXISTS t_strategy_template (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `template_name` VA<PERSON>HA<PERSON>(128) not null COMMENT '模版名称',
    `strategy_type` int(10) not null COMMENT '模版充放电模式',
    `project_id` VARCHAR(128) not null COMMENT '项目id',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模版表';

-- 运维信息
CREATE TABLE IF NOT EXISTS t_maintenance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id VARCHAR(128) not null COMMENT '项目id',
    factory_principal VARCHAR(255) COMMENT '厂区负责人',
    principal_contact VARCHAR(255) COMMENT '负责人联系方式',
    after_sale VARCHAR(255) COMMENT '售后负责人',
    after_sale_contact VARCHAR(255) COMMENT '售后负责人联系方式',
    project_address VARCHAR(255) COMMENT '项目地址'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运维信息表';

CREATE TABLE IF NOT EXISTS t_strategy_template_item (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `template_id` BIGINT not null COMMENT '模版id',
    `project_id` VARCHAR(128) not null COMMENT '项目id',
    `start_time` time not null COMMENT '开始时间',
    `end_time` time not null COMMENT '结束时间',
    `power` double (22,0) DEFAULT NULL  COMMENT '项目id',
    `soc` double (22,0) DEFAULT NULL  COMMENT '项目id',
    `type`  int(11) DEFAULT NULL COMMENT '策略item的type',
    `item_control_id` bigint(20) DEFAULT NULL COMMENT 'control id',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模版item表';


ALTER TABLE t_strategy ADD `strategy_date` varchar(50)  DEFAULT NULL COMMENT '策略的date日期';
ALTER TABLE t_strategy ADD `strategy_control_id` bigint(20)  DEFAULT NULL COMMENT '策略的date日期';


ALTER TABLE t_ammeter
    ADD `is_backup` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'isBackup';


CREATE TABLE IF NOT EXISTS t_strategy_template_item_control (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,

    `price_difference_controller`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价差额/kWh开关(false关闭)(true打开）',
    `price_difference`  double DEFAULT '0' COMMENT '电价差额',

    `price_benchmark_controller`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价定值开关(false关闭)(true打开）',
    `price_benchmark_condition`  varchar(128) DEFAULT NULL COMMENT '电价定值条件',
    `price_benchmark` double DEFAULT '0' COMMENT '电价定值',

    `price_base_value_controller`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价最值开关(false关闭)(true打开）',
    `price_base_value_range_condition`  varchar(128) DEFAULT NULL COMMENT '电价最值条件',
    `price_base_value_section_count` int(11) DEFAULT 0 COMMENT '电价最值段数',

    `price_continue_duration_controller`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价连续时长开关(false关闭)(true打开）',
    `price_continue_duration_condition`  varchar(128) DEFAULT NULL COMMENT '电价连续时长条件',
    `price_continue_duration_hour` int(11) DEFAULT 0 COMMENT '电价连续小时数',

    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='策略item control控制表';


CREATE TABLE IF NOT EXISTS t_strategy_month_template_bind (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `month` int(2) not null COMMENT 'month月份',
    `template_id` bigint not null COMMENT '模版id',
    `project_id` VARCHAR(128) not null COMMENT '项目id',
    `group_id` VARCHAR(128) not null COMMENT '分组id',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模版表';


CREATE TABLE IF NOT EXISTS t_strategy_day_template_bind (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `month` TINYINT UNSIGNED NOT NULL COMMENT 'month月份',
    `day` TINYINT UNSIGNED NOT NULL  COMMENT 'day天',
    `template_id` bigint not null COMMENT '模版id',
    `project_id` VARCHAR(128) not null COMMENT '项目id',
    `group_id` VARCHAR(128) not null COMMENT '分组id',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模版表';




CREATE TABLE IF NOT EXISTS t_strategy_control (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `project_id` VARCHAR(128) not null COMMENT '项目id',
    `group_id` VARCHAR(128) not null COMMENT '分组id',
    `strategy_date` VARCHAR(20) null COMMENT '策略date日期',
    `price_difference_controller`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价差额/kWh开关(false关闭)(true打开）',
    `price_difference`  double DEFAULT '0' COMMENT '电价差额',

    `price_benchmark_controller`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价定值开关(false关闭)(true打开）',
    `price_benchmark_condition`  varchar(128) DEFAULT NULL COMMENT '电价定值条件',
    `price_benchmark` double DEFAULT '0' COMMENT '电价定值',

    `price_base_value_controller`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价最值开关(false关闭)(true打开）',
    `price_base_value_range_condition`  varchar(128) DEFAULT NULL COMMENT '电价最值条件',
    `price_base_value_section_count` int(11) DEFAULT 0 COMMENT '电价最值段数',

    `price_continue_duration_controller`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价连续时长开关(false关闭)(true打开）',
    `price_continue_duration_condition`  varchar(128) DEFAULT NULL COMMENT '电价连续时长条件',
    `price_continue_duration_hour` int(11) DEFAULT 0 COMMENT '电价连续小时数',

    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='策略control控制表';

-- 添加电表协议字段
alter table t_controllable
    add dev varchar(20) default null comment '设备名称';
alter table t_controllable
    add baud_rate int default null comment '波特率';
alter table t_controllable
    add byte_size int default null comment '数据位';
alter table t_controllable
    add stop_bits int default null comment '停止位';
alter table t_controllable
    add parity varchar(1) default null comment '奇偶校验';
alter table t_controllable
    add max_temp float default null comment '最高温度';
alter table t_controllable
    add min_temp float default null comment '最低温度';
alter table t_controllable
    add control_mode varchar(32) default null comment '控制模式';
alter table t_controllable
    add channel_ids varchar(512) default null comment '通道ID';
alter table t_controllable
    add device_ids varchar(512) default null comment '设备ID';



ALTER TABLE t_device
    ADD `project_run_init_in_data` double COMMENT '投运初始时间InData';
ALTER TABLE t_device
    ADD `project_run_init_out_data` double COMMENT '投运初始时间OutData';

ALTER TABLE t_ammeter
    ADD `project_run_init_in_data` double COMMENT '投运初始时间InData';
ALTER TABLE t_ammeter
    ADD `project_run_init_out_data` double COMMENT '投运初始时间OutData';

-- 设备增加水冷预启动时间(Min)
alter table t_device
    add cooler_pre_open_time int default null comment '水冷预启动时间(Min)';


-- 新增场站权限
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60020, 1, '/homePage/loadRegulation', '负荷调控');
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60021, 1, '/homePage/iframe', 'iframe');

INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60022, 3, '/group/configuration', '项目配置');
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60023, 60022, '/configuration/add', '新增配置');
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60024, 60022, '/configuration/update', '更新配置');
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60025, 60022, '/configuration/order', '配置排序');
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60026, 60022, '/configuration/delete', '删除配置');
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60027, 60022, '/configuration/list', '查看配置列表');

INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60028, 12, '/system/maintenance', '运维信息');
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60029, 60028, '/maintenance/query', '查看运维信息');
INSERT INTO t_authority (id, pid, ap_key, name) VALUES (60030, 60028, '/maintenance/update', '更新运维信息');

DELETE from t_authority where name = '时间策略导入';
UPDATE t_authority set ap_key = '/homePage/hookup', name = '一次接线图' where name = '接线图密码';

ALTER TABLE `t_strategy` ADD INDEX `idx_groupid_weekday_starttime` (`group_id`, `week_day`, `start_time`);
ALTER TABLE `t_strategy` ADD INDEX `idx_projectid_weekday` (`project_id`, `week_day`);
ALTER TABLE `t_strategy_control` ADD INDEX `idx_strategydate_projectid_groupid` (`strategy_date`, `project_id`, `group_id`);

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUE(10112,	10,	'/diagram/getTemperatureRate',	'温控曲线'),


CREATE TABLE `t_capacity_email` (
                                    `id` varchar(128) NOT NULL COMMENT '通知识符uuid',
                                    `email` varchar(32) DEFAULT NULL COMMENT '邮箱',
                                    `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                                    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
                                    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(128) DEFAULT NULL COMMENT '修改者',
                                    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
                                                              (10223,	28,	'/capacity/email/add',	'增加通知'),
                                                              (10224,	28,	'/capacity/email/edit',	'修改通知'),
                                                              (10225,	28,	'/capacity/email/delete',	'删除通知'),
                                                              (10226,	28,	'/capacity/email/query',	'查询通知');

ALTER TABLE t_device
    ADD `device_control_model` varchar(100) default 'PANGU_CONTROL' COMMENT 'device控制模式';
