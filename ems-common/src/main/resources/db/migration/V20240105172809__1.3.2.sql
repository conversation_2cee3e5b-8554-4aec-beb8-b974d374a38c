ALTER TABLE t_favourite_project ADD `email` tinyint(1) NULL DEFAULT '0' COMMENT '是否邮箱通知';
ALTER TABLE t_group ALTER COLUMN has_fire_fighting SET DEFAULT 1;

CREATE TABLE `t_country` (
                             `id` int NOT NULL,
                             `datacenter` int NOT NULL,
                             `phone_code` varchar(10) NOT NULL,
                             `zh_cn` varchar(128) DEFAULT NULL,
                             `en_us` varchar(128) DEFAULT NULL,
                             `de_de` varchar(128) DEFAULT NULL,
                             `nl_nl` varchar(128) DEFAULT NULL,
                             `fr_fr` varchar(128) DEFAULT NULL,
                             `es_es` varchar(128) DEFAULT NULL,
                             `pt_pt` varchar(128) DEFAULT NULL,
                             `it_it` varchar(128) DEFAULT NULL,
                             `pl_pl` varchar(128) DEFAULT NULL
) ENGINE='InnoDB' COLLATE 'utf8_general_ci';

INSERT INTO `t_country` (`id`, `datacenter`, `phone_code`, `zh_cn`, `en_us`, `de_de`, `nl_nl`, `fr_fr`, `es_es`, `pt_pt`, `it_it`, `pl_pl`) VALUES
(20,	1,	'49',	'库拉索',	'Curaçao',	'Curaçao',	'Curaçao',	'Curaçao',	'Curazao',	'Curaçao',	'Curaçao',	'Curaçao'),
(21,	2,	'61',	'马来西亚',	'Malaysia',	'Malaysia',	'Maleisië',	'Malaisie',	'Malasia',	'Malásia',	'Malesia',	'Malezja'),
(22,	2,	'61',	'印度尼西亚',	'Indonesia',	'Indonesien',	'Indonesië',	'Indonésie',	'Indonesia',	'Indonésia',	'Indonesia',	'Indonezja'),
(23,	2,	'61',	'菲律宾',	'Philippines',	'Philippinen',	'Filipijnen',	'Philippines',	'Filipinas',	'Filipinas',	'Filippine',	'Filipiny'),
(24,	2,	'61',	'新西兰',	'New Zealand',	'Neuseeland',	'Nieuw-Zeeland',	'Nouvelle-Zélande',	'Nueva Zelanda',	'Nova Zelândia',	'Nuova Zelanda',	'Nowa Zelandia'),
(25,	2,	'61',	'泰国',	'Thailand',	'Thailand',	'Thailand',	'Thaïlande',	'Tailandia',	'Tailândia',	'Thailandia',	'Tajlandia'),
(26,	2,	'61',	'日本',	'Japan',	'Japan',	'Japan',	'Japon',	'Japón',	'Japão',	'Giappone',	'Japonia'),
(27,	2,	'61',	'韩国',	'South Korea',	'Südkorea',	'Zuid-Korea',	'Corée du Sud',	'Corea del Sur',	'Coreia do Sul',	'Corea del Sud',	'Korea Południowa'),
(28,	2,	'61',	'越南',	'Vietnam',	'Vietnam',	'Vietnam',	'Vietnam',	'Vietnam',	'Vietname',	'Vietnam',	'Wietnam'),
(29,	2,	'61',	'香港(中国)',	'Hong Kong (People’s Republic of China)',	'Hongkong (Volksrepublik China)',	'Hong Kong (Volksrepubliek China)',	'Hong Kong (République populaire de Chine)',	'Hong Kong (República Popular China)',	'Hong Kong (República Popular da China)',	'Hong Kong (Repubblica Popolare Cinese)',	'Hongkong (Chińska Republika Ludowa)'),
(30,	2,	'61',	'澳门(中国)',	'Macao (People’s Republic of China)',	'Macao (Volksrepublik China)',	'Macau (Volksrepubliek China)',	'Macao (République populaire de Chine)',	'Macao (República Popular China)',	'Macau (República Popular da China)',	'Macao (Repubblica Popolare Cinese)',	'Makau (Chińska Republika Ludowa)'),
(31,	2,	'61',	'台湾(中国)',	'Taiwan (People’s Republic of China)',	'Taiwan (Volksrepublik China)',	'Taiwan (Volksrepubliek China)',	'Taïwan (République populaire de Chine)',	'Taiwán (República Popular China)',	'Taiwan (República Popular da China)',	'Taiwan (Repubblica Popolare Cinese)',	'Tajwan (Chińska Republika Ludowa)'),
(32,	2,	'61',	'缅甸',	'Myanmar',	'Myanmar',	'Myanmar',	'Myanmar',	'Myanmar',	'Birmânia',	'Myanmar',	'Myanmar (Birma)'),
(33,	2,	'61',	'圣多美和普林西比',	'São Tomé and Príncipe',	'São Tomé und Príncipe',	'São Tomé en Príncipe',	'Sao Tomé-et-Principe',	'Santo Tomé y Príncipe',	'São Tomé e Príncipe',	'Sao Tome e Principe',	'São Tomé i Príncipe'),
(34,	2,	'61',	'几内亚比绍',	'Guinea-Bissau',	'Guinea-Bissau',	'Guinee-Bissau',	'Guinée-Bissau',	'Guinea-Bisáu',	'Guiné-Bissau',	'Guinea-Bissau',	'Gwinea Bissau'),
(35,	1,	'49',	'英属印度洋领地',	'British Indian Ocean Territory',	'Britisches Territorium im Indischen Ozean',	'Brits Indische Oceaanterritorium',	'Territoire britannique de l’océan Indien',	'Territorio Británico del Océano Índico',	'Território Britânico do Oceano Índico',	'Territorio britannico dell Oceano Indiano',	'Brytyjskie Terytorium Oceanu Indyjskiego'),
(36,	1,	'49',	'福克兰群岛',	'Falkland Islands',	'Falklandinseln',	'Falklandeilanden',	'Îles Malouines',	'Islas Malvinas',	'Ilhas Malvinas',	'Isole Falkland',	'Falklandy'),
(37,	1,	'49',	'法属圭亚那',	'French Guiana',	'Französisch-Guayana',	'Frans-Guyana',	'Guyane française',	'Guayana Francesa',	'Guiana Francesa',	'Guiana Francese',	'Gujana Francuska'),
(38,	2,	'61',	'东帝汶',	'Timor-Leste',	'Osttimor',	'Oost-Timor',	'Timor-Leste',	'Timor Oriental',	'Timor-Leste',	'Timor Est',	'Timor Wschodni'),
(39,	2,	'61',	'诺福克岛',	'Norfolk Island',	'Norfolkinsel',	'Norfolkeiland',	'île de Norfolk',	'Isla Norfolk',	'Ilha de Norfolk',	'Isola Norfolk',	'Wyspa Norfolk'),
(40,	2,	'61',	'瑙鲁',	'Nauru',	'Nauru',	'Nauru',	'Nauru',	'Nauru',	'Nauru',	'Nauru',	'Nauru'),
(41,	2,	'61',	'巴布亚新几内亚',	'Papua New Guinea',	'Papua-Neuguinea',	'Papoea Nieuw-Guinea',	'Papouasie-Nouvelle-Guinée',	'Papúa Nueva Guinea',	'Papua Nova Guiné',	'Papua Nuova Guinea',	'Papua Nowa Gwinea'),
(42,	2,	'61',	'所罗门群岛',	'Solomon Islands',	'Salomonen',	'Salomonseilanden',	'Îles Solomon',	'Islas Salomón',	'Ilhas Salomão',	'Isole Salomone',	'Wyspy Salomona'),
(43,	2,	'61',	'瓦努阿图',	'Vanuatu',	'Vanuatu',	'Vanuatu',	'Vanuatu',	'Vanuatu',	'Vanuatu',	'Vanuatu',	'Vanuatu'),
(44,	2,	'61',	'库克群岛',	'Cook Islands',	'Cook-Inseln',	'Cookeilanden',	'Îles Cook',	'Islas Cook',	'Ilhas Cook',	'Isole Cook',	'Wyspy Cooka'),
(45,	2,	'61',	'纽埃',	'Niue',	'Niue',	'Niue',	'Nioué',	'Niue',	'Niue',	'Niue',	'Niue'),
(46,	2,	'61',	'基里巴斯',	'Kiribati',	'Kiribati',	'Kiribati',	'Khiribaz',	'Kiribati',	'Quiribati',	'Kiribati',	'Kiribati'),
(47,	2,	'61',	'托克劳',	'Tokelau',	'Tokelau',	'Tokelau',	'Tokélaou',	'Tokelau',	'Toquelau',	'Tokelau',	'Tokelau'),
(48,	2,	'61',	'巴勒斯坦',	'Palestine',	'Palästina',	'Palestina',	'Palestine',	'Palestina',	'Palestina',	'Palestina',	'Palestyna'),
(49,	1,	'49',	'荷属圣马丁',	'Sint Maarten',	'Sint Maarten',	'Sint Maarten',	'Saint-Martin',	'Sint Maarten',	'São Martinho',	'Sint Maarten',	'Sint Maarten'),
(50,	2,	'61',	'斯瓦尔巴和扬马延',	'Svalbard and Jan Mayen',	'Spitzbergen und Jan Mayen',	'Svalbard en Jan Mayen',	'Svalbard et Jan Mayen',	'Svalbard y Jan Mayen',	'Svalbard e Jan Mayen',	'Svalbard e Jan Mayen',	'Svalbard i Jan Mayen'),
(51,	1,	'49',	'奥兰',	'Åland Islands',	'Ålandinseln',	'Ålandseilanden',	'Îles Aland',	'Islas Aland',	'Ilhas de Alanda',	'Isole Aland',	'Wyspy Alandzkie'),
(54,	1,	'49',	'安圭拉',	'Anguilla',	'Anguilla',	'Anguilla',	'Anguilla',	'Anguila',	'Anguila',	'Anguilla',	'Anguilla'),
(56,	1,	'49',	'英属维京群岛',	'British Virgin Islands',	'Britische Jungferninseln',	'De Britse Maagdeneilanden',	'Îles Vierges britanniques',	'Islas Vírgenes Británicas',	'Ilhas Virgens Britânicas',	'Isole Vergini Britanniche',	'Brytyjskie Wyspy Dziewicze'),
(58,	1,	'49',	'开曼群岛',	'Cayman Islands',	'Cayman-Inseln',	'Kaaimaneilanden',	'Îles Caïman',	'Islas Caimán',	'Ilhas Caimão',	'Isole Cayman',	'Kajmany'),
(59,	1,	'49',	'百慕大',	'Bermuda',	'Bermuda',	'Bermuda',	'Bermudes',	'Bermudas',	'Bermuda',	'Bermuda',	'Bermudy'),
(62,	1,	'49',	'蒙特塞拉特',	'Montserrat',	'Montserrat',	'Montserrat',	'Montserrat',	'Montserrat',	'Montserrat',	'Montserrat',	'Montserrat'),
(63,	2,	'61',	'北马里亚纳群岛',	'Northern Mariana Islands',	'Nördliche Marianen',	'Noordelijke Marianen',	'Îles Mariannes du Nord',	'Islas Marianas del Norte',	'Ilhas Marianas do Norte',	'Isole Marianne Settentrionali',	'Mariany Północne'),
(64,	2,	'61',	'关岛',	'Guam',	'Guam',	'Guam',	'Guam',	'Guam',	'Guam',	'Guam',	'Guam'),
(65,	2,	'61',	'美属萨摩亚',	'American Samoa',	'Amerikanisch-Samoa',	'Amerikaans Samoa',	'Samoa américaines',	'Samoa Americana',	'Samoa Americana',	'Samoa Americane',	'Samoa Amerykańskie'),
(72,	2,	'61',	'埃及',	'Egypt',	'Ägypten',	'Egypte',	'Égypte',	'Egipto',	'Egito',	'Egitto',	'Egipt'),
(73,	2,	'61',	'摩洛哥',	'Morocco',	'Marokko',	'Marokko',	'Maroc',	'Marruecos',	'Marrocos',	'Marocco',	'Maroko'),
(74,	2,	'61',	'阿尔及利亚',	'Algeria',	'Algerien',	'Algerije',	'Algérie',	'Argelia',	'Argélia',	'Algeria',	'Algieria'),
(75,	2,	'61',	'突尼斯',	'Tunisia',	'Tunesien',	'Tunesië',	'Tunisie',	'Túnez',	'Tunísia',	'Tunisia',	'Tunezja'),
(76,	2,	'61',	'利比亚',	'Libya',	'Libyen',	'Libië',	'Libye',	'Libia',	'Líbia',	'Libia',	'Libia'),
(77,	2,	'61',	'冈比亚',	'The Gambia',	'Gambia',	'Gambia',	'Gambie',	'Gambia',	'Gâmbia',	'Gambia',	'Gambia'),
(78,	2,	'61',	'塞内加尔',	'Senegal',	'Senegal',	'Senegal',	'Sénégal',	'Senegal',	'Senegal',	'Senegal',	'Senegal'),
(79,	2,	'61',	'毛利塔尼亚',	'Mauritania',	'Mauretanien',	'Mauritanië',	'Mauritanie',	'Mauritania',	'Mauritânia',	'Mauritania',	'Mauretania'),
(80,	2,	'61',	'马里',	'Mali',	'Mali',	'Mali',	'Mali',	'Malí',	'Mali',	'Mali',	'Mali'),
(81,	2,	'61',	'几内亚',	'Guinea',	'Guinea',	'Guinea',	'Guinée',	'Guinea',	'Guiné',	'Guinea',	'Gwinea'),
(82,	2,	'61',	'科特迪瓦',	'Côte d’Ivoire',	'Elfenbeinküste',	'Ivoorkust',	'Côte d’Ivoire',	'Costa de Marfil',	'Costa do Marfim',	'Costa d Avorio',	'Wybrzeże Kości Słoniowej'),
(83,	2,	'61',	'布基纳法索',	'Burkina Faso',	'Burkina Faso',	'Burkina Faso',	'Burkina Faso',	'Burkina Faso',	'Burkina Faso',	'Burkina Faso',	'Burkina Faso'),
(84,	2,	'61',	'尼日尔',	'Niger',	'Niger',	'Niger',	'Niger',	'Níger',	'Níger',	'Niger',	'Niger'),
(85,	2,	'61',	'多哥',	'Togo',	'Togo',	'Togo',	'Togo',	'Togo',	'Togo',	'Togo',	'Togo'),
(86,	2,	'61',	'贝宁',	'Benin',	'Benin',	'Benin',	'Bénin',	'Benín',	'Benim',	'Benin',	'Benin'),
(87,	2,	'61',	'毛里求斯',	'Mauritius',	'Mauritius',	'Mauritius',	'Maurice',	'Mauricio',	'Maurícias',	'Mauritius',	'Mauritius'),
(88,	2,	'61',	'利比里亚',	'Liberia',	'Liberia',	'Liberia',	'Liberia',	'Liberia',	'Libéria',	'Liberia',	'Liberia'),
(89,	2,	'61',	'塞拉利昂',	'Sierra Leone',	'Sierra Leone',	'Sierra Leone',	'Sierra Leone',	'Sierra Leona',	'Serra Leoa',	'Sierra Leone',	'Sierra Leone'),
(90,	2,	'61',	'加纳',	'Ghana',	'Ghana',	'Ghana',	'Ghana',	'Ghana',	'Gana',	'Ghana',	'Ghana'),
(91,	2,	'61',	'尼日利亚',	'Nigeria',	'Nigeria',	'Nigeria',	'Nigeria',	'Nigeria',	'Nigéria',	'Nigeria',	'Nigeria'),
(92,	2,	'61',	'乍得',	'Chad',	'Tschad',	'Tsjaad',	'Tchad',	'Chad',	'Chade',	'Ciad',	'Czad'),
(93,	2,	'61',	'中非共和国',	'Central African Republic',	'Zentralafrikanische Republik',	'Centraal Afrikaanse Republiek',	'République centrafricaine',	'República Centroafricana',	'República Centro-Africana',	'Repubblica Centrafricana',	'Republika Środkowoafrykańska'),
(94,	2,	'61',	'喀麦隆',	'Cameroon',	'Kamerun',	'Kameroen',	'Cameroun',	'Camerún',	'Camarões',	'Camerun',	'Kamerun'),
(95,	2,	'61',	'佛得角',	'Cabo Verde',	'Kap Verde',	'Cabo Verde',	'Cap Vert',	'Cabo Verde',	'Cabo Verde',	'Capo Verde',	'Wyspy Zielonego Przylądka'),
(96,	2,	'61',	'赤道几内亚',	'Equatorial Guinea',	'Äquatorial Guinea',	'Equatoriaal-Guinea',	'Guinée équatoriale',	'Guinea Ecuatorial',	'Guiné-Equatorial',	'Guinea Equatoriale',	'Gwinea Równikowa'),
(97,	2,	'61',	'加蓬',	'Gabon',	'Gabun',	'Gabon',	'Gabon',	'Gabón',	'Gabão',	'Gabon',	'Gabon'),
(98,	2,	'61',	'刚果(布)',	'Congo',	'Kongo',	'Congo',	'Congo',	'Congo',	'Congo',	'Congo',	'Kongo'),
(99,	2,	'61',	'刚果(金)',	'Congo (DRC)',	'Kongo (DRK)',	'Congo (DRC)',	'Congo (RDC)',	'Congo (RDC)',	'Congo (RDC)',	'Congo (RDC)',	'Kongo (DRK)'),
(100,	2,	'61',	'安哥拉',	'Angola',	'Angola',	'Angola',	'Angola',	'Angola',	'Angola',	'Angola',	'Angola'),
(101,	2,	'61',	'塞舌尔',	'Seychelles',	'Seychellen',	'Seychellen',	'Seychelles',	'Seychelles',	'Seychelles',	'Seychelles',	'Seszele'),
(102,	2,	'61',	'卢旺达',	'Rwanda',	'Ruanda',	'Rwanda',	'Rwanda',	'Ruanda',	'Ruanda',	'Ruanda',	'Rwanda'),
(103,	2,	'61',	'埃塞俄比亚',	'Ethiopia',	'Äthiopien',	'Ethiopië',	'Éthiopie',	'Etiopía',	'Etiópia',	'Etiopia',	'Etiopia'),
(104,	2,	'61',	'索马里',	'Somalia',	'Somalia',	'Somalië',	'Somalie',	'Somalia',	'Somália',	'Somalia',	'Somalia'),
(105,	2,	'61',	'吉布提',	'Djibouti',	'Dschibuti',	'Djibouti',	'Djibouti',	'Yibuti',	'Djibouti',	'Gibuti',	'Dżibuti'),
(106,	2,	'61',	'肯尼亚',	'Kenya',	'Kenia',	'Kenia',	'Kenya',	'Kenia',	'Quénia',	'Kenya',	'Kenia'),
(107,	2,	'61',	'坦桑尼亚',	'Tanzania',	'Tansania',	'Tanzania',	'Tanzanie',	'Tanzania',	'Tanzânia',	'Tanzania',	'Tanzania'),
(108,	2,	'61',	'乌干达',	'Uganda',	'Uganda',	'Oeganda',	'Ouganda',	'Uganda',	'Uganda',	'Uganda',	'Uganda'),
(109,	2,	'61',	'布隆迪',	'Burundi',	'Burundi',	'Burundi',	'Burundi',	'Burundi',	'Burundi',	'Burundi',	'Burundi'),
(110,	2,	'61',	'莫桑比克',	'Mozambique',	'Mosambik',	'Mozambique',	'Mozambique',	'Mozambique',	'Moçambique',	'Mozambico',	'Mozambik'),
(111,	2,	'61',	'赞比亚',	'Zambia',	'Sambia',	'Zambia',	'Zambie',	'Zambia',	'Zâmbia',	'Zambia',	'Zambia'),
(112,	2,	'61',	'马达加斯加',	'Madagascar',	'Madagaskar',	'Madagaskar',	'Madagascar',	'Madagascar',	'Madagáscar',	'Madagascar',	'Madagaskar'),
(113,	1,	'49',	'马约特',	'Mayotte',	'Mayotte',	'Mayotte',	'Mayotte',	'Mayotte',	'Mayotte',	'Mayotte',	'Majotta'),
(114,	2,	'61',	'津巴布韦',	'Zimbabwe',	'Simbabwe',	'Zimbabwe',	'Zimbabwe',	'Zimbabue',	'Zimbabué',	'Zimbabwe',	'Zimbabwe'),
(115,	2,	'61',	'纳米比亚',	'Namibia',	'Namibia',	'Namibië',	'Namibie',	'Namibia',	'Namíbia',	'Namibia',	'Namibia'),
(116,	2,	'61',	'马拉维',	'Malawi',	'Malawi',	'Malawi',	'Malawi',	'Malaui',	'Malawi',	'Malawi',	'Malawi'),
(117,	2,	'61',	'莱索托',	'Lesotho',	'Lesotho',	'Lesotho',	'Lesotho',	'Lesoto',	'Lesoto',	'Lesotho',	'Lesotho'),
(118,	2,	'61',	'博茨瓦纳',	'Botswana',	'Botsuana',	'Botswana',	'Botswana',	'Botsuana',	'Botswana',	'Botswana',	'Botswana'),
(119,	2,	'61',	'斯威士兰',	'Swaziland',	'Swasiland',	'Swaziland',	'Swaziland',	'Esuatini',	'Suazilândia',	'eSwatini',	'Suazi'),
(120,	2,	'61',	'科摩罗',	'Comoros',	'Komoren',	'Comoren',	'Comores',	'Comoras',	'Comores',	'Comore',	'Komory'),
(121,	2,	'61',	'南非',	'South Africa',	'Südafrika',	'Zuid-Afrika',	'Afrique du Sud',	'Sudáfrica',	'África do Sul',	'Sudafrica',	'Południowa Afryka'),
(122,	2,	'61',	'厄立特里亚',	'Eritrea',	'Eritrea',	'Eritrea',	'Érythrée',	'Eritrea',	'Eritreia',	'Eritrea',	'Erytrea'),
(123,	1,	'49',	'阿鲁巴',	'Aruba',	'Aruba',	'Aruba',	'Aruba',	'Aruba',	'Aruba',	'Aruba',	'Aruba'),
(124,	1,	'49',	'法罗群岛',	'Faroe Islands',	'Färöer-Inseln',	'Faeröer eilanden',	'Îles Féroé',	'Islas Feroe',	'Ilhas Faroé',	'Fær Øer',	'Wyspy Owcze'),
(125,	1,	'49',	'格陵兰',	'Greenland',	'Grönland',	'Groenland',	'Groenland',	'Groenlandia',	'Gronelândia',	'Groenlandia',	'Grenlandia'),
(126,	1,	'49',	'希腊',	'Greece',	'Griechenland',	'Griekenland',	'Grèce',	'Grecia',	'Grécia',	'Grecia',	'Grecja'),
(127,	1,	'49',	'荷兰',	'Netherlands',	'Niederlande',	'Nederland',	'Pays-Bas',	'Países Bajos',	'Países Baixos',	'Paesi Bassi',	'Holandia'),
(128,	1,	'49',	'比利时',	'Belgium',	'Belgien',	'België',	'Belgique',	'Bélgica',	'Bélgica',	'Belgio',	'Belgia'),
(129,	1,	'49',	'法国',	'France',	'Frankreich',	'Frankrijk',	'France',	'Francia',	'França',	'Francia',	'Francja'),
(130,	1,	'49',	'西班牙',	'Spain',	'Spanien',	'Spanje',	'Espagne',	'España',	'Espanha',	'Spagna',	'Hiszpania'),
(131,	1,	'49',	'直布罗陀',	'Gibraltar',	'Gibraltar',	'Gibraltar',	'Gibraltar',	'Gibraltar',	'Gibraltar',	'Gibilterra',	'Gibraltar'),
(132,	1,	'49',	'葡萄牙',	'Portugal',	'Portugal',	'Portugal',	'Portugal',	'Portugal',	'Portugal',	'Portogallo',	'Portugalia'),
(133,	1,	'49',	'卢森堡',	'Luxembourg',	'Luxemburg',	'Luxemburg',	'Luxembourg',	'Luxemburgo',	'Luxemburgo',	'Lussemburgo',	'Luksemburg'),
(134,	1,	'49',	'爱尔兰',	'Ireland',	'Irland',	'Ierland',	'Irlande',	'Irlanda',	'Irlanda',	'Irlanda',	'Irlandia'),
(135,	1,	'49',	'冰岛',	'Iceland',	'Island',	'IJsland',	'Islande',	'Islandia',	'Islândia',	'Islanda',	'Islandia'),
(136,	1,	'49',	'阿尔巴尼亚',	'Albania',	'Albanien',	'Albanië',	'Albanie',	'Albania',	'Albânia',	'Albania',	'Albania'),
(137,	1,	'49',	'马耳他',	'Malta',	'Malta',	'Malta',	'Malte',	'Malta',	'Malta',	'Malta',	'Malta'),
(138,	1,	'49',	'塞浦路斯',	'Cyprus',	'Zypern',	'Cyprus',	'Chypre',	'Chipre',	'Chipre',	'Cipro',	'Cypr'),
(139,	1,	'49',	'芬兰',	'Finland',	'Finnland',	'Finland',	'Finlande',	'Finlandia',	'Finlândia',	'Finlandia',	'Finlandia'),
(140,	1,	'49',	'保加利亚',	'Bulgaria',	'Bulgarien',	'Bulgarije',	'Bulgarie',	'Bulgaria',	'Bulgária',	'Bulgaria',	'Bułgaria'),
(141,	1,	'49',	'匈牙利',	'Hungary',	'Ungarn',	'Hongarije',	'Hongrie',	'Hungría',	'Hungria',	'Ungheria',	'Węgry'),
(142,	1,	'49',	'立陶宛',	'Lithuania',	'Litauen',	'Litouwen',	'Lituanie',	'Lituania',	'Lituânia',	'Lituania',	'Litwa'),
(143,	1,	'49',	'拉脱维亚',	'Latvia',	'Lettland',	'Letland',	'Lettonie',	'Letonia',	'Letónia',	'Lettonia',	'Łotwa'),
(144,	1,	'49',	'爱沙尼亚',	'Estonia',	'Estland',	'Estland',	'Estonie',	'Estonia',	'Estónia',	'Estonia',	'Estonia'),
(145,	1,	'49',	'摩尔多瓦',	'Moldova',	'Republik Moldau',	'Moldavië',	'Moldavie',	'Moldavia',	'Moldávia',	'Moldova',	'Mołdawia'),
(146,	2,	'61',	'亚美尼亚',	'Armenia',	'Armenien',	'Armenië',	'Arménie',	'Armenia',	'Arménia',	'Armenia',	'Armenia'),
(147,	1,	'49',	'白俄罗斯',	'Belarus',	'Weißrussland',	'Wit-Rusland',	'Biélorussie',	'Bielorrusia',	'Bielorrússia',	'Bielorussia',	'Białoruś'),
(148,	1,	'49',	'安道尔',	'Andorra',	'Andorra',	'Andorra',	'Andorre',	'Andorra',	'Andorra',	'Andorra',	'Andora'),
(149,	1,	'49',	'摩纳哥',	'Monaco',	'Monaco',	'Monaco',	'Monaco',	'Mónaco',	'Mónaco',	'Monaco',	'Monako'),
(150,	1,	'49',	'圣马力诺',	'San Marino',	'San Marino',	'San Marino',	'Saint-Marin',	'San Marino',	'San Marino',	'San Marino',	'San Marino'),
(151,	1,	'49',	'梵蒂冈城',	'Vatican City',	'Vatikanstadt',	'Vaticaanstad',	'Cité du Vatican',	'Ciudad del Vaticano',	'Cidade do Vaticano',	'Città del Vaticano',	'Watykan'),
(152,	1,	'49',	'乌克兰',	'Ukraine',	'Ukraine',	'Oekraïne',	'Ukraine',	'Ucrania',	'Ucrânia',	'Ucraina',	'Ukraina'),
(153,	1,	'49',	'塞尔维亚',	'Serbia',	'Serbien',	'Servië',	'Serbie',	'Serbia',	'Sérvia',	'Serbia',	'Serbia'),
(154,	1,	'49',	'黑山共和国',	'Montenegro',	'Montenegro',	'Montenegro',	'Monténégro',	'Montenegro',	'Montenegro',	'Montenegro',	'Czarnogóra'),
(155,	1,	'49',	'克罗地亚',	'Croatia',	'Kroatien',	'Kroatië',	'Croatie',	'Croacia',	'Croácia',	'Croazia',	'Chorwacja'),
(156,	1,	'49',	'斯洛文尼亚',	'Slovenia',	'Slovenien',	'Slovenië',	'Slovénie',	'Eslovenia',	'Eslovénia',	'Slovenia',	'Słowenia'),
(157,	1,	'49',	'波斯尼亚和黑塞哥维那',	'Bosnia and Herzegovina',	'Bosnien und Herzegowina',	'Bosnië en Herzegovina',	'Bosnie-Herzégovine',	'Bosnia y Herzegovina',	'Bósnia e Herzegovina',	'Bosnia Erzegovina',	'Bośnia i Hercegowina'),
(158,	1,	'49',	'马其顿',	'Macedonian',	'Mazedonien',	'Macedonië',	'Macédoine',	'Macedonia',	'Macedónia',	'Macedonia',	'Macedonia'),
(159,	1,	'49',	'意大利',	'Italy',	'Italien',	'Italië',	'Italie',	'Italia',	'Itália',	'Italia',	'Włochy'),
(160,	1,	'49',	'罗马尼亚',	'Romania',	'Rumänien',	'Roemenië',	'Roumanie',	'Rumanía',	'Roménia',	'Romania',	'Rumunia'),
(161,	1,	'49',	'瑞士',	'Switzerland',	'Schweiz',	'Zwitserland',	'Suisse',	'Suiza',	'Suíça',	'Svizzera',	'SzwajcariaKuwejt'),
(162,	1,	'49',	'捷克共和国',	'Czech Republic',	'Tschechische Republik',	'Tsjechië',	'République tchèque',	'República Checa',	'República Checa',	'Repubblica Ceca',	'Czechy'),
(163,	1,	'49',	'斯洛伐克',	'Slovakia',	'Slowakei',	'Slowakije',	'Slovaquie',	'Eslovaquia',	'Eslováquia',	'Slovacchia',	'Słowacja'),
(164,	1,	'49',	'列支敦士登',	'Liechtenstein',	'Liechtenstein',	'Liechtenstein',	'Liechtenstein',	'Liechtenstein',	'Liechtenstein',	'Liechtenstein',	'Liechtenstein'),
(165,	1,	'49',	'奥地利',	'Austria',	'Österreich',	'Oostenrijk',	'Autriche',	'Austria',	'Áustria',	'Austria',	'Austria'),
(166,	1,	'49',	'英国',	'United Kingdom',	'Großbritannien',	'Verenigd Koninkrijk',	'Royaume-Uni',	'Reino Unido',	'Reino Unido',	'Regno Unito',	'Wielka Brytania'),
(167,	1,	'49',	'丹麦',	'Denmark',	'Dänemark',	'Denemarken',	'Danemark',	'Dinamarca',	'Dinamarca',	'Danimarca',	'Dania'),
(168,	1,	'49',	'瑞典',	'Sweden',	'Schweden',	'Zweden',	'Suède',	'Suecia',	'Suécia',	'Svezia',	'Szwecja'),
(169,	1,	'49',	'挪威',	'Norway',	'Norwegen',	'Noorwegen',	'Norvège',	'Noruega',	'Noruega',	'Norvegia',	'Norwegia'),
(170,	1,	'49',	'波兰',	'Poland',	'Polen',	'Polen',	'Pologne',	'Polonia',	'Polónia',	'Polonia',	'Polska'),
(171,	1,	'49',	'德国',	'Germany',	'Deutschland',	'Duitsland',	'Allemagne',	'Alemania',	'Alemanha',	'Germania',	'Niemcy'),
(180,	1,	'49',	'法属圣马丁',	'Saint Martin',	'Saint-Martin',	'Sint Maarten',	'Saint-Martin',	'San Martín',	'Ilha de São Martinho',	'Saint Martin',	'Saint Martin'),
(182,	1,	'49',	'马提尼克',	'Martinique',	'Martinique',	'Martinique',	'Martinique',	'Martinica',	'Martinica',	'Martinica',	'Martynika'),
(183,	2,	'61',	'澳大利亚',	'Australia',	'Australien',	'Australië',	'Australie',	'Australia',	'Austrália',	'Australia',	'Australia'),
(184,	2,	'61',	'新加坡',	'Singapore',	'Singapur',	'Singapore',	'Singapour',	'Singapur',	'Singapura',	'Singapore',	'Singapur'),
(185,	2,	'61',	'文莱',	'Brunei',	'Brunei',	'Brunei',	'Brunei',	'Brunéi',	'Brunei',	'Brunei',	'Brunei'),
(186,	2,	'61',	'汤加',	'Tonga',	'Tonga',	'Tonga',	'Tonga',	'Tonga',	'Tonga',	'Tonga',	'Tonga'),
(187,	2,	'61',	'斐济',	'Fiji',	'Fidschi',	'Fiji',	'Fidji',	'Fiyi',	'Fiji',	'Figi',	'Fidżi'),
(188,	2,	'61',	'帕劳',	'Palau',	'Palau',	'Palau',	'Palaos',	'Palaos',	'Palau',	'Palau',	'Palau'),
(189,	2,	'61',	'瓦利斯和富图纳',	'Wallis and Futuna',	'Wallis und Futuna Inseln',	'Wallis en Futuna',	'Wallis et Futuna',	'Wallis y Futuna',	'Wallis e Futuna',	'Wallis e Futuna',	'Wallis i Futuna'),
(190,	2,	'61',	'萨摩亚',	'Samoa',	'Samoa',	'Samoa',	'Samoa',	'Samoa',	'Samoa',	'Samoa',	'Samoa'),
(191,	1,	'49',	'新喀里多尼亚',	'New Caledonia',	'Neukaledonien',	'Nieuw-Caledonië',	'Nouvelle-Calédonie',	'Nueva Caledonia',	'Nova Caledónia',	'Nuova Caledonia',	'Nowa Kaledonia'),
(192,	2,	'61',	'图瓦卢',	'Tuvalu',	'Tuvalu',	'Tuvalu',	'Tuvalu',	'Tuvalu',	'Tuvalu',	'Tuvalu',	'Tuvalu'),
(193,	1,	'49',	'法属玻利尼西亚',	'French Polynesia',	'Französisch-Polynesien',	'Frans-Polynesië',	'Polynésie française',	'Polinesia Francesa',	'Polinésia Francesa',	'Polinesia Francese',	'Polinezja Francuska'),
(194,	2,	'61',	'密克罗尼西亚联邦',	'Federated States of Micronesia',	'Föderierte Staaten von Mikronesien',	'Federale Staten van Micronesia',	'États fédérés de Micronésie',	'Estados Federados de Micronesia',	'Estados Federados da Micronésia',	'Stati Federati della Micronesia',	'Sfederowane Stany Mikronezji'),
(195,	2,	'61',	'马绍尔群岛',	'Marshall Islands',	'Marschall-Inseln',	'Marshalleilanden',	'Îles Marshall',	'Islas Marshall',	'Ilhas Marshall',	'Isole Marshall',	'Wyspy Marshalla'),
(196,	1,	'49',	'俄罗斯',	'Russia',	'Russland',	'Rusland',	'Russie',	'Rusia',	'Rússia',	'Russia',	'Rosja'),
(197,	2,	'61',	'柬埔寨',	'Cambodia',	'Kambodscha',	'Cambodja',	'Cambodge',	'Camboya',	'Camboja',	'Cambogia',	'Kambodża'),
(198,	2,	'61',	'老挝',	'Laos',	'Laos',	'Laos',	'Laos',	'Laos',	'Laos',	'Laos',	'Laos'),
(199,	2,	'61',	'孟加拉国',	'Bangladesh',	'Bangladesch',	'Bangladesh',	'Bangladesh',	'Bangladés',	'Bangladesh',	'Bangladesh',	'Bangladesz'),
(200,	2,	'61',	'土耳其',	'Turkey',	'Türkei',	'Turkije',	'Turquie',	'Turquía',	'Turquia',	'Turchia',	'Turcja'),
(201,	2,	'61',	'巴基斯坦',	'Pakistan',	'Pakistan',	'Pakistan',	'Pakistan',	'Pakistán',	'Paquistão',	'Pakistan',	'Pakistan'),
(202,	2,	'61',	'阿富汗',	'Afghanistan',	'Afghanistan',	'Afghanistan',	'Afghanistan',	'Afganistán',	'Afeganistão',	'Afghanistan',	'Afganistan'),
(203,	2,	'61',	'斯里兰卡',	'Sri Lanka',	'Sri Lanka',	'Sri Lanka',	'Sri Lanka',	'Sri Lanka',	'Sri Lanka',	'Sri Lanka',	'Sri Lanka'),
(204,	2,	'61',	'马尔代夫',	'Maldives',	'Malediven',	'Maldiven',	'Maldives',	'Maldivas',	'Maldivas',	'Maldive',	'Malediwy'),
(205,	2,	'61',	'黎巴嫩',	'Lebanon',	'Libanon',	'Libanon',	'Liban',	'Líbano',	'Líbano',	'Libano',	'Liban'),
(206,	2,	'61',	'约旦',	'Jordan',	'Jordanien',	'Jordanië',	'Jordanie',	'Jordania',	'Jordânia',	'Giordania',	'Jordania'),
(207,	2,	'61',	'伊拉克',	'Iraq',	'Irak',	'Irak',	'Irak',	'Iraq',	'Iraque',	'Iraq',	'Irak'),
(208,	2,	'61',	'科威特',	'Kuwait',	'Kuwait',	'Koeweit',	'Koweït',	'Kuwait',	'Kuwait',	'Kuwait',	'Kuwejt'),
(209,	2,	'61',	'沙特阿拉伯',	'Saudi Arabia',	'Saudi-Arabien',	'Saoedi-Arabië',	'Arabie saoudite',	'Arabia Saudita',	'Arábia Saudita',	'Arabia Saudita',	'Arabia Saudyjska'),
(210,	2,	'61',	'也门',	'Yemen',	'Jemen',	'Jemen',	'Yémen',	'Yemen',	'Iémen',	'Yemen',	'Jemen'),
(211,	2,	'61',	'阿曼',	'Oman',	'Oman',	'Oman',	'Oman',	'Omán',	'Omã',	'Oman',	'Oman'),
(212,	2,	'61',	'阿拉伯联合酋长国',	'United Arab Emirates',	'Vereinigte Arabische Emirate',	'Verenigde Arabische Emiraten',	'Émirats arabes unis',	'Emiratos Árabes Unidos',	'Emirados Árabes Unidos',	'Emirati Arabi Uniti',	'Zjednoczone Emiraty Arabskie'),
(213,	2,	'61',	'以色列',	'Israel',	'Israel',	'Israël',	'Israël',	'Israel',	'Israel',	'Israele',	'Izrael'),
(214,	2,	'61',	'巴林',	'Bahrain',	'Bahrain',	'Bahrein',	'Bahreïn',	'Baréin',	'Bahrein',	'Bahrein',	'Bahrajn'),
(215,	2,	'61',	'卡塔尔',	'Qatar',	'Katar',	'Qatar',	'Qatar',	'Qatar',	'Qatar',	'Qatar',	'Katar'),
(216,	2,	'61',	'不丹',	'Bhutan',	'Bhutan',	'Bhutan',	'Bhoutan',	'Bután',	'Butão',	'Bhutan',	'Bhutan'),
(217,	2,	'61',	'蒙古',	'Mongolia',	'Mongolei',	'Mongolië',	'Mongolie',	'Mongolia',	'Mongólia',	'Mongolia',	'Mongolia'),
(218,	2,	'61',	'尼泊尔',	'Nepal',	'Nepal',	'Nepal',	'Népal',	'Nepal',	'Nepal',	'Nepal',	'Nepal'),
(219,	2,	'61',	'塔吉克斯坦',	'Tajikistan',	'Tadschikistan',	'Tadzjikistan',	'Tadjikistan',	'Tayikistán',	'Tajiquistão',	'Tagikistan',	'Tadżykistan'),
(220,	2,	'61',	'土库曼斯坦',	'Turkmenistan',	'Turkmenistan',	'Turkmenistan',	'Turkménistan',	'Turkmenistán',	'Turquemenistão',	'Turkmenistan',	'Turkmenistan'),
(221,	2,	'61',	'阿塞拜疆',	'Azerbaijan',	'Aserbaidschan',	'Azerbeidzjan',	'Azerbaïdjan',	'Azerbaiyán',	'Azerbaijão',	'Azerbaigian',	'Azerbejdżan'),
(222,	2,	'61',	'格鲁吉亚',	'Georgia',	'Georgien',	'Georgië',	'Géorgie',	'Georgia',	'Geórgia',	'Georgia',	'Gruzja'),
(223,	2,	'61',	'吉尔吉斯斯坦',	'Kyrgyzstan',	'Kirgisistan',	'Kirgizië',	'Kirghizistan',	'Kirguistán',	'Quirguistão',	'Kirghizistan',	'Kirgistan'),
(224,	2,	'61',	'乌兹别克斯坦',	'Uzbekistan',	'Usbekistan',	'Oezbekistan',	'Ouzbékistan',	'Uzbekistán',	'Uzbequistão',	'Uzbekistan',	'Uzbekistan'),
(226,	3,	'86',	'中国',	'China',	'China',	'China',	'Chine',	'China',	'China',	'Cina',	'Chiny');


CREATE TABLE `t_time_zone` (
                               `id` int(11) NOT NULL AUTO_INCREMENT,
                               `timezone` varchar(100) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
                               `name` varchar(20) COLLATE utf8_unicode_ci NOT NULL,
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

INSERT INTO `t_time_zone` (`id`, `timezone`, `name`) VALUES
 (1,	'Africa/Abidjan',	'非洲/阿比让'),
 (2,	'Africa/Accra',	'非洲/阿克拉'),
 (3,	'Africa/Addis_Ababa',	'非洲/亚的斯亚贝巴'),
 (4,	'Africa/Algiers',	'非洲/阿尔及尔'),
 (5,	'Africa/Asmara',	'非洲/阿斯马拉'),
 (6,	'Africa/Bamako',	'非洲/巴马科'),
 (7,	'Africa/Bangui',	'非洲/班吉'),
 (8,	'Africa/Banjul',	'非洲/班珠尔'),
 (9,	'Africa/Bissau',	'非洲/比绍'),
 (10,	'Africa/Blantyre',	'非洲/布兰太尔'),
 (11,	'Africa/Brazzaville',	'非洲/布拉柴维尔'),
 (12,	'Africa/Bujumbura',	'非洲/布琼布拉'),
 (13,	'Africa/Cairo',	'非洲/开罗'),
 (14,	'Africa/Casablanca',	'非洲/卡萨布兰卡'),
 (15,	'Africa/Ceuta',	'非洲/休达'),
 (16,	'Africa/Conakry',	'非洲/科纳克里'),
 (17,	'Africa/Dakar',	'非洲/达卡'),
 (18,	'Africa/Dar_es_Salaam',	'非洲/达累斯萨拉姆'),
 (19,	'Africa/Djibouti',	'非洲/吉布提'),
 (20,	'Africa/Douala',	'非洲/杜阿拉'),
 (21,	'Africa/El_Aaiun',	'非洲/阿尤恩'),
 (22,	'Africa/Freetown',	'非洲/弗里敦'),
 (23,	'Africa/Gaborone',	'非洲/哈博罗内'),
 (24,	'Africa/Harare',	'非洲/哈拉雷'),
 (25,	'Africa/Johannesburg',	'非洲/约翰内斯堡'),
 (26,	'Africa/Juba',	'非洲/朱巴'),
 (27,	'Africa/Kampala',	'非洲/坎帕拉'),
 (28,	'Africa/Khartoum',	'非洲/喀土穆'),
 (29,	'Africa/Kigali',	'非洲/基加利'),
 (30,	'Africa/Kinshasa',	'非洲/金沙萨'),
 (31,	'Africa/Lagos',	'非洲/拉戈斯'),
 (32,	'Africa/Libreville',	'非洲/利伯维尔'),
 (33,	'Africa/Lome',	'非洲/洛美'),
 (34,	'Africa/Luanda',	'非洲/罗安达'),
 (35,	'Africa/Lubumbashi',	'非洲/卢本巴希'),
 (36,	'Africa/Lusaka',	'非洲/卢萨卡'),
 (37,	'Africa/Malabo',	'非洲/马拉博'),
 (38,	'Africa/Maputo',	'非洲/马普托'),
 (39,	'Africa/Maseru',	'非洲/马塞卢'),
 (40,	'Africa/Mbabane',	'非洲/巴巴内'),
 (41,	'Africa/Mogadishu',	'非洲/摩加迪沙'),
 (42,	'Africa/Monrovia',	'非洲/蒙罗维亚'),
 (43,	'Africa/Nairobi',	'非洲/内罗比'),
 (44,	'Africa/Ndjamena',	'非洲/恩贾梅纳'),
 (45,	'Africa/Niamey',	'非洲/尼亚美'),
 (46,	'Africa/Nouakchott',	'非洲/努瓦克肖特'),
 (47,	'Africa/Ouagadougou',	'非洲/瓦加杜古'),
 (48,	'Africa/Porto-Novo',	'非洲/波尔图诺沃'),
 (49,	'Africa/Sao_Tome',	'非洲/圣多美'),
 (50,	'Africa/Timbuktu',	'非洲/廷巴克图'),
 (51,	'Africa/Tripoli',	'非洲/的黎波里'),
 (52,	'Africa/Tunis',	'非洲/图尼'),
 (53,	'Africa/Windhoek',	'非洲/温德霍克'),
 (54,	'America/Adak',	'美洲/阿达克'),
 (55,	'America/Anchorage',	'美洲/安克雷奇'),
 (56,	'America/Anguilla',	'美洲/安圭拉'),
 (57,	'America/Antigua',	'美洲/安提瓜'),
 (58,	'America/Araguaina',	'美洲/阿拉瓜'),
 (59,	'America/Argentina/Buenos_Aires',	'美洲/阿根廷/布宜诺斯艾利斯'),
 (60,	'America/Argentina/Catamarca',	'美洲/阿根廷/卡塔马卡'),
 (61,	'America/Argentina/ComodRivadavia',	'美洲/阿根廷/科莫德-里瓦达维亚'),
 (62,	'America/Argentina/Cordoba',	'美洲/阿根廷/科尔多瓦'),
 (63,	'America/Argentina/Jujuy',	'美洲/阿根廷/胡胡伊'),
 (64,	'America/Argentina/La_Rioja',	'美洲/阿根廷/拉里奥哈'),
 (65,	'America/Argentina/Mendoza',	'美洲/阿根廷/门多萨'),
 (66,	'America/Argentina/Rio_Gallegos',	'美洲/阿根廷/里奥加耶戈斯'),
 (67,	'America/Argentina/Salta',	'美洲/阿根廷/萨尔塔'),
 (68,	'America/Argentina/San_Juan',	'美洲/阿根廷/圣胡安'),
 (69,	'America/Argentina/San_Luis',	'美洲/阿根廷/圣卢斯'),
 (70,	'America/Argentina/Tucuman',	'美洲/阿根廷/图库曼'),
 (71,	'America/Argentina/Ushuaia',	'美洲/阿根廷/乌斯怀亚'),
 (72,	'America/Aruba',	'美洲/阿鲁巴'),
 (73,	'America/Asuncion',	'美洲/亚松森'),
 (74,	'America/Atikokan',	'美洲/阿提科坎'),
 (75,	'America/Atka',	'美洲/阿特卡'),
 (76,	'America/Bahia',	'美洲/巴希亚'),
 (77,	'America/Barbados',	'美洲/巴巴多斯'),
 (78,	'America/Belem',	'美洲/贝勒姆'),
 (79,	'America/Belize',	'美洲/贝利泽'),
 (80,	'America/Blanc-Sablon',	'美洲/布朗-萨布隆'),
 (81,	'America/Boa_Vista',	'美洲/博阿维斯塔'),
 (82,	'America/Bogota',	'美洲/波哥大'),
 (83,	'America/Boise',	'美洲/博伊西'),
 (84,	'America/Buenos_Aires',	'美洲/布宜诺斯艾利斯'),
 (85,	'America/Cambridge_Bay',	'美洲/坎布里奇湾'),
 (86,	'America/Campo_Grande',	'美洲/坎波格兰德'),
 (87,	'America/Cancun',	'美洲/坎昆'),
 (88,	'America/Caracas',	'美洲/加拉加斯'),
 (89,	'America/Catamarca',	'美洲/卡塔马卡'),
 (90,	'America/Cayenne',	'美洲/卡宴'),
 (91,	'America/Cayman',	'美洲/开曼岛'),
 (92,	'America/Chicago',	'美洲/芝加哥'),
 (93,	'America/Chihuahua',	'美洲/奇瓦瓦'),
 (94,	'America/Coral_Harbour',	'美洲/珊瑚港'),
 (95,	'America/Cordoba',	'美洲/科尔多瓦'),
 (96,	'America/Costa_Rica',	'美洲/哥斯达黎加'),
 (97,	'America/Creston',	'美洲/克雷斯顿'),
 (98,	'America/Cuiaba',	'美洲/库亚巴'),
 (99,	'America/Curacao',	'美洲/库拉索'),
 (100,	'America/Danmarkshavn',	'美洲/丹麦港'),
 (101,	'America/Dawson',	'美洲/道森'),
 (102,	'America/Denver',	'美洲/丹佛'),
 (103,	'America/Detroit',	'美洲/底特律'),
 (104,	'America/Dominica',	'美洲/多米尼克'),
 (105,	'America/Edmonton',	'美洲/埃德蒙顿'),
 (106,	'America/Eirunepe',	'美洲/埃鲁内佩'),
 (107,	'America/El_Salvador',	'美洲/萨尔瓦多'),
 (108,	'America/Ensenada',	'美洲/恩塞纳达'),
 (109,	'America/Fort_Nelson',	'美洲/尼尔森堡'),
 (110,	'America/Fort_Wayne',	'美洲/韦恩堡'),
 (111,	'America/Fortaleza',	'美洲/费尔塔莱萨'),
 (112,	'America/Glace_Bay',	'美洲/格莱斯贝'),
 (113,	'America/Godthab',	'美洲/戈特霍布'),
 (114,	'America/Goose_Bay',	'美洲/鹅卵湾'),
 (115,	'America/Grand_Turk',	'美洲/大特克岛'),
 (116,	'America/Grenada',	'美洲/格林纳达'),
 (117,	'America/Guadeloupe',	'美洲/瓜德罗普岛'),
 (118,	'America/Guatemala',	'美洲/危地马拉'),
 (119,	'America/Guayaquil',	'美洲/瓜亚基尔'),
 (120,	'America/Guyana',	'美洲/圭亚那'),
 (121,	'America/Halifax',	'美洲/哈利法克斯'),
 (122,	'America/Havana',	'美洲/哈瓦那'),
 (123,	'America/Hermosillo',	'美洲/埃莫西约'),
 (124,	'America/Indiana/Indianapolis',	'美洲/印第安纳州/印第安纳波利斯'),
 (125,	'America/Indiana/Knox',	'美洲/印第安纳州/克诺克斯'),
 (126,	'America/Indiana/Marengo',	'美洲/印第安纳州/马伦戈'),
 (127,	'America/Indiana/Petersburg',	'美洲/印第安纳州/彼得堡'),
 (128,	'America/Indiana/Tell_City',	'美洲/印第安纳州/泰尔市'),
 (129,	'America/Indiana/Vevay',	'美洲/印第安纳州/维维市'),
 (130,	'America/Indiana/Vincennes',	'美洲/印第安纳州/文森内斯'),
 (131,	'America/Indiana/Winamac',	'美洲/印第安纳州/威纳马克'),
 (132,	'America/Indianapolis',	'美洲/印第安纳波利斯'),
 (133,	'America/Inuvik',	'美洲/伊努维克'),
 (134,	'America/Iqaluit',	'美洲/伊卡利特'),
 (135,	'America/Jamaica',	'美洲/牙买加'),
 (136,	'America/Juneau',	'美洲/朱诺'),
 (137,	'America/Kentucky/Louisville',	'美洲/肯塔基州/路易斯维尔'),
 (138,	'America/Kentucky/Monticello',	'美洲/肯塔基州/蒙蒂塞洛市'),
 (139,	'America/Knox_IN',	'美洲/诺克斯'),
 (140,	'America/Kralendijk',	'美洲/克拉伦迪克'),
 (141,	'America/La_Paz',	'美洲/拉巴斯'),
 (142,	'America/Lima',	'美洲/利马'),
 (143,	'America/Los_Angeles',	'美洲/洛斯-安吉利斯'),
 (144,	'America/Louisville',	'美洲/路易斯维尔'),
 (145,	'America/Lower_Princes',	'美洲/圣马丁岛'),
 (146,	'America/Maceio',	'美洲/马塞约'),
 (147,	'America/Managua',	'美洲/马纳瓜'),
 (148,	'America/Manaus',	'美洲/玛瑙斯'),
 (149,	'America/Marigot',	'美洲/马里戈特'),
 (150,	'America/Martinique',	'美洲/马提尼克'),
 (151,	'America/Matamoros',	'美洲/马塔莫罗斯'),
 (152,	'America/Mazatlan',	'美洲/马萨特兰'),
 (153,	'America/Mendoza',	'美洲/门多萨'),
 (154,	'America/Menominee',	'美洲/梅诺米尼'),
 (155,	'America/Merida',	'美洲/梅里达'),
 (156,	'America/Metlakatla',	'美洲/梅特拉卡特拉'),
 (157,	'America/Mexico_City',	'美洲/墨西哥城'),
 (158,	'America/Miquelon',	'美洲/密克隆岛'),
 (159,	'America/Moncton',	'美洲/蒙克顿'),
 (160,	'America/Monterrey',	'美洲/蒙特雷'),
 (161,	'America/Montevideo',	'美洲/蒙得维的亚'),
 (162,	'America/Montserrat',	'美洲/蒙特塞拉特'),
 (163,	'America/Nassau',	'美洲/拿骚'),
 (164,	'America/New_York',	'美洲/纽约'),
 (165,	'America/Nipigon',	'美洲/尼皮贡'),
 (166,	'America/Nome',	'美洲/诺姆'),
 (167,	'America/Noronha',	'美洲/诺罗尼亚'),
 (168,	'America/North_Dakota/Beulah',	'美洲/北达科他州/博拉市'),
 (169,	'America/North_Dakota/Center',	'美洲/北达科他州/中心'),
 (170,	'America/North_Dakota/New_Salem',	'美洲/北达科他州/新塞勒姆市'),
 (171,	'America/Nuuk',	'美洲/努克'),
 (172,	'America/Ojinaga',	'美洲/奥吉纳加'),
 (173,	'America/Panama',	'美洲/帕纳马'),
 (174,	'America/Pangnirtung',	'美洲/潘尼通'),
 (175,	'America/Paramaribo',	'美洲/帕拉马里博'),
 (176,	'America/Phoenix',	'美洲/凤凰城'),
 (177,	'America/Port-au-Prince',	'美洲/太子港'),
 (178,	'America/Port_of_Spain',	'美洲/西班牙港'),
 (179,	'America/Porto_Acre',	'美洲/波尔图-阿克里'),
 (180,	'America/Porto_Velho',	'美洲/波尔图-韦略'),
 (181,	'America/Puerto_Rico',	'美洲/波多黎各'),
 (182,	'America/Punta_Arenas',	'美洲/蓬塔-阿雷纳斯'),
 (183,	'America/Rainy_River',	'美洲/雷恩河'),
 (184,	'America/Rankin_Inlet',	'美洲/兰京海口'),
 (185,	'America/Recife',	'美洲/里奇夫'),
 (186,	'America/Regina',	'美洲/里贾纳'),
 (187,	'America/Resolute',	'美洲/雷索卢特'),
 (188,	'America/Rio_Branco',	'美洲/里奥布朗库'),
 (189,	'America/Rosario',	'美洲/罗萨里奥'),
 (190,	'America/Santa_Isabel',	'美洲/圣伊莎贝尔'),
 (191,	'America/Santarem',	'美洲/圣塔雷姆'),
 (192,	'America/Santiago',	'美洲/圣地亚哥'),
 (193,	'America/Santo_Domingo',	'美洲/圣多明戈'),
 (194,	'America/Sao_Paulo',	'美洲/圣保罗'),
 (195,	'America/Scoresbysund',	'美洲/斯库兹比松'),
 (196,	'America/Shiprock',	'美洲/西普岩'),
 (197,	'America/Sitka',	'美洲/西特卡'),
 (198,	'America/St_Barthelemy',	'美洲/圣巴泰勒米'),
 (199,	'America/St_Johns',	'美洲/圣约翰'),
 (200,	'America/St_Kitts',	'美洲/圣基茨'),
 (201,	'America/St_Lucia',	'美洲/圣卢西娅'),
 (202,	'America/St_Thomas',	'美洲/圣托马斯'),
 (203,	'America/St_Vincent',	'美洲/圣文森特'),
 (204,	'America/Swift_Current',	'美洲/斯威夫特电流'),
 (205,	'America/Tegucigalpa',	'美洲/特古西加尔巴'),
 (206,	'America/Thule',	'美洲/图勒'),
 (207,	'America/Thunder_Bay',	'美洲/桑德贝'),
 (208,	'America/Tijuana',	'美洲/提华纳'),
 (209,	'America/Toronto',	'美洲/多伦多'),
 (210,	'America/Tortola',	'美洲/托尔托拉岛'),
 (211,	'America/Vancouver',	'美洲/温哥华'),
 (212,	'America/Virgin',	'美洲/维尔金'),
 (213,	'America/Whitehorse',	'美洲/白马'),
 (214,	'America/Winnipeg',	'美洲/温尼伯'),
 (215,	'America/Yakutat',	'美洲/雅库塔特'),
 (216,	'America/Yellowknife',	'美洲/黄刀镇'),
 (217,	'Antarctica/Casey',	'南极洲/凯西'),
 (218,	'Antarctica/Davis',	'南极洲/戴维斯'),
 (219,	'Antarctica/DumontDUrville',	'南极洲/杜蒙杜维尔'),
 (220,	'Antarctica/Macquarie',	'南极洲/麦夸里'),
 (221,	'Antarctica/Mawson',	'南极洲/莫森'),
 (222,	'Antarctica/McMurdo',	'南极洲/麦克默多'),
 (223,	'Antarctica/Palmer',	'南极洲/帕尔默'),
 (224,	'Antarctica/Rothera',	'南极洲/罗瑟拉'),
 (225,	'Antarctica/South_Pole',	'南极洲/南极'),
 (226,	'Antarctica/Syowa',	'南极洲/西奥瓦'),
 (227,	'Antarctica/Troll',	'南极洲/托罗尔'),
 (228,	'Antarctica/Vostok',	'南极洲/沃斯托克'),
 (229,	'Arctic/Longyearbyen',	'北极/朗伊尔登'),
 (230,	'Asia/Aden',	'亚洲/亚丁'),
 (231,	'Asia/Almaty',	'亚洲/阿拉木图'),
 (232,	'Asia/Amman',	'亚洲/安曼'),
 (233,	'Asia/Anadyr',	'亚洲/阿纳迪尔'),
 (234,	'Asia/Aqtau',	'亚洲/阿克陶'),
 (235,	'Asia/Aqtobe',	'亚洲/阿克纠宾'),
 (236,	'Asia/Ashgabat',	'亚洲/阿什哈巴德'),
 (237,	'Asia/Atyrau',	'亚洲/阿特劳'),
 (238,	'Asia/Baghdad',	'亚洲/巴格达'),
 (239,	'Asia/Bahrain',	'亚洲/巴林'),
 (240,	'Asia/Baku',	'亚洲/巴库'),
 (241,	'Asia/Bangkok',	'亚洲/曼谷'),
 (242,	'Asia/Barnaul',	'亚洲/巴尔瑙尔'),
 (243,	'Asia/Beirut',	'亚洲/贝鲁特'),
 (244,	'Asia/Bishkek',	'亚洲/比什凯克'),
 (245,	'Asia/Brunei',	'亚洲/布鲁内'),
 (246,	'Asia/Calcutta',	'亚洲/卡尔库塔'),
 (247,	'Asia/Chita',	'亚洲/赤塔'),
 (248,	'Asia/Choibalsan',	'亚洲/乔巴山'),
 (249,	'Asia/Chongqing',	'亚洲/重庆'),
 (250,	'Asia/Colombo',	'亚洲/科伦坡'),
 (251,	'Asia/Damascus',	'亚洲/大马士革'),
 (252,	'Asia/Dhaka',	'亚洲/达卡'),
 (253,	'Asia/Dili',	'亚洲/帝力'),
 (254,	'Asia/Dubai',	'亚洲/杜拜'),
 (255,	'Asia/Dushanbe',	'亚洲/杜尚别'),
 (256,	'Asia/Famagusta',	'亚洲/法马古斯塔'),
 (257,	'Asia/Gaza',	'亚洲/加沙'),
 (258,	'Asia/Harbin',	'亚洲/哈尔滨'),
 (259,	'Asia/Hebron',	'亚洲/希伯伦'),
 (260,	'Asia/Ho_Chi_Minh',	'亚洲/胡志明市'),
 (261,	'Asia/Hovd',	'亚洲/哈瓦那'),
 (262,	'Asia/Irkutsk',	'亚洲/伊尔库茨克'),
 (263,	'Asia/Istanbul',	'亚洲/伊斯坦布尔'),
 (264,	'Asia/Jakarta',	'亚洲/雅加达'),
 (265,	'Asia/Jayapura',	'亚洲/贾亚普拉'),
 (266,	'Asia/Jerusalem',	'亚洲/耶路撒冷'),
 (267,	'Asia/Kabul',	'亚洲/喀布尔'),
 (268,	'Asia/Kamchatka',	'亚洲/堪察加'),
 (269,	'Asia/Karachi',	'亚洲/卡拉奇'),
 (270,	'Asia/Kashgar',	'亚洲/喀什'),
 (271,	'Asia/Kathmandu',	'亚洲/加德满都'),
 (272,	'Asia/Khandyga',	'亚洲/汉德加'),
 (273,	'Asia/Kolkata',	'亚洲/加尔各答'),
 (274,	'Asia/Krasnoyarsk',	'亚洲/克拉斯诺亚尔斯克'),
 (275,	'Asia/Kuala_Lumpur',	'亚洲/吉隆坡'),
 (276,	'Asia/Kuching',	'亚洲/古晋'),
 (277,	'Asia/Kuwait',	'亚洲/科威特'),
 (278,	'Asia/Macao',	'亚洲/澳门'),
 (279,	'Asia/Magadan',	'亚洲/马加丹'),
 (280,	'Asia/Makassar',	'亚洲/马卡萨'),
 (281,	'Asia/Manila',	'亚洲/马尼拉'),
 (282,	'Asia/Muscat',	'亚洲/马斯喀特'),
 (283,	'Asia/Nicosia',	'亚洲/尼科西亚'),
 (284,	'Asia/Novokuznetsk',	'亚洲/新库兹涅茨克'),
 (285,	'Asia/Novosibirsk',	'亚洲/新西伯利亚'),
 (286,	'Asia/Omsk',	'亚洲/鄂木斯克'),
 (287,	'Asia/Oral',	'亚洲/奥拉尔'),
 (288,	'Asia/Phnom_Penh',	'亚洲/金边'),
 (289,	'Asia/Pontianak',	'亚洲/庞迪亚克'),
 (290,	'Asia/Pyongyang',	'亚洲/平壤'),
 (291,	'Asia/Qatar',	'亚洲/卡塔尔'),
 (292,	'Asia/Qostanay',	'亚洲/古斯塔奈'),
 (293,	'Asia/Qyzylorda',	'亚洲/克孜勒达'),
 (294,	'Asia/Rangoon',	'亚洲/朗贡'),
 (295,	'Asia/Riyadh',	'亚洲/利雅得'),
 (296,	'Asia/Saigon',	'亚洲/西贡'),
 (297,	'Asia/Sakhalin',	'亚洲/萨哈林'),
 (298,	'Asia/Samarkand',	'亚洲/撒马尔罕'),
 (299,	'Asia/Seoul',	'亚洲/首尔'),
 (300,	'Asia/Shanghai',	'亚洲/上海'),
 (301,	'Asia/Singapore',	'亚洲/新加坡'),
 (302,	'Asia/Srednekolymsk',	'亚洲/中科雷姆斯克'),
 (303,	'Asia/Taipei',	'亚洲/台北'),
 (304,	'Asia/Tashkent',	'亚洲/塔什干'),
 (305,	'Asia/Tbilisi',	'亚洲/第比利斯'),
 (306,	'Asia/Tehran',	'亚洲/德黑兰'),
 (307,	'Asia/Tel_Aviv',	'亚洲/特拉维夫'),
 (308,	'Asia/Thimphu',	'亚洲/廷布'),
 (309,	'Asia/Tokyo',	'亚洲/东京'),
 (310,	'Asia/Tomsk',	'亚洲/汤姆斯克'),
 (311,	'Asia/Ujung_Pandang',	'亚洲/乌镇潘当'),
 (312,	'Asia/Ulaanbaatar',	'亚洲/乌兰巴托'),
 (313,	'Asia/Urumqi',	'亚洲/乌鲁木齐'),
 (314,	'Asia/Ust-Nera',	'亚洲/乌斯季纳拉'),
 (315,	'Asia/Vientiane',	'亚洲/万象'),
 (316,	'Asia/Vladivostok',	'亚洲/符拉迪沃斯托克'),
 (317,	'Asia/Yakutsk',	'亚洲/雅库茨克'),
 (318,	'Asia/Yangon',	'亚洲/扬子江'),
 (319,	'Asia/Yekaterinburg',	'亚洲/叶卡捷琳堡'),
 (320,	'Asia/Yerevan',	'亚洲/埃里温'),
 (321,	'Atlantic/Azores',	'大西洋/亚速尔群岛'),
 (322,	'Atlantic/Bermuda',	'大西洋/百慕大'),
 (323,	'Atlantic/Canary',	'大西洋/加那利群岛'),
 (324,	'Atlantic/Cape_Verde',	'大西洋/维德角'),
 (325,	'Atlantic/Faeroe',	'大西洋/法罗群岛'),
 (326,	'Atlantic/Jan_Mayen',	'大西洋/扬马延岛'),
 (327,	'Atlantic/Madeira',	'大西洋/马德拉'),
 (328,	'Atlantic/Reykjavik',	'大西洋/雷克雅未克'),
 (329,	'Atlantic/South_Georgia',	'大西洋/南乔治亚州'),
 (330,	'Atlantic/St_Helena',	'大西洋/圣海伦娜'),
 (331,	'Atlantic/Stanley',	'大西洋/斯坦利'),
 (332,	'Australia/Adelaide',	'澳大利亚/阿德莱德'),
 (333,	'Australia/Brisbane',	'澳大利亚/布里斯班'),
 (334,	'Australia/Broken_Hill',	'澳大利亚/布罗肯希尔'),
 (335,	'Australia/Canberra',	'澳大利亚/堪培拉'),
 (336,	'Australia/Currie',	'澳大利亚/柯里'),
 (337,	'Australia/Darwin',	'澳大利亚/达尔文'),
 (338,	'Australia/Eucla',	'澳大利亚/欧克拉'),
 (339,	'Australia/Hobart',	'澳大利亚/霍巴特'),
 (340,	'Australia/Lindeman',	'澳大利亚/林德曼'),
 (341,	'Australia/Lord_Howe',	'澳大利亚/豪勋爵岛'),
 (342,	'Australia/Melbourne',	'澳大利亚/墨尔本'),
 (343,	'Australia/NSW',	'澳大利亚/新南威尔士州'),
 (344,	'Australia/North',	'澳大利亚/北区'),
 (345,	'Australia/Perth',	'澳大利亚/珀斯'),
 (346,	'Australia/Queensland',	'澳大利亚/昆士兰'),
 (347,	'Australia/South',	'澳大利亚/南部'),
 (348,	'Australia/Sydney',	'澳大利亚/悉尼'),
 (349,	'Australia/Tasmania',	'澳大利亚/塔斯马尼亚'),
 (350,	'Australia/Victoria',	'澳大利亚/维多利亚'),
 (351,	'Australia/West',	'澳大利亚/西部'),
 (352,	'Australia/Yancowinna',	'澳大利亚/扬科维尼亚'),
 (353,	'Brazil/Acre',	'巴西/阿克里'),
 (354,	'Brazil/DeNoronha',	'巴西/德诺罗尼亚'),
 (355,	'Brazil/East',	'巴西/东部'),
 (356,	'Brazil/West',	'巴西/西部'),
 (357,	'Canada/Atlantic',	'加拿大/大西洋'),
 (358,	'Canada/Central',	'加拿大/中部'),
 (359,	'Canada/Eastern',	'加拿大/东部'),
 (360,	'Canada/Mountain',	'加拿大/山区'),
 (361,	'Canada/Newfoundland',	'加拿大/纽芬兰'),
 (362,	'Canada/Pacific',	'加拿大/太平洋地区'),
 (363,	'Canada/Saskatchewan',	'加拿大/萨斯喀彻温省'),
 (364,	'Canada/Yukon',	'加拿大/育空地区'),
 (365,	'Chile/Continental',	'智利/大陆'),
 (366,	'Chile/EasterIsland',	'智利/伊斯特岛'),
 (367,	'Cuba',	'古巴'),
 (368,	'Egypt',	'埃及'),
 (369,	'Eire',	'爱尔兰'),
 (370,	'Europe/Amsterdam',	'欧洲/阿姆斯特丹'),
 (371,	'Europe/Andorra',	'欧洲/安道尔'),
 (372,	'Europe/Astrakhan',	'欧洲/阿斯特拉罕'),
 (373,	'Europe/Athens',	'欧洲/雅典'),
 (374,	'Europe/Belfast',	'欧洲/贝尔法斯特'),
 (375,	'Europe/Belgrade',	'欧洲/贝尔格莱德'),
 (376,	'Europe/Berlin',	'欧洲/柏林'),
 (377,	'Europe/Bratislava',	'欧洲/布拉迪斯拉发'),
 (378,	'Europe/Brussels',	'欧洲/布鲁塞尔'),
 (379,	'Europe/Bucharest',	'欧洲/布加勒斯特'),
 (380,	'Europe/Budapest',	'欧洲/布达佩斯'),
 (381,	'Europe/Busingen',	'欧洲/布林森'),
 (382,	'Europe/Chisinau',	'欧洲/基希讷乌'),
 (383,	'Europe/Copenhagen',	'欧洲/哥本哈根'),
 (384,	'Europe/Dublin',	'欧洲/都柏林'),
 (385,	'Europe/Gibraltar',	'欧洲/直布罗陀'),
 (386,	'Europe/Guernsey',	'欧洲/根西岛'),
 (387,	'Europe/Helsinki',	'欧洲/赫尔辛基'),
 (388,	'Europe/Isle_of_Man',	'欧洲/马恩岛'),
 (389,	'Europe/Istanbul',	'欧洲/伊斯坦布尔'),
 (390,	'Europe/Jersey',	'欧洲/泽西州'),
 (391,	'Europe/Kaliningrad',	'欧洲/加里宁格勒'),
 (392,	'Europe/Kiev',	'欧洲/基辅'),
 (393,	'Europe/Kirov',	'欧洲/基洛夫'),
 (394,	'Europe/Lisbon',	'欧洲/里斯本'),
 (395,	'Europe/Ljubljana',	'欧洲/卢布尔雅那'),
 (396,	'Europe/London',	'欧洲/伦敦'),
 (397,	'Europe/Luxembourg',	'欧洲/卢森堡'),
 (398,	'Europe/Madrid',	'欧洲/马德里'),
 (399,	'Europe/Malta',	'欧洲/马耳他'),
 (400,	'Europe/Mariehamn',	'欧洲/玛丽港'),
 (401,	'Europe/Minsk',	'欧洲/明斯克'),
 (402,	'Europe/Monaco',	'欧洲/摩纳哥'),
 (403,	'Europe/Moscow',	'欧洲/莫斯科'),
 (404,	'Europe/Nicosia',	'欧洲/尼科西亚'),
 (405,	'Europe/Oslo',	'欧洲/奥斯陆'),
 (406,	'Europe/Paris',	'欧洲/巴黎'),
 (407,	'Europe/Podgorica',	'欧洲/波德戈里察'),
 (408,	'Europe/Prague',	'欧洲/布拉格'),
 (409,	'Europe/Riga',	'欧洲/里加'),
 (410,	'Europe/Rome',	'欧洲/罗马'),
 (411,	'Europe/Samara',	'欧洲/萨马拉'),
 (412,	'Europe/San_Marino',	'欧洲/圣马力诺'),
 (413,	'Europe/Sarajevo',	'欧洲/萨拉热窝'),
 (414,	'Europe/Saratov',	'欧洲/萨拉托夫'),
 (415,	'Europe/Simferopol',	'欧洲/辛菲罗波尔'),
 (416,	'Europe/Skopje',	'欧洲/斯科普里'),
 (417,	'Europe/Sofia',	'欧洲/索菲亚'),
 (418,	'Europe/Stockholm',	'欧洲/斯德哥尔摩'),
 (419,	'Europe/Tallinn',	'欧洲/塔林'),
 (420,	'Europe/Tirane',	'欧洲/提兰'),
 (421,	'Europe/Tiraspol',	'欧洲/提拉斯波尔'),
 (422,	'Europe/Ulyanovsk',	'欧洲/乌里扬诺夫斯克'),
 (423,	'Europe/Uzhgorod',	'欧洲/乌日哥罗德'),
 (424,	'Europe/Vaduz',	'欧洲/瓦杜兹'),
 (425,	'Europe/Vatican',	'欧洲/梵蒂冈'),
 (426,	'Europe/Vienna',	'欧洲/维也纳'),
 (427,	'Europe/Vilnius',	'欧洲/维尔纽斯'),
 (428,	'Europe/Volgograd',	'欧洲/伏尔加格勒'),
 (429,	'Europe/Warsaw',	'欧洲/华沙'),
 (430,	'Europe/Zagreb',	'欧洲/萨格勒布'),
 (431,	'Europe/Zaporozhye',	'欧洲/扎波罗热'),
 (432,	'Europe/Zurich',	'欧洲/苏黎世'),
 (433,	'Hongkong',	'香港'),
 (434,	'Iceland',	'冰岛'),
 (435,	'Indian/Antananarivo',	'塔那那利佛'),
 (436,	'Indian/Chagos',	'印度/查戈斯'),
 (437,	'Indian/Christmas',	'印度/圣诞'),
 (438,	'Indian/Cocos',	'印第安人/科科斯'),
 (439,	'Indian/Comoro',	'印度/科摩罗'),
 (440,	'Indian/Kerguelen',	'印度/克尔格伦'),
 (441,	'Indian/Mahe',	'印度/马赫'),
 (442,	'Indian/Maldives',	'印度/马尔代夫'),
 (443,	'Indian/Mauritius',	'印度/毛里求斯'),
 (444,	'Indian/Mayotte',	'印度/马约特'),
 (445,	'Indian/Reunion',	'印度洋/留尼汪'),
 (446,	'Iran',	'伊朗'),
 (447,	'Israel',	'以色列'),
 (448,	'Jamaica',	'牙买加'),
 (449,	'Kwajalein',	'夸贾林岛'),
 (450,	'Libya',	'利比亚'),
 (451,	'Mexico/BajaNorte',	'泰瓦利'),
 (452,	'Mexico/BajaSur',	'南下州'),
 (453,	'Mexico/General',	'墨西哥通用'),
 (454,	'Pacific/Apia',	'太平洋/阿皮亚'),
 (455,	'Pacific/Auckland',	'太平洋/奥克兰'),
 (456,	'Pacific/Bougainville',	'太平洋/布干维尔'),
 (457,	'Pacific/Chatham',	'太平洋/查塔姆'),
 (458,	'Pacific/Chuuk',	'太平洋/楚克'),
 (459,	'Pacific/Easter',	'太平洋/埃斯特'),
 (460,	'Pacific/Efate',	'太平洋/埃法特'),
 (461,	'Pacific/Enderbury',	'太平洋/恩德伯里'),
 (462,	'Pacific/Fakaofo',	'太平洋/法考福环礁'),
 (463,	'Pacific/Fiji',	'太平洋/菲济'),
 (464,	'Pacific/Funafuti',	'太平洋/富纳富提'),
 (465,	'Pacific/Galapagos',	'太平洋/加拉帕戈斯'),
 (466,	'Pacific/Gambier',	'太平洋/甘比耶'),
 (467,	'Pacific/Guadalcanal',	'太平洋/瓜达尔卡纳尔岛'),
 (468,	'Pacific/Guam',	'太平洋/关岛'),
 (469,	'Pacific/Honolulu',	'太平洋/火奴鲁鲁'),
 (470,	'Pacific/Johnston',	'太平洋/约翰斯顿'),
 (471,	'Pacific/Kanton',	'太平洋/坎顿'),
 (472,	'Pacific/Kiritimati',	'太平洋/基里蒂马蒂'),
 (473,	'Pacific/Kosrae',	'太平洋/科斯雷'),
 (474,	'Pacific/Kwajalein',	'太平洋/夸贾林岛'),
 (475,	'Pacific/Majuro',	'太平洋/马朱罗'),
 (476,	'Pacific/Marquesas',	'太平洋/马尔克斯群岛'),
 (477,	'Pacific/Midway',	'太平洋/中途岛'),
 (478,	'Pacific/Nauru',	'太平洋/瑙鲁'),
 (479,	'Pacific/Niue',	'太平洋/纽埃岛'),
 (480,	'Pacific/Norfolk',	'太平洋/诺福克岛'),
 (481,	'Pacific/Noumea',	'太平洋/努美阿'),
 (482,	'Pacific/Pago_Pago',	'太平洋/帕戈_帕戈'),
 (483,	'Pacific/Palau',	'太平洋/帕劳'),
 (484,	'Pacific/Pitcairn',	'太平洋/皮特凯恩'),
 (485,	'Pacific/Pohnpei',	'太平洋/波纳佩岛'),
 (486,	'Pacific/Port_Moresby',	'太平洋/莫里斯比港'),
 (487,	'Pacific/Rarotonga',	'太平洋/拉罗汤加岛'),
 (488,	'Pacific/Saipan',	'太平洋/塞班岛'),
 (489,	'Pacific/Samoa',	'太平洋/萨摩亚'),
 (490,	'Pacific/Tahiti',	'太平洋/大溪地'),
 (491,	'Pacific/Tarawa',	'太平洋/塔拉瓦'),
 (492,	'Pacific/Tongatapu',	'太平洋/汤加塔普岛'),
 (493,	'Pacific/Truk',	'太平洋/特鲁克'),
 (494,	'Pacific/Wake',	'太平洋/威克岛'),
 (495,	'Pacific/Wallis',	'太平洋/瓦利斯'),
 (496,	'Pacific/Yap',	'太平洋/雅浦'),
 (497,	'Poland',	'波兰'),
 (498,	'Portugal',	'葡萄牙'),
 (499,	'Singapore',	'新加坡'),
 (500,	'Turkey',	'土耳其'),
 (501,	'US/Alaska',	'美洲/阿拉斯加'),
 (502,	'US/Aleutian',	'美洲/阿留申州'),
 (503,	'US/Arizona',	'美洲/亚利桑那州'),
 (504,	'US/Central',	'美洲/中部'),
 (505,	'US/East-Indiana',	'美洲/东印第安纳州'),
 (506,	'US/Eastern',	'美洲/东部'),
 (507,	'US/Hawaii',	'美洲/夏威夷'),
 (508,	'US/Indiana-Starke',	'美洲/印第安纳州-斯塔克'),
 (509,	'US/Michigan',	'美洲/密歇根州'),
 (510,	'US/Mountain',	'美洲/山区'),
 (511,	'US/Pacific',	'美洲/太平洋地区');

ALTER TABLE t_project ADD `country` int(10)  NULL COMMENT '国家id';
ALTER TABLE t_project ADD `data_center` int(10) NULL COMMENT '数据分区';
ALTER TABLE t_project ADD `timezone` varchar(255) NOT NULL DEFAULT 'Asia/Shanghai' COMMENT '项目时区';

CREATE TABLE `t_area` (
                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                          `pid` int(11) DEFAULT NULL COMMENT '父id',
                          `city_name` varchar(100) DEFAULT NULL COMMENT '简称',
                          `name` varchar(100) DEFAULT NULL COMMENT '名称',
                          `merger_name` varchar(255) DEFAULT NULL COMMENT '全称',
                          `type` tinyint(4) unsigned DEFAULT '0' COMMENT '层级 1 2 3 省市区县',
                          `city_name_en` varchar(100) DEFAULT NULL COMMENT '拼音',
                          `code` varchar(100) DEFAULT NULL COMMENT '长途区号',
                          `zip_code` varchar(100) DEFAULT NULL COMMENT '邮编',
                          `first` varchar(50) DEFAULT NULL COMMENT '首字母',
                          `lng` varchar(100) DEFAULT NULL COMMENT '经度',
                          `lat` varchar(100) DEFAULT NULL COMMENT '纬度',
                          PRIMARY KEY (`id`) USING BTREE,
                          KEY `name,type` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;