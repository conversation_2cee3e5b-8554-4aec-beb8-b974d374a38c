CREATE TABLE t_retry_fail_task
(
    id            INT AUTO_INCREMENT PRIMARY KEY,
    task_id       VARCHAR(255) NOT NULL COMMENT '任务id',
    task_label    VARCHAR(255) NOT NULL COMMENT '任务标签',
    project_id    VARCHAR(255) NOT NULL COMMENT '项目id',
    state         BOOLEAN      NOT NULL COMMENT '状态, true 成功， false失败',
    retry_count   BIGINT       NOT NULL COMMENT '目前重试次数',
    fail_msg      TEXT COMMENT '失败信息',
    context_json  TEXT COMMENT '上下文json , 这个是根据需要的业务进行保存的',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`   varchar(32) DEFAULT NULL COMMENT '创建者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`   varchar(32) DEFAULT NULL COMMENT '更新者'
) COMMENT='retry失败的任务记录';

CREATE TABLE t_group_demand_month_income
(
    id                 INT AUTO_INCREMENT PRIMARY KEY,
    group_id           VARCHAR(255) NOT NULL COMMENT '分组id',
    project_id         VARCHAR(255) NOT NULL COMMENT '项目id',
    demand_max_power DOUBLE COMMENT '最大需量月度',
    demand_max_power_meter DOUBLE COMMENT '最大需量月度(计量电表)',
    demand_income_power DOUBLE COMMENT '计算需量收益的(降低需量功率)',
    `month_start_time` bigint(20) DEFAULT NULL COMMENT '月度开始时间',
    `month_end_time`   bigint(20) DEFAULT NULL COMMENT '月度结束时间',
    `create_time`      bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`        varchar(32) DEFAULT NULL COMMENT '创建者',
    `update_time`      bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`        varchar(32) DEFAULT NULL COMMENT '更新者',
    UNIQUE INDEX idx_unique_project_group_month (project_id, group_id, month_start_time)
) COMMENT='分组需量月度收益';

ALTER TABLE t_group
    add `demand_control` ENUM('disable', 'enable_show_demand_income', 'enable_hide_demand_income') NOT NULL DEFAULT 'enable_show_demand_income'  COMMENT '"控制需量整合下拉框(不启用disable, 启用,显示需量收益enable_show_demand_income, 启用,隐藏需量收益enable_hide_demand_income )';

ALTER TABLE t_group
    add `demand_control_adjust_model` ENUM('manual', 'auto_calc', 'auto_meter') NOT NULL DEFAULT 'manual'  COMMENT '当前控制需量调整模式 , manual, auto_calc, auto_meter';

ALTER TABLE t_group
    ADD `slip_time` int(10) DEFAULT NULL COMMENT '滑差';

ALTER TABLE t_group
    ADD `demand_period` int(10) DEFAULT NULL COMMENT '周期';

ALTER TABLE t_group
    ADD `demand_control_auto_rate` double DEFAULT 99.6 COMMENT '需量自动调整比例(%) , 自动抬升的时候需要';

ALTER TABLE t_group
    ADD `demand_control_auto_up_limit` double DEFAULT NULL COMMENT '需量自动调整上线(kW)';
ALTER TABLE t_group
    ADD `demand_remind_controller` boolean DEFAULT true COMMENT '需量提醒开关';

ALTER TABLE t_project
    MODIFY COLUMN `whether_backup` ENUM('on_grid_run', 'off_grid_run', 'on_off_grid_run_manual', 'on_off_grid_run_auto') NOT NULL DEFAULT 'on_grid_run' COMMENT '并离网切换 , 并网运行(默认), 离网运行, 并离网运行(手动), 并离网运行(自动)';
update t_project
set whether_backup = 'on_grid_run';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (80, 7, '/monitor/canControllableAction', '可控设备控制操作');

-- 负荷配图
ALTER TABLE t_group
    add `diagram_of_load` ENUM('common', 'charging_piles') NOT NULL DEFAULT 'common' COMMENT '负荷配图(common:通用 charging_piles:充电桩)';

CREATE TABLE `t_point_data_200`
(
    `point_id`      int(11) NOT NULL AUTO_INCREMENT,
    `point_offset`  tinytext COMMENT '偏移量',
    `point_address` int(11) DEFAULT NULL COMMENT '地址',
    `point_name`    tinytext COMMENT '点位名',
    `point_column`  tinytext COMMENT '字段名',
    `point_mul`     int(11) DEFAULT NULL COMMENT '数值倍率',
    `point_type`    varchar(10) NOT NULL COMMENT '数据类型',
    `point_level`   varchar(5)  DEFAULT NULL COMMENT '采样等级',
    `point_len`     tinytext COMMENT '单个采样长度',
    `create_time`   bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`     varchar(32) DEFAULT NULL COMMENT '创建者',
    `update_time`   bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`     varchar(32) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`point_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='点位数据对象';

ALTER TABLE t_group
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_ammeter
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_device
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_camera
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_controllable
    ADD `order_index` int(5) NOT NULL DEFAULT '0' COMMENT '排序字段';

ALTER TABLE t_ammeter
    ADD `frequency_regulation` varchar(20) NOT NULL DEFAULT 'nil' COMMENT '调频';

ALTER TABLE t_project
    ADD `order_rule` int NOT NULL DEFAULT '0' COMMENT '项目排序';
ALTER TABLE t_project
    ADD `order_one` int NOT NULL DEFAULT '9' COMMENT '一类排序';
ALTER TABLE t_project
    ADD `order_one_shield` int NOT NULL DEFAULT '9' COMMENT '一类告警排序';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (156, 15, '/manage/project/orderRule', '排序规则 默认 0，展示用 1');

ALTER TABLE t_group
    add `open_vpp` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启vpp: true 是,false否';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (86, 8, '/event/waveRecord', '录波事件');


-- 更新 t_project_ext 表中的 area 字段，使用 t_price_area 表中对应的 id
UPDATE t_project_ext p
    JOIN t_price_area pa
ON p.area = pa.area
    SET p.area = pa.id;

-- 对于无法匹配到的区域，输出日志
SELECT p.id, p.area
FROM t_project_ext p
         LEFT JOIN t_price_area pa ON p.area = pa.area
WHERE pa.id IS NULL;

-- 更新 t_group 表中的 demand_calc_model, demand_period, 和 slip_time 字段
UPDATE t_group
SET demand_calc_model = CASE
                            WHEN demand_calc_model = 1 THEN 1 -- FIXED 模式的值
                            WHEN demand_calc_model = 2 THEN 1
                            WHEN demand_calc_model = 3 THEN 2 -- SLIDING 模式的值
                            WHEN demand_calc_model = 4 THEN 2
                            ELSE demand_calc_model
    END,
    demand_period     = CASE
                            WHEN demand_calc_model = 1 THEN 15
                            WHEN demand_calc_model = 2 THEN 30
                            WHEN demand_calc_model = 3 THEN 15
                            WHEN demand_calc_model = 4 THEN 30
                            ELSE demand_period
        END,
    slip_time         = CASE
                            WHEN demand_calc_model = 3 THEN 1
                            WHEN demand_calc_model = 4 THEN 1
                            ELSE slip_time
        END
WHERE demand_calc_model IN (1, 2, 3, 4);

-- 更新 t_group 表中的 demand_control 字段
UPDATE t_group
SET demand_control = CASE
                         WHEN demand_controller = 1
                             THEN 'enable_show_demand_income' -- 对应 DemandControlEnum.enable_show_demand_income
                         ELSE 'disable' -- 对应 DemandControlEnum.disable
    END
WHERE demand_controller IS NOT NULL;

CREATE TABLE `t_meter_data_remedies` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                                         `meter_id` varchar(128) DEFAULT NULL COMMENT '电表 id',
                                         `time` bigint(20) NOT NULL COMMENT '数据时间',
                                         `day` bigint(20) NOT NULL COMMENT '日期',
                                         `report_type` varchar(10) NOT NULL DEFAULT '0' COMMENT '日报类型',
                                         `data_type` varchar(10) NOT NULL COMMENT '数据类型',
                                         `data` double NOT NULL DEFAULT '0' COMMENT '数据',
                                         `create_by` tinytext COMMENT '创建者',
                                         `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                         `update_by` tinytext COMMENT '更新者',
                                         `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据状态';

CREATE TABLE `t_report_data_remedies` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                                          `time` bigint(20) NOT NULL COMMENT '数据时间',
                                          `day` bigint(20) NOT NULL COMMENT '日期',
                                          `report_type` varchar(10) NOT NULL DEFAULT '0' COMMENT '日报类型',
                                          `data_type` varchar(10) NOT NULL COMMENT '数据类型',
                                          `data` double NOT NULL DEFAULT '0' COMMENT '数据',
                                          `create_by` tinytext COMMENT '创建者',
                                          `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                          `update_by` tinytext COMMENT '更新者',
                                          `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据状态';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9003, 9, '/report/remedies', '电站补值');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9004, 9, '/report/meter/remedies', '电表补值');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9005, 9, '/report/remedies/query', '补值查询');

ALTER TABLE t_group
    ADD `wave_record` boolean DEFAULT false COMMENT '开启录波';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
    (1105,	10,	'/diagram/getMeterDemand',	'电表需量曲线');

CREATE TABLE `t_event_code_language` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `point_column` varchar(50) DEFAULT NULL COMMENT '对应的点位字段',
                                         `device_type_code` int(11) DEFAULT NULL COMMENT '设备类型码',
                                         `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型名称',
                                         `event_level` varchar(20) DEFAULT NULL COMMENT '事件等级Fault、State、Alarm',
                                         `bit_offset` int(11) DEFAULT NULL COMMENT 'BIT码偏移量',
                                         `bit_value` int(11) DEFAULT NULL COMMENT 'BIT码',
                                         `zh_CN` varchar(255) DEFAULT NULL COMMENT '中文',
                                         `en_US` varchar(255) DEFAULT NULL COMMENT '英文',
                                         `de_DE` varchar(255) DEFAULT NULL COMMENT '德语',
                                         `nl_NL` varchar(255) DEFAULT NULL COMMENT '荷兰',
                                         `sv_SE` varchar(255) DEFAULT NULL COMMENT '瑞典',
                                         `it_IT` varchar(255) DEFAULT NULL COMMENT '意大利',
                                         `pl_PL` varchar(255) DEFAULT NULL COMMENT '波兰',
                                         `bg_BG` varchar(255) DEFAULT NULL COMMENT '保加利亚',
                                         `event_code` varchar(128) DEFAULT NULL,
                                         `create_by` varchar(20) DEFAULT NULL COMMENT '创建者',
                                         `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                         `update_by` varchar(20) DEFAULT NULL COMMENT '修改者',
                                         `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='告警信息表';