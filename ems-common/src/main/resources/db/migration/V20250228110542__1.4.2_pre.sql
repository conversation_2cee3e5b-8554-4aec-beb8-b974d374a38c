ALTER TABLE t_group
    ADD `waster_earnings_controller` tinyint(1) NOT NULL DEFAULT '0'  COMMENT '余热发电收益开关';
ALTER TABLE t_group
    ADD `waster_power_model` int(10) default 2 NOT NULL COMMENT '余热发电模式 1全额上网 2自发自用余量上网 3 自发自用(协议电价)';


CREATE TABLE `t_day_report`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT,
    `project_id`     varchar(50) NOT NULL,
    `equipment_id`   varchar(50) NOT NULL,
    `equipment_type` varchar(50) NOT NULL,
    `time`           bigint(20)  NOT NULL COMMENT '数据时间',
    `in_data` double (30,15) DEFAULT 0 COMMENT '输入电量',
    `out_data` double (30,15) DEFAULT 0 COMMENT '输出电量',
    `out_ems_dcdc_data` double (30,15) DEFAULT 0 COMMENT 'ems内部dcdc输出电量',
    `create_by`      varchar(20)  DEFAULT NULL COMMENT '创建者',
    `update_by`      varchar(20)  DEFAULT NULL COMMENT '修改者',
    `update_time`    bigint(20) DEFAULT NULL COMMENT '修改时间',
    `create_time`    bigint(20) DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`project_id`, `equipment_id`, `time`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='日报表缓存记录表';


ALTER table t_controllable
    add `connection_type` varchar(100) DEFAULT NULL COMMENT '连接方式';
update t_controllable
set connection_type= 'TCPCAN'
where vendor != 'HuaweiCharger';

ALTER table t_controllable
    add `interface_name` varchar(100) DEFAULT null COMMENT '串口名称';
ALTER table t_controllable
    add `bitrate` int(10) DEFAULT 0 COMMENT '波特率';