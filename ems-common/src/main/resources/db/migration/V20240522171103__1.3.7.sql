-- 添加从机号到相关表
ALTER TABLE t_ammeter
    ADD `slave_id` int(10) NOT NULL DEFAULT '1' COMMENT '从机号 默认 1';
ALTER TABLE t_controllable
    ADD `slave_id` int(10) NOT NULL DEFAULT '1' COMMENT '从机号 默认 1';
-- 模版添加数据中心字段筛选
alter table `t_price_template`
    add `price_type` ENUM('PEAK_VALLEYS_PRICE_PERIOD', 'FIXED_PRICE_PERIOD', 'DYNAMIC_PRICE_PERIOD') NOT NULL DEFAULT 'PEAK_VALLEYS_PRICE_PERIOD'  COMMENT '电价类型';


alter table `t_electric_price`
    add `on_line_price` DOUBLE DEFAULT NULL COMMENT '上网电价(馈网电价)';
alter table `t_electric_price`
    add `type` varchar(50) DEFAULT NULL COMMENT '类型区分是 海外的还是国内的';
alter table `t_electric_price`
    add `custom_period_name` varchar(100) DEFAULT NULL COMMENT '自定义的时段的名称';


-- 电价支持欧州的 需求
ALTER TABLE t_project
    ADD `electric_price_type` VARCHAR(100) DEFAULT 'PEAK_VALLEYS_PRICE_PERIOD' COMMENT '电价类型字段',
    ADD `electric_price_area` VARCHAR(100) DEFAULT NULL COMMENT '电价区域',
    ADD `electric_price_span` VARCHAR(100) DEFAULT NULL COMMENT '电价时段';
-- 把项目的电价类型全部设置成 尖峰平谷类型
UPDATE t_project
SET electric_price_type = 'PEAK_VALLEYS_PRICE_PERIOD';
UPDATE t_electric_price
SET type = 'PEAK_VALLEYS_PRICE_PERIOD'
WHERE pid IS NULL;

-- ----------------------------
-- Table structure for t_electric
-- ----------------------------
CREATE TABLE `t_electric_dynamic_period`
(
    `id`                int(32) NOT NULL AUTO_INCREMENT,
    `project_id`        varchar(128) NOT NULL COMMENT '项目id',
    `device_id`         varchar(128) NOT NULL COMMENT '设备Id',
    `period_start_time` bigint(20) NOT NULL COMMENT '数据段开始时间',
    `period_end_time`   bigint(20) NOT NULL COMMENT '数据段结束时间',
    `year`              int(4) NOT NULL COMMENT '年',
    `month`             int(2) NOT NULL COMMENT '月',
    `day`               int(2) NOT NULL COMMENT '日',
    `charge_quantity` double (30,15) DEFAULT NULL COMMENT '充电量',
    `charge_cost` double (30,15) DEFAULT NULL COMMENT '充电成本',
    `discharge_quantity` double (30,15) DEFAULT NULL COMMENT '放电量',
    `discharge_benefit` double (30,15) DEFAULT NULL COMMENT '放电收益',
    `sell_price` double DEFAULT NULL,
    `buy_price` double DEFAULT NULL,
    `total_benefit` double (30,15) DEFAULT NULL COMMENT '总收益(减去成本的)',
    PRIMARY KEY (`project_id`, `device_id`, `period_start_time`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4307 DEFAULT CHARSET=utf8;



CREATE TABLE `t_pv_wind_dynamic_profit`
(
    `id`                int(32) NOT NULL AUTO_INCREMENT,
    `project_id`        varchar(128) NOT NULL COMMENT '项目id',
    `period_start_time` bigint(20) NOT NULL COMMENT '数据段开始时间',
    `period_end_time`   bigint(20) NOT NULL COMMENT '数据段结束时间',
    `year`              int(4) NOT NULL COMMENT '年',
    `month`             int(2) NOT NULL COMMENT '月',
    `day`               int(2) NOT NULL COMMENT '日',
    `pv_or_wind_type`   varchar(128) NOT NULL COMMENT 'pv或者是wind',
    `discharge_quantity` double (30,15) DEFAULT NULL COMMENT '放电量(自用电)',
    `discharge_quantity_online` double (30,15) DEFAULT NULL COMMENT '上网电量',
    `discharge_quantity_dcdc` double (30,15) DEFAULT NULL COMMENT 'dcdc电量',
    `discharge_benefit` double (30,15) DEFAULT NULL COMMENT '放电收益',
    `total_full_internet_access_benefit` double (30,15) DEFAULT NULL COMMENT '全额上网模式收益',
    `agreement_benefit` double (30,15) DEFAULT NULL COMMENT '时段的协议模式收益(需要加上online收益)',
    `total_online_benefit` double (30,15) DEFAULT NULL COMMENT '上网的总收益',
    `total_discharge_quantity` double (30,15) DEFAULT NULL COMMENT 'pv的总电量',
    PRIMARY KEY (`project_id`, `pv_or_wind_type`, `period_start_time`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4307 DEFAULT CHARSET=utf8;


CREATE TABLE `t_income_divide_into`
(
    `id`               int(32) NOT NULL AUTO_INCREMENT,
    `project_id`       varchar(128) NOT NULL COMMENT '项目id',
    `name_of_employer` varchar(128) NOT NULL COMMENT '资方单位名称',
    `name_of_customer` varchar(128) NOT NULL COMMENT '客户单位名称',
    `customer_occupancy` double (30,15) DEFAULT NULL COMMENT '客户投资方占比',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

-- 系统配图 (0:储能柜 1:集装箱)
ALTER TABLE t_group
    add `diagram_of_system` ENUM('0', '1') NOT NULL DEFAULT '0' COMMENT '首页系统配图(0:储能柜 1:集装箱)';


ALTER TABLE t_price_area
    add `price_type` ENUM('PEAK_VALLEYS_PRICE_PERIOD', 'FIXED_PRICE_PERIOD', 'DYNAMIC_PRICE_PERIOD') NOT NULL DEFAULT 'PEAK_VALLEYS_PRICE_PERIOD'  COMMENT '电价类型';

ALTER TABLE t_price_area
    add `country_id` int(10) DEFAULT NULL COMMENT '国家id';

ALTER TABLE t_price_area
    add `electric_price_area` varchar(100) DEFAULT NULL COMMENT '电价区域';

ALTER TABLE t_price_area
    add `electric_price_span` varchar(100) DEFAULT NULL COMMENT '电价时段';


-- 添加一个 标记是否是数据校准的字段
ALTER TABLE t_pv_wind_profit
    ADD COLUMN data_calibration_flag boolean default false;


-- 添加一个 数据校准的操作人
ALTER TABLE t_pv_wind_profit
    ADD COLUMN user_id varchar(100) DEFAULT NULL COMMENT '数据校准的操作人';

-- 添加一个 数据校准的操作人
ALTER TABLE t_electric
    ADD COLUMN user_id varchar(100) DEFAULT NULL COMMENT '数据校准的操作人';

-- 把原本的 主键约束先删除
ALTER TABLE t_pv_wind_profit drop primary key;

-- 添加新的主键约束 多了一个 数据校准字段
ALTER TABLE t_pv_wind_profit
    add PRIMARY KEY (project_id, pv_or_wind_type, time, data_calibration_flag);


ALTER TABLE t_pv_wind_profit
    ADD COLUMN total_agreement_benefit double(30,15) DEFAULT NULL COMMENT '协议模式的收益(需要加上online收益)';

ALTER TABLE t_project
    ADD COLUMN time_sharing_cache boolean DEFAULT false COMMENT '分时缓存开关';

-- 系统配置和其他表的扩展
ALTER TABLE t_event_code
    ADD `permission` SMALLINT(6) DEFAULT NULL COMMENT '事件屏蔽级别', ADD `event_code` VARCHAR(128) COMMENT '事件编码';
ALTER TABLE t_meter_event_code
    ADD `event_code` VARCHAR(128) COMMENT '事件编码';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (83, 8, '/event/showHide', '查看隐藏事件');

alter table `t_ammeter`
    add `dlt645_device_address` varchar(128) DEFAULT NULL COMMENT 'dlt645设备地址';
alter table `t_ammeter`
    add `dlt645_prefix_cmd` varchar(128) DEFAULT NULL COMMENT 'dlt645前置cmd';
ALTER TABLE t_project
    ADD `non_hide_fault` int(10) NOT NULL DEFAULT '0' COMMENT '屏蔽后的故障数';
ALTER TABLE t_project
    ADD `non_hide_alarm` int(10) NOT NULL DEFAULT '0' COMMENT '屏蔽后的告警数';

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (84, 8, '/event/updateBatch', '批量确认');

ALTER TABLE t_controllable
    add `design_power` double COMMENT '设计功率';
ALTER TABLE t_controllable
    add `listen_port` int(10) COMMENT '监听端口';
ALTER TABLE t_controllable
    add `power_step_percentage` double NOT NULL DEFAULT '0.3' COMMENT '功率斜率';

CREATE TABLE `t_investor`
(
    `id`          varchar(100) NOT NULL COMMENT '资方 id',
    `name`        varchar(128) NOT NULL COMMENT '资方名称',
    `address`     varchar(1280) DEFAULT NULL COMMENT '资方地址',
    `create_by`   varchar(32)   DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(32)   DEFAULT NULL COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='投资方';


CREATE TABLE `t_investor_project`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '关系id',
    `investor_id` varchar(128) NOT NULL COMMENT '资方id',
    `project_id`  varchar(128) NOT NULL COMMENT '项目id',
    `create_by`   varchar(32) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(32) DEFAULT NULL COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `t_investor_project_pk` (`investor_id`,`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资方与项目关系';

ALTER TABLE t_project
    ADD `order_shield` int NOT NULL DEFAULT '99' COMMENT '屏蔽项目排序';
ALTER TABLE t_ammeter
    ADD `time_sharing_measurement` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分时计量';
ALTER TABLE t_ammeter
    ADD `max_demand_measurement` tinyint(1) NOT NULL DEFAULT '0' COMMENT '最大需量计量';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (212, 10, '/diagram/getMaxDemandRateFromMeter', '电表需量曲线查询');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9001, 9, '/report/readingMeter/month', '电表抄表月报');
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (9002, 9, '/report/readingMeter/year', '电表抄表年报');

ALTER TABLE t_strategy
    ADD `price_difference` double COMMENT '电价最大差价';
ALTER TABLE t_strategy
    ADD `price_benchmark` double COMMENT '电价定值';
ALTER TABLE t_strategy
    ADD `strategy_type` tinyint(1)  DEFAULT '1' COMMENT '策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式';
ALTER TABLE t_strategy_history
    ADD `price_difference` double COMMENT '电价最大差价';
ALTER TABLE t_strategy_history
    ADD `price_benchmark` double COMMENT '电价定值';
ALTER TABLE t_strategy_history
    ADD `strategy_type` tinyint(1) DEFAULT '1' COMMENT '策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式';

CREATE TABLE `t_strategy_upload_log`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id`  varchar(128) NOT NULL COMMENT '项目id',
    `type`        int(1) NOT NULL COMMENT ' 下发策略的场景',
    `retry`       int(1) DEFAULT NULL COMMENT '重试次数',
    `success`     tinyint(1) NOT NULL COMMENT 'true发送成功 false发送失败',
    `time`        bigint(20) unsigned NOT NULL COMMENT '发生时间',
    `fix_time`    bigint(20) unsigned DEFAULT NULL COMMENT '修复时间',
    `create_by`   tinytext COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`   tinytext COMMENT '更新者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (26, NULL, '/manage/investor', '资方管理'),
       (261, 26, '/manage/investor/add', '资方增加'),
       (262, 26, '/manage/investor/delete', '资方删除'),
       (263, 26, '/manage/investor/edit', '资方修改'),
       (264, 26, '/manage/investor/list', '资方列表查询');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (155, 15, '/manage/project/showInvestor', '资方管理');

CREATE TABLE `t_hide_event_code`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `event_code`  varchar(20)  DEFAULT NULL COMMENT '备注',
    `type`        varchar(10)  DEFAULT NULL,
    `project_id`  varchar(128) DEFAULT NULL,
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`   varchar(32)  DEFAULT NULL COMMENT '创建者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`   varchar(32)  DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='告警信息表';

ALTER TABLE t_electric
    ADD `calibration_type` tinyint(1) DEFAULT '1' NOT NULL COMMENT '校准类型，默认 1原始值  2校准后失效 3校准后新增';
INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (85, 8, '/event/showAlarmEvent', '查看告警事件');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (57, 5, '/notice/incomeDivideInto/add', '收益分成设置');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (126, 12, '/system/reRun', '重跑页面');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (129, 12, '/system/operateHideCode', '新增隐藏code');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (128, 12, '/system/showHideCode', '查看隐藏code');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (27, NULL, '/manage/function', '管理端功能配置');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (271, 27, '/manage/function/reRun', '重跑页面');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (114, 10, '/diagram/getApparentPowerRateByCustom', '自定义曲线电网视在功率');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (69, 5, '/operation/getRealTimePriceDiagram', '项目端电价曲线接口');

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
VALUES (60011, 24, '/cloudLargescreenPage', '展厅大屏显示');

-- INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`)
-- VALUES (150, 14, '/manage/price/getRealTimePriceDiagram', '管理端电价曲线接口');
