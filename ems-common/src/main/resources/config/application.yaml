spring:
  flyway:
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: classpath:db/migration
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 执行迁移时是否自动调用验证   当你的 版本不符合逻辑 比如 你先执行了 DML 而没有 对应的DDL 会抛出异常
    validate-on-migrate: true
    # 如果没有 flyway_schema_history 这个 metadata 表， 在执行 flyway migrate 命令之前, 必须先执行 flyway baseline 命令
    # 设置为 true 后 flyway 将在需要 baseline 的时候, 自动执行一次 baseline
    baseline-on-migrate: true
    # metadata 版本控制信息表 默认 flyway_schema_history
    table: t_flyway_schema_history
    # 指定 baseline 的版本号,默认值为 1, 低于该版本号的 SQL 文件, migrate 时会被忽略
#    baseline-version: 1

  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #延迟1秒启动定时任务
    startup-delay: 1s
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: false
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: ${QUARTZ_THREAD_COUNT:200}
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
    jdbc:
      initialize-schema: ${QUARTZ_INIT_SCHEMA:always}

sms:
  type: ${SMS_TYPE:aliyun}
  accessKeyId: ${SMS_ACCESS_KEY_ID:LTAIrNFx0D4vehqJ}
  accessKeySecret: ${SMS_ACCESS_KEY_SECRET:RJxZzAssRYqO7ZOgBYV0PHmFU8OUgj}
  signName: ${SMS_SIGN_NAME:为恒智能}
  loginId: ${LOGIN_ID:SMS_172745020}
  verifyId: ${VERIFY_ID:SMS_172745021}
  registerId: ${REGISTER_ID:SMS_172745018}
  loginExpire: ${LOGIN_EXPIRE:5}
  codeLength: ${CODE_LENGTH:6}
email:
  loginExpire: ${LOGIN_EMAIL_EXPIRE:10}
graphic:
  verifyExpire: ${GRIPHIC_EXPIRE:3}
user:
  regret: ${USER_REGRET:7}
efficiency:
  low: ${EFFICIENCY_LOW:0.6}
  high: ${EFFICIENCY_HIGH:1}

log:
  client:
    mobile: ${LOG_CLIENT_MOBILE:Android|iPhone|iPad|Mobile}
    pc: ${LOG_CLIENT_PC:Chrome|Safari|Firefox|Edge|360|360EE|360Spider}
