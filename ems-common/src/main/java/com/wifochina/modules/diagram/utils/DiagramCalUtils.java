package com.wifochina.modules.diagram.utils;

import com.wifochina.modules.diagram.VO.ValueVO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created on 2024/7/29 18:50.
 *
 * <AUTHOR>
 */
public class DiagramCalUtils {

    /**
     * 计算 视在功率
     *
     * @param activeList : 有功功率
     * @param reactiveList : 无功功率
     * @return : 功率因数 List<ValueVO>
     */
    public static List<ValueVO> getApparentPowerRate(
            List<ValueVO> activeList, List<ValueVO> reactiveList) {
        Map<Long, Double> activeMap =
                activeList.stream().collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        Map<Long, Double> reactiveMap =
                reactiveList.stream()
                        .collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        ValueVO templateValueVo = new ValueVO();
        List<ValueVO> apparentList = new ArrayList<>();
        for (Long time : activeMap.keySet()) {
            Double activeValue = activeMap.get(time);
            Double reactiveValue = reactiveMap.get(time);
            if (activeValue != null && reactiveValue != null) {
                double apparentPower =
                        Math.sqrt((Math.pow(activeValue, 2) + Math.pow(reactiveValue, 2)));
                ValueVO valueVO;
                try {
                    valueVO = (ValueVO) templateValueVo.clone();
                    valueVO.setTime(time);
                    valueVO.setValue(apparentPower);
                    apparentList.add(valueVO);
                } catch (CloneNotSupportedException e) {
                    e.printStackTrace();
                }
            }
        }
        return apparentList.stream()
                .sorted(Comparator.comparing(ValueVO::getTime))
                .collect(Collectors.toList());
    }

    /**
     * 获取 计算功率因数 or 视在 曲线
     *
     * @param resultMap
     * @param acPowerField
     * @param rePowerField
     * @return
     * @throws CloneNotSupportedException
     */
    public static List<ValueVO> getFactoryOrApparentRate(
            boolean factorFlag,
            Map<String, List<ValueVO>> resultMap,
            String acPowerField,
            String rePowerField)
            throws CloneNotSupportedException {
        List<ValueVO> activeList = resultMap.get(acPowerField);
        List<ValueVO> reactiveList = resultMap.get(rePowerField);
        if (activeList == null || reactiveList == null) {
            return Collections.emptyList();
        }
        Map<Long, Double> activeMap =
                activeList.stream().collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        Map<Long, Double> reactiveMap =
                reactiveList.stream()
                        .collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        ValueVO templateValueVo = new ValueVO();
        List<ValueVO> apparentList = new ArrayList<>();
        for (Long time : activeMap.keySet()) {
            Double activeValue = activeMap.get(time);
            Double reactiveValue = reactiveMap.get(time);
            if (activeValue != null && reactiveValue != null) {
                double apparentPower =
                        Math.sqrt((Math.pow(activeValue, 2) + Math.pow(reactiveValue, 2)));
                ValueVO valueVO = (ValueVO) templateValueVo.clone();
                valueVO.setTime(time);
                if (factorFlag) {
                    if (apparentPower == 0) {
                        valueVO.setValue(0.0);
                    } else {
                        double powerFactor = Math.abs(activeValue / apparentPower);
                        if (!Double.isNaN(powerFactor)) {
                            valueVO.setValue(powerFactor);
                        } else {
                            valueVO.setValue(0.0);
                        }
                    }
                } else {
                    valueVO.setValue(apparentPower);
                }
                apparentList.add(valueVO);
            }
        }
        // 计算功率因数 or 视在 曲线
        return apparentList;
    }
}
