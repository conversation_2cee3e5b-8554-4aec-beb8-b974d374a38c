package com.wifochina.modules.diagram.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ChargeVO
 * 
 * @date 4/18/2022 6:56 PM
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value = "充放电电压")
@Data
public class ChargeVO implements Cloneable {

    @ApiModelProperty(value = "功率KW")
    private Double power;

    @ApiModelProperty(value = "时间点")
    private Long time;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
