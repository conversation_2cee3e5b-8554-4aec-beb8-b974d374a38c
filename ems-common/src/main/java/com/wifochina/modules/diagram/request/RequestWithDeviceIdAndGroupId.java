package com.wifochina.modules.diagram.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2023/10/8 15:56.
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "通用设备分组按日期查询")
public class RequestWithDeviceIdAndGroupId {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;

    @ApiModelProperty(value = "所属设备id，默认为all，all代表全部，不能传空或者空字符串")
    private String deviceId;

    @ApiModelProperty(value = "时间间隔，可为null，或者5的倍数")
    private Integer period;

    @ApiModelProperty(value = "分组id，all代表全部，不能传空或者空字符串")
    private String groupId;

    @ApiModelProperty(value = "分组id，all代表全部，不能传空或者空字符串")
    private String itemId;
}
