package com.wifochina.modules.diagram.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.influx.FluxAdapter;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.VO.ValueVOSenior;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.NewDiagramService;
import com.wifochina.modules.diagram.service.SocDiagramService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.oauth.util.WebUtils;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * Created on 2024/10/17 14:59.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class SocDiagramServiceImpl implements SocDiagramService {

    private final DeviceService deviceService;
    private final InfluxClientService influxClientService;
    @Resource NewDiagramService newDiagramService;

    @Override
    public Map<String, List<ValueVO>> groupSoc(RequestWithGroupId requestWithGroupId) {
        RequestWithDeviceId requestWithDeviceId = new RequestWithDeviceId();
        BeanUtils.copyProperties(requestWithGroupId, requestWithDeviceId);
        requestWithDeviceId.setDeviceId(EmsConstants.ALL);
        requestWithGroupId.setItemId(EmsConstants.ALL);
        // 如果是all 则 .. 需要先查询 每个设备的额定容量 然后
        List<String> groupDeviceList = deviceService.getGroupDeviceList(requestWithGroupId);
        // 查询每个设备的 额定容量
        Map<String, ValueVO> emsDesignStorageMap =
                getDeviceStorageMap(
                        new RangeRequest()
                                .setStartDate(requestWithGroupId.getStartDate())
                                .setEndDate(requestWithGroupId.getEndDate()),
                        groupDeviceList);
        Set<String> notEmsSocDeviceIds = new HashSet<>();
        // 再查询 设备的 ems_soc 和  上面查询的额定容量 相乘 , 返回的是合计所有设备的 总和
        Map<Long, ValueVO> resultValueVoMap =
                emsScoMultiStorage(
                        groupDeviceList,
                        emsDesignStorageMap,
                        requestWithDeviceId,
                        notEmsSocDeviceIds);
        // 排除没有查询到设备的soc 的 为了限制  总额定容量
//        emsDesignStorageMap.keySet().removeAll(notEmsSocDeviceIds);
        //        double totalStorage =
        //
        // emsDesignStorageMap.values().stream().mapToDouble(ValueVO::getValue).sum();
        List<ValueVO> collect =
                resultValueVoMap.values().stream()
                        //                        .peek(valueVO ->
                        // valueVO.setValue(valueVO.getValue() / totalStorage))
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList());
        Map<String, Map<String, List<ValueVO>>> result = new HashMap<>();
        Map<String, List<ValueVO>> fieldMap = new HashMap<>();
        fieldMap.put(EmsFieldEnum.EMS_SOC.field(), collect);

        return fieldMap;
    }

    /**
     * 查询 每个设备 的 额定容量
     *
     * @param rangeRequest : rangeRequest
     * @param groupDeviceList :groupDeviceList
     * @return :Map<String, ValueVO> 设备id 为key
     */
    private Map<String, ValueVO> getDeviceStorageMap(
            RangeRequest rangeRequest, List<String> groupDeviceList) {
        FluxAdapter.InfluxResultStage emsDesignStorageEnergy =
                FluxAdapter.query(
                                FluxAdapter.builder()
                                        .projectId(WebUtils.projectId.get())
                                        .timeZoneSet(false)
                                        .from(influxClientService.getBucketForever())
                                        .range(
                                                rangeRequest.getStartDate(),
                                                rangeRequest.getEndDate())
                                        .measurement(
                                                influxClientService.getEmsT0Table(
                                                        WebUtils.projectId.get()))
                                        .fields(List.of("ems_design_storage_energy"))
                                        .equipmentIds(groupDeviceList)
                                        .toFlux()
                                        .first()
                                        .toString())
                        .handleResult();
        // 获取到 每个设备对应的 额定容量
        return emsDesignStorageEnergy.onlyOneMap(groupDeviceList);
    }

    private @NotNull Map<Long, ValueVO> emsScoMultiStorage(
            List<String> groupDeviceList,
            Map<String, ValueVO> emsDesignStorageMap,
            RequestWithDeviceId requestWithDeviceId,
            Set<String> notEmsSocDeviceIds) {
        Map<Long, ValueVO> resultValueVoMap = new HashMap<>();
        Map<Long, ValueVOSenior> resultValueVoMap2 = new HashMap<>();
        groupDeviceList.forEach(
                deviceId -> {
                    // 这个设备的 额定容量需要有 或者说 需要查询到 查询不到属于异常情况
                    if (emsDesignStorageMap.get(deviceId) != null
                            && emsDesignStorageMap.get(deviceId).getValue() != null) {
                        requestWithDeviceId.setDeviceId(deviceId);
                        List<ValueVO> emsSocs =
                                newDiagramService
                                        .getDeviceRate(
                                                requestWithDeviceId,
                                                influxClientService.getBucketForever(),
                                                List.of(EmsFieldEnum.EMS_SOC.field()))
                                        .get(deviceId)
                                        .get(EmsFieldEnum.EMS_SOC.field());
                        // 如果单个设备的 emsSoc 曲线查询不到 则 合计的时候不计算
                        if (CollectionUtil.isNotEmpty(emsSocs)) {
                            emsSocs.forEach(
                                    valueVO -> {
                                        ValueVOSenior sumValueVo =
                                                resultValueVoMap2.computeIfAbsent(
                                                        valueVO.getTime(),
                                                        k ->
                                                                new ValueVOSenior()
                                                                        .setTime(valueVO.getTime())
                                                                        .setValue(0.0)
                                                                        .setTotalStorage(0.0));
                                        // 把对应设备的额定容量 * 可放电
                                        //
                                        sumValueVo.setValue(
                                                sumValueVo.getValue()
                                                        + valueVO.getValue()
                                                                * emsDesignStorageMap
                                                                        .get(deviceId)
                                                                        .getValue());
                                        sumValueVo.setTotalStorage(
                                                sumValueVo.getTotalStorage()
                                                        + emsDesignStorageMap
                                                                .get(deviceId)
                                                                .getValue());
                                    });
                        } else {
                            notEmsSocDeviceIds.add(deviceId);
                        }
                    } else {
                        // 找不到对应的 设备的额定容量
                        throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
                    }
                });

        resultValueVoMap2.forEach(
                (time, senior) -> {
                    ValueVO valueVO = new ValueVO();
                    valueVO.setTime(time);
                    if (senior.getTotalStorage() != 0.0) {
                        valueVO.setValue(senior.getValue() / senior.getTotalStorage());
                    } else {
                        valueVO.setValue(0.0);
                    }
                    resultValueVoMap.put(time, valueVO);
                });

        return resultValueVoMap;
    }
}
