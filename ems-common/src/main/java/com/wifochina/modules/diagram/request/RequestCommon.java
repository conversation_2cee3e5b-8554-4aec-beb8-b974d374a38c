package com.wifochina.modules.diagram.request;

import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.oauth.util.WebUtils;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2024/7/24 11:45.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestCommon {

    private String projectId;

    public String getProjectId() {
        if (StringUtil.isEmpty(projectId)) {
            return WebUtils.projectId.get();
        } else {
            return projectId;
        }
    }
}
