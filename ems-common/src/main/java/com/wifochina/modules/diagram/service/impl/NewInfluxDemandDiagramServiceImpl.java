package com.wifochina.modules.diagram.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.influx.FluxAdapter;
import com.wifochina.common.influx.FluxResultHandlerContext;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.demand.entity.GroupDemandInfoEntity;
import com.wifochina.modules.demand.mapper.GroupDemandMonthIncomeMapper;
import com.wifochina.modules.diagram.VO.AggregationEquipmentMap;
import com.wifochina.modules.diagram.VO.MaxDemandVo;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.CapacityRequest;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.BaseDemandDiagramService;
import com.wifochina.modules.diagram.utils.DiagramCalUtils;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.Max;

/**
 * Created on 2024/7/31 13:53.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class NewInfluxDemandDiagramServiceImpl extends BaseDemandDiagramService {

    private final InfluxClientService influxClient;
    private final ProjectService projectService;
    @Resource private GroupDemandMonthIncomeMapper groupDemandMonthIncomeMapper;
    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 查询 原需量曲线接口 是根据 ems + grid 关口电表的 作为 原需量 计算得来的<br>
     *
     * @param requestWithGroupId 请求的group id以及日期
     * @return : List<ValueVo></ValueVo>
     */
    @Override
    public List<ValueVO> getOriginalDemandByCalculate(RequestWithGroupId requestWithGroupId) {
        String projectId = requestWithGroupId.getProjectId();
        // 查询 并网电表的id 需要满足对应分组
        List<String> meterIds =
                EmsUtil.getGridMeterIdsByGroupId(
                        requestWithGroupId.getGroupId(), ammeterService, groupAmmeterService);
        if (CollectionUtils.isEmpty(meterIds)) {
            return Collections.emptyList();
        }

        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithGroupId, holder);
        if (requestWithGroupId.getPeriod() != null) {
            holder.setPeriod(requestWithGroupId.getPeriod());
        }
        // 查询电表 的 功率 转换成 time 为key
        Map<Long, Double> meterMap =
                EquipmentTimeSeriesUtils.rateQueryEngine
                        .getRateAggregationList(
                                influxClient.getBucketMean(),
                                influxClient.getMeterTable(projectId),
                                holder,
                                MeterFieldEnum.AC_ACTIVE_POWER.field(),
                                () -> meterIds)
                        .getList()
                        // 电表的 功率要转换成 kw 单位 要/1000
                        .stream()
                        .collect(Collectors.toMap(ValueVO::getTime, v -> v.getValue() / 1000));
        if (meterMap.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> deviceIds =
                EmsUtil.getDeviceIdsByGroupId(requestWithGroupId.getGroupId(), groupDeviceService);
        if (CollectionUtils.isEmpty(deviceIds)) {
            // 没有 设备ems相关的直接拿 电表的返回
            return meterMap.entrySet().stream()
                    .map(entry -> new ValueVO(entry.getKey(), entry.getValue()))
                    .sorted(Comparator.comparing(ValueVO::getTime))
                    .collect(Collectors.toList());
        }
        // 查询ems的 功率
        Map<Long, Double> emsMap =
                EquipmentTimeSeriesUtils.rateQueryEngine
                        .getRateAggregationList(
                                influxClient.getBucketMean(),
                                influxClient.getEmsTable(projectId),
                                holder,
                                EmsFieldEnum.EMS_AC_ACTIVE_POWER.field(),
                                () -> deviceIds)
                        .getList()
                        .stream()
                        .collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));

        // 用 ems 的 视的视在对应的时间 - 并网电表的视在 就是原始的 需量
        return meterMap.keySet().stream()
                .filter(time -> emsMap.containsKey(time) && meterMap.containsKey(time))
                .map(
                        time ->
                                new ValueVO(
                                        time,
                                        Double.parseDouble(
                                                String.format(
                                                        "%.2f",
                                                        emsMap.get(time) - meterMap.get(time)))))
                .sorted(Comparator.comparing(ValueVO::getTime))
                .collect(Collectors.toList());
    }

    /**
     * 和getOriginalDemandByCalculate 接口基本相似 只是 使用的视在 <br>
     * * 1.4.0 change acActivePower to apparentPower 就是原需量功率 以前是按照 有功计算的, 现在是 视在功率计算
     *
     * @param requestWithGroupId :requestWithGroupId
     * @return :List<ValueVO>
     */
    @Override
    public List<ValueVO> getOriginalDemandByCalculateWithApparent(
            RequestWithGroupId requestWithGroupId) {

        String projectId = requestWithGroupId.getProjectId();
        // 查询 并网电表的id 需要满足对应分组
        List<String> meterIds =
                EmsUtil.getGridMeterIdsByGroupId(
                        requestWithGroupId.getGroupId(), ammeterService, groupAmmeterService);
        if (CollectionUtils.isEmpty(meterIds)) {
            return Collections.emptyList();
        }

        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithGroupId, holder);
        if (requestWithGroupId.getPeriod() != null) {
            holder.setPeriod(requestWithGroupId.getPeriod());
        }
        WebUtils.projectId.set(projectId);
        AggregationEquipmentMap meterAggregationMap =
                EquipmentTimeSeriesUtils.rateQueryEngine.getRateAggregationMap(
                        influxClient.getBucketMean(),
                        influxClient.getMeterTable(projectId),
                        holder,
                        List.of(
                                MeterFieldEnum.AC_ACTIVE_POWER.field(),
                                MeterFieldEnum.AC_REACTIVE_POWER.field()),
                        () -> meterIds);
        if (meterAggregationMap.getMap().isEmpty()) {
            return new ArrayList<>();
        }
        // 计算有功
        List<ValueVO> meterAcActiveList =
                meterAggregationMap.getMap().get(MeterFieldEnum.AC_ACTIVE_POWER.field()).stream()
                        .map(v -> new ValueVO(v.getTime(), v.getValue() / 1000))
                        .collect(Collectors.toList());
        // 计算无功
        List<ValueVO> meterAcReactiveList =
                meterAggregationMap.getMap().get(MeterFieldEnum.AC_REACTIVE_POWER.field()).stream()
                        // 关口电表需要除以 1000
                        .map(v -> new ValueVO(v.getTime(), v.getValue() / 1000))
                        .collect(Collectors.toList());
        // 计算 电表的 视在功率
        List<ValueVO> meterApparentPowerRate =
                DiagramCalUtils.getApparentPowerRate(meterAcActiveList, meterAcReactiveList);
        List<String> deviceIds =
                EmsUtil.getDeviceIdsByGroupId(requestWithGroupId.getGroupId(), groupDeviceService);
        if (CollectionUtils.isEmpty(deviceIds)) {
            // 没有 设备ems相关的直接拿 电表的返回
            return meterApparentPowerRate.stream()
                    .sorted(Comparator.comparing(ValueVO::getTime))
                    .collect(Collectors.toList());
        }
        // 查询ems的 功率
        AggregationEquipmentMap emsAggregationMap =
                EquipmentTimeSeriesUtils.rateQueryEngine.getRateAggregationMap(
                        influxClient.getBucketMean(),
                        influxClient.getEmsTable(projectId),
                        holder,
                        List.of(
                                EmsFieldEnum.EMS_AC_ACTIVE_POWER.field(),
                                EmsFieldEnum.EMS_AC_REACTIVE_POWER.field()),
                        () -> deviceIds);

        Map<Long, Double> emsApparentPowerRateMap =
                DiagramCalUtils.getApparentPowerRate(
                                emsAggregationMap
                                        .getMap()
                                        .get(EmsFieldEnum.EMS_AC_ACTIVE_POWER.field()),
                                emsAggregationMap
                                        .getMap()
                                        .get(EmsFieldEnum.EMS_AC_REACTIVE_POWER.field()))
                        .stream()
                        .collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));

        Map<Long, Double> meterApparentPowerRateMap =
                meterApparentPowerRate.stream()
                        .collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        List<ValueVO> emsActivePowerList =
                emsAggregationMap.getMap().get(EmsFieldEnum.EMS_AC_ACTIVE_POWER.field());
        return calculateOriginGridPowerList(
                        meterApparentPowerRateMap,
                        emsApparentPowerRateMap,
                        meterAcActiveList,
                        emsActivePowerList)
                .stream()
                .sorted(Comparator.comparing(ValueVO::getTime))
                .collect(Collectors.toList());
    }

    // 原功率 = 电网视在功率(取电为正，馈网为负)+储能视在功率(充电为负，放电为正)
    public static List<ValueVO> calculateOriginGridPowerList(
            Map<Long, Double> meterApparentPowerValueMap,
            Map<Long, Double> emsApparentPowerValueMap,
            List<ValueVO> meterAcActiveList,
            List<ValueVO> emsActivePowerList) {
        List<ValueVO> finalPowerList = new ArrayList<>();
        // 将 List<ValueVO> 转换为 Map<Long, ValueVO> 以便快速查找
        Map<Long, ValueVO> emsActivePowerMap = new HashMap<>();
        Map<Long, ValueVO> meterAcActiveMap = new HashMap<>();
        for (ValueVO vo : emsActivePowerList) {
            emsActivePowerMap.put(vo.getTime(), vo);
        }
        for (ValueVO vo : meterAcActiveList) {
            meterAcActiveMap.put(vo.getTime(), vo);
        }
        // 假设两个map中的时间点是相同的，遍历其中一个map即可
        for (Map.Entry<Long, Double> entry : meterApparentPowerValueMap.entrySet()) {
            Long time = entry.getKey();
            Double meterApparentPower = entry.getValue();
            Double emsApparentPower = emsApparentPowerValueMap.get(time);

            if (meterApparentPower != null && emsApparentPower != null) {
                ValueVO meterActiveVO = meterAcActiveMap.get(time);
                ValueVO emsActiveVO = emsActivePowerMap.get(time);
                if (meterActiveVO != null && emsActiveVO != null) {
                    Double meterActiveValue = meterActiveVO.getValue();
                    Double emsActiveValue = emsActiveVO.getValue();
                    // 根据 activeValue 判断是否取反 meterApparentPower 和 emsApparentPower
                    if (meterActiveValue > 0) {
                        meterApparentPower = -meterApparentPower;
                    }
                    if (emsActiveValue < 0) {
                        emsApparentPower = -emsApparentPower;
                    }
                    Double finalApparentPower = meterApparentPower + emsApparentPower;
                    ValueVO finalValueVO = new ValueVO();
                    finalValueVO.setTime(time);
                    finalValueVO.setValue(finalApparentPower);
                    finalPowerList.add(finalValueVO);
                }
            }
        }
        return finalPowerList;
    }

    /**
     * 需量曲线查询 接口 它是根据 分组去查询的 是查询的需量job计算后 写入的库和表的数据
     *
     * @param requestWithGroupId 请求的及日期
     * @return Map<String, List<ValueVO>> result
     */
    @Override
    public Map<String, Map<String, List<ValueVO>>> getDemandRate(
            RequestWithGroupId requestWithGroupId) {
        String projectId = WebUtils.projectId.get();
        GroupEntity systemGroup = groupService.systemGroupEntity(projectId);
        Pair<Integer, List<String>> modelAndGroupIds =
                getDemandGroupIds(requestWithGroupId, projectId);
        List<String> groupIds = modelAndGroupIds.getSecond();
        if (CollectionUtils.isEmpty(groupIds)) {
            return null;
        }
        // just one groupId
        String groupId = groupIds.get(0);
        // Map<String, List<ValueVO>> influxMap =
        FluxResultHandlerContext resultContext =
                FluxAdapter.query(
                                FluxAdapter.builder()
                                        .projectId(projectId)
                                        // cloud is `forever` bucket
                                        // local is `control` bucket
                                        .from(influxClient.getBucketDemand())
                                        .range(
                                                requestWithGroupId.getStartDate(),
                                                requestWithGroupId.getEndDate())
                                        .measurement(
                                                influxClient.getDemandTable(projectId, systemGroup))
                                        .groupId(groupId)
                                        .toFlux()
                                        .pivot(
                                                List.of(EmsConstants.INFLUX_TIME),
                                                List.of(EmsConstants.INFLUX_FIELD),
                                                EmsConstants.INFLUX_VALUE)
                                        // 1.4.1 发现的bug 如果不加sort 会有可能时间错乱导致, app前端时间没有排序处理发现的这个问题
                                        .sort(List.of(EmsConstants.INFLUX_TIME), false)
                                        // 为什么加这个限制呢 因为现在 会在需量表里 插入 单独的只有 controlPower的数据
                                        .filter(
                                                Restrictions.and(
                                                        Restrictions.tag("meter_power").exists(),
                                                        Restrictions.tag("original_Demand")
                                                                .exists()))
                                        .toString())
                        .handleResult()
                        .getResultContext();
        Map<String, List<ValueVO>> resultMap = demandMultiFieldHandler(resultContext, false);
        return Map.of(groupId, transformMapKey(resultMap));
    }

    private static @NotNull Map<String, List<ValueVO>> demandMultiFieldHandler(
            FluxResultHandlerContext context, boolean forMax) {
        Map<String, List<ValueVO>> resultMap = new HashMap<>(2);
        int i = 1;
        for (FluxTable fluxTable : context.getFluxTables()) {
            for (FluxRecord record : fluxTable.getRecords()) {
                if (context.isDropFirst()) {
                    if (i == 1) {
                        i++;
                        continue;
                    }
                }
                Instant time = (Instant) record.getValueByKey(context.getTimeKey());
                if (time != null) {
                    Double originalDemand = (Double) record.getValueByKey("original_Demand");
                    ValueVO originalDemandValueVo = new ValueVO(time.getEpochSecond());
                    originalDemandValueVo.setValue(originalDemand);
                    Double meterPower = (Double) record.getValueByKey("meter_power");
                    if (meterPower == null) {
                        log.info("null");
                    }
                    ValueVO meterPowerValueVo = new ValueVO(time.getEpochSecond());
                    meterPowerValueVo.setValue(meterPower);
                    Double controlPower = (Double) record.getValueByKey("control_power");
                    if (!forMax) {}
                    ValueVO controlPowerValueVo = new ValueVO(time.getEpochSecond());
                    controlPowerValueVo.setValue(controlPower);
                    resultMap
                            .computeIfAbsent("original_Demand", k -> new ArrayList<>())
                            .add(originalDemandValueVo);

                    resultMap
                            .computeIfAbsent("meter_power", k -> new ArrayList<>())
                            .add(meterPowerValueVo);
                    resultMap
                            .computeIfAbsent("control_power", k -> new ArrayList<>())
                            .add(controlPowerValueVo);
                }
            }
        }
        return resultMap;
    }

    private Map<String, List<ValueVO>> transformMapKey(Map<String, List<ValueVO>> influxMap) {
        // 处理一下 null的情况
        List<ValueVO> realControl =
                influxMap.get("control_power") == null ? List.of() : influxMap.get("control_power");
        List<ValueVO> realDemand =
                influxMap.get("meter_power") == null ? List.of() : influxMap.get("meter_power");
        List<ValueVO> originalDemand =
                influxMap.get("original_Demand") == null
                        ? List.of()
                        : influxMap.get("original_Demand");
        return Map.of(
                "realControl",
                realControl,
                "realDemand",
                realDemand,
                "originalDemand",
                originalDemand);
    }

    @Override
    public List<ValueVO> getControlPowerRate(RequestWithGroupId rateRequest) {
        String projectId = WebUtils.projectId.get();
        GroupEntity systemGroup = groupService.systemGroupEntity(projectId);
        // 相差的天数
        long periodTime = (rateRequest.getEndDate() - rateRequest.getStartDate()) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = (periodTime / 2 + 1);
        // 1.3.9 因为下面改成了 withCreateEmpty true 导致限制一下 结束时间 否则前端会查询到 今天的最后时间
        long epochSecond = Instant.now().getEpochSecond();
        if (rateRequest.getEndDate() >= epochSecond) {
            rateRequest.setEndDate(Instant.now().getEpochSecond());
        }
        return FluxAdapter.query(
                        FluxAdapter.builder()
                                .projectId(projectId)
                                .from(influxClient.getBucketDemand())
                                .range(rateRequest.getStartDate(), rateRequest.getEndDate())
                                .measurement(influxClient.getDemandTable(projectId, systemGroup))
                                .groupId(rateRequest.getGroupId())
                                .fields(List.of(MeterFieldEnum.CONTROL_POWER.field()))
                                .toFlux()
                                .aggregateWindow(
                                        period, ChronoUnit.MINUTES, EmsConstants.INFLUX_LAST_FUNC)
                                // 这个没有 会给我最后一条数据 不是整点的一个数据
                                .withTimeSrc("_start")
                                .withCreateEmpty(true)
                                // TODO for 1.4.4 timeSharing 分段要展示 这里会导致填充了 其他段的 控需的
                                //                                .fill()
                                //                                .withUsePrevious(true)
                                .toString())
                .decimal(2)
                .handleResult()
                .toListFilterNullValue();
    }

    public static void main(String[] args) {

        Flux flux =
                Flux.from("123")
                        .range(0L, 0L)
                        .filter()
                        .withFunction(
                                "fn: (r)",
                                " date.hour(t: r._time) >= 12 and date.hour(t: r._time) < 15");
        String string = flux.toString();

        System.out.println(string);
        //                .collectImports(Set.of("date", "timezone"));
    }

    @Override
    public Map<String, Map<String, List<ValueVO>>> getMaxDemand(
            RequestWithGroupId requestWithGroupId) {
        String projectId = requestWithGroupId.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        Pair<Integer, List<String>> modelAndGroupIds =
                getDemandGroupIds(requestWithGroupId, projectId);
        List<String> groupIds = modelAndGroupIds.getSecond();
        if (CollectionUtils.isEmpty(groupIds)) {
            // 这里如果抛错 前端那边可能会因为某个分组没开需 导致页面抛错
            return new HashMap<>();
        }
        GroupEntity systemGroup = groupService.systemGroupEntity(projectId);
        // just one groupId
        String groupId = groupIds.get(0);
        FluxResultHandlerContext resultContext =
                FluxAdapter.query(
                                FluxAdapter.builder()
                                        .project(projectEntity)
                                        .timeZoneSet(true)
                                        .from(influxClient.getBucketDemand())
                                        .range(
                                                requestWithGroupId.getStartDate(),
                                                requestWithGroupId.getEndDate())
                                        .measurement(
                                                influxClient.getDemandTable(projectId, systemGroup))
                                        .fields(List.of(MeterFieldEnum.METER_POWER.field()))
                                        .groupId(groupId)
                                        .toFlux()
                                        .aggregateWindow(
                                                1L, ChronoUnit.MONTHS, EmsConstants.INFLUX_MAX_FUNC)
                                        .withCreateEmpty(false)
                                        .timeShift(-5L, ChronoUnit.DAYS)
                                        .pivot(
                                                List.of(EmsConstants.INFLUX_TIME),
                                                List.of(EmsConstants.INFLUX_FIELD),
                                                EmsConstants.INFLUX_VALUE)
                                        .toString())
                        .handleResult()
                        .getResultContext();
        return Map.of(groupId, transformMapKey(demandMultiFieldHandler(resultContext, true)));
    }

    /**
     * |> filter(fn: (r) => (date.hour(t: r._time) == 12 and date.minute(t: r._time) >= 30) and
     * (date.hour(t: r._time) >= 12 and date.hour(t: r._time) <= 12) and (date.hour(t: r._time) ==
     * 12 and date.minute(t: r._time) < 50) )
     *
     * @param timeSharingDemand
     * @return
     */
    private String periodStrGet(TimeSharingDemandEntity timeSharingDemand) {
        LocalTime startTime = timeSharingDemand.getStartTime();
        LocalTime endTime = timeSharingDemand.getEndTime();
        int startTimeHour = startTime.getHour();
        int startTimeMinute = startTime.getMinute();
        int endTimeHour = endTime.getHour();
        int endTimeMinute = endTime.getMinute();
        return "(date.hour(t: r._time) >= "
                + startTimeHour
                + " and date.hour(t: r._time) <= "
                + endTimeHour
                + ") and ("
                + " (date.hour(t: r._time) == "
                + startTimeHour
                + " and date.minute(t: r._time) >= "
                + startTimeMinute
                + " ) \n"
                + " or  (date.hour(t: r._time) == "
                + endTimeHour
                + " and date.minute(t: r._time) < "
                + endTimeMinute
                + ")"
                + ")";
    }

    @Override
    public Map<Long, List<MaxDemandVo>> getTimeSharingMaxDemandNew(
            RequestWithGroupId requestWithGroupId) throws InterruptedException {
        String projectId = requestWithGroupId.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        Pair<Integer, List<String>> modelAndGroupIds =
                getTimeSharingDemandGroupIds(requestWithGroupId, projectId);
        List<String> groupIds = modelAndGroupIds.getSecond();
        if (CollectionUtils.isEmpty(groupIds)) {
            // 这里如果抛错 前端那边可能会因为某个分组没开需 导致页面抛错
            return new HashMap<>();
        }
        GroupEntity systemGroup = groupService.systemGroupEntity(projectId);
        // just one groupId
        String groupId = groupIds.get(0);
        List<TimeSharingDemandEntity> timeSharingDemandEntities =
                timeSharingDemandService.listPivotBy(projectId, groupId);
        List<MaxDemandVo> timeSharingMaxDemandList = new ArrayList<>();
        CountDownLatch countDownLatch = new CountDownLatch(timeSharingDemandEntities.size());
        timeSharingDemandEntities.forEach(
                timeSharingDemand -> {
                    threadPoolTaskExecutor.submit(
                            () -> {
                                String periodFunStr = periodStrGet(timeSharingDemand);
                                ValueVO valueVO =
                                        FluxAdapter.query(
                                                        FluxAdapter.importBuilder().importDate()
                                                                + FluxAdapter.builder()
                                                                        .project(projectEntity)
                                                                        .timeZoneSet(true)
                                                                        .from(
                                                                                influxClient
                                                                                        .getBucketDemand())
                                                                        .range(
                                                                                requestWithGroupId
                                                                                        .getStartDate(),
                                                                                requestWithGroupId
                                                                                        .getEndDate())
                                                                        .measurement(
                                                                                influxClient
                                                                                        .getDemandTable(
                                                                                                projectId,
                                                                                                systemGroup))
                                                                        .fields(
                                                                                List.of(
                                                                                        MeterFieldEnum
                                                                                                .METER_POWER
                                                                                                .field()))
                                                                        .groupId(groupId)
                                                                        .toFlux()
                                                                        .filter()
                                                                        .withFunction(
                                                                                "fn: (r)",
                                                                                periodFunStr)
                                                                        .aggregateWindow(
                                                                                1L,
                                                                                ChronoUnit.MINUTES,
                                                                                EmsConstants
                                                                                        .INFLUX_MAX_FUNC)
                                                                        .withCreateEmpty(false)
                                                                        .max())
                                                .handleResult()
                                                .onlyOneValue();
                                timeSharingMaxDemandList.add(
                                        new MaxDemandVo()
                                                .setTimeSharingMaxDemandVo(
                                                        new MaxDemandVo.TimeSharingMaxDemandVo()
                                                                .setValueVO(valueVO)
                                                                .setTimeSharingDemandId(
                                                                        timeSharingDemand.getId())
                                                                .setTimeSharingDemandName(
                                                                        timeSharingDemand
                                                                                .getName()))
                                                .setTimeSharingFlag(true));
                                countDownLatch.countDown();
                            });
                });

        countDownLatch.await(10, TimeUnit.SECONDS);
        List<MaxDemandVo> sortedList =
                timeSharingMaxDemandList.stream()
                        .sorted(
                                Comparator.comparingLong(
                                        e -> e.getTimeSharingMaxDemandVo().getValueVO().getTime()))
                        .collect(Collectors.toList());
        return Map.of(requestWithGroupId.getStartDate(), sortedList);
    }

    @Override
    public Map<Long, List<MaxDemandVo>> getMaxDemandRateFromDbNew(
            RequestWithGroupId requestWithGroupId, boolean isMeasurementSearch) {
        String projectId = requestWithGroupId.getProjectId();
        Pair<Integer, List<String>> modelAndGroupIds =
                getDemandGroupIds(requestWithGroupId, projectId);
        List<String> groupIds = modelAndGroupIds.getSecond();
        if (CollectionUtils.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        // just one groupId
        String groupId = groupIds.get(0);
        GroupEntity group = groupService.getById(groupId);
        // 改从数据库里面查询
        List<GroupDemandInfoEntity> list =
                groupDemandMonthIncomeMapper.selectList(
                        new LambdaQueryWrapper<GroupDemandInfoEntity>()
                                .eq(GroupDemandInfoEntity::getProjectId, projectId)
                                .eq(GroupDemandInfoEntity::getGroupId, groupId)
                                .between(
                                        GroupDemandInfoEntity::getStartTime,
                                        requestWithGroupId.getStartDate(),
                                        requestWithGroupId.getEndDate()));

        Map<Long, List<MaxDemandVo>> result = new HashMap<>();
        // 1. 根据 start_time 分组 得到每个月的数据
        Map<Long, List<GroupDemandInfoEntity>> groupStartTimeMap =
                list.stream().collect(Collectors.groupingBy(GroupDemandInfoEntity::getStartTime));
        groupStartTimeMap.forEach(
                (startTime, groupDemandInfos) -> {
                    Map<Boolean, List<GroupDemandInfoEntity>> groupTimeSharingFlagMap =
                            groupDemandInfos.stream()
                                    .collect(
                                            Collectors.groupingBy(
                                                    GroupDemandInfoEntity::getTimeSharingFlag));
                    // 兼容一下 如果以前没有分时的数据 就把月份的返回 所以有一个 && 后面的判断
                    if (Boolean.TRUE.equals(group.getTimeSharingDemandController())
                            && CollUtil.isNotEmpty(groupTimeSharingFlagMap.get(true))) {
                        List<GroupDemandInfoEntity> groupDemandInfoEntityList =
                                groupTimeSharingFlagMap.get(true);
                        List<MaxDemandVo> maxDemandVos = new ArrayList<>();
                        groupDemandInfoEntityList.forEach(
                                groupDemandInfoEntity -> {
                                    MaxDemandVo maxDemandVo =
                                            new MaxDemandVo()
                                                    .setTimeSharingFlag(true)
                                                    .setTimeSharingMaxDemandVo(
                                                            new MaxDemandVo.TimeSharingMaxDemandVo()
                                                                    .setValueVO(
                                                                            new ValueVO()
                                                                                    .setValue(
                                                                                            groupDemandInfoEntity
                                                                                                    .getDemandMaxPower()))
                                                                    .setTimeSharingDemandName(
                                                                            groupDemandInfoEntity
                                                                                    .getTimeSharingDemandName()));
                                    maxDemandVos.add(maxDemandVo);
                                });
                        result.put(startTime, maxDemandVos);
                    } else {
                        List<GroupDemandInfoEntity> groupDemandInfoEntityList =
                                groupTimeSharingFlagMap.get(false);
                        List<MaxDemandVo> maxDemandVos = new ArrayList<>();
                        if (CollUtil.isNotEmpty(groupDemandInfoEntityList)) {
                            groupDemandInfoEntityList.forEach(
                                    groupDemandInfoEntity -> {
                                        MaxDemandVo maxDemandVo =
                                                new MaxDemandVo()
                                                        .setTimeSharingFlag(false)
                                                        .setMonthMaxDemandVo(
                                                                new MaxDemandVo.MonthMaxDemandVo()
                                                                        .setValueVO(
                                                                                new ValueVO()
                                                                                        .setValue(
                                                                                                isMeasurementSearch
                                                                                                        ? groupDemandInfoEntity
                                                                                                                .getDemandMaxPowerMeter()
                                                                                                        : groupDemandInfoEntity
                                                                                                                .getDemandMaxPower())));
                                        maxDemandVos.add(maxDemandVo);
                                    });
                        }
                        result.put(startTime, maxDemandVos);
                    }
                });
        return result;
        // 2. 再对 这个分组数据 进行 遍历分组 按照 time_sharing_flag 分组 得到 月的和分时的 2个类型
        // 3. 如果开关开了 有分时用分时 无分时用 以前的
        //        list.stream().collect(Collectors.groupingBy(GroupDemandInfoEntity::timesha))
    }

    @Override
    public Map<Long, List<MaxDemandVo>> getRealTimeMaxDemand(
            RequestWithGroupId rateRequest, GroupEntity group) throws InterruptedException {
        // 根据当前开关 去查询当前月份 是按照分时还是以前的 如果是以前的可能需要转换一下
        Map<Long, List<MaxDemandVo>> timeSharingMaxDemandResultMap = new HashMap<>();
        if (group != null && Boolean.TRUE.equals(group.getTimeSharingDemandController())) {
            timeSharingMaxDemandResultMap = getTimeSharingMaxDemandNew(rateRequest);
        } else {
            Map<String, Map<String, List<ValueVO>>> maxDemand = getMaxDemand(rateRequest);
            if (maxDemand.containsKey(group.getId())) {
                List<ValueVO> valueVOS = maxDemand.get(group.getId()).get("realDemand");
                if (CollUtil.isNotEmpty(valueVOS)) {
                    ValueVO valueVO = valueVOS.get(0);
                    timeSharingMaxDemandResultMap.put(
                            valueVO.getTime(),
                            List.of(
                                    new MaxDemandVo()
                                            .setMonthMaxDemandVo(
                                                    new MaxDemandVo.MonthMaxDemandVo()
                                                            .setValueVO(valueVO))
                                            .setTimeSharingFlag(false)));
                }
            }
        }
        return timeSharingMaxDemandResultMap;
    }

    @Override
    public Map<String, Map<String, List<ValueVO>>> getMaxDemandRateFromDb(
            RequestWithGroupId requestWithGroupId, boolean isMeasurementSearch) {
        String projectId = requestWithGroupId.getProjectId();
        Pair<Integer, List<String>> modelAndGroupIds =
                getDemandGroupIds(requestWithGroupId, projectId);
        List<String> groupIds = modelAndGroupIds.getSecond();
        if (CollectionUtils.isEmpty(groupIds)) {
            return null;
        }
        // just one groupId
        String groupId = groupIds.get(0);
        // 改从数据库里面查询
        List<GroupDemandInfoEntity> list =
                groupDemandMonthIncomeMapper.selectList(
                        new LambdaQueryWrapper<GroupDemandInfoEntity>()
                                .eq(GroupDemandInfoEntity::getProjectId, projectId)
                                .eq(GroupDemandInfoEntity::getGroupId, groupId)
                                .eq(GroupDemandInfoEntity::getTimeSharingFlag, false)
                                .between(
                                        GroupDemandInfoEntity::getStartTime,
                                        requestWithGroupId.getStartDate(),
                                        requestWithGroupId.getEndDate()));

        List<ValueVO> values = new ArrayList<>();
        Map<String, List<ValueVO>> influxMap = new HashMap<>();
        for (GroupDemandInfoEntity groupDemandInfoEntity : list) {
            values.add(
                    new ValueVO()
                            .setValue(
                                    // 如果是计量电表 查询 则使用 getDemandMaxPowerMeter 字段
                                    isMeasurementSearch
                                            ? groupDemandInfoEntity.getDemandMaxPowerMeter()
                                            : groupDemandInfoEntity.getDemandMaxPower())
                            .setTime(groupDemandInfoEntity.getEndTime()));
        }
        influxMap.put("meter_power", values);
        return Map.of(groupId, transformMapKey(influxMap));
    }

    @Override
    public Map<String, List<ValueVO>> getMaxDemandMeterMeasurement(
            RequestWithGroupId requestWithGroupId) {
        String projectId = requestWithGroupId.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<String> meterIds =
                ammeterService.findDemandMetersByGroupId(requestWithGroupId.getGroupId()).stream()
                        .map(AmmeterEntity::getId)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(meterIds)) {
            return new HashMap<>();
        }
        return FluxAdapter.query(
                        FluxAdapter.builder()
                                .project(projectEntity)
                                .timeZoneSet(true)
                                .from(influxClient.getBucketForever())
                                .range(
                                        requestWithGroupId.getStartDate(),
                                        requestWithGroupId.getEndDate())
                                .measurement(influxClient.getMeterTable(projectId))
                                .equipmentIds(meterIds)
                                .fields(
                                        List.of(
                                                MeterFieldEnum.MAX_NEGATIVE_ACTIVE_POWER_DEMAND
                                                        .field()))
                                .toFlux()
                                .aggregateWindow(
                                        1L, ChronoUnit.MONTHS, EmsConstants.INFLUX_LAST_FUNC)
                                .withCreateEmpty(false)
                                .timeShift(-5L, ChronoUnit.DAYS)
                                .groupBy(
                                        List.of(
                                                EmsConstants.INFLUX_TIME,
                                                EmsConstants.INFLUX_FIELD))
                                .sum()
                                .toString())
                .handleResult()
                .toMap();
    }

    @Override
    public List<ValueVO> getCapacity(CapacityRequest capacityRequest) {
        return FluxAdapter.query(
                        FluxAdapter.builder()
                                .projectId(capacityRequest.getProjectId())
                                .from(influxClient.getBucketDemand())
                                .range(capacityRequest.getStart(), capacityRequest.getEnd())
                                .measurement(
                                        influxClient.getCapacityTable(
                                                capacityRequest.getProjectId()))
                                .groupId(capacityRequest.getGroupId())
                                .fields(List.of(MeterFieldEnum.CONTROL_POWER.field()))
                                .toFlux()
                                .toString())
                .handleResult()
                .toListFilterNullValue();
    }
}
