package com.wifochina.modules.diagram.controller;

import com.sun.xml.bind.v2.TODO;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.ApplicationHolder;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.diagram.GroupDiagramServiceKt;
import com.wifochina.modules.diagram.VO.BatteryVO;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndGroupId;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndPcs;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.AbstractDiagramExtendService;
import com.wifochina.modules.diagram.service.AmmeterCustomRateStrategy.CustomFieldStrategy;
import com.wifochina.modules.diagram.service.DefaultAmmeterCustomRateStrategy;
import com.wifochina.modules.diagram.service.NewDiagramService;
import com.wifochina.modules.diagram.service.SocDiagramService;
import com.wifochina.modules.diagram.utils.DiagramCalUtils;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.oauth.util.WebUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * DiagramController
 *
 * <AUTHOR>
 * @version 1.0
 * @since 4/18/2022 6:47 PM
 */
@RequestMapping("/diagram")
@RestController
@Api(tags = "16-历史曲线")
@RequiredArgsConstructor
public class DiagramController {

    private final AbstractDiagramExtendService diagramExtendService;

    private final DeviceService deviceService;

    private final DataService dataService;

    private final AmmeterService ammeterService;

    private final DefaultAmmeterCustomRateStrategy defaultAmmeterCustomRateStrategy;

    private final NewDiagramService newDiagramService;
    private final InfluxClientService influxClient;

    private final SocDiagramService socDiagramService;

    private final PointListHolder pointListHolder;

    private final GroupDiagramServiceKt groupDiagramService;

    @PostMapping("/getTemperatureRate")
    @ApiOperation("温度监控曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getTemperatureRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getTemperatureRate(
            @RequestBody RequestWithDeviceId requestWithDeviceId) {
        // fields 可能无法统一 site 那边是 没有前缀 custom 这里如果go改不了就只能这边兼容了
        // site 采集任务里面加了 custom_ 这里还是这样吧
        //        return Result.success(
        Map<String, Map<String, List<ValueVO>>> controllableRate =
                newDiagramService.getControllableRate(
                        requestWithDeviceId,
                        influxClient.getBucketRealtime(),
                        List.of(
                                "custom_temperature_return_difference",
                                "custom_debug_limit_power",
                                "custom_phase_a_temperature",
                                "custom_phase_b_temperature",
                                "custom_phase_c_temperature",
                                "custom_circuit_d_temperature"));

        if (controllableRate != null) {
            controllableRate.forEach(
                    (deviceId, fieldMaps) -> {
                        Map<String, List<ValueVO>> resultMap = new HashMap<>();
                        List<ValueVO> results = new ArrayList<>();
                        List<ValueVO> debugLimitPowerListValues =
                                fieldMaps.get("custom_debug_limit_power");
                        List<ValueVO> returnDifferenceListValues =
                                fieldMaps.get("custom_temperature_return_difference");
                        Map<Long, ValueVO> returnDifferenceMaps =
                                returnDifferenceListValues.stream()
                                        .collect(
                                                Collectors.toMap(
                                                        ValueVO::getTime, valueVO -> valueVO));
                        if (debugLimitPowerListValues != null) {
                            debugLimitPowerListValues.forEach(
                                    debugLimitPowerValue -> {
                                        ValueVO resultVo = new ValueVO();
                                        resultVo.setTime(debugLimitPowerValue.getTime());
                                        ValueVO differenceValue =
                                                returnDifferenceMaps.get(
                                                        debugLimitPowerValue.getTime());
                                        if (differenceValue != null) {
                                            resultVo.setValue(
                                                    debugLimitPowerValue.getValue()
                                                            - differenceValue.getValue());
                                        }
                                        results.add(resultVo);
                                    });
                        }
                        resultMap.put("custom_restore_temperature", results);
                        fieldMaps.putAll(resultMap);
                    });
        }
        return Result.success(controllableRate);
    }

    /**
     * 1.4.1 added agcRate 曲线 是新的 group bucket里面的 <br>
     * 使用新的 一个 groupDiagramService kt 实现的
     *
     * @param requestWithGroupId : requestWithGroupId
     * @return : field key , 和 对应 List<ValueVO>
     */
    @PostMapping("/getAgcRate")
    @ApiOperation("agc曲线查询")
    // TODO 打开权限注释
    @PreAuthorize("hasAuthority('/diagram/getAgcRate')")
    public Result<Map<String, List<ValueVO>>> getAgcRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                groupDiagramService.getGroupRate(
                        requestWithGroupId,
                        influxClient.getBucketGroup(),
                        List.of(
                                EmsFieldEnum.POWER_RESULT_UDP_EMS_ACTIVE.field(),
                                EmsFieldEnum.POWER_RESULT_UDP_ACTIVE_POWER_COMMAND.field())));
    }

    /**
     * 1.4.1 added avcRate 曲线 是新的 group bucket里面的 <br>
     * 使用新的 一个 groupDiagramService kt 实现的
     *
     * @param requestWithGroupId : requestWithGroupId
     * @return : field key , 和 对应 List<ValueVO>
     */
    @PostMapping("/getAvcRate")
    @ApiOperation("avc曲线查询")
    // TODO 打开权限注释
    @PreAuthorize("hasAuthority('/diagram/getAvcRate')")
    public Result<Map<String, List<ValueVO>>> getAvcRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                groupDiagramService.getGroupRate(
                        requestWithGroupId,
                        influxClient.getBucketGroup(),
                        List.of(
                                EmsFieldEnum.POWER_RESULT_UDP_EMS_REACTIVE.field(),
                                EmsFieldEnum.POWER_RESULT_UDP_REACTIVE_POWER_COMMAND.field())));
    }

    /** 充电曲线查询 */
    @PostMapping("/getChargeRate")
    @ApiOperation("充放电曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getChargeRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getChargeRate(
            @RequestBody RequestWithDeviceId requestWithDeviceId) {
        return Result.success(
                newDiagramService.getDeviceRate(
                        requestWithDeviceId,
                        influxClient.getBucketMean(),
                        List.of(
                                EmsFieldEnum.EMS_AC_ACTIVE_POWER_POS.field(),
                                EmsFieldEnum.EMS_AC_ACTIVE_POWER_NEG.field())));
    }

    /** 充放电功率单线 */
    @PostMapping("/getChargeOneRate")
    @ApiOperation("充放电曲线单线(单个设备的)")
    public Result<Map<String, Map<String, List<ValueVO>>>> getChargeOneRate(
            @RequestBody RequestWithDeviceId requestWithDeviceId) {
        return Result.success(
                newDiagramService.getDeviceRate(
                        requestWithDeviceId,
                        influxClient.getBucketMean(),
                        List.of(
                                EmsFieldEnum.EMS_AC_ACTIVE_POWER.field(),
                                EmsFieldEnum.EMS_AC_REACTIVE_POWER.field())));
    }

    @PostMapping("/getChargeRateGroup")
    @ApiOperation("充放电曲线分组")
    public Result<List<ValueVO>> getChargeRateGroup(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                newDiagramService
                        .getGroupDeviceAggregationRate(
                                requestWithGroupId,
                                influxClient.getBucketMean(),
                                List.of(EmsFieldEnum.EMS_AC_ACTIVE_POWER.field()))
                        .get(EmsFieldEnum.EMS_AC_ACTIVE_POWER.field()));
    }

    @PostMapping("/getEmsRateGroupSelf")
    @ApiOperation("自定义充放电曲线分组")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getEmsRateGroupSelf(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                newDiagramService.getGroupDeviceAggregationRate(
                        requestWithGroupId,
                        influxClient.getBucketMean(),
                        List.of(
                                EmsFieldEnum.EMS_AC_ACTIVE_POWER.field(),
                                EmsFieldEnum.EMS_AC_REACTIVE_POWER.field())));
    }

    /** 储能pcs有功无功曲线 */
    @PostMapping("/getPcsRate")
    @ApiOperation("储能pcs有功无功曲线")
    @PreAuthorize("hasAuthority('/diagram/getPcsRate')")
    public Result<Map<String, List<ValueVO>>> getPcsRate(
            @RequestBody RequestWithDeviceIdAndPcs requestWithDeviceIdAndPcs) {
        return Result.success(newDiagramService.getPcsRate(requestWithDeviceIdAndPcs));
    }

    /** BMS-SOC曲线 */
    @PostMapping("/getSocRate")
    @ApiOperation("BMS-SOC曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getSocRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getSocRate(
            @RequestBody RequestWithDeviceIdAndGroupId requestWithDeviceIdAndGroupId) {
        // 1.4.0 需求 是 BSM_SOC曲线 如果是查询某个分组 按照公式
        // 计算公式是  :  ( sum 每个设备的 ( ems_soc * 单个设备的额定容量 ) ) / 总额定容量
        if (EmsConstants.ALL.equals(requestWithDeviceIdAndGroupId.getDeviceId())) {
            RequestWithGroupId requestWithGroupId = new RequestWithGroupId();
            BeanUtils.copyProperties(requestWithDeviceIdAndGroupId, requestWithGroupId);
            Map<String, List<ValueVO>> fieldMap = socDiagramService.groupSoc(requestWithGroupId);
            Map<String, Map<String, List<ValueVO>>> result = new HashMap<>();
            // 前端要求 第一个key为 all
            result.put(EmsConstants.ALL, fieldMap);
            return Result.success(result);
        } else {
            RequestWithDeviceId requestWithDeviceId = new RequestWithDeviceId();
            BeanUtils.copyProperties(requestWithDeviceIdAndGroupId, requestWithDeviceId);
            return Result.success(
                    newDiagramService.getDeviceRate(
                            requestWithDeviceId,
                            influxClient.getBucketForever(),
                            List.of(EmsFieldEnum.EMS_SOC.field())));
        }
    }

    /** BMS-电压曲线 */
    @PostMapping("/getVoltageRate")
    @ApiOperation("BMS-电压曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getVoltageRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getVoltageRate(
            @RequestBody RequestWithDeviceId requestWithDeviceId) {
        return Result.success(
                newDiagramService.getDeviceRate(
                        requestWithDeviceId,
                        influxClient.getBucketForever(),
                        List.of(EmsFieldEnum.BMS_VOLTAGE.field())));
    }

    /** BMS-电压曲线 */
    @PostMapping("/getVoltageDiffRate")
    @ApiOperation("BMS-电压差曲线")
    @PreAuthorize("hasAuthority('/diagram/getVoltageDiffRate')")
    public Result<List<ValueVO>> getVoltageDiffRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        // TODO refactor
        List<ValueVO> voltageRateList =
                newDiagramService.getBatteryDiffRate(
                        requestWithGroupId,
                        List.of(
                                EmsFieldEnum.BMS_CELL_HIGH_VOLTAGE.field(),
                                EmsFieldEnum.BMS_CELL_LOW_VOLTAGE.field()));
        return Result.success(voltageRateList);
    }

    /** BMS-电压曲线 */
    @PostMapping("/getMeterDemand")
    @ApiOperation("电表需量曲线")
    @PreAuthorize("hasAuthority('/diagram/getMeterDemand')")
    public Result<List<ValueVO>> getMeterDemand(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        List<ValueVO> meterDeamndList =
                newDiagramService.getDemandMeterRate(requestWithGroupId, WebUtils.projectId.get());
        return Result.success(meterDeamndList);
    }

    /** BMS-温度曲线 */
    @PostMapping("/getTemperatureDiffRate")
    @ApiOperation("BMS-温度差曲线")
    @PreAuthorize("hasAuthority('/diagram/getTemperatureDiffRate')")
    public Result<List<ValueVO>> getTemperatureDiffRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        List<ValueVO> temperatureRateList =
                newDiagramService.getBatteryDiffRate(
                        requestWithGroupId,
                        List.of(
                                EmsFieldEnum.BMS_CELL_HIGH_TEMPERATURE.field(),
                                EmsFieldEnum.BMS_CELL_LOW_TEMPERATURE.field()));

        return Result.success(temperatureRateList);
    }

    /** 获取当前系统所有的设备、电池簇等数目 */
    @GetMapping("/getBatteryNumber")
    @ApiOperation("电池数量查询")
    public Result<Map<String, BatteryVO>> getBatteryNumber() {
        List<DeviceEntity> deviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                        .eq(DeviceEntity::getUnreal, false)
                        .list();
        Map<String, BatteryVO> resultMap = new HashMap<>(deviceEntities.size());
        for (DeviceEntity deviceEntity : deviceEntities) {
            int[] data = dataService.get(deviceEntity.getId());
            BatteryVO batteryVO = new BatteryVO();
            batteryVO.setDeviceName(deviceEntity.getName());
            batteryVO.setDeviceId(deviceEntity.getId());
            if (data != null) {
                batteryVO.setPcs_count(data[pointListHolder.getPcsNum(data[42])]);
                batteryVO.setBms_count(data[pointListHolder.getBmsNum(data[42])]);
                batteryVO.setBms_cluster_count(data[pointListHolder.getBmsClusterNum(data[42])]);
                batteryVO.setBms_stack_per_cluster_count(
                        data[pointListHolder.getBmsStackNum(data[42])]);
            }
            resultMap.put(deviceEntity.getId(), batteryVO);
        }

        return Result.success(resultMap);
    }

    /** 实际需量曲线查询 */
    @PostMapping("/getRealDemandRate")
    @ApiOperation("实际需量曲线查询")
    @PreAuthorize(
            "hasAuthority('/diagram/getRealDemandRate') or "
                    + " hasAuthority('/diagram/getRealDemandRate_wh')")
    public Result<List<ValueVO>> getActualDemandRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        // 并网电表的值 需要取反操作 , 以前的取反是在 方法里面 现在走通用方法
        return Result.success(
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterTypeEnum.GRID.meterType().toString()));
        // return Result.success(valueVOList);
    }

    /** pv曲线查询 */
    @PostMapping("/getDcdcPvRate")
    @ApiOperation("dcdc曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getPvRate') or hasAuthority('/diagram/getPvRate_wh')")
    public Result<List<ValueVO>> getDcdcPvRate(@RequestBody RequestWithGroupId requestWithGroupId) {
        String field = EmsFieldEnum.DCDC_METER_POWER.field();
        // TODO 改成 influxClient.getBucketMean() 等task任务跑完,
        return Result.success(
                newDiagramService
                        .getGroupDeviceAggregationRate(
                                requestWithGroupId, influxClient.getBucketForever(), List.of(field))
                        .get(field));
    }

    /** pv曲线查询 */
    @PostMapping("/getPvRate")
    @ApiOperation("pv曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getPvRate') or hasAuthority('/diagram/getPvRate_wh')")
    public Result<List<ValueVO>> getPvRate(@RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterTypeEnum.PV.meterType().toString()));
    }

    /** 风电曲线查询 */
    @PostMapping("/getWindRate")
    @ApiOperation("风电曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getWindRate') or hasAuthority('/diagram/getWindRate_wh')")
    public Result<List<ValueVO>> getWindRate(@RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterTypeEnum.WIND.meterType().toString()));
    }

    /** 柴油曲线查询 */
    @PostMapping("/getDieselRate")
    @ApiOperation("柴油曲线查询")
    @PreAuthorize(
            "hasAuthority('/diagram/getDieselRate') or hasAuthority('/diagram/getDieselRate_wh')")
    public Result<List<ValueVO>> geDieselRate(@RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterTypeEnum.DIESEL.meterType().toString()));
    }

    /** 充电桩曲线查询 */
    @PostMapping("/getPileRate")
    @ApiOperation("充电桩曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getPileRate') or hasAuthority('/diagram/getPileRate_wh')")
    public Result<List<ValueVO>> gePileRate(@RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterTypeEnum.PILE.meterType().toString()));
    }

    /** 燃气曲线查询 */
    @PostMapping("/getGasRate")
    @ApiOperation("燃气曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getGasRate') or hasAuthority('/diagram/getGasRate_wh')")
    public Result<List<ValueVO>> geGasRate(@RequestBody RequestWithGroupId requestWithGroupId) {
        return Result.success(
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterTypeEnum.GAS.meterType().toString()));
    }

    /** 电网功率曲线查询 */
    @PostMapping("/getGridRate")
    @ApiOperation("电网曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getGridRate') or hasAuthority('/diagram/getGridRate_wh')")
    public Result<Map<String, List<ValueVO>>> getGridRate(
            @RequestBody RequestWithGroupId requestWithGroupId) throws CloneNotSupportedException {

        String activeField = MeterFieldEnum.AC_ACTIVE_POWER.field();

        List<ValueVO> activeList =
                newDiagramService.getMeterRate(
                        requestWithGroupId, activeField, MeterTypeEnum.GRID.meterType().toString());

        String reactiveField = MeterFieldEnum.AC_REACTIVE_POWER.field();
        List<ValueVO> reactiveList =
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        reactiveField,
                        MeterTypeEnum.GRID.meterType().toString());

        Map<String, List<ValueVO>> map = new HashMap<>(2);
        map.put(activeField, activeList);
        map.put(reactiveField, reactiveList);

        // 计算功率因数 曲线
        List<ValueVO> apparentPowerRate =
                DiagramCalUtils.getFactoryOrApparentRate(
                        false,
                        map,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterFieldEnum.AC_REACTIVE_POWER.field());
        map.put(
                "apparent_power_rate",
                apparentPowerRate.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()));
        return Result.success(map);
    }

    @PostMapping("/getAcCurrentRate")
    @ApiOperation("交流曲线查询")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getAcCurrentRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        String meterId = requestWithGroupId.getItemId();
        AmmeterEntity ammeterEntity = ammeterService.getById(meterId);
        List<String> fields =
                List.of("ac_current", "ac_currents_0", "ac_currents_1", "ac_currents_2");
        Map<String, List<ValueVO>> map = new HashMap<>(4);
        for (String field : fields) {
            List<ValueVO> acCurrentList =
                    newDiagramService.getMeterRate(
                            requestWithGroupId, field, String.valueOf(ammeterEntity.getType()));
            map.put(field, acCurrentList);
        }
        return Result.success(map);
    }

    /**
     * 查询 DCDC设备 功率 曲线 传参的itemId 是设备id 不是电表id
     *
     * @param requestWithDeviceId: requestWithDeviceId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getDcdcPowerRateByCustom")
    @ApiOperation("自定义曲线DCDC设备的电表功率")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getDcdcPowerRateByCustom(
            @RequestBody RequestWithDeviceId requestWithDeviceId)
            throws CloneNotSupportedException {
        String deviceId = requestWithDeviceId.getDeviceId();
        if (!StringUtils.hasLength(deviceId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        // 查询 DCDC光伏侧功率(kW)
        return Result.success(
                newDiagramService
                        .getDeviceRate(
                                requestWithDeviceId,
                                influxClient.getBucketMean(),
                                List.of(EmsFieldEnum.DCDC_METER_POWER.field()))
                        .get(deviceId));
    }

    /**
     * 查询 DCDC设备 功率 曲线 传参的itemId 是设备id 不是电表id
     *
     * @param requestWithDeviceId: requestWithDeviceId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getDcdcVoltageRateByCustom")
    @ApiOperation("自定义曲线DCDC设备的电压")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getDcdcVoltageRateByCustom(
            @RequestBody RequestWithDeviceId requestWithDeviceId)
            throws CloneNotSupportedException {
        String projectId = WebUtils.projectId.get();
        String deviceId = requestWithDeviceId.getDeviceId();
        if (!StringUtils.hasLength(deviceId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        // 查询 DCDC光伏侧电压(V)
        // Map<String, Map<String, List<ValueVO>>> chargeRateMap =
        // 注意这里是 forever 如果influxdb
        return Result.success(
                newDiagramService
                        .getDeviceRate(
                                requestWithDeviceId,
                                influxClient.getBucketMean(),
                                List.of("dcdc_meter_voltage"))
                        .get(deviceId));
    }

    /**
     * 查询 DCDC设备 功率 曲线 传参的itemId 是设备id 不是电表id
     *
     * @param requestWithDeviceId: requestWithDeviceId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getDcdcCurrentRateByCustom")
    @ApiOperation("自定义曲线DCDC设备的电流")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getDcdcCurrentRateByCustom(
            @RequestBody RequestWithDeviceId requestWithDeviceId)
            throws CloneNotSupportedException {
        String deviceId = requestWithDeviceId.getDeviceId();
        if (!StringUtils.hasLength(deviceId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        // 查询 DCDC光伏侧电流(A)
        return Result.success(
                newDiagramService
                        .getDeviceRate(
                                requestWithDeviceId,
                                influxClient.getBucketMean(),
                                List.of("dcdc_meter_current"))
                        .get(deviceId));
    }

    /**
     * 查询 DCDC设备 功率 曲线 传参的itemId 是设备id 不是电表id
     *
     * @param requestWithDeviceId: requestWithDeviceId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getDcdcHistoryRateByCustom")
    @ApiOperation("自定义曲线DCDC设备的历史数据")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getDcdcHistoryRateByCustom(
            @RequestBody RequestWithDeviceId requestWithDeviceId)
            throws CloneNotSupportedException {
        String projectId = WebUtils.projectId.get();

        String deviceId = requestWithDeviceId.getDeviceId();
        if (!StringUtils.hasLength(deviceId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        // 查询 历史电量输入输出
        return Result.success(
                newDiagramService
                        .getDeviceRate(
                                requestWithDeviceId,
                                influxClient.getBucketMean(),
                                List.of(
                                        "dcdc_meter_history_energy_pos",
                                        "dcdc_meter_history_energy_neg"))
                        .get(deviceId));
    }

    /**
     * 查询 电表的 有功功率 曲线
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getMeterAcPowerRateByCustom")
    @ApiOperation("自定义曲线电表有功功率")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getMeterAcPowerRateByCustom(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        String ammeterId = requestWithGroupId.getItemId();
        if (!StringUtils.hasLength(ammeterId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        Map<String, List<ValueVO>> resultMap =
                defaultAmmeterCustomRateStrategy.customRate(
                        requestWithGroupId,
                        new CustomFieldStrategy() {
                            @Override
                            public List<String> dcFields() {
                                return List.of(MeterFieldEnum.DC_POWER.field());
                            }

                            @Override
                            public List<String> acFields() {
                                return List.of(
                                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                                        MeterFieldEnum.AC_ACTIVE_POWERS_0.field(),
                                        MeterFieldEnum.AC_ACTIVE_POWERS_1.field(),
                                        MeterFieldEnum.AC_ACTIVE_POWERS_2.field());
                            }
                        });
        return Result.success(resultMap);
    }

    /**
     * 查询电表的 无功功率 曲线
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getMeterRcPowerRateByCustom")
    @ApiOperation("自定义曲线电表无功功率")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getMeterRcPowerRateByCustom(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        String ammeterId = requestWithGroupId.getItemId();
        if (!StringUtils.hasLength(ammeterId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        Map<String, List<ValueVO>> resultMap =
                defaultAmmeterCustomRateStrategy.customRate(
                        requestWithGroupId,
                        new CustomFieldStrategy() {
                            // 直流电表没有 无功功率
                            @Override
                            public List<String> acFields() {
                                return List.of(
                                        MeterFieldEnum.AC_REACTIVE_POWER.field(),
                                        MeterFieldEnum.AC_REACTIVE_POWERS_0.field(),
                                        MeterFieldEnum.AC_REACTIVE_POWERS_1.field(),
                                        MeterFieldEnum.AC_REACTIVE_POWERS_2.field());
                            }
                        });
        return Result.success(resultMap);
    }

    /**
     * 查询电表的 电压曲线
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getMeterVoltageRateByCustom")
    @ApiOperation("自定义曲线电表电压")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getMeterVoltageRateByCustom(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        String ammeterId = requestWithGroupId.getItemId();
        if (!StringUtils.hasLength(ammeterId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        Map<String, List<ValueVO>> resultMap =
                defaultAmmeterCustomRateStrategy.customRate(
                        requestWithGroupId,
                        new CustomFieldStrategy() {
                            @Override
                            public List<String> dcFields() {
                                return List.of(MeterFieldEnum.DC_VOLTAGE.field());
                            }

                            @Override
                            public List<String> acFields() {
                                return List.of(
                                        MeterFieldEnum.AC_VOLTAGE.field(),
                                        MeterFieldEnum.AC_VOLTAGES_0.field(),
                                        MeterFieldEnum.AC_VOLTAGES_1.field(),
                                        MeterFieldEnum.AC_VOLTAGES_2.field());
                            }
                        });
        return Result.success(resultMap);
    }

    /**
     * 查询 余热发电电表的 功率曲线图
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getMeterWasterPowerRateByCustom")
    @ApiOperation("自定义曲线 余热发电电表的功率曲线图")
    @PreAuthorize("hasAuthority('/diagram/selfWasterDefine')")
    public Result<List<ValueVO>> getMeterWasterPowerRateByCustom(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        String ammeterId = requestWithGroupId.getItemId();
        if (!StringUtils.hasLength(ammeterId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        return Result.success(
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterTypeEnum.WASTER.meterType().toString()));
        // return Result.success(valueVOList);
    }

    /**
     * 查询电表的 频率曲线
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getMeterFrequencyRateByCustom")
    @ApiOperation("自定义曲线电表频率")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getMeterFrequencyRateByCustom(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        String ammeterId = requestWithGroupId.getItemId();
        if (!StringUtils.hasLength(ammeterId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        Map<String, List<ValueVO>> resultMap =
                defaultAmmeterCustomRateStrategy.customRate(
                        requestWithGroupId,
                        new CustomFieldStrategy() {
                            // 直流电表没有 频率
                            @Override
                            public List<String> acFields() {
                                return List.of(MeterFieldEnum.FREQUENCY.field());
                            }

                            @Override
                            public boolean async() {
                                return false;
                            }
                        });
        return Result.success(resultMap);
    }

    /**
     * 查询电表的 功率因数(计算出来的)曲线
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getMeterFactoryRateByCustom")
    @ApiOperation("自定义曲线电表功率因数")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getMeterFactoryRateByCustom(
            @RequestBody RequestWithGroupId requestWithGroupId) throws CloneNotSupportedException {
        String ammeterId = requestWithGroupId.getItemId();
        if (!StringUtils.hasLength(ammeterId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        Map<String, List<ValueVO>> result = new HashMap<>();
        Map<String, List<ValueVO>> resultMap =
                defaultAmmeterCustomRateStrategy.customRate(
                        requestWithGroupId,
                        new CustomFieldStrategy() {
                            // 直流电表没有 频率
                            @Override
                            public List<String> acFields() {
                                // 计算功率因数 需要有总功功率和总无功功率
                                return List.of(
                                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                                        MeterFieldEnum.AC_REACTIVE_POWER.field(),
                                        MeterFieldEnum.AC_ACTIVE_POWERS_0.field(),
                                        MeterFieldEnum.AC_ACTIVE_POWERS_1.field(),
                                        MeterFieldEnum.AC_ACTIVE_POWERS_2.field(),
                                        MeterFieldEnum.AC_REACTIVE_POWERS_0.field(),
                                        MeterFieldEnum.AC_REACTIVE_POWERS_1.field(),
                                        MeterFieldEnum.AC_REACTIVE_POWERS_2.field());
                            }
                        });
        getFactoryRates(result, resultMap);
        return Result.success(result);
    }

    @PostMapping("/getApparentPowerRateByCustom")
    @ApiOperation("自定义视在功率曲线")
    @PreAuthorize("hasAuthority('/diagram/getApparentPowerRateByCustom')")
    public Result<Map<String, List<ValueVO>>> getApparentPowerRateByCustom(
            @RequestBody RequestWithGroupId requestWithGroupId) throws CloneNotSupportedException {
        Map<String, List<ValueVO>> result = new HashMap<>();
        // TODO 和 Gird的一个方法差不多 不知道为什么..
        String activeField = MeterFieldEnum.AC_ACTIVE_POWER.field();
        String reactiveField = MeterFieldEnum.AC_REACTIVE_POWER.field();
        List<ValueVO> activeList =
                newDiagramService.getMeterRate(
                        requestWithGroupId, activeField, MeterTypeEnum.GRID.meterType().toString());
        List<ValueVO> reactiveList =
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        reactiveField,
                        MeterTypeEnum.GRID.meterType().toString());

        Map<String, List<ValueVO>> map = new HashMap<>(2);
        map.put(activeField, activeList);
        map.put(reactiveField, reactiveList);
        // 计算功率因数 曲线
        List<ValueVO> apparentPowerRate =
                DiagramCalUtils.getFactoryOrApparentRate(
                        false,
                        map,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterFieldEnum.AC_REACTIVE_POWER.field());
        result.put(
                "apparent_power_rate",
                apparentPowerRate.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()));
        return Result.success(result);
    }

    private void getFactoryRates(
            Map<String, List<ValueVO>> result, Map<String, List<ValueVO>> resultMap)
            throws CloneNotSupportedException {

        // 计算功率因数 曲线
        List<ValueVO> factoryRate =
                DiagramCalUtils.getFactoryOrApparentRate(
                        true,
                        resultMap,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterFieldEnum.AC_REACTIVE_POWER.field());
        result.put(
                "factoryRate",
                factoryRate.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()));

        // 计算功率因数 曲线
        List<ValueVO> factoryRate0 =
                DiagramCalUtils.getFactoryOrApparentRate(
                        true,
                        resultMap,
                        MeterFieldEnum.AC_ACTIVE_POWERS_0.field(),
                        MeterFieldEnum.AC_REACTIVE_POWERS_0.field());
        result.put(
                "factoryRate0",
                factoryRate0.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()));

        // 计算功率因数 曲线
        List<ValueVO> factoryRate1 =
                DiagramCalUtils.getFactoryOrApparentRate(
                        true,
                        resultMap,
                        MeterFieldEnum.AC_ACTIVE_POWERS_1.field(),
                        MeterFieldEnum.AC_REACTIVE_POWERS_1.field());
        result.put(
                "factoryRate1",
                factoryRate1.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()));

        // 计算功率因数 曲线
        List<ValueVO> factoryRate2 =
                DiagramCalUtils.getFactoryOrApparentRate(
                        true,
                        resultMap,
                        MeterFieldEnum.AC_ACTIVE_POWERS_2.field(),
                        MeterFieldEnum.AC_REACTIVE_POWERS_2.field());
        result.put(
                "factoryRate2",
                factoryRate2.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()));
    }

    /**
     * 查询电表的 历史电量曲线
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : Result<Map < String, List < ValueVO>>>
     */
    @PostMapping("/getMeterHistoryInOutRateByCustom")
    @ApiOperation("自定义曲线电表历史输入输出电量")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getMeterHistoryInOutRateByCustom(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        String ammeterId = requestWithGroupId.getItemId();
        if (!StringUtils.hasLength(ammeterId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        AmmeterEntity ammeterEntity = ammeterService.getById(ammeterId);
        Map<String, List<ValueVO>> resultMap;
        if (Boolean.TRUE.equals(ammeterEntity.getTimeSharingMeasurement())) {
            resultMap = getTimeSharingHistory(requestWithGroupId, ammeterEntity);
        } else {
            resultMap = getNonTimeSharingHistory(requestWithGroupId, ammeterEntity);
        }
        return Result.success(resultMap);
    }

    private Map<String, List<ValueVO>> getTimeSharingHistory(
            RequestWithGroupId requestWithGroupId, AmmeterEntity ammeterEntity) {
        Map<String, List<ValueVO>> resultMap =
                defaultAmmeterCustomRateStrategy.customRate(
                        requestWithGroupId,
                        new CustomFieldStrategy() {
                            // 直流电表没有 无功功率
                            @Override
                            public List<String> acFields() {
                                return List.of(
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_1.field(),
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_2.field(),
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_3.field(),
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_4.field(),
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_5.field(),
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field(),
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_1.field(),
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_2.field(),
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_3.field(),
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_4.field(),
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_5.field());
                            }
                        });

        if (Objects.equals(ammeterEntity.getType(), MeterTypeEnum.GRID.meterType())) {
            // 定义需要交换的字段枚举对
            MeterFieldEnum[][] fieldsToSwap = {
                {
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH,
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH
                },
                {
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_1,
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_1
                },
                {
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_2,
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_2
                },
                {
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_3,
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_3
                },
                {
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_4,
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_4
                },
                {
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_5,
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_5
                }
            };
            // 遍历每一对需要交换的字段，并进行值的交换
            for (MeterFieldEnum[] fieldPair : fieldsToSwap) {
                MeterFieldEnum positiveField = fieldPair[0];
                MeterFieldEnum negativeField = fieldPair[1];
                List<ValueVO> positiveValues = resultMap.get(positiveField.field());
                List<ValueVO> negativeValues = resultMap.get(negativeField.field());
                if (positiveValues != null && negativeValues != null) {
                    resultMap.put(negativeField.field(), positiveValues);
                    resultMap.put(positiveField.field(), negativeValues);
                }
            }
        }
        return resultMap;
    }

    private Map<String, List<ValueVO>> getNonTimeSharingHistory(
            RequestWithGroupId requestWithGroupId, AmmeterEntity ammeterEntity) {
        Map<String, List<ValueVO>> resultMap =
                defaultAmmeterCustomRateStrategy.customRate(
                        requestWithGroupId,
                        new CustomFieldStrategy() {
                            @Override
                            public List<String> dcFields() {
                                return List.of(
                                        MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                                        MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                            }

                            @Override
                            public List<String> acFields() {
                                return List.of(
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                            }
                        });
        if (Objects.equals(ammeterEntity.getType(), MeterTypeEnum.GRID.meterType())) {
            if (Boolean.TRUE.equals(ammeterEntity.getDcMeter())) {
                List<ValueVO> dcOutList =
                        resultMap.get(MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field());
                List<ValueVO> dcInList =
                        resultMap.get(MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                resultMap.put(MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field(), dcInList);
                resultMap.put(MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH.field(), dcOutList);
            } else {
                List<ValueVO> acOutList =
                        resultMap.get(MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field());
                List<ValueVO> acInList =
                        resultMap.get(MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                resultMap.put(MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(), acInList);
                resultMap.put(MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field(), acOutList);
            }
        }
        return resultMap;
    }

    @PostMapping("/getDcCurrentRate")
    @ApiOperation("直流曲线查询")
    @PreAuthorize("hasAuthority('/diagram/selfDefine')")
    public Result<Map<String, List<ValueVO>>> getDcCurrentRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        String dcCurrentField = MeterFieldEnum.DC_CURRENT.field();
        // TODO 确认一下这个type null的情况 null表示肯定要有 对应的电表id等来查询
        List<ValueVO> dcCurrentList =
                newDiagramService.getMeterRate(requestWithGroupId, dcCurrentField, null);
        Map<String, List<ValueVO>> map = new HashMap<>(1);
        map.put(dcCurrentField, dcCurrentList);
        return Result.success(map);
    }

    /** 电网功率因数曲线查询 */
    @PostMapping("/getGridFactorRate")
    @ApiOperation("电网功率因数曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getGridFactorRate')")
    public Result<List<ValueVO>> getGridFactorRate(
            @RequestBody RequestWithGroupId requestWithGroupId) throws CloneNotSupportedException {
        List<ValueVO> activeList =
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterTypeEnum.GRID.meterType().toString());
        List<ValueVO> reactiveList =
                newDiagramService.getMeterRate(
                        requestWithGroupId,
                        MeterFieldEnum.AC_REACTIVE_POWER.field(),
                        MeterTypeEnum.GRID.meterType().toString());
        // 计算 功率因数
        List<ValueVO> factoryRate =
                DiagramCalUtils.getFactoryOrApparentRate(
                        true,
                        Map.of(
                                MeterFieldEnum.AC_ACTIVE_POWER.field(),
                                activeList,
                                MeterFieldEnum.AC_REACTIVE_POWER.field(),
                                reactiveList),
                        MeterFieldEnum.AC_ACTIVE_POWER.field(),
                        MeterFieldEnum.AC_REACTIVE_POWER.field());
        return Result.success(
                factoryRate.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()));
    }

    /** 负载功率曲线查询 */
    @PostMapping("/getLoadRate")
    @ApiOperation("负载曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getLoadRate')")
    public Result<Map<String, List<ValueVO>>> getLoadRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        Map<String, List<ValueVO>> map = newDiagramService.getLoadRate(requestWithGroupId);
        return Result.success(map);
    }

    /** 查询系统效率曲线图 */
    @PostMapping("/getEfficiencyRate")
    @ApiOperation("查询系统效率曲线图")
    @PreAuthorize("hasAuthority('/diagram/getEfficiencyRate')")
    public Result<Map<Long, Double>> getEfficiencyRate(
            @RequestBody RequestWithGroupId requestWithGroupId) {
        Map<Long, Double> efficiencyRate =
                diagramExtendService.getEfficiencyRate(requestWithGroupId);
        return Result.success(efficiencyRate);
    }
}
