package com.wifochina.modules.diagram.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BatteryRequest
 *
 * @date 4/24/2022 9:35 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "按日期查询电池芯数据")
public class BatteryRequest {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;

    @ApiModelProperty(value = "device id")
    private String deviceId;

    @ApiModelProperty(value = "period")
    private Integer period;

    @ApiModelProperty(value = "BMS数量(BMS_COUNT),序号从0开始)")
    private Integer bms_count;

    @ApiModelProperty(value = " 电池簇数量(CLUSTER <= 50,序号从0开始)")
    private Integer bms_cluster_count;

    @ApiModelProperty(value = " 每簇电池组数量(STACK <= 30),序号从0开始")
    private Integer bms_stack_per_cluster_count;
}
