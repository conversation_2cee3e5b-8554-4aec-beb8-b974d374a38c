package com.wifochina.modules.diagram.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * RequestWithMeterId
 *
 * @date 4/27/2022 9:06 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "通用电表按日期查询")
@Accessors(chain = true)
public class RequestWithGroupId extends RequestCommon {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;

    @ApiModelProperty(value = "时间间隔，可为1分钟，或者1的倍数")
    private Long period;

    @ApiModelProperty(value = "分组id，all代表全部，不能传空或者空字符串")
    private String groupId;

    @ApiModelProperty(value = "分组id，all代表全部，不能传空或者空字符串")
    private String itemId;

    // 覆写父类的 setProjectId 方法
    @Override
    public RequestWithGroupId setProjectId(String projectId) {
        super.setProjectId(projectId); // 调用父类方法
        return this; // 返回子类对象
    }
}
