package com.wifochina.modules.diagram.service;

import com.google.common.collect.Maps;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.oauth.util.WebUtils;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created on 2023/11/6 13:35. <br>
 * 电表自定义曲线查询 策略 默认实现 电表自定义曲线查询 策略
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class DefaultAmmeterCustomRateStrategy implements AmmeterCustomRateStrategy {

    private final AmmeterService ammeterService;
    private final NewDiagramService newDiagramService;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 获取电表的 自定义 曲线 , 根据电表是直流电表还是交流电表 去执行不同Field的查询
     *
     * @param requestWithGroupId : 请求参数
     * @param customFieldStrategy : 自定义字段策略
     * @return : Map<String, List<ValueVO>>
     */
    @Override
    public Map<String, List<ValueVO>> customRate(
            RequestWithGroupId requestWithGroupId, CustomFieldStrategy customFieldStrategy) {
        AmmeterEntity ammeterEntity = ammeterService.getById(requestWithGroupId.getItemId());
        if (ammeterEntity == null) {
            // 电表空
            throw new ServiceException("电表为空");
        }
        if (ammeterEntity.getType() != null) {
            if (ammeterEntity.getDcMeter() != null) {
                if (ammeterEntity.getDcMeter()) {
                    return getConcurrentMeterCustomRate(
                            ammeterEntity,
                            customFieldStrategy.dcFields(),
                            requestWithGroupId,
                            customFieldStrategy.async());
                } else {
                    return getConcurrentMeterCustomRate(
                            ammeterEntity,
                            customFieldStrategy.acFields(),
                            requestWithGroupId,
                            customFieldStrategy.async());
                }
            } else {
                return getConcurrentMeterCustomRate(
                        ammeterEntity,
                        customFieldStrategy.acFields(),
                        requestWithGroupId,
                        customFieldStrategy.async());
                // 如果一个电表 还不确定 是直流还是交流 前端不应该进来查询
                // return Map.of();
            }
        } else {
            throw new ServiceException("电表类型为空");
        }
    }

    /**
     * 根据 查询 fields 并发执行
     *
     * @param ammeterEntity : 电表
     * @param fields : 查询fields 列表
     * @param requestWithGroupId : 请求参数
     * @return : Map<String, List<ValueVO>>
     */
    private Map<String, List<ValueVO>> getConcurrentMeterCustomRate(
            AmmeterEntity ammeterEntity,
            List<String> fields,
            RequestWithGroupId requestWithGroupId,
            boolean async) {
        Map<String, List<ValueVO>> resultMap = Maps.newConcurrentMap();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        if (async) {
            asyncConcurrentQuery(ammeterEntity, fields, requestWithGroupId, resultMap, futures);
        } else {
            syncQuery(ammeterEntity, fields, requestWithGroupId, resultMap);
        }
        return resultMap;
    }

    private void syncQuery(
            AmmeterEntity ammeterEntity,
            List<String> fields,
            RequestWithGroupId requestWithGroupId,
            Map<String, List<ValueVO>> resultMap) {
        for (String field : fields) {
            //            List<ValueVO> valueVOList =
            //                    diagramService.getMeterFirst(
            //                            requestWithGroupId, field,
            // String.valueOf(ammeterEntity.getType()));
            List<ValueVO> valueVOList =
                    newDiagramService.getMeterRate(
                            requestWithGroupId, field, String.valueOf(ammeterEntity.getType()));

            resultMap.put(field, valueVOList);
        }
    }

    private void asyncConcurrentQuery(
            AmmeterEntity ammeterEntity,
            List<String> fields,
            RequestWithGroupId requestWithGroupId,
            Map<String, List<ValueVO>> resultMap,
            List<CompletableFuture<Void>> futures) {
        String projectId = WebUtils.projectId.get();
        for (String field : fields) {
            CompletableFuture<Void> itemCompletableFuture =
                    CompletableFuture.runAsync(
                            () -> {
                                List<ValueVO> valueVOList;
                                // 注意要让子线程拥有 这个 !!
                                WebUtils.projectId.set(projectId);
                                try {
                                    valueVOList =
                                            newDiagramService.getMeterRate(
                                                    requestWithGroupId,
                                                    field,
                                                    String.valueOf(ammeterEntity.getType()));
                                    //
                                    // diagramService.getMeterFirst(
                                    //
                                    // requestWithGroupId,
                                    //                                                    field,
                                    //
                                    // String.valueOf(ammeterEntity.getType()));
                                } finally {
                                    WebUtils.projectId.remove();
                                }
                                resultMap.put(field, valueVOList);
                            },
                            threadPoolTaskExecutor);
            futures.add(itemCompletableFuture);
        }
        CompletableFuture<Void> allOfTask =
                CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new));
        allOfTask.join();
    }
}
