package com.wifochina.modules.diagram.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ChargeVO
 *
 * @date 4/18/2022 6:56 PM
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value = "soc曲线值、bms-电压值")
@Data
@Accessors(chain = true)
public class ValueVOSenior implements Cloneable {

    @ApiModelProperty(value = "通用值")
    private Double value;

    @ApiModelProperty(value = "时间点")
    private Long time;

    private Double totalStorage;

    public ValueVOSenior() {}

    public ValueVOSenior(Long time) {
        this.time = time;
    }

    public ValueVOSenior(Long time, Double value) {
        this.time = time;
        this.value = value;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
