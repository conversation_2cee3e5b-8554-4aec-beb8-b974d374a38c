package com.wifochina.modules.diagram.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * chargeRequest
 * 
 * @date 4/18/2022 6:44 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "通用设备和索引按日期查询")
public class RequestWithDeviceIdAndPcs {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;

    @ApiModelProperty(value = "所属设备id，默认为all，all代表全部，不能传空或者空字符串")
    private String deviceId;

    @ApiModelProperty(value = "pcs序号，默认为all，all代表全部，不能传空或者空字符串")
    private String index;

    @ApiModelProperty(value = "时间间隔，可为null，或者5的倍数")
    private Integer period;
}