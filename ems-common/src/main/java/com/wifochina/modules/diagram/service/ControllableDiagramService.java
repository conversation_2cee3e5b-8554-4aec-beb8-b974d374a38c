package com.wifochina.modules.diagram.service;

import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithGroupId;

import java.util.List;
import java.util.Map;

/**
 * Created on 2025/7/4 11:11.
 *
 * <AUTHOR>
 */
public interface ControllableDiagramService {
    Map<String, Map<String, List<ValueVO>>> getControllableRate(
            RequestWithDeviceId requestWithDeviceId, String bucket, List<String> fields);
}
