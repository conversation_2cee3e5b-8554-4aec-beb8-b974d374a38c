package com.wifochina.modules.diagram.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2024/7/26 18:37.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FluxRateDeviceHolder {
    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;

    @ApiModelProperty(value = "时间间隔，可为1分钟，或者1的倍数")
    private Integer period;
}
