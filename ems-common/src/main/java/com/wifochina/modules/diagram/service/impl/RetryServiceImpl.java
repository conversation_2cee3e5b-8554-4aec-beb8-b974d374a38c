package com.wifochina.modules.diagram.service.impl;

import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.wifochina.modules.diagram.service.RetryService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RetryServiceImpl implements RetryService {
    @Override
    @Retryable(maxAttempts = 3, value = IllegalArgumentException.class)
    public void testRetry() {
        log.info("retry method ...");
        throw new IllegalArgumentException("manaul excetpion");
    }

    @Recover
    public void testRecover(IllegalArgumentException exception) {
        // save to db
        log.info("recover test {}", exception);
    }

    @Override
    public void testRetryTemplate() {
        log.info("retry template method ...");
        throw new IllegalArgumentException("manaul template excetpion");
    }
}
