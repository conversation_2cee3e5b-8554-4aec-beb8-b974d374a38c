package com.wifochina.modules.diagram.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.util.DemandControlEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.*;

import com.wifochina.modules.strategy.service.StrategyService;
import com.wifochina.modules.strategy.service.TimeSharingDemandService;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.util.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-02-27 9:45 AM
 */
public abstract class BaseDemandDiagramService implements DemandDiagramService {
    @Resource protected GroupService groupService;
    @Resource protected DeviceService deviceService;
    @Resource protected AmmeterService ammeterService;
    @Resource protected GroupDeviceService groupDeviceService;
    @Resource protected GroupAmmeterService groupAmmeterService;

    @Resource protected StrategyService strategyService;
    @Resource protected TimeSharingDemandService timeSharingDemandService;

    protected static final int DEFAULT_MAP_SIZE = 16;

    @NotNull
    protected Pair<Integer, List<String>> getTimeSharingDemandGroupIds(
            RequestWithGroupId requestWithGroupId, String projectId) {
        LambdaQueryWrapper<GroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(GroupEntity::getProjectId, projectId)
                // 1.4.4 added 分时需量控制 开关
                .eq(GroupEntity::getTimeSharingDemandController, true)
                .in(
                        GroupEntity::getDemandControl,
                        DemandControlEnum.enable_show_demand_income.name(),
                        DemandControlEnum.enable_hide_demand_income.name());
        if (!EmsConstants.ALL.equals(requestWithGroupId.getGroupId())) {
            queryWrapper.eq(GroupEntity::getId, requestWithGroupId.getGroupId());
        }
        List<GroupEntity> groups = groupService.list(queryWrapper);
        if (groups.isEmpty()) {
            return Pair.of(0, new ArrayList<>());
        }
        return Pair.of(
                groups.get(0).getDemandCalcModel(),
                groups.stream().map(GroupEntity::getId).collect(Collectors.toList()));
    }

    @NotNull
    protected Pair<Integer, List<String>> getDemandGroupIds(
            RequestWithGroupId requestWithGroupId, String projectId) {
        LambdaQueryWrapper<GroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(GroupEntity::getProjectId, projectId)
                .in(
                        GroupEntity::getDemandControl,
                        DemandControlEnum.enable_show_demand_income.name(),
                        DemandControlEnum.enable_hide_demand_income.name());
        //                .eq(GroupEntity::getDemandController, true);
        if (!EmsConstants.ALL.equals(requestWithGroupId.getGroupId())) {
            queryWrapper.eq(GroupEntity::getId, requestWithGroupId.getGroupId());
        }
        List<GroupEntity> groups = groupService.list(queryWrapper);
        if (groups.isEmpty()) {
            return Pair.of(0, new ArrayList<>());
        }
        return Pair.of(
                groups.get(0).getDemandCalcModel(),
                groups.stream().map(GroupEntity::getId).collect(Collectors.toList()));
    }

    @NotNull
    protected static Map<String, List<ValueVO>> getGroupDemandMap() {
        List<ValueVO> originalDemandList = new ArrayList<>(DEFAULT_MAP_SIZE * 10);
        List<ValueVO> realDemandList = new ArrayList<>(DEFAULT_MAP_SIZE * 10);
        List<ValueVO> realControlList = new ArrayList<>(DEFAULT_MAP_SIZE * 10);
        Map<String, List<ValueVO>> groupDemandMap = new HashMap<>(3);
        groupDemandMap.put("originalDemand", originalDemandList);
        groupDemandMap.put("realDemand", realDemandList);
        groupDemandMap.put("realControl", realControlList);
        return groupDemandMap;
    }

    protected static int getPeriod(Long start, Long end, Integer demandModel) {
        // 相差的天数
        int periodTime = (int) ((end - start) / 86400);
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        int demandCalMode = demandModel;
        int period = 15;
        if (demandCalMode == 3) {
            period = (periodTime / 2 + 1);
        } else if (demandCalMode == 2) {
            period = 30;
        }
        return period;
    }
}
