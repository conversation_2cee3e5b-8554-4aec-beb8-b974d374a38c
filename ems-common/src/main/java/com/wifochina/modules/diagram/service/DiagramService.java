package com.wifochina.modules.diagram.service;

import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndPcs;
import com.wifochina.modules.diagram.request.RequestWithGroupId;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DiagramService
 *
 * @date 4/18/2022 6:50 PM
 * <AUTHOR>
 * @version 1.0
 */
public interface DiagramService {

    /**
     * @param requestWithDeviceId 请求的设备id以及日期
     * @return Map<String, SocVO> Soc
     */
    Map<String, Map<String, List<ValueVO>>> getRate(
            String bucket,
            RequestWithDeviceId requestWithDeviceId,
            List<String> filed,
            String type,
            String measurement);

    /**
     * @param requestWithDeviceId 请求的设备id以及日期
     * @return Map<String, SocVO> Soc
     */
    Map<String, List<ValueVO>> getRateForLargeScreen(
            String bucket,
            RequestWithDeviceId requestWithDeviceId,
            List<String> filed,
            String measurement);

    /**
     * @param requestWithDeviceId 请求的设备id以及日期
     * @return Map<String, SocVO> Soc
     */
    Map<String, Map<String, List<ValueVO>>> getBatteryRate(
            RequestWithDeviceId requestWithDeviceId,
            List<String> filed,
            int cluster,
            int stack,
            int cell,
            int cellT);

    Map<String, List<ValueVO>> getPcsRate(RequestWithDeviceIdAndPcs requestWithDeviceIdAndPcs);

    /**
     * @param requestWithGroupId 请求的group id以及日期
     * @return List<ValueVO>
     */
    List<ValueVO> getMeterRate(RequestWithGroupId requestWithGroupId, String filed, String type);

    List<ValueVO> getMeterFirst(RequestWithGroupId requestWithGroupId, String field, String type);

    /**
     * @param requestWithDeviceId 请求的及日期
     * @return List<ValueVO>
     */
    List<ValueVO> getEmsRate(RequestWithGroupId requestWithDeviceId);

    Map<String, List<ValueVO>> getLoadRate(RequestWithGroupId loadRateRequest);

    default List<ValueVO> getFactoryRate(List<ValueVO> activeList, List<ValueVO> reactiveList)
            throws CloneNotSupportedException {
        Map<Long, Double> activeMap =
                activeList.stream().collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        Map<Long, Double> reactiveMap =
                reactiveList.stream()
                        .collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        ValueVO templateValueVo = new ValueVO();
        List<ValueVO> apparentList = new ArrayList<>();
        for (Long time : activeMap.keySet()) {
            Double activeValue = activeMap.get(time);
            Double reactiveValue = reactiveMap.get(time);
            if (activeValue != null && reactiveValue != null) {
                double apparentPower =
                        Math.sqrt((Math.pow(activeValue, 2) + Math.pow(reactiveValue, 2)));
                if (apparentPower != 0) {
                    ValueVO valueVO = (ValueVO) templateValueVo.clone();
                    valueVO.setTime(time);
                    double powerFactor = Math.abs(activeValue / apparentPower);
                    if (!Double.isNaN(powerFactor)) {
                        valueVO.setValue(powerFactor);
                    } else {
                        valueVO.setValue(0.0);
                    }
                    apparentList.add(valueVO);
                }
            }
        }
        return apparentList;
    }

    /**
     * 计算 视在功率
     *
     * @param activeList : 有功功率
     * @param reactiveList : 无功功率
     * @return : 功率因数 List<ValueVO>
     */
    default List<ValueVO> getApparentPowerRate(List<ValueVO> activeList, List<ValueVO> reactiveList)
            throws CloneNotSupportedException {
        Map<Long, Double> activeMap =
                activeList.stream().collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        Map<Long, Double> reactiveMap =
                reactiveList.stream()
                        .collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
        ValueVO templateValueVo = new ValueVO();
        List<ValueVO> apparentList = new ArrayList<>();
        for (Long time : activeMap.keySet()) {
            Double activeValue = activeMap.get(time);
            Double reactiveValue = reactiveMap.get(time);
            if (activeValue != null && reactiveValue != null) {
                double apparentPower =
                        Math.sqrt((Math.pow(activeValue, 2) + Math.pow(reactiveValue, 2)));
                if (apparentPower != 0) {
                    ValueVO valueVO = (ValueVO) templateValueVo.clone();
                    valueVO.setTime(time);
                    valueVO.setValue(apparentPower);
                    apparentList.add(valueVO);
                }
            }
        }
        return apparentList;
    }

    List<ValueVO> getBatteryDiffRate(RequestWithGroupId requestWithGroupId, String column);

    List<ValueVO> getRateForDcdc(
            String bucket,
            RequestWithGroupId requestWithGroupId,
            String filed,
            String type,
            String measurement);
}
