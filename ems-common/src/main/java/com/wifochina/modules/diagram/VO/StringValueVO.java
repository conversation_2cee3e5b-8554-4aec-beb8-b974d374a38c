package com.wifochina.modules.diagram.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * StringValueVO
 *
 * @date 2025-08-14 17:02:36
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value = "soc曲线值、bms-电压值")
@Data
@Accessors(chain = true)
public class StringValueVO implements Cloneable {

    @ApiModelProperty(value = "通用值")
    private String value;

    @ApiModelProperty(value = "时间点")
    private Long time;

    public StringValueVO() {}

    public StringValueVO(Long time) {
        this.time = time;
    }

    public StringValueVO(Long time, String value) {
        this.time = time;
        this.value = value;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
