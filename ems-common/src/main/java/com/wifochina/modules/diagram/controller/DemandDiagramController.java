package com.wifochina.modules.diagram.controller;

import cn.hutool.core.collection.CollUtil;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.page.Result;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.diagram.VO.MaxDemandVo;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.CapacityRequest;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.DemandDiagramService;
import com.wifochina.modules.diagram.service.NewDiagramService;
import com.wifochina.modules.diagram.utils.DiagramCalUtils;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-02-27 10:29 AM
 */
@RequestMapping("/diagram")
@RestController
@Api(tags = "16-历史曲线")
@RequiredArgsConstructor
@Slf4j
public class DemandDiagramController {

    @Resource private DemandDiagramService demandDiagramService;

    @Resource private NewDiagramService newDiagramService;

    @Resource private GroupService groupService;
    @Resource private ProjectService projectService;

    /** 申报需量+原需量+实际需量+控制需量 */
    @PostMapping("/getDemandRate")
    @ApiOperation("需量曲线查询")
    @PreAuthorize(
            "hasAuthority('/diagram/getDemandRate') or hasAuthority('diagram/getGirdSupplyPoint')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getDemandRate(
            @RequestBody RequestWithGroupId rateRequest) {
        return Result.success(demandDiagramService.getDemandRate(rateRequest));
    }

    @PostMapping("/getMaxDemandRateNew")
    @ApiOperation("最大需量曲线查询New 支持分时")
    @PreAuthorize("hasAuthority('/diagram/getMaxDemandRate')")
    public Result<Map<Long, List<MaxDemandVo>>> getMaxDemandRateNew(
            @RequestBody RequestWithGroupId rateRequest) throws InterruptedException {
        rateRequest.setProjectId(WebUtils.projectId.get());
        // 这里要判断一下如果是 查询当月开始的 缓存里没有数据 需要实时从influxdb里查询
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        GroupEntity group = groupService.getById(rateRequest.getGroupId());
        long currentMonthZeroTime = MyTimeUtil.getCurrentMonthZeroTime(projectEntity.getTimezone());
        Map<Long, List<MaxDemandVo>> timeSharingMaxDemandResultMap;
        if (rateRequest.getStartDate() >= currentMonthZeroTime) {
            timeSharingMaxDemandResultMap =
                    demandDiagramService.getRealTimeMaxDemand(rateRequest, group);
        } else {
            if (rateRequest.getEndDate() > currentMonthZeroTime) {
                Map<Long, List<MaxDemandVo>> currentMonthMaxDemandMap;
                currentMonthMaxDemandMap =
                        demandDiagramService.getRealTimeMaxDemand(
                                new RequestWithGroupId()
                                        .setGroupId(rateRequest.getGroupId())
                                        .setProjectId(rateRequest.getProjectId())
                                        .setStartDate(currentMonthZeroTime)
                                        .setEndDate(rateRequest.getEndDate()),
                                group);
                timeSharingMaxDemandResultMap =
                        demandDiagramService.getMaxDemandRateFromDbNew(rateRequest, false);
                if (timeSharingMaxDemandResultMap != null) {
                    if (CollUtil.isNotEmpty(currentMonthMaxDemandMap)) {
                        List<MaxDemandVo> currentMaxDemandVos =
                                currentMonthMaxDemandMap.get(rateRequest.getStartDate());
                        timeSharingMaxDemandResultMap.put(
                                rateRequest.getStartDate(), currentMaxDemandVos);
                    }
                }
            } else {
                timeSharingMaxDemandResultMap =
                        demandDiagramService.getMaxDemandRateFromDbNew(rateRequest, false);
            }
        }
        return Result.success(timeSharingMaxDemandResultMap);
    }

    /** 最大需量曲线 */
    @PostMapping("/getMaxDemandRate")
    @ApiOperation("最大需量曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getMaxDemandRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getMaxDemandRate(
            @RequestBody RequestWithGroupId rateRequest) {
        rateRequest.setProjectId(WebUtils.projectId.get());
        // 这里要判断一下如果是 查询当月开始的 缓存里没有数据 需要实时从influxdb里查询
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        long currentMonthZeroTime = MyTimeUtil.getCurrentMonthZeroTime(projectEntity.getTimezone());
        if (rateRequest.getStartDate() >= currentMonthZeroTime) {
            // 查询当月的
            //            Map<Long, List<MaxDemandVo>> timeSharingMaxDemandNew =
            //                    demandDiagramService.getTimeSharingMaxDemandNew(rateRequest);
            return Result.success(demandDiagramService.getMaxDemand(rateRequest));
        } else {
            if (rateRequest.getEndDate() > currentMonthZeroTime) {
                RequestWithGroupId rateRequest2 = new RequestWithGroupId();
                BeanUtils.copyProperties(rateRequest, rateRequest2);
                rateRequest2.setStartDate(currentMonthZeroTime);
                Map<String, Map<String, List<ValueVO>>> maxDemand =
                        demandDiagramService.getMaxDemand(rateRequest2);
                ValueVO currentMonthValuesVo = new ValueVO();
                currentMonthValuesVo.setValue(0.0);
                currentMonthValuesVo.setTime(rateRequest.getEndDate());
                if (!maxDemand.isEmpty()) {
                    List<ValueVO> currentMonthList =
                            maxDemand.get(rateRequest.getGroupId()).get("realDemand");
                    if (!currentMonthList.isEmpty()) {
                        currentMonthValuesVo = currentMonthList.get(0);
                    }
                }
                Map<String, Map<String, List<ValueVO>>> maxDemandRateFromDb =
                        demandDiagramService.getMaxDemandRateFromDb(rateRequest, false);
                if (maxDemandRateFromDb != null && !maxDemandRateFromDb.isEmpty()) {
                    maxDemandRateFromDb
                            .get(rateRequest.getGroupId())
                            .get("realDemand")
                            .add(currentMonthValuesVo);
                } else {
                    maxDemandRateFromDb = new HashMap<>();
                    maxDemandRateFromDb.put(
                            rateRequest.getGroupId(),
                            Map.of(
                                    "realControl",
                                    List.of(),
                                    "realDemand",
                                    List.of(),
                                    "originalDemand",
                                    List.of()));
                }
                return Result.success(maxDemandRateFromDb);
            } else {
                return Result.success(
                        demandDiagramService.getMaxDemandRateFromDb(rateRequest, false));
            }
        }
        //        return Result.success(demandDiagramService.getMaxDemandRate(rateRequest));
    }

    /** 最大需量曲线电表 */
    @PostMapping("/getMaxDemandRateFromMeter")
    @ApiOperation("最大需量曲线查询(电表)")
    @PreAuthorize("hasAuthority('/diagram/getMaxDemandRateFromMeter')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getMaxDemandRateFromMeter(
            @RequestBody RequestWithGroupId rateRequest) {
        rateRequest.setProjectId(WebUtils.projectId.get());
        // 这里要判断一下如果是 查询当月开始的 缓存里没有数据 需要实时从influxdb里查询
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        long currentMonthZeroTime = MyTimeUtil.getCurrentMonthZeroTime(projectEntity.getTimezone());
        if (rateRequest.getStartDate().equals(currentMonthZeroTime)) {
            // 查询当月的
            Map<String, List<ValueVO>> maxDemandMeterMeasurement =
                    demandDiagramService.getMaxDemandMeterMeasurement(rateRequest);
            List<ValueVO> valueVoList = null;
            if (maxDemandMeterMeasurement != null && !maxDemandMeterMeasurement.isEmpty()) {
                valueVoList =
                        maxDemandMeterMeasurement.get(
                                MeterFieldEnum.MAX_NEGATIVE_ACTIVE_POWER_DEMAND.field());
            }
            return Result.success(
                    Map.of(
                            rateRequest.getGroupId(),
                            Map.of(
                                    "realControl",
                                    List.of(),
                                    "realDemand",
                                    valueVoList == null ? List.of() : valueVoList,
                                    "originalDemand",
                                    List.of())));
        } else {
            if (rateRequest.getEndDate() > currentMonthZeroTime) {
                RequestWithGroupId rateRequest2 = new RequestWithGroupId();
                BeanUtils.copyProperties(rateRequest, rateRequest2);
                rateRequest2.setStartDate(currentMonthZeroTime);
                Map<String, List<ValueVO>> maxDemandFromMeter =
                        demandDiagramService.getMaxDemandMeterMeasurement(rateRequest2);
                ValueVO currentMonthValuesVo = new ValueVO();
                if (!maxDemandFromMeter.isEmpty()) {
                    List<ValueVO> currentMonthList =
                            maxDemandFromMeter.get(
                                    MeterFieldEnum.MAX_NEGATIVE_ACTIVE_POWER_DEMAND.field());
                    currentMonthValuesVo = currentMonthList.get(0);
                } else {
                    currentMonthValuesVo.setValue(0.0);
                    currentMonthValuesVo.setTime(rateRequest.getEndDate());
                }
                Map<String, Map<String, List<ValueVO>>> maxDemandMeterFromDb =
                        demandDiagramService.getMaxDemandRateFromDb(rateRequest, true);
                if (maxDemandMeterFromDb != null && !maxDemandMeterFromDb.isEmpty()) {
                    maxDemandMeterFromDb
                            .get(rateRequest.getGroupId())
                            .get("realDemand")
                            .add(currentMonthValuesVo);
                } else {
                    maxDemandMeterFromDb = new HashMap<>();
                    maxDemandMeterFromDb.put(
                            rateRequest.getGroupId(),
                            Map.of(
                                    "realControl",
                                    List.of(),
                                    "realDemand",
                                    List.of(),
                                    "originalDemand",
                                    List.of()));
                }
                return Result.success(maxDemandMeterFromDb);
            } else {
                return Result.success(
                        demandDiagramService.getMaxDemandRateFromDb(rateRequest, true));
            }
        }
    }

    /** 最大需量曲线电表 */
    @PostMapping("/getCapacity")
    @ApiOperation("并网点目标控制功率查询")
    @PreAuthorize("hasAuthority('/diagram/getGirdSupplyPoint')")
    public Result<List<ValueVO>> getCapacity(@RequestBody CapacityRequest capacityRequest) {
        return Result.success(demandDiagramService.getCapacity(capacityRequest));
    }

    /** 申报需量+原需量+实际需量+控制需量 并网点功率曲线 用视在算的 */
    @PostMapping("/getGirdPowerRateOrigin")
    @ApiOperation("并网点功率曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getDemandRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getGirdPowerRateOrigin(
            @RequestBody RequestWithGroupId rateRequest) {
        RequestWithGroupId requestWithGroupId = new RequestWithGroupId();
        requestWithGroupId.setStartDate(rateRequest.getStartDate());
        requestWithGroupId.setEndDate(rateRequest.getEndDate());
        requestWithGroupId.setItemId(EmsConstants.ALL);
        requestWithGroupId.setPeriod(rateRequest.getPeriod());
        requestWithGroupId.setProjectId(WebUtils.projectId.get());
        Map<String, Map<String, List<ValueVO>>> map = new HashMap<>();
        if (EmsConstants.ALL.equals(rateRequest.getGroupId())) {
            List<String> groupIds =
                    groupService.queryEnableDemandControl(WebUtils.projectId.get()).stream()
                            .map(GroupEntity::getId)
                            .collect(Collectors.toList());
            groupIds.forEach(
                    groupId -> {
                        requestWithGroupId.setGroupId(groupId);
                        rateRequest.setGroupId(groupId);
                        // 这里如果是并网点功率曲线 则是取的是 视在计算方式
                        Map<String, List<ValueVO>> groupDemandMap =
                                getGirdPowerRate(rateRequest, requestWithGroupId);
                        map.put(groupId, groupDemandMap);
                    });
        } else {
            requestWithGroupId.setGroupId(rateRequest.getGroupId());
            Map<String, List<ValueVO>> groupDemandMap =
                    getGirdPowerRate(rateRequest, requestWithGroupId);
            map.put(rateRequest.getGroupId(), groupDemandMap);
        }
        return Result.success(map);
    }

    /** 申报需量+原需量+实际需量+控制需量 */
    @PostMapping("/getDemandRateOrigin")
    @ApiOperation("原始需量曲线查询")
    @PreAuthorize("hasAuthority('/diagram/getDemandRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getDemandRateOrigin(
            @RequestBody RequestWithGroupId rateRequest) {
        RequestWithGroupId requestWithGroupId = new RequestWithGroupId();
        requestWithGroupId.setStartDate(rateRequest.getStartDate());
        requestWithGroupId.setEndDate(rateRequest.getEndDate());
        requestWithGroupId.setItemId(EmsConstants.ALL);
        requestWithGroupId.setPeriod(rateRequest.getPeriod());
        requestWithGroupId.setProjectId(WebUtils.projectId.get());
        Map<String, Map<String, List<ValueVO>>> map = new HashMap<>();
        if (EmsConstants.ALL.equals(rateRequest.getGroupId())) {
            List<String> groupIds =
                    groupService.queryEnableDemandControl(WebUtils.projectId.get()).stream()
                            .map(GroupEntity::getId)
                            .collect(Collectors.toList());
            groupIds.forEach(
                    groupId -> {
                        requestWithGroupId.setGroupId(groupId);
                        rateRequest.setGroupId(groupId);
                        Map<String, List<ValueVO>> groupDemandMap =
                                getDemandRate(rateRequest, requestWithGroupId);
                        map.put(groupId, groupDemandMap);
                    });
        } else {
            requestWithGroupId.setGroupId(rateRequest.getGroupId());
            Map<String, List<ValueVO>> groupDemandMap =
                    getDemandRate(rateRequest, requestWithGroupId);
            map.put(rateRequest.getGroupId(), groupDemandMap);
        }
        return Result.success(map);
    }

    /**
     * 查询 并网点功率曲线 这个是视在的计算方式
     *
     * @param rateRequest :rateRequest
     * @param requestWithGroupId :requestWithGroupId
     * @return :Map<String, List<ValueVO>>
     */
    @NotNull
    private Map<String, List<ValueVO>> getGirdPowerRate(
            RequestWithGroupId rateRequest, RequestWithGroupId requestWithGroupId) {
        // 原需量
        CompletableFuture<List<ValueVO>> originalFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(requestWithGroupId.getProjectId());
                            // 这里调用的是 视在计算方式 1.3.9
                            return demandDiagramService.getOriginalDemandByCalculateWithApparent(
                                    requestWithGroupId);
                        });

        // 有功的
        CompletableFuture<List<ValueVO>> realDemandAcActiveFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(requestWithGroupId.getProjectId());
                            return newDiagramService.getMeterRate(
                                    requestWithGroupId,
                                    MeterFieldEnum.AC_ACTIVE_POWER.field(),
                                    MeterTypeEnum.GRID.meterType().toString());
                        });
        // 无功
        CompletableFuture<List<ValueVO>> realDemandReActiveFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(requestWithGroupId.getProjectId());
                            return newDiagramService.getMeterRate(
                                    requestWithGroupId,
                                    MeterFieldEnum.AC_REACTIVE_POWER.field(),
                                    MeterTypeEnum.GRID.meterType().toString());
                        });
        CompletableFuture<List<ValueVO>> realControlFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(requestWithGroupId.getProjectId());
                            return demandDiagramService.getControlPowerRate(rateRequest);
                        });

        CompletableFuture<Void> allOf =
                CompletableFuture.allOf(
                        originalFuture,
                        realDemandAcActiveFuture,
                        realDemandReActiveFuture,
                        realControlFuture);
        allOf.join();
        // 前面全部执行完毕
        Map<String, List<ValueVO>> map = new HashMap<>(4);
        try {
            List<ValueVO> originalDemandList = originalFuture.get();
            List<ValueVO> realDemandAcActivePowerList = realDemandAcActiveFuture.get();
            List<ValueVO> realDemandReacActivePowerList = realDemandReActiveFuture.get();
            List<ValueVO> realDemandList =
                    DiagramCalUtils.getApparentPowerRate(
                            realDemandAcActivePowerList, realDemandReacActivePowerList);
            List<ValueVO> realControlList = realControlFuture.get();
            map.put("originalDemand", originalDemandList);
            map.put(
                    "realDemand",
                    setGridApparentDirect(realDemandAcActivePowerList, realDemandList));
            map.put("realControl", realControlList);
        } catch (Exception e) {
            log.error("getGirdPowerRate method error:{}", e.getMessage());
            e.printStackTrace();
        }
        return map;
    }

    List<ValueVO> setGridApparentDirect(
            List<ValueVO> acActivePowerList, List<ValueVO> realDemandList) {
        // 创建一个 map 来存储 acActivePowerList 中的时间和对应的 value
        Map<Long, Double> acActivePowerMap = new HashMap<>();
        for (ValueVO vo : acActivePowerList) {
            if (vo.getValue() < 0) {
                acActivePowerMap.put(vo.getTime(), vo.getValue());
            }
        }
        // 遍历 realDemandList 并取反对应时间的 value
        return realDemandList.stream()
                .map(
                        vo -> {
                            if (acActivePowerMap.containsKey(vo.getTime())) {
                                ValueVO valueVO = new ValueVO();
                                valueVO.setTime(vo.getTime());
                                valueVO.setValue(-vo.getValue());
                                return valueVO;
                            } else {
                                return vo;
                            }
                        })
                .collect(Collectors.toList());
    }

    @NotNull
    private Map<String, List<ValueVO>> getDemandRate(
            RequestWithGroupId rateRequest, RequestWithGroupId requestWithGroupId) {
        // 原需量
        CompletableFuture<List<ValueVO>> originalFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(requestWithGroupId.getProjectId());
                            return demandDiagramService.getOriginalDemandByCalculate(
                                    requestWithGroupId);
                        });

        CompletableFuture<List<ValueVO>> realDemandAcActiveFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(requestWithGroupId.getProjectId());
                            return newDiagramService.getMeterRate(
                                    requestWithGroupId,
                                    MeterFieldEnum.AC_ACTIVE_POWER.field(),
                                    MeterTypeEnum.GRID.meterType().toString());
                        });
        CompletableFuture<List<ValueVO>> realControlFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(requestWithGroupId.getProjectId());
                            return demandDiagramService.getControlPowerRate(rateRequest);
                        });

        CompletableFuture<Void> allOf =
                CompletableFuture.allOf(
                        originalFuture, realDemandAcActiveFuture, realControlFuture);
        allOf.join();
        // 前面全部执行完毕
        Map<String, List<ValueVO>> map = new HashMap<>(4);
        try {
            List<ValueVO> originalDemandList = originalFuture.get();
            List<ValueVO> realDemandAcActivePowerList = realDemandAcActiveFuture.get();
            List<ValueVO> realControlList = realControlFuture.get();
            map.put("originalDemand", originalDemandList);
            map.put("realDemand", realDemandAcActivePowerList);
            map.put("realControl", realControlList);
        } catch (Exception e) {
            log.error("getDemandRate method error:{}", e.getMessage());
        }
        return map;
    }
}
