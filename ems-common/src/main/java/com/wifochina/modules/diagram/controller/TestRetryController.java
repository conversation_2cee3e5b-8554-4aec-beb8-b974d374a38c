package com.wifochina.modules.diagram.controller;

import com.alibaba.fastjson.JSON;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.realtimemodel.common.WeihengEms100;
import com.wifochina.common.constants.EmailLogTypeEnum;
import com.wifochina.common.page.Result;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmailService;
import com.wifochina.modules.altertest.AlterManagerOpenApiFeign;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.demand.service.NewDemandServiceKt;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.diagram.service.RetryService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.initelectric.InitElectricService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.modules.strategytemplate.service.predicate.DefaultCustomModelStrategy;
import com.wifochina.realtimemodel.common.WeihengEms200;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.retry.RecoveryCallback;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "testjava")
@Slf4j
@AllArgsConstructor
public class TestRetryController {

    private final RetryService retryService;
    private final EmailService emailService;
    private final NewDemandServiceKt newDemandServiceKt;
    private final ProjectService projectService;
    private final GroupService groupService;
    private final InfluxClientService influxClientService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final DefaultCustomModelStrategy defaultCustomModelStrategy;

    //    private final AlterManagerOpenApiFeign alterManagerOpenApiFeign;

    private final InitElectricService initElectricService;

    private final DeviceService deviceService;
    @Autowired AlterManagerOpenApiFeign alterManagerOpenApiFeign;

    @Autowired RestTemplate restTemplate;
    @Autowired RedisTemplate<String, String> redisTemplate;

    @PostMapping("/testDemand")
    @ApiOperation("/testDemand")
    public Result<Map<Long, OaDemandData>> testDemand() {
        ProjectEntity projectEntity = projectService.getById("4f537620d37d40e19dd25be5ca6ad941");
        GroupEntity group = groupService.getById("eb93f1dad570466a88bd8da5482b08cb");
        Map<Long, OaDemandData> longOaDemandDataMap =
                newDemandServiceKt.platformDemandCal(
                        new RangeRequest(),
                        demandCalContext -> {
                            demandCalContext.setBucket(influxClientService.getBucketRealtime());
                            demandCalContext.setProject(projectEntity);
                            demandCalContext.setGroup(group);
                            return null;
                        });
        return Result.success(longOaDemandDataMap);
    }

    @PostMapping("testHello")
    @ApiOperation("testHello")
    public Result<String> testHello(@RequestBody List<String> emailList) {
        //        retryService.testRetry();
        String subject = String.format("%s实际需量超出控制需量", "手动测试邮件");
        String message =
                String.format(
                        "%s %s 在 %s 实际需量(%skW)超出当前控制需量(%skW),阈值为%s%%, 请注意查看",
                        "手动测试邮件",
                        "手动测试邮件",
                        LocalDateTime.ofEpochSecond(
                                Instant.now().getEpochSecond(),
                                0,
                                MyTimeUtil.getZoneOffsetFromZoneCode("Asia/Shanghai")),
                        String.format("%.2f", 100.12),
                        String.format("%.2f", 100.12),
                        String.format("%.2f", 100.12));

        emailService.sendMessage(
                new ArrayList<>(emailList),
                subject,
                message,
                () ->
                        // type = 1 需量超了 @see EmailLogTypeEnum
                        new EmailService.ServiceInfo()
                                .setProjectId("6eebf618a8cb408bb7a76e964a06f932")
                                .setGroupId("5f51566dbb8b4c56b96bb02a1c986d7a")
                                .setEmailLogTypeEnum(EmailLogTypeEnum.COUNTRY_OVER));
        return Result.success();
    }

    public static void main(String[] args) {

        String string =
                Flux.from("a")
                        .range(1L, 1L)
                        .window(1L, ChronoUnit.MINUTES, 15L, ChronoUnit.MINUTES)
                        .mean()
                        .map("({r with duration: int(v: r._stop) - int(v: r._start)})")
                        .filter(Restrictions.column("duration").equal(900000000000L))
                        //                        .filter(
                        //                                Restrictions.Restrictions.column(
                        //                                                "int(v: r._stop) - int(v:
                        // r._start)")
                        //                                        .custom(900000000000L, "=="))
                        .toString();
        System.out.println(string);
    }

    /** 用于测试的 retry 的 */
    @GetMapping("testE")
    @ApiOperation("testE")
    public void testE() {
        try {
            JSON.parseObject(
                    redisTemplate
                            .opsForValue()
                            .get(
                                    "realtime:device:4f537620d37d40e19dd25be5ca6ad941:5f206dfafef5452fa16dba6d2f1ec6ca"),
                    WeihengEms200.class);
            //            WeihengEms200 weihengEms200 =
            //                    restTemplate.getForObject(
            //
            // "http://localhost:8875/realtime/get/4f537620d37d40e19dd25be5ca6ad941/5f206dfafef5452fa16dba6d2f1ec6ca",
            //                            WeihengEms200.class);

            //            log.info("weiheng info : {}", weihengEms200.emsProtocolVersion(""));

        } catch (Exception e) {
            throw new NullPointerException("hello");
        }
    }

    /** 用于测试的 retry 的 */
    @GetMapping("testRetryTemplate")
    @ApiOperation("testRetryTemplate")
    public void testRetryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        // 模拟重启的时候从db里读取失败的记录 然后执行对应的方法和任务
        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(IllegalArgumentException.class, true);
        SimpleRetryPolicy simpleRetryPolicy = new SimpleRetryPolicy(3, retryableExceptions);
        retryTemplate.setRetryPolicy(simpleRetryPolicy);
        retryTemplate.execute(
                new RetryCallback<Object, IllegalArgumentException>() {

                    @Override
                    public Object doWithRetry(RetryContext context)
                            throws IllegalArgumentException {
                        context.setAttribute("name", "johnny");
                        retryService.testRetryTemplate();
                        return null;
                    }
                },
                new RecoveryCallback<Object>() {
                    @Override
                    public Object recover(RetryContext context) throws Exception {
                        Object name = (String) context.getAttribute("name");
                        log.info("recover test get attr from doWithRetry method  name:{} ", name);
                        return null;
                    }
                });
        threadPoolTaskExecutor.submit(
                () -> {
                    log.info("hello..mdc");
                });
    }
}
