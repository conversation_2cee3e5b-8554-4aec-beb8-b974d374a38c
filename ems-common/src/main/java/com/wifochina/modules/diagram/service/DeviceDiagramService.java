package com.wifochina.modules.diagram.service;

import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndPcs;
import com.wifochina.modules.diagram.request.RequestWithGroupId;

import java.util.List;
import java.util.Map;

/**
 * Created on 2024/7/29 10:06. 只限 device相关的 图表 service
 *
 * <AUTHOR>
 */
public interface DeviceDiagramService {

    /**
     * 查询 设备的 曲线 返回的是根据 deivceId 作为key 的 每个设备的 每个属性的 List<ValueVo></ValueVo>
     *
     * @param requestWithDeviceId : requestWithDeviceId
     * @param bucket : @see forever or mean
     * @param fields : fields
     * @return : Map<String, Map<String, List<ValueVO>>> Map的第一个key 是deviceId, 里面的Map 的key 是field 属性
     */
    Map<String, Map<String, List<ValueVO>>> getDeviceRate(
            RequestWithDeviceId requestWithDeviceId, String bucket, List<String> fields);

    /**
     * 查询 设备的 曲线 返回的是多个设备聚合 后的 多个设备的 每个属性的 sum List<ValueVo></ValueVo>
     *
     * @param requestWithDeviceId : requestWithDeviceId
     * @param bucket : @see forever or mean
     * @param fields : fields
     * @return : Map<String, List<ValueVO>> Map 的key 是field 属性
     */
    Map<String, List<ValueVO>> getDeviceAggregationRate(
            RequestWithDeviceId requestWithDeviceId, String bucket, List<String> fields);

    Map<String, List<ValueVO>> getGroupDeviceAggregationRate(
            RequestWithGroupId requestWithGroupId, String bucket, List<String> fields);

    Map<String, List<ValueVO>> getPcsRate(RequestWithDeviceIdAndPcs requestWithDeviceIdAndPcs);


}
