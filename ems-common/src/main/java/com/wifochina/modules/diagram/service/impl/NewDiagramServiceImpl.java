package com.wifochina.modules.diagram.service.impl;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.FilterFlux;
import com.influxdb.query.dsl.functions.GroupFlux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.influx.FluxAdapter;
import com.wifochina.common.influx.FluxBuilder;
import com.wifochina.common.onoff.OnOffComponent;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.ApplicationHolder;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.common.search.PeriodUtils;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndPcs;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.AbstractDiagramExtendService;
import com.wifochina.modules.diagram.service.NewDiagramService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.ControllableService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * maxTable = from(bucket:"ems_forever_dev") |> range(start:1722182400, stop:1722268799) |>
 * filter(fn: (r) => r["_measurement"] == "ems100@28689") |> filter(fn: (r) => r["projectId"] ==
 * "4f537620d37d40e19dd25be5ca6ad941") |> filter(fn: (r) => (r["deviceId"] ==
 * "5f206dfafef5452fa16dba6d2f1ec6ca")) |> filter(fn: (r) => (r["_field"] ==
 * "bms_cell_high_voltage")) |> aggregateWindow(every:5m, fn:last) |> pivot(rowKey:["_time"],
 * columnKey:["_field"], valueColumn:"_value") |> group(columns:["_time"]) |>
 * max(column:"bms_cell_high_voltage")
 */

/**
 * Created on 2024/7/29 10:07.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class NewDiagramServiceImpl extends AbstractDiagramExtendService
        implements NewDiagramService {

    private final OnOffComponent onOffComponent;

    private static final Set<String> NOT_TRANSFORM_FIELDS =
            Set.of(
                    MeterFieldEnum.AC_VOLTAGE.field(),
                    MeterFieldEnum.AC_VOLTAGES_0.field(),
                    MeterFieldEnum.AC_VOLTAGES_1.field(),
                    MeterFieldEnum.AC_VOLTAGES_2.field(),
                    MeterFieldEnum.FREQUENCY.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field(),
                    MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                    MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_1.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_2.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_3.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_4.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_5.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_1.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_2.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_3.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_4.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_5.field(),
                    MeterFieldEnum.MAX_NEGATIVE_ACTIVE_POWER_DEMAND.field(),
                    MeterFieldEnum.AC_MAX_NEGATIVE_ACTIVE_POWER.field());

    private final InfluxClientService influxClient;
    private final DeviceService deviceService;
    private final AmmeterService ammeterService;
    private final ControllableService controllableService;
    private final GroupService groupService;
    private static final int DEFAULT_MAP_SIZE = 16;

    /**
     * 设备的 曲线 不聚合 key 是每个设备的deviceId
     *
     * @param requestWithDeviceId : requestWithDeviceId
     * @param bucket : @see forever or mean
     * @param fields : fields
     * @return : Map<String, Map<String, List<ValueVO>>>
     */
    @Override
    public Map<String, Map<String, List<ValueVO>>> getDeviceRate(
            RequestWithDeviceId requestWithDeviceId, String bucket, List<String> fields) {
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithDeviceId, holder);
        fillPeriodTemp(requestWithDeviceId, holder);
        return EquipmentTimeSeriesUtils.rateQueryEngine
                .getRateMap(
                        bucket,
                        influxClient.getEmsTable(WebUtils.projectId.get()),
                        holder,
                        fields,
                        () ->
                                deviceService.deviceIdsPrepare(
                                        WebUtils.projectId.get(),
                                        requestWithDeviceId.getDeviceId()))
                .getMap();
    }

    /**
     * 设备的 曲线 聚合
     *
     * @param requestWithDeviceId : requestWithDeviceId
     * @param bucket : @see forever or mean
     * @param fields : fields
     * @return : Map<String, List<ValueVO>> 聚合后的 只有属性对应的数据
     */
    @Override
    public Map<String, List<ValueVO>> getDeviceAggregationRate(
            RequestWithDeviceId requestWithDeviceId, String bucket, List<String> fields) {
        // 和上面的区别就是 这个是多个设备 聚合后的 对应的属性
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithDeviceId, holder);
        fillPeriodTemp(requestWithDeviceId, holder);
        return EquipmentTimeSeriesUtils.rateQueryEngine
                .getRateAggregationMap(
                        bucket,
                        influxClient.getEmsTable(WebUtils.projectId.get()),
                        holder,
                        fields,
                        () ->
                                deviceService.deviceIdsPrepare(
                                        WebUtils.projectId.get(),
                                        requestWithDeviceId.getDeviceId()))
                .getMap();
    }

    private void fillPeriodTemp(
            RequestWithDeviceId requestWithDeviceId, FluxRateCommonHolder holder) {
        if (requestWithDeviceId.getPeriod() != null) {
            holder.setPeriod(Long.valueOf(requestWithDeviceId.getPeriod()));
        }
    }

    /**
     * 支持分组的 设备的 曲线 聚合 和上面的方法类似 只是deviceIds 不同
     *
     * @param requestWithGroupId : requestWithGroupId
     * @param bucket : bucket
     * @param fields : fields
     * @return : Map<String, List<ValueVO>>
     */
    @Override
    public Map<String, List<ValueVO>> getGroupDeviceAggregationRate(
            RequestWithGroupId requestWithGroupId, String bucket, List<String> fields) {
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithGroupId, holder);
        getPeriodTempForGroup(requestWithGroupId, holder);
        return EquipmentTimeSeriesUtils.rateQueryEngine
                .getRateAggregationMap(
                        bucket,
                        //                        influxClient.getBucketMean(),
                        influxClient.getEmsTable(WebUtils.projectId.get()),
                        holder,
                        fields,
                        () -> deviceService.getGroupDeviceList(requestWithGroupId))
                .getMap();
    }

    @Override
    public Map<String, List<ValueVO>> getPcsRate(
            RequestWithDeviceIdAndPcs requestWithDeviceIdAndPcs) {
        String activeFiled =
                "pcs_{i}_active_power".replace("{i}", requestWithDeviceIdAndPcs.getIndex());
        String reactiveFiled =
                "pcs_{i}_reactive_power".replace("{i}", requestWithDeviceIdAndPcs.getIndex());
        // 如果查询的 index 是 all 表示不查询详细的某个 pcs 则按照 整体的属性
        if (EmsConstants.ALL.equals(requestWithDeviceIdAndPcs.getIndex())) {
            activeFiled = EmsFieldEnum.EMS_AC_ACTIVE_POWER.field();
            reactiveFiled = EmsFieldEnum.EMS_AC_REACTIVE_POWER.field();
        }
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithDeviceIdAndPcs, holder);
        // BeanUtils copy integer 和 long 不会copy 先设置一下 后续替换成Mapstruc
        if (requestWithDeviceIdAndPcs.getPeriod() != null) {
            holder.setPeriod(Long.valueOf(requestWithDeviceIdAndPcs.getPeriod()));
        }
        List<ValueVO> activeList = List.of();
        List<ValueVO> reactiveList = List.of();
        if (ApplicationHolder.isCloudDeployType()) {

            // 原版本需要特殊处理一下
            if (!EmsConstants.ALL.equals(requestWithDeviceIdAndPcs.getIndex())) {
                activeFiled = "pcs_{i}_active_power";
                reactiveFiled = "pcs_{i}_reactive_power";
            }
            List<String> fields = List.of(activeFiled, reactiveFiled);

            String deviceIndex = requestWithDeviceIdAndPcs.getIndex();
            long period =
                    PeriodUtils.period(
                            requestWithDeviceIdAndPcs.getStartDate(),
                            requestWithDeviceIdAndPcs.getEndDate(),
                            requestWithDeviceIdAndPcs.getPeriod() == null
                                    ? null
                                    : Long.valueOf(requestWithDeviceIdAndPcs.getPeriod()),
                            influxClient.getBucketForever());
            for (String field : fields) {
                // 查询 pcs status 的sql 但是后续要以这个为主 , 并且要把这个refactor 一下
                String string =
                        getPcsStatusSqlForCloud(
                                requestWithDeviceIdAndPcs, field, deviceIndex, period);
                // forever 库 不需要drop 第一行 并且时间偏移 不需要2倍
                Map<String, List<ValueVO>> map =
                        FluxAdapter.query(string)
                                .decimal(2)
                                .timeOffset(-period * 60 * 2)
                                .handleResult()
                                .toMap();
                if (field.equals(activeFiled)) {
                    activeList = map.containsKey(activeFiled) ? map.get(activeFiled) : List.of();
                } else {
                    reactiveList =
                            map.containsKey(reactiveFiled) ? map.get(reactiveFiled) : List.of();
                }
            }
        } else {
            // 这里是支持的 场站的方式的 查询
            activeList =
                    EquipmentTimeSeriesUtils.rateQueryEngine
                            .getRateAggregationList(
                                    influxClient.getBucketForever(),
                                    influxClient.getEmsTable(WebUtils.projectId.get()),
                                    holder,
                                    activeFiled,
                                    () ->
                                            deviceService.deviceIdsPrepare(
                                                    WebUtils.projectId.get(),
                                                    requestWithDeviceIdAndPcs.getDeviceId()))
                            .getList();
            reactiveList =
                    EquipmentTimeSeriesUtils.rateQueryEngine
                            .getRateAggregationList(
                                    influxClient.getBucketForever(),
                                    influxClient.getEmsTable(WebUtils.projectId.get()),
                                    holder,
                                    reactiveFiled,
                                    () ->
                                            deviceService.deviceIdsPrepare(
                                                    WebUtils.projectId.get(),
                                                    requestWithDeviceIdAndPcs.getDeviceId()))
                            .getList();
        }
        Map<String, List<ValueVO>> rateMap = new HashMap<>(2);
        rateMap.put("active", activeList);
        rateMap.put("reactive", reactiveList);
        return rateMap;
    }

    private @NotNull String getPcsStatusSqlForCloud(
            RequestWithDeviceIdAndPcs requestWithDeviceIdAndPcs,
            String field,
            String deviceIndex,
            long period) {
        FluxBuilder.InfluxQueryFilterStage fields =
                FluxAdapter.builder()
                        .projectId(WebUtils.projectId.get())
                        .from(influxClient.getBucketForever())
                        .range(
                                requestWithDeviceIdAndPcs.getStartDate(),
                                requestWithDeviceIdAndPcs.getEndDate() + 1)
                        .measurement(influxClient.getEmsTable(WebUtils.projectId.get()))
                        .equipmentIds(
                                deviceService.deviceIdsPrepare(
                                        WebUtils.projectId.get(),
                                        requestWithDeviceIdAndPcs.getDeviceId()))
                        .fields(List.of(field));
        if (!deviceIndex.equals(EmsConstants.ALL)) {
            // 新增的一个
            fields = fields.deviceIndex(deviceIndex);
        }
        return fields.toFlux()
                .aggregateWindow(period, ChronoUnit.MINUTES, EmsConstants.INFLUX_LAST_FUNC)
                .withCreateEmpty(false)
                .groupBy(List.of(EmsConstants.INFLUX_TIME, EmsConstants.INFLUX_FIELD))
                .sum()
                .toString();
    }

    /**
     * 电表相关的 rate 查询方法 比较通用
     *
     * @param requestWithGroupId : requestWithGroupId
     * @param field : field
     * @param type : 电表类型
     * @return : List<ValueVo>
     */
    @Override
    public List<ValueVO> getMeterRate(
            RequestWithGroupId requestWithGroupId, String field, String type) {
        // 相差的天数
        // 根据组查询到电表id
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithGroupId, holder);
        getPeriodTempForGroup(requestWithGroupId, holder);
        String bucket = influxClient.getBucketMean();
        if (NOT_TRANSFORM_FIELDS.contains(field)) {
            bucket = influxClient.getBucketForever();
        }
        return EquipmentTimeSeriesUtils.rateQueryEngine
                .getRateAggregationList(
                        bucket,
                        influxClient.getMeterTable(WebUtils.projectId.get()),
                        holder,
                        field,
                        () -> ammeterService.meterIdsPrepare(requestWithGroupId, type))
                .getList()
                .stream()
                // mean的bucket 电表的数据是w , 前端都需要 kw
                .peek(
                        valueVO -> {
                            // 除了这些
                            if (!NOT_TRANSFORM_FIELDS.contains(field)) {
                                // 查询的是 功率 需要 / 1000
                                valueVO.setValue(valueVO.getValue() / EmsConstants.CONSTANT_1000);
                                if (MeterTypeEnum.GRID.meterType().toString().equals(type)) {
                                    // 如果是 并网电表 且查询的是 功率的 需要 取反
                                    valueVO.setValue(-valueVO.getValue());
                                }
                                // 充电桩也需要 翻转
                                if (MeterTypeEnum.PILE.meterType().toString().equals(type)) {
                                    // 如果是 并网电表 且查询的是 功率的 需要 取反
                                    valueVO.setValue(-valueVO.getValue());
                                }
                            }
                            // 如果查询的是 非功率的 不需要特殊处理
                        })
                .collect(Collectors.toList());
    }

    @Override
    public List<ValueVO> getDemandMeterRate(
            RequestWithGroupId requestWithGroupId, String projectId) {
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithGroupId, holder);
        String bucket = influxClient.getBucketForever();
        return EquipmentTimeSeriesUtils.rateQueryEngine
                .getRateAggregationList(
                        bucket,
                        influxClient.getMeterTable(WebUtils.projectId.get()),
                        holder,
                        MeterFieldEnum.MAX_NEGATIVE_ACTIVE_POWER_DEMAND.field(),
                        () ->
                                ammeterService.findDemandMeterIdsByGroupIdAndItemId(
                                        projectId,
                                        requestWithGroupId.getGroupId(),
                                        requestWithGroupId.getItemId()))
                .getList();
    }

    private void getPeriodTempForGroup(
            RequestWithGroupId requestWithGroupId, FluxRateCommonHolder holder) {
        if (requestWithGroupId.getPeriod() != null) {
            holder.setPeriod(Long.valueOf(requestWithGroupId.getPeriod()));
        }
    }

    @Override
    public Map<String, List<ValueVO>> getLoadRate(RequestWithGroupId requestWithGroupId) {
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(WebUtils.projectId.get());
        String activeField = MeterFieldEnum.AC_ACTIVE_POWER.field();
        String reactiveField = MeterFieldEnum.AC_REACTIVE_POWER.field();
        String maxActiveField = MeterFieldEnum.MAX_AC_ACTIVE_POWER.field();
        String maxReactiveField = MeterFieldEnum.MAX_AC_REACTIVE_POWER.field();
        List<ValueVO> maxActiveValues = new ArrayList<>();
        List<ValueVO> maxReactiveValues = new ArrayList<>();
        ValueVO maxActiveValueVo = new ValueVO().setValue(0.0);
        List<ValueVO> activeList = new ArrayList<>();
        List<ValueVO> reactiveList = new ArrayList<>();
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableLoadGrid())) {
            // 直接从 负载电表Load 中读取
            activeList =
                    this.getMeterRate(
                            requestWithGroupId,
                            activeField,
                            MeterTypeEnum.LOAD.meterType().toString());
            activeList.stream()
                    .max(Comparator.comparing(ValueVO::getValue))
                    .ifPresent(maxActiveValues::add);
            reactiveList =
                    this.getMeterRate(
                            requestWithGroupId,
                            reactiveField,
                            MeterTypeEnum.LOAD.meterType().toString());
            reactiveList.stream()
                    .max(Comparator.comparing(ValueVO::getValue))
                    .ifPresent(maxReactiveValues::add);
        } else {
            // 没有负载电表 需要通过其他电表等计算
            List<String> meterTypes =
                    List.of(
                            MeterTypeEnum.PV.meterType().toString(),
                            MeterTypeEnum.GRID.meterType().toString(),
                            MeterTypeEnum.WIND.meterType().toString(),
                            MeterTypeEnum.DIESEL.meterType().toString(),
                            MeterTypeEnum.PILE.meterType().toString(),
                            MeterTypeEnum.GAS.meterType().toString(),
                            MeterTypeEnum.WASTER.meterType().toString(),
                            EmsConstants.EMS);
            Map<String, Map<Long, Double>> mapCollect = new HashMap<>();
            for (String meterType : meterTypes) {
                Map<Long, Double> map;
                List<ValueVO> voList = new ArrayList<>();
                boolean enableFlag = onOffComponent.checkMeterEnable(systemGroupEntity, meterType);
                // 把这个方法用 上面这个替代 封装到 开关组件里面
                //                boolean enableFlag = checkEnable(systemGroupEntity, meterType);
                if (meterType.equals(EmsConstants.EMS)) {
                    // EMS 特殊处理 走这个方法 其他的都是电表 走 getMeterRate方法
                    if (enableFlag) {
                        String field = EmsFieldEnum.EMS_AC_ACTIVE_POWER.field();
                        voList =
                                getGroupDeviceAggregationRate(
                                                requestWithGroupId,
                                                influxClient.getBucketMean(),
                                                List.of(field))
                                        .get(field);
                    }
                } else {
                    if (enableFlag) {
                        voList = this.getMeterRate(requestWithGroupId, activeField, meterType);
                    }
                }
                // fix bug for ems voList is null check
                if (voList == null || voList.isEmpty()) {
                    continue;
                }
                map =
                        voList.stream()
                                .collect(Collectors.toMap(ValueVO::getTime, ValueVO::getValue));
                mapCollect.put(meterType, map);
            }
            Map<Long, Double> keyMap = new HashMap<>(DEFAULT_MAP_SIZE);
            for (Map.Entry<String, Map<Long, Double>> entry : mapCollect.entrySet()) {
                Map<Long, Double> value = entry.getValue();
                if (value != null && !value.isEmpty()) {
                    keyMap = value;
                    break;
                }
            }
            for (Long time : keyMap.keySet()) {
                // 计算负载
                double timeLoad = getLoadList(time, mapCollect, activeList);
                if (maxActiveValueVo.getValue() < timeLoad) {
                    maxActiveValueVo.setValue(timeLoad);
                    maxActiveValueVo.setTime(time);
                }
            }
            // 这里只有 active的 计算得不到 reactive的
            maxActiveValues.add(maxActiveValueVo);
        }
        return Map.of(
                activeField,
                activeList.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()),
                reactiveField,
                reactiveList.stream()
                        .sorted(Comparator.comparing(ValueVO::getTime))
                        .collect(Collectors.toList()),
                maxActiveField,
                maxActiveValues,
                maxReactiveField,
                maxReactiveValues);
    }

    private static double getLoadList(
            Long time, Map<String, Map<Long, Double>> mapCollect, List<ValueVO> activeList) {
        double totalPower = 0.0;
        for (Map.Entry<String, Map<Long, Double>> entry : mapCollect.entrySet()) {
            String meterType = entry.getKey();
            Map<Long, Double> map = entry.getValue();
            // 前面已经判断过了
            totalPower += map.get(time) != null ? map.get(time) : 0.0;
        }
        ValueVO valueVO = new ValueVO();
        valueVO.setTime(time);
        valueVO.setValue(totalPower);
        activeList.add(valueVO);
        return totalPower;
    }

    @Deprecated
    private static boolean checkEnable(GroupEntity systemGroupEntity, String meterType) {
        if (MeterTypeEnum.PV.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController());
        }
        if (MeterTypeEnum.GRID.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableElectricGrid());
        }

        if (MeterTypeEnum.WIND.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration());
        }
        if (MeterTypeEnum.DIESEL.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableWoodPowerGeneration());
        }
        if (MeterTypeEnum.PILE.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableChargingPilePower());
        }

        if (MeterTypeEnum.GAS.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableGasPowerGeneration());
        }
        if (EmsConstants.EMS.equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableEms());
        }
        if (MeterTypeEnum.WASTER.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration());
        }
        return false;
    }

    // TODO refactor 把这个 abstract extend service 去掉..
    @Override
    protected Map<Long, Double> getSocDiffMap(long start, long end) {
        Map<Long, Double> socMap = new HashMap<>(30);
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        String queryString =
                "import \"timezone\"\n"
                        + "option location = timezone.fixed(offset: "
                        + MyTimeUtil.getOffsetSecondsFromZoneCode(projectEntity.getTimezone())
                        + "s)\n"
                        + "\n"
                        + "from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getEmsTable(projectId)
                        + "\")\n"
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"ems_soc\")\n"
                        + "  |> filter(fn: (r) => r[\"projectId\"] == \""
                        + projectId
                        + "\")\n"
                        + "  |> aggregateWindow(every: 1d, fn: first, createEmpty: false)\n"
                        + "  |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "  |> mean(column: \"_value\")";
        queryString =
                queryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (int i = tables.size() - 1; i >= 1; i--) {
            FluxTable fluxTable1 = tables.get(i);
            List<FluxRecord> record1 = fluxTable1.getRecords();
            Instant time1 = (Instant) record1.get(0).getValueByKey("_time");
            Double value1 = (Double) record1.get(0).getValueByKey("_value");
            FluxTable fluxTable2 = tables.get(i - 1);
            List<FluxRecord> record2 = fluxTable2.getRecords();
            Instant time2 = (Instant) record2.get(0).getValueByKey("_time");
            Double value2 = (Double) record2.get(0).getValueByKey("_value");
            assert time2 != null;
            assert time1 != null;
            if ((time1.getEpochSecond() - time2.getEpochSecond()) > MyTimeUtil.ONE_DAY_SECONDS) {
                continue;
            }
            if (ObjectUtils.isEmpty(value1) || ObjectUtils.isEmpty(value2)) {
                continue;
            }
            socMap.put(
                    time2.getEpochSecond() - MyTimeUtil.ONE_DAY_SECONDS, (value1 - value2) / 100);
        }
        return socMap;
    }

    /** only for lindorm */
    // TODO refactor 暂时先不动 去掉..
    @Override
    public Map<String, Map<String, List<ValueVO>>> getBatteryRate(
            RequestWithDeviceId requestWithDeviceId,
            List<String> fields,
            int cluster,
            Integer stack,
            int cell,
            int cellT) {
        // 相差的天数
        long periodTime =
                (requestWithDeviceId.getEndDate() - requestWithDeviceId.getStartDate()) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = (periodTime / 2 + 1) * 5;
        if (requestWithDeviceId.getPeriod() != null) {
            period = requestWithDeviceId.getPeriod();
        }
        Map<String, List<ValueVO>> dataMap = new HashMap<>();
        FilterFlux filter =
                Flux.from(influxClient.getBucketForever())
                        .range(requestWithDeviceId.getStartDate(), requestWithDeviceId.getEndDate())
                        .filter(
                                Restrictions.measurement()
                                        .equal(influxClient.getCellTable(WebUtils.projectId.get())))
                        .filter(Restrictions.tag("projectId").equal(WebUtils.projectId.get()))
                        .filter(
                                Restrictions.tag(influxClient.getDeviceKey())
                                        .equal(requestWithDeviceId.getDeviceId()))
                        .filter(Restrictions.tag("cellCluster").equal(String.valueOf(cluster)));

        Flux flux;
        if (stack != null) {
            filter = filter.filter(Restrictions.tag("cellStack").equal(String.valueOf(stack)));
        }
        flux = filter.aggregateWindow(period, ChronoUnit.MINUTES, "last").withCreateEmpty(false);
        String queryString = flux.toString();
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable fluxTable : tables) {
            for (FluxRecord record : fluxTable.getRecords()) {
                String field = (String) record.getValueByKey("_field");
                Double value = (Double) record.getValueByKey("_value");
                Instant time = (Instant) record.getValueByKey("_time");
                String cellStack = (String) record.getValueByKey("cellStack");
                // 1.4.2 支持一下 不传 stack 的情况
                if (stack == null) {
                    stack = Integer.valueOf(cellStack);
                }
                assert time != null;
                // 这里 * 2 不清楚 因为forever 正常只需要 一倍的
                ValueVO valueVO = new ValueVO(time.getEpochSecond() - (period * 60) * 2, value);
                String index = (String) record.getValueByKey("cellIndex");
                assert index != null;
                assert field != null;
                if (field.contains("bms_cell_voltage")) {
                    String id = "bms_cell_voltage_" + cluster + "_" + stack + "_" + index;
                    dataMap.computeIfAbsent(id, k -> new ArrayList<>()).add(valueVO);
                } else if (field.contains("bms_cell_temperature")) {
                    String id = "bms_cell_temperature_" + cluster + "_" + stack + "_" + index;
                    dataMap.computeIfAbsent(id, k -> new ArrayList<>()).add(valueVO);
                }
            }
        }
        Map<String, Map<String, List<ValueVO>>> map = new HashMap<>();
        map.put(requestWithDeviceId.getDeviceId(), dataMap);
        return map;
    }

    @Override
    public Map<String, Map<String, List<ValueVO>>> getBatteryRateWhoStation(
            RequestWithDeviceId requestWithDeviceId, List<String> fields) {
        // 这里控制一下 这个整站太小会查询不出来
        long period = 10;
        if (requestWithDeviceId.getPeriod() != null) {
            period = requestWithDeviceId.getPeriod();
        }
        Flux flux =
                Flux.from(influxClient.getBucketForever())
                        .range(requestWithDeviceId.getStartDate(), requestWithDeviceId.getEndDate())
                        .filter(
                                Restrictions.measurement()
                                        .equal(
                                                influxClient.getCellTable(
                                                        requestWithDeviceId.getProjectId())))
                        .filter(
                                Restrictions.tag("projectId")
                                        .equal(requestWithDeviceId.getProjectId()))
                        .aggregateWindow(period, ChronoUnit.MINUTES, "last")
                        .withCreateEmpty(false);
        String queryString = flux.toString();
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        Map<String, Map<String, List<ValueVO>>> map = new HashMap<>();
        Map<String, List<ValueVO>> dataMap = new HashMap<>();
        for (FluxTable fluxTable : tables) {
            for (FluxRecord record : fluxTable.getRecords()) {
                String field = (String) record.getValueByKey("_field");
                Double value = (Double) record.getValueByKey("_value");
                Instant time = (Instant) record.getValueByKey("_time");
                String deviceId = (String) record.getValueByKey("deviceId");
                assert time != null;
                // 这里 * 2 不清楚 因为forever 正常只需要 一倍的
                ValueVO valueVO = new ValueVO(time.getEpochSecond() - (period * 60) * 2, value);
                String index = (String) record.getValueByKey("cellIndex");
                String cellStack = (String) record.getValueByKey("cellStack");
                String cellCluster = (String) record.getValueByKey("cellCluster");
                assert index != null;
                assert field != null;
                if (field.contains("bms_cell_voltage")) {
                    String id = "bms_cell_voltage_" + cellCluster + "_" + cellStack + "_" + index;
                    dataMap.computeIfAbsent(id, k -> new ArrayList<>()).add(valueVO);
                } else if (field.contains("bms_cell_temperature")) {
                    String id =
                            "bms_cell_temperature_" + cellCluster + "_" + cellStack + "_" + index;
                    dataMap.computeIfAbsent(id, k -> new ArrayList<>()).add(valueVO);
                }
                map.put(deviceId, dataMap);
            }
        }
        return map;
    }

    @Override
    public List<ValueVO> getBatteryDiffRate(
            RequestWithGroupId requestWithGroupId, List<String> fields) {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 注意这里的 顺序有要求
        String maxColumn = fields.get(0);
        String minColumn = fields.get(1);
        long period =
                PeriodUtils.period(
                        requestWithGroupId.getStartDate(),
                        requestWithGroupId.getEndDate(),
                        requestWithGroupId.getPeriod(),
                        influxClient.getBucketForever());
        // 这个是需要的 不清楚 历史遗留问题
        //        requestWithGroupId.setItemId(EmsConstants.ALL);
        Map<String, Flux> fieldTableMap = new HashMap<>(2);

        // 构建 2个 table 每个 table 生成类似如下注释
        // maxTable = from(bucket:"ems_forever_dev")
        // |> range(start:1722182400, stop:1722268799)
        // |> filter(fn: (r) => r["_measurement"] == "ems100@28689")
        // |> filter(fn: (r) => r["projectId"] == "4f537620d37d40e19dd25be5ca6ad941")
        // |> filter(fn: (r) => (r["deviceId"] == "5f206dfafef5452fa16dba6d2f1ec6ca"))
        // |> filter(fn: (r) => (r["_field"] == "bms_cell_high_voltage"))
        // |> aggregateWindow(every:5m, fn:last)
        // |> pivot(rowKey:["_time"], columnKey:["_field"], valueColumn:"_value")
        // |> group(columns:["_time"])
        // |> max(column:"bms_cell_high_voltage")
        for (String field : List.of(maxColumn, minColumn)) {
            GroupFlux groupFlux =
                    FluxAdapter.builder()
                            .project(projectEntity)
                            .timeZoneSet(false)
                            .from(influxClient.getBucketForever())
                            .range(
                                    requestWithGroupId.getStartDate(),
                                    requestWithGroupId.getEndDate())
                            .measurement(influxClient.getEmsTable(projectId))
                            .equipmentIds(deviceService.getGroupDeviceList(requestWithGroupId))
                            .fields(List.of(field))
                            .toFlux()
                            .aggregateWindow(
                                    period, ChronoUnit.MINUTES, EmsConstants.INFLUX_LAST_FUNC)
                            .pivot(
                                    List.of(EmsConstants.INFLUX_TIME),
                                    List.of(EmsConstants.INFLUX_FIELD),
                                    EmsConstants.INFLUX_VALUE)
                            .groupBy(EmsConstants.INFLUX_TIME);
            Flux flux;
            if (field.equals(maxColumn)) {
                flux = groupFlux.max(maxColumn);
            } else {
                flux = groupFlux.min(minColumn);
            }
            fieldTableMap.put(field, flux);
        }
        // 最前面添加 timezone相关 , 这个Flux 工具 只有 from 后才有withLocation方法 所以没办法
        String timeZoneSql =
                " import \"timezone\"\n"
                        + "option location = timezone.fixed(offset: "
                        + MyTimeUtil.getOffsetSecondsFromZoneCode(projectEntity.getTimezone())
                        + "s) \n";
        Flux max = fieldTableMap.get(maxColumn);
        Flux min = fieldTableMap.get(minColumn);
        String mapFuncString = "({ r with difference: r." + maxColumn + " - r." + minColumn + " })";
        String queryString =
                timeZoneSql
                        + Flux.join()
                                .withTable("maxTable", max)
                                .withTable("minTable", min)
                                .withOn(EmsConstants.INFLUX_TIME)
                                .map(mapFuncString)
                                .keep(List.of(EmsConstants.INFLUX_TIME, "difference"));
        log.debug(queryString);
        return FluxAdapter.query(queryString)
                .valueKey("difference")
                // 和线上的lindorm 对上的话要 -2分钟
                .timeOffset(-period * ChronoUnit.MINUTES.getDuration().getSeconds() * 2)
                .handleResult()
                .toListFilterNullValue();
    }

    @Override
    public Map<String, Map<String, List<ValueVO>>> getControllableRate(
            RequestWithDeviceId requestWithDeviceId, String bucket, List<String> fields) {
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        BeanUtils.copyProperties(requestWithDeviceId, holder);
        fillPeriodTemp(requestWithDeviceId, holder);
        return EquipmentTimeSeriesUtils.rateQueryEngine
                .getRateMap(
                        bucket,
                        influxClient.getControllableTable(WebUtils.projectId.get()),
                        holder,
                        fields,
                        () -> List.of(requestWithDeviceId.getDeviceId()))
                .getMap();
    }
}
