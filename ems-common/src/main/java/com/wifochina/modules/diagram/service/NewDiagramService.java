package com.wifochina.modules.diagram.service;

import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithGroupId;

import java.util.List;
import java.util.Map;

/**
 * Created on 2024/7/29 10:06. 只限 device相关的 图表 service
 *
 * <AUTHOR>
 */
public interface NewDiagramService
        extends DeviceDiagramService, MeterDiagramService, ControllableDiagramService {

    /**
     * 这个方法 涉及 meter和ems 没有放在 对应的 service里
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : Map<String, List<ValueVO>>
     */
    Map<String, List<ValueVO>> getLoadRate(RequestWithGroupId requestWithGroupId);

    Map<String, Map<String, List<ValueVO>>> getBatteryRate(
            RequestWithDeviceId requestWithDeviceId,
            List<String> fields,
            int cluster,
            Integer stack,
            int cell,
            int cellT);

    Map<String, Map<String, List<ValueVO>>> getBatteryRateWhoStation(
            RequestWithDeviceId requestWithDeviceId, List<String> fields);

    List<ValueVO> getBatteryDiffRate(RequestWithGroupId requestWithGroupId, List<String> fields);

    List<ValueVO> getDemandMeterRate(RequestWithGroupId requestWithGroupId, String projectId);
}
