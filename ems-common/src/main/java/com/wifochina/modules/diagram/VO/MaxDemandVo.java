package com.wifochina.modules.diagram.VO;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2025/8/11 10:30.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MaxDemandVo {

    private MonthMaxDemandVo monthMaxDemandVo;
    private TimeSharingMaxDemandVo timeSharingMaxDemandVo;
    private Boolean timeSharingFlag;

    @Accessors(chain = true)
    @Data
    public static class TimeSharingMaxDemandVo {
        private ValueVO valueVO;
        private Long timeSharingDemandId;
        private String timeSharingDemandName;
    }

    @Accessors(chain = true)
    @Data
    public static class MonthMaxDemandVo {
        private ValueVO valueVO;
    }
}
