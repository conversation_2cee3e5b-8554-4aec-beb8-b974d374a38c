package com.wifochina.modules.diagram.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BatteryVO
 * 
 * @date 4/24/2022 9:16 AM
 * <AUTHOR>
 * @version 1.0
 */

@ApiModel(value = "电池芯信息")
@Data
public class BatteryVO {

    @ApiModelProperty(value = "device id")
    private String deviceId;

    @ApiModelProperty(value = "device name")
    private String deviceName;

    @ApiModelProperty(value = "PCS数量(PCS_COUNT),序号从0开始)")
    private Integer pcs_count;

    @ApiModelProperty(value = "BMS数量(BMS_COUNT),序号从0开始)")
    private Integer bms_count;

    @ApiModelProperty(value = " 电池簇数量(CLUSTER <= 50,序号从0开始)")
    private Integer bms_cluster_count;

    @ApiModelProperty(value = " 每簇电池组数量(STACK <= 30),序号从0开始")
    private Integer bms_stack_per_cluster_count;
}
