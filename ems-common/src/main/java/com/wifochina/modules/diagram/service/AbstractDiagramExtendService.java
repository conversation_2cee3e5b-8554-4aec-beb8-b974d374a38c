package com.wifochina.modules.diagram.service;

import cn.hutool.core.collection.CollUtil;
import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.ParseCapacity;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.electric.ElectricAdapterChooser;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.util.OperationUtil;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.modules.report.entity.DayReportEntity;
import com.wifochina.modules.report.service.DayReportService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * Created on 2023/11/15 13:50.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public abstract class AbstractDiagramExtendService implements DiagramExtendService {

    @Resource public ProjectService projectService;
    @Resource public GroupService groupService;
    @Resource public DeviceService deviceService;
    @Resource public AmmeterService ammeterService;
    @Resource public ProjectExtService projectExtService;
    @Resource public DayReportService dayReportService;

    @Value("${efficiency.high}")
    private String efficiencyHigh;

    @Value("${efficiency.low}")
    private String efficiencyLow;

    @Resource private ElectricAdapterChooser electricAdapterChooser;

    @Override
    public Map<Long, Double> getEfficiencyRate(RequestWithGroupId requestWithGroupId) {
        // 把时间段拆分成每天的0点
        long start = requestWithGroupId.getStartDate();
        // 把时间变成当0点
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        start = MyTimeUtil.getOneDayZeroTime(start, projectEntity.getTimezone());
        long end = requestWithGroupId.getEndDate();
        // 开启了 收益标签字段 则需要根据 打了收益标签的电表+EMS设备 进行计算
        // 异步查询 EMS 和 打了收益标签的电表的 出入电量 合计
        Pair<Map<Long, Double>, Map<Long, Double>> resultPair =
                asyncGetEmsAndAmmeterInOutMap(start, end);
        Map<Long, Double> socDiffMap = getSocDiffMap(start, end + 1 + MyTimeUtil.ONE_DAY_SECONDS);
        return getEfficiencyLongDoubleMap(resultPair, socDiffMap);
    }

    /**
     * 获得soc差值
     *
     * @param start 开始的零点
     * @param end 第二天的零点
     * @return 差值map 带符号
     */
    protected abstract Map<Long, Double> getSocDiffMap(long start, long end);

    @NotNull
    private Map<Long, Double> getEfficiencyLongDoubleMap(
            Pair<Map<Long, Double>, Map<Long, Double>> resultPair,
            Map<Long, Double> getSocDiffMap) {
        ProjectExtEntity projectExtEntity = projectExtService.getById(WebUtils.projectId.get());
        Pair<Long, Long> pair = ParseCapacity.getCapacity(projectExtEntity.getSize());
        Map<Long, Double> outMap = resultPair.getFirst();
        if (outMap.isEmpty()) {
            return new HashMap<>(1);
        }
        Map<Long, Double> inMap = resultPair.getSecond();
        // 计算效率 保留2位小数 效率= 出电量/入电量
        Map<Long, Double> result = new HashMap<>(outMap.size());
        for (Map.Entry<Long, Double> entry : outMap.entrySet()) {
            Long time = entry.getKey();
            Double outValue = entry.getValue();
            Double inValue = inMap.get(time);
            if (inValue != null && inValue > 0) {
                // 保留2位小数
                if (Objects.isNull(getSocDiffMap.get(time))) {
                    continue;
                }
                double socDiff = getSocDiffMap.get(time);
                outValue = outValue + socDiff * pair.getSecond();
                double efficiencyValue = outValue / inValue;
                if (efficiencyValue > Double.parseDouble(efficiencyHigh)) {
                    efficiencyValue = 1d;
                }
                if (efficiencyValue < Double.parseDouble(efficiencyLow)) {
                    efficiencyValue = 0d;
                }
                result.put(time, efficiencyValue);
            } else {
                // 充电量为0
                result.put(time, 0d);
            }
        }
        return result;
    }

    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ResultHolder {
        private Map<Long, Double> outMap;
        private Map<Long, Double> inMap;
    }

    /**
     * 计算电表的出入电量 需要加上MES的
     *
     * @param start : 开始时间
     * @param end : 结束时间
     * @return :
     *     org.springframework.data.util.Pair<java.util.Map<java.lang.Long,java.lang.Double>,java.util.Map<java.lang.Long,java.lang.Double>>
     */
    private Pair<Map<Long, Double>, Map<Long, Double>> asyncGetEmsAndAmmeterInOutMap(
            Long start, long end) {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<String> deviceIds =
                OperationUtil.getIncomeDeviceIds(projectId, deviceService, ammeterService);
        // 添加补值的数据
        deviceIds.addAll(groupService.queryGroupIdsByProjectId(projectId));
        ElectricRequest electricRequest =
                new ElectricRequest()
                        .setProjectId(projectId)
                        .setStart(start)
                        .setEnd(end)
                        .setDeviceIdList(deviceIds);
        // 判断项目是否开启收益
        Map<String, Boolean> projectOpenIncomeMap =
                projectService.getIncomeOpenStatusByProjectIds(
                        Collections.singletonList(projectId));
        ResultHolder resultHolder;
        if (projectOpenIncomeMap.get(projectId)) {
            // 开启收益，通过电价计算充放电量
            resultHolder = calDayProfitMap(projectEntity, electricRequest);
        } else {
            // 未开启收益，通过dayReport计算充放电量
            resultHolder = calDayProfitMapByDayReport(projectId, start, end);
        }
        return Pair.of(resultHolder.outMap, resultHolder.inMap);
    }

    /** 项目未开启时，通过dayReport计算充放电量 */
    private ResultHolder calDayProfitMapByDayReport(String projectId, Long start, Long end) {
        ResultHolder resultHolder = new ResultHolder();
        List<DayReportEntity> dayAllReports =
                dayReportService.findDayAllReports(
                        projectId,
                        EquipmentTypeEnums.EMS_DEVICE,
                        new RangeRequest().setStartDate(start).setEndDate(end));
        if (CollUtil.isEmpty(dayAllReports)) {
            return resultHolder;
        }
        Map<Long, Double> inMap =
                dayAllReports.stream()
                        .collect(
                                Collectors.toMap(
                                        DayReportEntity::getTime, DayReportEntity::getInData));
        Map<Long, Double> outMap =
                dayAllReports.stream()
                        .collect(
                                Collectors.toMap(
                                        DayReportEntity::getTime,
                                        i -> i.getOutData() + i.getOutEmsDcdcData()));
        return new ResultHolder(outMap, inMap);
    }

    /** 项目开启收益时，计算充放电量 */
    private ResultHolder calDayProfitMap(
            ProjectEntity projectEntity, ElectricRequest electricRequest) {
        ResultHolder resultHolder = new ResultHolder();
        electricAdapterChooser
                .choose(projectEntity.getElectricPriceType())
                .queryElectricEveryDayProfit(
                        electricRequest,
                        new ElectricAdapterService.QueryElectricsResult() {
                            @Override
                            public void peakValleysPeriodPostProcessor(List<ElectricEntity> list) {
                                Map<Long, Double> outMap =
                                        list.stream()
                                                .collect(
                                                        Collectors.toMap(
                                                                ElectricEntity::getTime,
                                                                ElectricEntity
                                                                        ::getTotalDischargeQuantity));

                                Map<Long, Double> inMap =
                                        list.stream()
                                                .collect(
                                                        Collectors.toMap(
                                                                ElectricEntity::getTime,
                                                                ElectricEntity
                                                                        ::getTotalChargeQuantity));
                                resultHolder.setInMap(inMap);
                                resultHolder.setOutMap(outMap);
                            }

                            @Override
                            public void dynamicPeriodPostProcessor(
                                    List<ElectricDynamicPeriodEntity> list) {
                                // 根据 年月日去 确定 key 时间戳 因为这个查询是把时段按照天都合并了  不会有具体天的时间戳 key
                                Map<Long, Double> outMap = new HashMap<>();
                                Map<Long, Double> inMap = new HashMap<>();
                                list.forEach(
                                        electricDynamicPeriodEntity -> {
                                            long dayKey =
                                                    MyTimeUtil.getEveryDayKey(
                                                            projectEntity.getTimezone(),
                                                            electricDynamicPeriodEntity.getYear(),
                                                            electricDynamicPeriodEntity.getMonth(),
                                                            electricDynamicPeriodEntity.getDay());
                                            outMap.put(
                                                    dayKey,
                                                    electricDynamicPeriodEntity
                                                            .getDischargeQuantity());
                                            inMap.put(
                                                    dayKey,
                                                    electricDynamicPeriodEntity
                                                            .getChargeQuantity());
                                        });
                                resultHolder.setInMap(inMap);
                                resultHolder.setOutMap(outMap);
                            }
                        });
        return resultHolder;
    }
}
