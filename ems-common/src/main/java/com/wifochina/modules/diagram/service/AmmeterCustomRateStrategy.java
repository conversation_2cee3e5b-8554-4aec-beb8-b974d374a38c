package com.wifochina.modules.diagram.service;

import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithGroupId;

import java.util.List;
import java.util.Map;

/**
 * Created on 2023/11/6 13:32. <br>
 * 电表自定义曲线查询 策略
 *
 * <AUTHOR>
 */
public interface AmmeterCustomRateStrategy {

    /**
     * 获取电表的 自定义 曲线
     *
     * @param requestWithGroupId : 请求参数
     * @param customFieldStrategy : 自定义字段策略
     * @return : Map<String, List<ValueVO>>
     * @throws CloneNotSupportedException : 克隆异常
     */
    Map<String, List<ValueVO>> customRate(
            RequestWithGroupId requestWithGroupId, CustomFieldStrategy customFieldStrategy)
            throws CloneNotSupportedException;

    interface CustomFieldStrategy {
        /**
         * 直流电表的需要查询的自定义 Field 列表
         *
         * @return : List<String>
         */
        default List<String> dcFields() {
            return List.of();
        }

        /**
         * 直流电表的需要查询的自定义 Field 列表
         *
         * @return : List<String>
         */
        default List<String> acFields() {
            return List.of();
        }

        /**
         * 是否异步查询
         *
         * @return : boolean
         */
        default boolean async() {
            return true;
        }
    }
}
