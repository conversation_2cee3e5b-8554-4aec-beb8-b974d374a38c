package com.wifochina.modules.diagram.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

@Data
public class MaxMeterDemandRequest {
    @ApiModelProperty(value = "开始时间", required = true)
    private long start;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long end;

    @ApiModelProperty(value = "分组id,传all查所有,不能传空或者空字符串", required = true)
    private String groupId;

    private String projectId;
}
