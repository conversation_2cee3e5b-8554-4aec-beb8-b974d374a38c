package com.wifochina.modules.diagram.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * RequestWithMeterId
 * 
 * @date 4/27/2022 9:06 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "负载按日期查询")
public class LoadRateRequest {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;

    @ApiModelProperty(value = "时间间隔，可为null，或者5的倍数")
    private Integer period;
}