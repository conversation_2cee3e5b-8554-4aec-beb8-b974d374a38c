package com.wifochina.modules.pricetemplate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.pricetemplate.entity.PriceTemplateEntity;
import com.wifochina.modules.pricetemplate.request.PriceTemplateCommonRequest;
import com.wifochina.modules.pricetemplate.request.PriceTemplatePageRequest;
import com.wifochina.modules.pricetemplate.request.PriceTemplateRequest;

import java.util.List;

/**
 * Created on 2024/4/18 15:19.
 *
 * <AUTHOR>
 */
public interface PriceTemplateService extends IService<PriceTemplateEntity> {
    void savePriceTemplate(PriceTemplateRequest request);

    /**
     * 删除 电价模版id
     *
     * @param id : id
     */
    void deletePriceTemplate(Long id);

    IPage<PriceTemplateEntity> getPages(boolean isManage, PriceTemplatePageRequest request);

    PriceTemplateRequest detail(Long id);

    List<PriceTemplateEntity> getAll(boolean isManager, PriceTemplateCommonRequest commonRequest);
}
