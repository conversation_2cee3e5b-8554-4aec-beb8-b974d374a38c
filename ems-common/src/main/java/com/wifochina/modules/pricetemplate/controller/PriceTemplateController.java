package com.wifochina.modules.pricetemplate.controller;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.PriceTemplateLogDetailService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.pricetemplate.entity.PriceTemplateEntity;
import com.wifochina.modules.pricetemplate.request.PriceTemplateCommonRequest;
import com.wifochina.modules.pricetemplate.request.PriceTemplatePageRequest;
import com.wifochina.modules.pricetemplate.request.PriceTemplateRequest;
import com.wifochina.modules.pricetemplate.service.PriceTemplateService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.AllArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据状态 前端控制器
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@RestController
@RequestMapping("/priceTemplate")
@Api(tags = "xx-电价模版控制器")
@AllArgsConstructor
public class PriceTemplateController {

    private final PriceTemplateService priceTemplateService;
    private final ProjectService projectService;

    @PostMapping("getAll")
    @ApiOperation("查询所有电价配置模版列表(供select选择框使用)")
    public Result<List<PriceTemplateEntity>> getAll(
            @RequestBody PriceTemplateCommonRequest commonRequest) {
        // for 1.3.7 新增了 数据中心的查询 必须要提供 来切分开 是哪个数据中心的模版  海外模版和国内的模版不同
        if (CollectionUtil.isEmpty(commonRequest.getPriceType())) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        String projectId = WebUtils.projectId.get();
        boolean isManager;
        if (!StringUtil.isEmpty(projectId)) {
            isManager = false;
            ProjectEntity projectEntity = projectService.getById(projectId);
            if (Boolean.TRUE.equals(projectEntity.getPriceProxy())) {
                isManager = true;
            }
        } else {
            isManager = true;
        }
        // 根据
        List<PriceTemplateEntity> results = priceTemplateService.getAll(isManager, commonRequest);
        return Result.success(results);
    }

    @PostMapping("/getPriceTemplates")
    @ApiOperation("分页查询电价配置模版列表")
    public Result<IPage<PriceTemplateEntity>> pagePriceTemplates(
            @RequestBody PriceTemplatePageRequest priceTemplatePageRequest) {
        // for 1.3.7 新增了 数据中心的查询 必须要提供 来切分开 是哪个数据中心的模版  海外模版和国内的模版不同
        String projectId = WebUtils.projectId.get();
        boolean isManager = false;
        if (StringUtil.isEmpty(projectId)) {
            isManager = true;
        }
        IPage<PriceTemplateEntity> pages =
                priceTemplateService.getPages(isManager, priceTemplatePageRequest);
        return Result.success(pages);
    }

    @PostMapping("/detail/{id}")
    @ApiOperation("查询某个电价配置模版的详情")
    public Result<PriceTemplateRequest> detail(@PathVariable("id") Long id) {
        // 查询某个模版id的详情
        PriceTemplateRequest result = priceTemplateService.detail(id);
        return Result.success(result);
    }

    /** 添加电价/时段配置 */
    @PostMapping("/savePriceForTemplate")
    @ApiOperation("保存价格配置 for 模版的")
    @PreAuthorize(
            "hasAuthority('/manage/priceTemplate/update')  or hasAuthority('/operation/priceTemplate/update')")
    @Log(module = "PRICE_TEMPLATE", methods = "PRICE_TEMPLATE_SAVE_UPDATE", type = OperationType.ADD)
    public Result<Object> savePriceForTemplate(@RequestBody PriceTemplateRequest request) {
        // 这里的 外层的 electricPriceList的 start 和 end 是空 没有时段
        // for 1.3.7 新增了 数据中心的查询 必须要提供 来切分开 是哪个数据中心的模版  海外模版和国内的模版不同
        if (StringUtil.isEmpty(request.getPriceType())) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        priceTemplateService.savePriceTemplate(request);
        return Result.success();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除电价模版")
    @PreAuthorize(
            "hasAuthority('/manage/priceTemplate/delete') or hasAuthority('/operation/priceTemplate/delete')")
    @Log(
            module = "PRICE_TEMPLATE",
            type = OperationType.DEL,
            logDetailServiceClass = PriceTemplateLogDetailService.class)
    public Result<String> deletePriceTemplate(@PathVariable("id") String id) {
        //        PriceTemplateRequest priceTemplateRequest = priceTemplateService.detail(id);
        priceTemplateService.deletePriceTemplate(Long.parseLong(id));
        return Result.success();
    }
}
