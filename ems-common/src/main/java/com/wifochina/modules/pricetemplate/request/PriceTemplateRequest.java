package com.wifochina.modules.pricetemplate.request;

import com.wifochina.modules.operation.entity.ElectricPriceEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-04-10 19:20:43
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PriceTemplateRequest {

    private Long id;
    private String templateName;
    private List<ElectricPriceEntity> priceEntityList;

    /** 下面属性 和 Ele 的电价实体一样 这里复用一下 之后用于复制到每个 priceEntityList里面吧 */
    @ApiModelProperty(value = "需量价格")
    private Double demandPrice;

    @ApiModelProperty(value = "光伏自用价格")
    private Double pvSelfPrice;

    @ApiModelProperty(value = "脱硫标杆价格")
    private Double pvDfPrice;

    @ApiModelProperty(value = "光伏国家补贴价格")
    private Double pvSubsidyPrice;

    @ApiModelProperty(value = "光伏发电价格")
    private Double pvPrice;

    @ApiModelProperty(value = "风电自用价格")
    private Double windSelfPrice;

    @ApiModelProperty(value = "风电脱硫标杆价格")
    private Double windDfPrice;

    @ApiModelProperty(value = "风电国家补贴价格")
    private Double windSubsidyPrice;

    @ApiModelProperty(value = "风电发电价格")
    private Double windPrice;

    @ApiModelProperty(value = "数据中心 用于标识这个模版是海外还是国内")
    private String priceType;

    @ApiModelProperty(value = "余热发电脱硫标杆价格")
    private Double wasterDfPrice;

    @ApiModelProperty(value = "余热发电价格")
    private Double wasterPrice;

    @ApiModelProperty(value = "余热发电自用价格")
    private Double wasterSelfPrice;

    @ApiModelProperty(value = "余热发电国家补贴价格")
    private Double wasterSubsidyPrice;
}
