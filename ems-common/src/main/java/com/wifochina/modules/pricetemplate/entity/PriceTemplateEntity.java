package com.wifochina.modules.pricetemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 电价配置模版 实体类
 *
 * <AUTHOR>
 * @since 2024-04-18 14:13:55
 */
@Getter
@Setter
@TableName("t_price_template")
@ApiModel(value = "PriceTemplateEntity 对象")
@Accessors(chain = true)
public class PriceTemplateEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模版id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("模版名称")
    private String templateName;

    @ApiModelProperty(value = "需量价格")
    private Double demandPrice;

    @ApiModelProperty(value = "光伏自用价格")
    private Double pvSelfPrice;

    @ApiModelProperty(value = "脱硫标杆价格")
    private Double pvDfPrice;

    @ApiModelProperty(value = "光伏国家补贴价格")
    private Double pvSubsidyPrice;

    @ApiModelProperty(value = "光伏发电价格")
    private Double pvPrice;

    @ApiModelProperty(value = "风电自用价格")
    private Double windSelfPrice;

    @ApiModelProperty(value = "风电脱硫标杆价格")
    private Double windDfPrice;

    @ApiModelProperty(value = "风电国家补贴价格")
    private Double windSubsidyPrice;

    @ApiModelProperty(value = "风电发电价格")
    private Double windPrice;

    @ApiModelProperty(value = "数据中心 用于标识这个模版是海外还是国内")
    private String priceType;

    @ApiModelProperty(value = "余热发电脱硫标杆价格")
    private String wasterDfPrice;

    @ApiModelProperty(value = "余热发电价格")
    private String wasterPrice;

    @ApiModelProperty(value = "余热发电自用价格")
    private String wasterSelfPrice;

    @ApiModelProperty(value = "余热发电国家补贴价格")
    private String wasterSubsidyPrice;
}
