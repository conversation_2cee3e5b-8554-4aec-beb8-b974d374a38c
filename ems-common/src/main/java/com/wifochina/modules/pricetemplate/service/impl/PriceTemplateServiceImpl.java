package com.wifochina.modules.pricetemplate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.pricetemplate.entity.PriceTemplateEntity;
import com.wifochina.modules.pricetemplate.mapper.PriceTemplateMapper;
import com.wifochina.modules.pricetemplate.request.PriceTemplateCommonRequest;
import com.wifochina.modules.pricetemplate.request.PriceTemplatePageRequest;
import com.wifochina.modules.pricetemplate.request.PriceTemplateRequest;
import com.wifochina.modules.pricetemplate.service.PriceTemplateService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created on 2024/4/18 15:20.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class PriceTemplateServiceImpl extends ServiceImpl<PriceTemplateMapper, PriceTemplateEntity>
        implements PriceTemplateService {
    private final ElectricPriceService electricPriceService;

    @Override
    public void savePriceTemplate(PriceTemplateRequest request) {
        // 获取当前登录用户的projectId
        String projectId = WebUtils.projectId.get();
        if (request.getPriceEntityList() != null && !request.getPriceEntityList().isEmpty()) {
            // 如果日期不符合规则，直接抛出异常

            List<ElectricPriceEntity> list = request.getPriceEntityList();
            list =
                    list.stream()
                            .sorted(
                                    Comparator.comparing(ElectricPriceEntity::getStartTime)
                                            .thenComparing(ElectricPriceEntity::getEndTime))
                            .collect(Collectors.toList());
            ElectricPriceEntity electricityPrice1 = list.get(0);
            for (int i = 1; i < list.size(); i++) {
                // 当前节点
                ElectricPriceEntity electricityPrice2 = list.get(i);
                ServiceAssert.isTrue(
                        electricityPrice1.getEndTime().compareTo(electricityPrice2.getStartTime())
                                <= 0,
                        ErrorResultCode.TIME_OVERLAPPED.value());
                ServiceAssert.isTrue(
                        electricityPrice1.getEndTime().compareTo(electricityPrice2.getStartTime())
                                >= 0,
                        ErrorResultCode.TIME_IS_NOT_FULL.value());
                if (list.size() - 1 == i) {
                    //// 最后一个如果前后相同，证明传入的都是00:00:00，那肯定没有排满24小时
                    ServiceAssert.isTrue(
                            electricityPrice2.getEndTime() == list.get(0).getStartTime(),
                            ErrorResultCode.TIME_IS_NOT_FULL.value());
                    break;
                }
                electricityPrice1 = electricityPrice2;
            }

            PriceTemplateEntity template = new PriceTemplateEntity();
            BeanUtil.copyProperties(request, template);
            template.setProjectId(projectId);
            template.setPriceType(request.getPriceType());

            if (request.getId() != null && request.getId() != 0) {
                // 编辑
                this.baseMapper.updateById(template);
                electricPriceService.remove(
                        new LambdaQueryWrapper<ElectricPriceEntity>()
                                .eq(ElectricPriceEntity::getPriceTemplateId, template.getId()));
                for (ElectricPriceEntity timePrice : list) {
                    if (timePrice.getStartTime() == timePrice.getEndTime()) {
                        continue;
                    }
                    // 冗余保存 把 外层的 保存在内部里面
                    redundancySave(request, timePrice);
                    // 这里保存模版的id 算是扩展
                    timePrice.setPriceTemplateId(template.getId());
                    timePrice.setProjectId(projectId);
                    template.setPriceType(request.getPriceType());
                    if (!electricPriceService.save(timePrice)) {
                        throw new ServiceException(ErrorResultCode.DISTRIBUTION_FAILED.value());
                    }
                }
            } else {
                // 先保存 模版
                this.baseMapper.insert(template);
                for (ElectricPriceEntity timePrice : list) {
                    if (timePrice.getStartTime() == timePrice.getEndTime()) {
                        continue;
                    }
                    redundancySave(request, timePrice);
                    // 这里保存模版的id 算是扩展
                    timePrice.setPriceTemplateId(template.getId());
                    timePrice.setProjectId(projectId);
                    template.setPriceType(request.getPriceType());
                    if (!electricPriceService.save(timePrice)) {
                        throw new ServiceException(ErrorResultCode.DISTRIBUTION_FAILED.value());
                    }
                }
            }
        }
    }

    private static void redundancySave(
            PriceTemplateRequest request, ElectricPriceEntity timePrice) {
        timePrice.setDemandPrice(request.getDemandPrice());
        timePrice.setPvDfPrice(request.getPvDfPrice());
        timePrice.setPvSelfPrice(request.getPvSelfPrice());
        timePrice.setPvSubsidyPrice(request.getPvSubsidyPrice());
        timePrice.setPvPrice(request.getPvPrice());
        timePrice.setWindPrice(request.getWindPrice());
        timePrice.setWindDfPrice(request.getWindDfPrice());
        timePrice.setWindSubsidyPrice(request.getWindSubsidyPrice());
        timePrice.setWindSelfPrice(request.getWindSelfPrice());
        timePrice.setWasterPrice(request.getWasterPrice());
        timePrice.setWasterDfPrice(request.getWasterDfPrice());
        timePrice.setWasterSelfPrice(request.getWasterSelfPrice());
        timePrice.setWasterSubsidyPrice(request.getWasterSubsidyPrice());
    }

    @Override
    public void deletePriceTemplate(Long id) {
        // 删除基础的模版  信息
        this.baseMapper.deleteById(id);
        // 删除模版所包含的 电价的时段价格信息
        electricPriceService.remove(
                new LambdaQueryWrapper<ElectricPriceEntity>()
                        .eq(ElectricPriceEntity::getPriceTemplateId, id));
    }

    /**
     * 分页查询
     *
     * @param request : request
     * @return : IPage
     */
    @Override
    public IPage<PriceTemplateEntity> getPages(
            boolean isManager, PriceTemplatePageRequest request) {
        Page<PriceTemplateEntity> page = Page.of(request.getPageNum(), request.getPageSize());
        if (isManager) {
            // 如果是管理端查询 那么就查询 projectId 是null的
            return page(
                    page,
                    Wrappers.lambdaQuery(PriceTemplateEntity.class)
                            // 新增的 查询数据中心
                            .in(
                                    CollectionUtil.isNotEmpty(request.getPriceType()),
                                    PriceTemplateEntity::getPriceType,
                                    request.getPriceType())
                            .isNull(PriceTemplateEntity::getProjectId)
                            .like(
                                    !StringUtil.isEmpty(request.getTemplateName()),
                                    PriceTemplateEntity::getTemplateName,
                                    request.getTemplateName())
                            .orderByAsc(PriceTemplateEntity::getCreateTime));
        } else {
            return page(
                    page,
                    Wrappers.lambdaQuery(PriceTemplateEntity.class)
                            // 新增的 查询数据中心
                            .in(
                                    CollectionUtil.isNotEmpty(request.getPriceType()),
                                    PriceTemplateEntity::getPriceType,
                                    request.getPriceType())
                            .eq(PriceTemplateEntity::getProjectId, WebUtils.projectId.get())
                            .like(
                                    !StringUtil.isEmpty(request.getTemplateName()),
                                    PriceTemplateEntity::getTemplateName,
                                    request.getTemplateName())
                            .orderByAsc(PriceTemplateEntity::getCreateTime));
        }
    }

    @Override
    public PriceTemplateRequest detail(Long id) {
        PriceTemplateRequest result = new PriceTemplateRequest();
        if (id != null && id != 0) {
            PriceTemplateEntity template = this.baseMapper.selectById(id);
            if (template != null) {
                BeanUtil.copyProperties(template, result);
                // 查询详情
                List<ElectricPriceEntity> list =
                        this.electricPriceService.list(
                                new LambdaQueryWrapper<ElectricPriceEntity>()
                                        .eq(ElectricPriceEntity::getPriceTemplateId, id));
                result.setPriceEntityList(list);
            } else {
                throw new ServiceException(ErrorResultCode.PRICE_TEMPLATE_NOT_EXIST.value());
            }
        } else {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        return result;
    }

    @Override
    public List<PriceTemplateEntity> getAll(
            boolean isManager, PriceTemplateCommonRequest commonRequest) {
        if (isManager) {
            return this.baseMapper.selectList(
                    new LambdaQueryWrapper<PriceTemplateEntity>()
                            .isNull(PriceTemplateEntity::getProjectId)
                            .in(PriceTemplateEntity::getPriceType, commonRequest.getPriceType()));
        } else {
            return this.baseMapper.selectList(
                    new LambdaQueryWrapper<PriceTemplateEntity>()
                            .eq(PriceTemplateEntity::getProjectId, WebUtils.projectId.get())
                            .in(PriceTemplateEntity::getPriceType, commonRequest.getPriceType()));
        }
    }
}
