package com.wifochina.modules.report.job.jobservice;

import com.wifochina.modules.income.job.CustomTimezoneQuartzService;
import com.wifochina.modules.report.job.DayReportJob;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

/**
 * Created on 2025/2/19 10:38.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DayReportJobQuartzServiceImpl implements CustomTimezoneQuartzService {

    private static final String JOB_NAME = "day-report-job:%s";
    private static final String JOB_TRIGGER_NAME = "day-report-trigger:%s";
    private static final String JOB_GROUP = "day-report-job";

    @Resource protected Scheduler scheduler;

    public void startNowTest(String timezone) {
        JobDetail jobDetail = getJobDetail(timezone);
        Trigger trigger =
                TriggerBuilder.newTrigger()
                        .withIdentity("triggerName", JOB_GROUP)
                        .startNow()
                        .build();
        try {
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void addJob(String timezone) {
        //        startNowTest(timezone);
        //         构建JobDetail (表示一个具体的可执行的调度程序，Job是这个可执行调度程序所要执行的内容)
        JobDetail jobDetail = getJobDetail(timezone);
        // 构建出发去Trigger （调度参数的配置，代表何时出发该任务)
        Trigger trigger = getTrigger(timezone, getJobCrontab());
        if (trigger != null) {
            try {
                scheduler.scheduleJob(jobDetail, trigger);
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
            } catch (SchedulerException ignored) {
                log.error("");
            }
        }
    }

    public Trigger getTrigger(String timezone, String jobCron) {
        String triggerName = String.format(JOB_TRIGGER_NAME, timezone);
        return TriggerBuilder.newTrigger()
                .withIdentity(triggerName, JOB_GROUP)
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(jobCron)
                                .inTimeZone(TimeZone.getTimeZone(timezone)))
                .build();
    }

    public JobDetail getJobDetail(String timezone) {
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = String.format(JOB_NAME, timezone);
        jobData.put("timezone", timezone);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        return JobBuilder.newJob(DayReportJob.class)
                .withIdentity(jobName, JOB_GROUP)
                .usingJobData(jobDataMap)
                .storeDurably()
                .build();
    }

    public String getJobCrontab() {
        return "0 56 0 * * ?";
    }
}
