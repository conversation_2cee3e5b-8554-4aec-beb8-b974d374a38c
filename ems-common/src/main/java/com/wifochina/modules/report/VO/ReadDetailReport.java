package com.wifochina.modules.report.VO;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-13 4:20 PM
 */
@Data
public class ReadDetailReport {

    @ApiModelProperty(value = "总峰放电量")
    private Double peakDischargeReadingQuantity;

    @ApiModelProperty(value = "总谷放电量")
    private Double vallyDischargeReadingQuantity;

    @ApiModelProperty(value = "总平放电量")
    private Double flatDischargeReadingQuantity;

    @ApiModelProperty(value = "总尖放电量")
    private Double tipDischargeReadingQuantity;

    @ApiModelProperty(value = "总深谷放电量")
    private Double deepVallyDischargeReadingQuantity;

    @ApiModelProperty(value = "总峰充电量")
    private Double peakChargeReadingQuantity;

    @ApiModelProperty(value = "总谷充电量")
    private Double vallyChargeReadingQuantity;

    @ApiModelProperty(value = "总平充电量")
    private Double flatChargeReadingQuantity;

    @ApiModelProperty(value = "总尖充电量")
    private Double tipChargeReadingQuantity;

    @ApiModelProperty(value = "总深谷充电量")
    private Double deepVallyChargeReadingQuantity;

    @ApiModelProperty(value = "报告细节")
    private List<ReadMonthDetail> readMonthDetail;
}
