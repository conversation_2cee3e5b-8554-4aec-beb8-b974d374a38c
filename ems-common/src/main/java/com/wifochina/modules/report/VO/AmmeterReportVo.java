package com.wifochina.modules.report.VO;

import com.wifochina.modules.report.entity.DayReportEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2025/2/24 11:19.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AmmeterReportVo {
    private AmmeterBaseVo ammeterBaseVo;
    private Map<Long, DayReportEntity> dayReports = new HashMap<>();
}
