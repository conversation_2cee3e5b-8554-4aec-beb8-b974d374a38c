package com.wifochina.modules.report.controller;

import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.report.entity.MeterDataRemediesEntity;
import com.wifochina.modules.report.request.RemediesMeterQueryRequest;
import com.wifochina.modules.report.request.RemediesMeterRequest;
import com.wifochina.modules.report.service.MeterDataRemediesService;

import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RequestMapping("/report")
@RestController
@Api(tags = "15-报表")
@RequiredArgsConstructor
public class MeterDataRemediesController {

    private final MeterDataRemediesService meterDataRemediesService;

    /** 电站日报 */
    @PostMapping("/dailyMeterRemedies")
    @ApiOperation("日报补值")
    @PreAuthorize("hasAuthority('/report/meter/remedies')")
    @Log(module = "REMEDIES_METER", methods = "DAILY_REMEDIES", type = OperationType.ADD)
    public Result<Object> dailyMeterRemedies(@RequestBody RemediesMeterRequest remediesRequest) {
        meterDataRemediesService.updateRemediesData(
                remediesRequest, WebUtils.projectId.get(), EmsConstants.DAILY);
        return Result.success();
    }

    /** 电站月报 */
    @PostMapping("/monthMeterRemedies")
    @ApiOperation("月报补值")
    @PreAuthorize("hasAuthority('/report/meter/remedies')")
    @Log(module = "REMEDIES_METER", methods = "MONTH_REMEDIES", type = OperationType.ADD)
    public Result<Object> monthMeterRemedies(@RequestBody RemediesMeterRequest remediesRequest) {
        meterDataRemediesService.updateRemediesData(
                remediesRequest, WebUtils.projectId.get(), EmsConstants.MONTH);
        return Result.success();
    }

    /** 电站年报 */
    @PostMapping("/yearMeterRemedies")
    @ApiOperation("年报补值")
    @PreAuthorize("hasAuthority('/report/meter/remedies')")
    @Log(module = "REMEDIES_METER", methods = "YEAR_REMEDIES", type = OperationType.ADD)
    public Result<Map<String, Object>> yearMeterRemedies(
            @RequestBody RemediesMeterRequest remediesRequest) {
        meterDataRemediesService.updateRemediesData(
                remediesRequest, WebUtils.projectId.get(), EmsConstants.YEAR);
        return Result.success();
    }

    /** 电站日报 */
    @PostMapping("/dailyMeterRecord")
    @ApiOperation("日报补值记录")
    @PreAuthorize("hasAuthority('/report/remedies/query')")
    public Result<List<MeterDataRemediesEntity>> dailyMeterRecord(
            @RequestBody RemediesMeterQueryRequest remediesQueryRequest) {
        List<MeterDataRemediesEntity> list =
                meterDataRemediesService.getMeterRemediesData(
                        remediesQueryRequest, WebUtils.projectId.get(), EmsConstants.DAILY);
        return Result.success(list);
    }

    /** 电站月报 */
    @PostMapping("/monthMeterRecord")
    @ApiOperation("月报补值记录")
    @PreAuthorize("hasAuthority('/report/remedies/query')")
    public Result<List<MeterDataRemediesEntity>> monthMeterRecord(
            @RequestBody RemediesMeterQueryRequest remediesQueryRequest) {
        List<MeterDataRemediesEntity> list =
                meterDataRemediesService.getMeterRemediesData(
                        remediesQueryRequest, WebUtils.projectId.get(), EmsConstants.MONTH);
        return Result.success(list);
    }

    /** 电站年报 */
    @PostMapping("/yearMeterRecord")
    @ApiOperation("年报补值记录")
    @PreAuthorize("hasAuthority('/report/remedies/query')")
    public Result<List<MeterDataRemediesEntity>> yearMeterRecord(
            @RequestBody RemediesMeterQueryRequest remediesQueryRequest) {
        List<MeterDataRemediesEntity> list =
                meterDataRemediesService.getMeterRemediesData(
                        remediesQueryRequest, WebUtils.projectId.get(), EmsConstants.YEAR);
        return Result.success(list);
    }
}
