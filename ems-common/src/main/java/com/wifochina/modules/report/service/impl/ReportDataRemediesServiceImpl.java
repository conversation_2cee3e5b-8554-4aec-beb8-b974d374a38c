package com.wifochina.modules.report.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.RemediesDataTypeEnum;
import com.wifochina.modules.report.entity.ReportDataRemediesEntity;
import com.wifochina.modules.report.mapper.ReportDataRemediesMapper;
import com.wifochina.modules.report.request.RemediesQueryRequest;
import com.wifochina.modules.report.request.RemediesReportRequest;
import com.wifochina.modules.report.service.ReportDataRemediesService;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据状态 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Service
public class ReportDataRemediesServiceImpl
        extends ServiceImpl<ReportDataRemediesMapper, ReportDataRemediesEntity>
        implements ReportDataRemediesService {

    @Override
    @Transactional
    public void updateRemediesData(
            RemediesReportRequest remediesRequest, String projectId, String type) {

        this.lambdaUpdate()
                .eq(ReportDataRemediesEntity::getProjectId, projectId)
                .eq(ReportDataRemediesEntity::getDay, remediesRequest.getDay())
                .eq(ReportDataRemediesEntity::getReportType, type)
                .eq(ReportDataRemediesEntity::getTime, remediesRequest.getTime())
                .eq(ReportDataRemediesEntity::getDataType, remediesRequest.getDataType())
                .remove();
        ReportDataRemediesEntity reportDataRemediesEntity = new ReportDataRemediesEntity();
        reportDataRemediesEntity.setProjectId(projectId);
        reportDataRemediesEntity.setDay(remediesRequest.getDay());
        reportDataRemediesEntity.setTime(remediesRequest.getTime());
        reportDataRemediesEntity.setReportType(type);
        reportDataRemediesEntity.setDataType(remediesRequest.getDataType());
        reportDataRemediesEntity.setData(remediesRequest.getData());
        this.baseMapper.insert(reportDataRemediesEntity);
    }

    @Override
    public List<ReportDataRemediesEntity> getReportRemediesData(
            RemediesQueryRequest remediesQueryRequest, String projectId, String type) {
        return this.lambdaQuery()
                .eq(ReportDataRemediesEntity::getProjectId, projectId)
                .eq(ReportDataRemediesEntity::getReportType, type)
                .eq(
                        StringUtils.hasLength(remediesQueryRequest.getDataType()),
                        ReportDataRemediesEntity::getDataType,
                        remediesQueryRequest.getDataType())
                .ge(ReportDataRemediesEntity::getTime, remediesQueryRequest.getStart())
                .lt(ReportDataRemediesEntity::getTime, remediesQueryRequest.getEnd())
                .orderByDesc(ReportDataRemediesEntity::getTime)
                .list();
    }

    @Override
    public List<ReportDataRemediesEntity> getReportRemediesData(
            String projectId, Long day, String reportType) {
        return this.lambdaQuery()
                .eq(ReportDataRemediesEntity::getProjectId, projectId)
                .eq(ReportDataRemediesEntity::getReportType, reportType)
                .ge(ReportDataRemediesEntity::getDay, day)
                .orderByDesc(ReportDataRemediesEntity::getTime)
                .list();
    }

    /**
     * 历史遗留问题 暂时不改 这里稍微封装了一下 把这段又长又无聊的 代码 放在ReportDataRemediesServiceImpl class 里面
     *
     * @param projectId :projectId
     * @param startTime : 开始时间
     * @param reportType : month or year
     * @return : 外层key 是 EquipmentTypeEnum + _ + in/out 的,
     */
    @Override
    public Map<String, Map<Long, Double>> getReportRemediesDataMapByDataType(
            String projectId, Long startTime, String reportType) {
        List<ReportDataRemediesEntity> reportDataRemediesEntities =
                this.getReportRemediesData(projectId, startTime, reportType);
        Map<Long, Double> remendiesPvMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.PV.getType());
        Map<Long, Double> remendiesGridOutMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.GRIDOUT.getType());
        Map<Long, Double> remendiesGridInMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.GRIDIN.getType());
        Map<Long, Double> remendiesEmsOutMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.EMSOUT.getType());
        Map<Long, Double> remendiesEmsInMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.EMSIN.getType());
        Map<Long, Double> remendiesWindMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.WIND.getType());
        Map<Long, Double> remendiesDieselMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.DIESEL.getType());
        Map<Long, Double> remendiesGasMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.GAS.getType());
        Map<Long, Double> remendiesWasterMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.WASTER.getType());
        Map<Long, Double> remendiesPileMap =
                getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.PILE.getType());

        Map<String, Map<Long, Double>> resultMap = new HashMap<>();
        resultMap.put(
                EquipmentTypeEnums.PV_METER.name() + EmsConstants.UNDER_LINE + EmsConstants.OUT,
                remendiesPvMap);
        resultMap.put(
                EquipmentTypeEnums.WIND_METER.name() + EmsConstants.UNDER_LINE + EmsConstants.OUT,
                remendiesWindMap);
        resultMap.put(
                EquipmentTypeEnums.DIESEL_METER.name() + EmsConstants.UNDER_LINE + EmsConstants.OUT,
                remendiesDieselMap);
        resultMap.put(
                EquipmentTypeEnums.GAS_METER.name() + EmsConstants.UNDER_LINE + EmsConstants.OUT,
                remendiesGasMap);
        resultMap.put(
                EquipmentTypeEnums.WASTER_METER.name() + EmsConstants.UNDER_LINE + EmsConstants.OUT,
                remendiesWasterMap);
        resultMap.put(
                EquipmentTypeEnums.PILE_METER.name() + EmsConstants.UNDER_LINE + EmsConstants.OUT,
                remendiesPileMap);
        resultMap.put(
                EquipmentTypeEnums.GRID_METER.name() + EmsConstants.UNDER_LINE + EmsConstants.OUT,
                remendiesGridOutMap);
        // 只有下面2个 类型是 有 in 和 out的 上面的都是只有 out
        resultMap.put(
                EquipmentTypeEnums.GRID_METER.name() + EmsConstants.UNDER_LINE + EmsConstants.IN,
                remendiesGridInMap);

        resultMap.put(
                EquipmentTypeEnums.EMS_DEVICE.name() + EmsConstants.UNDER_LINE + EmsConstants.OUT,
                remendiesEmsOutMap);
        resultMap.put(
                EquipmentTypeEnums.EMS_DEVICE.name() + EmsConstants.UNDER_LINE + EmsConstants.IN,
                remendiesEmsInMap);
        return resultMap;
    }

    private static @NotNull Map<Long, Double> getRemediesMap(
            List<ReportDataRemediesEntity> reportDataRemediesEntities, String type) {
        return reportDataRemediesEntities.stream()
                .filter(e -> e.getDataType().equals(type))
                .collect(
                        Collectors.toMap(
                                ReportDataRemediesEntity::getTime,
                                ReportDataRemediesEntity::getData));
    }
}
