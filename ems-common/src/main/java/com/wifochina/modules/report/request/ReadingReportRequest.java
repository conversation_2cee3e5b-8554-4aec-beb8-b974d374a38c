package com.wifochina.modules.report.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * reportRequest
 * 
 * @since 5/6/2022 11:17 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "按日期查询报表")
public class ReadingReportRequest {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "抄表Id", required = true)
    private String meterId;
}
