package com.wifochina.modules.report.service;

import com.wifochina.modules.report.VO.AmmeterReportVo;
import com.wifochina.modules.report.VO.ReadDetailReport;
import com.wifochina.modules.report.VO.ReadReport;
import com.wifochina.modules.report.request.ReportRequest;

import java.util.List;
import java.util.Map;

/**
 * operationProfitService
 *
 * @since 4/14/2022 11:24 AM
 * <AUTHOR>
 * @version 1.0
 */
public interface ReportService {

    Map<String, Object> getWholeStationReport(ReportRequest reportRequest, String type);

    /** 报表，type type代表类型。daily 日报；month 月报； */
    Map<String, Object> getReport(ReportRequest reportRequest, String type);

    /** type type代表类型。daily 日报；month 月报； */
    Map<String, Object> getMeterReport(ReportRequest reportRequest, String type);



    Map<String, List<AmmeterReportVo>> getMeterReportNew(ReportRequest reportRequest, String type);

    Map<String, ReadReport> getReadMonth(Long start, String meterId, String projectId);

    Map<String, ReadDetailReport> getReadMonthDetail(Long start, String meterId, String projectId);

    Map<String, ReadReport> getReadYear(Long start, String meterId, String projectId);

    Map<String, ReadDetailReport> getReadYearDetail(Long start, String meterId, String projectId);

    Map<String, ReadReport> getReadMeter(Long start, MeterHolder meterHolder);

    Map<String, ReadDetailReport> getReadMeterDetail(Long start, MeterHolder meterHolder);

    interface MeterHolder {

        String meterId();

        String period();

        String projectId();
    }
}
