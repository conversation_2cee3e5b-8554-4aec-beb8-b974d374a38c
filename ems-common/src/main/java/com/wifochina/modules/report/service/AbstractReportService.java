package com.wifochina.modules.report.service;

import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.data.entity.MeterContentData;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.electric.ElectricDynamicPeriodService;
import com.wifochina.modules.electric.ElectricService;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.report.VO.ReadDetailReport;
import com.wifochina.modules.report.VO.ReadMonth;
import com.wifochina.modules.report.VO.ReadMonthDetail;
import com.wifochina.modules.report.VO.ReadReport;

import com.wifochina.modules.report.entity.DayReportEntity;
import com.wifochina.modules.report.request.DayReportRequest;
import org.aspectj.weaver.ast.Var;
import org.springframework.data.util.Pair;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-10-24 3:32 PM
 */
public abstract class AbstractReportService implements ReportService {

    @Resource public ProjectService projectService;

    @Resource public GroupService groupService;

    @Resource public DataService dataService;

    @Resource public ElectricService electricService;
    @Resource public ElectricDynamicPeriodService electricDynamicPeriodService;

    @Resource public AmmeterService ammeterService;

    @Resource public DayReportService dayReportService;

    @Override
    public Map<String, ReadReport> getReadMonth(Long start, String meterId, String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectEntity.getId());

        long end = calcMonthEnd(start, projectEntity.getTimezone());
        start = start < projectEntity.getCreateTime() ? projectEntity.getCreateTime() : start;
        if (end <= start) {
            return new HashMap<>(0);
        }
        List<AmmeterEntity> ammeterEntities = getAmmeterEntities(meterId, projectId);

        if (!ammeterEntities.isEmpty()) {
            // 原本的 根据 收益来的
            if (systemGroupEntity.getCalcEarningsController()) {
                return getReport(start, end, projectEntity, ammeterEntities, EmsConstants.MONTH);
            } else {
                // 未开收益
                return getReportFromDayReport(
                        start, end, projectEntity, ammeterEntities, EmsConstants.MONTH);
            }
        }
        return Map.of();
    }

    private Map<String, ReadReport> getReportFromDayReport(
            Long start,
            long end,
            ProjectEntity projectEntity,
            List<AmmeterEntity> ammeterEntities,
            String type) {
        Map<String, ReadReport> monthReportMap = new HashMap<>(ammeterEntities.size());
        Map<String, MeterContentData> meterContentDataMap =
                dataService.collectMeterDataPoint(projectEntity.getId());
        Map<String, List<ReadMonth>> map = new HashMap<>(ammeterEntities.size());
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            String meterId = ammeterEntity.getId();
            List<ReadMonth> list = new ArrayList<>(31);
            map.put(meterId, list);
        }
        String equipmentId = ammeterEntities.get(0).getId();

        EquipmentTypeEnums meterType =
                EquipmentTypeEnums.getMeterType(ammeterEntities.get(0).getType());
        Map<String, ReadMonth> initMap = new HashMap<>(12);
        DayReportRequest dayReportRequest =
                new DayReportRequest()
                        .setEquipmentId(equipmentId)
                        .setEquipmentTypeEnums(meterType)
                        .setRangeRequest(
                                new RangeRequest()
                                        .setStartDate(projectEntity.getCreateTime())
                                        .setEndDate(start));
        dayReportRequest.setProjectId(projectEntity.getId());

        if (projectEntity.getCreateTime() <= start) {
            RangeRequest preRangeRequest =
                    new RangeRequest()
                            .setStartDate(projectEntity.getCreateTime())
                            .setEndDate(start);
            dayReportRequest.setRangeRequest(preRangeRequest);
            dayReportRequest.setProjectId(projectEntity.getId());
            // core
            setSumFromDayReport(start, dayReportRequest, initMap);
        }
        RangeRequest rangeRequest = new RangeRequest().setStartDate(start).setEndDate(end);
        dayReportRequest.setRangeRequest(rangeRequest);
        setEveryFromDayReport(type, dayReportRequest, map);
        if (EmsConstants.MONTH.equals(type)) {
            for (AmmeterEntity ammeterEntity : ammeterEntities) {
                List<ReadMonth> list = map.get(ammeterEntity.getId());
                if (end > Instant.now().getEpochSecond() && !list.isEmpty()) {
                    ReadMonth readMonth = new ReadMonth();
                    ReadMonth lastReadMonth = list.get(list.size() - 1);
                    readMonth.setTime(lastReadMonth.getTime() + MyTimeUtil.ONE_DAY_SECONDS);
                    readMonth.init();
                    if ((lastReadMonth.getTime() + MyTimeUtil.ONE_DAY_SECONDS) < end) {
                        list.add(readMonth);
                    }
                }
            }
        }
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            String meterId = ammeterEntity.getId();
            List<ReadMonth> list = map.get(meterId);
            double totalCharging = 0;
            double totalDisCharging = 0;
            ReadMonth readMonthInit = initMap.get(meterId);
            if (readMonthInit == null) {
                readMonthInit = new ReadMonth();
                readMonthInit.init();
            }
            for (int i = 0; i < list.size(); i++) {
                ReadMonth readMonth = list.get(i);
                readMonth.setChargeReading(readMonthInit.getChargeReading() + totalCharging);
                readMonth.setDischargeReading(
                        readMonthInit.getDischargeReading() + totalDisCharging);
                // 最后一条是别的月或者别的年1月1日的数据，设为空 且不加
                if (i == list.size() - 1) {
                    readMonth.setChargeReadingQuantity(null);
                    readMonth.setDischargeReadingQuantity(null);
                } else {
                    totalCharging = totalCharging + readMonth.getChargeReadingQuantity();
                    totalDisCharging = totalDisCharging + readMonth.getDischargeReadingQuantity();
                }
            }
            ReadReport readMonthReport = new ReadReport();
            readMonthReport.setReadMonth(list);
            readMonthReport.setMonthIn(totalCharging);
            readMonthReport.setMonthOut(totalDisCharging);
            MeterContentData meterContentData = meterContentDataMap.get(meterId);
            Optional.ofNullable(meterContentData)
                    .ifPresent(
                            e -> {
                                readMonthReport.setCurrentIn(
                                        e.getAc_history_negative_power_in_kwh());
                                readMonthReport.setCurrentOut(
                                        e.getAc_history_positive_power_in_kwh());
                            });
            monthReportMap.put(meterId, readMonthReport);
        }
        return monthReportMap;
    }

    protected static long calcMonthEnd(Long start, String timezone) {
        LocalDateTime startTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(start),
                        MyTimeUtil.getZoneOffsetFromZoneCode(timezone));
        LocalDateTime endTime = startTime.plusMonths(1).plusHours(1);
        return endTime.atZone(MyTimeUtil.getZoneOffsetFromZoneCode(timezone))
                .toInstant()
                .getEpochSecond();
    }

    protected static long calcReadMonthEnd(Long start, String timezone) {
        LocalDateTime startTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(start),
                        MyTimeUtil.getZoneOffsetFromZoneCode(timezone));
        LocalDateTime endTime = startTime.plusMonths(1).plusDays(1);
        return endTime.atZone(MyTimeUtil.getZoneOffsetFromZoneCode(timezone))
                .toInstant()
                .getEpochSecond();
    }

    @Override
    public Map<String, ReadReport> getReadYear(Long start, String meterId, String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectEntity.getId());
        long end = calcYearEnd(start, projectEntity.getTimezone());
        start = start < projectEntity.getCreateTime() ? projectEntity.getCreateTime() : start;
        if (end <= start) {
            return new HashMap<>(0);
        }
        List<AmmeterEntity> ammeterEntities = getAmmeterEntities(meterId, projectId);
        Map<String, ReadReport> monthReportMap = new HashMap<>(ammeterEntities.size());
        if (!ammeterEntities.isEmpty()) {
            if (systemGroupEntity.getCalcEarningsController()) {
                return getReport(start, end, projectEntity, ammeterEntities, EmsConstants.YEAR);
            } else {
                // 未开收益
                return getReportFromDayReport(
                        start, end, projectEntity, ammeterEntities, EmsConstants.YEAR);
            }
        }

        return monthReportMap;
    }

    private static long calcYearEnd(Long start, String timezone) {
        LocalDateTime startTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(start),
                        MyTimeUtil.getZoneOffsetFromZoneCode(timezone));
        LocalDateTime endTime = startTime.plusYears(1).plusHours(1);
        return endTime.atZone(MyTimeUtil.getZoneOffsetFromZoneCode(timezone))
                .toInstant()
                .getEpochSecond();
    }

    private static long calcReadYearEnd(Long start, String timezone) {
        LocalDateTime startTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(start),
                        MyTimeUtil.getZoneOffsetFromZoneCode(timezone));
        LocalDateTime endTime = startTime.plusYears(1).plusMonths(1);
        return endTime.atZone(MyTimeUtil.getZoneOffsetFromZoneCode(timezone))
                .toInstant()
                .getEpochSecond();
    }

    private Map<String, ReadReport> getReport(
            Long start,
            long end,
            ProjectEntity projectEntity,
            List<AmmeterEntity> ammeterEntities,
            String type) {
        Map<String, ReadReport> monthReportMap = new HashMap<>(ammeterEntities.size());
        Map<String, MeterContentData> meterContentDataMap =
                dataService.collectMeterDataPoint(projectEntity.getId());
        Map<String, List<ReadMonth>> map = new HashMap<>(ammeterEntities.size());
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            String meterId = ammeterEntity.getId();
            List<ReadMonth> list = new ArrayList<>(31);
            map.put(meterId, list);
        }
        List<String> deviceIdList =
                ammeterEntities.stream().map(AmmeterEntity::getId).collect(Collectors.toList());
        Map<String, ReadMonth> initMap = new HashMap<>(12);
        ElectricRequest electricRequest = new ElectricRequest();
        if (projectEntity.getCreateTime() <= start) {
            electricRequest.setProjectId(projectEntity.getId());
            electricRequest.setDeviceIdList(deviceIdList);
            electricRequest.setStart(projectEntity.getCreateTime());
            electricRequest.setEnd(start);
            if (ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD
                    .getValue()
                    .equals(projectEntity.getElectricPriceType())) {
                setPeakValleysSum(start, electricRequest, initMap);
            } else {
                setNonPeakValleySum(start, electricRequest, initMap);
            }
        }
        electricRequest.setStart(start);
        electricRequest.setEnd(end);
        if (ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD
                .getValue()
                .equals(projectEntity.getElectricPriceType())) {
            setPeakValleyList(projectEntity, type, electricRequest, map);
        } else {
            setNonPeakValleyList(projectEntity, type, electricRequest, map);
        }
        if (EmsConstants.MONTH.equals(type)) {
            for (AmmeterEntity ammeterEntity : ammeterEntities) {
                List<ReadMonth> list = map.get(ammeterEntity.getId());
                if (end > Instant.now().getEpochSecond() && !list.isEmpty()) {
                    ReadMonth readMonth = new ReadMonth();
                    ReadMonth lastReadMonth = list.get(list.size() - 1);
                    readMonth.setTime(lastReadMonth.getTime() + MyTimeUtil.ONE_DAY_SECONDS);
                    readMonth.init();
                    if ((lastReadMonth.getTime() + MyTimeUtil.ONE_DAY_SECONDS) < end) {
                        list.add(readMonth);
                    }
                }
            }
        }
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            String meterId = ammeterEntity.getId();
            List<ReadMonth> list = map.get(meterId);
            double totalCharging = 0;
            double totalDisCharging = 0;
            ReadMonth readMonthInit = initMap.get(meterId);
            if (readMonthInit == null) {
                readMonthInit = new ReadMonth();
                readMonthInit.init();
            }
            for (int i = 0; i < list.size(); i++) {
                ReadMonth readMonth = list.get(i);
                readMonth.setChargeReading(readMonthInit.getChargeReading() + totalCharging);
                readMonth.setDischargeReading(
                        readMonthInit.getDischargeReading() + totalDisCharging);
                // 最后一条是别的月或者别的年1月1日的数据，设为空 且不加
                if (i == list.size() - 1) {
                    readMonth.setChargeReadingQuantity(null);
                    readMonth.setDischargeReadingQuantity(null);
                } else {
                    totalCharging = totalCharging + readMonth.getChargeReadingQuantity();
                    totalDisCharging = totalDisCharging + readMonth.getDischargeReadingQuantity();
                }
            }
            ReadReport readMonthReport = new ReadReport();
            readMonthReport.setReadMonth(list);
            readMonthReport.setMonthIn(totalCharging);
            readMonthReport.setMonthOut(totalDisCharging);
            MeterContentData meterContentData = meterContentDataMap.get(meterId);
            Optional.ofNullable(meterContentData)
                    .ifPresent(
                            e -> {
                                readMonthReport.setCurrentIn(
                                        e.getAc_history_negative_power_in_kwh());
                                readMonthReport.setCurrentOut(
                                        e.getAc_history_positive_power_in_kwh());
                            });
            monthReportMap.put(meterId, readMonthReport);
        }
        return monthReportMap;
    }

    private void setEveryFromDayReport(
            String type, DayReportRequest dayReportRequest, Map<String, List<ReadMonth>> map) {

        List<DayReportEntity> dayReportEntities = new ArrayList<>();
        if (EmsConstants.MONTH.equals(type)) {
            dayReportEntities =
                    dayReportService.findEquipmentDayReports(
                            dayReportRequest.getProjectId(),
                            dayReportRequest.getEquipmentId(),
                            dayReportRequest.getEquipmentTypeEnums(),
                            dayReportRequest.getRangeRequest());
        } else if (EmsConstants.YEAR.equals(type)) {
            dayReportEntities =
                    dayReportService.findEveryEquipmentMonthlyAggregatedReports(
                            dayReportRequest.getProjectId(),
                            dayReportRequest.getEquipmentId(),
                            dayReportRequest.getEquipmentTypeEnums(),
                            dayReportRequest.getRangeRequest());
        }
        for (DayReportEntity dayReportEntity : dayReportEntities) {
            ReadMonth readMonth = new ReadMonth();
            readMonth.setTime(dayReportEntity.getTime());
            readMonth.setChargeReadingQuantity(dayReportEntity.getInData());
            readMonth.setDischargeReadingQuantity(dayReportEntity.getOutData());
            map.get(dayReportRequest.getEquipmentId()).add(readMonth);
        }
    }

    private void setPeakValleyList(
            ProjectEntity projectEntity,
            String type,
            ElectricRequest electricRequest,
            Map<String, List<ReadMonth>> map) {
        List<ElectricEntity> electricMonthList = new ArrayList<>();
        if (EmsConstants.MONTH.equals(type)) {
            electricMonthList = electricService.queryElectricMonthGroupByDeviceId(electricRequest);
        } else if (EmsConstants.YEAR.equals(type)) {
            electricMonthList = electricService.queryElectricYear(electricRequest);
        }
        for (ElectricEntity electricEntity : electricMonthList) {
            ReadMonth readMonth = new ReadMonth();
            LocalDateTime localDateTime;
            if (EmsConstants.MONTH.equals(type)) {
                localDateTime =
                        LocalDateTime.of(
                                electricEntity.getYear(),
                                electricEntity.getMonth(),
                                electricEntity.getDay(),
                                0,
                                0,
                                0);
            } else {
                localDateTime =
                        LocalDateTime.of(
                                electricEntity.getYear(), electricEntity.getMonth(), 1, 0, 0, 0);
            }
            readMonth.setTime(
                    localDateTime.toEpochSecond(
                            MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone())));
            readMonth.setChargeReadingQuantity(electricEntity.getTotalChargeQuantity());
            readMonth.setDischargeReadingQuantity(electricEntity.getTotalDischargeQuantity());
            map.get(electricEntity.getDeviceId()).add(readMonth);
        }
    }

    private void setNonPeakValleyList(
            ProjectEntity projectEntity,
            String type,
            ElectricRequest electricRequest,
            Map<String, List<ReadMonth>> map) {
        List<ElectricDynamicPeriodEntity> electricMonthList = new ArrayList<>();
        if (EmsConstants.MONTH.equals(type)) {
            electricMonthList =
                    electricDynamicPeriodService.queryElectricMonthGroupByDeviceId(electricRequest);
        } else if (EmsConstants.YEAR.equals(type)) {
            electricMonthList = electricDynamicPeriodService.queryElectricYear(electricRequest);
        }
        for (ElectricDynamicPeriodEntity electricEntity : electricMonthList) {
            ReadMonth readMonth = new ReadMonth();
            LocalDateTime localDateTime;
            if (EmsConstants.MONTH.equals(type)) {
                localDateTime =
                        LocalDateTime.of(
                                electricEntity.getYear(),
                                electricEntity.getMonth(),
                                electricEntity.getDay(),
                                0,
                                0,
                                0);
            } else {
                localDateTime =
                        LocalDateTime.of(
                                electricEntity.getYear(), electricEntity.getMonth(), 1, 0, 0, 0);
            }
            readMonth.setTime(
                    localDateTime.toEpochSecond(
                            MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone())));
            readMonth.setChargeReadingQuantity(electricEntity.getChargeQuantity());
            readMonth.setDischargeReadingQuantity(electricEntity.getDischargeQuantity());
            map.get(electricEntity.getDeviceId()).add(readMonth);
        }
    }

    private void setNonPeakValleySum(
            Long start, ElectricRequest electricRequest, Map<String, ReadMonth> initMap) {
        List<ElectricDynamicPeriodEntity> electricDynamicPeriodEntities =
                electricDynamicPeriodService.queryElectricSumGroupByDeviceId(electricRequest);
        for (ElectricDynamicPeriodEntity electricEntity : electricDynamicPeriodEntities) {
            ReadMonth readMonthSum = new ReadMonth();
            readMonthSum.setTime(start);
            readMonthSum.setChargeReading(electricEntity.getChargeQuantity());
            readMonthSum.setDischargeReading(electricEntity.getDischargeQuantity());
            initMap.put(electricEntity.getDeviceId(), readMonthSum);
        }
    }

    private void setSumFromDayReport(
            Long start, DayReportRequest dayReportRequest, Map<String, ReadMonth> initMap) {
        DayReportEntity sumByCondition =
                dayReportService.findSumByCondition(
                        List.of(dayReportRequest.getProjectId()),
                        dayReportRequest.getEquipmentTypeEnums(),
                        dayReportRequest.getEquipmentId(),
                        dayReportRequest.getRangeRequest());
        ReadMonth readMonthSum = new ReadMonth();
        readMonthSum.setTime(start);
        readMonthSum.setChargeReading(sumByCondition.getInData());
        readMonthSum.setDischargeReading(sumByCondition.getOutData());
        initMap.put(dayReportRequest.getEquipmentId(), readMonthSum);
    }

    private void setPeakValleysSum(
            Long start, ElectricRequest electricRequest, Map<String, ReadMonth> initMap) {
        List<ElectricEntity> electricSumList =
                electricService.queryElectricSumGroupByDeviceId(electricRequest);
        for (ElectricEntity electricEntity : electricSumList) {
            ReadMonth readMonthSum = new ReadMonth();
            readMonthSum.setTime(start);
            readMonthSum.setChargeReading(electricEntity.getTotalChargeQuantity());
            readMonthSum.setDischargeReading(electricEntity.getTotalDischargeQuantity());
            initMap.put(electricEntity.getDeviceId(), readMonthSum);
        }
    }

    @Override
    public Map<String, ReadDetailReport> getReadMonthDetail(
            Long start, String meterId, String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        LocalDateTime startTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(start),
                        MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
        LocalDateTime endTime = startTime.plusMonths(1).plusHours(1);
        if (start < projectEntity.getCreateTime()) {
            start = projectEntity.getCreateTime();
        }
        long end =
                endTime.toInstant(MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()))
                        .getEpochSecond();
        if (end <= start) {
            return new HashMap<>(0);
        }
        List<AmmeterEntity> ammeterEntities = getAmmeterEntities(meterId, projectId);
        Map<String, ReadDetailReport> monthReportMap = new HashMap<>(ammeterEntities.size());
        if (!ammeterEntities.isEmpty()) {
            getReadDetailReport(
                    start, end, projectEntity, ammeterEntities, monthReportMap, EmsConstants.MONTH);
        }
        return monthReportMap;
    }

    @Override
    public Map<String, ReadDetailReport> getReadYearDetail(
            Long start, String meterId, String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        LocalDateTime startTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(start),
                        MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
        LocalDateTime endTime = startTime.plusYears(1).plusHours(1);
        if (start < projectEntity.getCreateTime()) {
            start = projectEntity.getCreateTime();
        }
        long end =
                endTime.toInstant(MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()))
                        .getEpochSecond();
        if (end <= start) {
            return new HashMap<>(0);
        }
        List<AmmeterEntity> ammeterEntities = getAmmeterEntities(meterId, projectId);
        Map<String, ReadDetailReport> yearReportMap = new HashMap<>(ammeterEntities.size());
        if (!ammeterEntities.isEmpty()) {
            getReadDetailReport(
                    start, end, projectEntity, ammeterEntities, yearReportMap, EmsConstants.YEAR);
        }
        return yearReportMap;
    }

    private List<AmmeterEntity> getAmmeterEntities(String meterId, String projectId) {
        List<AmmeterEntity> ammeterEntities = new ArrayList<>(1);
        if (EmsConstants.ALL.equals(meterId) || !StringUtils.hasLength(meterId)) {
            ammeterEntities =
                    ammeterService
                            .lambdaQuery()
                            .eq(AmmeterEntity::getMeterReading, true)
                            .eq(AmmeterEntity::getProjectId, projectId)
                            .list();
        } else {
            AmmeterEntity ammeterEntity = ammeterService.getById(meterId);
            ammeterEntities.add(ammeterEntity);
        }
        return ammeterEntities;
    }

    private void getReadDetailReport(
            Long start,
            long end,
            ProjectEntity projectEntity,
            List<AmmeterEntity> ammeterEntities,
            Map<String, ReadDetailReport> monthReportMap,
            String type) {
        Map<String, ReadMonthDetail> initMap = new HashMap<>(ammeterEntities.size());
        Map<String, List<ReadMonthDetail>> readMonthMap = new HashMap<>(ammeterEntities.size());
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            String meterId = ammeterEntity.getId();
            List<ReadMonthDetail> list = new ArrayList<>(31);
            readMonthMap.put(meterId, list);
        }
        List<String> deviceIdList =
                ammeterEntities.stream().map(AmmeterEntity::getId).collect(Collectors.toList());
        ElectricRequest electricRequest = new ElectricRequest();
        electricRequest.setStart(projectEntity.getCreateTime());
        electricRequest.setEnd(start);
        electricRequest.setProjectId(projectEntity.getId());
        electricRequest.setDeviceIdList(deviceIdList);
        List<ElectricEntity> electricSumList =
                electricService.queryElectricSumGroupByDeviceId(electricRequest);
        for (ElectricEntity electricEntity : electricSumList) {
            ReadMonthDetail readSumDetail = new ReadMonthDetail();
            readSumDetail.setTime(start);
            readSumDetail.setPeakChargeReading(electricEntity.getPeakChargeQuantity());
            readSumDetail.setVallyChargeReading(electricEntity.getVallyChargeQuantity());
            readSumDetail.setFlatChargeReading(electricEntity.getFlatChargeQuantity());
            readSumDetail.setTipChargeReading(electricEntity.getTipChargeQuantity());
            readSumDetail.setDeepVallyChargeReading(electricEntity.getDeepVallyChargeQuantity());
            readSumDetail.setPeakDischargeReading(electricEntity.getPeakDischargeQuantity());
            readSumDetail.setVallyDischargeReading(electricEntity.getVallyDischargeQuantity());
            readSumDetail.setFlatDischargeReading(electricEntity.getFlatDischargeQuantity());
            readSumDetail.setTipDischargeReading(electricEntity.getTipDischargeQuantity());
            readSumDetail.setDeepVallyDischargeReading(
                    electricEntity.getDeepVallyDischargeQuantity());
            initMap.put(electricEntity.getDeviceId(), readSumDetail);
        }
        electricRequest.setStart(start);
        electricRequest.setEnd(end);
        List<ElectricEntity> electricMonthList = new ArrayList<>();
        if (EmsConstants.MONTH.equals(type)) {
            electricMonthList = electricService.queryElectricMonthGroupByDeviceId(electricRequest);
        } else if (EmsConstants.YEAR.equals(type)) {
            electricMonthList = electricService.queryElectricYear(electricRequest);
        }
        for (ElectricEntity electricEntity : electricMonthList) {
            ReadMonthDetail readMonthDetail = new ReadMonthDetail();
            readMonthDetail.setPeakChargeReadingQuantity(electricEntity.getPeakChargeQuantity());
            readMonthDetail.setVallyChargeReadingQuantity(electricEntity.getVallyChargeQuantity());
            readMonthDetail.setFlatChargeReadingQuantity(electricEntity.getFlatChargeQuantity());
            readMonthDetail.setTipChargeReadingQuantity(electricEntity.getTipChargeQuantity());
            readMonthDetail.setDeepVallyChargeReadingQuantity(
                    electricEntity.getDeepVallyChargeQuantity());
            readMonthDetail.setPeakDischargeReadingQuantity(
                    electricEntity.getPeakDischargeQuantity());
            readMonthDetail.setVallyDischargeReadingQuantity(
                    electricEntity.getVallyDischargeQuantity());
            readMonthDetail.setFlatDischargeReadingQuantity(
                    electricEntity.getFlatDischargeQuantity());
            readMonthDetail.setTipDischargeReadingQuantity(
                    electricEntity.getTipDischargeQuantity());
            readMonthDetail.setDeepVallyDischargeReadingQuantity(
                    electricEntity.getDeepVallyDischargeQuantity());
            LocalDateTime localDateTime;
            if (EmsConstants.MONTH.equals(type)) {
                localDateTime =
                        LocalDateTime.of(
                                electricEntity.getYear(),
                                electricEntity.getMonth(),
                                electricEntity.getDay(),
                                0,
                                0,
                                0);
            } else {
                localDateTime =
                        LocalDateTime.of(
                                electricEntity.getYear(), electricEntity.getMonth(), 1, 0, 0, 0);
            }
            readMonthDetail.setTime(
                    localDateTime.toEpochSecond(
                            MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone())));
            readMonthMap.get(electricEntity.getDeviceId()).add(readMonthDetail);
        }
        if (EmsConstants.MONTH.equals(type)) {
            for (AmmeterEntity ammeterEntity : ammeterEntities) {
                List<ReadMonthDetail> list = readMonthMap.get(ammeterEntity.getId());
                if (end > Instant.now().getEpochSecond() && !list.isEmpty()) {
                    ReadMonthDetail readMonthDetail = new ReadMonthDetail();
                    ReadMonthDetail lastReadMonthDetail = list.get(list.size() - 1);
                    readMonthDetail.setTime(
                            lastReadMonthDetail.getTime() + MyTimeUtil.ONE_DAY_SECONDS);
                    if ((lastReadMonthDetail.getTime() + MyTimeUtil.ONE_DAY_SECONDS) < end) {
                        list.add(readMonthDetail);
                    }
                }
            }
        }
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            String meterId = ammeterEntity.getId();
            List<ReadMonthDetail> list = readMonthMap.get(meterId);
            double totalPeakCharging = 0;
            double totalVallyCharging = 0;
            double totalFlatCharging = 0;
            double totalTipCharging = 0;
            double totalDeepVallyCharging = 0;
            double totalPeakDischarging = 0;
            double totalVallyDischarging = 0;
            double totalFlatDischarging = 0;
            double totalTipDischarging = 0;
            double totalDeepVallyDischarging = 0;
            ReadMonthDetail readMonthDetailInit = initMap.get(meterId);
            if (readMonthDetailInit == null) {
                readMonthDetailInit = new ReadMonthDetail();
            }
            for (ReadMonthDetail readMonthDetail : list) {
                readMonthDetail.setPeakChargeReading(
                        readMonthDetailInit.getPeakChargeReading() + totalPeakCharging);
                totalPeakCharging =
                        totalPeakCharging + readMonthDetail.getPeakChargeReadingQuantity();
                readMonthDetail.setVallyChargeReading(
                        readMonthDetailInit.getVallyChargeReading() + totalVallyCharging);
                totalVallyCharging =
                        totalVallyCharging + readMonthDetail.getVallyChargeReadingQuantity();
                readMonthDetail.setFlatChargeReading(
                        readMonthDetailInit.getFlatChargeReading() + totalFlatCharging);
                totalFlatCharging =
                        totalFlatCharging + readMonthDetail.getFlatChargeReadingQuantity();
                readMonthDetail.setTipChargeReading(
                        readMonthDetailInit.getTipChargeReading() + totalTipCharging);
                totalTipCharging = totalTipCharging + readMonthDetail.getTipChargeReadingQuantity();
                readMonthDetail.setDeepVallyChargeReading(
                        readMonthDetailInit.getDeepVallyChargeReading() + totalDeepVallyCharging);
                totalDeepVallyCharging =
                        totalDeepVallyCharging
                                + readMonthDetail.getDeepVallyChargeReadingQuantity();
                readMonthDetail.setPeakDischargeReading(
                        readMonthDetailInit.getPeakDischargeReading() + totalPeakDischarging);
                totalPeakDischarging =
                        totalPeakDischarging + readMonthDetail.getPeakDischargeReadingQuantity();
                readMonthDetail.setVallyDischargeReading(
                        readMonthDetailInit.getVallyDischargeReading() + totalVallyDischarging);
                totalVallyDischarging =
                        totalVallyDischarging + readMonthDetail.getVallyDischargeReadingQuantity();
                readMonthDetail.setFlatDischargeReading(
                        readMonthDetailInit.getFlatDischargeReading() + totalFlatDischarging);
                totalFlatDischarging =
                        totalFlatDischarging + readMonthDetail.getFlatDischargeReadingQuantity();
                readMonthDetail.setTipDischargeReading(
                        readMonthDetailInit.getTipDischargeReading() + totalTipDischarging);
                totalTipDischarging =
                        totalTipDischarging + readMonthDetail.getTipDischargeReadingQuantity();
                readMonthDetail.setDeepVallyDischargeReading(
                        readMonthDetailInit.getDeepVallyDischargeReading()
                                + totalDeepVallyDischarging);
                totalDeepVallyDischarging =
                        totalDeepVallyDischarging
                                + readMonthDetail.getDeepVallyDischargeReadingQuantity();
            }
            ReadDetailReport readMonthDetailReport = new ReadDetailReport();
            readMonthDetailReport.setPeakChargeReadingQuantity(totalPeakCharging);
            readMonthDetailReport.setVallyChargeReadingQuantity(totalVallyCharging);
            readMonthDetailReport.setFlatChargeReadingQuantity(totalFlatCharging);
            readMonthDetailReport.setTipChargeReadingQuantity(totalTipCharging);
            readMonthDetailReport.setDeepVallyDischargeReadingQuantity(totalDeepVallyCharging);
            readMonthDetailReport.setPeakDischargeReadingQuantity(totalPeakDischarging);
            readMonthDetailReport.setVallyDischargeReadingQuantity(totalVallyDischarging);
            readMonthDetailReport.setFlatDischargeReadingQuantity(totalFlatDischarging);
            readMonthDetailReport.setTipDischargeReadingQuantity(totalTipDischarging);
            readMonthDetailReport.setDeepVallyChargeReadingQuantity(totalDeepVallyDischarging);
            readMonthDetailReport.setReadMonthDetail(list);
            monthReportMap.put(meterId, readMonthDetailReport);
        }
    }

    @Override
    public Map<String, ReadReport> getReadMeter(Long start, ReportService.MeterHolder meterHolder) {
        ProjectEntity projectEntity = projectService.getById(meterHolder.projectId());
        long end =
                EmsConstants.MONTH.equals(meterHolder.period())
                        ? calcReadMonthEnd(start, projectEntity.getTimezone())
                        : calcReadYearEnd(start, projectEntity.getTimezone());
        start = start < projectEntity.getCreateTime() ? projectEntity.getCreateTime() : start;
        ReadReport readMonthReport = new ReadReport();
        List<ReadMonth> readMonth = getReadingMeter(start, end, meterHolder);
        Pair<ReadMonth, List<ReadMonth>> pair = calcReadMonth(readMonth);
        setMeterHistory(meterHolder.meterId(), meterHolder.projectId(), readMonthReport);
        readMonthReport.setMonthIn(pair.getFirst().getChargeReadingQuantity());
        readMonthReport.setMonthOut(pair.getFirst().getDischargeReadingQuantity());
        readMonthReport.setReadMonth(pair.getSecond());
        return Map.of(meterHolder.meterId(), readMonthReport);
    }

    public abstract List<ReadMonth> getReadingMeter(
            long start, long end, ReportService.MeterHolder meterHolder);

    public Pair<ReadMonth, List<ReadMonth>> calcReadMonth(List<ReadMonth> readMonthList) {
        List<ReadMonth> readMonths = new ArrayList<>();
        double monthInSum = 0;
        double monthOutSum = 0;
        if (readMonthList.size() <= 1) {
            return Pair.of(new ReadMonth(), List.of());
        }
        for (int i = 0; i < readMonthList.size() - 1; i++) {
            ReadMonth current = readMonthList.get(i);
            ReadMonth next = readMonthList.get(i + 1);
            if (next.getTime() > Instant.now().getEpochSecond()) {
                continue;
            }
            if (current.getChargeReading() == null || next.getChargeReading() == null) {
                continue;
            }
            if (current.getDischargeReading() == null || next.getDischargeReading() == null) {
                continue;
            }
            double in = next.getChargeReading() - current.getChargeReading();
            double out = next.getDischargeReading() - current.getDischargeReading();
            current.setChargeReadingQuantity(in);
            current.setDischargeReadingQuantity(out);
            monthInSum += in;
            monthOutSum += out;
            readMonths.add(current);
            if (i == readMonthList.size() - 2) {
                readMonths.add(next);
            }
        }
        ReadMonth readMonth = new ReadMonth();
        readMonth.setChargeReadingQuantity(monthInSum);
        readMonth.setDischargeReadingQuantity(monthOutSum);
        return Pair.of(readMonth, readMonths);
    }

    public void setMeterHistory(String meterId, String projectId, ReadReport readMonthReport) {
        Map<String, MeterContentData> meterContentDataMap =
                dataService.collectMeterDataPoint(projectId);
        MeterContentData meterContentData = meterContentDataMap.get(meterId);
        Optional.ofNullable(meterContentData)
                .ifPresent(
                        e -> {
                            readMonthReport.setCurrentIn(e.getAc_history_negative_power_in_kwh());
                            readMonthReport.setCurrentOut(e.getAc_history_positive_power_in_kwh());
                        });
    }

    @Override
    public Map<String, ReadDetailReport> getReadMeterDetail(Long start, MeterHolder meterHolder) {
        ProjectEntity projectEntity = projectService.getById(meterHolder.projectId());
        long end =
                EmsConstants.MONTH.equals(meterHolder.period())
                        ? calcReadMonthEnd(start, projectEntity.getTimezone())
                        : calcReadYearEnd(start, projectEntity.getTimezone());
        start = start < projectEntity.getCreateTime() ? projectEntity.getCreateTime() : start;
        List<ReadMonthDetail> detailList = getReadingMeterDetail(start, end, meterHolder);
        ReadDetailReport readMonthDetailReport = handleSumReport(detailList);
        return Map.of(meterHolder.meterId(), readMonthDetailReport);
    }

    private ReadDetailReport handleSumReport(List<ReadMonthDetail> detailList) {
        List<ReadMonthDetail> readMonths = new ArrayList<>();
        ReadDetailReport readMonthDetailReport = new ReadDetailReport();
        double totalPeakDischargeReadingQuantity = 0.0;
        double totalValleyDischargeReadingQuantity = 0.0;
        double totalFlatDischargeReadingQuantity = 0.0;
        double totalTipDischargeReadingQuantity = 0.0;
        double totalDeepValleyDischargeReadingQuantity = 0.0;
        double totalPeakChargeReadingQuantity = 0.0;
        double totalValleyChargeReadingQuantity = 0.0;
        double totalFlatChargeReadingQuantity = 0.0;
        double totalTipChargeReadingQuantity = 0.0;
        double totalDeepValleyChargeReadingQuantity = 0.0;
        for (int i = 0; i < detailList.size() - 1; i++) {
            ReadMonthDetail current = detailList.get(i);
            ReadMonthDetail next = detailList.get(i + 1);
            if (next.getTime() > Instant.now().getEpochSecond()) {
                continue;
            }
            //            if (current.getFlatChargeReading() == 0 || next.getFlatChargeReading() ==
            // 0) {
            //                continue;
            //            }
            double peakDischargeReadingQuantity =
                    next.getPeakDischargeReading() - current.getPeakDischargeReading();
            double vallyDischargeReadingQuantity =
                    next.getVallyDischargeReading() - current.getVallyDischargeReading();
            double flatDischargeReadingQuantity =
                    next.getFlatDischargeReading() - current.getFlatDischargeReading();
            double tipDischargeReadingQuantity =
                    next.getTipDischargeReading() - current.getTipDischargeReading();
            double deepVallyDischargeReadingQuantity =
                    next.getDeepVallyDischargeReading() - current.getDeepVallyDischargeReading();
            double peakChargeReadingQuantity =
                    next.getPeakChargeReading() - current.getPeakChargeReading();
            double vallyChargeReadingQuantity =
                    next.getVallyChargeReading() - current.getVallyChargeReading();
            double flatChargeReadingQuantity =
                    next.getFlatChargeReading() - current.getFlatChargeReading();
            double tipChargeReadingQuantity =
                    next.getTipChargeReading() - current.getTipChargeReading();
            double deepVallyChargeReadingQuantity =
                    next.getDeepVallyChargeReading() - current.getDeepVallyChargeReading();
            current.setPeakDischargeReadingQuantity(peakDischargeReadingQuantity);
            current.setVallyDischargeReadingQuantity(vallyDischargeReadingQuantity);
            current.setFlatDischargeReadingQuantity(flatDischargeReadingQuantity);
            current.setTipDischargeReadingQuantity(tipDischargeReadingQuantity);
            current.setDeepVallyDischargeReadingQuantity(deepVallyDischargeReadingQuantity);
            current.setPeakChargeReadingQuantity(peakChargeReadingQuantity);
            current.setVallyChargeReadingQuantity(vallyChargeReadingQuantity);
            current.setFlatChargeReadingQuantity(flatChargeReadingQuantity);
            current.setTipChargeReadingQuantity(tipChargeReadingQuantity);
            current.setDeepVallyChargeReadingQuantity(deepVallyChargeReadingQuantity);
            totalPeakDischargeReadingQuantity += peakDischargeReadingQuantity;
            totalValleyDischargeReadingQuantity += vallyDischargeReadingQuantity;
            totalFlatDischargeReadingQuantity += flatDischargeReadingQuantity;
            totalTipDischargeReadingQuantity += tipDischargeReadingQuantity;
            totalDeepValleyDischargeReadingQuantity += deepVallyDischargeReadingQuantity;
            totalPeakChargeReadingQuantity += peakChargeReadingQuantity;
            totalValleyChargeReadingQuantity += vallyChargeReadingQuantity;
            totalFlatChargeReadingQuantity += flatChargeReadingQuantity;
            totalTipChargeReadingQuantity += tipChargeReadingQuantity;
            totalDeepValleyChargeReadingQuantity += deepVallyChargeReadingQuantity;
            readMonths.add(current);
            if (i == detailList.size() - 2) {
                readMonths.add(next);
            }
        }
        readMonthDetailReport.setPeakDischargeReadingQuantity(totalPeakDischargeReadingQuantity);
        readMonthDetailReport.setVallyDischargeReadingQuantity(totalValleyDischargeReadingQuantity);
        readMonthDetailReport.setFlatDischargeReadingQuantity(totalFlatDischargeReadingQuantity);
        readMonthDetailReport.setTipDischargeReadingQuantity(totalTipDischargeReadingQuantity);
        readMonthDetailReport.setDeepVallyDischargeReadingQuantity(
                totalDeepValleyDischargeReadingQuantity);
        readMonthDetailReport.setPeakChargeReadingQuantity(totalPeakChargeReadingQuantity);
        readMonthDetailReport.setVallyChargeReadingQuantity(totalValleyChargeReadingQuantity);
        readMonthDetailReport.setFlatChargeReadingQuantity(totalFlatChargeReadingQuantity);
        readMonthDetailReport.setTipChargeReadingQuantity(totalTipChargeReadingQuantity);
        readMonthDetailReport.setDeepVallyChargeReadingQuantity(
                totalDeepValleyChargeReadingQuantity);
        readMonthDetailReport.setReadMonthDetail(readMonths);
        return readMonthDetailReport;
    }

    public abstract List<ReadMonthDetail> getReadingMeterDetail(
            long start, long end, MeterHolder meterHolder);
}
