package com.wifochina.modules.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.report.entity.MeterDataRemediesEntity;
import com.wifochina.modules.report.request.RemediesMeterQueryRequest;
import com.wifochina.modules.report.request.RemediesMeterRequest;

import java.util.List;

/**
 * 数据状态 服务类
 *
 * <AUTHOR>
 * @since 2024-10-16
 */
public interface MeterDataRemediesService extends IService<MeterDataRemediesEntity> {
    void updateRemediesData(RemediesMeterRequest remediesRequest, String projectId, String type);

    List<MeterDataRemediesEntity> getMeterRemediesData(
            RemediesMeterQueryRequest remediesQueryRequest, String projectId, String type);

    List<MeterDataRemediesEntity> getMeterRemediesData(
            String projectId, Long day, String reportType);
}
