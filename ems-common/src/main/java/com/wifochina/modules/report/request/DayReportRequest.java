package com.wifochina.modules.report.request;

import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.modules.diagram.request.RequestCommon;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2025/7/16 17:10.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DayReportRequest extends RequestCommon {

    private RangeRequest rangeRequest;
    private EquipmentTypeEnums equipmentTypeEnums;
    private String equipmentId;
}
