package com.wifochina.modules.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.report.entity.DayReportEntity;
import com.wifochina.modules.report.mapper.DayReportMapper;
import com.wifochina.modules.report.service.DayReportService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created on 2025/2/19 14:45.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class DayReportServiceImpl extends ServiceImpl<DayReportMapper, DayReportEntity>
        implements DayReportService {

    @Override
    public List<DayReportEntity> fillExistingIds(List<DayReportEntity> dayReportEntities) {
        List<DayReportEntity> existingRecords = getBaseMapper().findExistingIds(dayReportEntities);
        // 2. 转成 Map（Key 是 组合 ID，Value 是数据库 ID）
        Map<String, Integer> existingIdMap =
                existingRecords.stream()
                        .collect(
                                Collectors.toMap(
                                        record ->
                                                record.getProjectId()
                                                        + "_"
                                                        + record.getEquipmentId()
                                                        + "_"
                                                        + record.getTime(),
                                        DayReportEntity::getId));
        // 3. 遍历 dayReportEntities，匹配已存在的 ID
        if (!existingIdMap.isEmpty()) {
            dayReportEntities.forEach(
                    entity -> {
                        String key =
                                entity.getProjectId()
                                        + "_"
                                        + entity.getEquipmentId()
                                        + "_"
                                        + entity.getTime();
                        if (existingIdMap.containsKey(key)) {
                            entity.setId(existingIdMap.get(key)); // 赋值 ID，表示更新
                        }
                    });
        }
        return dayReportEntities;
    }

    @Override
    public List<DayReportEntity> findAllByRangeAndProjectId(
            String projectId, RangeRequest rangeRequest) {
        return this.baseMapper.selectList(
                new LambdaQueryWrapper<DayReportEntity>()
                        .eq(DayReportEntity::getProjectId, projectId)
                        .between(
                                DayReportEntity::getTime,
                                rangeRequest.getStartDate(),
                                rangeRequest.getEndDate()));
    }

    @Override
    public List<DayReportEntity> findAllByRangeEquipmentTypeAndProjectId(
            String projectId, EquipmentTypeEnums equipmentTypeEnums, RangeRequest rangeRequest) {
        return this.baseMapper.selectList(
                new LambdaQueryWrapper<DayReportEntity>()
                        .eq(DayReportEntity::getProjectId, projectId)
                        .eq(DayReportEntity::getEquipmentType, equipmentTypeEnums.name())
                        .between(
                                DayReportEntity::getTime,
                                rangeRequest.getStartDate(),
                                rangeRequest.getEndDate()));
    }

    @Override
    public DayReportEntity findSumByCondition(
            List<String> projectIds,
            EquipmentTypeEnums equipmentTypeEnums,
            String equipmentId,
            RangeRequest rangeRequest) {
        if (rangeRequest == null) {
            rangeRequest = new RangeRequest();
        }
        if (projectIds.isEmpty()) {
            log.warn("findSumByCondition projectIds is null not to search");
            return null;
        }
        return this.baseMapper.findSumByCondition(
                projectIds,
                equipmentTypeEnums == null ? null : equipmentTypeEnums.name(),
                StringUtil.isEmpty(equipmentId) ? null : equipmentId,
                rangeRequest.getStartDate(),
                rangeRequest.getEndDate());
    }

    @Override
    public List<DayReportEntity> findDayAggregatedReports(
            String projectId, RangeRequest rangeRequest) {
        return this.baseMapper.findDayAggregatedReports(
                projectId, rangeRequest.getStartDate(), rangeRequest.getEndDate());
    }

    @Override
    public List<DayReportEntity> findDayAllReports(
            String projectId, EquipmentTypeEnums equipmentTypeEnums, RangeRequest rangeRequest) {
        return this.baseMapper.findDayAllReports(
                projectId,
                equipmentTypeEnums.name(),
                rangeRequest.getStartDate(),
                rangeRequest.getEndDate());
    }

    /**
     * 月度的, Group by equipment id
     *
     * @param projectId
     * @param rangeRequest
     * @return
     */
    @Override
    public List<DayReportEntity> findEveryEquipmentMonthlyAggregatedReports(
            String projectId,
            String equipmentId,
            EquipmentTypeEnums equipmentTypeEnums,
            RangeRequest rangeRequest) {
        return this.baseMapper.findEveryEquipmentMonthlyAggregatedReports(
                projectId,
                equipmentTypeEnums == null ? null : equipmentTypeEnums.name(),
                StringUtil.isEmpty(equipmentId) ? null : equipmentId,
                rangeRequest.getStartDate(),
                rangeRequest.getEndDate());
    }

    /**
     * 月度的, Group by equipment type
     *
     * @param projectId
     * @param rangeRequest
     * @return
     */
    @Override
    public List<DayReportEntity> findMonthlyAggregatedReports(
            String projectId, RangeRequest rangeRequest) {
        return this.baseMapper.findMonthlyAggregatedReports(
                projectId, rangeRequest.getStartDate(), rangeRequest.getEndDate());
    }

    @Override
    public List<DayReportEntity> findEquipmentDayReports(
            String projectId,
            String equipmentId,
            EquipmentTypeEnums equipmentTypeEnums,
            RangeRequest rangeRequest) {
        return this.baseMapper.findEquipmentDayReports(
                projectId,
                equipmentId,
                equipmentTypeEnums.name(),
                rangeRequest.getStartDate(),
                rangeRequest.getEndDate());
    }
}
