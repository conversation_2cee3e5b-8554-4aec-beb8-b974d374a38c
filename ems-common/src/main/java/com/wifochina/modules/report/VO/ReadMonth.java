package com.wifochina.modules.report.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-13 4:20 PM
 */
@Data
public class ReadMonth {
    @ApiModelProperty(value = "时间")
    private Long time;

    @ApiModelProperty(value = "放电量读数")
    private Double dischargeReading;

    @ApiModelProperty(value = "放电量")
    private Double dischargeReadingQuantity;

    @ApiModelProperty(value = "充电量读数")
    private Double chargeReading;

    @ApiModelProperty(value = "充电量")
    private Double chargeReadingQuantity;

    public void init(){
        dischargeReading = 0d;
        dischargeReadingQuantity = 0d;
        chargeReading = 0d;
        chargeReadingQuantity = 0d;
    }
}
