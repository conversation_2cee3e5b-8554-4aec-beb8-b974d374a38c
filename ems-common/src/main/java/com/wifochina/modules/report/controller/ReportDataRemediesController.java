package com.wifochina.modules.report.controller;

import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.report.entity.ReportDataRemediesEntity;
import com.wifochina.modules.report.request.RemediesQueryRequest;
import com.wifochina.modules.report.request.RemediesReportRequest;
import com.wifochina.modules.report.service.ReportDataRemediesService;
import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RequestMapping("/report")
@RestController
@Api(tags = "15-报表")
@RequiredArgsConstructor
public class ReportDataRemediesController {

    private final ReportDataRemediesService reportDataRemediesService;
    private final LogService logService;

    /** 电站日报 */
    @PostMapping("/dailyReportRemedies")
    @ApiOperation("日报补值")
    @PreAuthorize("hasAuthority('/report/remedies')")
    @Log(module = "REMEDIES_WHOLE_STATION", methods = "DAILY_REMEDIES", type = OperationType.ADD)
    public Result<Object> dailyReportRemedies(@RequestBody RemediesReportRequest remediesRequest) {
        reportDataRemediesService.updateRemediesData(
                remediesRequest, WebUtils.projectId.get(), EmsConstants.DAILY);
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("REPORT")
        //                        .method("DAILY_REMEDIES")
        //                        .object(remediesRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.REPORT_DAILY_REMEDIES_TRACE_FORMAT,
        //                                        WebUtils.projectId.get()))
        //                        .build());
        return Result.success();
    }

    /** 电站月报 */
    @PostMapping("/monthReportRemedies")
    @ApiOperation("月报补值")
    @PreAuthorize("hasAuthority('/report/remedies')")
    @Log(module = "REMEDIES_WHOLE_STATION", methods = "MONTH_REMEDIES", type = OperationType.ADD)
    public Result<Object> monthReportRemedies(@RequestBody RemediesReportRequest remediesRequest) {
        reportDataRemediesService.updateRemediesData(
                remediesRequest, WebUtils.projectId.get(), EmsConstants.MONTH);
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("REPORT")
        //                        .method("MONTH_REMEDIES")
        //                        .object(remediesRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.REPORT_MONTH_REMEDIES_TRACE_FORMAT,
        //                                        WebUtils.projectId.get()))
        //                        .build());
        return Result.success();
    }

    /** 电站年报 */
    @PostMapping("/yearReportRemedies")
    @ApiOperation("年报补值")
    @PreAuthorize("hasAuthority('/report/remedies')")
    @Log(module = "REMEDIES_WHOLE_STATION", methods = "YEAR_REMEDIES", type = OperationType.ADD)
    public Result<Map<String, Object>> yearReportRemedies(
            @RequestBody RemediesReportRequest remediesRequest) {
        reportDataRemediesService.updateRemediesData(
                remediesRequest, WebUtils.projectId.get(), EmsConstants.YEAR);
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("REPORT")
        //                        .method("YEAR_REMEDIES")
        //                        .object(remediesRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.REPORT_YEAR_REMEDIES_TRACE_FORMAT,
        //                                        WebUtils.projectId.get()))
        //                        .build());
        return Result.success();
    }

    /** 电站日报 */
    @PostMapping("/dailyReportRecord")
    @ApiOperation("日报补值记录")
    @PreAuthorize("hasAuthority('/report/remedies/query')")
    public Result<List<ReportDataRemediesEntity>> dailyReportRecord(
            @RequestBody RemediesQueryRequest remediesQueryRequest) {
        List<ReportDataRemediesEntity> list =
                reportDataRemediesService.getReportRemediesData(
                        remediesQueryRequest, WebUtils.projectId.get(), EmsConstants.DAILY);
        return Result.success(list);
    }

    /** 电站月报 */
    @PostMapping("/monthReportRecord")
    @ApiOperation("月报补值记录")
    @PreAuthorize("hasAuthority('/report/remedies/query')")
    public Result<List<ReportDataRemediesEntity>> monthReportRecord(
            @RequestBody RemediesQueryRequest remediesQueryRequest) {
        List<ReportDataRemediesEntity> list =
                reportDataRemediesService.getReportRemediesData(
                        remediesQueryRequest, WebUtils.projectId.get(), EmsConstants.MONTH);
        return Result.success(list);
    }

    /** 电站年报 */
    @PostMapping("/yearReportRecord")
    @ApiOperation("年报补值记录")
    @PreAuthorize("hasAuthority('/report/remedies/query')")
    public Result<List<ReportDataRemediesEntity>> yearReportRecord(
            @RequestBody RemediesQueryRequest remediesQueryRequest) {
        List<ReportDataRemediesEntity> list =
                reportDataRemediesService.getReportRemediesData(
                        remediesQueryRequest, WebUtils.projectId.get(), EmsConstants.YEAR);
        return Result.success(list);
    }
}
