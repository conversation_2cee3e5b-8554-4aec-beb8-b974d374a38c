package com.wifochina.modules.report.job.init;

import com.wifochina.modules.demand.service.DemandQuartzService;
import com.wifochina.modules.income.job.CustomProjectQuartzService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.modules.report.job.jobservice.DayReportJobQuartzServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-02-19 10:31:47
 */
@Component
public class DayReportJobStart implements CommandLineRunner {
    @Resource private ProjectService projectService;

    @Autowired private DayReportJobQuartzServiceImpl dayReportJobQuartzService;

    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public void run(String... args) {
        List<ProjectEntity> projectEntities =
                projectService.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list();

        List<String> timezoneList =
                projectEntities.stream()
                        .map(ProjectEntity::getTimezone)
                        .distinct()
                        .collect(Collectors.toList());
        // dayReportJobQuartzService.addJob("Asia/Shanghai");
        for (String timezone : timezoneList) {
            threadPoolTaskExecutor.submit(
                    () -> {
                        // 每个timezone 一个job
                        dayReportJobQuartzService.addJob(timezone);
                    });
        }
    }
}
