package com.wifochina.modules.report.job;

import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.report.service.DayReportCacheService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @since 2024-03-14 2:02 PM
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class DayReportJob extends QuartzJobBean {

    private final ProjectService projectService;

    private final DayReportCacheService dayReportCacheService;

    public static void main(String[] args) {
        long yesterdayZero =
                MyTimeUtil.getTodayZeroTime("Asia/Shanghai") - EmsConstants.ONE_DAY_SECOND;
        long yesterdayEndTime = MyTimeUtil.getTodayZeroTime("Asia/Shanghai") - 1;
        System.out.println(yesterdayZero);
        System.out.println(yesterdayEndTime);
    }

    @Override
    protected void executeInternal(JobExecutionContext context) {
        String timezone = (String) context.getJobDetail().getJobDataMap().get("timezone");
        List<ProjectEntity> projectEntities = projectService.getProjectsByTimeZone(timezone);
        for (ProjectEntity projectEntity : projectEntities) {
            try {
                log.info("dayReportJob start");
                saveYesterdayDayReport(projectEntity);
                log.info("dayReportJob  end");
            } catch (Exception e) {
                log.error("项目 {} 保存昨日电量收益异常 {}", projectEntity.getProjectName(), e.getMessage());
            }
        }
    }

    public void saveYesterdayDayReport(ProjectEntity projectEntity) {
        long yesterdayZero =
                MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone())
                        - EmsConstants.ONE_DAY_SECOND;
        long yesterdayEndTime = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone()) - 1;
        dayReportCacheService.saveDayReport(
                projectEntity,
                new RangeRequest().setStartDate(yesterdayZero).setEndDate(yesterdayEndTime));
    }
}
