package com.wifochina.modules.report.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-13 4:20 PM
 */
@Data
public class ReadMonthDetail {

    @ApiModelProperty(value = "时间")
    private Long time;

    @ApiModelProperty(value = "峰放电量读数")
    private double peakDischargeReading;

    @ApiModelProperty(value = "谷放电量读数")
    private double vallyDischargeReading;

    @ApiModelProperty(value = "平放电量读数")
    private double flatDischargeReading;

    @ApiModelProperty(value = "尖放电量读数")
    private double tipDischargeReading;

    @ApiModelProperty(value = "深谷放电量读数")
    private double deepVallyDischargeReading;

    @ApiModelProperty(value = "峰放电量")
    private double peakDischargeReadingQuantity;

    @ApiModelProperty(value = "谷放电量")
    private double vallyDischargeReadingQuantity;

    @ApiModelProperty(value = "平放电量")
    private double flatDischargeReadingQuantity;

    @ApiModelProperty(value = "尖放电量")
    private double tipDischargeReadingQuantity;

    @ApiModelProperty(value = "深谷放电量")
    private double deepVallyDischargeReadingQuantity;

    @ApiModelProperty(value = "峰充电量读数")
    private double peakChargeReading;

    @ApiModelProperty(value = "谷充电量读数")
    private double vallyChargeReading;

    @ApiModelProperty(value = "平充电量读数")
    private double flatChargeReading;

    @ApiModelProperty(value = "尖充电量读数")
    private double tipChargeReading;

    @ApiModelProperty(value = "深谷充电量读数")
    private double deepVallyChargeReading;

    @ApiModelProperty(value = "峰充电量")
    private double peakChargeReadingQuantity;

    @ApiModelProperty(value = "谷充电量")
    private double vallyChargeReadingQuantity;

    @ApiModelProperty(value = "平充电量")
    private double flatChargeReadingQuantity;

    @ApiModelProperty(value = "尖充电量")
    private double tipChargeReadingQuantity;

    @ApiModelProperty(value = "深谷充电量")
    private double deepVallyChargeReadingQuantity;

}
