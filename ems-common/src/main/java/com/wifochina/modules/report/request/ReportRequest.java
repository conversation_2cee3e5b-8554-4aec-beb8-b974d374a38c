package com.wifochina.modules.report.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * reportRequest
 * 
 * @date 5/6/2022 11:17 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "按日期查询报表")
public class ReportRequest {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;

    @ApiModelProperty(value = "UTC与客户端的时区差,东8区，传入-8", required = true)
    private Integer duration;

    private String projectId;
}
