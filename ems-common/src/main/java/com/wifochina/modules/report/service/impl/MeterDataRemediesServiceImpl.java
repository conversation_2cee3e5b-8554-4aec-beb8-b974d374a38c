package com.wifochina.modules.report.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.report.entity.MeterDataRemediesEntity;
import com.wifochina.modules.report.mapper.MeterDataRemediesMapper;
import com.wifochina.modules.report.request.RemediesMeterQueryRequest;
import com.wifochina.modules.report.request.RemediesMeterRequest;
import com.wifochina.modules.report.service.MeterDataRemediesService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据状态 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-16
 */
@Service
public class MeterDataRemediesServiceImpl
        extends ServiceImpl<MeterDataRemediesMapper, MeterDataRemediesEntity>
        implements MeterDataRemediesService {

    @Resource private AmmeterService ammeterService;

    @Override
    @Transactional
    public void updateRemediesData(
            RemediesMeterRequest remediesRequest, String projectId, String type) {

        this.lambdaUpdate()
                .eq(MeterDataRemediesEntity::getProjectId, projectId)
                .eq(MeterDataRemediesEntity::getDay, remediesRequest.getDay())
                .eq(MeterDataRemediesEntity::getReportType, type)
                .eq(MeterDataRemediesEntity::getMeterId, remediesRequest.getMeterId())
                .eq(MeterDataRemediesEntity::getTime, remediesRequest.getTime())
                .eq(MeterDataRemediesEntity::getDataType, remediesRequest.getDataType())
                .remove();
        MeterDataRemediesEntity meterDataRemediesEntity = new MeterDataRemediesEntity();
        meterDataRemediesEntity.setProjectId(projectId);
        meterDataRemediesEntity.setDay(remediesRequest.getDay());
        meterDataRemediesEntity.setTime(remediesRequest.getTime());
        meterDataRemediesEntity.setReportType(type);
        meterDataRemediesEntity.setMeterId(remediesRequest.getMeterId());
        meterDataRemediesEntity.setData(remediesRequest.getData());
        meterDataRemediesEntity.setDataType(remediesRequest.getDataType());
        this.baseMapper.insert(meterDataRemediesEntity);
    }

    @Override
    public List<MeterDataRemediesEntity> getMeterRemediesData(
            RemediesMeterQueryRequest remediesQueryRequest, String projectId, String type) {
        List<MeterDataRemediesEntity> meterDataRemediesEntities =
                this.lambdaQuery()
                        .eq(MeterDataRemediesEntity::getProjectId, projectId)
                        .eq(MeterDataRemediesEntity::getReportType, type)
                        .eq(
                                StringUtils.hasLength(remediesQueryRequest.getDataType()),
                                MeterDataRemediesEntity::getDataType,
                                remediesQueryRequest.getDataType())
                        .eq(
                                StringUtils.hasLength(remediesQueryRequest.getMeterId()),
                                MeterDataRemediesEntity::getMeterId,
                                remediesQueryRequest.getMeterId())
                        .ge(MeterDataRemediesEntity::getTime, remediesQueryRequest.getStart())
                        .lt(MeterDataRemediesEntity::getTime, remediesQueryRequest.getEnd())
                        .orderByDesc(MeterDataRemediesEntity::getTime)
                        .list();
        setMeterName(meterDataRemediesEntities);
        return meterDataRemediesEntities;
    }

    void setMeterName(List<MeterDataRemediesEntity> list) {
        List<AmmeterEntity> ammeterEntities =
                ammeterService
                        .lambdaQuery()
                        .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                        .list();
        Map<String, String> meterNameMap =
                ammeterEntities.stream()
                        .collect(Collectors.toMap(AmmeterEntity::getId, AmmeterEntity::getName));
        list.forEach(
                item -> {
                    item.setName(meterNameMap.get(item.getMeterId()));
                });
    }

    @Override
    public List<MeterDataRemediesEntity> getMeterRemediesData(
            String projectId, Long day, String reportType) {
        return this.lambdaQuery()
                .eq(MeterDataRemediesEntity::getProjectId, projectId)
                .eq(MeterDataRemediesEntity::getReportType, reportType)
                .ge(MeterDataRemediesEntity::getDay, day)
                .orderByDesc(MeterDataRemediesEntity::getTime)
                .list();
    }
}
