package com.wifochina.modules.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.report.entity.ReportDataRemediesEntity;
import com.wifochina.modules.report.request.RemediesQueryRequest;
import com.wifochina.modules.report.request.RemediesReportRequest;

import java.util.List;
import java.util.Map;

/**
 * 数据状态 服务类
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
public interface ReportDataRemediesService extends IService<ReportDataRemediesEntity> {
    void updateRemediesData(RemediesReportRequest remediesRequest, String projectId, String type);

    List<ReportDataRemediesEntity> getReportRemediesData(
            RemediesQueryRequest remediesQueryRequest, String projectId, String type);

    List<ReportDataRemediesEntity> getReportRemediesData(
            String projectId, Long day, String reportType);

    Map<String, Map<Long, Double>> getReportRemediesDataMapByDataType(
            String projectId, Long startTime, String reportType);
}
