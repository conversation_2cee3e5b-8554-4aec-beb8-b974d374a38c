package com.wifochina.modules.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Created on 2025/2/19 11:47.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("t_day_report")
@ApiModel(value = "DayReportEntity对象", description = "日报缓存数据")
@Accessors(chain = true)
public class DayReportEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("设备id(电表orEms)")
    private String equipmentId;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("设备类型 pv/wind/...ems, 电表的类型+ems")
    private String equipmentType;

    @ApiModelProperty("进电量")
    private Double inData = 0.0;

    @ApiModelProperty("出电量")
    private Double outData = 0.0;

    @ApiModelProperty("单属于ems内部的dcdc的发电量")
    private Double outEmsDcdcData = 0.0;
}
