package com.wifochina.modules.report.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RemediesReportRequest {
    @ApiModelProperty("所在日、月或者年")
    private Long day;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("数据类型: pv,gridOut,gridIn,emsOut,emsIn,load,wind,diesel,gas,waster,pile")
    private String dataType;

    @ApiModelProperty("输出值")
    private double data;
}
