package com.wifochina.modules.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据状态
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Getter
@Setter
@TableName("t_report_data_remedies")
@ApiModel(value = "ReportDataRemediesEntity对象", description = "数据状态")
public class ReportDataRemediesEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("所在日、月或者年")
    private Long day;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("日报类型 daily,month,year")
    private String reportType;

    @ApiModelProperty("数据类型: pv,gridOut,gridIn,emsOut,emsIn,load,wind,diesel,gas,water,pile")
    private String dataType;

    @ApiModelProperty("输出值")
    private Double data;
}
