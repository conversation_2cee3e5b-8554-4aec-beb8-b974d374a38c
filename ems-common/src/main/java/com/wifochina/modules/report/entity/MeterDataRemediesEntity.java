package com.wifochina.modules.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据状态
 *
 * <AUTHOR>
 * @since 2024-10-16
 */
@Getter
@Setter
@TableName("t_meter_data_remedies")
@ApiModel(value = "MeterDataRemediesEntity对象", description = "数据状态")
public class MeterDataRemediesEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("电表 id")
    private String meterId;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("日期")
    private Long day;

    @ApiModelProperty("日报类型 daily,month,year")
    private String reportType;

    @ApiModelProperty("数据类型: out in")
    private String dataType;

    @ApiModelProperty("数据")
    private Double data;

    @ApiModelProperty("电表名称")
    @TableField(exist = false)
    private String name;
}
