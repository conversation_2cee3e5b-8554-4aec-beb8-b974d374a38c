package com.wifochina.modules.report.service.impl;

import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.common.search.AbstractEquipmentIdsQuerySupplier;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.common.search.TimeSeriesQuerySource;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.report.entity.DayReportEntity;
import com.wifochina.modules.report.service.DayReportCacheService;
import com.wifochina.modules.report.service.DayReportService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.*;

/**
 * Created on 2025/2/19 14:45.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class DayReportCacheServiceImpl implements DayReportCacheService {

    private final DayReportService dayReportService;

    private final AmmeterService ammeterService;
    private final DeviceService deviceService;

    private final InfluxClientService influxClientService;
    private static final Map<String, String> FIELD_MAP = Map.of();
    private static final Map<Integer, Map<String, String>> METER_TYPE_FIELD_MAP = Map.of();

    private static final String OUT_FIELD = "out";
    private static final String IN_FIELD = "in";
    private static final String DCDC_OUT_FIELD = "dcdc_out";

    private static final Map<String, String> EMS_FIELD_MAP =
            Map.of(
                    OUT_FIELD, EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field(),
                    IN_FIELD, EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field(),
                    DCDC_OUT_FIELD, EmsFieldEnum.DCDC_METER_HISTORY_ENERGY_POS.field());
    private static final Set<Integer> JUST_OUT_FIELD =
            Set.of(
                    MeterTypeEnum.PV.meterType(),
                    MeterTypeEnum.WIND.meterType(),
                    MeterTypeEnum.DIESEL.meterType(),
                    MeterTypeEnum.WASTER.meterType(),
                    MeterTypeEnum.GAS.meterType());
    private static final Set<Integer> IN_OUT_FIELD =
            Set.of(
                    MeterTypeEnum.GRID.meterType(),
                    MeterTypeEnum.LOAD.meterType(),
                    MeterTypeEnum.COMMON.meterType(),
                    MeterTypeEnum.EMS.meterType(),
                    MeterTypeEnum.PILE.meterType());

    @Override
    public void saveDayReport(ProjectEntity project, RangeRequest rangeRequest) {
        log.info(
                "saveDayReport run projectName:{} rangeRequest startTime{} , endTime:{} ",
                project.getProjectName(),
                MyTimeUtil.formatMilliTimestampWithTimezone(
                        rangeRequest.getStartDate() * 1000, project.getTimezone()),
                MyTimeUtil.formatMilliTimestampWithTimezone(
                        rangeRequest.getEndDate() * 1000, project.getTimezone()));
        FluxRateCommonHolder holder =
                new FluxRateCommonHolder()
                        .setStartDate(rangeRequest.getStartDate())
                        .setEndDate(rangeRequest.getEndDate());
        List<AmmeterEntity> ammeterEntities = ammeterService.findAllMeters(project.getId());
        List<DeviceEntity> deviceEntities = deviceService.getDevicesByPid(project.getId());
        if (ammeterEntities.isEmpty() && deviceEntities.isEmpty()) {
            log.info("saveDayReport 项目:{} 无设备或者电表 无法运行 saveDayReport", project.getProjectName());
            return;
        }
        List<DayReportEntity> meterDayReports;
        List<DayReportEntity> allReports = new ArrayList<>();
        allReports.addAll(saveMeterDayReport(project, ammeterEntities, holder));
        allReports.addAll(saveEmsDayReport(project, deviceEntities, holder));
        if (!allReports.isEmpty()) {
            meterDayReports = dayReportService.fillExistingIds(allReports);
            dayReportService.saveOrUpdateBatch(meterDayReports);
        }
    }

    @Override
    public List<DayReportEntity> findTodayEmsDayReports(
            ProjectEntity project, RangeRequest rangeRequest) {
        List<DeviceEntity> deviceEntities = deviceService.getDevicesByPid(project.getId());
        FluxRateCommonHolder holder =
                new FluxRateCommonHolder()
                        .setStartDate(rangeRequest.getStartDate())
                        .setEndDate(rangeRequest.getEndDate());
        return saveEmsDayReport(project, deviceEntities, holder);
    }

    private List<DayReportEntity> saveEmsDayReport(
            ProjectEntity project, List<DeviceEntity> deviceEntities, FluxRateCommonHolder holder) {
        List<DayReportEntity> emsDayReportList = new ArrayList<>();
        for (DeviceEntity deviceEntity : deviceEntities) {
            DayReportEntity dayReportEntity =
                    new DayReportEntity()
                            .setProjectId(project.getId())
                            .setTime(holder.getStartDate())
                            .setEquipmentId(deviceEntity.getId())
                            // 固定的这个 设备类型
                            .setEquipmentType(EquipmentTypeEnums.EMS_DEVICE.name());
            EMS_FIELD_MAP.forEach(
                    (fieldType, field) -> {
                        Map<String, Double> dataMap =
                                getRangeDifferenceMap(
                                        project,
                                        influxClientService.getEmsTable(project.getId()),
                                        field,
                                        deviceEntity.getId(),
                                        holder);
                        double power =
                                dataMap.values().stream().mapToDouble(Double::doubleValue).sum();
                        if (fieldType.equals(OUT_FIELD)) {
                            dayReportEntity.setOutData(power);
                        }
                        if (fieldType.equals(IN_FIELD)) {
                            dayReportEntity.setInData(power);
                        }
                        if (fieldType.equals(DCDC_OUT_FIELD)) {
                            // dcdc的情况
                            dayReportEntity.setOutEmsDcdcData(power);
                        }
                    });
            emsDayReportList.add(dayReportEntity);
        }
        return emsDayReportList;
    }

    private List<DayReportEntity> saveMeterDayReport(
            ProjectEntity project,
            List<AmmeterEntity> ammeterEntities,
            FluxRateCommonHolder holder) {
        List<DayReportEntity> meterDayReportList = new ArrayList<>();
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            EquipmentTypeEnums equipmentTypeEnums =
                    EquipmentTypeEnums.getMeterType(ammeterEntity.getType());
            if (equipmentTypeEnums == null) {
                log.error(
                        "Discover an unknown meter id: {}, unknown type  :{} ",
                        ammeterEntity.getId(),
                        ammeterEntity.getType());
                continue;
            }
            DayReportEntity dayReportEntity =
                    new DayReportEntity()
                            .setProjectId(project.getId())
                            .setTime(holder.getStartDate())
                            .setEquipmentId(ammeterEntity.getId());
            // 根据电表的类型 获取到 这个电表 需要去查询的 属性字段, key是out/in ,对应 输出输入的属性, 因为gird 的属性反过来了
            // ..还需要注意这里 是 负载电表 是读取负载电表的 电量存储的, 而整站查询出来的负载电量是 通过平台计算的
            Map<String, String> fieldsByMeter = getFieldsByMeter(ammeterEntity);
            // 需要通过type 获取到 对应的type
            dayReportEntity.setEquipmentType(equipmentTypeEnums.name());
            fieldsByMeter.forEach(
                    (fieldType, field) -> {
                        //
                        Map<String, Double> dataMap =
                                getRangeDifferenceMap(
                                        project,
                                        influxClientService.getMeterTable(project.getId()),
                                        field,
                                        ammeterEntity.getId(),
                                        holder);
                        // 得到电表的 属性 差值
                        double power =
                                dataMap.values().stream().mapToDouble(Double::doubleValue).sum();
                        if (fieldType.equals(OUT_FIELD)) {
                            dayReportEntity.setOutData(power);
                        }
                        if (fieldType.equals(IN_FIELD)) {
                            dayReportEntity.setInData(power);
                        }
                    });
            meterDayReportList.add(dayReportEntity);
        }
        return meterDayReportList;
    }

    private Map<String, String> getFieldsByMeter(AmmeterEntity ammeterEntity) {
        Map<String, String> map = new HashMap<>(2);
        // 只查询 out field 属性
        if (JUST_OUT_FIELD.contains(ammeterEntity.getType())) {
            if (Boolean.TRUE.equals(ammeterEntity.getDcMeter())) {
                map.put(OUT_FIELD, MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field());
            } else {
                map.put(OUT_FIELD, MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field());
            }
        } else if (IN_OUT_FIELD.contains(ammeterEntity.getType())) {
            // 并网电表 的 out 放电量 是负值  是在 negative 属性里, 特殊处理一下 和其他不一样
            if (ammeterEntity.getType().equals(MeterTypeEnum.GRID.meterType())) {
                if (Boolean.TRUE.equals(ammeterEntity.getDcMeter())) {
                    map.put(OUT_FIELD, MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                    map.put(IN_FIELD, MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field());
                } else {
                    map.put(OUT_FIELD, MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                    map.put(IN_FIELD, MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field());
                }
            } else {
                if (Boolean.TRUE.equals(ammeterEntity.getDcMeter())) {
                    map.put(OUT_FIELD, MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field());
                    map.put(IN_FIELD, MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                } else {
                    map.put(OUT_FIELD, MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field());
                    map.put(IN_FIELD, MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                }
            }
        } else {
            // 电表类型 未知
            log.error("meter type not find {} ", ammeterEntity.getType());
            return Map.of();
        }
        return map;
    }

    private Map<String, Double> getRangeDifferenceMap(
            ProjectEntity project,
            String measurement,
            String field,
            String equipmentId,
            FluxRateCommonHolder holder) {
        return EquipmentTimeSeriesUtils.differenceQueryEngine.getRangeDifferenceEquipmentsMap(
                new TimeSeriesQuerySource()
                        .setBucket(influxClientService.getBucketForever())
                        .setMeasurement(measurement)
                        .setProject(project),
                holder,
                field,
                // 这里是根据 type 设备类型去查询, 也可以根据 ids去查询 实现类不同罢了, 原本重构前是 type去查询 所以这里保持一致
                new AbstractEquipmentIdsQuerySupplier() {
                    @Override
                    public List<String> supplier() {
                        return List.of(equipmentId);
                    }
                });
    }
}
