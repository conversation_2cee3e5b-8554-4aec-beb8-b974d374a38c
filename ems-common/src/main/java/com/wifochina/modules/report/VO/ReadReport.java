package com.wifochina.modules.report.VO;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-13 4:20 PM
 */
@Data
public class ReadReport {
    @ApiModelProperty(value = "当前充电量")
    private Double currentIn;

    @ApiModelProperty(value = "当前放电量")
    private Double currentOut;

    @ApiModelProperty(value = "月充电量")
    private Double monthIn;

    @ApiModelProperty(value = "月放电量")
    private Double monthOut;

    @ApiModelProperty(value = "月报")
    private List<ReadMonth> readMonth;
}
