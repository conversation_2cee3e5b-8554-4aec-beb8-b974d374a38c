package com.wifochina.modules.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.modules.report.entity.DayReportEntity;

import java.util.List;

/**
 * Created on 2025/2/19 14:43.
 *
 * <AUTHOR>
 */
public interface DayReportService extends IService<DayReportEntity> {

    List<DayReportEntity> fillExistingIds(List<DayReportEntity> dayReportEntities);

    List<DayReportEntity> findAllByRangeAndProjectId(String projectId, RangeRequest rangeRequest);

    List<DayReportEntity> findAllByRangeEquipmentTypeAndProjectId(
            String projectId, EquipmentTypeEnums equipmentTypeEnums, RangeRequest rangeRequest);

    DayReportEntity findSumByCondition(
            List<String> projectIds,
            EquipmentTypeEnums equipmentTypeEnums,
            String equipmentId,
            RangeRequest rangeRequest);

    /**
     * 根据时间范围 去聚合 每个月的 按照 equipmentType去 group by
     *
     * @param projectId
     * @param rangeRequest
     * @return
     */
    List<DayReportEntity> findEveryEquipmentMonthlyAggregatedReports(
            String projectId,
            String equipmentId,
            EquipmentTypeEnums equipmentTypeEnums,
            RangeRequest rangeRequest);

    /**
     * 根据时间范围 去聚合 每天的 按照 equipmentType去 group by
     *
     * @param projectId : 项目id
     * @param rangeRequest : 时间范围
     * @return : 按照 设备的 分类把 每个分类的 每天数据 sum 聚合一下
     */
    List<DayReportEntity> findDayAggregatedReports(String projectId, RangeRequest rangeRequest);

    /**
     * 根据时间范围去聚合每天的数据，只用time group by
     *
     * @param projectId : 项目id
     * @param rangeRequest : 时间范围
     * @return : 按照 设备的 分类把 每个分类的 每天数据 sum 聚合一下
     */
    List<DayReportEntity> findDayAllReports(
            String projectId, EquipmentTypeEnums equipmentTypeEnums, RangeRequest rangeRequest);

    /**
     * 和上面这个方法 是类似的 根据时间范围 去 聚合 每个月的 按照 equipmentType去 group by
     *
     * @param projectId : 项目id
     * @param rangeRequest
     * @return
     */
    List<DayReportEntity> findMonthlyAggregatedReports(String projectId, RangeRequest rangeRequest);

    List<DayReportEntity> findEquipmentDayReports(
            String projectId,
            String equipmentId,
            EquipmentTypeEnums equipmentTypeEnums,
            RangeRequest rangeRequest);
}
