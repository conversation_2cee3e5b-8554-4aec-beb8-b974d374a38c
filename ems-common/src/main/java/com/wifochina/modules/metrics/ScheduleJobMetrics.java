package com.wifochina.modules.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用于监控运营报告任务的执行情况
 *
 * <AUTHOR>
 * @date 2025/7/28 19:51
 * @version 1.0
 */
@Component
public class ScheduleJobMetrics {

    private final MeterRegistry registry;
    
    // 存储各个项目和任务类型的成功计数器
    private final Map<String, Counter> successCounters = new ConcurrentHashMap<>();
    
    // 存储各个项目和任务类型的失败计数器
    private final Map<String, Counter> failureCounters = new ConcurrentHashMap<>();

    public ScheduleJobMetrics(MeterRegistry registry) {
        this.registry = registry;
    }

    /**
     * 记录任务执行成功
     * 
     * @param projectId 项目ID
     * @param projectName 项目名称
     * @param jobType 任务类型（month/year）
     */
    public void recordSuccess(String projectId, String projectName, String jobType) {
        String key = generateKey(projectId, jobType);
        Counter counter = successCounters.computeIfAbsent(key, k -> 
            Counter.builder("operation_report_job_success_total")
                    .description("运营报告任务执行成功总数")
                    .tag("project_id", projectId)
                    .tag("project_name", projectName)
                    .tag("job_type", jobType)
                    .tag("occurrence_time", String.valueOf(Instant.now().getEpochSecond()))
                    .register(registry)
        );
        counter.increment();
    }

    /**
     * 记录任务执行失败
     * 
     * @param projectId 项目ID
     * @param projectName 项目名称
     * @param jobType 任务类型（month/year）
     * @param errorMessage 错误信息
     */
    public void recordFailure(String projectId, String projectName, String jobType, String errorMessage) {
        String key = generateKey(projectId, jobType);
        Counter counter = failureCounters.computeIfAbsent(key, k -> 
            Counter.builder("operation_report_job_failure_total")
                    .description("运营报告任务执行失败总数")
                    .tag("project_id", projectId)
                    .tag("project_name", projectName)
                    .tag("job_type", jobType)
                    .tag("error_message", truncateErrorMessage(errorMessage))
                    .tag("occurrence_time", String.valueOf(Instant.now().getEpochSecond()))
                    .register(registry)
        );
        counter.increment();
    }

    /**
     * 生成缓存键
     * 
     * @param projectId 项目ID
     * @param jobType 任务类型
     * @return 缓存键
     */
    private String generateKey(String projectId, String jobType) {
        return projectId + ":" + jobType;
    }

    /**
     * 截断错误信息，避免标签值过长
     * 
     * @param errorMessage 原始错误信息
     * @return 截断后的错误信息
     */
    private String truncateErrorMessage(String errorMessage) {
        if (errorMessage == null) {
            return "unknown";
        }
        // 限制错误信息长度为100个字符
        return errorMessage.length() > 100 ? errorMessage.substring(0, 100) + "..." : errorMessage;
    }
}
