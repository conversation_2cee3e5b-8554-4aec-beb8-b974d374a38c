package com.wifochina.modules.favourite.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.favourite.entity.FavouriteProjectEntity;
import com.wifochina.modules.favourite.vo.FavouriteProjectVo;

/**
 * <AUTHOR>
 * @since 2023-01-04
 */
public interface FavouriteProjectService extends IService<FavouriteProjectEntity> {

    IPage<FavouriteProjectVo> queryFavouriteProjectVo(IPage<FavouriteProjectVo> favouriteProjectPage,
        String projectName, String userId);
}
