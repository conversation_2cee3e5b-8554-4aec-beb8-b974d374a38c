<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.favourite.mapper.FavouriteProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="FavouriteProjectMap" type="com.wifochina.modules.favourite.vo.FavouriteProjectVo">
        <id column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="project_id" property="projectId"/>
        <result column="user_id" property="userId"/>
        <result column="focus_status" property="focusStatus"/>
        <result column="sms" property="sms"/>
        <result column="phone" property="phone"/>
        <result column="wechat" property="wechat"/>
        <result column="email" property="email"/>
        <result column="other" property="other"/>
        <result column="project_name" property="projectName"/>
        <result column="status" property="status"/>
        <result column="fault" property="fault"/>
        <result column="alarm" property="alarm"/>
    </resultMap>

    <select id="queryFavouriteProjectVo" resultMap="FavouriteProjectMap">
        select
        f.id,f.project_id,f.user_id,f.focus_status,f.sms,f.phone,f.wechat,f.email,f.other,f.create_by,f.update_by,f.update_time,f.create_time,p.project_name,p.fault,p.alarm,p.status
        from t_favourite_project f,t_project p
        where f.user_id = #{userId}
        <if test="projectName!=null and projectName.trim()!=''">
            and p.project_name like CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        and f.project_id = p.id
        order by f.create_time desc
    </select>
</mapper>
