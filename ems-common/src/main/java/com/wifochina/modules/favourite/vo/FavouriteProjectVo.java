package com.wifochina.modules.favourite.vo;

import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-01-04 7:25 PM
 */
@Data
public class FavouriteProjectVo extends BaseEntity {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "关注状态")
    private String focusStatus;

    @ApiModelProperty(value = "短信")
    private Boolean sms;

    @ApiModelProperty(value = "电话")
    private Boolean phone;

    @ApiModelProperty(value = "微信")
    private Boolean wechat;

    @ApiModelProperty(value = "邮箱")
    private Boolean email;

    private Boolean other;

    @ApiModelProperty(value = "项目名")
    private String projectName;

    @ApiModelProperty(value = "项目状态")
    private Integer status;

    @ApiModelProperty(value = "项目故障")
    private Integer fault;

    @ApiModelProperty(value = "项目告警")
    private Integer alarm;

}
