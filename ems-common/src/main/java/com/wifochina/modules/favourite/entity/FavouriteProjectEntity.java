package com.wifochina.modules.favourite.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-01-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_favourite_project")
@ApiModel(value = "FavouriteProjectEntity对象")
public class FavouriteProjectEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "关注状态")
    private String focusStatus;

    @ApiModelProperty(value = "短信")
    private Boolean sms;

    @ApiModelProperty(value = "电话")
    private Boolean phone;

    @ApiModelProperty(value = "微信")
    private Boolean wechat;

    @ApiModelProperty(value = "邮箱")
    private Boolean email;

    private Boolean other;

}
