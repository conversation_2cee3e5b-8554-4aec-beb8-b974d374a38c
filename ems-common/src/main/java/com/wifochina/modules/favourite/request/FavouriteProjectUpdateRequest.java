package com.wifochina.modules.favourite.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-01-04
 */
@Data
public class FavouriteProjectUpdateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "关注状态(1离线 2停机 3故障 4告警),多个以逗号隔开，如'1,2,3'")
    private String focusStatus;

    @ApiModelProperty(value = "短信")
    private Boolean sms;

    @ApiModelProperty(value = "电话")
    private Boolean phone;

    @ApiModelProperty(value = "微信")
    private Boolean wechat;

    @ApiModelProperty(value = "邮箱")
    private Boolean email;

    private Boolean other;

}
