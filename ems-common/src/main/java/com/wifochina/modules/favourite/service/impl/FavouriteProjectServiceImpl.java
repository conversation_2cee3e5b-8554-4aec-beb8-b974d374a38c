package com.wifochina.modules.favourite.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.favourite.entity.FavouriteProjectEntity;
import com.wifochina.modules.favourite.mapper.FavouriteProjectMapper;
import com.wifochina.modules.favourite.service.FavouriteProjectService;
import com.wifochina.modules.favourite.vo.FavouriteProjectVo;

/**
 * <AUTHOR>
 * @since 2023-01-04
 */
@Service
public class FavouriteProjectServiceImpl extends ServiceImpl<FavouriteProjectMapper, FavouriteProjectEntity>
    implements FavouriteProjectService {

    @Override
    public IPage<FavouriteProjectVo> queryFavouriteProjectVo(IPage<FavouriteProjectVo> favouriteProjectPage,
        String projectName, String userId) {
        return this.baseMapper.queryFavouriteProjectVo(favouriteProjectPage, projectName, userId);
    }
}
