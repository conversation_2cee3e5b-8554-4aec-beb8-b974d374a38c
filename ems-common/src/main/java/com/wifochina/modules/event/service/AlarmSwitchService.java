package com.wifochina.modules.event.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.request.AlarmSwitchRequest;
import com.wifochina.modules.event.request.HideCodePageRequest;

import java.util.List;
import java.util.Map;

/**
 * 告警开关服务类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface AlarmSwitchService extends IService<AlarmSwitchEntity> {
    
    /**
     * 查询告警开关的事件编码
     *
     * @param projectId 项目ID
     * @param hideCodePageRequest 分页请求
     * @return 分页结果
     */
    IPage<EventCodeEntity> getAlarmSwitchEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest);

    IPage<MeterEventCodeEntity> getMeterAlarmSwitchEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest);

    /**
     * 查询告警开关列表
     *
     * @param projectId 项目ID
     * @return 告警开关列表
     */
    List<AlarmSwitchEntity> getAlarmSwitchList(String projectId);

    /**
     * 查询所有类型的告警开关事件编码
     *
     * @param projectId 项目ID
     * @return 事件编码列表
     */
    List<String> getAllTypeAlarmSwitchEventCodes(String projectId);

    /**
     * 查询EMS类型的告警开关事件编码
     *
     * @param projectId 项目ID
     * @return 事件编码列表
     */
    List<String> getEmsTypeAlarmSwitchEventCodes(String projectId);

    /**
     * 查询METER类型的告警开关事件编码
     *
     * @param projectId 项目ID
     * @return 事件编码列表
     */
    List<String> getMeterTypeAlarmSwitchEventCodes(String projectId);

    void updateSwitch(AlarmSwitchRequest alarmSwitchRequest);

    Map<String, Object> listAlarmSwitch(HideCodePageRequest alarmSwitchPageRequest);

    Map<String, Object> listMeterAlarmSwitch(HideCodePageRequest alarmSwitchPageRequest);

    void batchUpdateSwitch(AlarmSwitchRequest alarmSwitchRequest);

    void allUpdateSwitch(AlarmSwitchRequest alarmSwitchRequest);

    AlarmCacheDTO listAlarmSwitch(String projectId);
}
