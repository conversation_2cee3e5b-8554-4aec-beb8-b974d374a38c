package com.wifochina.modules.event.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.event.VO.DeviceVo;
import com.wifochina.modules.event.VO.EventMessageVO;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.mapper.EventMessageMapper;
import com.wifochina.modules.event.request.EventMessageRequest;
import com.wifochina.modules.event.service.EventCodeLanguageService;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.CameraService;
import com.wifochina.modules.group.service.ControllableService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 事件表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Service
public class EventMessageServiceImpl extends ServiceImpl<EventMessageMapper, EventMessageEntity>
        implements EventMessageService {

    @Resource private AmmeterService ammeterService;
    @Resource private DeviceService deviceService;
    @Resource private CameraService cameraService;
    @Resource private ControllableService controllableService;
    @Resource private EventCodeLanguageService eventCodeLanguageService;

    @Override
    public IPage<EventMessageVO> queryEventMessage(IPage<EventMessageEntity> pageReq,
                                                   EventMessageRequest request) {
        // 分页查询事件列表
        LambdaQueryWrapper<EventMessageEntity> wrapper = Wrappers.lambdaQuery(EventMessageEntity.class)
                .eq(request.getMaintain() != null,
                        EventMessageEntity::getMaintain,
                        request.getMaintain())
                .eq(StringUtils.hasLength(request.getDeviceId()),
                        EventMessageEntity::getDeviceId,
                        request.getDeviceId())
                .eq(StringUtils.hasLength(request.getProjectId()),
                        EventMessageEntity::getProjectId,
                        WebUtils.projectId.get())
                .like(StringUtils.hasLength(request.getEventDescription()),
                        EventMessageEntity::getEventDescription,
                        request.getEventDescription())
                .like(StringUtils.hasLength(request.getEventDescriptionEn()),
                        EventMessageEntity::getEventDescriptionEn,
                        request.getEventDescriptionEn())
                .like(StringUtils.hasLength(request.getEventCode()),
                        EventMessageEntity::getEventKey,
                        request.getEventCode())
                .like(StringUtils.hasLength(request.getEquipType()),
                        EventMessageEntity::getEquipType,
                        request.getEquipType())
                .in(CollUtil.isNotEmpty(request.getStatus()),
                        EventMessageEntity::getStatus,
                        request.getStatus())
                .in(CollUtil.isNotEmpty(request.getEventType()),
                        EventMessageEntity::getEventType,
                        request.getEventType())
                .ge(request.getStartDate() != null,
                        EventMessageEntity::getCreateTime,
                        request.getStartDate())
                .le(request.getEndDate() != null,
                        EventMessageEntity::getCreateTime,
                        request.getEndDate())
                .eq(EventMessageEntity::getWhetherDelete, false)
                .eq(!SecurityUtil.hasAuthority(EmsConstants.MESSAGE_SHOW_HIDE),
                        EventMessageEntity::getWhetherHide, false)
                .orderByDesc(EventMessageEntity::getCreateTime);
        IPage<EventMessageEntity> pageQueryRes = this.baseMapper.selectPage(pageReq, wrapper);
        // 获取各类设备名称
        List<DeviceVo> deviceList = getDeviceAndControllableList();
        Map<String, String> map = deviceList.stream().collect(Collectors.toMap(DeviceVo::getDeviceId, DeviceVo::getDeviceName));
        // 构建分页结果
        List<EventMessageVO> list = pageQueryRes.getRecords().stream().map(e -> {
            EventMessageVO item = new EventMessageVO();
            BeanUtils.copyProperties(e, item);
            item.setDeviceName(map.get(e.getDeviceId()));
            eventCodeLanguageService.transEventMessageVOForLanguage(item);
            return item;
        }).collect(Collectors.toList());
        Page<EventMessageVO> pageRes = new Page<>();
        BeanUtils.copyProperties(pageQueryRes, pageRes);
        pageRes.setRecords(list);
        return pageRes;
    }

    @Override
    public List<DeviceVo> getDeviceAndControllableList() {
        List<DeviceEntity> devices = deviceService.lambdaQuery()
                .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                .eq(DeviceEntity::getUnreal, false)
                .orderByAsc(DeviceEntity::getCreateTime)
                .list();
        List<AmmeterEntity> ammeters = ammeterService.lambdaQuery()
                .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                .orderByAsc(AmmeterEntity::getCreateTime)
                .list();
        List<ControllableEntity> controls = controllableService.lambdaQuery()
                .eq(ControllableEntity::getProjectId, WebUtils.projectId.get())
                .list();
        List<CameraEntity> cameras = cameraService.lambdaQuery()
                .eq(CameraEntity::getProjectId, WebUtils.projectId.get())
                .list();
        List<DeviceVo> list = new ArrayList<>(devices.size() + ammeters.size()
                + controls.size() + cameras.size());
        // 然后分别调用该方法处理四个集合
        processDevicesToStatusList(devices, list);
        processDevicesToStatusList(ammeters, list);
        processDevicesToStatusList(controls, list);
        processDevicesToStatusList(cameras, list);
        return list;
    }

    /**
     * 查询 error or alarm num
     *
     * @param projectId : projectId
     * @param rangeRequest : rangeRequest
     * @param type : type
     * @return : long
     */
    @Override
    public Long getErrorOrAlarmNum(
            String projectId, RangeRequest rangeRequest, EventLevelEnum eventLevelEnum) {
        return this.count(
                Wrappers.lambdaQuery(EventMessageEntity.class)
                        .eq(EventMessageEntity::getEventType, eventLevelEnum.getLevel())
                        .eq(EventMessageEntity::getProjectId, projectId)
                        .between(
                                EventMessageEntity::getCreateTime,
                                rangeRequest.getStartDate() * 1000,
                                rangeRequest.getEndDate() * 1000)
                        .eq(EventMessageEntity::getWhetherDelete, false)
                        // 应该是是否有查询权限的
                        .eq(
                                !SecurityUtil.hasAuthority(EmsConstants.MESSAGE_SHOW_HIDE),
                                EventMessageEntity::getWhetherHide,
                                false));
    }

    @Override
    public List<String> equipTypeList(RangeRequest request) {
        LambdaQueryWrapper<EventMessageEntity> wrapper = Wrappers.<EventMessageEntity>lambdaQuery()
                .eq(EventMessageEntity::getProjectId, WebUtils.projectId.get())
                .between(EventMessageEntity::getCreateTime,
                        request.getStartDate() * 1000,
                        request.getEndDate() * 1000)
                .eq(EventMessageEntity::getWhetherDelete, false);
        List<EventMessageEntity> list = this.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(EventMessageEntity::getEquipType).collect(Collectors.toList());
    }

    private <T extends BaseEquipEntity> void processDevicesToStatusList(
            List<T> entities, List<DeviceVo> list) {
        entities.forEach(
                entity -> {
                    DeviceVo deviceVo = new DeviceVo();
                    deviceVo.setDeviceId(entity.getId());
                    deviceVo.setDeviceName(entity.getName());
                    list.add(deviceVo);
                });
    }
}
