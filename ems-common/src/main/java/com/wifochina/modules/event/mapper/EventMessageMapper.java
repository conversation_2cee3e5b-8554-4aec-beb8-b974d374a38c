package com.wifochina.modules.event.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wifochina.modules.event.VO.EventMessageVO;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.request.EventMessageRequest;

/**
 * <p>
 * 事件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public interface EventMessageMapper extends BaseMapper<EventMessageEntity> {
    /**
     * queryEventMessage
     * 
     * @param eventMessagePage
     * @param eventMessageRequest
     * @return IPage<EventMessageVO>
     */
    IPage<EventMessageVO> queryEventMessage(IPage<EventMessageVO> eventMessagePage,
        @Param("eventRequest") EventMessageRequest eventMessageRequest);
}
