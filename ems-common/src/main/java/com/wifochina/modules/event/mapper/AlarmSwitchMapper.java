package com.wifochina.modules.event.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.request.HideCodePageRequest;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 告警开关Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Mapper
public interface AlarmSwitchMapper extends BaseMapper<AlarmSwitchEntity> {

    /**
     * 查询告警开关的事件编码
     *
     * @param page 分页对象
     * @param projectId 项目ID
     * @param request 请求参数
     * @return 分页结果
     */
    IPage<EventCodeEntity> getAlarmSwitchEventCode(
            Page<EventCodeEntity> page,
            @Param("projectId") String projectId,
            @Param("request") HideCodePageRequest request);

    /**
     * 查询告警开关列表
     *
     * @param projectId 项目ID
     * @return 告警开关列表
     */
    List<AlarmSwitchEntity> getAlarmSwitchList(@Param("projectId") String projectId);

    /**
     * 查询所有类型的告警开关事件编码
     *
     * @param projectId 项目ID
     * @return 事件编码列表
     */
    List<String> getAllTypeAlarmSwitchEventCodes(@Param("projectId") String projectId);

    /**
     * 查询EMS类型的告警开关事件编码
     *
     * @param projectId 项目ID
     * @return 事件编码列表
     */
    List<String> getEmsTypeAlarmSwitchEventCodes(@Param("projectId") String projectId);

    /**
     * 查询METER类型的告警开关事件编码
     *
     * @param projectId 项目ID
     * @return 事件编码列表
     */
    List<String> getMeterTypeAlarmSwitchEventCodes(@Param("projectId") String projectId);
}
