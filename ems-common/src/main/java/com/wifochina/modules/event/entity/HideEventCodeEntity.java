package com.wifochina.modules.event.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 告警信息表
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_hide_event_code")
@ApiModel(value = "HideEventCodeEntity对象", description = "隐藏")
public class HideEventCodeEntity extends BaseEntity {

    // for mybatis need a constructor
    @SuppressWarnings("unused")
    public HideEventCodeEntity() {}

    public HideEventCodeEntity(String projectId, String eventCode, String type) {
        this.eventCode = eventCode;
        this.projectId = projectId;
        this.type = type;
    }

    public HideEventCodeEntity(String eventCode) {
        this.eventCode = eventCode;
    }

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "事件编码")
    private String eventCode;

    @ApiModelProperty(value = "类型 EMS 或者METER")
    private String type;
}
