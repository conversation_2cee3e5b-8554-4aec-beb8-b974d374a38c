package com.wifochina.modules.event.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 事件表
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_event_message")
@ApiModel(value = "EventMessageEntity对象", description = "事件表")
public class EventMessageEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "装备类型（汇川630kWPCS、派能高压电池箱）")
    private String equipType;

    @ApiModelProperty(value = "装备名称（pcs1，bms1）")
    private String equipName;

    @ApiModelProperty(value = "事件类型（state、alarm、fault）")
    private String eventType;

    @ApiModelProperty(value = "事件编码（事件bit偏移量+bit地址）")
    @JsonProperty("eventAddress")
    private Integer eventCode;

    @ApiModelProperty(value = "状态（1已确认，0未确认）")
    private Integer status;

    @ApiModelProperty(value = "事件描述")
    private String eventDescription;

    @ApiModelProperty(value = "英文事件描述")
    private String eventDescriptionEn;

    @ApiModelProperty(value = "事件触发、消失描述")
    private String eventOnOff;

    @ApiModelProperty(value = "project id")
    private String projectId;

    @ApiModelProperty(value = "设备是否维护")
    private Boolean maintain;

    @ApiModelProperty(value = "是否逻辑删除，默认false")
    private Boolean whetherDelete;

    @ApiModelProperty(value = "是否隐藏")
    private Boolean whetherHide;

    @ApiModelProperty(value = "事件编码（事件bit偏移量+bit地址）")
    @JsonProperty("eventCode")
    private String eventKey;
}
