package com.wifochina.modules.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * EventMessageRequest
 * 
 * @since 4/2/2022 3:33 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "事件删除请求")
@EqualsAndHashCode(callSuper = false)
public class EventMessageDeleteRequest{

    @ApiModelProperty(value = "开始时间")
    private Long startDate;

    @ApiModelProperty(value = "结束时间")
    private Long endDate;
}
