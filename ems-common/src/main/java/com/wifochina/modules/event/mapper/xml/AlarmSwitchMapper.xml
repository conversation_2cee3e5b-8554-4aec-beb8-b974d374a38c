<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.event.mapper.AlarmSwitchMapper">

    <!-- 查询告警开关的事件编码 -->
    <select id="getAlarmSwitchEventCode" resultType="com.wifochina.modules.event.entity.EventCodeEntity">
        SELECT DISTINCT
            ec.id,
            ec.point_column,
            ec.device_type_code,
            ec.device_type,
            ec.event_level,
            ec.bit_offset,
            ec.bit_value,
            ec.event_description,
            ec.bit_stand,
            ec.remarks,
            ec.create_time,
            ec.create_by,
            ec.update_time,
            ec.update_by,
            CASE WHEN ase.enabled IS NOT NULL THEN ase.enabled ELSE true END as value
        FROM t_event_code ec
        LEFT JOIN t_alarm_switch ase ON ec.id = ase.event_code AND ase.project_id = #{projectId}
        <where>
            <if test="request.label != null">
                <choose>
                    <when test="request.label == 1">
                        AND ec.device_type_code = 65535
                    </when>
                    <when test="request.label == 2">
                        AND ec.device_type_code != 65535 AND ec.device_type_code != -1
                    </when>
                    <when test="request.label == 3">
                        AND ec.device_type_code = -1
                    </when>
                </choose>
            </if>
            <if test="request.eventDescription != null and request.eventDescription != ''">
                AND ec.event_description LIKE CONCAT('%', #{request.eventDescription}, '%')
            </if>
        </where>
        ORDER BY ec.id
    </select>

    <!-- 查询告警开关列表 -->
    <select id="getAlarmSwitchList" resultType="com.wifochina.modules.event.entity.AlarmSwitchEntity">
        SELECT 
            id,
            project_id,
            event_code,
            type,
            enabled,
            create_time,
            create_by,
            update_time,
            update_by
        FROM t_alarm_switch
        WHERE project_id = #{projectId}
        ORDER BY id
    </select>

    <!-- 查询所有类型的告警开关事件编码 -->
    <select id="getAllTypeAlarmSwitchEventCodes" resultType="java.lang.String">
        SELECT event_code
        FROM t_alarm_switch
        WHERE project_id = #{projectId}
        AND enabled = false
    </select>

    <!-- 查询EMS类型的告警开关事件编码 -->
    <select id="getEmsTypeAlarmSwitchEventCodes" resultType="java.lang.String">
        SELECT event_code
        FROM t_alarm_switch
        WHERE project_id = #{projectId}
        AND enabled = false
        AND type IN ('1', '2')
    </select>

    <!-- 查询METER类型的告警开关事件编码 -->
    <select id="getMeterTypeAlarmSwitchEventCodes" resultType="java.lang.String">
        SELECT event_code
        FROM t_alarm_switch
        WHERE project_id = #{projectId}
        AND enabled = false
        AND type = '3'
    </select>

</mapper>
