package com.wifochina.modules.event.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.request.HideCodePageRequest;

import java.util.List;

/**
 * 告警信息表 服务类
 *
 * <AUTHOR>
 * @since 2022-03-31 19:22:30
 */
public interface HideEventCodeService extends IService<HideEventCodeEntity> {
    IPage<EventCodeEntity> getHideEmsEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest);

    IPage<MeterEventCodeEntity> getHideMeterEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest);

    List<HideEventCodeEntity> getHideEventCode(String projectId);

    // ----------------------------------下面是refactor 后的
    /**
     * 查询 所有类型 event code 包括 ems 和 meter
     *
     * @see com.wifochina.modules.event.enums.HideCodeLabelEnums
     * @param projectId : projectId
     * @return : all (kernel sub_device and meter ) type event codes list
     */
    List<String> getHideAllTypeEventCodeList(String projectId);

    /**
     * 查询 Ems 包括 EMS_KERNEL 和 EMS_SUB_DEVICE
     *
     * @see com.wifochina.modules.event.enums.HideCodeLabelEnums
     * @param projectId : projectId
     * @return : ems (kernel and sub device ) type event codes list
     */
    List<String> getHideEmsTypeEventCodes(String projectId);

    /**
     * 查询 测控 包括 和 METER_DEVICE
     *
     * @see com.wifochina.modules.event.enums.HideCodeLabelEnums
     * @param projectId : projectId
     * @return : meter 测控 type event codes list
     */
    List<String> getHideMeterTypeEventCodes(String projectId);
}
