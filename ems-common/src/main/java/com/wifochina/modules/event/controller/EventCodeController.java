package com.wifochina.modules.event.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.request.AlarmSwitchRequest;
import com.wifochina.modules.event.request.EmsEventCodeRequest;
import com.wifochina.modules.event.request.HideCodePageRequest;
import com.wifochina.modules.event.request.HideCodeRequest;
import com.wifochina.modules.event.service.AlarmSwitchService;
import com.wifochina.modules.event.service.EmsEventCodeService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 告警信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@RestController
@RequestMapping("/event")
@Api(value = "event", tags = "13-事件通知告警")
public class EventCodeController {

    @Resource private EmsEventCodeService eventCodeService;

    @Resource private HideEventCodeService hideEventCodeService;

    @Resource private AlarmSwitchService alarmSwitchService;

    @Resource private ProjectExtService projectExtService;

    @PostMapping("/eventEmsCode/get")
    @ApiOperation("查询eventEmsCode")
    @PreAuthorize("hasAuthority('/system/showHideCode')")
    public Result<IPage<EventCodeEntity>> getEventCodeEntity(
            @RequestBody EmsEventCodeRequest emsEventCodeRequest) {
        return Result.success(
                eventCodeService.queryEventMessage(
                        Page.of(
                                emsEventCodeRequest.getPageNum(),
                                emsEventCodeRequest.getPageSize()),
                        emsEventCodeRequest));
    }

    @Transactional
    @PostMapping("/hideEmsCode/add")
    @ApiOperation("增加屏蔽的eventEmsCode")
    @Log(module = "EVENT", methods = "EVENT_EMS_HIDE", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/system/operateHideCode')")
    public Result<Object> saveHideCode(@RequestBody HideCodeRequest hideCodeRequest) {
        if (hideCodeRequest.getHideAll()) {
            if (hideCodeRequest.getLabel() == 1) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getHideAllEmsKernelCode, true)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (hideCodeRequest.getLabel() == 2) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getHideAllEmsSubDeviceCode, true)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            return Result.success();
        } else {
            if (hideCodeRequest.getLabel() == 1) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getHideAllEmsKernelCode, false)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            if (hideCodeRequest.getLabel() == 2) {
                projectExtService
                        .lambdaUpdate()
                        .set(ProjectExtEntity::getHideAllEmsSubDeviceCode, false)
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .update();
            }
            List<HideEventCodeEntity> list =
                    hideCodeRequest.getEventCode() == null
                            ? new ArrayList<>()
                            : hideCodeRequest.getEventCode().stream()
                                    .map(
                                            e ->
                                                    new HideEventCodeEntity(
                                                            WebUtils.projectId.get(),
                                                            e,
                                                            hideCodeRequest.getLabel().toString()))
                                    .collect(Collectors.toList());
            hideEventCodeService.remove(
                    Wrappers.lambdaQuery(HideEventCodeEntity.class)
                            .eq(HideEventCodeEntity::getProjectId, WebUtils.projectId.get())
                            .eq(
                                    HideEventCodeEntity::getType,
                                    hideCodeRequest.getLabel().toString()));
            hideEventCodeService.saveBatch(list);
        }

        return Result.success();
    }

    @Transactional
    @PostMapping("/hideEmsCode/list")
    @ApiOperation("查询屏蔽的eventEmsCode")
    @PreAuthorize("hasAuthority('/system/showHideCode')")
    public Result<Object> listHideCode(@RequestBody HideCodePageRequest hideCodePageRequest) {
        IPage<EventCodeEntity> page =
                hideEventCodeService.getHideEmsEventCode(
                        WebUtils.projectId.get(), hideCodePageRequest);
        ProjectExtEntity projectExtEntity = projectExtService.getById(WebUtils.projectId.get());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Map<String, Object> result;
            if (page != null) {
                // 将 page 转为 Map
                result = objectMapper.convertValue(page, new TypeReference<>() {});
            } else {
                result = new HashMap<>();
            }
            // 增加自定义属性
            result.put("hideAllEmsKernelCode", projectExtEntity.getHideAllEmsKernelCode());
            result.put("hideAllEmsSubDeviceCode", projectExtEntity.getHideAllEmsSubDeviceCode());
            // 返回带有额外属性的结果
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("转换失败: " + e.getMessage());
        }
    }

    @Transactional
    @PostMapping("/alarmSwitch/add")
    @ApiOperation("增加告警开关")
    @Log(module = "EVENT", methods = "EVENT_ALARM_SWITCH", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/system/operateAlarmSwitch')")
    public Result<Object> saveAlarmSwitch(@RequestBody AlarmSwitchRequest alarmSwitchRequest) {
        alarmSwitchService.updateSwitch(alarmSwitchRequest);
        return Result.success();
    }

    @Transactional
    @PostMapping("/alarmSwitch/list")
    @ApiOperation("查询告警开关")
    @PreAuthorize("hasAuthority('/system/showAlarmSwitch')")
    public Result<Object> listAlarmSwitch(@RequestBody HideCodePageRequest alarmSwitchPageRequest) {
        Map<String, Object> res = alarmSwitchService.listAlarmSwitch(alarmSwitchPageRequest);
        return Result.success(res);
    }
}
