package com.wifochina.modules.event.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.mapper.EventCodeMapper;
import com.wifochina.modules.event.request.EmsEventCodeRequest;
import com.wifochina.modules.event.service.EmsEventCodeService;

import io.jsonwebtoken.lang.Strings;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 告警信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-31 19:22:30
 */
@Service
public class EmsEventCodeServiceImpl extends ServiceImpl<EventCodeMapper, EventCodeEntity>
        implements EmsEventCodeService {

    public static Map<Integer, String> DEVICE_TYPE_CODE_MAP;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        List<EventCodeEntity> list =
                this.list(
                        Wrappers.<EventCodeEntity>query()
                                .select("DISTINCT `device_type_code`, `device_type`")
                                .lambda());
        DEVICE_TYPE_CODE_MAP =
                list.stream()
                        .collect(
                                Collectors.toMap(
                                        EventCodeEntity::getDeviceTypeCode,
                                        EventCodeEntity::getDeviceType));
    }

    @Override
    public IPage<EventCodeEntity> queryEventMessage(
            IPage<EventCodeEntity> eventCodePage, EmsEventCodeRequest emsEventCodeRequest) {
        return this.baseMapper.selectPage(
                eventCodePage,
                Wrappers.lambdaQuery(EventCodeEntity.class)
                        .like(
                                Strings.hasLength(emsEventCodeRequest.getEventCode()),
                                EventCodeEntity::getEventCode,
                                emsEventCodeRequest.getEventCode())
                        .like(
                                Strings.hasLength(emsEventCodeRequest.getEventDescription()),
                                EventCodeEntity::getEventDescription,
                                emsEventCodeRequest.getEventDescription())
                        .like(
                                Strings.hasLength(emsEventCodeRequest.getEventDescriptionEn()),
                                EventCodeEntity::getEventDescriptionEn,
                                emsEventCodeRequest.getEventDescriptionEn())
                        .like(
                                Strings.hasLength(emsEventCodeRequest.getPointColumn()),
                                EventCodeEntity::getPointColumn,
                                emsEventCodeRequest.getPointColumn())
                        .eq(
                                emsEventCodeRequest.getLabel() == 1,
                                EventCodeEntity::getDeviceType,
                                "EMS Kernel")
                        .ne(
                                emsEventCodeRequest.getLabel() == 2,
                                EventCodeEntity::getDeviceType,
                                "EMS Kernel")
                        .in(
                                !CollectionUtils.isEmpty(emsEventCodeRequest.getEventLevel()),
                                EventCodeEntity::getEventLevel,
                                emsEventCodeRequest.getEventLevel()));
    }
}
