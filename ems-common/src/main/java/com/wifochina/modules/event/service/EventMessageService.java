package com.wifochina.modules.event.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.event.VO.DeviceVo;
import com.wifochina.modules.event.VO.EventMessageVO;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.request.EventMessageRequest;

/**
 * 事件表 服务类
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public interface EventMessageService extends IService<EventMessageEntity> {

    /**
     * queryEventMessage 获取事件分页列表
     *
     * @param eventMessagePage 事件排序列表
     * @param eventMessageRequest 事件请求
     * @return 事件分页VO
     */
    IPage<EventMessageVO> queryEventMessage(
            IPage<EventMessageEntity> eventMessagePage, EventMessageRequest eventMessageRequest);

    /**
     * 设备id和名称对应关系
     *
     * @return List<DeviceVo> id and name
     */
    List<DeviceVo> getDeviceAndControllableList();

    Long getErrorOrAlarmNum(
            String projectId, RangeRequest rangeRequest, EventLevelEnum eventLevelEnum);

    /**
     * @return 事件中设备类型集合
     */
    List<String> equipTypeList(RangeRequest request);
}
