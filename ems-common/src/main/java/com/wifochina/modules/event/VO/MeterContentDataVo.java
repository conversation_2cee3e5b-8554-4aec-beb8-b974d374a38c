package com.wifochina.modules.event.VO;

import java.util.List;

import com.wifochina.modules.data.entity.MeterContentData;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.group.entity.AmmeterEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-07-18 11:22 AM
 */

@Data
public class MeterContentDataVo extends MeterContentData {

    @ApiModelProperty(value = "时间列表")
    List<MeterEventVo> meterEventList;

    @ApiModelProperty(value = "电表可控列表")
    List<MeterEventCodeEntity> meterControllableItems;

    @ApiModelProperty(value = "电表可控列表")
    private AmmeterEntity ammeterBaseInfo;

    @ApiModelProperty(value = "交流电表功率因数")
    private List<Double> powerFactors;

    @ApiModelProperty(value = "交流电表总功率因数")
    private Double powerFactor;

}
