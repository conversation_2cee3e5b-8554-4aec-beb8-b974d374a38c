package com.wifochina.modules.event.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/*
 * 告警信息表
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@ApiModel(value = "屏蔽事件编码请求")
public class HideCodeRequest {

    @ApiModelProperty(value = "事件编码")
    private List<String> eventCode;

    @ApiModelProperty(value = "分类标签 1 ems-kernel，2 ems-子设备的 3 meter")
    private Integer label;

    @ApiModelProperty(value = "是否屏蔽所有")
    private Boolean hideAll;
}
