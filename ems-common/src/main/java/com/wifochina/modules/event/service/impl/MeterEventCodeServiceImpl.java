package com.wifochina.modules.event.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.mapper.MeterEventCodeMapper;
import com.wifochina.modules.event.request.CommonEventCodeRequest;
import com.wifochina.modules.event.service.MeterEventCodeService;

import io.jsonwebtoken.lang.Strings;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 时间信息告警信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service
public class MeterEventCodeServiceImpl
        extends ServiceImpl<MeterEventCodeMapper, MeterEventCodeEntity>
        implements MeterEventCodeService {

    @Override
    public IPage<MeterEventCodeEntity> queryEventMessage(
            IPage<MeterEventCodeEntity> eventCodePage,
            CommonEventCodeRequest commonEventCodeRequest) {
        return this.baseMapper.selectPage(
                eventCodePage,
                Wrappers.lambdaQuery(MeterEventCodeEntity.class)
                        .like(
                                Strings.hasLength(commonEventCodeRequest.getEventCode()),
                                MeterEventCodeEntity::getEventCode,
                                commonEventCodeRequest.getEventCode())
                        .like(
                                Strings.hasLength(commonEventCodeRequest.getEventDescription()),
                                MeterEventCodeEntity::getDescription,
                                commonEventCodeRequest.getEventDescription())
                        .like(
                                Strings.hasLength(commonEventCodeRequest.getEventDescriptionEn()),
                                MeterEventCodeEntity::getDescriptionEn,
                                commonEventCodeRequest.getEventDescriptionEn())
                        .in(
                                !CollectionUtils.isEmpty(commonEventCodeRequest.getEventLevel()),
                                MeterEventCodeEntity::getEventLevel,
                                commonEventCodeRequest.getEventLevel()));
    }
}
