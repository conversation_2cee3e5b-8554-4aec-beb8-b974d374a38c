package com.wifochina.modules.event.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * 告警信息表
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@ApiModel(value = "事件编码请求")
public class CommonEventCodeRequest extends PageBean {

    @ApiModelProperty(value = "设备类型名称")
    private String deviceType;

    @ApiModelProperty(value = "事件等级Fault、State、Alarm")
    private List<String> eventLevel;

    @ApiModelProperty(value = "事件描述")
    private String eventDescription;

    @ApiModelProperty(value = "英文事件描述")
    private String eventDescriptionEn;

    @ApiModelProperty(value = "事件编码")
    private String eventCode;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
