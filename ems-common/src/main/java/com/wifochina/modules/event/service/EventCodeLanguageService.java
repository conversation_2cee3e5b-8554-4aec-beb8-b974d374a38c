package com.wifochina.modules.event.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.event.VO.EventMessageVO;
import com.wifochina.modules.event.entity.EventCodeLanguageEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 告警信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public interface EventCodeLanguageService extends IService<EventCodeLanguageEntity> {
    Map<String, EventCodeLanguageEntity> eventCodeLanguageMap = new HashMap<>();

    EventMessageVO transEventMessageVOForLanguage(EventMessageVO eventMessageVO);

}
