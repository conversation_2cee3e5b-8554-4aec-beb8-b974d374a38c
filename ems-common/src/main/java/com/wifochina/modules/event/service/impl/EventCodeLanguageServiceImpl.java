package com.wifochina.modules.event.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.event.VO.EventMessageVO;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.EventCodeLanguageEntity;
import com.wifochina.modules.event.mapper.EventCodeLanguageMapper;
import com.wifochina.modules.event.service.EventCodeLanguageService;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

/**
 * 告警信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Service
public class EventCodeLanguageServiceImpl
        extends ServiceImpl<EventCodeLanguageMapper, EventCodeLanguageEntity>
        implements EventCodeLanguageService {

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        List<EventCodeLanguageEntity> list = this.baseMapper.selectList(null);
        for (EventCodeLanguageEntity entity : list) {
            EventCodeLanguageServiceImpl.eventCodeLanguageMap.put(entity.getEventCode(), entity);
        }
    }

    @Override
    public EventMessageVO transEventMessageVOForLanguage(EventMessageVO eventMessageVO) {
        try {
            HttpServletRequest request =
                    ((ServletRequestAttributes)
                                    Objects.requireNonNull(
                                            RequestContextHolder.getRequestAttributes()))
                            .getRequest();
            String language = request.getHeader("Accept-Language");
            String[] area = language.split(",");
            EventCodeLanguageEntity eventCodeLanguageEntity =
                    EventCodeLanguageService.eventCodeLanguageMap.get(eventMessageVO.getEventKey());
            if (eventCodeLanguageEntity != null) {
                String description = getLanguageValueByReflection(eventCodeLanguageEntity, area[0]);
                if (description != null) {
                    eventMessageVO.setEventDescriptionEn(description);
                }
            }
        } catch (Exception e) {
            // todo
            log.error("[language]---->" + e.getMessage());
        }
        return eventMessageVO;
    }

    public static EventCodeEntity transEventCodeForLanguage(EventCodeEntity eventCodeEntity) {
        try {
            HttpServletRequest request =
                    ((ServletRequestAttributes)
                                    Objects.requireNonNull(
                                            RequestContextHolder.getRequestAttributes()))
                            .getRequest();
            String language = request.getHeader("Accept-Language");
            String[] area = language.split(",");
            EventCodeLanguageEntity eventCodeLanguageEntity =
                    EventCodeLanguageService.eventCodeLanguageMap.get(
                            eventCodeEntity.getEventCode());
            if (eventCodeLanguageEntity != null) {
                String description = getLanguageValueByReflection(eventCodeLanguageEntity, area[0]);
                if (description != null) {
                    eventCodeEntity.setEventDescriptionEn(description);
                }
            }
        } catch (Exception ignored) {
        }
        return eventCodeEntity;
    }

    static String getLanguageValueByReflection(EventCodeLanguageEntity entity, String languageTag) {
        // 将语言标识转换为驼峰命名格式，比如 zh-CN -> zhCn
        String fieldName = languageTag.replace("-", "");

        try {
            // 获取相应的 getter 方法，比如 getZhCn
            String getterName =
                    "get"
                            + Character.toUpperCase(fieldName.charAt(0))
                            + fieldName.charAt(1)
                            + fieldName.substring(3);
            Method getterMethod = EventCodeLanguageEntity.class.getMethod(getterName);
            return (String) getterMethod.invoke(entity); // 调用 getter 方法
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            return null; // 处理没有对应方法的情况
        }
    }
}
