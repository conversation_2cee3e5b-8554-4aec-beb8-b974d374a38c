<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.event.mapper.EventMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wifochina.modules.event.entity.EventMessageEntity">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="device_id" property="deviceId"/>
        <result column="equip_type" property="equipType"/>
        <result column="equip_name" property="equipName"/>
        <result column="event_type" property="eventType"/>
        <result column="event_code" property="eventCode"/>
        <result column="status" property="status"/>
        <result column="event_description" property="eventDescription"/>
        <result column="event_description_en" property="eventDescriptionEn"/>
        <result column="event_on_off" property="eventOnOff"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="EventMessageVOMap" type="com.wifochina.modules.event.VO.EventMessageVO">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="device_id" property="deviceId"/>
        <result column="name" property="deviceName"/>
        <result column="equip_type" property="equipType"/>
        <result column="equip_name" property="equipName"/>
        <result column="event_type" property="eventType"/>
        <result column="event_key" property="eventKey"/>
        <result column="status" property="status"/>
        <result column="event_description" property="eventDescription"/>
        <result column="event_description_en" property="eventDescriptionEn"/>
        <result column="event_on_off" property="eventOnOff"/>
    </resultMap>

    <select id="queryEventMessage" parameterType="com.wifochina.modules.event.request.EventMessageRequest"
            resultMap="EventMessageVOMap">
        select
        d.name,m.id,m.create_time,m.device_id,m.equip_type,m.equip_name,m.event_type,m.event_key,e,m.status,m.event_description,m.event_description_en,
        m.event_on_off from t_event_message m force index(device_id_status_event_type_create_time) ,t_device d where
        m.project_id = #{eventRequest.projectId} and d.id = m.device_id
        <if test="eventRequest.deviceId!=null and eventRequest.deviceId.trim()!=''">
            and m.device_id = #{eventRequest.deviceId}
        </if>
        <if test="eventRequest.maintain!=null">
            and m.maintain = #{eventRequest.maintain}
        </if>
        <if test="eventRequest.eventDescription!=null and eventRequest.eventDescription.trim()!=''">
            and m.event_description like CONCAT(CONCAT('%',#{eventRequest.eventDescription}),'%')
        </if>
        <if test="eventRequest.eventCode!=null and eventRequest.eventCode.trim()!=''">
            and m.event_key = #{eventRequest.eventCode}
        </if>
        <if test="eventRequest.equipType!=null and eventRequest.equipType.trim()!=''">
            and m.equip_type like CONCAT(CONCAT('%',#{eventRequest.equipType}),'%')
        </if>
        <if test="eventRequest.status!=null and eventRequest.status.size()>0">
            and (m.status in<foreach collection="eventRequest.status" index="index" item="item" open="(" separator=","
                                     close=")">#{item}</foreach>)
        </if>
        <if test="eventRequest.eventType!= null and eventRequest.eventType.size()>0">
            and (m.event_type in<foreach collection="eventRequest.eventType" index="index" item="item" open="("
                                         separator="," close=")">#{item}</foreach>)
        </if>
        <if test="eventRequest.startDate!=null">
            <!--    <![CDATA[ and DATE_FORMAT(m.create_time, '%Y-%m-%d %H:%i:%s') >= DATE_FORMAT(#{eventRequest.startDate}, '%Y-%m-%d %H:%i:%s') ]]>-->
            and m.create_time >= #{eventRequest.startDate}
        </if>
        <if test="eventRequest.endDate!=null">
            <!--    <![CDATA[ and DATE_FORMAT(m.create_time, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT(#{eventRequest.endDate}, '%Y-%m-%d %H:%i:%s') ]]>-->
            <![CDATA[ and m.create_time <= #{eventRequest.endDate} ]]>
        </if>
        order by m.create_time desc
    </select>

</mapper>
