package com.wifochina.modules.event.VO;

import com.baomidou.mybatisplus.annotation.TableField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 时间信息告警信息表
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Getter
@Setter
@ApiModel(value = "SteerableEventVo对象")
public class SteerableEventVo {

    @ApiModelProperty(value = "事件等级Fault、State、Alarm")
    private String eventLevel;

    @ApiModelProperty("事件描述")
    private String description;

    @ApiModelProperty("英文事件描述")
    private String descriptionEn;

    @ApiModelProperty(value = "是否触发，true为告警，false为正常")
    @TableField(exist = false)
    private Boolean value;
}
