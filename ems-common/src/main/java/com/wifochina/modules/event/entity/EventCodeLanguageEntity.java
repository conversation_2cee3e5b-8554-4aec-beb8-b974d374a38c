package com.wifochina.modules.event.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;

import lombok.Getter;
import lombok.Setter;

/**
 * 告警信息表
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Getter
@Setter
@TableName("t_event_code_language")
@ApiModel(value = "EventCodeLanguageEntity对象", description = "告警信息表")
public class EventCodeLanguageEntity extends AbstractEventCode {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("zh_CN")
    private String zhCN;

    @TableField("en_US")
    private String enUS;

    @TableField("de_DE")
    private String deDE;

    @TableField("nl_NL")
    private String nlNL;

    @TableField(value = "fr_FR", exist = false)
    private String frFR;

    @TableField(value = "es_ES", exist = false)
    private String esES;

    @TableField(value = "pt_PT", exist = false)
    private String ptPT;

    @TableField("it_IT")
    private String itIT;

    @TableField("pl_PL")
    private String plPL;

    @TableField("sv_SE")
    private String svSE;

    @TableField("bg_BG")
    private String bgBG;
}
