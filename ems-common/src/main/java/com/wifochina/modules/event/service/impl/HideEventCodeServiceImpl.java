package com.wifochina.modules.event.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.enums.HideCodeLabelEnums;
import com.wifochina.modules.event.mapper.HideEventCodeMapper;
import com.wifochina.modules.event.request.HideCodePageRequest;
import com.wifochina.modules.event.service.EmsEventCodeService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.event.service.MeterEventCodeService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 告警信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-31 19:22:30
 */
@Service
public class HideEventCodeServiceImpl extends ServiceImpl<HideEventCodeMapper, HideEventCodeEntity>
        implements HideEventCodeService {

    @Resource private EmsEventCodeService eventCodeService;

    @Resource private MeterEventCodeService meterEventCodeService;

    @Resource private ProjectExtService projectExtService;

    @Override
    public IPage<EventCodeEntity> getHideEmsEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest) {
        ProjectExtEntity projectExtEntity =
                projectExtService
                        .lambdaQuery()
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .one();
        boolean hideAll = false;
        if (hideCodePageRequest.getLabel() == 1) {
            if (projectExtEntity.getHideAllEmsKernelCode()) {
                hideAll = true;
            }
        }
        if (hideCodePageRequest.getLabel() == 2) {
            if (projectExtEntity.getHideAllEmsSubDeviceCode()) {
                hideAll = true;
            }
        }
        List<HideEventCodeEntity> hideEventCodeEntities =
                this.lambdaQuery()
                        .eq(HideEventCodeEntity::getProjectId, projectId)
                        .eq(HideEventCodeEntity::getType, hideCodePageRequest.getLabel())
                        .list();
        // 提取事件代码列表，并做非空判断
        List<String> eventCodes =
                hideEventCodeEntities.stream()
                        .map(HideEventCodeEntity::getEventCode)
                        .collect(Collectors.toList());
        if (!hideAll && eventCodes.isEmpty()) {
            return null;
        }
        // 分页查询事件代码，使用提取的事件代码列表
        return eventCodeService.page(
                Page.of(hideCodePageRequest.getPageNum(), hideCodePageRequest.getPageSize()),
                Wrappers.lambdaQuery(EventCodeEntity.class)
                        .eq(
                                hideCodePageRequest.getLabel() == 1,
                                EventCodeEntity::getDeviceType,
                                "EMS Kernel")
                        .ne(
                                hideCodePageRequest.getLabel() == 2,
                                EventCodeEntity::getDeviceType,
                                "EMS Kernel")
                        .in(!hideAll, EventCodeEntity::getEventCode, eventCodes));
    }

    @Override
    public IPage<MeterEventCodeEntity> getHideMeterEventCode(
            String projectId, HideCodePageRequest hideCodePageRequest) {
        ProjectExtEntity projectExtEntity =
                projectExtService
                        .lambdaQuery()
                        .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                        .one();
        List<HideEventCodeEntity> hideEventCodeEntities =
                this.lambdaQuery()
                        .eq(HideEventCodeEntity::getProjectId, projectId)
                        .eq(
                                HideEventCodeEntity::getType,
                                String.valueOf(HideCodeLabelEnums.METER_DEVICE.getLabel()))
                        .list();
        // 提取事件代码列表，并做非空判断
        List<String> eventCodes =
                hideEventCodeEntities.stream()
                        .map(HideEventCodeEntity::getEventCode)
                        .collect(Collectors.toList());
        if (!projectExtEntity.getHideAllMeterCode() && eventCodes.isEmpty()) {
            return null;
        }
        // 分页查询事件代码，使用提取的事件代码列表
        return meterEventCodeService.page(
                Page.of(hideCodePageRequest.getPageNum(), hideCodePageRequest.getPageSize()),
                Wrappers.lambdaQuery(MeterEventCodeEntity.class)
                        .in(
                                !projectExtEntity.getHideAllMeterCode(),
                                MeterEventCodeEntity::getEventCode,
                                eventCodes));
    }

    @Override
    public List<HideEventCodeEntity> getHideEventCode(String projectId) {
        return getHideAllTypeEventCodeList(projectId).stream()
                .map(HideEventCodeEntity::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getHideAllTypeEventCodeList(String projectId) {
        ProjectExtEntity projectExtEntity = projectExtService.getById(projectId);
        List<String> eventHideCodeList = new ArrayList<>();
        if (projectExtEntity.getHideAllEmsKernelCode()) {
            List<EventCodeEntity> eventCodeEntities =
                    eventCodeService
                            .lambdaQuery()
                            .eq(EventCodeEntity::getDeviceType, "EMS Kernel")
                            .list();
            eventHideCodeList.addAll(
                    eventCodeEntities.stream()
                            .map(EventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        } else {
            List<HideEventCodeEntity> hideEventCodeEntities =
                    this.lambdaQuery()
                            .eq(HideEventCodeEntity::getProjectId, projectId)
                            .eq(
                                    HideEventCodeEntity::getType,
                                    String.valueOf(HideCodeLabelEnums.EMS_KERNEL.getLabel()))
                            .list();
            eventHideCodeList.addAll(
                    hideEventCodeEntities.stream()
                            .map(HideEventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        }

        if (projectExtEntity.getHideAllEmsSubDeviceCode()) {
            List<EventCodeEntity> eventCodeEntities =
                    eventCodeService
                            .lambdaQuery()
                            .ne(EventCodeEntity::getDeviceType, "EMS Kernel")
                            .list();
            eventHideCodeList.addAll(
                    eventCodeEntities.stream()
                            .map(EventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        } else {
            List<HideEventCodeEntity> hideEventCodeEntities =
                    this.lambdaQuery()
                            .eq(HideEventCodeEntity::getProjectId, projectId)
                            .eq(
                                    HideEventCodeEntity::getType,
                                    String.valueOf(HideCodeLabelEnums.EMS_SUB_DEVICE.getLabel()))
                            .list();
            eventHideCodeList.addAll(
                    hideEventCodeEntities.stream()
                            .map(HideEventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        }
        if (projectExtEntity.getHideAllMeterCode()) {
            List<MeterEventCodeEntity> meterEventCodeEntities =
                    meterEventCodeService
                            .lambdaQuery()
                            .eq(MeterEventCodeEntity::getProjectId, projectId)
                            .list();
            eventHideCodeList.addAll(
                    meterEventCodeEntities.stream()
                            .map(MeterEventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        } else {
            List<HideEventCodeEntity> hideEventCodeEntities =
                    this.lambdaQuery()
                            .eq(HideEventCodeEntity::getProjectId, projectId)
                            .eq(
                                    HideEventCodeEntity::getType,
                                    String.valueOf(HideCodeLabelEnums.METER_DEVICE.getLabel()))
                            .list();
            eventHideCodeList.addAll(
                    hideEventCodeEntities.stream()
                            .map(HideEventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        }
        return eventHideCodeList;
    }

    @Override
    public List<String> getHideEmsTypeEventCodes(String projectId) {
        ProjectExtEntity projectExtEntity = projectExtService.getById(projectId);
        List<String> eventHideCodeList = new ArrayList<>();
        if (projectExtEntity.getHideAllEmsKernelCode()) {
            List<EventCodeEntity> eventCodeEntities =
                    eventCodeService
                            .lambdaQuery()
                            .eq(EventCodeEntity::getDeviceType, "EMS Kernel")
                            .list();
            eventHideCodeList.addAll(
                    eventCodeEntities.stream()
                            .map(EventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        } else {
            List<HideEventCodeEntity> hideEventCodeEntities =
                    this.lambdaQuery()
                            .eq(HideEventCodeEntity::getProjectId, projectId)
                            .eq(
                                    HideEventCodeEntity::getType,
                                    String.valueOf(HideCodeLabelEnums.EMS_KERNEL.getLabel()))
                            .list();
            eventHideCodeList.addAll(
                    hideEventCodeEntities.stream()
                            .map(HideEventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        }

        if (projectExtEntity.getHideAllEmsSubDeviceCode()) {
            List<EventCodeEntity> eventCodeEntities =
                    eventCodeService
                            .lambdaQuery()
                            .ne(EventCodeEntity::getDeviceType, "EMS Kernel")
                            .list();
            eventHideCodeList.addAll(
                    eventCodeEntities.stream()
                            .map(EventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        } else {
            List<HideEventCodeEntity> hideEventCodeEntities =
                    this.lambdaQuery()
                            .eq(HideEventCodeEntity::getProjectId, projectId)
                            .eq(
                                    HideEventCodeEntity::getType,
                                    String.valueOf(HideCodeLabelEnums.EMS_SUB_DEVICE.getLabel()))
                            .list();
            eventHideCodeList.addAll(
                    hideEventCodeEntities.stream()
                            .map(HideEventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        }
        return eventHideCodeList;
    }

    @Override
    public List<String> getHideMeterTypeEventCodes(String projectId) {
        ProjectExtEntity projectExtEntity = projectExtService.getById(projectId);
        List<String> eventHideCodeList = new ArrayList<>();
        if (projectExtEntity.getHideAllMeterCode()) {
            List<MeterEventCodeEntity> meterEventCodeEntities =
                    meterEventCodeService
                            .lambdaQuery()
                            .eq(MeterEventCodeEntity::getProjectId, projectId)
                            .list();
            eventHideCodeList.addAll(
                    meterEventCodeEntities.stream()
                            .map(MeterEventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        } else {
            List<HideEventCodeEntity> hideEventCodeEntities =
                    this.lambdaQuery()
                            .eq(HideEventCodeEntity::getProjectId, projectId)
                            .eq(
                                    HideEventCodeEntity::getType,
                                    String.valueOf(HideCodeLabelEnums.METER_DEVICE.getLabel()))
                            .list();
            eventHideCodeList.addAll(
                    hideEventCodeEntities.stream()
                            .map(HideEventCodeEntity::getEventCode)
                            .collect(Collectors.toList()));
        }

        return eventHideCodeList;
    }
}
