package com.wifochina.modules.event.entity;

import com.wifochina.modules.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AbstractEventCode extends BaseEntity {
    @ApiModelProperty(value = "对应的点位字段")
    private String pointColumn;

    @ApiModelProperty(value = "设备类型码")
    private Integer deviceTypeCode;

    @ApiModelProperty(value = "设备类型名称")
    private String deviceType;

    @ApiModelProperty(value = "事件等级Fault、State、Alarm")
    private String eventLevel;

    @ApiModelProperty(value = "BIT码偏移量")
    private Integer bitOffset;

    @ApiModelProperty(value = "BIT码")
    private Integer bitValue;

    @ApiModelProperty(value = "事件编码")
    private String eventCode;
}
