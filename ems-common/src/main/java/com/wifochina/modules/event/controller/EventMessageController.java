package com.wifochina.modules.event.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.modules.event.VO.DeviceVo;
import com.wifochina.modules.event.VO.EventMessageVO;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.request.EventMessageDeleteRequest;
import com.wifochina.modules.event.request.EventMessageRequest;
import com.wifochina.modules.event.request.EventStatusChangeRequest;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 事件表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@RestController
@RequestMapping("/event")
@Api(value = "event", tags = "13-事件通知告警")
@RequiredArgsConstructor
public class EventMessageController {

    private final EventMessageService eventMessageService;

    private final ProjectService projectService;

    private final HideEventCodeService hideEventCodeService;

    private final LogService logService;

    @PostMapping("/getEventMessage")
    @ApiOperation("查询所有事件、告警、故障")
    @PreAuthorize("hasAuthority('/event')")
    public Result<IPage<EventMessageVO>> getEventMessage(@RequestBody EventMessageRequest request) {
        ServiceAssert.isTrue(
                request.getEndDate().compareTo(request.getStartDate()) >= 0,
                ErrorResultCode.START_GE_END_TIME.value());
        request.setProjectId(
                Optional.ofNullable(request.getProjectId()).orElse(WebUtils.projectId.get()));
        ProjectEntity projectEntity = projectService.getById(request.getProjectId());
        // 暂时做一下兼容 后续可以把项目id校验放到 filter里
        if (projectEntity == null) {
            return Result.failure(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        if (request.getStartDate() < projectEntity.getCreateTime()) {
            request.setStartDate(projectEntity.getCreateTime());
            if (request.getEndDate() < projectEntity.getCreateTime()) {
                request.setEndDate(projectEntity.getCreateTime());
            }
        }
        // 非管理员需校验维护权限
        final boolean isEmptyMaintain = request.getMaintain() == null;
        final boolean isAdmin = EmsConstants.WH_ADMIN_USERNAME.equals(SecurityUtil.getUsername());
        final boolean hasMaintainAuth =
                SecurityUtil.hasAuthority(EmsConstants.DEVICE_MAINTAIN_AUTHORITY);
        if (isEmptyMaintain && !hasMaintainAuth) {
            request.setMaintain(false);
        } else if (!isAdmin && !hasMaintainAuth) {
            request.setMaintain(false);
        }
        IPage<EventMessageVO> list =
                eventMessageService.queryEventMessage(
                        Page.of(request.getPageNum(), request.getPageSize()), request);
        return Result.success(list);
    }

    @PostMapping("/getEventMessage_inner")
    @ApiOperation("内部查询所有事件、告警、故障")
    public Result<IPage<EventMessageVO>> getEventMessageInner(
            @RequestBody EventMessageRequest eventMessageRequest) {
        return getEventMessage(eventMessageRequest);
    }

    @PostMapping("/update")
    @ApiOperation("确认告警、故障")
    @PreAuthorize("hasAuthority('/event/confirm') or hasAuthority('/event/updateBatch')")
    @Log(module = "EVENT", methods = "EVENT_CONFIRM", type = OperationType.UPDATE_SIMPLE)
    public Result<IPage<EventMessageVO>> updateEventMessage(
            @RequestBody EventStatusChangeRequest eventStatusChangeRequest) {
        eventMessageService.update(
                Wrappers.lambdaUpdate(EventMessageEntity.class)
                        .set(EventMessageEntity::getStatus, 1)
                        .in(EventMessageEntity::getId, eventStatusChangeRequest.getIds()));
        return Result.success();
    }

    @PostMapping("/delete")
    @ApiOperation("删除告警、故障")
    @PreAuthorize("hasAuthority('/event/delete')")
    @Log(module = "EVENT", methods = "EVENT_DELETE", type = OperationType.DEL_SIMPLE)
    public Result<Object> delete(@RequestBody EventMessageDeleteRequest eventMessageDeleteRequest) {
        eventMessageService.update(
                Wrappers.lambdaUpdate(EventMessageEntity.class)
                        .set(EventMessageEntity::getWhetherDelete, true)
                        .eq(EventMessageEntity::getProjectId, WebUtils.projectId.get())
                        .ge(
                                EventMessageEntity::getCreateTime,
                                eventMessageDeleteRequest.getStartDate())
                        .le(
                                EventMessageEntity::getCreateTime,
                                eventMessageDeleteRequest.getEndDate())
                        .in(
                                EventMessageEntity::getEventType,
                                List.of(
                                        EventLevelEnum.ALARM.getLevel(),
                                        EventLevelEnum.FAULT.getLevel())));
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        //        logService.logSelfDefinedDetail(
        //                LogInfo.builder()
        //                        .module("EVENT")
        //                        .method("EVENT_DELETE")
        //                        .object(eventMessageDeleteRequest)
        //                        .detail(
        //                                String.format(
        //                                        "清除 %s 至 %s 的事件",
        //                                        MyTimeUtil.formatMilliTimestampWithTimezone(
        //                                                eventMessageDeleteRequest.getStartDate(),
        //                                                projectEntity.getTimezone()),
        //                                        MyTimeUtil.formatMilliTimestampWithTimezone(
        //                                                eventMessageDeleteRequest.getEndDate(),
        //                                                projectEntity.getTimezone())))
        //                        .traceId(
        //                                String.format(
        //                                        LogService.EVENT_DELETE_TRACE_FORMAT,
        //                                        WebUtils.projectId.get()))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/updateBatch")
    @ApiOperation("批量确认告警、故障")
    @PreAuthorize("hasAuthority('/event/updateBatch') or hasAuthority('/event/confirm')")
    @Log(module = "EVENT", methods = "EVENT_CONFIRM", type = OperationType.UPDATE_SIMPLE)
    public Result<Object> updateBatch(
            @RequestBody EventMessageDeleteRequest eventMessageDeleteRequest) {
        eventMessageService.update(
                Wrappers.lambdaUpdate(EventMessageEntity.class)
                        .set(EventMessageEntity::getStatus, 1)
                        .eq(EventMessageEntity::getProjectId, WebUtils.projectId.get())
                        .ge(
                                EventMessageEntity::getCreateTime,
                                eventMessageDeleteRequest.getStartDate())
                        .le(
                                EventMessageEntity::getCreateTime,
                                eventMessageDeleteRequest.getEndDate())
                        .in(
                                EventMessageEntity::getEventType,
                                List.of(
                                        EventLevelEnum.ALARM.getLevel(),
                                        EventLevelEnum.FAULT.getLevel())));
        return Result.success();
    }

    @PostMapping("/getEmsAndControlName")
    @ApiOperation("EMS和可控设备集合")
    public Result<List<DeviceVo>> deviceAndControllableList() {
        return Result.success(eventMessageService.getDeviceAndControllableList());
    }

    @PostMapping("/getHideCode")
    @ApiOperation("查询所有的hideCode")
    public Result<List<HideEventCodeEntity>> getHideEventCode() {
        return Result.success(hideEventCodeService.getHideEventCode(WebUtils.projectId.get()));
    }

    @GetMapping("/get_equip_type")
    @ApiOperation("获取所有设备类型合集")
    public Result<List<String>> equipTypeList(@RequestBody RangeRequest request) {
        return Result.success(eventMessageService.equipTypeList(request));
    }
}
