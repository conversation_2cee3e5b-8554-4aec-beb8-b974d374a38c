package com.wifochina.modules.event.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 告警开关表
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_alarm_switch")
@ApiModel(value = "AlarmSwitchEntity对象", description = "告警开关")
public class AlarmSwitchEntity extends BaseEntity {

    // for mybatis need a constructor
    @SuppressWarnings("unused")
    public AlarmSwitchEntity() {}

    public AlarmSwitchEntity(String projectId, String eventCode, String type) {
        this.eventCode = eventCode;
        this.projectId = projectId;
        this.type = type;
    }

    public AlarmSwitchEntity(String eventCode) {
        this.eventCode = eventCode;
    }

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "事件编码")
    private String eventCode;

    @ApiModelProperty(value = "类型 1-EMS核心 2-EMS子设备 3-METER")
    private String type;

    @ApiModelProperty(value = "是否启用告警 true-启用 false-禁用")
    private Boolean enabled;
}
