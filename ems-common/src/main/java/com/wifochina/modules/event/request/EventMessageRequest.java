package com.wifochina.modules.event.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * EventMessageRequest
 *
 * @since 4/2/2022 3:33 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "事件请求")
@EqualsAndHashCode(callSuper = false)
public class EventMessageRequest extends PageBean {

    @ApiModelProperty(value = "开始时间")
    private Long startDate;

    @ApiModelProperty(value = "结束时间")
    private Long endDate;

    @ApiModelProperty(value = "设备ID，如果查询所有则传入null或者空字符串")
    private String deviceId;

    @ApiModelProperty(value = "告警名称(事件描述)")
    private String eventDescription;

    @ApiModelProperty(value = "英文告警名称(事件描述)")
    private String eventDescriptionEn;

    @ApiModelProperty(value = "告警编码")
    private String eventCode;

    @ApiModelProperty(value = "设备类型")
    private String equipType;

    @ApiModelProperty(value = "事件处理状态，1已处理，0未处理")
    private List<String> status;

    @ApiModelProperty(value = "告警类型：Fault(故障)、Alarm(告警)、State(事件)")
    private List<String> eventType;

    @ApiModelProperty(value = "project id")
    private String projectId;

    @ApiModelProperty(value = "是否维护，不需要传递")
    private Boolean maintain;
}
