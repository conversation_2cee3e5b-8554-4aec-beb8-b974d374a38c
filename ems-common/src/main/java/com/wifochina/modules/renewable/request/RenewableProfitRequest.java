package com.wifochina.modules.renewable.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-04-10 19:20:43
 */
@Data
@Accessors(chain = true)
public class RenewableProfitRequest {

    public RenewableProfitRequest() {}

    public RenewableProfitRequest(Long start, Long end, String projectId, String type) {
        this.start = start;
        this.end = end;
        this.projectId = projectId;
        this.type = type;
    }

    public RenewableProfitRequest(String projectId, String type) {
        this.projectId = projectId;
        this.type = type;
    }

    @ApiModelProperty(value = "开始时间 时间戳", required = true)
    private Long start;

    @ApiModelProperty(value = "结束时间 时间戳", required = true)
    private Long end;

    @ApiModelProperty(value = "项目id", required = true)
    private String projectId;

    @ApiModelProperty(value = "类型", required = true)
    private String type;

    private List<Long> dataCalibrationTimes;
}
