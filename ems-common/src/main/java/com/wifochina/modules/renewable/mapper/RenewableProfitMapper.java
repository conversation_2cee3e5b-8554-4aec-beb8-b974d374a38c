package com.wifochina.modules.renewable.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.renewable.request.RenewableProfitRequest;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface RenewableProfitMapper extends BaseMapper<RenewableProfitEntity> {
    RenewableProfitEntity queryRenewableTotalProfit(RenewableProfitRequest renewableProfitRequest);
}
