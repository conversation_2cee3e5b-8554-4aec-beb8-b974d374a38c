package com.wifochina.modules.renewable.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-04-10 19:20:43
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class RenewableProfitOneRequest {

    @ApiModelProperty(value = "时间 时间戳", required = true)
    private Long time;

    @ApiModelProperty(value = "项目id", required = true)
    private String projectId;

    @ApiModelProperty(value = "类型", required = true)
    private String type;

    @ApiModelProperty(value = "是否是数据校准数据", required = true)
    private boolean dataCalibrationFlag;
}
