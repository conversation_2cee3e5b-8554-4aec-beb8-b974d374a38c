package com.wifochina.modules.renewable;

import com.wifochina.modules.renewable.entity.RenewablePeriod;

import java.lang.reflect.Field;

/**
 * Created on 2024/6/21 16:54.
 *
 * <AUTHOR>
 */
public class RenewableUtils {

    /**
     * 通过反射把 double类型的 除了Price字段开头的 其他的字段 值相加 写回到 result里
     *
     * @param result : 返回的结果的对象
     * @param source : source
     */
    public static <T extends RenewablePeriod> void renewableProfitFieldAdd(
            RenewablePeriod result, RenewablePeriod source) {
        for (Field field : result.getClass().getDeclaredFields()) {
            if (field.getType() == double.class) {
                try {
                    if (field.getName().contains("Price")) {
                        continue;
                    }
                    field.setAccessible(true);
                    double value = field.getDouble(source);
                    double value2 = field.getDouble(result);
                    field.set(result, value + value2);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }
}
