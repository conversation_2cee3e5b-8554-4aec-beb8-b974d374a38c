package com.wifochina.modules.renewable;

import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.remedies.request.DataCalibrationRenewable;
import com.wifochina.modules.renewable.context.CalcRenewableProfitCoreContext;
import com.wifochina.modules.renewable.entity.RenewableDynamicProfitEntity;
import com.wifochina.modules.renewable.entity.RenewablePeriod;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.renewable.request.RenewableProfitOneRequest;
import com.wifochina.modules.renewable.vo.RenewableProfitVo;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import javax.sql.rowset.serial.SerialException;

/**
 * 保存点收益的 适配器类 adapter <br>
 * Created on 2024/5/23 19:34.
 *
 * @param T : extends PvWindPeriod
 * <AUTHOR>
 */
public interface RenewableAdapterService<T extends RenewablePeriod> {

    void calcRenewablePeriodProfitCore(
            CalcRenewableProfitCoreContext context,
            ElectricPriceEntity price,
            ElectricPriceEntity superPrice,
            T renewablePeriodEntity);

    default void initPrice(ElectricPriceEntity superPrice, ElectricPriceEntity price) {
        superPrice.setSellPrice(Objects.requireNonNullElse(superPrice.getSellPrice(), 0.0));
        superPrice.setBuyPrice(Objects.requireNonNullElse(superPrice.getBuyPrice(), 0.0));
        superPrice.setPvPrice(Objects.requireNonNullElse(superPrice.getPvPrice(), 0.0));
        superPrice.setPvDfPrice(Objects.requireNonNullElse(superPrice.getPvDfPrice(), 0.0));
        superPrice.setPvSelfPrice(Objects.requireNonNullElse(superPrice.getPvSelfPrice(), 0.0));
        superPrice.setPvSubsidyPrice(
                Objects.requireNonNullElse(superPrice.getPvSubsidyPrice(), 0.0));
        superPrice.setWindPrice(Objects.requireNonNullElse(superPrice.getWindPrice(), 0.0));
        superPrice.setWindDfPrice(Objects.requireNonNullElse(superPrice.getWindDfPrice(), 0.0));
        superPrice.setWindSelfPrice(Objects.requireNonNullElse(superPrice.getWindSelfPrice(), 0.0));
        superPrice.setWindSubsidyPrice(
                Objects.requireNonNullElse(superPrice.getWindSubsidyPrice(), 0.0));
        // 1.4.2 add 余热发电相关
        superPrice.setWasterPrice(Objects.requireNonNullElse(superPrice.getWasterPrice(), 0.0));
        superPrice.setWasterDfPrice(Objects.requireNonNullElse(superPrice.getWasterDfPrice(), 0.0));
        superPrice.setWasterSelfPrice(
                Objects.requireNonNullElse(superPrice.getWasterSelfPrice(), 0.0));
        superPrice.setWasterSubsidyPrice(
                Objects.requireNonNullElse(superPrice.getWasterSubsidyPrice(), 0.0));
    }

    default RenewablePowerDatas getPowerDatas(
            double pvPower, double onLinePower, double dcdcPower) {
        return new RenewablePowerDatas() {
            @Override
            public Double power() {
                double power;
                power = pvPower < 0 ? 0 : pvPower;
                return power + dcdcPower();
            }

            @Override
            public Double onlinePower() {
                return onLinePower;
            }

            @Override
            public Double dcdcPower() {
                return dcdcPower;
            }

            @Override
            public Double selfPower() {
                double power = power();
                // 自用电 =  pv - online
                double selfPower = power - onlinePower();
                selfPower = selfPower < 0 ? 0 : selfPower;
                return selfPower;
            }
        };
    }

    /**
     * 保存 可再生能源相关收益 (PV,WIND,Waster)
     *
     * @param context : ElectricAdapterContext
     */
    default void saveRenewable(RenewableAdapterContext context) {
        // 默认要去处理 PV WIND 和 余热发电 3种收益
        List<String> needCalculateTypes =
                List.of(
                        CalculateTypeEnum.PV.name(),
                        CalculateTypeEnum.WIND.name(),
                        CalculateTypeEnum.WASTER.name());
        Optional.ofNullable(context.electricPriceParent())
                .flatMap(
                        electricPriceEntity ->
                                Optional.ofNullable(electricPriceEntity.getPeriodPriceList()))
                .ifPresent(
                        periodList -> {
                            needCalculateTypes.forEach(
                                    calculateType ->
                                            calculateSave(
                                                    // 正常job执行是零点后 跑前一天收益数据 所以 minus - 1
                                                    LocalDate.now(
                                                                    ZoneId.of(
                                                                            context.project()
                                                                                    .getTimezone()))
                                                            .minusDays(1),
                                                    calculateType,
                                                    context));
                        });
    }

    void calculateSave(LocalDate time, String calculateType, RenewableAdapterContext context);

    void calculateSaveForDataCalibration(
            LocalDate time, String calculateType, RenewableDataCalibrationAdapterContext context)
            throws SerialException;

    List<T> calculateRenewableProfit(
            LocalDate time, String calculateType, RenewableAdapterContext context);

    List<T> calculateRenewableProfitForDataCalibration(
            LocalDate time, String calculateType, RenewableDataCalibrationAdapterContext context);

    RenewableProfitVo calculateRenewableProfitVo(
            BenefitRequest benefitRequest, String calculateType, ProjectEntity project);

    /**
     * 计算 时段数据
     *
     * @param projectEntity
     * @param superPrice
     * @param price
     * @param electricPeriod
     * @param pvOrWindType
     * @param renewablePowerDatas
     */
    void calculatePeriod(
            ProjectEntity projectEntity,
            ElectricPriceEntity superPrice,
            ElectricPriceEntity price,
            T electricPeriod,
            String pvOrWindType,
            RenewablePowerDatas renewablePowerDatas);

    void queryPvOrWindData(RenewableProfitOneRequest request, QueryRenewableResult result);

    void queryCalibration(
            String projectId, String type, Long start, Long end, QueryRenewableResult result);

    /**
     * 电价时段 类型 区分 尖峰苹果(原来的) 和 新增的 海外 动态自定义电价方式
     *
     * @return : ElectricPriceTypeEnum.value
     * @see com.wifochina.common.constants.ElectricPriceTypeEnum
     */
    Set<String> type();

    interface RenewableAdapterContext {
        /**
         * 电价 时段 的实体列表
         *
         * @return : list
         */
        ElectricPriceEntity electricPriceParent();

        /**
         * 项目entity
         *
         * @return : entity
         */
        ProjectEntity project();
    }

    interface RenewableDataCalibrationAdapterContext extends RenewableAdapterContext {
        DataCalibrationRenewable dataCalibration();
    }

    interface QueryRenewableResult {
        void peakValleysPeriodPostProcessor(List<RenewableProfitEntity> t);

        void dynamicPeriodPostProcessor(List<RenewableDynamicProfitEntity> t);
    }
}
