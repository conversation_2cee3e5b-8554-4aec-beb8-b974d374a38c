package com.wifochina.modules.renewable.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;

/**
 * Created on 2024/4/22 18:25.
 *
 * <AUTHOR>
 */
@Data
public class RenewableProfitVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电量识符id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("年度")
    private Integer year;

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("日")
    private Integer day;

    @ApiModelProperty("pv还是wind/waster")
    private String renewableType;

    @ApiModelProperty("峰段放电量")
    private double peakDischargeQuantity;

    @ApiModelProperty("谷段放电量")
    private double vallyDischargeQuantity;

    @ApiModelProperty("平段放电量")
    private double flatDischargeQuantity;

    @ApiModelProperty("尖峰放电量")
    private double tipDischargeQuantity;

    @ApiModelProperty("深谷放电量")
    private double deepVallyDischargeQuantity;

    @ApiModelProperty("峰段自用电量")
    private double peakDischargeQuantityOnline;

    @ApiModelProperty("谷段自用放电量")
    private double vallyDischargeQuantityOnline;

    @ApiModelProperty("平段自用放电量")
    private double flatDischargeQuantityOnline;

    @ApiModelProperty("尖峰自用放电量")
    private double tipDischargeQuantityOnline;

    @ApiModelProperty("深谷自用放电量")
    private double deepVallyDischargeQuantityOnline;

    @ApiModelProperty("峰段自用电量")
    private double peakDischargeQuantityDcdc;

    @ApiModelProperty("谷段自用放电量")
    private double vallyDischargeQuantityDcdc;

    @ApiModelProperty("平段自用放电量")
    private double flatDischargeQuantityDcdc;

    @ApiModelProperty("尖峰自用放电量")
    private double tipDischargeQuantityDcdc;

    @ApiModelProperty("深谷自用放电量")
    private double deepVallyDischargeQuantityDcdc;

    @ApiModelProperty("峰段收益")
    private double peakDischargeBenefit;

    @ApiModelProperty("谷段收益")
    private double vallyDischargeBenefit;

    @ApiModelProperty("平段收益")
    private double flatDischargeBenefit;

    @ApiModelProperty("尖峰收益")
    private double tipDischargeBenefit;

    @ApiModelProperty("深谷收益")
    private double deepVallyDischargeBenefit;

    @ApiModelProperty("峰价格")
    private double peakPrice;

    @ApiModelProperty("谷价格")
    private double vallyPrice;

    @ApiModelProperty("平价格")
    private double flatPrice;

    @ApiModelProperty("尖价格")
    private double tipPrice;

    @ApiModelProperty("深谷价格")
    private double deepVallyPrice;

    @ApiModelProperty("总自用电电量")
    private double totalDischargeSelfQuantity;

    @ApiModelProperty("全额上网 计算方式的 总收益")
    private double totalFullInternetAccessBenefit;

    @ApiModelProperty("总放电自用收益")
    private double totalDischargeSelfBenefit;

    private double totalOnlineBenefit;
    private double totalOnlineQuantity;

    @ApiModelProperty("总pv电量")
    private double totalDischargeQuantity;

    @ApiModelProperty("协议模式 计算方式的 总收益")
    private double totalAgreementBenefit;

    private String userId;
}
