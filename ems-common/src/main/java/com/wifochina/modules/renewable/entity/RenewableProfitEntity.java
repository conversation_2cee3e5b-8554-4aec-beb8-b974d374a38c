package com.wifochina.modules.renewable.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.remedies.request.DataCalibrationOperatorGet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
@TableName("t_renewable_profit")
@ApiModel(value = "RenewableEntity对象")
public class RenewableProfitEntity
        implements Serializable, RenewablePeriod, DataCalibrationOperatorGet {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电量识符id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("数据校准")
    private boolean dataCalibrationFlag;

    @ApiModelProperty("年度")
    private Integer year;

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("日")
    private Integer day;

    @ApiModelProperty("pv还是wind")
    // TODO change to type
    private String renewableType;

    @ApiModelProperty("峰段放电量")
    private double peakDischargeQuantity;

    @ApiModelProperty("谷段放电量")
    private double vallyDischargeQuantity;

    @ApiModelProperty("平段放电量")
    private double flatDischargeQuantity;

    @ApiModelProperty("尖峰放电量")
    private double tipDischargeQuantity;

    @ApiModelProperty("深谷放电量")
    private double deepVallyDischargeQuantity;

    @ApiModelProperty("峰段自用电量")
    private double peakDischargeQuantityOnline;

    @ApiModelProperty("谷段自用放电量")
    private double vallyDischargeQuantityOnline;

    @ApiModelProperty("平段自用放电量")
    private double flatDischargeQuantityOnline;

    @ApiModelProperty("尖峰自用放电量")
    private double tipDischargeQuantityOnline;

    @ApiModelProperty("深谷自用放电量")
    private double deepVallyDischargeQuantityOnline;

    @ApiModelProperty("峰段自用电量")
    private double peakDischargeQuantityDcdc;

    @ApiModelProperty("谷段自用放电量")
    private double vallyDischargeQuantityDcdc;

    @ApiModelProperty("平段自用放电量")
    private double flatDischargeQuantityDcdc;

    @ApiModelProperty("尖峰自用放电量")
    private double tipDischargeQuantityDcdc;

    @ApiModelProperty("深谷自用放电量")
    private double deepVallyDischargeQuantityDcdc;

    @ApiModelProperty("峰段收益")
    private double peakDischargeBenefit;

    @ApiModelProperty("谷段收益")
    private double vallyDischargeBenefit;

    @ApiModelProperty("平段收益")
    private double flatDischargeBenefit;

    @ApiModelProperty("尖峰收益")
    private double tipDischargeBenefit;

    @ApiModelProperty("深谷收益")
    private double deepVallyDischargeBenefit;

    @ApiModelProperty("峰价格")
    private double peakSellPrice;

    @ApiModelProperty("谷价格")
    private double vallySellPrice;

    @ApiModelProperty("平价格")
    private double flatSellPrice;

    @ApiModelProperty("尖价格")
    private double tipSellPrice;

    @ApiModelProperty("深谷价格")
    private double deepVallySellPrice;

    @ApiModelProperty("总放自用电的电量")
    private double totalDischargeSelfQuantity;

    @ApiModelProperty("全额上网 计算方式的 总收益")
    private double totalFullInternetAccessBenefit;

    /** */
    @ApiModelProperty("协议模式 计算方式的 总收益")
    private double totalAgreementBenefit;

    @ApiModelProperty("总放电自用电的收益")
    private double totalDischargeSelfBenefit;

    @ApiModelProperty("上网的总收益")
    private double totalOnlineBenefit;

    @ApiModelProperty("上网的总电量")
    private double totalOnlineQuantity;

    @ApiModelProperty("总PV电量")
    private double totalDischargeQuantity;

    @ApiModelProperty("数据校准的操作人")
    private String userId;

    @ApiModelProperty("校准操作人userName 数据库不存在这个字段 通过userId查询出来的")
    @TableField(exist = false)
    private String userName;
}
