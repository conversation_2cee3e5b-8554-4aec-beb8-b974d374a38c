package com.wifochina.modules.renewable;

import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.onoff.OnOffComponent;
import com.wifochina.modules.income.caculate.pvwind.RenewableTimeSeriesService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.renewable.context.CalcOnOffFlagContext;
import com.wifochina.modules.renewable.context.CalcRenewableProfitCoreContext;
import com.wifochina.modules.renewable.entity.RenewablePeriod;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;

/**
 * Created on 2025/2/7 11:10.
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public abstract class AbstractRenewableAdapterService<T extends RenewablePeriod>
        implements RenewableAdapterService<T> {

    private final RenewableTimeSeriesService renewableTimeSeriesService;
    private final OnOffComponent onOffComponent;

    protected CalcOnOffFlagContext getCalcOnOffFlagContext(
            ProjectEntity project, String calculateType) {
        boolean dcdcFlag = false;
        boolean renewableFlag =
                onOffComponent.renewableProfitOnOff(
                        project.getId(), CalculateTypeEnum.valueOf(calculateType));
        if (CalculateTypeEnum.PV.name().equals(calculateType)) {
            // 只有 pv 才涉及 dcdc 目前 ,  包括wind 和 余热发电都没有 dcdc
            dcdcFlag =
                    onOffComponent.dcdcAndProfitOnOff(project.getId(), new ArrayList<>()::addAll);
        }
        return new CalcOnOffFlagContext().setRenewableFlag(renewableFlag).setDcdcFlag(dcdcFlag);
    }

    @Override
    public void calcRenewablePeriodProfitCore(
            CalcRenewableProfitCoreContext context,
            ElectricPriceEntity price,
            ElectricPriceEntity superPrice,
            T renewablePeriodEntity) {
        LocalDate time = context.time();
        ProjectEntity project = context.project();
        CalculateTypeEnum calculateTypeEnum = context.calculateType();
        String calculateType = calculateTypeEnum.name();
        Boolean renewableFlag = context.calcOnOffFlagContext().getRenewableFlag();
        Boolean dcdcFlag = context.calcOnOffFlagContext().getDcdcFlag();
        double renewablePower = 0.0;
        double renewableOnLinePower = 0.0;
        double renewableDcdcPower = 0.0;
        if (renewableFlag) {
            // 计算 可再生能源相关的的 发电量
            renewablePower =
                    renewableTimeSeriesService.getPower(calculateType, time, project, price);
            // 计算 可再生能源相关的 上网的电量
            renewableOnLinePower =
                    renewableTimeSeriesService.getPower(
                            CalculateTypeEnum.ONLINE_POWER.name(), time, project, price);
        }
        if (dcdcFlag) {
            // 计算dcdc的 power ,注意目前只有 pv的情况才有这个, 非pv 则dcdcFlag都是false
            renewableDcdcPower =
                    renewableTimeSeriesService.getPower(
                            CalculateTypeEnum.DCDC_POWER.name(), time, project, price);
        }
        RenewablePowerDatas renewablePowerDatas =
                getPowerDatas(renewablePower, renewableOnLinePower, renewableDcdcPower);
        // 根据 3个 power计算出来
        calculatePeriod(
                project,
                superPrice,
                price,
                renewablePeriodEntity,
                calculateType,
                renewablePowerDatas);
    }
}
