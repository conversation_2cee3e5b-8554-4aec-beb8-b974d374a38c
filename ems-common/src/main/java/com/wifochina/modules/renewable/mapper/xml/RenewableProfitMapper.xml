<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.renewable.mapper.RenewableProfitMapper">
    <resultMap id="BaseResultMap" type="com.wifochina.modules.renewable.entity.RenewableProfitEntity">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="time" property="time"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="renewable_type" property="renewableType"/>
        <result property="peakDischargeQuantity" column="peak_discharge_quantity"/>
        <result property="vallyDischargeQuantity" column="vally_discharge_quantity"/>
        <result property="flatDischargeQuantity" column="flat_discharge_quantity"/>
        <result property="tipDischargeQuantity" column="tip_discharge_quantity"/>
        <result property="deepVallyDischargeQuantity" column="deep_vally_discharge_quantity"/>

        <result property="peakDischargeQuantityOnline" column="peak_discharge_quantity_online"/>
        <result property="vallyDischargeQuantityOnline" column="vally_discharge_quantity_online"/>
        <result property="flatDischargeQuantityOnline" column="flat_discharge_quantity_online"/>
        <result property="tipDischargeQuantityOnline" column="tip_discharge_quantity_online"/>
        <result property="deepVallyDischargeQuantityOnline" column="deep_vally_discharge_quantity_online"/>

        <result property="peakDischargeQuantityDcdc" column="peak_discharge_quantity_dcdc"/>
        <result property="vallyDischargeQuantityDcdc" column="vally_discharge_quantity_dcdc"/>
        <result property="flatDischargeQuantityDcdc" column="flat_discharge_quantity_dcdc"/>
        <result property="tipDischargeQuantityDcdc" column="tip_discharge_quantity_dcdc"/>
        <result property="deepVallyDischargeQuantityDcdc" column="deep_vally_discharge_quantity_dcdc"/>

        <result property="peakDischargeBenefit" column="peak_discharge_benefit"/>
        <result property="vallyDischargeBenefit" column="vally_discharge_benefit"/>
        <result property="flatDischargeBenefit" column="flat_discharge_benefit"/>
        <result property="tipDischargeBenefit" column="tip_discharge_benefit"/>
        <result property="deepVallyDischargeBenefit" column="deep_vally_discharge_benefit"/>

        <result property="peakSellPrice" column="peak_sell_price"/>
        <result property="vallySellPrice" column="vally_sell_price"/>
        <result property="flatSellPrice" column="flat_sell_price"/>
        <result property="tipSellPrice" column="tip_sell_price"/>
        <result property="deepVallySellPrice" column="deep_vally_sell_price"/>

        <result property="totalDischargeSelfQuantity" column="total_discharge_self_quantity"/>
        <result property="totalDischargeQuantity" column="total_discharge_quantity"/>
        <!--        全额上网的 计算方式的 收益-->
        <result property="totalFullInternetAccessBenefit" column="total_full_internet_access_benefit"/>
        <!--        自发自用的 计算方式的 收益-->
        <result property="totalDischargeSelfBenefit" column="total_discharge_self_benefit"/>
        <result property="totalOnlineBenefit" column="total_online_benefit"/>
        <result property="totalOnlineQuantity" column="total_online_quantity"/>

        <result property="totalAgreementBenefit" column="total_agreement_benefit"/>

        <result property="userId" column="user_id"/>
    </resultMap>


    <select id="queryRenewableTotalProfit"
            parameterType="com.wifochina.modules.renewable.request.RenewableProfitRequest"
            resultMap="BaseResultMap">
        select sum(total_discharge_quantity) as total_discharge_quantity,
        sum(total_agreement_benefit) as total_agreement_benefit,
        sum(total_discharge_self_quantity) as total_discharge_self_quantity,
        sum(total_discharge_self_benefit) as total_discharge_self_benefit,
        sum(total_full_internet_access_benefit) as total_full_internet_access_benefit,
        sum(total_online_benefit) as total_online_benefit,
        sum(total_online_quantity) as total_online_quantity,
        SUM(peak_discharge_quantity) AS peak_discharge_quantity,
        SUM(vally_discharge_quantity) AS vally_discharge_quantity,
        SUM(flat_discharge_quantity) AS flat_discharge_quantity,
        SUM(tip_discharge_quantity) AS tip_discharge_quantity,
        SUM(deep_vally_discharge_quantity) AS deep_vally_discharge_quantity,

        SUM(peak_discharge_quantity_online) AS peak_discharge_quantity_online,
        SUM(vally_discharge_quantity_online) AS vally_discharge_quantity_online,
        SUM(flat_discharge_quantity_online) AS flat_discharge_quantity_online,
        SUM(tip_discharge_quantity_online) AS tip_discharge_quantity_online,
        SUM(deep_vally_discharge_quantity_online) AS deep_vally_discharge_quantity_online,

        SUM(peak_discharge_quantity_dcdc) AS peak_discharge_quantity_dcdc,
        SUM(vally_discharge_quantity_dcdc) AS vally_discharge_quantity_dcdc,
        SUM(flat_discharge_quantity_dcdc) AS flat_discharge_quantity_dcdc,
        SUM(tip_discharge_quantity_dcdc) AS tip_discharge_quantity_dcdc,
        SUM(deep_vally_discharge_quantity_dcdc) AS deep_vally_discharge_quantity_dcdc,

        SUM(peak_discharge_benefit) AS peak_discharge_benefit,
        SUM(vally_discharge_benefit) AS vally_discharge_benefit,
        SUM(flat_discharge_benefit) AS flat_discharge_benefit,
        SUM(tip_discharge_benefit) AS tip_discharge_benefit,
        SUM(deep_vally_discharge_benefit) AS deep_vally_discharge_benefit
        from t_renewable_profit
        where <![CDATA[ time >= #{start}
          and time
            < #{end} ]]>
        and project_id = #{projectId}
        and renewable_type = #{type}
        <if test="dataCalibrationTimes != null and !dataCalibrationTimes.isEmpty()">
            AND time NOT IN
            <foreach item="dataCalibrationTime" collection="dataCalibrationTimes" open="(" separator="," close=")">
                #{dataCalibrationTime}
            </foreach>
        </if>
    </select>


</mapper>
