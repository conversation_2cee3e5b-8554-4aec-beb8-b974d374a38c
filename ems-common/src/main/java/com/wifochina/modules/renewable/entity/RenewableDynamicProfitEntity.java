package com.wifochina.modules.renewable.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Created on 2024-05-31 15:04:07
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("t_renewable_dynamic_profit")
@ApiModel(value = "RenewableDynamicProfitEntity 对象")
public class RenewableDynamicProfitEntity implements Serializable, RenewablePeriod {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电量识符id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("数据段开始时间")
    private Long periodStartTime;

    @ApiModelProperty("数据段结束时间")
    private Long periodEndTime;

    @ApiModelProperty("年度")
    private Integer year;

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("日")
    private Integer day;

    @ApiModelProperty("pv还是wind/waster")
    private String renewableType;

    @ApiModelProperty("放电量(自用电)")
    private double dischargeQuantity;

    @ApiModelProperty("上网电量")
    private double dischargeQuantityOnline;

    @ApiModelProperty("dcdc电量")
    private double dischargeQuantityDcdc;

    //    @ApiModelProperty("售卖电价(时段的时候使用)")
    //    private double sellPrice;
    //
    //    @ApiModelProperty("买入电价(时段的时候使用)kk")
    //    private double buyPrice;
    //
    //    @ApiModelProperty("自用电价格")
    //    private double selfPrice;
    //
    //    @ApiModelProperty("国家补贴价格")
    //    private double subsidyPrice;
    //
    //    @ApiModelProperty("脱硫标杆价格")
    //    private double dfPrice;
    //
    //    @ApiModelProperty("全额上网电价")
    //    private double fullInternetPrice;

    @ApiModelProperty("时段 放电收益")
    private double dischargeBenefit;

    @ApiModelProperty("全额上网 计算方式的 总收益")
    private double totalFullInternetAccessBenefit;

    @ApiModelProperty("协议模式 计算方式的 总收益")
    private double agreementBenefit;

    @ApiModelProperty("上网的总收益")
    private double totalOnlineBenefit;

    @ApiModelProperty("总的pv电量")
    private double totalDischargeQuantity;
}
