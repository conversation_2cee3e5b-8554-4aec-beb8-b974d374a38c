<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.renewable.mapper.RenewableDynamicProfitMapper">
    <resultMap id="BaseResultMap" type="com.wifochina.modules.renewable.entity.RenewableDynamicProfitEntity">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="period_start_time" property="periodStartTime"/>
        <result column="period_end_time" property="periodEndTime"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="renewable_type" property="renewableType"/>

        <!--        电量相关-->
        <result property="dischargeQuantity" column="discharge_quantity"/>
        <result property="dischargeQuantityOnline" column="discharge_quantity_online"/>
        <result property="dischargeQuantityDcdc" column="discharge_quantity_dcdc"/>
        <result property="totalDischargeQuantity" column="total_discharge_quantity"/>


        <!--        收益相关-->
        <result property="dischargeBenefit" column="discharge_benefit"/>
        <result property="totalFullInternetAccessBenefit" column="total_full_internet_access_benefit"/>
        <result property="agreementBenefit" column="agreement_benefit"/>
        <result property="totalOnlineBenefit" column="total_online_benefit"/>
    </resultMap>

    <select id="queryRenewableDynamicTotalProfit"
            parameterType="com.wifochina.modules.renewable.request.RenewableProfitRequest"
            resultMap="BaseResultMap">
        select sum(discharge_quantity)                 as discharge_quantity,
               sum(discharge_quantity_online)          as discharge_quantity_online,
               sum(discharge_quantity_dcdc)            as discahrge_quantity_dcdc,

               sum(discharge_benefit)                  as discharge_benefit,
               sum(total_full_internet_access_benefit) as total_full_internet_access_benefit,
               sum(agreement_benefit)                  as agreement_benefit,
               SUM(total_online_benefit)               as total_online_benefit,
               sum(total_discharge_quantity)           as total_discharge_quantity
        from t_renewable_dynamic_profit
        where <![CDATA[ period_start_time >= #{start}
          and period_start_time
            < #{end} ]]>
        and project_id = #{projectId}
          and renewable_type = #{type}
    </select>
</mapper>
