package com.wifochina.modules.renewable;

import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.renewable.entity.RenewablePeriod;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created on 2024/5/23 19:46.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class RenewableAdapterChooser {
    private final List<RenewableAdapterService<? extends RenewablePeriod>> renewableAdapterServices;

    /**
     * @param type
     * @return
     */
    public RenewableAdapterService<? extends RenewablePeriod> choose(String type) {
        return renewableAdapterServices.stream()
                .filter(pvWindAdapterService -> pvWindAdapterService.type().contains(type))
                .findFirst()
                .orElseThrow(() -> new ServiceException(""));
    }
}
