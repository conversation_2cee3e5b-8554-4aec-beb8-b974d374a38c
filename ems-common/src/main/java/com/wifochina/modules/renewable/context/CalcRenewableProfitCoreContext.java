package com.wifochina.modules.renewable.context;

import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.modules.project.entity.ProjectEntity;

import java.time.LocalDate;

/**
 * Created on 2025/2/7 12:01.
 *
 * <AUTHOR>
 */
public interface CalcRenewableProfitCoreContext {

    LocalDate time();

    ProjectEntity project();

    CalculateTypeEnum calculateType();

    CalcOnOffFlagContext calcOnOffFlagContext();
}
