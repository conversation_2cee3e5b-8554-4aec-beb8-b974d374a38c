package com.wifochina.modules.capacity.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.capacity.request.CapacityAlarmRecordPageRequest;
import com.wifochina.modules.capacity.service.CapacityAlarmRecordService;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import com.wifochina.modules.demand.request.DemandLogPageRequest;
import com.wifochina.modules.demand.service.DemandLogService;
import com.wifochina.modules.oauth.util.WebUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2024-01-12
 */
@Slf4j
@RestController
@RequestMapping("/capacity/alarm")
@Api(tags = "30-容量告警记录")
public class CapacityAlarmRecordController {
    @Resource private CapacityAlarmRecordService capacityAlarmRecordService;

    @PostMapping("/query")
    @ApiOperation("查询容量告警记录")
    @PreAuthorize("hasAuthority('/capacity/alarm/query')")
//    @PreAuthorize("hasAuthority('/capacity/alarm/query')")
    public Result<IPage<CapacityAlarmRecordEntity>> query(
            @RequestBody CapacityAlarmRecordPageRequest capacityAlarmRecordPageRequest) {
        return Result.success(
                capacityAlarmRecordService.pageQuery(
                        WebUtils.projectId.get(), capacityAlarmRecordPageRequest));
    }
}
