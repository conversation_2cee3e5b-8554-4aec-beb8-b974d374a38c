package com.wifochina.modules.capacity.service.adjustmodel;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.demand.service.adjustmodel.DemandControlAdjustModelService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * Created on 2024/9/27 14:20.
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CapacityControlAdjustModelChooser {
    @Resource private List<CapacityControlAdjustModelService> capacityControlAdjustModelServices;

    public CapacityControlAdjustModelService chooseAdjustModel(String type) {
        CapacityControlAdjustModelService capacityControlAdjustModelService =
                capacityControlAdjustModelServices.stream()
                        .filter((service) -> service.type().contains(type))
                        .findFirst()
                        .orElse(null);
        if (capacityControlAdjustModelService != null) {
            return capacityControlAdjustModelService;
        } else {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }
}
