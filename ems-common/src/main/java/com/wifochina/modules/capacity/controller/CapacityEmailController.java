package com.wifochina.modules.capacity.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.capacity.entity.CapacityEmailEntity;
import com.wifochina.modules.capacity.request.CapacityEmailPageRequest;
import com.wifochina.modules.capacity.request.CapacityEmailRequest;
import com.wifochina.modules.capacity.service.CapacityEmailService;
import com.wifochina.modules.demand.entity.DemandEmailEntity;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.CapacityEmailLogDetailService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2025-07-10 15:29:30
 */
@Slf4j
@RestController
@Api(tags = "28-容量通知邮箱设置")
@RequestMapping("/capacity")
public class CapacityEmailController {

    @Resource private CapacityEmailService capacityEmailService;

    @Resource private UserService userService;

    @PostMapping("/addEmail")
    @ApiOperation("增加通知")
    @Log(module = "Capacity_Email", type = OperationType.ADD)
    @PreAuthorize("hasAuthority('/capacity/email/add')")
    public Result<Object> addEmail(@RequestBody CapacityEmailRequest capacityEmailRequest) {
        com.wifochina.modules.capacity.entity.CapacityEmailEntity capacityEmailEntity =
                new com.wifochina.modules.capacity.entity.CapacityEmailEntity();
        BeanUtils.copyProperties(capacityEmailRequest, capacityEmailEntity);
        capacityEmailEntity.setId(StringUtil.uuid());
        capacityEmailEntity.setProjectId(WebUtils.projectId.get());
        capacityEmailService.save(capacityEmailEntity);
        return Result.success();
    }

    /** 修改电表 */
    @PostMapping("/updateEmail")
    @ApiOperation("修改通知")
    @Log(module = "Capacity_Email", type = OperationType.UPDATE)
    @PreAuthorize("hasAuthority('/capacity/email/edit')")
    public Result<String> updateEmail(@RequestBody CapacityEmailRequest capacityEmailRequest) {
        com.wifochina.modules.capacity.entity.CapacityEmailEntity capacityEmailEntity =
                new com.wifochina.modules.capacity.entity.CapacityEmailEntity();
        BeanUtils.copyProperties(capacityEmailRequest, capacityEmailEntity);
        capacityEmailService.updateById(capacityEmailEntity);
        return Result.success();
    }

    /** 删除电表 */
    @PostMapping("/deleteEmail/{id}")
    @ApiOperation("删除通知")
    @Log(
            module = "Capacity_Email",
            type = OperationType.DEL,
            logDetailServiceClass = CapacityEmailLogDetailService.class)
    @PreAuthorize("hasAuthority('/capacity/email/delete')")
    public Result<String> deleteEmail(@PathVariable("id") String id) {
        capacityEmailService.removeById(id);
        return Result.success();
    }

    /** 查询电表 */
    @PostMapping("/queryEmail")
    @ApiOperation("查询通知")
    @PreAuthorize("hasAuthority('/capacity/email/query')")
    public Result<IPage<CapacityEmailEntity>> queryEmail(
            @RequestBody CapacityEmailPageRequest capacityEmailPageRequest) {
        Page<CapacityEmailEntity> page =
                Page.of(
                        capacityEmailPageRequest.getPageNum(),
                        capacityEmailPageRequest.getPageSize());
        boolean containAdEmail = EmsUtil.isContainAd(SecurityUtil.getUserId(), userService);
        // 用户角色类型 client项目普通ad域控admin项目管理super超级
        IPage<CapacityEmailEntity> list =
                capacityEmailService.page(
                        page,
                        Wrappers.lambdaQuery(CapacityEmailEntity.class)
                                .eq(CapacityEmailEntity::getProjectId, WebUtils.projectId.get())
                                .like(
                                        StringUtil.notEmpty(capacityEmailPageRequest.getEmail()),
                                        CapacityEmailEntity::getEmail,
                                        capacityEmailPageRequest.getEmail())
                                .notLike(
                                        !containAdEmail,
                                        CapacityEmailEntity::getEmail,
                                        EmsConstants.AD_EMAIL_SUFFIXES)
                                .orderByAsc(CapacityEmailEntity::getCreateTime));
        return Result.success(list);
    }
}
