package com.wifochina.modules.capacity.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-03-18 16:08:58
 */
@TableName("t_capacity_alarm_record")
@ApiModel(value = "容量告警记录对象")
@Accessors(chain = true)
@Data
public class CapacityAlarmRecordEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("分组id")
    private String groupId;

    // 只是为了给前端
    @ApiModelProperty("分组name")
    @TableField(exist = false)
    private String groupName;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("1容量超了")
    private Integer type;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("英文描述")
    private String descriptionEn;

    @ApiModelProperty("发生时间")
    private Long time;

    @ApiModelProperty("关口视在功率")
    private Double alarmValue;

    @ApiModelProperty("容量控制值")
    private Double controlValue;
}
