package com.wifochina.modules.capacity.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.CapacityAlarmTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.capacity.mapper.CapacityAlarmRecordMapper;
import com.wifochina.modules.capacity.request.CapacityAlarmRecordPageRequest;
import com.wifochina.modules.capacity.service.CapacityAlarmRecordService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-05 11:32 AM
 */
@Component
public class CapacityAlarmRecordServiceImpl
        extends ServiceImpl<CapacityAlarmRecordMapper, CapacityAlarmRecordEntity>
        implements CapacityAlarmRecordService {

    @Autowired private CapacityAlarmRecordMapper capacityAlarmRecordMapper;

    /**
     * 查询 rangeRequest范围时间内的 发生的 列表
     *
     * @param projectId : projectId
     * @param groupId : groupId
     * @param rangeRequest : rangeRequest
     * @return : 告警的记录
     */
    @Override
    public List<CapacityAlarmRecordEntity> getCapacityAlarmUpRecords(
            String projectId, String groupId, RangeRequest rangeRequest) {
        return this.lambdaQuery()
                .eq(CapacityAlarmRecordEntity::getProjectId, projectId)
                .eq(CapacityAlarmRecordEntity::getGroupId, groupId)
                .eq(CapacityAlarmRecordEntity::getType, CapacityAlarmTypeEnum.HAPPEN.type())
                .between(
                        CapacityAlarmRecordEntity::getTime,
                        rangeRequest.getStartDate(),
                        rangeRequest.getEndDate())
                .list();
    }

    @Override
    public IPage<CapacityAlarmRecordEntity> pageQuery(
            String projectId, CapacityAlarmRecordPageRequest capacityAlarmRecordPageRequest) {
        Page<CapacityAlarmRecordEntity> page =
                Page.of(
                        capacityAlarmRecordPageRequest.getPageNum(),
                        capacityAlarmRecordPageRequest.getPageSize());
        return this.page(
                page,
                Wrappers.lambdaQuery(CapacityAlarmRecordEntity.class)
                        .eq(CapacityAlarmRecordEntity::getProjectId, projectId)
                        .eq(
                                capacityAlarmRecordPageRequest.getType() != null,
                                CapacityAlarmRecordEntity::getType,
                                capacityAlarmRecordPageRequest.getType())
                        .eq(
                                !StringUtil.isEmpty(capacityAlarmRecordPageRequest.getGroupId()),
                                CapacityAlarmRecordEntity::getGroupId,
                                capacityAlarmRecordPageRequest.getGroupId())
                        .ge(
                                CapacityAlarmRecordEntity::getTime,
                                capacityAlarmRecordPageRequest.getStartDate())
                        .le(
                                CapacityAlarmRecordEntity::getTime,
                                capacityAlarmRecordPageRequest.getEndDate())
                        .orderByDesc(CapacityAlarmRecordEntity::getCreateTime));
    }

    /**
     * 查询 这个项目id下面的 不同分组的 每个最新的 一条记录 且不是log日志type的 的 发生的告警类型 就是每个分组 最新一条记录要是发生的 才会查询出来
     *
     * @param projectId : projectId
     * @param groupIds: 哪些开了 容量告警的分组
     * @return : List<CapacityAlarmRecordEntity>
     */
    @Override
    public List<CapacityAlarmRecordEntity> getLatestRecordsUpByGroups(
            String projectId, List<String> groupIds) {
        if (CollectionUtil.isNotEmpty(groupIds)) {
            return capacityAlarmRecordMapper.getLatestRecordsUpByGroups(projectId, groupIds);
        }
        return List.of();
    }

    @Override
    public CapacityAlarmRecordEntity getLatestRecord(String projectId, String groupId) {
        if (!StringUtil.isEmpty(projectId) && !StringUtil.isEmpty(groupId)) {
            return capacityAlarmRecordMapper.selectOne(
                    new LambdaQueryWrapper<CapacityAlarmRecordEntity>()
                            .eq(CapacityAlarmRecordEntity::getProjectId, projectId)
                            .eq(CapacityAlarmRecordEntity::getGroupId, groupId)
                            .ne(
                                    CapacityAlarmRecordEntity::getType,
                                    CapacityAlarmTypeEnum.LOG_RECORD.type())
                            .orderByDesc(CapacityAlarmRecordEntity::getCreateTime)
                            .last("LIMIT 1"));
        }
        return null;
    }
}
