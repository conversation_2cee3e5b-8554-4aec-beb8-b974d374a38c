<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.capacity.mapper.CapacityAlarmRecordMapper">



    <select id="getLatestRecordsUpByGroups" resultType="com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity">
        SELECT r.*
        FROM t_capacity_alarm_record r
        JOIN (
        SELECT group_id, MAX(create_time) AS max_time
        FROM t_capacity_alarm_record
        WHERE project_id = #{projectId}
        AND group_id IN
        <foreach item="groupId" collection="groupEnableCapacityIds" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        GROUP BY group_id
        ) latest ON r.group_id = latest.group_id AND r.create_time = latest.max_time
        WHERE r.project_id = #{projectId}
        AND r.type = 1;
    </select>


</mapper>
