package com.wifochina.modules.capacity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.capacity.request.CapacityAlarmRecordPageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-05 11:31 AM
 */
public interface CapacityAlarmRecordService extends IService<CapacityAlarmRecordEntity> {

    List<CapacityAlarmRecordEntity> getCapacityAlarmUpRecords(
            String projectId, String groupId, RangeRequest rangeRequest);

    IPage<CapacityAlarmRecordEntity> pageQuery(
            String projectId, CapacityAlarmRecordPageRequest capacityAlarmRecordPageRequest);

    List<CapacityAlarmRecordEntity> getLatestRecordsUpByGroups(
            String projectId, List<String> groupIds);

    CapacityAlarmRecordEntity getLatestRecord(String projectId, String groupId);
}
