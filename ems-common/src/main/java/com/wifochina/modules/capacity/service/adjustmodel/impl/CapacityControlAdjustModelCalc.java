package com.wifochina.modules.capacity.service.adjustmodel.impl;

import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.CapacityControlAdjustModelEnum;
import com.wifochina.common.util.DemandControlAdjustModelEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.capacity.service.adjustmodel.CommonCapacityControlAdjustModelService;
import com.wifochina.modules.capacity.vo.CapacityRoundData;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.demand.service.NewDemandServiceKt;
import com.wifochina.modules.demand.service.adjustmodel.CommonDemandControlAdjustModelService;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.NewDiagramService;
import com.wifochina.modules.diagram.utils.DiagramCalUtils;
import com.wifochina.modules.group.entity.GroupEntity;

import com.wifochina.modules.oauth.util.WebUtils;
import lombok.extern.slf4j.Slf4j;

import org.mapstruct.ap.internal.util.RoundContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * Created on 2024/9/24 16:32. <br>
 * 计算模式 支持 manual 和 auto_calc 都需要执行这个
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CapacityControlAdjustModelCalc extends CommonCapacityControlAdjustModelService {

    @Autowired private NewDiagramService newDiagramService;

    /**
     * 平台计算的 这一轮最大容量视在功率 *
     *
     * @param roundContext : context
     * @return : OaDemandData
     */
    @Override
    protected CapacityRoundData roundMaxDemand(RoundContext roundContext) {
        // 获取平台的计算的 需量数据 + 保存 influxdb
        //        Map<Long, OaDemandData> calcDemandMap =
        //                calcDemandMapAndSaveDb(
        //                        roundContext.getGroup(),
        //                        roundContext.getProject(),
        //                        roundContext.getControlPower());
        // 得到 每分钟的 视在功率 可能是 10分钟的数据,
        // 00:11 执行 去获取 0-10分钟的数据
        // 00:13 执行 去获取 2-12分钟的数据

        // 10分钟前的时间戳
        // 当前的时间戳
        long currentMinuteTimestamp =
                Instant.now().truncatedTo(ChronoUnit.MINUTES).getEpochSecond();
        long tenMinutesAgoTimestamp = currentMinuteTimestamp - 900;
        // 得到 start 时间
        RequestWithGroupId requestWithGroupId = new RequestWithGroupId();
        requestWithGroupId.setStartDate(tenMinutesAgoTimestamp);
        requestWithGroupId.setEndDate(currentMinuteTimestamp);
        requestWithGroupId.setItemId(EmsConstants.ALL);
        requestWithGroupId.setPeriod(1L);
        requestWithGroupId.setProjectId(roundContext.getProject().getId());
        requestWithGroupId.setGroupId(roundContext.getGroup().getId());
        // 有功的
        CompletableFuture<List<ValueVO>> realDemandAcActiveFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(roundContext.getProject().getId());
                            return newDiagramService.getMeterRate(
                                    requestWithGroupId,
                                    MeterFieldEnum.AC_ACTIVE_POWER.field(),
                                    MeterTypeEnum.GRID.meterType().toString());
                        });
        // 无功
        CompletableFuture<List<ValueVO>> realDemandReActiveFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(requestWithGroupId.getProjectId());
                            return newDiagramService.getMeterRate(
                                    requestWithGroupId,
                                    MeterFieldEnum.AC_REACTIVE_POWER.field(),
                                    MeterTypeEnum.GRID.meterType().toString());
                        });
        // 得到视在
        CompletableFuture<Void> allOf =
                CompletableFuture.allOf(realDemandAcActiveFuture, realDemandReActiveFuture);
        allOf.join();
        List<ValueVO> realDemandAcActivePowerList;
        List<ValueVO> realDemandReacActivePowerList;
        try {
            realDemandAcActivePowerList = realDemandAcActiveFuture.get();
            realDemandReacActivePowerList = realDemandReActiveFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }

        List<ValueVO> realDemandList =
                DiagramCalUtils.getApparentPowerRate(
                        realDemandAcActivePowerList, realDemandReacActivePowerList);

        // 只取最新的1条数据, 每条是每分钟
        realDemandList =
                realDemandList.stream().max(Comparator.comparing(ValueVO::getTime)).stream()
                        .limit(1)
                        .collect(Collectors.toList());

        // 拿这个去比较 容量控制
        // 对这个视在功率列表获取最大的 一个 然后拿出来进行操作
        Map<Long, CapacityRoundData> calcCapacityMap =
                realDemandList.stream()
                        .collect(
                                Collectors.toMap(
                                        ValueVO::getTime,
                                        v ->
                                                new CapacityRoundData()
                                                        .setTime(v.getTime())
                                                        .setGirdApparent(v.getValue())));

        if (!calcCapacityMap.isEmpty()) {
            // 这里逻辑稍微改了一下 只取这一个周期里最大的那个需量
            Map.Entry<Long, CapacityRoundData> maxOaCapacityRoundEntry =
                    calcCapacityMap.entrySet().stream()
                            .max(Comparator.comparing(entry -> entry.getValue().getGirdApparent()))
                            .orElseThrow();
            Long time = maxOaCapacityRoundEntry.getKey();
            CapacityRoundData capacityRoundData = maxOaCapacityRoundEntry.getValue();
            capacityRoundData.setTime(time);
            return capacityRoundData;
        }
        return null;
    }

    @Override
    public Set<String> type() {
        // 目前只有  manual 都执行这个
        return Set.of(CapacityControlAdjustModelEnum.manual.getName());
    }
}
