package com.wifochina.modules.capacity.service.adjustmodel;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.constants.CapacityAlarmTypeEnum;
import com.wifochina.common.constants.EmailLogTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.DemandControlAdjustModelEnum;
import com.wifochina.common.util.EmailService;
import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.capacity.service.CapacityAlarmRecordService;
import com.wifochina.modules.capacity.service.CapacityEmailService;
import com.wifochina.modules.capacity.vo.CapacityRoundData;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import com.wifochina.modules.demand.service.*;
import com.wifochina.modules.demand.service.adjustmodel.CommonDemandControlAdjustModelService;
import com.wifochina.modules.demand.service.adjustmodel.DemandAutoUpRetryTaskExecutor;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

/**
 * Created on 2024/9/25 14:25. 通用的 需量计算逻辑 包含 大体的流程
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RefreshScope // 让 Bean 具备动态刷新能力
public abstract class CommonCapacityControlAdjustModelService
        implements CapacityControlAdjustModelService {

    @Autowired private CapacityAlarmDebugConfigProperties debugConfigProperties;

    @Resource private StrategyService strategyService;
    @Resource private DemandLogService demandLogService;

    @Resource private EmailService emailService;

    @Resource private NewDemandServiceKt newDemandServiceKt;
    @Resource private InfluxClientService influxClientService;

    @Resource private CapacityAlarmRecordService capacityAlarmRecordService;

    //    @Resource private DemandEmailService demandEmailService;
    @Resource private CapacityEmailService capacityEmailService;

    protected abstract CapacityRoundData roundMaxDemand(RoundContext roundContext);

    /**
     * 数据时间 的 前5分钟时间
     *
     * @param roundContext
     * @return
     */
    private CompareResult compareExceeded(RoundContext roundContext) {
        ProjectEntity project = roundContext.project;
        GroupEntity group = roundContext.group;
        Double controlPower = roundContext.controlPower;
        CapacityRoundData capacityRoundData = roundContext.capacityRoundData;
        if (capacityRoundData == null) {
            return new CompareResult().setNeedRecord(false);
        }
        long newTime = capacityRoundData.getTime();
        long fiveMinutesAgo = newTime - 300;
        log.info(
                "CapacityCalcPlatformJob projectId:{} , groupId:{} , capacityRoundData:{} fiveMinutesAgo:{}",
                project.getId(),
                group.getId(),
                capacityRoundData,
                fiveMinutesAgo);
        // 1. 得到当前的这轮的数据 并且先比较 如果 < 小于  则要看 数据库里的 前5分钟
        // 1.从数据库里查询出 这个分组的当前时间的 最新的数据 这个数据如果 和 当前时间 > 5分钟 并且存在 那么就表示前5分钟有 后5分钟也有 不记录重复
        // 2.从数据库里查询出 这个分组的当前时间的 最新的数据 这个数据如果 和 当前时间 > 10分钟有 并且当前时间
        // 查询出前10分钟数据 基于最新的这个的前10分钟
        //        List<CapacityAlarmRecordEntity> capacityAlarmRecords =
        //                capacityAlarmRecordService.getCapacityAlarmUpRecords(
        //                        project.getId(),
        //                        group.getId(),
        //                        // 查询10分钟前的 按照 newTime去查询 它10分钟前的数据
        //                        new RangeRequest().setStartDate(newTime -
        // 600).setEndDate(newTime));
        CapacityAlarmRecordEntity latestRecord =
                capacityAlarmRecordService.getLatestRecord(project.getId(), group.getId());
        CapacityAlarmTypeEnum upCapacityAlarmType = null;
        if (latestRecord != null) {
            Integer type = latestRecord.getType();
            assert type != null;
            upCapacityAlarmType = CapacityAlarmTypeEnum.fromType(type);
            log.info("upCapacityAlarm type:{} , record: {}", upCapacityAlarmType, latestRecord);
            // 如果最后一条时间 和 这个轮的最大时间比都比这个大 那么可能存在 influxdb 节点数据不均衡问题导致的 这里避免一下
            if (latestRecord.getTime() > capacityRoundData.getTime()) {
                //
                log.warn(
                        "CapacityCalcPlatformJob projectId:{} , groupId:{} maybe数据不均衡? 容量告警db最新数据time:{} > 当前round time:{} ",
                        project.getId(),
                        group.getId(),
                        latestRecord.getTime(),
                        capacityRoundData.getTime());
                return new CompareResult()
                        .setNeedRecord(false)
                        .setCapacityAlarmTypeEnum(null)
                        .setCapacityAlarmValue(capacityRoundData.getGirdApparent())
                        .setTime(capacityRoundData.getTime());
            }
        }

        // 前5分钟数据 (10分钟前 ~ 5分钟前)
        //        List<CapacityAlarmRecordEntity> firstHalfRecords = new ArrayList<>();
        //        // 后5分钟数据 (5分钟前 ~ 现在)
        //        List<CapacityAlarmRecordEntity> secondHalfRecords = new ArrayList<>();
        //        for (CapacityAlarmRecordEntity record : capacityAlarmRecords) {
        //            if (record.getTime() < fiveMinutesAgo) {
        //                firstHalfRecords.add(record);
        //            } else {
        //                secondHalfRecords.add(record);
        //            }
        //        }
        //
        //        log.info(
        //                "CapacityCalcPlatformJob projectId:{} , groupId:{} , firstHalfRecords:{}
        // secondHalfRecords:{}",
        //                project.getId(),
        //                group.getId(),
        //                firstHalfRecords,
        //                secondHalfRecords);
        // 处理一下精度
        // TODO remove template
        //        if (debugConfigProperties.getCapacityAlarmValue() != null) {
        //            log.info(
        //                    "CapacityCalcPlatformJob projectId:{} , groupId:{} , debug
        // capacityAlarmValue:{}",
        //                    project.getId(),
        //                    group.getId(),
        //                    debugConfigProperties.getCapacityAlarmValue());
        //
        // capacityRoundData.setGirdApparent(debugConfigProperties.getCapacityAlarmValue());
        //        }
        capacityRoundData.setGirdApparent(
                BigDecimal.valueOf(capacityRoundData.getGirdApparent())
                        .setScale(2, RoundingMode.HALF_UP)
                        .doubleValue());
        // 前提是超了
        boolean needRecord = false;
        CapacityAlarmTypeEnum alarmTypeEnum = null;
        if (capacityRoundData.getGirdApparent()
                > controlPower * (group.getCapacityAlarmThreshold() / 100)) {
            needRecord = true;
            alarmTypeEnum = CapacityAlarmTypeEnum.HAPPEN;

            // 下面这个段代码 是按照周涛以前说的什么10分钟5分钟的乱七八糟的一堆 看不懂的需求, 后来他说要实时性 所以这里直接改成了1分钟执行 只要是超了就插入数据库
            //                        // 如果前5分钟 存在 后5分钟也存在 则不重复记录
            //                        if (!firstHalfRecords.isEmpty() &&
            // !secondHalfRecords.isEmpty()) {
            //                            // 不重复记录
            //                            log.info(
            //                                    "CapacityCalcPlatformJob projectId:{} , groupId:{}
            // ,
            //             不重复记录容量告警发生记录 前5分钟和后5分钟都已经存在告警记录 firstHalfRecords:{},
            // secondHalfRecords:{}",
            //                                    project.getId(),
            //                                    group.getId(),
            //                                    firstHalfRecords,
            //                                    secondHalfRecords);
            //                        } else if (secondHalfRecords.isEmpty()) {
            //                            needRecord = true;
            //                            // 后5分钟都没有 记录一下 当前超的值
            //                            log.info(
            //                                    "CapacityCalcPlatformJob projectId:{} , groupId:{}
            // , 记录容量告警发生
            //             后5分钟不存在告警记录 secondHalfRecords:{}",
            //                                    project.getId(),
            //                                    group.getId(),
            //                                    secondHalfRecords);
            //                            alarmTypeEnum = CapacityAlarmTypeEnum.HAPPEN;
            //                        }
        } else {
            // 上一个是发生 这个最新的数据 已经不超了 就记录消失
            if (upCapacityAlarmType != null
                    && upCapacityAlarmType.name().equals(CapacityAlarmTypeEnum.HAPPEN.name())) {
                // 如果前5分钟有 后5分钟没有
                // 记录一下消失
                log.info(
                        "CapacityCalcPlatformJob  projectId:{} , groupId:{} , 记录容量告警消失  up alarm is HAPPEN",
                        project.getId(),
                        group.getId());
                needRecord = true;
                alarmTypeEnum = CapacityAlarmTypeEnum.DISAPPEAR;
            }
        }
        return new CompareResult()
                .setNeedRecord(needRecord)
                .setCapacityAlarmTypeEnum(alarmTypeEnum)
                .setCapacityAlarmValue(capacityRoundData.getGirdApparent())
                .setTime(capacityRoundData.getTime());
    }

    /**
     * 需量自适应执行
     *
     * @param holder :
     */
    @Override
    public void adjustExecute(HandlerAdjustModelHolder holder) {
        GroupEntity group = holder.group();
        ProjectEntity project = holder.project();
        StrategyEntity strategyEntity = strategyService.getOuterStrategyByGroupId(group.getId());
        // 控制 容量值
        Double controlPower = strategyEntity.getControlPower();
        // 如果没有设置  直接返回
        if (controlPower == null) {
            log.warn(
                    "项目Id: {} 项目名称: {} 分组:Id: {} 容量控制没有设置 无法进行容量计算",
                    project.getId(),
                    project.getProjectName(),
                    group.getId());
            return;
        }
        RoundContext roundContext =
                new RoundContext()
                        .setProject(project)
                        .setGroup(group)
                        .setControlPower(controlPower);
        // 获取平台的计算的 最大需量 或者 电表的最大需量
        CapacityRoundData capacityMaxRoundData = roundMaxDemand(roundContext);
        if (capacityMaxRoundData == null) {
            log.warn(
                    "项目Id:{} 项目名称:{} 分组Id:{} 平台计算数据为空 无法进行容量计算",
                    project.getId(),
                    project.getProjectName(),
                    group.getId());
            return;
        }
        roundContext.setCapacityRoundData(capacityMaxRoundData);
        // core 调用子类的 compare 比较方法 不同的方式 比较方法不同, 但是都需要先得到计算的然后保存
        CompareResult compareResult = compareExceeded(roundContext);
        boolean needRecord = compareResult.isNeedRecord();
        if (needRecord) {
            if (Boolean.TRUE.equals(group.getCapacityRemindController())) {
                remindAlarmRecordToDb(project, group, controlPower, compareResult);

            } else {
                log.warn(
                        "CapacityCalcPlatformJob projectId:{} , groupId:{} , 分组未开容量告警提醒 capacityRemindController:{}",
                        project.getId(),
                        group.getId(),
                        group.getCapacityRemindController());
            }
        }
    }

    /**
     * 记录 容量告警or消失的日志
     *
     * @param project : project
     * @param group : group
     * @param controlPower: controlPower
     * @param compareResult : compareResult
     */
    private void remindAlarmRecordToDb(
            ProjectEntity project,
            GroupEntity group,
            Double controlPower,
            CompareResult compareResult) {
        CapacityAlarmRecordEntity capacityAlarmRecordEntity =
                new CapacityAlarmRecordEntity()
                        .setProjectId(project.getId())
                        .setGroupId(group.getId())
                        .setTime(compareResult.getTime())
                        .setControlValue(controlPower)
                        .setAlarmValue(compareResult.getCapacityAlarmValue())
                        .setType(compareResult.capacityAlarmTypeEnum.type());
        if (compareResult
                .capacityAlarmTypeEnum
                .type()
                .equals(CapacityAlarmTypeEnum.HAPPEN.type())) {
            capacityAlarmRecordEntity
                    .setDescription(
                            String.format(
                                    "%s 并网点实际功率(%skVA) 超出当前并网点控制目标(%skVA),阈值为(%s%s)",
                                    group.getName(),
                                    compareResult.getCapacityAlarmValue(),
                                    controlPower,
                                    group.getCapacityAlarmThreshold(),
                                    "%"))
                    .setDescriptionEn(
                            String.format(
                                    "%s The actual grid connection power (%skVA) exceeds the current "
                                            + "grid connection control target (%skVA), with a threshold of (%s%s).",
                                    group.getName(),
                                    compareResult.getCapacityAlarmValue(),
                                    controlPower,
                                    group.getCapacityAlarmThreshold(),
                                    "%"));
            CapacityAlarmRecordEntity oldSameTimeRecord =
                    capacityAlarmRecordService.getOne(
                            new LambdaQueryWrapper<CapacityAlarmRecordEntity>()
                                    .eq(CapacityAlarmRecordEntity::getProjectId, project.getId())
                                    .eq(CapacityAlarmRecordEntity::getGroupId, group.getId())
                                    .eq(
                                            CapacityAlarmRecordEntity::getType,
                                            CapacityAlarmTypeEnum.HAPPEN.type())
                                    .eq(
                                            CapacityAlarmRecordEntity::getTime,
                                            compareResult.getTime()));
            if (oldSameTimeRecord != null) {
                log.info(
                        "CapacityCalcPlatformJob oldSameTimeRecord 相同time记录已经存在不插入 : {}",
                        oldSameTimeRecord);
            } else {
                capacityAlarmRecordService.save(capacityAlarmRecordEntity);
                Set<String> emailList = capacityEmailService.getNoticeEmail(project.getId());
                // 需量提醒告警的信息 使用的是 需量实际的值 不是优化过的值
                NoticeEmailInfo noticeEmailInfo =
                        new NoticeEmailInfo()
                                .setTime(compareResult.getTime())
                                .setActualDemand(compareResult.capacityAlarmValue)
                                .setControlPower(controlPower)
                                .setEmailList(emailList);
                // 新增需求 需要根据是否开了 提醒 才去发送邮件
                // 要根据是否开启了 需量提醒开关 来发送邮件
                sendNoticeMessage(group, noticeEmailInfo, project);
            }
        }
        if (compareResult
                .capacityAlarmTypeEnum
                .type()
                .equals(CapacityAlarmTypeEnum.DISAPPEAR.type())) {
            capacityAlarmRecordEntity
                    .setDescription(
                            String.format(
                                    "%s 并网点实际功率(%skVA) 低于当前并网点控制目标(%skVA),阈值为(%s%s)",
                                    group.getName(),
                                    compareResult.getCapacityAlarmValue(),
                                    controlPower,
                                    group.getCapacityAlarmThreshold(),
                                    "%"))
                    .setDescriptionEn(
                            String.format(
                                    "%s The actual grid connection power (%skVA) is below the current "
                                            + "grid connection control target (%skVA), with a threshold of (%s%s).",
                                    group.getName(),
                                    compareResult.getCapacityAlarmValue(),
                                    controlPower,
                                    group.getCapacityAlarmThreshold(),
                                    "%"));
            log.info(
                    "CapacityCalcPlatformJob projectId:{} , groupId:{} , save to db  capacityAlarmRecordEntity :{}",
                    project.getId(),
                    group.getId(),
                    capacityAlarmRecordEntity);
            capacityAlarmRecordService.save(capacityAlarmRecordEntity);
        }
    }

    /**
     * 计算平台的 需量数据 和 入库
     *
     * @param group : groupEntity
     * @param project : projectEntity
     * @param controlPower : controlPower
     * @return : Map<Long, OaDemandData>
     */
    protected @Nullable Map<Long, OaDemandData> calcDemandMapAndSaveDb(
            GroupEntity group, ProjectEntity project, Double controlPower) {
        Long end = Instant.now().getEpochSecond();
        // 1.3.9 现在和以前的不一样 不是固定的几个 calModel
        // manual 和 auto calc 都是按照以前的逻辑 计算的到的
        Map<Long, OaDemandData> demandMap =
                newDemandServiceKt.platformDemandCal(
                        new RangeRequest(),
                        demandCalContext -> {
                            demandCalContext.setBucket(influxClientService.getBucketRealtime());
                            demandCalContext.setProject(project);
                            demandCalContext.setGroup(group);
                            return null;
                        });

        if (CollectionUtils.isEmpty(demandMap)) {
            return null;
        }
        return demandMap;
    }

    @Data
    @Accessors(chain = true)
    public static class NoticeEmailInfo {
        Long time;
        double actualDemand;
        double controlPower;
        Set<String> emailList;
    }

    protected void sendNoticeMessage(
            GroupEntity groupEntity, NoticeEmailInfo noticeEmailInfo, ProjectEntity projectEntity) {
        Double capacityAlarmThreshold = groupEntity.getCapacityAlarmThreshold();
        String subject;
        String message;
        if (projectEntity.getCountry() == 226) {
            subject = String.format("%s实际容量超出并网点控制目标", projectEntity.getProjectName());
            message =
                    String.format(
                            "%s %s 在 %s 实际容量(%skVA)超出当前并网点控制目标(%skVA),阈值为%s%% 请注意查看",
                            projectEntity.getProjectName(),
                            groupEntity.getName(),
                            LocalDateTime.ofEpochSecond(
                                    noticeEmailInfo.getTime(),
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())),
                            String.format("%.2f", noticeEmailInfo.getActualDemand()),
                            String.format("%.2f", noticeEmailInfo.getControlPower()),
                            String.format("%.2f", capacityAlarmThreshold));
        } else {
            subject =
                    String.format(
                            "actual capacity for %s has exceeded", projectEntity.getProjectName());
            message =
                    String.format(
                            "Please note: at %s the actual capacity(%s kVA) of %s within %s exceeds"
                                    + " its current controlled capacity (%s kVA)  the  threshold is %s%%",
                            LocalDateTime.ofEpochSecond(
                                    noticeEmailInfo.getTime(),
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())),
                            String.format("%.2f", noticeEmailInfo.getActualDemand()),
                            groupEntity.getName(),
                            projectEntity.getProjectName(),
                            String.format("%.2f", noticeEmailInfo.getControlPower()),
                            String.format("%.2f", capacityAlarmThreshold));
        }
        // 发送邮件
        emailService.sendMessage(
                new ArrayList<>(noticeEmailInfo.getEmailList()),
                subject,
                message,
                () ->
                        // type = 1 需量超了 @see EmailLogTypeEnum
                        new EmailService.ServiceInfo()
                                .setProjectId(projectEntity.getId())
                                .setGroupId(groupEntity.getId())
                                .setEmailLogTypeEnum(EmailLogTypeEnum.COUNTRY_OVER));
    }

    @Data
    @Accessors(chain = true)
    protected static class RoundContext {
        GroupEntity group;
        ProjectEntity project;
        Double controlPower;
        //        OaDemandData roundMaxDemandData;
        CapacityRoundData capacityRoundData;
    }

    @Data
    @Accessors(chain = true)
    public static class CompareResult {
        boolean isNeedRecord;
        CapacityAlarmTypeEnum capacityAlarmTypeEnum;
        Double capacityAlarmValue;
        Long time;
    }
}
