package com.wifochina.modules.capacity.service.impl;

import cn.hutool.extra.spring.SpringUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.config.EmailSuperNoticeConfig;
import com.wifochina.modules.capacity.entity.CapacityEmailEntity;
import com.wifochina.modules.capacity.mapper.CapacityEmailMapper;
import com.wifochina.modules.capacity.service.CapacityEmailService;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-10 15:21:08
 */
@Service
public class CapacityEmailServiceImpl extends ServiceImpl<CapacityEmailMapper, CapacityEmailEntity>
        implements CapacityEmailService {

    @Override
    public Set<String> getNoticeEmail(String projectId) {
        EmailSuperNoticeConfig emailSuperNotice = SpringUtil.getBean(EmailSuperNoticeConfig.class);
        Set<String> emailList =
                this.baseMapper
                        .selectList(
                                new LambdaQueryWrapper<CapacityEmailEntity>()
                                        .eq(CapacityEmailEntity::getProjectId, projectId))
                        .stream()
                        .map(CapacityEmailEntity::getEmail)
                        .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(emailSuperNotice.getDemandExceed())
                && !emailSuperNotice.getDemandExceed().get(0).startsWith("${EMS_EMAIL_NOTICE")) {
            emailList.addAll(emailSuperNotice.getDemandExceed());
        }
        return emailList;
    }
}
