package com.wifochina.modules.capacity.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025-07-10 15:07:59
 */
@Getter
@Setter
@TableName("t_capacity_email")
@ApiModel(value = "CapacityEmailEntity对象")
public class CapacityEmailEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通知识符uuid")
    private String id;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("项目id")
    private String projectId;
}
