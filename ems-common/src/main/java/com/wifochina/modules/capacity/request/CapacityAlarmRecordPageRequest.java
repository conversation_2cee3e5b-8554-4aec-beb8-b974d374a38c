package com.wifochina.modules.capacity.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-19 11:05:22
 */
@Data
public class CapacityAlarmRecordPageRequest extends PageBean {
    /**
     * 告警的类型 发生 or 消失
     *
     * @see com.wifochina.common.constants.CapacityAlarmTypeEnum
     */
    @ApiModelProperty("1发生 0消失")
    private Integer type;

    @ApiModelProperty(value = "开始时间")
    private Long startDate;

    @ApiModelProperty(value = "结束时间")
    private Long endDate;

    @ApiModelProperty(value = "分组id")
    private String groupId;

    private String projectId;
}
