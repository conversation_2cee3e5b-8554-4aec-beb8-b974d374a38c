package com.wifochina.modules.capacity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-18 16:14:01
 */
public interface CapacityAlarmRecordMapper extends BaseMapper<CapacityAlarmRecordEntity> {

    /**
     * 查询 这个项目id下面的 不同分组的 每个最新的 一条记录 且不是log日志type的 的 发生的告警类型 就是每个分组 最新一条记录要是发生的 才会查询出来
     *
     * @param projectId : projectId
     * @param groupEnableCapacityIds : groupEnableCapacityIds
     * @return : List<CapacityAlarmRecordEntity>
     */
    List<CapacityAlarmRecordEntity> getLatestRecordsUpByGroups(
            @Param("projectId") String projectId,
            @Param("groupEnableCapacityIds") List<String> groupEnableCapacityIds);
}
