package com.wifochina.modules.heatmap.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * RequestWithMeterId
 * 
 * @date 4/27/2022 9:06 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "热力图分组查询")
public class HeatMapRequestWithGroupId {

    @ApiModelProperty(value = "type代表类型。week 7天；month 近30天；year近1年 ", required = true)
    private String type;

    @ApiModelProperty(value = "分组id，all代表全部，不能传空或者空字符串")
    private String groupId;

    @ApiModelProperty(value = "设备id，all代表全部，不能传空或者空字符串")
    private String itemId;
}