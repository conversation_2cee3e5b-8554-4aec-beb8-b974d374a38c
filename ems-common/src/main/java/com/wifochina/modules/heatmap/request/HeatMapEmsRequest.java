package com.wifochina.modules.heatmap.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * reportRequest
 * 
 * @date 5/6/2022 11:17 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "EMS热力图充放电查询")
public class HeatMapEmsRequest {

    @ApiModelProperty(value = "type代表类型。week 7天；month 近30天；year近1年 ", required = true)
    private String type;

    @ApiModelProperty(value = "设备id，all代表所有", required = true)
    private String deviceId;

}
