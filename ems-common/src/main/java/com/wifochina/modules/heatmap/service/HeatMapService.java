package com.wifochina.modules.heatmap.service;

import java.util.Map;

import com.wifochina.modules.heatmap.request.HeatMapRequestWithGroupId;

/**
 * @date 4/14/2022 11:24 AM
 * <AUTHOR>
 * @version 1.0
 */
public interface HeatMapService {

    /**
     * 热力图
     */
    Map<Long, Double> getHeatMap(HeatMapRequestWithGroupId heatMapRequestWithGroupId, String column, String meterType,
        String deviceId);

    /**
     * 电压热力图
     */
    Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryVoltage(String deviceId, Long time);

    /**
     * 温度热力图
     */
    Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryTemperature(String deviceId, Long time);

    /**
     * 热力图
     */
    Map<Long, Double> getLoad(HeatMapRequestWithGroupId heatMapRequestWithGroupId);

}
