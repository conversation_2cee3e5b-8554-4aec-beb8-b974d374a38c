package com.wifochina.modules.heatmap.controller;

import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.page.Result;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.heatmap.request.HeatMapRequestWithGroupId;
import com.wifochina.modules.heatmap.service.NewHeatMapService;
import com.wifochina.modules.oauth.util.WebUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@RequestMapping("/heatMap")
@RestController
@Api(tags = "11-热力图")
@RequiredArgsConstructor
public class HeatMapController {

    private final NewHeatMapService newHeatMapService;

    private final GroupService groupService;

    /** 充电功率 */
    @PostMapping("/charge")
    @ApiOperation("充电功率")
    @PreAuthorize("hasAuthority('/heatMap/charge')")
    public Result<Map<Long, Double>> charge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> emsInMap =
                newHeatMapService.getDeviceHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field());
        return Result.success(timeShift(emsInMap));
    }

    /** 放电功率 */
    @PostMapping("/discharge")
    @ApiOperation("放电功率")
    @PreAuthorize("hasAuthority('/heatMap/discharge')")
    public Result<Map<Long, Double>> discharge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> emsOutMap =
                newHeatMapService.getDeviceHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ems_history_output_energy");
        return Result.success(timeShift(emsOutMap));
    }

    /** PV发电电功率 */
    @PostMapping("/pvDischarge")
    @ApiOperation("PV发电功率")
    @PreAuthorize("hasAuthority('/heatMap/pvDischarge') or hasAuthority('/diagram/pvDischarge_wh')")
    public Result<Map<Long, Double>> pvDischarge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> pvMap =
                newHeatMapService.getMeterHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ac_history_positive_power_in_kwh",
                        MeterTypeEnum.PV.meterType());
        return Result.success(timeShift(pvMap));
    }

    @PostMapping("/pvDcdcDischarge")
    @ApiOperation("DCDC PV热力图")
    @PreAuthorize("hasAuthority('/heatMap/pvDischarge') or hasAuthority('/diagram/pvDischarge_wh')")
    public Result<Map<Long, Double>> pvDcdcDischarge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> pvMap =
                newHeatMapService.getDeviceHeatMap(
                        WebUtils.projectId.get(), heatMapRequestWithGroupId, "dcdc_meter_power");
        return Result.success(timeShift(pvMap));
    }

    @PostMapping("/pvWasterDischarge")
    @ApiOperation("余热发电 热力图")
    @PreAuthorize("hasAuthority('/heatMap/pvWasterDischarge')")
    public Result<Map<Long, Double>> pvWasterDischarge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> wasterMap =
                newHeatMapService.getMeterHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ac_history_positive_power_in_kwh",
                        MeterTypeEnum.WASTER.meterType());
        return Result.success(timeShift(wasterMap));
    }

    /** 风电发电电功率 */
    @PostMapping("/windDischarge")
    @ApiOperation("风电发电功率")
    @PreAuthorize(
            "hasAuthority('/heatMap/windDischarge') or hasAuthority('/diagram/windDischarge_wh')")
    public Result<Map<Long, Double>> windDischarge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> windMap =
                newHeatMapService.getMeterHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ac_history_positive_power_in_kwh",
                        MeterTypeEnum.WIND.meterType());
        return Result.success(timeShift(windMap));
    }

    /** 柴油发电电功率 */
    @PostMapping("/dieselDischarge")
    @ApiOperation("柴油发电功率")
    @PreAuthorize(
            "hasAuthority('/heatMap/dieselDischarge') or"
                    + " hasAuthority('/diagram/dieselDischarge_wh')")
    public Result<Map<Long, Double>> dieselDischarge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> dieselMap =
                newHeatMapService.getMeterHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ac_history_positive_power_in_kwh",
                        MeterTypeEnum.DIESEL.meterType());
        return Result.success(timeShift(dieselMap));
    }

    /** 充电桩功率 */
    @PostMapping("/pileDischarge")
    @ApiOperation("充电桩功率")
    @PreAuthorize(
            "hasAuthority('/heatMap/pileDischarge') or hasAuthority('/diagram/pileDischarge_wh')")
    public Result<Map<Long, Double>> pileDischarge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> pileMap =
                newHeatMapService.getMeterHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ac_history_positive_power_in_kwh",
                        MeterTypeEnum.PILE.meterType());
        return Result.success(timeShift(pileMap));
    }

    /** 燃油发电电功率 */
    @PostMapping("/gasDischarge")
    @ApiOperation("燃油发电功率")
    @PreAuthorize(
            "hasAuthority('/heatMap/gasDischarge') or hasAuthority('/diagram/gasDischarge_wh')")
    public Result<Map<Long, Double>> gasDischarge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        Map<Long, Double> gasMap =
                newHeatMapService.getMeterHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ac_history_positive_power_in_kwh",
                        MeterTypeEnum.GAS.meterType());
        return Result.success(timeShift(gasMap));
    }

    /** 集装箱温度 */
    @PostMapping("/temperature")
    @ApiOperation("集装箱温度")
    @PreAuthorize("hasAuthority('/heatMap/temperature')")
    public Result<Map<String, Map<String, Map<String, Map<String, Double>>>>> getBatteryTemperature(
            @RequestParam @ApiParam(required = true, value = "设备id，all代表所有") String deviceId,
            @RequestParam(required = false) @ApiParam(value = "时间戳") Long time) {
        Map<String, Map<String, Map<String, Map<String, Double>>>> map =
                newHeatMapService.getBatteryTemperature(deviceId, time);
        return Result.success(map);
    }

    /** 集装箱电压 */
    @PostMapping("/voltage")
    @ApiOperation("集装箱电压")
    @PreAuthorize("hasAuthority('/heatMap/voltage')")
    public Result<Map<String, Map<String, Map<String, Map<String, Double>>>>> getBatteryVoltage(
            @RequestParam @ApiParam(required = true, value = "设备id，all代表所有") String deviceId,
            @RequestParam(required = false) @ApiParam(value = "时间戳") Long time) {
        Map<String, Map<String, Map<String, Map<String, Double>>>> map =
                newHeatMapService.getBatteryVoltage(deviceId, time);
        return Result.success(map);
    }

    /** 负载功率 */
    @PostMapping("/load")
    @ApiOperation("负载功率")
    @PreAuthorize("hasAuthority('/heatMap/load')")
    public Result<Map<Long, Double>> getLoad(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        GroupEntity groupEntity = groupService.systemGroupEntity(WebUtils.projectId.get());
        Map<Long, Double> map;
        if (groupEntity.getEnableLoadGrid()) {
            map =
                    newHeatMapService.getMeterHeatMap(
                            WebUtils.projectId.get(),
                            heatMapRequestWithGroupId,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.LOAD.meterType());
        } else {
            map = newHeatMapService.getLoad(WebUtils.projectId.get(), heatMapRequestWithGroupId);
        }
        return Result.success(timeShift(map));
    }

    // 处理时间，让时间整体向后偏移59分59秒 = 60*60 -1 =3599
    // 我们是frist 来求差值。 1点取的是0点的值， 2点取的是1点的值，2点-1点求差实际是00-01点的值，但这个点落在02上。
    // 然后会向前偏移2个间隔，就会落在0点上。。
    public Map<Long, Double> timeShift(Map<Long, Double> map) {
        Map<Long, Double> updatedMap = new HashMap<>();
        for (Map.Entry<Long, Double> entry : map.entrySet()) {
            long key = entry.getKey();
            double value = entry.getValue();
            long updatedKey = key + 3599;
            updatedMap.put(updatedKey, value);
        }
        return updatedMap;
    }
}
