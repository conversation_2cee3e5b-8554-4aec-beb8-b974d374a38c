package com.wifochina.modules.heatmap.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Report
 * 
 * @date 5/6/2022 12:14 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class Report {

    // 总PV发电量 pv-sum(diff(PV的ac_history_positive_power_in_kwh)):
    // 总电网取电电量 grid-sum(diff(PV的ac_history_negitive_power_in_kwh)):
    // 总馈网发电量 grid-sum(diff(PV的ac_history_positive_power_in_kwh)):
    // 电池充电电量 sum(diff(EMS的ems_history_input_energy))
    // 电池放电电量 sum(diff(EMS的ems_history_output_energy))
    //
    // EMS净出 电池放电电量 - 电池充电电量
    // 电网净出 电网取电 - 馈网电量
    // 总消耗 = 电网馈 + pv发电 +EMS放 - ems充- grid馈

    @ApiModelProperty(value = "时间")
    private Long time;

    @ApiModelProperty(value = "pv放电量")
    private double pvPower;

    @ApiModelProperty(value = "电网取电量")
    private double gridOutPower;

    @ApiModelProperty(value = "馈网电量")
    private double gridInPower;

    @ApiModelProperty(value = "EMS放电电量")
    private double emsOutPower;

    @ApiModelProperty(value = "EMS充电电量")
    private double emsInPower;

    @ApiModelProperty(value = "消耗电量")
    private double loadPower;

}
