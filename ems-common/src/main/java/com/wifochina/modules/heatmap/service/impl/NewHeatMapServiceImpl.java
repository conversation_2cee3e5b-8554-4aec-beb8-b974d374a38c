package com.wifochina.modules.heatmap.service.impl;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.heatmap.request.HeatMapRequestWithGroupId;
import com.wifochina.modules.heatmap.service.NewHeatMapService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 4/14/2022 11:25 AM
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NewHeatMapServiceImpl implements NewHeatMapService {
    private static final int MAP_DEFAULT_SIZE = 16;
    private final DeviceService deviceService;
    private final DataService dataService;
    private final InfluxClientService influxClientService;
    private final AmmeterService ammeterService;
    private final GroupService groupService;
    private final ProjectService projectService;
    private final Environment environment;
    private final PointListHolder pointListHolder;

    @Override
    public Map<Long, Double> getMeterHeatMap(
            String projectId,
            HeatMapRequestWithGroupId heatMapRequestWithGroupId,
            String column,
            Integer type) {
        FluxRateCommonHolder holder = getFluxRateCommonHolder(projectId, heatMapRequestWithGroupId);
        return getMeterDifferenceMap(projectId, heatMapRequestWithGroupId, column, type, holder);
    }

    private Map<Long, Double> getMeterDifferenceMap(
            String projectId,
            HeatMapRequestWithGroupId heatMapRequestWithGroupId,
            String column,
            Integer type,
            FluxRateCommonHolder holder) {
        return EquipmentTimeSeriesUtils.rateQueryEngine.getDifferenceMap(
                influxClientService.getBucketForever(),
                influxClientService.getMeterTable(projectId),
                holder,
                List.of(column),
                () ->
                        ammeterService.findMeterIdsByGroupIdAndItemIdAndType(
                                projectId,
                                heatMapRequestWithGroupId.getGroupId(),
                                heatMapRequestWithGroupId.getItemId(),
                                type));
    }

    public FluxRateCommonHolder getFluxRateCommonHolder(
            String projectId, HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        if (projectEntity == null) {
            return holder;
        }
        ZoneOffset zoneOffset = MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone());
        LocalDateTime endDateTime = LocalDateTime.now(zoneOffset.normalized());
        LocalDateTime startDateTime = null;
        if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.WEEK)) {
            startDateTime = endDateTime.minusDays(7);
        } else if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.MONTH)) {
            startDateTime = endDateTime.minusMonths(1);
        } else if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.YEAR)) {
            startDateTime = endDateTime.minusYears(1);
        }
        assert startDateTime != null;
        holder.setPeriod(1L);
        holder.setChronoUnit(ChronoUnit.HOURS);
        holder.setStartDate(startDateTime.toEpochSecond(zoneOffset));
        holder.setEndDate(endDateTime.toEpochSecond(zoneOffset));
        return holder;
    }

    @Override
    public Map<Long, Double> getDeviceHeatMap(
            String projectId, HeatMapRequestWithGroupId heatMapRequestWithGroupId, String column) {
        FluxRateCommonHolder holder = getFluxRateCommonHolder(projectId, heatMapRequestWithGroupId);
        return getDeviceDifferenceMap(projectId, heatMapRequestWithGroupId, column, holder);
    }

    private Map<Long, Double> getDeviceDifferenceMap(
            String projectId,
            HeatMapRequestWithGroupId heatMapRequestWithGroupId,
            String column,
            FluxRateCommonHolder holder) {
        return EquipmentTimeSeriesUtils.rateQueryEngine.getDifferenceMap(
                influxClientService.getBucketForever(),
                influxClientService.getEmsTable(projectId),
                holder,
                List.of(column),
                () ->
                        deviceService.getGroupDeviceIdList(
                                heatMapRequestWithGroupId.getGroupId(),
                                heatMapRequestWithGroupId.getItemId(),
                                false));
    }

    /**
     * 获取集装箱温度状态
     *
     * @param deviceId 设备
     * @return Map<String, Map < String, Map < String, Map < String, Double>>>> map
     */
    @Override
    public Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryTemperature(
            String deviceId, Long time) {
        String projectId = WebUtils.projectId.get();
        return getStringMapMap(deviceId, time, projectId, "temperature");
    }

    /**
     * 获取集装箱电压状态
     *
     * @param deviceId ems id
     * @return Map<String, Map>
     */
    @Override
    public Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryVoltage(
            String deviceId, Long time) {
        String projectId = WebUtils.projectId.get();
        return getStringMapMap(deviceId, time, projectId, "voltage");
    }

    private @NotNull Map<String, Map<String, Map<String, Map<String, Double>>>> getStringMapMap(
            String deviceId, Long time, String projectId, String key) {
        Map<String, Map<String, Map<String, Map<String, Double>>>> resultMap =
                new HashMap<>(MAP_DEFAULT_SIZE);
        List<String> deviceIds = new ArrayList<>();

        if (EmsConstants.ALL.equals(deviceId)) {
            deviceIds =
                    deviceService.getDevicesByPid(projectId).stream()
                            .map(DeviceEntity::getId)
                            .collect(Collectors.toList());
        } else {
            deviceIds.add(deviceId);
        }
        // 标记一下 是否是 云版本的
        boolean cloudFlag =
                Boolean.TRUE.equals(environment.getProperty("ems.cloud", Boolean.class));
        for (String id : deviceIds) {
            int[] data = dataService.get(id);
            if (data == null) {
                continue;
            }
            int emsType = data[42];
            // 电池簇数量
            int cluster = data[pointListHolder.getBmsClusterNum(emsType)];
            // 每簇电池组数量
            int stack = data[pointListHolder.getBmsStackNum(emsType)];
            // 每组电芯数量
            int cell = data[pointListHolder.getBmsCellNum(emsType)];
            // 每组电芯温度传感器数量
            int cellT = data[pointListHolder.getBmsCellTNum(emsType)];
            Map<String, Map<String, Map<String, Double>>> clusterMap =
                    new HashMap<>(cluster * stack * cell);
            int cellInitIndex = pointListHolder.getCellInitIndex(emsType);
            //            int cellIndex = 4000 + cluster * stack * cell;
            int cellIndex = cellInitIndex + cluster * stack * cell;
            for (int i = 0; i < cluster; i++) {
                Map<String, Map<String, Double>> stackTemperatureMap = new HashMap<>(stack * cellT);
                for (int j = 0; j < stack; j++) {
                    Map<String, Double> bmuTemperatureMap = new HashMap<>(cellT);
                    for (int k = 0; k < cellT; k++) {
                        double value =
                                (double) data[cellIndex + k + (j) * cellT + cellT * stack * i] / 10;
                        bmuTemperatureMap.put("cell_" + k, value);
                    }
                    stackTemperatureMap.put("stack_" + j, bmuTemperatureMap);
                }
                clusterMap.put("cluster_" + i, stackTemperatureMap);
            }
            if (time != null) {
                List<FluxTable> tables = getPower(projectId, id, time - 10 * 60, time);
                for (FluxTable table : tables) {
                    for (FluxRecord fluxRecord : table.getRecords()) {
                        Double value = (Double) fluxRecord.getValueByKey("_value");
                        String filed = (String) fluxRecord.getValueByKey("_field");
                        String index = (String) fluxRecord.getValueByKey("cellIndex");
                        String cellCluster = (String) fluxRecord.getValueByKey("cellCluster");
                        String cellStack = (String) fluxRecord.getValueByKey("cellStack");
                        assert filed != null;
                        if (filed.contains(key)) {
                            if (cloudFlag) {
                                clusterMap
                                        .get("cluster_" + cellCluster)
                                        .get("stack_" + cellStack)
                                        .put("cell_" + index, value);
                            } else {
                                String[] cIndex = filed.split("_");
                                clusterMap
                                        .get("cluster_" + cIndex[3])
                                        .get("stack_" + cIndex[4])
                                        .put("cell_" + cIndex[5], value);
                            }
                        }
                    }
                }
            }
            resultMap.put(String.valueOf(id), clusterMap);
        }
        return resultMap;
    }

    List<FluxTable> getPower(String projectId, String deviceId, long start, long end) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        Flux flux =
                Flux.from(influxClientService.getBucketForever())
                        .range(start, end)
                        .filter(
                                Restrictions.and(
                                        Restrictions.measurement()
                                                .equal(influxClientService.getCellTable(projectId)),
                                        Restrictions.tag("projectId").equal(projectId),
                                        Restrictions.tag("deviceId").equal(deviceId)))
                        .aggregateWindow(1L, ChronoUnit.MINUTES, "last")
                        .withCreateEmpty(false)
                        .timeShift(
                                -MyTimeUtil.getOffsetSecondsFromZoneCode(
                                        projectEntity.getTimezone()),
                                ChronoUnit.SECONDS,
                                Collections.singleton("_time"));
        return influxClientService.getQueryApi().query(flux.toString());
    }

    @Override
    public Map<Long, Double> getLoad(
            String projectId, HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        FluxRateCommonHolder holder = getFluxRateCommonHolder(projectId, heatMapRequestWithGroupId);
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectId);
        Map<Long, Double> pvMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> gridInMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> gridOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> emsInMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> emsOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> windOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> dieselOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> pileOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> gasOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> loadMap = new HashMap<>(MAP_DEFAULT_SIZE);
        if (Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())) {
            pvMap =
                    getMeterDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.PV.meterType(),
                            holder);
        }
        // 电网日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableElectricGrid())) {
            gridInMap =
                    getMeterDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.GRID.meterType(),
                            holder);
            gridOutMap =
                    getMeterDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ac_history_negative_power_in_kwh",
                            MeterTypeEnum.GRID.meterType(),
                            holder);
        }
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableEms())) {
            emsInMap =
                    getDeviceDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ems_history_input_energy",
                            holder);
            emsOutMap =
                    getDeviceDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ems_history_output_energy",
                            holder);
        }
        // 风电日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())) {
            windOutMap =
                    getMeterDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.WIND.meterType(),
                            holder);
        }
        // 柴发日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWoodPowerGeneration())) {
            dieselOutMap =
                    getMeterDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.DIESEL.meterType(),
                            holder);
        }
        // 充电桩日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableChargingPilePower())) {
            pileOutMap =
                    getMeterDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.PILE.meterType(),
                            holder);
        }
        // 燃气日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableGasPowerGeneration())) {
            gasOutMap =
                    getMeterDifferenceMap(
                            projectId,
                            heatMapRequestWithGroupId,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.GAS.meterType(),
                            holder);
        }
        // EMS净出 电池放电电量 - 电池充电电量
        // 电网净出 电网取电 - 馈网电量
        // 总消耗 = 电网馈 + pv发电 +EMS放 - ems充- grid馈
        Map<Long, Double> keyMap;
        List<Map<Long, Double>> maps =
                Arrays.asList(
                        gridOutMap, emsInMap, windOutMap, dieselOutMap, pileOutMap, gasOutMap);
        Optional<Map<Long, Double>> optionalKeyMap =
                maps.stream().filter(map -> !map.isEmpty()).findFirst();
        keyMap = optionalKeyMap.orElseGet(Collections::emptyMap);
        for (Long time : keyMap.keySet()) {
            double pv = 0;
            double gridIn = 0;
            double gridOut = 0;
            double emsOut = 0;
            double emsIn = 0;
            double windPower = 0;
            double dieselPower = 0;
            double pilePower = 0;
            double gasPower = 0;
            if (Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())
                    && pvMap.get(time) != null) {
                pv = pvMap.get(time);
            }
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableElectricGrid())) {
                if (gridInMap.get(time) != null) {
                    gridIn = gridInMap.get(time);
                }
                if (gridOutMap.get(time) != null) {
                    gridOut = gridOutMap.get(time);
                }
            }
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableEms())) {
                if (emsOutMap.get(time) != null) {
                    emsOut = emsOutMap.get(time);
                }
                if (emsInMap.get(time) != null) {
                    emsIn = emsInMap.get(time);
                }
            }
            // 风电日量
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())
                    && windOutMap.get(time) != null) {
                windPower = windOutMap.get(time);
            }
            // 柴发日量
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableWoodPowerGeneration())
                    && dieselOutMap.get(time) != null) {
                dieselPower = dieselOutMap.get(time);
            }
            // 充电桩日量
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableChargingPilePower())
                    && pileOutMap.get(time) != null) {
                pilePower = pileOutMap.get(time);
            }
            // 燃气日量
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableGasPowerGeneration())
                    && gasOutMap.get(time) != null) {
                gasPower = gasOutMap.get(time);
            }
            double load =
                    emsOut
                            + pv
                            + gridOut
                            - gridIn
                            - emsIn
                            + windPower
                            + dieselPower
                            + gasPower
                            - pilePower;
            if (load > 10000) {
                load = 0;
            }
            loadMap.put(time, Double.valueOf(String.format("%.2f", load)));
        }
        return loadMap;
    }
}
