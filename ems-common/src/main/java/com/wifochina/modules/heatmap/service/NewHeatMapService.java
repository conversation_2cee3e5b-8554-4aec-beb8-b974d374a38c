package com.wifochina.modules.heatmap.service;

import com.wifochina.modules.heatmap.request.HeatMapRequestWithGroupId;

import java.util.Map;

/**
 * @since 4/14/2022 11:24 AM
 * <AUTHOR>
 * @version 1.0
 */
public interface NewHeatMapService {

    /** 电压热力图 */
    Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryVoltage(
            String deviceId, Long time);

    /** 温度热力图 */
    Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryTemperature(
            String deviceId, Long time);

    /** 热力图 */
    Map<Long, Double> getLoad(
            String projectId, HeatMapRequestWithGroupId heatMapRequestWithGroupId);

    Map<Long, Double> getMeterHeatMap(
            String projectId,
            HeatMapRequestWithGroupId heatMapRequestWithGroupId,
            String column,
            Integer type);

    Map<Long, Double> getDeviceHeatMap(
            String projectId, HeatMapRequestWithGroupId heatMapRequestWithGroupId, String column);
}
