package com.wifochina.modules.weather.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WeatherRealtimeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "区域id")
    private String areaId;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "历史记录时间")
    private String obsTime;

    @ApiModelProperty(value = "温度，默认单位：摄氏度")
    private Double temp;

    @ApiModelProperty(value = "体感温度，默认单位：摄氏度")
    private Double feelsLike;

    @ApiModelProperty(value = "天气状况和图标的代码")
    private String icon;

    @ApiModelProperty(value = "天气状况的文字描述，包括阴晴雨雪等天气状态的描述")
    private String text;

    @ApiModelProperty(value = "风向360角度")
    private Double wind360;

    @ApiModelProperty(value = "风向")
    private String windDir;

    @ApiModelProperty(value = "风力等级")
    private Double windScale;

    @ApiModelProperty(value = "风速，公里/小时")
    private Double windSpeed;

    @ApiModelProperty(value = "相对湿度，百分比数值")
    private Double humidity;

    @ApiModelProperty(value = "当前小时累计降水量，默认单位：毫米")
    private Double precip;

    @ApiModelProperty(value = "大气压强，默认单位：百帕")
    private Double pressure;

    @ApiModelProperty(value = "能见度，默认单位：公里")
    private Double vis;

    @ApiModelProperty(value = "云量，百分比数值。可能为空")
    private Double cloud;

    @ApiModelProperty(value = "露点温度。可能为空")
    private Double dew;

    @ApiModelProperty(value = "精度类型,默认为areaId")
    private String type;

    @ApiModelProperty(value = "上次查询时间")
    private Long createTime;

}
