package com.wifochina.modules.weather.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.weather.service.WeatherService;
import com.wifochina.modules.weather.vo.WeatherRealtimeVo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-02-28 1:34 PM
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WeatherServiceImpl implements WeatherService {

    private final ProjectExtService projectExtService;

    private final RestTemplate restTemplate;

    @Value("${qweather.official-key}")
    private String officialKey;

    public static Map<String, WeatherRealtimeVo> weatherRealtimeVoMap = new HashMap<>();

    @Override
    public WeatherRealtimeVo getWeatherRealtime(String projectId) {
        long now = Instant.now().getEpochSecond();
        WeatherRealtimeVo weatherRealtimeVo = weatherRealtimeVoMap.get(projectId);
        int secondOfTenMinute = 600;
        if (weatherRealtimeVo == null
                || weatherRealtimeVo.getCreateTime() < now - secondOfTenMinute) {
            ProjectExtEntity projectExtEntity = projectExtService.getById(projectId);
            if (projectExtEntity == null) {
                weatherRealtimeVoMap.put(projectId, null);
                return null;
            }
            try {
                String url =
                        "https://api.qweather.com/v7/grid-weather/now?location={longitude},{latitude}&key={officialKey}&gzip=n";
                Map<String, Object> params = new HashMap<>(3);
                params.put("latitude", String.format("%.2f", projectExtEntity.getLatitude()));
                params.put("longitude", String.format("%.2f", projectExtEntity.getLongitude()));
                params.put("officialKey", officialKey);
                String result = restTemplate.getForObject(url, String.class, params);
                JSONObject jsonObject = JSON.parseObject(result);
                log.info("【实时天气】{}观察开始:", projectExtEntity.getArea());
                assert jsonObject != null;
                JSONObject jsonWeatherObject = (JSONObject) jsonObject.get("now");
                weatherRealtimeVo = new WeatherRealtimeVo();
                weatherRealtimeVo.setAreaId(projectExtEntity.getAreaId());
                weatherRealtimeVo.setProjectId(projectExtEntity.getId());
                if (jsonWeatherObject != null) {
                    weatherRealtimeVo.setCloud(jsonWeatherObject.getDouble("cloud"));
                    weatherRealtimeVo.setDew(jsonWeatherObject.getDouble("dew"));
                    weatherRealtimeVo.setFeelsLike(jsonWeatherObject.getDouble("feelsLike"));
                    weatherRealtimeVo.setHumidity(jsonWeatherObject.getDouble("humidity"));
                    weatherRealtimeVo.setObsTime(jsonWeatherObject.getString("obsTime"));
                    weatherRealtimeVo.setPrecip(jsonWeatherObject.getDouble("precip"));
                    weatherRealtimeVo.setTemp(jsonWeatherObject.getDouble("temp"));
                    weatherRealtimeVo.setPressure(jsonWeatherObject.getDouble("pressure"));
                    weatherRealtimeVo.setWindScale(jsonWeatherObject.getDouble("windScale"));
                    weatherRealtimeVo.setWindSpeed(jsonWeatherObject.getDouble("windSpeed"));
                    weatherRealtimeVo.setVis(jsonWeatherObject.getDouble("vis"));
                    weatherRealtimeVo.setWindDir(jsonWeatherObject.getString("windDir"));
                    weatherRealtimeVo.setText(jsonWeatherObject.getString("text"));
                    weatherRealtimeVo.setIcon(jsonWeatherObject.getString("icon"));
                    weatherRealtimeVo.setWind360(jsonWeatherObject.getDouble("wind360"));
                    weatherRealtimeVo.setType("areaId");
                    weatherRealtimeVo.setCreateTime(now);
                    weatherRealtimeVoMap.put(projectExtEntity.getId(), weatherRealtimeVo);
                    log.info(
                            "【实时天气】{}观察结束，观察日期：{}",
                            projectExtEntity.getArea(),
                            weatherRealtimeVo.getObsTime());
                }
            } catch (Exception e) {
                log.error(
                        "【实时天气】{} 观察失败：{}",
                        projectExtEntity.getArea() == null ? projectId : projectExtEntity.getArea(),
                        e.getCause().toString());
            }
        }
        return weatherRealtimeVo;
    }
}
