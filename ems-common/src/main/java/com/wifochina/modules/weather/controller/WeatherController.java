package com.wifochina.modules.weather.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.page.Result;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.weather.service.WeatherService;
import com.wifochina.modules.weather.vo.WeatherRealtimeVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */

@RequestMapping("/weather")
@RestController
@Api(tags = "22-天气")
@RequiredArgsConstructor
public class WeatherController {

    private final WeatherService weatherService;

    /**
     * 实时天气
     */
    @PostMapping("/getRealtime")
    @ApiOperation("实时天气")
    public Result<WeatherRealtimeVo> getRealtime() {
        WeatherRealtimeVo weatherRealtimeVo = weatherService.getWeatherRealtime(WebUtils.projectId.get());
        return Result.success(weatherRealtimeVo);
    }

}
