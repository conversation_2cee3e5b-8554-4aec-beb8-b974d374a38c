package com.wifochina.modules.maintenance.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/18 14:24
 * @version 1.0
 */
@Data
public class MaintenanceRequest {

    @ApiModelProperty(value = "厂区负责人")
    private String factoryPrincipal;

    @ApiModelProperty(value = "负责人联系方式")
    private String principalContact;

    @ApiModelProperty(value = "售后负责人")
    private String afterSale;

    @ApiModelProperty(value = "售后负责人联系方式")
    private String afterSaleContact;

    @ApiModelProperty(value = "项目地址")
    private String projectAddress;
}
