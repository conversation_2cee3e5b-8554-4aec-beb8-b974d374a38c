package com.wifochina.modules.maintenance.controller;

import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.configuration.entity.ConfigurationEntity;
import com.wifochina.modules.configuration.request.ConfigurationRequest;
import com.wifochina.modules.configuration.service.ConfigurationService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.ConfigurationLogDetailService;

import com.wifochina.modules.maintenance.entity.MaintenanceEntity;
import com.wifochina.modules.maintenance.request.MaintenanceRequest;
import com.wifochina.modules.maintenance.service.MaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/16 17:11
 * @version 1.0
 */
@Slf4j
@RestController
@Api(tags = "33-运维信息")
@RequestMapping("/maintenance")
public class MaintenanceController {

    @Resource private MaintenanceService maintenanceService;

    @GetMapping("/query")
    @ApiOperation("查询运维信息")
    @PreAuthorize("hasAuthority('/maintenance/query')")
    public Result<MaintenanceEntity> query() {
        MaintenanceEntity item = maintenanceService.queryMaintenance();
        return Result.success(item);
    }

    @PutMapping("/update")
    @ApiOperation("更新运维信息")
    @PreAuthorize("hasAuthority('/maintenance/update')")
    @Log(module = "MAINTENANCE", methods = "MAINTENANCE_UPDATE", type = OperationType.UPDATE_SIMPLE)
    public Result<Void> update(@RequestBody MaintenanceRequest request) {
        maintenanceService.saveOrUpd(request);
        return Result.success();
    }
}
