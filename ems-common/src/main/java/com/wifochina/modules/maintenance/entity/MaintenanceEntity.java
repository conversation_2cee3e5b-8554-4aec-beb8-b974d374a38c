package com.wifochina.modules.maintenance.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 运维信息
 *
 * <AUTHOR>
 * @date 2025/4/16 15:34
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("t_maintenance")
@ApiModel(value = "MaintenanceEntity", description = "MaintenanceEntity")
public class MaintenanceEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "厂区负责人")
    private String factoryPrincipal;

    @ApiModelProperty(value = "负责人联系方式")
    private String principalContact;

    @ApiModelProperty(value = "售后负责人")
    private String afterSale;

    @ApiModelProperty(value = "售后负责人联系方式")
    private String afterSaleContact;

    @ApiModelProperty(value = "项目地址")
    private String projectAddress;
}
