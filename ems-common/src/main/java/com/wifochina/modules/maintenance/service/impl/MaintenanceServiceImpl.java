package com.wifochina.modules.maintenance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.configuration.entity.ConfigurationEntity;
import com.wifochina.modules.configuration.mapper.ConfigurationMapper;
import com.wifochina.modules.maintenance.entity.MaintenanceEntity;
import com.wifochina.modules.maintenance.mapper.MaintenanceMapper;
import com.wifochina.modules.maintenance.request.MaintenanceRequest;
import com.wifochina.modules.maintenance.service.MaintenanceService;
import com.wifochina.modules.oauth.util.WebUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/18 13:55
 * @version 1.0
 */
@Service
public class MaintenanceServiceImpl extends ServiceImpl<MaintenanceMapper, MaintenanceEntity>
        implements MaintenanceService {

    @Override
    public MaintenanceEntity queryMaintenance() {
        String projectId = WebUtils.projectId.get();
        return lambdaQuery().eq(MaintenanceEntity::getProjectId, projectId).one();
    }

    @Override
    public void saveOrUpd(MaintenanceRequest request) {
        MaintenanceEntity item = queryMaintenance();
        if (item == null) {
            MaintenanceEntity newItem = new MaintenanceEntity();
            BeanUtils.copyProperties(request, newItem);
            newItem.setProjectId(WebUtils.projectId.get());
            this.save(newItem);
            return;
        }
        item.setFactoryPrincipal(request.getFactoryPrincipal());
        item.setPrincipalContact(request.getPrincipalContact());
        item.setAfterSale(request.getAfterSale());
        item.setAfterSaleContact(request.getAfterSaleContact());
        item.setProjectAddress(request.getProjectAddress());
        this.updateById(item);
    }
}
