package com.wifochina.modules.maintenance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.configuration.entity.ConfigurationEntity;
import com.wifochina.modules.configuration.request.ConfigurationRequest;
import com.wifochina.modules.maintenance.entity.MaintenanceEntity;
import com.wifochina.modules.maintenance.request.MaintenanceRequest;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @date 2025/4/18 14:23
 * @version 1.0
 */
public interface MaintenanceService extends IService<MaintenanceEntity> {

    MaintenanceEntity queryMaintenance();

    void saveOrUpd(MaintenanceRequest request);
}
