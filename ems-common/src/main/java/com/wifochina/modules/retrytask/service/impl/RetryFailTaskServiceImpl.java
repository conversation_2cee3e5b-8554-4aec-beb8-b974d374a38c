package com.wifochina.modules.retrytask.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.retrytask.entity.RetryFailTaskEntity;
import com.wifochina.modules.retrytask.mapper.RetryFailTaskMapper;
import com.wifochina.modules.retrytask.service.RetryFailTaskService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created on 2024-08-30 18:01:32
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RetryFailTaskServiceImpl extends ServiceImpl<RetryFailTaskMapper, RetryFailTaskEntity>
        implements RetryFailTaskService {}
