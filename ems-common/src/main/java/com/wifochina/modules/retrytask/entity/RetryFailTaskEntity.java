package com.wifochina.modules.retrytask.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("t_retry_fail_task")
@ApiModel(value = "RetryFailTaskEntity对象", description = "retry失败的任务记录")
public class RetryFailTaskEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("任务id")
    private String taskLabel;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("状态, true 成功， false失败")
    private Boolean state;

    @ApiModelProperty("目前重试次数")
    private Long retryCount;

    @ApiModelProperty("失败信息")
    private String failMsg;

    @ApiModelProperty("上下文json , 这个是根据需要的业务进行保存的")
    private String contextJson;
}
