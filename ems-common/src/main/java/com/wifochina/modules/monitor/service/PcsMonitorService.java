package com.wifochina.modules.monitor.service;

import java.util.List;
import java.util.Map;

import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.largescreen.request.LargeScreenPcsStatusRequest;
import com.wifochina.modules.monitor.request.PcsStatusRequest;
import com.wifochina.modules.monitor.vo.PcsStatusVo;

/**
 * @date 4/22/2022 5:17 PM
 * <AUTHOR>
 * @version 1.0
 */
public interface PcsMonitorService {

    /**
     * pcs status
     * 
     * @param pcsStatusRequest id
     * @return map
     */
    PcsStatusVo getPcsStatus(PcsStatusRequest pcsStatusRequest);

    /**
     * 获取指定 点位的 pcs 数据
     * 
     * @param request: request
     * @return : key :pcs , list 点位数据
     */
    Map<Integer, List<PointDataEntity>> getPcsAssignPointRunData(LargeScreenPcsStatusRequest request,
        List<String> list);

}
