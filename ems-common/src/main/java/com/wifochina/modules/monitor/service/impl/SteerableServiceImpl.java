package com.wifochina.modules.monitor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.RestGoUtils;
import com.wifochina.modules.data.entity.SteerableContentData;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.event.VO.SteerableEventVo;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.service.EmsEventCodeService;
import com.wifochina.modules.monitor.request.SteerableActionRequest;
import com.wifochina.modules.monitor.service.SteerableService;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;

import javax.annotation.Resource;

/**
 * @since 2024-03-15 2:28 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class SteerableServiceImpl implements SteerableService {

    @Resource private DataService dataService;
    @Resource private EmsEventCodeService eventCodeService;
    @Resource private RestGoUtils restGoUtils;

    @Override
    public SteerableContentData getSteerableStatus(String projectId, String controllableId) {
        return dataService.collectSteerableDataPoint(projectId).get(controllableId);
    }

    public List<SteerableEventVo> getSteerableEvent(String projectId, String controllableId) {

        SteerableContentData steerableContentData = getSteerableStatus(projectId, controllableId);
        List<SteerableEventVo> steerableEventVos = new ArrayList<>();
        if (steerableContentData == null) {
            return steerableEventVos;
        }
        List<EventCodeEntity> eventCodeEntityList =
                eventCodeService
                        .lambdaQuery()
                        .eq(
                                EventCodeEntity::getDeviceTypeCode,
                                steerableContentData.getDeviceTypeCode())
                        .list();
        Optional.ofNullable(steerableContentData.getStateCode())
                .ifPresent(
                        stateCode -> {
                            Map<Integer, Map<Integer, EventCodeEntity>> stateEventCodeMap =
                                    getEventMap(
                                            eventCodeEntityList,
                                            EmsConstants.CONTROLLABLE_STATE_BIT_PATTERN);
                            steerableEventVos.addAll(
                                    getEvent(
                                            steerableContentData.getStateCode(),
                                            stateEventCodeMap));
                        });
        Optional.ofNullable(steerableContentData.getAlarmCode())
                .ifPresent(
                        stateCode -> {
                            Map<Integer, Map<Integer, EventCodeEntity>> alarmEventCodeMap =
                                    getEventMap(
                                            eventCodeEntityList,
                                            EmsConstants.CONTROLLABLE_ALARM_BIT_PATTERN);
                            steerableEventVos.addAll(
                                    getEvent(
                                            steerableContentData.getAlarmCode(),
                                            alarmEventCodeMap));
                        });
        Optional.ofNullable(steerableContentData.getFaultCode())
                .ifPresent(
                        stateCode -> {
                            Map<Integer, Map<Integer, EventCodeEntity>> faultEventCodeMap =
                                    getEventMap(
                                            eventCodeEntityList,
                                            EmsConstants.CONTROLLABLE_FAULT_BIT_PATTERN);
                            steerableEventVos.addAll(
                                    getEvent(
                                            steerableContentData.getFaultCode(),
                                            faultEventCodeMap));
                        });
        return steerableEventVos;
    }

    @Override
    public Map<String, SteerableContentData> getAllSteerableStatus(String projectId) {
        return dataService.collectSteerableDataPoint(projectId);
    }

    @Override
    public void controllableActionPost(SteerableActionRequest steerableActionRequest) {
        log.info("controllableActionPost start request: {}", steerableActionRequest);
        // 发送 可控控制请求到 go
        JSONObject jsonObject;
        jsonObject =
                restGoUtils.sendPost(
                        new RestGoUtils.GoRestApi() {
                            @Override
                            public String api() {
                                return "/api/v1/controllable_cmd";
                            }

                            @Override
                            public Object request() {
                                Map<String, Object> map = new HashMap<>(3);
                                map.put("controllable_uuid", steerableActionRequest.getId());
                                map.put("value", steerableActionRequest.getValue());
                                return map;
                            }
                        },
                        JSONObject.class);
        assert jsonObject != null;
        boolean success = jsonObject.getBoolean("success");
        if (!success) {
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    private List<SteerableEventVo> getEvent(
            Integer[] codes, Map<Integer, Map<Integer, EventCodeEntity>> eventCodeMap) {
        List<SteerableEventVo> vos = new ArrayList<>();
        for (int j = 0; j < codes.length; j++) {
            for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
                EventCodeEntity eventCodeEntity =
                        eventCodeMap.getOrDefault(j, new HashMap<>()).get(i);
                if (eventCodeEntity != null) {
                    vos.add(getSteerableEventVo(codes[j], eventCodeEntity, i));
                }
            }
        }
        return vos;
    }

    @NotNull
    private static SteerableEventVo getSteerableEventVo(
            Integer j, EventCodeEntity eventCodeEntity, int i) {
        // 事件对象
        SteerableEventVo steerableEventVo = new SteerableEventVo();
        // 设备id
        steerableEventVo.setEventLevel(eventCodeEntity.getEventLevel());
        // 装备名称
        steerableEventVo.setDescription(eventCodeEntity.getEventDescription());
        // 是否维护
        steerableEventVo.setDescriptionEn(eventCodeEntity.getEventDescriptionEn());
        steerableEventVo.setValue((j & (1 << i)) != 0);
        return steerableEventVo;
    }

    private static Map<Integer, Map<Integer, EventCodeEntity>> getEventMap(
            List<EventCodeEntity> eventCodeEntityList, String s) {
        Map<Integer, Map<Integer, EventCodeEntity>> correctGroupedMap = new HashMap<>();
        eventCodeEntityList.stream()
                .filter(e -> e.getPointColumn().contains(s))
                .forEach(
                        entity -> {
                            int key1 = entity.getBitOffset() / 16;
                            int key2 = entity.getBitValue();
                            if (!correctGroupedMap.containsKey(key1)) {
                                correctGroupedMap.put(key1, new HashMap<>());
                            }
                            correctGroupedMap.get(key1).put(key2, entity);
                        });
        return correctGroupedMap;
    }
}
