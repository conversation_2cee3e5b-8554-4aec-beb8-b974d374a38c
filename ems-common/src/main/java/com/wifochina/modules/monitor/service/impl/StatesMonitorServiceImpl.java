package com.wifochina.modules.monitor.service.impl;

import com.wifochina.common.constants.EquipNumberEnum;
import com.wifochina.common.constants.TypeCodeIndexEnum;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.event.service.impl.EmsEventCodeServiceImpl;
import com.wifochina.modules.monitor.service.StatesMonitorService;
import com.wifochina.modules.monitor.vo.CommonDataAndStatusVo;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * @since 4/22/2022 5:17 PM
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class StatesMonitorServiceImpl implements StatesMonitorService {

    @Resource private DataService dataService;

    @Resource private PointListHolder pointListHolder;

    @Override
    public Map<Integer, CommonDataAndStatusVo> getStatesStatus(String deviceId) {
        int[] data = dataService.get(deviceId);
        if (data == null) {
            return Collections.emptyMap();
        }
        // 1.4.1 2025-03-05 16:59:09  修复一下 阿勇没有处理 ems200的协议问题
        // 扩展设备数量
        int statesNum = data[pointListHolder.getStatesNum(data[pointListHolder.getEmsTypeIndex()])];
        //        int statesNum = data[EquipNumberEnum.STATES.getIndex()];
        Map<Integer, CommonDataAndStatusVo> statesResultMap = new HashMap<>(statesNum);
        List<PointDataEntity> statesPointList = pointListHolder.getStatesDataPointList(data[42]);
        for (int k = 0; k < statesNum; k++) {
            CommonDataAndStatusVo statesMap =
                    MonitorServiceImpl.getDataAndStatus(
                            pointListHolder.getStatesBitPointList(data[42]),
                            k,
                            data,
                            statesPointList,
                            pointListHolder);
            int type =
                    data[
                            pointListHolder.getTypeCodeIndex(
                                    data[pointListHolder.getEmsTypeIndex()],
                                    TypeCodeIndexEnum.STATES.getCode(),
                                    k)];
            String typeCode = EmsEventCodeServiceImpl.DEVICE_TYPE_CODE_MAP.get(type);
            statesMap.setTypeCode(typeCode);
            statesResultMap.put(k, statesMap);
        }
        return statesResultMap;
    }
}
