package com.wifochina.modules.monitor.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.data.entity.SteerableContentData;
import com.wifochina.modules.event.VO.SteerableEventVo;
import com.wifochina.modules.monitor.request.SteerableActionRequest;
import com.wifochina.modules.monitor.service.*;
import com.wifochina.modules.oauth.util.WebUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 电表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@RestController
@Api(tags = "12-运行监控")
@RequestMapping("/monitor")
public class SteerableMonitorController {

    @Resource private SteerableService steerableService;

    /** 设备运行状态 */
    @PostMapping("/steerable")
    @ApiOperation("可控设备运行监控")
    @PreAuthorize("hasAuthority('/monitor/steerable')")
    public Result<Map<String, Object>> steerable(
            @RequestParam @ApiParam(required = true, name = "controllableId", value = "可控设备id")
                    String controllableId) {
        SteerableContentData steerableContentData =
                steerableService.getSteerableStatus(WebUtils.projectId.get(), controllableId);
        List<SteerableEventVo> eventVoList =
                steerableService.getSteerableEvent(WebUtils.projectId.get(), controllableId);
        if (steerableContentData == null) {
            return Result.success();
        }
        Map<String, Object> map = new HashMap<>(3);
        map.put("typeCode", steerableContentData.getDeviceTypeCode());
        map.put("data", steerableContentData.getData());
        map.put("action", steerableContentData.getSupportCmd());
        map.put("eventVoList", eventVoList);
        return Result.success(map);
    }

    @PostMapping("/canControllableAction")
    @ApiOperation("可控设备控制")
    @PreAuthorize("hasAuthority('/monitor/canControllableAction')")
    public Result<Void> canControllablePost(
            @RequestBody SteerableActionRequest steerableActionRequest) {
        steerableService.controllableActionPost(steerableActionRequest);
        return Result.success();
    }
}
