package com.wifochina.modules.monitor.service.impl;

import com.wifochina.common.constants.TypeCodeIndexEnum;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.event.service.impl.EmsEventCodeServiceImpl;
import com.wifochina.modules.monitor.request.DcdcStatusRequest;
import com.wifochina.modules.monitor.service.DcdcMonitorService;
import com.wifochina.modules.monitor.vo.CommonDataAndStatusVo;
import com.wifochina.modules.monitor.vo.DcdcMonitorDataVo;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-11-17 11:03 AM
 */
@Component
public class DcdcMonitorServiceImpl implements DcdcMonitorService {

    @Resource private DataService dataService;

    @Resource private PointListHolder pointListHolder;

    @Override
    public DcdcMonitorDataVo getDcdcStatus(DcdcStatusRequest dcdcStatusRequest) {
        DcdcMonitorDataVo dcdcMonitorDataVo = new DcdcMonitorDataVo();
        double dcdcTotalPv;
        double dcdcOutTotal;
        double dcdcInTotal;
        double dcdcMeterVoltage;
        double dcdcMeterCurrent;
        int[] data = dataService.get(dcdcStatusRequest.getDeviceId());
        if (data == null) {
            return null;
        }
        int emsType = data[pointListHolder.getEmsTypeIndex()];

        int dcdcNum = data[pointListHolder.getDcDcNum(emsType)];
        Map<Integer, CommonDataAndStatusVo> dcdcResultMap = new HashMap<>(dcdcNum);
        List<PointDataEntity> dcdcPointList = pointListHolder.getDcdcDataPointList(emsType);
        for (int k = 0; k < dcdcNum; k++) {
            CommonDataAndStatusVo dcdcMap =
                    MonitorServiceImpl.getDataAndStatus(
                            pointListHolder.getDcdcBitPointList(emsType),
                            k,
                            data,
                            dcdcPointList,
                            pointListHolder);
            int typeCodeIndex =
                    data[
                            pointListHolder.getTypeCodeIndex(
                                    data[pointListHolder.getEmsTypeIndex()],
                                    TypeCodeIndexEnum.DCDC.getCode(),
                                    k)];
            String typeCode = EmsEventCodeServiceImpl.DEVICE_TYPE_CODE_MAP.get(typeCodeIndex);
            dcdcMap.setTypeCode(typeCode);
            dcdcResultMap.put(k, dcdcMap);
        }
        // 获取到 DCDC的合计的相关数据
        dcdcTotalPv = (short) data[pointListHolder.getDcdcMeterPower(emsType)];
        dcdcTotalPv = Double.parseDouble(String.format("%.2f", dcdcTotalPv / 10));
        int high = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType)];
        int low = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType) + 1];
        dcdcOutTotal = (high << 16) + low;
        dcdcOutTotal = Double.parseDouble(String.format("%.2f", dcdcOutTotal / 10));
        dcdcMeterVoltage = data[pointListHolder.getDcdcMeterVoltage(emsType)];
        dcdcMeterVoltage = Double.parseDouble(String.format("%.2f", dcdcMeterVoltage / 10));
        dcdcMeterCurrent = (short) data[pointListHolder.getDcdcMeterCurrent(emsType)];
        dcdcMeterCurrent = Double.parseDouble(String.format("%.2f", dcdcMeterCurrent / 10));
        int high2 = data[pointListHolder.getDcdcMeterHistoryEnergyEng(emsType)];
        int low2 = data[pointListHolder.getDcdcMeterHistoryEnergyEng(emsType) + 1];
        dcdcInTotal = (high2 << 16) + low2;
        dcdcInTotal = Double.parseDouble(String.format("%.2f", dcdcInTotal / 10));
        dcdcMonitorDataVo.setDcdcDataStatusMap(dcdcResultMap);
        dcdcMonitorDataVo.setDcdcTotalPv(dcdcTotalPv);
        dcdcMonitorDataVo.setDcdcOutTotal(dcdcOutTotal);
        dcdcMonitorDataVo.setDcdcMeterVoltage(dcdcMeterVoltage);
        dcdcMonitorDataVo.setDcdcMeterCurrent(dcdcMeterCurrent);
        dcdcMonitorDataVo.setDcdcInTotal(dcdcInTotal);
        return dcdcMonitorDataVo;
    }
}
