package com.wifochina.modules.monitor.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BatteryVO
 *
 * @date 4/24/2022 9:16 AM
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value = "电池芯信息")
@Data
public class SystemInfoVO {

    @ApiModelProperty(value = "device id")
    private String deviceId;

    @ApiModelProperty(value = "device name")
    private String deviceName;

    @ApiModelProperty(value = "pcs数量（PCS_COUNT),序号从0开始)")
    private Integer pcsNum;

    @ApiModelProperty(value = "BMS数量(BMS_COUNT),序号从0开始)")
    private Integer bmsNum;

    @ApiModelProperty(value = " 电池簇数量(CLUSTER <= 50,序号从0开始)")
    private Integer bmsClusterNum;

    @ApiModelProperty(value = "空调数量(AIR_COUNT <= 4)")
    private Integer airNum;

    @ApiModelProperty(value = "消防设备数量(FIRE_COUNT <= 4)")
    private Integer fireNum;

    @ApiModelProperty(value = "水冷机数量(WATER_COUNT <= 4)")
    private Integer waterNum;

    @ApiModelProperty(value = "pcs control")
    private Boolean pcsControl;
}
