package com.wifochina.modules.monitor.service;

import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.data.entity.MeterContentData;
import com.wifochina.modules.event.VO.MeterContentDataVo;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.monitor.request.ControllableActionRequest;
import com.wifochina.modules.monitor.vo.CommonDataAndStatusVo;

import org.springframework.data.util.Pair;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @since 4/22/2022 5:17 PM
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorService {

    /**
     * bms status
     *
     * @param deviceId id
     * @return map
     */
    Map<String, CommonDataAndStatusVo> getBmsStatus(String deviceId);

    /**
     * batter status
     *
     * @param deviceId id
     * @return map
     */
    Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryStatus(String deviceId);

    /**
     * air fire status
     *
     * @param deviceId id
     * @return map
     */
    Map<String, Map<Integer, CommonDataAndStatusVo>> getAirFireStatus(String deviceId);

    /**
     * controllable status
     *
     * @param meterId id
     * @return map
     */
    MeterContentDataVo getControllableStatus(String project, String meterId);

    /**
     * run status
     *
     * @return map
     * @throws ExecutionException from cache
     */
    Map<String, Map<String, Object>> getRunStatus(
            String projectId, boolean checkPermission, boolean showMaintain)
            throws ExecutionException;

    /**
     * @param projectId project id
     * @param deviceId device id
     * @return Map<String, Map<String, Object>>
     * @throws ExecutionException from cache
     */
    Map<String, Map<String, Object>> getOneEmsRunStatus(
            String projectId, String deviceId, boolean notShowHide, boolean showHideMaintain)
            throws ExecutionException;

    /**
     * 根据电表id 和 projectId 查询 项目下面的 电表的可控项
     *
     * @param meterId : 电表id
     * @return : List<MeterEventCodeEntity> 可控项列表
     */
    List<MeterEventCodeEntity> getControllableItem(String project, String meterId);

    void controllableActionPost(ControllableActionRequest controllableActionRequest);

    List<MeterContentDataVo> realTimeMetersMonitor();

    MeterContentDataVo realTimeMeter(
            AmmeterEntity ammeterEntity, Map<String, MeterContentData> meterContentDataMap);

    Pair<Integer, Integer> setEmsRunningStatus(
            String projectId,
            Map<String, Map<String, Object>> resultMap,
            List<String> serialNumbers,
            DeviceEntity deviceEntity,
            boolean notShowHide)
            throws ExecutionException;

    List<CapacityAlarmRecordEntity> getCapacityAlarmStatus(String projectId);

    Map<String, List<Map<String, Object>>>  getHookUpData();
}
