package com.wifochina.modules.monitor.service;

import com.wifochina.modules.data.entity.SteerableContentData;
import com.wifochina.modules.event.VO.SteerableEventVo;
import com.wifochina.modules.monitor.request.SteerableActionRequest;

import java.util.List;
import java.util.Map;

/**
 * @since 2024-03-15 12:03 PM
 * <AUTHOR>
 */
public interface SteerableService {
    SteerableContentData getSteerableStatus(String projectId, String controllableId);

    List<SteerableEventVo> getSteerableEvent(String projectId, String controllableId);

    Map<String, SteerableContentData> getAllSteerableStatus(String projectId);

    void controllableActionPost(SteerableActionRequest steerableActionRequest);
}
