package com.wifochina.modules.monitor.vo;

import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.event.entity.EventCodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-07 1:06 PM
 */
@ApiModel(value = "通用数据和状态：air,water,fire")
@Data
public class CommonDataAndStatusVo {
    @ApiModelProperty(value = "pcs状态")
    private List<EventCodeEntity> status;

    @ApiModelProperty(value = "pcs运行数据")
    private List<PointDataEntity> data;

    @ApiModelProperty(value = "pcs type code")
    private String typeCode;
}
