package com.wifochina.modules.monitor.controller;

import com.wifochina.common.constants.DeviceOnlineStatusEnum;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.demand.service.DemandAlarmService;
import com.wifochina.modules.demand.vo.GroupDemandAlarmVo;
import com.wifochina.modules.event.VO.MeterContentDataVo;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.enums.DeviceControlModelEnums;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.monitor.request.ControllableActionRequest;
import com.wifochina.modules.monitor.request.DcdcStatusRequest;
import com.wifochina.modules.monitor.request.PcsStatusRequest;
import com.wifochina.modules.monitor.request.StatesStatusRequest;
import com.wifochina.modules.monitor.service.DcdcMonitorService;
import com.wifochina.modules.monitor.service.MonitorService;
import com.wifochina.modules.monitor.service.PcsMonitorService;
import com.wifochina.modules.monitor.service.StatesMonitorService;
import com.wifochina.modules.monitor.vo.CommonDataAndStatusVo;
import com.wifochina.modules.monitor.vo.DcdcMonitorDataVo;
import com.wifochina.modules.monitor.vo.PcsStatusVo;
import com.wifochina.modules.monitor.vo.SystemInfoVO;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

/**
 * 电表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@RestController
@Api(tags = "12-运行监控")
@RequestMapping("/monitor")
public class MonitorController {

    @Resource private MonitorService monitorService;
    @Resource private DeviceService deviceService;
    @Resource private DataService dataService;
    @Resource private PcsMonitorService pcsMonitorService;
    @Resource private DcdcMonitorService dcdcMonitorService;
    @Resource private StatesMonitorService statesMonitorService;
    @Resource private DemandAlarmService demandAlarmService;
    @Autowired private PointListHolder pointListHolder;

    @GetMapping("/getHookUpData")
    @ApiOperation("接线图信息")
    //    @PreAuthorize("hasAuthority('/monitor/bmsStatus')")
    public Result<Map<String, List<Map<String, Object>>>> getHookUpData() {
        Map<String, List<Map<String, Object>>> info = monitorService.getHookUpData();
        return Result.success(info);
    }

    /** bms运行状态 */
    @GetMapping("/bmsStatus")
    @ApiOperation("bms运行状态")
    @PreAuthorize("hasAuthority('/monitor/bmsStatus')")
    public Result<Map<String, CommonDataAndStatusVo>> getBmsStatus(
            @RequestParam @ApiParam(required = true, name = "deviceId", value = "设备id")
                    String deviceId) {
        Map<String, CommonDataAndStatusVo> resultMap = monitorService.getBmsStatus(deviceId);
        return Result.success(resultMap);
    }

    /** 电池簇单体状态查询 */
    @GetMapping("/batteryStatus")
    @ApiOperation("电池簇单体状态")
    @PreAuthorize("hasAuthority('/monitor/batteryStatus')")
    public Result<Map<String, Map<String, Map<String, Map<String, Double>>>>> getBatteryStatus(
            @RequestParam @ApiParam(required = true, name = "deviceId", value = "设备id")
                    String deviceId) {
        Map<String, Map<String, Map<String, Map<String, Double>>>> resultMap =
                monitorService.getBatteryStatus(deviceId);
        return Result.success(resultMap);
    }

    /** 空调消防状态查询 */
    @GetMapping("/airFireStatus")
    @ApiOperation("空调消防状态")
    @PreAuthorize("hasAuthority('/monitor/airFireStatus')")
    public Result<Map<String, Map<Integer, CommonDataAndStatusVo>>> getAirFireStatus(
            @RequestParam @ApiParam(required = true, name = "deviceId", value = "设备id")
                    String deviceId) {
        Map<String, Map<Integer, CommonDataAndStatusVo>> resultMap =
                monitorService.getAirFireStatus(deviceId);
        return Result.success(resultMap);
    }

    /** 测控电表状态 */
    @GetMapping("/controllableStatus")
    @ApiOperation("测控电表状态")
    @PreAuthorize("hasAuthority('/monitor/controllableStatus')")
    public Result<MeterContentDataVo> getControllableStatus(
            @RequestParam @ApiParam(required = true, name = "meterId", value = "电表id")
                    String meterId) {
        // 2023-11-07 16:14:15 add 查询测控电表的 可以控制的项
        List<MeterEventCodeEntity> controllableItems =
                monitorService.getControllableItem(WebUtils.projectId.get(), meterId);
        MeterContentDataVo meterContentDataVo =
                monitorService.getControllableStatus(WebUtils.projectId.get(), meterId);
        meterContentDataVo.setMeterControllableItems(controllableItems);
        return Result.success(meterContentDataVo);
    }

    /**
     * 2023-11-07 16:17:30 add 操作按钮是单独的权限
     *
     * @param controllableActionRequest : request
     * @return : void
     */
    @PostMapping("/controllableAction")
    @ApiOperation("测控电表控制")
    @PreAuthorize("hasAuthority('/monitor/controllableAction')")
    public Result<Void> controllablePost(
            @RequestBody ControllableActionRequest controllableActionRequest) {
        // 2023-11-07 16:14:15 add 查询测控电表的 可以控制的项
        monitorService.controllableActionPost(controllableActionRequest);
        return Result.success();
    }

    @PostMapping("/realTimeMeterMonitor")
    @ApiOperation("实时电表运行监控")
    @PreAuthorize("hasAuthority('/monitor/realTimeMeterMonitor')")
    public Result<List<MeterContentDataVo>> realTimeMeterMonitor() {
        // 实时电表运行监控
        return Result.success(monitorService.realTimeMetersMonitor());
    }

    /** 获取当前系统所有的装备数目 */
    @GetMapping("/getSystemInfo")
    @ApiOperation("系统各装备数量查询")
    public Result<Map<String, SystemInfoVO>> getSystemInfo() {
        List<DeviceEntity> deviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                        .eq(DeviceEntity::getUnreal, false)
                        .list();
        Map<String, SystemInfoVO> resultMap = new HashMap<>(deviceEntities.size());
        for (DeviceEntity deviceEntity : deviceEntities) {
            int[] data = dataService.get(deviceEntity.getId());
            Optional.ofNullable(data)
                    .ifPresent(
                            e -> {
                                int emsType = e[42];
                                SystemInfoVO systemInfoVO = new SystemInfoVO();
                                systemInfoVO.setDeviceName(deviceEntity.getName());
                                systemInfoVO.setDeviceId(deviceEntity.getId());
                                systemInfoVO.setBmsNum(data[pointListHolder.getBmsNum(emsType)]);
                                systemInfoVO.setBmsClusterNum(
                                        data[pointListHolder.getBmsClusterNum(emsType)]);
                                systemInfoVO.setPcsNum(data[pointListHolder.getPcsNum(emsType)]);
                                systemInfoVO.setAirNum(data[pointListHolder.getAirNum(emsType)]);
                                systemInfoVO.setFireNum(data[pointListHolder.getFireNum(emsType)]);
                                systemInfoVO.setWaterNum(
                                        data[pointListHolder.getWaterNum(emsType)]);
                                // 1.4.4 added pcs control
                                systemInfoVO.setPcsControl(
                                        DeviceControlModelEnums.PCS_CONTROL
                                                .name()
                                                .equals(deviceEntity.getDeviceControlModel()));
                                resultMap.put(deviceEntity.getId(), systemInfoVO);
                            });
        }

        return Result.success(resultMap);
    }

    /** 运行状态 */
    @GetMapping("/getRunStatus")
    @ApiOperation("系统运行状态")
    public Result<Map<String, Object>> getRunStatus() throws ExecutionException {
        Map<String, Object> resultMap = new HashMap<>(16);
        List<GroupDemandAlarmVo> list = demandAlarmService.getDemandAlarm(WebUtils.projectId.get());
        resultMap.put(EmsConstants.DEMAND_NOTICE, list);
        // 1.4.2 added 容量告警查询 async
        String projectId = WebUtils.projectId.get();
        CompletableFuture<List<CapacityAlarmRecordEntity>> listCompletableFuture =
                CompletableFuture.supplyAsync(
                        () -> monitorService.getCapacityAlarmStatus(projectId));
        Map<String, Map<String, Object>> statusMap =
                monitorService.getRunStatus(
                        WebUtils.projectId.get(),
                        SecurityUtil.hasAuthority(EmsConstants.MESSAGE_SHOW_HIDE),
                        SecurityUtil.hasAuthority(EmsConstants.DEVICE_MAINTAIN_AUTHORITY));
        resultMap.putAll(statusMap);
        CompletableFuture<Void> allOf = CompletableFuture.allOf(listCompletableFuture);
        allOf.join();
        List<CapacityAlarmRecordEntity> lastCapacityAlarmRecords;
        try {
            lastCapacityAlarmRecords = listCompletableFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        resultMap.put(EmsConstants.CAPACITY_NOTICE, lastCapacityAlarmRecords);
        return Result.success(resultMap);
    }

    /** pcs运行状态 */
    @PostMapping("/pcsStatus")
    @ApiOperation("pcs运行状态")
    @PreAuthorize("hasAuthority('/monitor/pcsStatus')")
    public Result<PcsStatusVo> getRunStatus(@RequestBody PcsStatusRequest pcsStatusRequest) {
        PcsStatusVo pcsStatusVo = pcsMonitorService.getPcsStatus(pcsStatusRequest);
        return Result.success(pcsStatusVo);
    }

    /** dcdc运行状态 */
    @PostMapping("/dcdcStatus")
    @ApiOperation("dcdc运行状态")
    @PreAuthorize("hasAuthority('/monitor/dcdcStatus')")
    public Result<DcdcMonitorDataVo> getDcdcStatus(
            @RequestBody DcdcStatusRequest dcdcStatusRequest) {
        DcdcMonitorDataVo dcdcMonitorDataVo = dcdcMonitorService.getDcdcStatus(dcdcStatusRequest);
        return Result.success(dcdcMonitorDataVo);
    }

    /** states运行状态 */
    @PostMapping("/statesStatus")
    @ApiOperation("status运行状态")
    @PreAuthorize("hasAuthority('/monitor/statesStatus')")
    public Result<Map<String, Object>> getStatesStatus(
            @RequestBody StatesStatusRequest statesStatusRequest) {
        Map<String, Object> devicesMap = new HashMap<>(10);
        if (EmsConstants.ALL.equals(statesStatusRequest.getDeviceId())) {
            List<DeviceEntity> deviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                            .eq(DeviceEntity::getUnreal, false)
                            .orderByAsc(DeviceEntity::getCreateTime)
                            .list();
            for (DeviceEntity deviceEntity : deviceEntities) {
                setStateMap(deviceEntity, devicesMap);
            }
        } else {
            DeviceEntity deviceEntity = deviceService.getById(statesStatusRequest.getDeviceId());
            setStateMap(deviceEntity, devicesMap);
        }
        return Result.success(devicesMap);
    }

    private void setStateMap(DeviceEntity deviceEntity, Map<String, Object> devicesMap) {
        Map<Integer, CommonDataAndStatusVo> resultMap =
                statesMonitorService.getStatesStatus(deviceEntity.getId());
        Map<String, Object> deviceMap = new HashMap<>(2);
        deviceMap.put("name", deviceEntity.getName());
        deviceMap.put("states", resultMap);
        devicesMap.put(deviceEntity.getId(), deviceMap);
    }
}
