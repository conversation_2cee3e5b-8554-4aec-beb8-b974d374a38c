package com.wifochina.modules.monitor.service.impl;

import com.wifochina.common.constants.TypeCodeIndexEnum;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndPcs;
import com.wifochina.modules.diagram.service.NewDiagramService;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.service.impl.EmsEventCodeServiceImpl;
import com.wifochina.modules.largescreen.request.LargeScreenPcsStatusRequest;
import com.wifochina.modules.monitor.request.PcsStatusRequest;
import com.wifochina.modules.monitor.service.PcsMonitorService;
import com.wifochina.modules.monitor.vo.PcsStatusVo;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MonitorServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 4/22/2022 5:18 PM
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PcsMonitorServiceImpl implements PcsMonitorService {

    private final PointListHolder pointListHolder;

    private final NewDiagramService newDiagramService;

    private final DataService dataService;

    /**
     * 获取pcs运行状态
     *
     * @param pcsStatusRequest 设备id
     * @return Map
     */
    @Override
    public PcsStatusVo getPcsStatus(PcsStatusRequest pcsStatusRequest) {
        int[] data = dataService.get(pcsStatusRequest.getDeviceId());
        if (data == null) {
            return null;
        }
        if (pcsStatusRequest.getIndex() == null
                || EmsConstants.ALL.equals(pcsStatusRequest.getIndex())) {
            pcsStatusRequest.setIndex("0");
        }
        // 查询出非状态位数据
        List<PointDataEntity> pointList =
                pointListHolder.getPcsDataPointList(data[pointListHolder.getEmsTypeIndex()]);
        // 查询出状态位数据
        List<PointDataEntity> bitPointList =
                pointListHolder.getPcsBitPointList(data[pointListHolder.getEmsTypeIndex()]);
        RequestWithDeviceIdAndPcs requestWithDeviceIdAndPcs = new RequestWithDeviceIdAndPcs();
        requestWithDeviceIdAndPcs.setDeviceId(pcsStatusRequest.getDeviceId());
        requestWithDeviceIdAndPcs.setEndDate(Instant.now().getEpochSecond());
        requestWithDeviceIdAndPcs.setStartDate(
                Instant.now().getEpochSecond() - MyTimeUtil.ONE_DAY_SECONDS);
        requestWithDeviceIdAndPcs.setIndex(pcsStatusRequest.getIndex());
        // 获取有功无功曲线
        // 第一个key是设备id，第二个key是pcs_0_active_power
        Map<String, List<ValueVO>> rateMap =
                newDiagramService.getPcsRate(requestWithDeviceIdAndPcs);
        // TODO refactor remove code
        // Map<String, List<ValueVO>> rateMap =
        // diagramService.getPcsRate(requestWithDeviceIdAndPcs);
        // 事件返回值
        PcsStatusVo pcsStatusVo = new PcsStatusVo();
        // 获取到key
        pcsStatusVo.setActive(rateMap.get("active"));
        pcsStatusVo.setReactive(rateMap.get("reactive"));
        // 存储事件状态列表
        List<EventCodeEntity> eventCodeEntityList = new ArrayList<>();
        MonitorServiceImpl.getEventList(
                bitPointList,
                Integer.parseInt(pcsStatusRequest.getIndex()),
                data,
                eventCodeEntityList,
                pointListHolder);
        pcsStatusVo.setStatus(eventCodeEntityList);
        // 存储运行数据列表
        List<PointDataEntity> pointEntityList = new ArrayList<>();
        // 存储运行数据列表
        for (PointDataEntity pointDataEntityTemplate : pointList) {
            PointDataEntity pointDataEntity = new PointDataEntity();
            BeanUtils.copyProperties(pointDataEntityTemplate, pointDataEntity);
            int index =
                    MonitorServiceImpl.getIndex(
                            Integer.parseInt(pcsStatusRequest.getIndex()), pointDataEntity);
            String value =
                    MonitorServiceImpl.getValue(
                            data,
                            index,
                            pointDataEntity.getPointType(),
                            pointDataEntity.getPointMul());
            pointDataEntity.setValue(value);
            pointEntityList.add(pointDataEntity);
        }
        // 添加pcs型号
        int type =
                data[
                        pointListHolder.getTypeCodeIndex(
                                data[pointListHolder.getEmsTypeIndex()],
                                TypeCodeIndexEnum.PCS.getCode(),
                                Integer.valueOf(pcsStatusRequest.getIndex()))];
        ;
        String typeCode = EmsEventCodeServiceImpl.DEVICE_TYPE_CODE_MAP.get(type);
        pcsStatusVo.setTypeCode(typeCode);
        pcsStatusVo.setData(pointEntityList);
        return pcsStatusVo;
    }

    /**
     * 根据设备 获取 设备内 所有pcs 指定点位 dcPointColumns 的运行数据
     *
     * @param request: 请求 request
     * @param dcPointColumns : 指定点位point_column 列表
     * @return : Map<Integer, List<PointDataEntity>> , key = 第几个pcs , value 是 这个pcs的 电流 电压
     */
    @SneakyThrows
    @Override
    public Map<Integer, List<PointDataEntity>> getPcsAssignPointRunData(
            LargeScreenPcsStatusRequest request, List<String> dcPointColumns) {
        int[] data = dataService.get(request.getDeviceId());
        if (data == null) {
            log.error(
                    "-------------------data is null-------------deviceId: {}------",
                    request.getDeviceId());
            return null;
        }
        // 获取到设备的pcs数量
        int pcsCount = data[pointListHolder.getPcsNum(data[42])];
        Map<Integer, List<PointDataEntity>> result = new HashMap<>(pcsCount);

        // 查询出非状态位数据
        List<PointDataEntity> pointList = pointListHolder.getPcsDataPointList(data[42]);
        pointList =
                pointList.stream()
                        .filter(
                                pointDataEntity ->
                                        dcPointColumns.contains(pointDataEntity.getPointColumn()))
                        .collect(Collectors.toList());
        // 只关心电流 电压 点位数据 存储运行数据列表
        for (int i = 1; i <= pcsCount; i++) {
            List<PointDataEntity> pcsPointDataEntityList = new ArrayList<>();
            for (PointDataEntity pointDataEntityTemplate : pointList) {
                PointDataEntity pointDataEntity = (PointDataEntity) pointDataEntityTemplate.clone();
                // i 代表 pcs index
                // index : 该点位要从哪个位置读取
                int index =
                        MonitorServiceImpl.getIndex(
                                Integer.parseInt(String.valueOf(i - 1)), pointDataEntity);
                String value =
                        MonitorServiceImpl.getValue(
                                data,
                                index,
                                pointDataEntity.getPointType(),
                                pointDataEntity.getPointMul());
                pointDataEntity.setValue(value);
                pcsPointDataEntityList.add(pointDataEntity);
            }
            // key = 第几个pcs , value 是 这个pcs的 电流 电压
            result.put(i, pcsPointDataEntityList);
        }
        return result;
    }
}
