package com.wifochina.modules.monitor.vo;

import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2024/1/11 17:01.
 *
 * <AUTHOR>
 */
@Data
public class DcdcMonitorDataVo {

    private Map<Integer, CommonDataAndStatusVo> dcdcDataStatusMap;
    @ApiModelProperty("DCDC光伏侧功率(kW)")
    private Double dcdcTotalPv;
    @ApiModelProperty("DCDC光伏侧历史输出能量(kWh)")
    private Double dcdcOutTotal;
    @ApiModelProperty("DCDC光伏侧历史输入能量(kWh)")
    private Double dcdcInTotal;
    @ApiModelProperty("DCDC光伏侧电压(V)")

    private Double dcdcMeterVoltage;
    @ApiModelProperty("DCDC光伏侧电流(A)")
    private Double dcdcMeterCurrent;

}
