package com.wifochina.modules.monitor.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-03-03 6:14 PM
 */
@Data
@ApiModel(value = "按照某个pcs查询设备")
public class PcsStatusRequest {
    @ApiModelProperty(value = "所属设备id，默认为all，all代表全部，不能传空或者空字符串")
    private String deviceId;

    @ApiModelProperty(value = "pcs序号")
    private String index;
}
