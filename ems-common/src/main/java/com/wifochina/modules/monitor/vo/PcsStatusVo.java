package com.wifochina.modules.monitor.vo;

import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.event.entity.EventCodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-07 11:09 AM
 */
@ApiModel(value = "pcs状态")
@Data
public class PcsStatusVo extends CommonDataAndStatusVo{

    @ApiModelProperty(value = "psc有功功率")
    private List<ValueVO> active;

    @ApiModelProperty(value = "pcs24小时无功功率")
    private List<ValueVO> reactive;

}
