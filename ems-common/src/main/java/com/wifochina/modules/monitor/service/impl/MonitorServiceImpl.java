package com.wifochina.modules.monitor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.InstallationEnum;
import com.wifochina.common.constants.TypeCodeIndexEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.realtime.RealTimeUtils;
import com.wifochina.common.util.*;
import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.capacity.service.CapacityAlarmRecordService;
import com.wifochina.modules.data.entity.*;
import com.wifochina.modules.data.listener.DataEventListener;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.event.VO.MeterContentDataVo;
import com.wifochina.modules.event.VO.MeterEventVo;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.enums.HideCodeLabelEnums;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.event.service.MeterEventCodeService;
import com.wifochina.modules.event.service.impl.EmsEventCodeServiceImpl;
import com.wifochina.modules.event.service.impl.EventCodeLanguageServiceImpl;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.group.vo.StatusVo;
import com.wifochina.modules.monitor.request.ControllableActionRequest;
import com.wifochina.modules.monitor.service.MonitorService;
import com.wifochina.modules.monitor.vo.CommonDataAndStatusVo;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.realtimemodel.common.*;
import com.wifochina.realtimemodel.resolve.DefaultRealTimeResolve;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * MonitorServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 4/22/2022 5:18 PM
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MonitorServiceImpl implements MonitorService {

    private final PointListHolder pointListHolder;
    private final DeviceService deviceService;

    private final DataService dataService;

    private final MeterEventCodeService meterEventCodeService;

    private final AmmeterService ammeterService;

    private final RestTemplate restTemplate;

    private final ProjectService projectService;

    private final ControllerService controllerService;

    private final ControllableService controllableService;

    private final CameraService cameraService;

    private final RestGoUtils restGoUtils;

    private final HideEventCodeService hideEventCodeService;

    private final RedisTemplate<String, String> redisTemplate;
    private final CapacityAlarmRecordService capacityAlarmRecordService;
    private final GroupService groupService;

    @Autowired private RealTimeUtils realTimeUtils;

    @Autowired private DefaultRealTimeResolve defaultRealTimeResolve;

    private static final String DEVICE = "device:";
    private static final String SERIAL = "serial";

    @Value("${ems.gateway}")
    private String gatewayUrl;

    private static final LoadingCache<String, Integer> CACHE =
            CacheBuilder.newBuilder()
                    // 缓存项写入后多久过期，下文详述
                    .expireAfterWrite(1, TimeUnit.HOURS)
                    // 创建一个 CacheLoader，load 表示缓存不存在的时候加载到缓存并返回
                    .build(
                            new CacheLoader<>() {
                                // 加载缓存数据的方法
                                @Override
                                public Integer load(@NotNull String key) {
                                    return 0;
                                }
                            });

    /** 获取bms运行状态 */
    @Override
    public Map<String, CommonDataAndStatusVo> getBmsStatus(String deviceId) {
        int[] data = dataService.get(deviceId);
        if (data == null) {
            return Collections.emptyMap();
        }
        // cluster点位
        int clusterNum =
                data[pointListHolder.getBmsClusterNum(data[pointListHolder.getEmsTypeIndex()])];
        // 存储整个接口数据
        Map<String, CommonDataAndStatusVo> resultMap = new HashMap<>(clusterNum + 1);
        // 存储common数据
        // 筛选出公共运行数据 不是状态且不是集群读数
        List<PointDataEntity> commonDataList =
                pointListHolder.getBmsCommonDataList(data[pointListHolder.getEmsTypeIndex()]);
        // 存储运行数据列表
        for (PointDataEntity pointDataEntity : commonDataList) {
            int index =
                    pointDataEntity.getPointAddress()
                            + Integer.parseInt(pointDataEntity.getPointOffset());
            String value =
                    getValue(
                            data,
                            index,
                            pointDataEntity.getPointType(),
                            pointDataEntity.getPointMul());
            pointDataEntity.setValue(value);
        }
        CommonDataAndStatusVo bmsDataAndStatusVo = new CommonDataAndStatusVo();
        bmsDataAndStatusVo.setData(commonDataList);
        // 筛选出公共运行状态
        List<PointDataEntity> commonBitList =
                pointListHolder.getBmsCommonBitList(data[pointListHolder.getEmsTypeIndex()]);
        // 存储事件状态列表
        List<EventCodeEntity> eventCodeEntityList = new ArrayList<>();
        getEventList(commonBitList, null, data, eventCodeEntityList, pointListHolder);
        bmsDataAndStatusVo.setStatus(eventCodeEntityList);
        int type =
                data[
                        pointListHolder.getTypeCodeIndex(
                                data[pointListHolder.getEmsTypeIndex()],
                                TypeCodeIndexEnum.BMS.getCode(),
                                null)];
        String typeCode = EmsEventCodeServiceImpl.DEVICE_TYPE_CODE_MAP.get(type);
        bmsDataAndStatusVo.setTypeCode(typeCode);
        resultMap.put("common", bmsDataAndStatusVo);
        // 筛选出电池簇运行数据
        List<PointDataEntity> clusterDataList =
                pointListHolder.getBmsClusterDataPointList(data[pointListHolder.getEmsTypeIndex()]);
        // 筛选出电池簇运行状态
        List<PointDataEntity> clusterBitList =
                pointListHolder.getBmsClusterBitPointList(data[pointListHolder.getEmsTypeIndex()]);
        for (int k = 0; k < clusterNum; k++) {
            CommonDataAndStatusVo clusterMap =
                    getDataAndStatus(clusterBitList, k, data, clusterDataList, pointListHolder);
            resultMap.put("cluster" + k, clusterMap);
        }
        return resultMap;
    }

    public static CommonDataAndStatusVo getDataAndStatus(
            List<PointDataEntity> bitList,
            int k,
            int[] data,
            List<PointDataEntity> pointDataList,
            PointListHolder pointListHolder) {
        CommonDataAndStatusVo commonDataAndStatusVo = new CommonDataAndStatusVo();
        // 事件返回值
        if (bitList != null && !bitList.isEmpty()) {
            // 存储事件状态列表
            List<EventCodeEntity> clusterEventList = new ArrayList<>();
            List<EventCodeEntity> eventCodeEntityList =
                    getEventList(bitList, k, data, clusterEventList, pointListHolder);
            commonDataAndStatusVo.setStatus(eventCodeEntityList);
        }
        if (pointDataList != null && !pointDataList.isEmpty()) {
            // 存储运行数据列表
            List<PointDataEntity> pointEntityList = new ArrayList<>();
            // 存储运行数据列表
            for (PointDataEntity pointDataEntityTemplate : pointDataList) {
                PointDataEntity pointDataEntity = new PointDataEntity();
                BeanUtils.copyProperties(pointDataEntityTemplate, pointDataEntity);
                int index = getIndex(k, pointDataEntity);
                String value =
                        getValue(
                                data,
                                index,
                                pointDataEntity.getPointType(),
                                pointDataEntity.getPointMul());
                pointDataEntity.setValue(value);
                pointEntityList.add(pointDataEntity);
            }
            commonDataAndStatusVo.setData(pointEntityList);
        }
        return commonDataAndStatusVo;
    }

    /** 获取电池簇运行状态 */
    @Override
    public Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryStatus(
            String deviceId) {
        int[] data = dataService.get(deviceId);
        if (data == null) {
            return null;
        }
        // 电池簇数量
        int cluster =
                data[pointListHolder.getBmsClusterNum(data[pointListHolder.getEmsTypeIndex()])];
        // 每簇电池组数量
        int stack = data[pointListHolder.getBmsStackNum(data[pointListHolder.getEmsTypeIndex()])];
        // 每组电芯数量
        int cell = data[pointListHolder.getBmsCellNum(data[pointListHolder.getEmsTypeIndex()])];
        // 每组电芯温度传感器数量
        int cellT = data[pointListHolder.getBmsCellTNum(data[pointListHolder.getEmsTypeIndex()])];
        Map<String, Map<String, Map<String, Map<String, Double>>>> resultMap = new HashMap<>(2);
        Map<String, Map<String, Map<String, Double>>> clusterVoltageMap = new HashMap<>(1000);
        Map<String, Map<String, Map<String, Double>>> clusterTemperatureMap = new HashMap<>(1000);
        int cellInitIndex =
                pointListHolder.getCellInitIndex(data[pointListHolder.getEmsTypeIndex()]);
        for (int i = 0; i < cluster; i++) {
            Map<String, Map<String, Double>> stackVoltageMap = new HashMap<>(stack);
            for (int j = 0; j < stack; j++) {
                Map<String, Double> bmuVoltageMap = new HashMap<>(cell);
                for (int k = 0; k < cell; k++) {
                    double value =
                            (double) data[cellInitIndex + k + (j) * cell + cell * stack * i] / 1000;
                    bmuVoltageMap.put("cell_" + k, value);
                }
                stackVoltageMap.put("stack_" + j, bmuVoltageMap);
            }
            clusterVoltageMap.put("cluster_" + i, stackVoltageMap);
        }
        resultMap.put("voltage", clusterVoltageMap);
        int cellIndex = cellInitIndex + cluster * stack * cell;
        for (int i = 0; i < cluster; i++) {
            Map<String, Map<String, Double>> stackTemperatureMap = new HashMap<>(stack);
            for (int j = 0; j < stack; j++) {
                Map<String, Double> bmuTemperatureMap = new HashMap<>(1000);
                for (int k = 0; k < cellT; k++) {
                    double value =
                            (double) data[cellIndex + k + (j) * cellT + cellT * stack * i] / 10;
                    bmuTemperatureMap.put("cell_" + k, value);
                }
                stackTemperatureMap.put("stack_" + j, bmuTemperatureMap);
            }
            clusterTemperatureMap.put("cluster" + i, stackTemperatureMap);
        }
        resultMap.put("temperature", clusterTemperatureMap);
        return resultMap;
    }

    /** 获取空调消防运行状态 */
    @Override
    public Map<String, Map<Integer, CommonDataAndStatusVo>> getAirFireStatus(String deviceId) {
        int[] data = dataService.get(deviceId);
        if (data == null) {
            return null;
        }
        // air数量
        int airNum = data[pointListHolder.getAirNum(data[pointListHolder.getEmsTypeIndex()])];
        // fire数量
        int fireNum = data[pointListHolder.getFireNum(data[pointListHolder.getEmsTypeIndex()])];
        // water数量
        int waterNum = data[pointListHolder.getWaterNum(data[pointListHolder.getEmsTypeIndex()])];
        // 查询出空调非状态位数据
        List<PointDataEntity> airPointList =
                pointListHolder.getAirDataPointList(data[pointListHolder.getEmsTypeIndex()]);

        List<PointDataEntity> waterPointList =
                pointListHolder.getWaterDataPointList(data[pointListHolder.getEmsTypeIndex()]);

        // 空调处理
        Map<Integer, CommonDataAndStatusVo> airResultMap = new HashMap<>(airNum);
        for (int k = 0; k < airNum; k++) {
            CommonDataAndStatusVo airMap =
                    getDataAndStatus(
                            pointListHolder.getAirBitPointList(
                                    data[pointListHolder.getEmsTypeIndex()]),
                            k,
                            data,
                            airPointList,
                            pointListHolder);
            int type =
                    data[
                            pointListHolder.getTypeCodeIndex(
                                    data[pointListHolder.getEmsTypeIndex()],
                                    TypeCodeIndexEnum.AIR.getCode(),
                                    k)];
            String typeCode = EmsEventCodeServiceImpl.DEVICE_TYPE_CODE_MAP.get(type);
            airMap.setTypeCode(typeCode);
            airResultMap.put(k, airMap);
        }
        // 水冷空调处理
        Map<Integer, CommonDataAndStatusVo> waterResultMap = new HashMap<>(waterNum);
        for (int k = 0; k < waterNum; k++) {
            CommonDataAndStatusVo waterMap =
                    getDataAndStatus(
                            pointListHolder.getWaterBitPointList(
                                    data[pointListHolder.getEmsTypeIndex()]),
                            k,
                            data,
                            waterPointList,
                            pointListHolder);
            int type =
                    data[
                            pointListHolder.getTypeCodeIndex(
                                    data[pointListHolder.getEmsTypeIndex()],
                                    TypeCodeIndexEnum.WATER_COOLER.getCode(),
                                    k)];
            String typeCode = EmsEventCodeServiceImpl.DEVICE_TYPE_CODE_MAP.get(type);
            waterMap.setTypeCode(typeCode);
            waterResultMap.put(k, waterMap);
        }
        // 消防处理
        Map<Integer, CommonDataAndStatusVo> firResultMap = new HashMap<>(fireNum);
        for (int k = 0; k < fireNum; k++) {
            CommonDataAndStatusVo fireMap =
                    getDataAndStatus(
                            pointListHolder.getFireBitPointList(
                                    data[pointListHolder.getEmsTypeIndex()]),
                            k,
                            data,
                            null,
                            pointListHolder);
            int type =
                    data[
                            pointListHolder.getTypeCodeIndex(
                                    data[pointListHolder.getEmsTypeIndex()],
                                    TypeCodeIndexEnum.FIRE.getCode(),
                                    k)];
            String typeCode = EmsEventCodeServiceImpl.DEVICE_TYPE_CODE_MAP.get(type);
            fireMap.setTypeCode(typeCode);
            firResultMap.put(k, fireMap);
        }
        Map<String, Map<Integer, CommonDataAndStatusVo>> resultMap = new HashMap<>(3);
        resultMap.put("air", airResultMap);
        resultMap.put("fire", firResultMap);
        resultMap.put("water", waterResultMap);
        return resultMap;
    }

    /** 获取可控设备运行状态 */
    @Override
    public MeterContentDataVo getControllableStatus(String projectId, String meterId) {
        MeterContentData meterContentData =
                dataService.collectMeterDataPoint(projectId).get(meterId);
        MeterContentDataVo meterContentDataVo = new MeterContentDataVo();
        AmmeterEntity ammeterEntity = ammeterService.getById(meterId);
        Optional.ofNullable(ammeterEntity)
                .ifPresent(
                        ammeter -> {
                            BeanUtils.copyProperties(meterContentData, meterContentDataVo);
                            List<MeterEventVo> list = getMeterStatus(ammeter, meterContentData);
                            meterContentDataVo.setMeterEventList(list);
                        });
        return meterContentDataVo;
    }

    List<MeterEventVo> getMeterStatus(
            AmmeterEntity ammeterEntity, MeterContentData meterContentData) {
        List<MeterEventVo> list = new ArrayList<>();
        List<MeterEventCodeEntity> meterCodeList =
                meterEventCodeService
                        .lambdaQuery()
                        .eq(MeterEventCodeEntity::getProjectId, ammeterEntity.getProjectId())
                        .eq(MeterEventCodeEntity::getDigital0Analog1Control2, 0)
                        .eq(MeterEventCodeEntity::getMeterType, ammeterEntity.getVendor())
                        .list();
        if (!meterCodeList.isEmpty()) {
            List<Integer> auxDigitalList = meterContentData.getAux_digital();
            Optional.ofNullable(auxDigitalList)
                    .ifPresent(
                            data -> {
                                if (!data.isEmpty()) {
                                    for (MeterEventCodeEntity meterEventCodeEntity :
                                            meterCodeList) {
                                        MeterEventVo meterEventVo = new MeterEventVo();
                                        BeanUtils.copyProperties(
                                                meterEventCodeEntity, meterEventVo);
                                        meterEventVo.setValue(
                                                (data.get(meterEventCodeEntity.getBitOffset())
                                                                & (1
                                                                        << meterEventCodeEntity
                                                                                .getBitValue()))
                                                        != 0);
                                        list.add(meterEventVo);
                                    }
                                }
                            });
        }
        return list;
    }

    @Override
    public Map<String, Map<String, Object>> getOneEmsRunStatus(
            String projectId, String deviceId, boolean checkPermission, boolean showMaintain)
            throws ExecutionException {
        return getEmsRunStatus(projectId, deviceId, checkPermission, showMaintain);
    }

    @Override
    public List<MeterEventCodeEntity> getControllableItem(String projectId, String meterId) {

        List<MeterEventCodeEntity> meterControllableList = new ArrayList<>();
        // 根据电表和projetId 获取电表的可空状态列表
        AmmeterEntity ammeterEntity = ammeterService.getById(meterId);
        if (ammeterEntity != null) {
            meterControllableList =
                    meterEventCodeService
                            .lambdaQuery()
                            .eq(MeterEventCodeEntity::getProjectId, projectId)
                            .eq(MeterEventCodeEntity::getDigital0Analog1Control2, 2)
                            .eq(MeterEventCodeEntity::getMeterType, ammeterEntity.getVendor())
                            .list();
        }
        return meterControllableList;
    }

    /** 控制操作 */
    @Override
    public void controllableActionPost(ControllableActionRequest controllableActionRequest) {
        log.info("controllableActionPost start request: {}", controllableActionRequest);
        // 发送 电表控制请求到 go
        JSONObject jsonObject;
        jsonObject =
                restGoUtils.sendPost(
                        new RestGoUtils.GoRestApi() {
                            @Override
                            public String api() {
                                return "/api/v1/meter_control";
                            }

                            @Override
                            public Object request() {
                                Map<String, Object> map = new HashMap<>(3);
                                map.put(
                                        "address",
                                        Integer.valueOf(controllableActionRequest.getAddress()));
                                map.put(
                                        "value",
                                        Integer.valueOf(controllableActionRequest.getValue()));
                                map.put("meter_uuid", controllableActionRequest.getMeterId());
                                return map;
                            }
                        },
                        JSONObject.class);
        assert jsonObject != null;
        boolean success = jsonObject.getBoolean("success");
        if (!success) {
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    @Override
    public List<MeterContentDataVo> realTimeMetersMonitor() {
        List<MeterContentDataVo> meterContentDataVos = new ArrayList<>();
        List<AmmeterEntity> ammeterEntities =
                ammeterService.list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                                .orderByAsc(AmmeterEntity::getOrderIndex)
                                .orderByAsc(AmmeterEntity::getCreateTime));
        Map<String, MeterContentData> meterContentDataMap =
                dataService.collectMeterDataPoint(WebUtils.projectId.get());
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            MeterContentDataVo meterContentDataVo =
                    realTimeMeter(ammeterEntity, meterContentDataMap);
            if (meterContentDataVo != null) {
                meterContentDataVos.add(meterContentDataVo);
            }
        }
        return meterContentDataVos;
    }

    @Override
    public MeterContentDataVo realTimeMeter(
            AmmeterEntity ammeterEntity, Map<String, MeterContentData> meterContentDataMap) {
        MeterContentData tempMeterContentData = meterContentDataMap.get(ammeterEntity.getId());
        MeterContentDataVo meterContentDataVo = null;
        if (tempMeterContentData != null) {
            meterContentDataVo = new MeterContentDataVo();
            BeanUtils.copyProperties(tempMeterContentData, meterContentDataVo);
            double acActivePower = meterContentDataVo.getAc_active_power() / 1000;
            meterContentDataVo.setAc_active_power(
                    ammeterEntity.getType() == 2 ? -acActivePower : acActivePower);
            List<Double> activePowers = meterContentDataVo.getAc_active_powers();
            meterContentDataVo.setAc_active_powers(
                    activePowers.stream()
                            .map(
                                    e -> {
                                        if (ammeterEntity.getType() == 2) {
                                            return -e / 1000;
                                        } else {
                                            return e / 1000;
                                        }
                                    })
                            .collect(Collectors.toList()));
            double acReactivePower = meterContentDataVo.getAc_reactive_power() / 1000;
            meterContentDataVo.setAc_reactive_power(
                    ammeterEntity.getType() == 2 ? -acReactivePower : acReactivePower);
            List<Double> reactivePowers = meterContentDataVo.getAc_reactive_powers();
            meterContentDataVo.setAc_reactive_powers(
                    reactivePowers.stream()
                            .map(
                                    e -> {
                                        if (ammeterEntity.getType() == 2) {
                                            return -e / 1000;
                                        } else {
                                            return e / 1000;
                                        }
                                    })
                            .collect(Collectors.toList()));
            double dcPower = meterContentDataVo.getDc_power() / 1000;
            meterContentDataVo.setDc_power(ammeterEntity.getType() == 2 ? -dcPower : dcPower);
            // 计算 功率因数
            Boolean dcMeter = ammeterEntity.getDcMeter();
            if (Boolean.FALSE.equals(dcMeter)) {
                DecimalFormat df = new DecimalFormat();
                df.setMaximumFractionDigits(2);
                List<Double> powerFactors = new ArrayList<>();
                // 交流电表 可以计算 功率因数
                int acSize = meterContentDataVo.getAc_active_powers().size();
                int reSize = meterContentDataVo.getAc_reactive_powers().size();
                for (int i = 0; i < 3; i++) {
                    boolean effecFlag = false;
                    if (i < acSize && i < reSize) {
                        // 计算 三相的功率因数
                        Double activeValue = meterContentDataVo.getAc_active_powers().get(i);
                        Double reactiveValue = meterContentDataVo.getAc_reactive_powers().get(i);
                        Double powerFactor = EmsUtil.getPowerFactor(activeValue, reactiveValue);
                        if (!powerFactor.isNaN()) {
                            effecFlag = true;
                            powerFactors.add(Double.valueOf(df.format(powerFactor)));
                        }
                    }
                    if (!effecFlag) {
                        powerFactors.add(0.0);
                    }
                }
                // 设置三相的功率因数 注意是List
                meterContentDataVo.setPowerFactors(powerFactors);
                // 计算 总的功率因数 注意是单个Doule
                Double activeValue = meterContentDataVo.getAc_active_power();
                Double reactiveValue = meterContentDataVo.getAc_reactive_power();
                Double powerFactor = EmsUtil.getPowerFactor(activeValue, reactiveValue);
                if (!powerFactor.isNaN()) {
                    meterContentDataVo.setPowerFactor(Double.valueOf(df.format(powerFactor)));
                } else {
                    meterContentDataVo.setPowerFactor(0.0);
                }
            }
            if (ammeterEntity.getType() == 2) {
                coverGridMeterVO(meterContentDataVo);
            }
            meterContentDataVo.setAmmeterBaseInfo(ammeterEntity);
            List<MeterEventVo> meterEventVoList =
                    getMeterStatus(ammeterEntity, tempMeterContentData);
            meterContentDataVo.setMeterEventList(meterEventVoList);

            //            meterContentDataVos.add(meterContentDataVo);
        }
        return meterContentDataVo;
    }

    private static void coverGridMeterVO(MeterContentDataVo meterContentDataVo) {
        // 反转正负总能量（交流部分）
        Double acHistoryPositivePowerInKwh =
                meterContentDataVo.getAc_history_positive_power_in_kwh();
        Double acHistoryNegativePowerInKwh =
                meterContentDataVo.getAc_history_negative_power_in_kwh();
        meterContentDataVo.setAc_history_negative_power_in_kwh(acHistoryPositivePowerInKwh);
        meterContentDataVo.setAc_history_positive_power_in_kwh(acHistoryNegativePowerInKwh);
        // 反转正负总能量（直流部分）
        Double dcHistoryNegativePowerInKwh =
                meterContentDataVo.getDc_history_negative_power_in_kwh();
        Double dcHistoryPositivePowerInKwh =
                meterContentDataVo.getDc_history_positive_power_in_kwh();
        meterContentDataVo.setDc_history_negative_power_in_kwh(dcHistoryPositivePowerInKwh);
        meterContentDataVo.setDc_history_positive_power_in_kwh(dcHistoryNegativePowerInKwh);
        // 交换其他属性
        ElectricityUsage tempElectricityUsage = meterContentDataVo.getPositiveElectricityUsage();
        meterContentDataVo.setPositiveElectricityUsage(
                meterContentDataVo.getNegativeElectricityUsage());
        meterContentDataVo.setNegativeElectricityUsage(tempElectricityUsage);

        // ** 因为前端取了反向需量，所以这边被迫不反 **/
        //        Double tempMaxActivePowerDemand =
        // meterContentDataVo.getMax_positive_active_power_demand();
        //        Long tempMaxActivePowerDemandTime =
        //                meterContentDataVo.getMax_positive_active_power_demand_happened_time();
        //        meterContentDataVo.setMax_positive_active_power_demand(
        //                meterContentDataVo.getMax_negative_active_power_demand());
        //        meterContentDataVo.setMax_positive_active_power_demand_happened_time(
        //                meterContentDataVo.getMax_negative_active_power_demand_happened_time());
        //        meterContentDataVo.setMax_negative_active_power_demand(tempMaxActivePowerDemand);
        //        meterContentDataVo.setMax_negative_active_power_demand_happened_time(
        //                tempMaxActivePowerDemandTime);
        //        Double tempMaxReactivePowerDemand =
        //                meterContentDataVo.getMax_positive_reactive_power_demand();
        //        Long tempMaxReactivePowerDemandTime =
        //                meterContentDataVo.getMax_positive_reactive_power_demand_happened_time();
        //        meterContentDataVo.setMax_positive_reactive_power_demand(
        //                meterContentDataVo.getMax_negative_reactive_power_demand());
        //        meterContentDataVo.setMax_positive_reactive_power_demand_happened_time(
        //                meterContentDataVo.getMax_negative_reactive_power_demand_happened_time());
        //
        // meterContentDataVo.setMax_negative_reactive_power_demand(tempMaxReactivePowerDemand);
        //        meterContentDataVo.setMax_negative_reactive_power_demand_happened_time(
        //                tempMaxReactivePowerDemandTime);
    }

    @Override
    public Map<String, Map<String, Object>> getRunStatus(
            String projectId, boolean showHide, boolean showMaintain) throws ExecutionException {
        Map<String, Map<String, Object>> resultMap =
                getEmsRunStatus(projectId, null, showHide, showMaintain);
        setMeterEvent(projectId, resultMap, showHide);
        List<CameraEntity> cameraEntities =
                cameraService.lambdaQuery().eq(CameraEntity::getProjectId, projectId).list();
        List<ControllableEntity> controllableEntities =
                controllableService
                        .lambdaQuery()
                        .eq(ControllableEntity::getProjectId, projectId)
                        .list();
        try {
            JSONObject jsonObject;
            ProjectEntity projectEntity = projectService.getById(projectId);
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(ControllerEntity::getProjectId, projectId));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/get_online", null, JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/get_online", null, JSONObject.class);
            }
            assert jsonObject != null;
            if (jsonObject.getJSONArray(InstallationEnum.CAMERA.getName()) != null) {
                List<StatusVo> cameraList =
                        jsonObject.getJSONArray("camera").toJavaList(StatusVo.class);
                Map<String, Boolean> cameraMap =
                        cameraList.stream()
                                .collect(Collectors.toMap(StatusVo::getUuid, StatusVo::getOnline));
                for (CameraEntity cameraEntity : cameraEntities) {
                    boolean status =
                            cameraMap.get(cameraEntity.getId()) != null
                                    && cameraMap.get(cameraEntity.getId());
                    Map<String, Object> statusMap = new HashMap<>(3);
                    statusMap.put("offline", status ? 0 : 1);
                    statusMap.put("name", cameraEntity.getName());
                    statusMap.put("type", "camera");
                    resultMap.put(cameraEntity.getId(), statusMap);
                }
            }
            if (jsonObject.getJSONArray(InstallationEnum.CONTROLLABLE.getName()) != null) {
                List<StatusVo> controllableList =
                        jsonObject.getJSONArray("controllable").toJavaList(StatusVo.class);
                Map<String, Boolean> controllableMap =
                        controllableList.stream()
                                .collect(Collectors.toMap(StatusVo::getUuid, StatusVo::getOnline));
                Map<String, SteerableContentData> steerableContentDataMap =
                        dataService.collectSteerableDataPoint(projectId);
                for (ControllableEntity controllableEntity : controllableEntities) {
                    boolean status =
                            controllableMap.get(controllableEntity.getId()) != null
                                    && controllableMap.get(controllableEntity.getId());
                    Map<String, Object> statusMap = new HashMap<>(3);
                    statusMap.put("offline", status ? 0 : 1);
                    statusMap.put("name", controllableEntity.getName());
                    statusMap.put("type", "controllable");
                    statusMap.put(
                            "status", steerableContentDataMap.get(controllableEntity.getId()));
                    resultMap.put(controllableEntity.getId(), statusMap);
                }
            }
        } catch (Exception e) {
            for (CameraEntity cameraEntity : cameraEntities) {
                Map<String, Object> statusMap = new HashMap<>(3);
                statusMap.put("offline", 1);
                statusMap.put("name", cameraEntity.getName());
                statusMap.put("type", "camera");
                resultMap.put(cameraEntity.getId(), statusMap);
            }
            for (ControllableEntity controllableEntity : controllableEntities) {
                Map<String, Object> statusMap = new HashMap<>(3);
                statusMap.put("offline", 1);
                statusMap.put("name", controllableEntity.getName());
                statusMap.put("type", "controllable");
                resultMap.put(controllableEntity.getId(), statusMap);
            }
        }

        return resultMap;
    }

    private void setMeterEvent(
            String projectId, Map<String, Map<String, Object>> resultMap, boolean showHide) {
        List<AmmeterEntity> ammeterEntities =
                ammeterService.lambdaQuery().eq(AmmeterEntity::getProjectId, projectId).list();
        if (ammeterEntities.isEmpty()) {
            return;
        }
        List<HideEventCodeEntity> hideEventCodeEntities =
                hideEventCodeService
                        .lambdaQuery()
                        .eq(HideEventCodeEntity::getProjectId, projectId)
                        .eq(
                                HideEventCodeEntity::getType,
                                // 只查询 测控的
                                String.valueOf(HideCodeLabelEnums.METER_DEVICE.getLabel()))
                        .list();
        List<String> meterHideEventKey =
                hideEventCodeEntities.stream()
                        //                        .filter(
                        //                                e ->
                        //
                        // String.valueOf(HideCodeLabelEnums.METER_DEVICE.getLabel())
                        //                                                .equals(e.getType()))
                        .map(HideEventCodeEntity::getEventCode)
                        .collect(Collectors.toList());
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            if (Boolean.TRUE.equals(ammeterEntity.getMaintain())
                    && !SecurityUtil.hasAuthority(EmsConstants.DEVICE_MAINTAIN_AUTHORITY)) {
                continue;
            }
            Map<Integer, Map<String, Object>> meterResultMap = new HashMap<>(1);
            List<MeterEventVo> alarmList = new ArrayList<>();
            List<MeterEventVo> faultList = new ArrayList<>();
            MeterContentData meterContentData =
                    dataService
                            .collectMeterDataPoint(WebUtils.projectId.get())
                            .get(ammeterEntity.getId());
            Map<String, Object> eventMap = new HashMap<>(4);
            Map<String, Object> statusMap = new HashMap<>(3);
            statusMap.put(
                    "offline",
                    meterContentData == null ? 1 : (meterContentData.getOnline() ? 0 : 1));
            statusMap.put("name", ammeterEntity.getName());
            statusMap.put("type", "meter");
            resultMap.put(ammeterEntity.getId(), statusMap);
            if (Boolean.TRUE.equals(ammeterEntity.getControllable())) {
                if (meterContentData == null) {
                    continue;
                }
                List<MeterEventCodeEntity> meterCodeList =
                        meterEventCodeService
                                .lambdaQuery()
                                .eq(MeterEventCodeEntity::getProjectId, WebUtils.projectId.get())
                                .eq(MeterEventCodeEntity::getDigital0Analog1Control2, 0)
                                .ne(
                                        MeterEventCodeEntity::getEventLevel,
                                        EventLevelEnum.STATE.getLevel())
                                .eq(MeterEventCodeEntity::getMeterType, ammeterEntity.getVendor())
                                .list();
                if (meterCodeList.isEmpty()) {
                    continue;
                }
                List<Integer> auxDigitalList = meterContentData.getAux_digital();
                if (auxDigitalList == null || auxDigitalList.isEmpty()) {
                    continue;
                }
                for (MeterEventCodeEntity meterEventCodeEntity : meterCodeList) {
                    if (!showHide
                            && meterHideEventKey.contains(meterEventCodeEntity.getEventCode())) {
                        continue;
                    }
                    MeterEventVo meterEventVo = new MeterEventVo();
                    BeanUtils.copyProperties(meterEventCodeEntity, meterEventVo);
                    try {
                        if ((auxDigitalList.get(meterEventCodeEntity.getBitOffset())
                                        & (1 << meterEventCodeEntity.getBitValue()))
                                != 0) {
                            meterEventVo.setValue(true);
                            if (EventLevelEnum.FAULT
                                    .getLevel()
                                    .equals(meterEventCodeEntity.getEventLevel())) {
                                faultList.add(meterEventVo);
                            } else if (EventLevelEnum.ALARM
                                    .getLevel()
                                    .equals(meterEventCodeEntity.getEventLevel())) {
                                alarmList.add(meterEventVo);
                            }
                        }

                    } catch (Exception e) {
                        log.error(
                                "error: meter {} index:{} ,auxDigitalList {}",
                                meterEventCodeEntity.getBitOffset(),
                                ammeterEntity.getId(),
                                auxDigitalList);
                    }
                }
            }
            eventMap.put("alarm", alarmList);
            eventMap.put("alarm_count", alarmList.size());
            eventMap.put("fault", faultList);
            eventMap.put("fault_count", faultList.size());
            meterResultMap.put(0, eventMap);
            statusMap.put("meter", meterResultMap);
        }
    }

    public Map<String, Map<String, Object>> getEmsRunStatus(
            String projectId, String deviceId, boolean showHide, boolean showMaintain)
            throws ExecutionException {
        List<DeviceEntity> deviceEntities;
        if (deviceId == null) {
            deviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getProjectId, projectId)
                            .eq(DeviceEntity::getUnreal, false)
                            .orderByAsc(DeviceEntity::getCreateTime)
                            .list();
        } else {
            deviceEntities =
                    deviceService.list(
                            Wrappers.lambdaQuery(DeviceEntity.class)
                                    .eq(DeviceEntity::getProjectId, projectId)
                                    .eq(DeviceEntity::getId, deviceId)
                                    .orderByAsc(DeviceEntity::getCreateTime));
        }
        Map<String, Map<String, Object>> resultMap = new HashMap<>(deviceEntities.size());
        // TODO 优化 目前 直接这方法加了 synchronized 为了控制下面这大段逻辑 能够同步 否则可能多个线程调用的时候  serialNumbers
        // 这个列表会导致重复添加了 多次 误导致
        // conflict 冲突了  , 线上问题 看了一圈 只能是这里
        List<String> serialNumbers = Collections.synchronizedList(new ArrayList<>());
        for (DeviceEntity deviceEntity : deviceEntities) {
            if (Boolean.TRUE.equals(deviceEntity.getMaintain()) && !showMaintain) {
                continue;
            }
            setEmsRunningStatus(projectId, resultMap, serialNumbers, deviceEntity, showHide);
        }
        List<String> duplicateNumbers = EmsUtil.findDuplicateNumbers(serialNumbers);
        resultMap.forEach(
                (k, v) -> {
                    if (duplicateNumbers.contains((String) v.get(SERIAL))) {
                        v.put("snConflict", 1);
                    } else {
                        v.put("snConflict", 0);
                    }
                });
        return resultMap;
    }

    @Override
    public Pair<Integer, Integer> setEmsRunningStatus(
            String projectId,
            Map<String, Map<String, Object>> resultMap,
            List<String> serialNumbers,
            DeviceEntity deviceEntity,
            boolean showHide)
            throws ExecutionException {
        Map<String, Object> statusMap = new HashMap<>(9);
        int[] data = dataService.get(deviceEntity.getId(), projectId);
        Pair<Integer, Integer> faultAndAlarmPair = Pair.of(0, 0);
        // 3次之内可能网络波动
        statusMap.put("offline", 0);
        statusMap.put("name", deviceEntity.getName());
        statusMap.put("type", "ems");
        if (data == null) {
            int count = CACHE.get(deviceEntity.getId());
            count++;
            if (count >= EmsConstants.CACHE_EMS_RUNNING_STATUS_TIME - 1) {
                // 离线
                statusMap.put("offline", 1);
            }
            CACHE.put(deviceEntity.getId(), count);
            Map<Object, Object> deviceInfo =
                    redisTemplate.opsForHash().entries(DEVICE + deviceEntity.getId());
            if (deviceInfo.get(SERIAL) != null) {
                statusMap.put(SERIAL, deviceInfo.get(SERIAL));
                serialNumbers.add((String) deviceInfo.get(SERIAL));
            }
            resultMap.put(deviceEntity.getId(), statusMap);
            return Pair.of(0, 0);
        }
        int emsType = data[42];
        StringBuilder value = new StringBuilder();
        for (int i = 0; i < pointListHolder.getEmsSerialLength(emsType); i++) {
            if (data[pointListHolder.getEmsSerial(emsType) + i] == 0) {
                continue;
            }
            value.append((char) (data[pointListHolder.getEmsSerial(emsType) + i] >> 8));
            value.append((char) (data[pointListHolder.getEmsSerial(emsType) + i] & 0xFF));
        }
        String emsSerial = value.toString();
        if (StringUtils.hasLength(emsSerial)) {
            redisTemplate.opsForHash().put(DEVICE + deviceEntity.getId(), SERIAL, emsSerial);
            redisTemplate.expire(DEVICE + deviceEntity.getId(), 30, TimeUnit.DAYS);
        }
        statusMap.put(SERIAL, emsSerial);
        serialNumbers.add(emsSerial);
        CACHE.put(deviceEntity.getId(), 0);
        // air数量
        int airNum = data[pointListHolder.getAirNum(data[pointListHolder.getEmsTypeIndex()])];
        // fire数量
        int fireNum = data[pointListHolder.getFireNum(data[pointListHolder.getEmsTypeIndex()])];
        // cluster点位
        int clusterNum =
                data[pointListHolder.getBmsClusterNum(data[pointListHolder.getEmsTypeIndex()])];
        // pcs数量
        int pcsNum = data[pointListHolder.getPcsNum(data[pointListHolder.getEmsTypeIndex()])];
        // 水冷数量
        int waterNum = data[pointListHolder.getWaterNum(data[pointListHolder.getEmsTypeIndex()])];
        // dcdc数量
        int dcdcNum = data[pointListHolder.getDcDcNum(data[pointListHolder.getEmsTypeIndex()])];
        // 状态机数量
        int statesNum = data[pointListHolder.getStatesNum(data[pointListHolder.getEmsTypeIndex()])];
        // 空调处理
        Map<Integer, Map<String, Object>> airResultMap = new HashMap<>(4);

        List<String> emsHideEventKey = hideEventCodeService.getHideAllTypeEventCodeList(projectId);
        faultAndAlarmPair =
                setFaultAndAlarmToResultMap(
                        emsHideEventKey,
                        pointListHolder.getAirBitPointList(data[pointListHolder.getEmsTypeIndex()]),
                        data,
                        airNum,
                        airResultMap,
                        faultAndAlarmPair,
                        showHide);
        // 水冷空调处理water
        Map<Integer, Map<String, Object>> waterResultMap = new HashMap<>(4);
        faultAndAlarmPair =
                setFaultAndAlarmToResultMap(
                        emsHideEventKey,
                        pointListHolder.getWaterBitPointList(
                                data[pointListHolder.getEmsTypeIndex()]),
                        data,
                        waterNum,
                        waterResultMap,
                        faultAndAlarmPair,
                        showHide);
        // 消防处理处理
        Map<Integer, Map<String, Object>> firResultMap = new HashMap<>(4);
        faultAndAlarmPair =
                setFaultAndAlarmToResultMap(
                        emsHideEventKey,
                        pointListHolder.getFireBitPointList(
                                data[pointListHolder.getEmsTypeIndex()]),
                        data,
                        fireNum,
                        firResultMap,
                        faultAndAlarmPair,
                        showHide);
        // DCDC 处理
        Map<Integer, Map<String, Object>> dcdcResultMap = new HashMap<>(4);
        faultAndAlarmPair =
                setFaultAndAlarmToResultMap(
                        emsHideEventKey,
                        pointListHolder.getDcdcBitPointList(
                                data[pointListHolder.getEmsTypeIndex()]),
                        data,
                        dcdcNum,
                        dcdcResultMap,
                        faultAndAlarmPair,
                        showHide);
        // 查询BMS点位
        Map<Object, Map<String, Object>> bmsResultMap = new HashMap<>(clusterNum + 1);
        // BMS公共存储事件状态列表
        Map<String, Object> bmsCommonMap = new HashMap<>(4);
        List<EventCodeEntity> bmsCommonAlarmList = new ArrayList<>();
        List<EventCodeEntity> bmsCommonFaultList = new ArrayList<>();
        setFaultAndAlarmList(
                emsHideEventKey,
                pointListHolder.getBmsCommonBitList(data[pointListHolder.getEmsTypeIndex()]),
                null,
                data,
                bmsCommonAlarmList,
                bmsCommonFaultList,
                showHide);
        bmsCommonMap.put("alarm", bmsCommonAlarmList);
        bmsCommonMap.put("alarm_count", bmsCommonAlarmList.size());
        bmsCommonMap.put("fault", bmsCommonFaultList);
        bmsCommonMap.put("fault_count", bmsCommonFaultList.size());
        bmsResultMap.put("common", bmsCommonMap);
        faultAndAlarmPair =
                Pair.of(
                        faultAndAlarmPair.getFirst() + bmsCommonFaultList.size(),
                        faultAndAlarmPair.getSecond() + bmsCommonAlarmList.size());
        // 单个BMS存储事件状态列表'
        faultAndAlarmPair =
                setFaultAndAlarmToResultMapWithIndex(
                        emsHideEventKey,
                        pointListHolder.getBmsClusterBitPointList(
                                data[pointListHolder.getEmsTypeIndex()]),
                        data,
                        clusterNum,
                        bmsResultMap,
                        faultAndAlarmPair,
                        showHide);
        // statesNum
        Map<Object, Map<String, Object>> statesResultMap = new HashMap<>(statesNum + 1);
        faultAndAlarmPair =
                setFaultAndAlarmToResultMapWithIndex(
                        emsHideEventKey,
                        pointListHolder.getStatesBitPointList(
                                data[pointListHolder.getEmsTypeIndex()]),
                        data,
                        statesNum,
                        statesResultMap,
                        faultAndAlarmPair,
                        showHide);
        // 查询pcs点位
        Map<Integer, Map<String, Object>> pcsResultMap = new HashMap<>(4);
        faultAndAlarmPair =
                setFaultAndAlarmToResultMap(
                        emsHideEventKey,
                        pointListHolder.getPcsBitPointList(data[pointListHolder.getEmsTypeIndex()]),
                        data,
                        pcsNum,
                        pcsResultMap,
                        faultAndAlarmPair,
                        showHide);
        // 查询系统点位
        Map<Integer, Map<String, Object>> systemResultMap = new HashMap<>(1);
        // 事件返回值
        Map<String, Object> sysMap = new HashMap<>(4);
        // 存储事件状态列表
        List<EventCodeEntity> alarmList = new ArrayList<>();
        List<EventCodeEntity> faultList = new ArrayList<>();
        setFaultAndAlarmList(
                emsHideEventKey,
                pointListHolder.getSystemBitPointList(data[pointListHolder.getEmsTypeIndex()]),
                null,
                data,
                alarmList,
                faultList,
                showHide);
        sysMap.put("alarm", alarmList);
        sysMap.put("alarm_count", alarmList.size());
        sysMap.put("fault", faultList);
        sysMap.put("fault_count", faultList.size());
        systemResultMap.put(0, sysMap);
        faultAndAlarmPair =
                Pair.of(
                        faultAndAlarmPair.getFirst() + faultList.size(),
                        faultAndAlarmPair.getSecond() + alarmList.size());
        statusMap.put("system", systemResultMap);
        statusMap.put("air", airResultMap);
        statusMap.put("fire", firResultMap);
        statusMap.put("bms", bmsResultMap);
        statusMap.put("pcs", pcsResultMap);
        statusMap.put("water", waterResultMap);
        statusMap.put("dcdc", dcdcResultMap);
        statusMap.put("states", statesResultMap);
        resultMap.put(deviceEntity.getId(), statusMap);
        return faultAndAlarmPair;
    }

    /**
     * 系统监控去查询 关于 容量告警的记录
     *
     * @param projectId : projectId
     * @return : 告警记录 按照分组
     */
    @Override
    public List<CapacityAlarmRecordEntity> getCapacityAlarmStatus(String projectId) {
        // 处理一下 容量告警
        // 获取到所有开启了容量告警的分组
        List<GroupEntity> groupEntities = groupService.queryCapacityGroup(projectId);
        // 过滤了没开 容量告警提醒的
        groupEntities =
                groupEntities.stream()
                        .filter(group -> Boolean.TRUE.equals(group.getCapacityRemindController()))
                        .collect(Collectors.toList());
        Map<String, GroupEntity> groupEntityMap =
                groupEntities.stream().collect(Collectors.toMap(GroupEntity::getId, v -> v));
        List<String> groupEnableCapacityIds =
                groupEntities.stream().map(GroupEntity::getId).collect(Collectors.toList());
        if (!groupEnableCapacityIds.isEmpty()) {
            // 这里可以改成并发去按照 projectId 和 groupId去查询最新的 一条数据 然后过滤type=0的返回, 这里用一个sql解决了 但是explain 后发现
            // 性能可能还不如并发去查询 再说吧 who want optmized code  who do it
            List<CapacityAlarmRecordEntity> alarmRecords =
                    capacityAlarmRecordService.getLatestRecordsUpByGroups(
                            projectId, groupEnableCapacityIds);
            alarmRecords.forEach(
                    record -> {
                        GroupEntity group = groupEntityMap.get(record.getGroupId());
                        if (group != null) {
                            record.setGroupName(group.getName());
                        }
                    });
            return alarmRecords;
        }
        return List.of();
    }

    @Override
    public Map<String, List<Map<String, Object>>> getHookUpData() {
        List<DeviceEntity> deviceEntities =
                deviceService.list(
                        Wrappers.lambdaQuery(DeviceEntity.class)
                                .eq(DeviceEntity::getProjectId, WebUtils.projectId.get()));
        List<Map<String, Object>> emsInfos = new ArrayList<>();
        List<Map<String, Object>> meterInfos = new ArrayList<>();
        for (DeviceEntity deviceEntity : deviceEntities) {
            Map<String, Object> emsInfo = new HashMap<>();
            emsInfo.put("id", deviceEntity.getId());
            emsInfo.put("name", deviceEntity.getName());

            realTimeUtils.getData(
                    WebUtils.projectId.get(),
                    deviceEntity.getId(),
                    new ProtocolDataHandler() {
                        @Override
                        public void common(@NotNull IProtocolData data) {
                            Map<String, Object> fieldValuesByPointColumns =
                                    defaultRealTimeResolve.fixedResolve(
                                            data,
                                            pointListHolder
                                                    .getFixedPointList(data.vendorType())
                                                    .stream()
                                                    .map(PointDataEntity::getPointColumn)
                                                    .collect(Collectors.toList()),
                                            true);
                            Object val =
                                    fieldValuesByPointColumns.get(
                                            StringUtil.underlineToCamel(
                                                    EmsFieldEnum.EMS_AC_ACTIVE_POWER.field()));
                            double point =
                                    (val instanceof Number) ? ((Number) val).doubleValue() : 0.0;
                            double emsOutPower = 0;
                            double emsInPower = 0;
                            if (point > 0) {
                                // 放电
                                emsOutPower += point;
                            } else {
                                // 充电
                                emsInPower += Math.abs(point);
                            }
                            double diffPower = emsOutPower - emsInPower;
                            emsInfo.put("power", diffPower);
                            emsInfo.putAll(fieldValuesByPointColumns);
                            List<PointDataEntity> pcsDataPointList =
                                    pointListHolder.getPcsDataPointList(data.vendorType());
                            List<PointDataEntity> bmsClusterDataPointList =
                                    pointListHolder.getBmsClusterDataPointList(data.vendorType());
                            emsInfo.put(
                                    "pcsInfos",
                                    defaultRealTimeResolve.pcsIResolve(
                                            data,
                                            pcsDataPointList.stream()
                                                    .map(PointDataEntity::getPointColumn)
                                                    .collect(Collectors.toList()),
                                            null));
                            emsInfo.put(
                                    "bmsClusterInfos",
                                    defaultRealTimeResolve.bmsIResolve(
                                            data,
                                            bmsClusterDataPointList.stream()
                                                    .map(PointDataEntity::getPointColumn)
                                                    .collect(Collectors.toList()),
                                            null));
                        }
                    });
            emsInfos.add(emsInfo);
        }

        // TODO 迁移到 realtime-service 服务上去 通过realtimeUtils 去查询
        Map<String, MeterContentData> meterMap =
                dataService.collectMeterDataPoint(WebUtils.projectId.get());
        List<AmmeterEntity> ammeterEntities =
                ammeterService.list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get()));
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            MeterContentData meterContentData = meterMap.get(ammeterEntity.getId());
            Map<String, Object> meterInfo = new HashMap<>();
            if (meterContentData != null) {
                meterInfo = ReflectionUtils.meterFieldToCamelMap(meterContentData);
                meterInfo.put("id", ammeterEntity.getId());
                meterInfo.put("name", ammeterEntity.getName());
            }
            //            Map<String, Object> meterInfo = new HashMap<>();
            //            Double acCurrent = meterContentData.getAc_current();
            //            List<Double> acCurrents = meterContentData.getAc_currents();
            //            List<Double> acVoltages = meterContentData.getAc_voltages();
            //            Double acVoltage = meterContentData.getAc_voltage();
            //            meterInfo.put("acCurrent", acCurrent);
            //            meterInfo.put("acVoltage", acVoltage);
            //            meterInfo.put("acCurrents", acCurrents);
            //            meterInfo.put("acVoltages", acVoltages);
            //            meterInfo.put("id", ammeterEntity.getId());
            //            meterInfo.put("name", ammeterEntity.getName());
            meterInfos.add(meterInfo);
        }
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        result.put("ems_list", emsInfos);
        result.put("meter_list", meterInfos);

        return result;
    }

    private Pair<Integer, Integer> setFaultAndAlarmToResultMapWithIndex(
            List<String> emsHideKey,
            List<PointDataEntity> bmsBitPointList,
            int[] data,
            int num,
            Map<Object, Map<String, Object>> resultMap,
            Pair<Integer, Integer> faultAndAlarmPair,
            boolean notShowHide) {
        int fault = faultAndAlarmPair.getFirst();
        int alarm = faultAndAlarmPair.getSecond();
        for (int index = 0; index < num; index++) {
            // 事件返回值
            Map<String, Object> clusterMap = new HashMap<>(4);
            // 存储事件状态列表
            List<EventCodeEntity> alarmList = new ArrayList<>();
            List<EventCodeEntity> faultList = new ArrayList<>();
            setFaultAndAlarmList(
                    emsHideKey, bmsBitPointList, index, data, alarmList, faultList, notShowHide);
            clusterMap.put("alarm", alarmList);
            clusterMap.put("alarm_count", alarmList.size());
            alarm = alarm + alarmList.size();
            clusterMap.put("fault", faultList);
            clusterMap.put("fault_count", faultList.size());
            fault = fault + faultList.size();
            resultMap.put(index, clusterMap);
        }
        return Pair.of(fault, alarm);
    }

    private Pair<Integer, Integer> setFaultAndAlarmToResultMap(
            List<String> emsHideEventKey,
            List<PointDataEntity> pointDataEntities,
            int[] data,
            int num,
            Map<Integer, Map<String, Object>> resultMap,
            Pair<Integer, Integer> faultAndAlarmPair,
            boolean showHide) {
        int fault = faultAndAlarmPair.getFirst();
        int alarm = faultAndAlarmPair.getSecond();
        for (int index = 0; index < num; index++) {
            // 事件返回值
            Map<String, Object> map = new HashMap<>(4);
            // 存储事件状态列表
            List<EventCodeEntity> alarmList = new ArrayList<>();
            List<EventCodeEntity> faultList = new ArrayList<>();
            setFaultAndAlarmList(
                    emsHideEventKey,
                    pointDataEntities,
                    index,
                    data,
                    alarmList,
                    faultList,
                    showHide);
            map.put("alarm", alarmList);
            map.put("alarm_count", alarmList.size());
            alarm = alarm + alarmList.size();
            map.put("fault", faultList);
            map.put("fault_count", faultList.size());
            resultMap.put(index, map);
            fault = fault + faultList.size();
        }
        return Pair.of(fault, alarm);
    }

    /** 解析故障和告警状态 */
    public void setFaultAndAlarmList(
            List<String> emsHideEventKey,
            List<PointDataEntity> bitPointList,
            Integer index,
            int[] data,
            List<EventCodeEntity> alarmList,
            List<EventCodeEntity> faultList,
            boolean showHide) {
        for (PointDataEntity pointDataEntityTemplate : bitPointList) {
            PointDataEntity pointDataEntity = new PointDataEntity();
            BeanUtils.copyProperties(pointDataEntityTemplate, pointDataEntity);
            int typeCode =
                    data[
                            pointListHolder.getTypeCodeIndex(
                                    data[pointListHolder.getEmsTypeIndex()],
                                    pointDataEntity.getPointColumn(),
                                    index)];
            Map<Integer, EventCodeEntity> eventBitMap =
                    DataEventListener.emsEventCodeMap.get(
                            typeCode + "_" + pointDataEntity.getPointColumn());
            // 该点位暂未使用
            if (eventBitMap == null) {
                continue;
            }
            int address;
            if (index == null) {
                address =
                        pointDataEntity.getPointAddress()
                                + Integer.parseInt(pointDataEntity.getPointOffset());
            } else {
                address = getIndex(index, pointDataEntity);
            }
            for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
                if (eventBitMap.get(i) == null) {
                    continue;
                }
                EventCodeEntity eventCodeEntityTemplate = eventBitMap.get(i);
                if (!showHide && emsHideEventKey.contains(eventCodeEntityTemplate.getEventCode())) {
                    continue;
                }
                EventCodeEntity eventCodeEntity = new EventCodeEntity();
                BeanUtils.copyProperties(eventCodeEntityTemplate, eventCodeEntity);
                EventCodeLanguageServiceImpl.transEventCodeForLanguage(eventCodeEntity);
                // (data[index] & (1 << i)) == 0 ? 0 : 1) 判断当前点位值是否是1
                if (eventCodeEntity.getBitStand() == ((data[address] & (1 << i)) == 0 ? 0 : 1)) {
                    eventCodeEntity.setValue(true);
                    if (eventCodeEntity.getEventLevel().equals(EventLevelEnum.FAULT.getLevel())) {
                        faultList.add(eventCodeEntity);
                    } else if (eventCodeEntity
                            .getEventLevel()
                            .equals(EventLevelEnum.ALARM.getLevel())) {
                        alarmList.add(eventCodeEntity);
                    }
                }
            }
        }
    }

    /** 解析事件状态 */
    public static List<EventCodeEntity> getEventList(
            List<PointDataEntity> bitPointList,
            Integer k,
            int[] data,
            List<EventCodeEntity> eventCodeEntityList,
            PointListHolder pointListHolder) {
        for (PointDataEntity pointDataEntityTemplate : bitPointList) {
            PointDataEntity pointDataEntity = new PointDataEntity();
            BeanUtils.copyProperties(pointDataEntityTemplate, pointDataEntity);
            // 0代表system
            int typeCode =
                    data[
                            pointListHolder.getTypeCodeIndex(
                                    data[pointListHolder.getEmsTypeIndex()],
                                    pointDataEntity.getPointColumn(),
                                    k)];
            Map<Integer, EventCodeEntity> eventBitMap =
                    DataEventListener.emsEventCodeMap.get(
                            typeCode + "_" + pointDataEntity.getPointColumn());
            Optional.ofNullable(eventBitMap)
                    .ifPresent(
                            bitMap -> {
                                int index;
                                if (k == null) {
                                    index =
                                            pointDataEntity.getPointAddress()
                                                    + Integer.parseInt(
                                                            pointDataEntity.getPointOffset());
                                } else {
                                    index = getIndex(k, pointDataEntity);
                                }
                                for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
                                    int finalI = i;
                                    Optional.ofNullable(eventBitMap.get(i))
                                            .ifPresent(
                                                    e -> {
                                                        EventCodeEntity eventCodeEntity;
                                                        try {
                                                            eventCodeEntity =
                                                                    (EventCodeEntity)
                                                                            eventBitMap
                                                                                    .get(finalI)
                                                                                    .clone();
                                                        } catch (
                                                                CloneNotSupportedException
                                                                        exception) {
                                                            throw new RuntimeException(exception);
                                                        }
                                                        EventCodeLanguageServiceImpl
                                                                .transEventCodeForLanguage(
                                                                        eventCodeEntity);
                                                        // (data[index] & (1 << i)) == 0 ? 0 : 1)
                                                        // 判断当前点位值是否是1
                                                        // 这里的意思就是 读取BIT类型的 index点位数据 -> 是一个Uint16位置
                                                        // , 根据 finalI 偏移 可以得到 具体的位的值
                                                        // 根据得到的值和数据库里的 这个位的标准值 bit_stand 作对比
                                                        // 如果是一样,就是这个event是触发或者正常, 如果不一样就是不正常
                                                        // 或者未开启或者之类的意思
                                                        eventCodeEntity.setValue(
                                                                eventCodeEntity.getBitStand()
                                                                        == ((data[index]
                                                                                                & (1
                                                                                                        << finalI))
                                                                                        == 0
                                                                                ? 0
                                                                                : 1));
                                                        eventCodeEntityList.add(eventCodeEntity);
                                                    });
                                }
                            });
        }
        return eventCodeEntityList;
    }

    /** 带*的column解析点位值 */
    public static int getIndex(int k, PointDataEntity pointDataEntity) {
        String offset = pointDataEntity.getPointOffset();
        // 初始位置
        int addressInit = pointDataEntity.getPointAddress();
        // 计算出offset的起点位置
        int offsetStart = Integer.parseInt(offset.substring(0, offset.indexOf("+")).trim());
        // 计算出offset的倍数
        int multiple = Integer.parseInt(offset.substring(offset.indexOf("*") + 1).trim());
        return addressInit + offsetStart + k * multiple;
    }

    /** 根据不同类型，计算出点位值 */
    public static String getValue(int[] data, int index, String pointType, Integer mul) {
        StringBuilder value = new StringBuilder();
        switch (Objects.requireNonNull(PointDataType.getByDesc(pointType))) {
            case ASCII:
                // 只有是采集asc的表，才插入数据，否则不插入数据
                for (int i = 0; i < mul; i++) {
                    if (data[index + i] == 0) {
                        continue;
                    }
                    value.append((char) (data[index + i] >> 8));
                    value.append((char) (data[index + i] & 0xFF));
                }
                value = new StringBuilder(value.toString().trim());
                break;
            case UINT16:
                double d1 = (double) data[index] / mul;
                value = new StringBuilder(String.format("%.2f", d1));
                break;
            case INT16:
                double d2 = (float) ((short) data[index]) / mul;
                value = new StringBuilder(String.format("%.2f", d2));
                break;
            case INT32:
                int high = data[index];
                int low = data[index + 1];
                int temp = (high << 16) + low;
                double d3 = (double) temp / mul;
                value = new StringBuilder(String.format("%.2f", d3));
                break;
            case UINT32:
                int highU = data[index];
                int lowU = data[index + 1];
                long tempU = ((long) highU << 16) + lowU;
                double d4 = (double) tempU / mul;
                value = new StringBuilder(String.format("%.2f", d4));
                break;
            default:
        }
        return value.toString();
    }
}
