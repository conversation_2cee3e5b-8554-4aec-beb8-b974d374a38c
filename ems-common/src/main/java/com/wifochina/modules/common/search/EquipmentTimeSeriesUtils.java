package com.wifochina.modules.common.search;

import lombok.Data;

/**
 * Created on 2024/7/26 11:12.
 *
 * <AUTHOR>
 */
@Data
public class EquipmentTimeSeriesUtils {

    public static DefaultRateQueryEngine rateQueryEngine;

    public static DefaultDifferenceQueryEngine differenceQueryEngine;

    public static synchronized void setRateQueryEngine(DefaultRateQueryEngine rateQueryEngine) {
        if (EquipmentTimeSeriesUtils.rateQueryEngine == null) {
            EquipmentTimeSeriesUtils.rateQueryEngine = rateQueryEngine;
        }
    }

    public static synchronized void setDifferenceQueryEngine(
            DefaultDifferenceQueryEngine differenceQueryEngine) {
        if (EquipmentTimeSeriesUtils.differenceQueryEngine == null) {
            EquipmentTimeSeriesUtils.differenceQueryEngine = differenceQueryEngine;
        }
    }
}
