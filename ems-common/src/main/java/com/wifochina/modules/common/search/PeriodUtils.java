package com.wifochina.modules.common.search;

import com.wifochina.modules.client.InfluxClientService;

/**
 * Created on 2024/7/26 13:55.
 *
 * <AUTHOR>
 */
public class PeriodUtils {
    public static InfluxClientService influxClientService;

    public static long period5(long start, long end, Long periodParams, String bucket) {
        return period(start,end,periodParams,bucket) * 5;
    }

    public static long period(long start, long end, Long periodParams, String bucket) {
        long period;
        if (periodParams != null) {
            period = periodParams;
            return period;
        }
        // 待确认 以前有个 -240 现在去掉了 和 孙勇商量后
        //        LocalDateTime startDate =

        //                LocalDateTime.ofEpochSecond(requestWithGroupId.getStartDate(), 0,
        // ZoneOffset.UTC);
        //        LocalDateTime endDate =
        //                LocalDateTime.ofEpochSecond(
        //                        requestWithGroupId.getEndDate() - 240, 0, ZoneOffset.UTC);
        //        // 开始时间不晚于结束时间，佛则返回时间错误异常
        //        ServiceAssert.isTrue(!endDate.isBefore(startDate),
        // ErrorResultCode.TIME_OVERLAPPED.value());

        // 相差的天数
        long periodTime = (end - start) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        period = (periodTime / 2 + 1);
        if (influxClientService.getBucketMean().equals(bucket)) {
            period = (periodTime / 2 + 1);
        }
        /**
         * long periodTime = (requestWithGroupId.getEndDate() - requestWithGroupId.getStartDate()) /
         * 86400; // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推 long period = (periodTime / 2 + 1);
         * if (requestWithGroupId.getPeriod() != null) { period = requestWithGroupId.getPeriod(); }l
         */
        return period;
    }
}
