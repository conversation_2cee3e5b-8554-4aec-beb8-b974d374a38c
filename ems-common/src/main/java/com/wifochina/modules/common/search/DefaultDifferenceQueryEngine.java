package com.wifochina.modules.common.search;

import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * Created on 2025/2/8 11:35.
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DefaultDifferenceQueryEngine extends AbstractDifferenceQuery {
    private final FluxCommonSearchService fluxCommonSearchService;

    @Override
    public Map<String, Double> getRangeDifferenceEquipmentsMap(
            TimeSeriesQuerySource source,
            FluxRateCommonHolder fluxRateCommonHolder,
            String field,
            EquipmentQuerySupplier equipmentQuerySupplier) {
        checkValid(source);
        ProjectEntity project = source.getProject();
        // 按照设备id查询influx db ，避免查询到数据需要反复判断deviceId来决定数据归属
        EquipmentQuerySupplierEnums equipmentQuerySupplierEnums =
                equipmentQuerySupplier.equipmentQuerySupplierType();
        List<String> supplier = equipmentQuerySupplier.supplier();
        if (CollectionUtils.isEmpty(supplier)) {
            return Map.of();
        }
        FluxCommonSearchService.FluxSearchContext fluxSearchContext =
                new FluxCommonSearchService.FluxSearchContext().setProjectEntity(project);
        // 支持 根据 ids 传递进来 和  比如 电表的类型传递进来 类型
        if (equipmentQuerySupplierEnums
                .name()
                .equals(EquipmentQuerySupplierEnums.EQUIPMENT_IDS_SUPPLIER.name())) {
            fluxSearchContext.setEquipmentIds(equipmentQuerySupplier.supplier());
        }
        if (equipmentQuerySupplierEnums
                .name()
                .equals(EquipmentQuerySupplierEnums.EQUIPMENT_TYPES_SUPPLIER.name())) {
            fluxSearchContext.setEquipmentTypes(equipmentQuerySupplier.supplier());
        }
        fluxSearchContext.setFilterSearch(
                new FluxCommonSearchService.FluxFilterSearch()
                        .setBucket(source.getBucket())
                        .setMeasurement(source.getMeasurement())
                        .setStart(fluxRateCommonHolder.getStartDate())
                        .setEnd(fluxRateCommonHolder.getEndDate())
                        .setFields(List.of(field)));
        return fluxCommonSearchService.mapRangeDifference(fluxSearchContext);
    }
}
