package com.wifochina.modules.common.search;

import cn.hutool.core.collection.CollectionUtil;

import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.influx.FluxAdapter;
import com.wifochina.common.influx.FluxBuilder;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.diagram.VO.AggregationEquipmentMap;
import com.wifochina.modules.diagram.VO.EquipmentMap;
import com.wifochina.modules.diagram.VO.ValueVO;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created on 2024/7/26 10:08.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class SimpleFluxCommonSearchImpl implements FluxCommonSearchService {
    private final InfluxClientService influxClientService;

    private static final Map<ChronoUnit, Long> UNIT_TO_SECONDS = new HashMap<>();

    static {
        UNIT_TO_SECONDS.put(ChronoUnit.SECONDS, 1L);
        UNIT_TO_SECONDS.put(ChronoUnit.MINUTES, 60L);
        UNIT_TO_SECONDS.put(ChronoUnit.HOURS, 3600L);
        UNIT_TO_SECONDS.put(ChronoUnit.DAYS, 86400L);
        // 可以继续添加其他单位
    }

    /**
     * 聚合类的 查询 返回所有的equipment聚合后的 field 作为key的 数据 并且包装到 AggregationEquipmentMap 对象中
     *
     * @param context : FluxSearchContext
     * @return : EquipmentMap
     */
    @Override
    public AggregationEquipmentMap mapAggregation(FluxSearchContext context) {
        FluxFilterSearch filterSearch = context.getFilterSearch();
        long period = filterSearch.getPeriod();
        String bucket = filterSearch.getBucket();
        // 特殊处理一下 EndTime时间
        specialHandlerEndTime(filterSearch, bucket);
        FluxAdapter.InfluxPrepareStage prepareStage =
                FluxAdapter.query(
                                fluxBuilder ->
                                        // 这里查询的是 聚合sql
                                        getAggregationSql(
                                                fluxBuilder, context, filterSearch, period))
                        .decimal(2);
        FluxAdapter.InfluxResultStage influxResultStage;
        if (influxClientService.getBucketMean().equals(bucket)) {
            // 如果是 mean的 bucket 需要丢掉第一条并且 做时间偏移操作
            influxResultStage =
                    prepareStage
                            .dropFirst()
                            // a2024-08-19 17:30:10 add 时间偏移2倍 让从00:00开始
                            .timeOffset(
                                    2
                                            * (-period
                                                    * UNIT_TO_SECONDS.get(
                                                            filterSearch.getChronoUnit())))
                            .handleResult();
        } else if (influxClientService.getBucketForever().equals(filterSearch.getBucket())) {
            // 需量希望往前移2个时间点
            if (filterSearch
                    .getFields()
                    .contains(MeterFieldEnum.MAX_NEGATIVE_ACTIVE_POWER_DEMAND.field())) {
                influxResultStage =
                        prepareStage
                                .timeOffset(
                                        -2
                                                * period
                                                * UNIT_TO_SECONDS.get(filterSearch.getChronoUnit()))
                                .handleResult();
            } else {
                // forever 库 不需要drop 第一行 并且时间偏移 不需要2倍
                influxResultStage =
                        prepareStage
                                .timeOffset(
                                        -period * UNIT_TO_SECONDS.get(filterSearch.getChronoUnit()))
                                .handleResult();
            }
        } else {
            influxResultStage = prepareStage.handleResult();
        }
        return new AggregationEquipmentMap().setMap(influxResultStage.toMap());
    }

    @NotNull
    private static String getAggregationSql(
            FluxBuilder fluxBuilder,
            FluxSearchContext context,
            FluxFilterSearch filterSearch,
            long period) {
        return fluxBuilder
                .project(context.getProjectEntity())
                .timeZoneSet(filterSearch.needSetTimeZone())
                .from(filterSearch.getBucket())
                .range(filterSearch.getStart(), filterSearch.getEnd())
                .measurement(filterSearch.getMeasurement())
                .equipmentIds(context.getEquipmentIds())
                .fields(filterSearch.getFields())
                .toFlux()
                .aggregateWindow(period, filterSearch.getChronoUnit(), filterSearch.getFunc())
                .withCreateEmpty(false)
                .groupBy(List.of(EmsConstants.INFLUX_TIME, EmsConstants.INFLUX_FIELD))
                .sum()
                .toString();
    }

    /**
     * 非聚合类的 查询 返回以equipmentId 作为key 的 数据 并且包装到 EquipmentMap对象中
     *
     * @param context : FluxSearchContext
     * @return : EquipmentMap
     */
    @Override
    public EquipmentMap map(FluxSearchContext context) {
        Map<String, Map<String, List<ValueVO>>> result = new HashMap<>();
        FluxFilterSearch filterSearch = context.getFilterSearch();
        long period = filterSearch.getPeriod();
        // 聚合equipment 的 列表 查询
        List<String> equipmentIds = context.getEquipmentIds();
        specialHandlerEndTime(filterSearch, filterSearch.getBucket());
        FluxAdapter.InfluxPrepareStage prepareStage =
                FluxAdapter.query(fluxBuilder -> getSql(fluxBuilder, context, filterSearch, period))
                        // 保留2位小数
                        .decimal(2);
        FluxAdapter.InfluxResultStage influxResultStage;
        if (influxClientService.getBucketMean().equals(filterSearch.getBucket())) {
            // 如果是 mean的 bucket 需要丢掉第一条并且 做时间偏移操作
            influxResultStage =
                    prepareStage
                            .dropFirst()
                            .timeOffset(
                                    2
                                            * (-period
                                                    * UNIT_TO_SECONDS.get(
                                                            filterSearch.getChronoUnit())))
                            .handleResult();
        } else if (influxClientService.getBucketForever().equals(filterSearch.getBucket())) {
            // forever 库 不需要drop 第一行 并且时间偏移 不需要2倍
            influxResultStage =
                    prepareStage
                            .timeOffset(-period * UNIT_TO_SECONDS.get(filterSearch.getChronoUnit()))
                            .handleResult();
        } else {
            influxResultStage = prepareStage.handleResult();
        }
        equipmentIds.forEach(
                equipmentId -> {
                    Map<String, List<ValueVO>> map = influxResultStage.toMap(equipmentId);
                    result.put(equipmentId, map);
                });
        return new EquipmentMap().setMap(result);
    }

    /**
     * 因为 forever 库因为时间偏移 导致 比如只有58这个点 mean库导致只有 57 这个点 + 1 后就能让 forever 到59 mean库到58,
     * 那么mean库再特殊处理加一个单位的end 时间 就让数据查询到 第二天的01这个点 -2分钟 就是 59了
     *
     * <p>see timeOffset
     *
     * @param filterSearch
     */
    private void specialHandlerEndTime(FluxFilterSearch filterSearch, String filterSearch1) {
        filterSearch.setEnd(filterSearch.getEnd() + 1);
        if (influxClientService.getBucketMean().equals(filterSearch1)) {
            // mean库需要再加一个 单位
            filterSearch.setEnd(
                    filterSearch.getEnd()
                            + filterSearch.getPeriod()
                                    * filterSearch.getChronoUnit().getDuration().getSeconds());
        }
    }

    @NotNull
    private static String getSql(
            FluxBuilder fluxBuilder,
            FluxSearchContext context,
            FluxFilterSearch filterSearch,
            long period) {
        return fluxBuilder
                .project(context.getProjectEntity())
                .timeZoneSet(filterSearch.needSetTimeZone())
                .from(filterSearch.getBucket())
                .range(filterSearch.getStart(), filterSearch.getEnd())
                .measurement(filterSearch.getMeasurement())
                .equipmentIds(context.getEquipmentIds())
                .fields(filterSearch.getFields())
                .toFlux()
                .aggregateWindow(period, filterSearch.getChronoUnit(), filterSearch.getFunc())
                .withCreateEmpty(false)
                .toString();
    }

    /**
     * 非聚合类的 查询 返回以equipmentId 作为key 的 数据 并且包装到 EquipmentMap对象中
     *
     * @param context : FluxSearchContext
     * @return : EquipmentMap
     */
    @Override
    public Map<Long, Double> mapDifference(FluxSearchContext context) {
        FluxFilterSearch filterSearch = context.getFilterSearch();
        long period = filterSearch.getPeriod();

        FluxAdapter.InfluxPrepareStage prepareStage =
                FluxAdapter.query(
                                fluxBuilder ->
                                        filterSearch.getChronoUnit().equals(ChronoUnit.MONTHS)
                                                ? getDifferenceSqlYear(
                                                        fluxBuilder, context, filterSearch)
                                                : getDifferenceSql(
                                                        fluxBuilder, context, filterSearch, period))
                        // 保留2位小数
                        .decimal(2);
        FluxAdapter.InfluxResultStage influxResultStage =
                filterSearch.getChronoUnit().equals(ChronoUnit.MONTHS)
                        ? prepareStage.handleResult()
                        : prepareStage
                                .timeOffset(
                                        -period
                                                * 2
                                                * filterSearch
                                                        .getChronoUnit()
                                                        .getDuration()
                                                        .getSeconds())
                                .handleResult();
        return influxResultStage.toDouble();
    }

    /**
     * 只查询一个range 范围内的 差值 会根据 key 是 电表or设备id 作为key, 对应每个设备的 range的差值,并且只支持一个属性
     *
     * @param context :FluxSearchContext
     * @return : key: 电表or设备id , value : 差值
     */
    @Override
    public Map<String, Double> mapRangeDifference(FluxSearchContext context) {
        FluxFilterSearch filterSearch = context.getFilterSearch();
        boolean equipmentIdsFlag;
        if (CollectionUtil.isEmpty(context.getEquipmentIds())
                && CollectionUtil.isEmpty(context.getEquipmentTypes())) {
            log.error(
                    "mapRangeDifference No valid equipmentIds or equipmentTypes params are provided , projectId:{} , field:{}",
                    context.getProjectEntity().getId(),
                    filterSearch.getFields());
            return Map.of();
        }
        equipmentIdsFlag = CollectionUtil.isNotEmpty(context.getEquipmentIds());
        FluxAdapter.InfluxPrepareStage prepareStage =
                FluxAdapter.query(
                                fluxBuilder -> {
                                    FluxBuilder.InfluxQueryFilterStage filterStage =
                                            fluxBuilder
                                                    .projectId(context.getProjectEntity().getId())
                                                    .timeZoneSet(true)
                                                    .from(filterSearch.getBucket())
                                                    .range(
                                                            filterSearch.getStart(),
                                                            filterSearch.getEnd())
                                                    .measurement(filterSearch.getMeasurement())
                                                    .fields(filterSearch.getFields());
                                    if (equipmentIdsFlag) {
                                        filterStage.equipmentIds(context.getEquipmentIds());
                                    } else {
                                        filterStage.equipmentTypes(context.getEquipmentTypes());
                                    }
                                    return filterStage.toFlux().difference().sum().toString();
                                })
                        // 保留2位小数
                        .decimal(4);
        return prepareStage.handleResult().toMapEachEquipmentOnlyValue().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getValue()));
    }

    /**
     * import "timezone" option location = timezone.fixed(offset: 28800s)
     * from(bucket:"ems_forever_dev") |> range(start:1721874439, stop:1722479239) |> filter(fn: (r)
     * => r["_measurement"] == "ems100@28689") |> filter(fn: (r) => r["projectId"] ==
     * "4f537620d37d40e19dd25be5ca6ad941") |> filter(fn: (r) => (r["deviceId"] ==
     * "5f206dfafef5452fa16dba6d2f1ec6ca")) |> filter(fn: (r) => (r["_field"] ==
     * "ems_history_input_energy")) |> aggregateWindow(every:1h, fn:first, createEmpty:false) |>
     * difference() |> group(columns:["_time"]) |> sum()
     */
    @NotNull
    private static String getDifferenceSql(
            FluxBuilder fluxBuilder,
            FluxSearchContext context,
            FluxFilterSearch filterSearch,
            long period) {
        return fluxBuilder
                .project(context.getProjectEntity())
                .timeZoneSet(true)
                .from(filterSearch.getBucket())
                .range(filterSearch.getStart(), filterSearch.getEnd())
                .measurement(filterSearch.getMeasurement())
                .equipmentIds(context.getEquipmentIds())
                .fields(filterSearch.getFields())
                .toFlux()
                .aggregateWindow(period, filterSearch.getChronoUnit(), filterSearch.getFunc())
                .withCreateEmpty(false)
                .difference()
                .groupBy(EmsConstants.INFLUX_TIME)
                .sum()
                .toString();
    }

    @NotNull
    private static String getDifferenceSqlYear(
            FluxBuilder fluxBuilder, FluxSearchContext context, FluxFilterSearch filterSearch) {
        return fluxBuilder
                .project(context.getProjectEntity())
                .timeZoneSet(false)
                .from(filterSearch.getBucket())
                .range(filterSearch.getStart(), filterSearch.getEnd())
                .measurement(filterSearch.getMeasurement())
                .equipmentIds(context.getEquipmentIds())
                .fields(filterSearch.getFields())
                .toFlux()
                .timeShift(
                        MyTimeUtil.getOffSetHourByZoneCode(
                                context.getProjectEntity().getTimezone()),
                        ChronoUnit.HOURS)
                .aggregateWindow(1L, ChronoUnit.MONTHS, filterSearch.getFunc())
                .timeShift(-2L, ChronoUnit.MONTHS)
                .timeShift(
                        -MyTimeUtil.getOffSetHourByZoneCode(
                                context.getProjectEntity().getTimezone()),
                        ChronoUnit.HOURS)
                .difference()
                .groupBy(EmsConstants.INFLUX_TIME)
                .sum()
                .toString();
    }
}
