package com.wifochina.modules.common.search;

import com.wifochina.modules.diagram.VO.AggregationEquipmentList;
import com.wifochina.modules.diagram.VO.AggregationEquipmentMap;
import com.wifochina.modules.diagram.VO.EquipmentMap;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;

import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * Created on 2024/7/26 11:25.
 *
 * <AUTHOR>
 */
public abstract class AbstractRateQuery {

    public abstract EquipmentMap getRateMap(
            String bucket,
            String measurement,
            FluxRateCommonHolder fluxRateCommonHolder,
            List<String> fields,
            Supplier<List<String>> supplier);

    public abstract AggregationEquipmentMap getRateAggregationMap(
            String bucket,
            String measurement,
            FluxRateCommonHolder fluxRateCommonHolder,
            List<String> fields,
            Supplier<List<String>> supplier);

    public abstract AggregationEquipmentList getRateAggregationList(
            String bucket,
            String measurement,
            FluxRateCommonHolder fluxRateCommonHolder,
            String filed,
            Supplier<List<String>> supplier);

    public abstract Map<Long, Double> getDifferenceMap(
            String bucket,
            String measurement,
            FluxRateCommonHolder fluxRateCommonHolder,
            List<String> fields,
            Supplier<List<String>> supplier);
}
