package com.wifochina.modules.common.search;

import com.wifochina.modules.diagram.VO.AggregationEquipmentList;
import com.wifochina.modules.diagram.VO.AggregationEquipmentMap;
import com.wifochina.modules.diagram.VO.EquipmentMap;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * Created on 2024/7/26 11:19.
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DefaultRateQueryEngine extends AbstractRateQuery {

    private final FluxCommonSearchService fluxCommonSearchService;
    private final ProjectService projectService;

    @Override
    public AggregationEquipmentMap getRateAggregationMap(
            String bucket,
            String measurement,
            FluxRateCommonHolder fluxRateCommonHolder,
            List<String> fields,
            Supplier<List<String>> supplier) {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 按照设备id查询influx db ，避免查询到数据需要反复判断deviceId来决定数据归属
        List<String> deviceIds = supplier.get();
        if (CollectionUtils.isEmpty(deviceIds)) {
            // 防止调用的地方 取这个属性 得到 null 然后空指针 , 这里至少保证
            Map<String, List<ValueVO>> emptyMap = new HashMap<>();
            for (String field : fields) {
                emptyMap.put(field, new ArrayList<>());
            }
            return new AggregationEquipmentMap().setMap(emptyMap);
        }
        long period =
                PeriodUtils.period(
                        fluxRateCommonHolder.getStartDate(),
                        fluxRateCommonHolder.getEndDate(),
                        fluxRateCommonHolder.getPeriod(),
                        bucket);
        return fluxCommonSearchService.mapAggregation(
                new FluxCommonSearchService.FluxSearchContext()
                        .setProjectEntity(projectEntity)
                        .setEquipmentIds(deviceIds)
                        .setFilterSearch(
                                new FluxCommonSearchService.FluxFilterSearch()
                                        .setBucket(bucket)
                                        .setStart(fluxRateCommonHolder.getStartDate())
                                        .setEnd(fluxRateCommonHolder.getEndDate())
                                        .setMeasurement(measurement)
                                        .setFields(fields)
                                        .setPeriod(period)));
    }

    @Override
    public AggregationEquipmentList getRateAggregationList(
            String bucket,
            String measurement,
            FluxRateCommonHolder fluxRateCommonHolder,
            String filed,
            Supplier<List<String>> supplier) {
        AggregationEquipmentMap rateAggregationMap =
                getRateAggregationMap(
                        bucket, measurement, fluxRateCommonHolder, List.of(filed), supplier);

        return rateAggregationMap.getMap().containsKey(filed)
                ? new AggregationEquipmentList().setList(rateAggregationMap.getMap().get(filed))
                : new AggregationEquipmentList().setList(List.of());
    }

    @Override
    public EquipmentMap getRateMap(
            String bucket,
            String measurement,
            FluxRateCommonHolder fluxRateCommonHolder,
            List<String> fields,
            Supplier<List<String>> deivceIdsSupplier) {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        if (projectEntity == null) {
            return new EquipmentMap().setMap(Map.of());
        }
        // 按照设备id查询influx db ，避免查询到数据需要反复判断deviceId来决定数据归属
        List<String> deviceIds = deivceIdsSupplier.get();
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new EquipmentMap().setMap(Map.of());
        }
        long period =
                PeriodUtils.period(
                        fluxRateCommonHolder.getStartDate(),
                        fluxRateCommonHolder.getEndDate(),
                        fluxRateCommonHolder.getPeriod(),
                        bucket);
        return fluxCommonSearchService.map(
                new FluxCommonSearchService.FluxSearchContext()
                        .setProjectEntity(projectEntity)
                        .setEquipmentIds(deviceIds)
                        .setFilterSearch(
                                new FluxCommonSearchService.FluxFilterSearch()
                                        .setBucket(bucket)
                                        .setStart(fluxRateCommonHolder.getStartDate())
                                        .setEnd(fluxRateCommonHolder.getEndDate())
                                        .setMeasurement(measurement)
                                        .setFields(fields)
                                        .setPeriod(period)));
    }

    @Override
    public Map<Long, Double> getDifferenceMap(
            String bucket,
            String measurement,
            FluxRateCommonHolder fluxRateCommonHolder,
            List<String> fields,
            Supplier<List<String>> supplier) {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 按照设备id查询influx db ，避免查询到数据需要反复判断deviceId来决定数据归属
        List<String> equipmentIds = supplier.get();
        if (CollectionUtils.isEmpty(equipmentIds)) {
            return Map.of();
        }
        return fluxCommonSearchService.mapDifference(
                new FluxCommonSearchService.FluxSearchContext()
                        .setProjectEntity(projectEntity)
                        .setEquipmentIds(equipmentIds)
                        .setFilterSearch(
                                new FluxCommonSearchService.FluxFilterSearch()
                                        .setBucket(bucket)
                                        .setStart(fluxRateCommonHolder.getStartDate())
                                        .setEnd(fluxRateCommonHolder.getEndDate())
                                        .setMeasurement(measurement)
                                        .setFields(fields)
                                        .setPeriod(fluxRateCommonHolder.getPeriod())
                                        .setFunc("first")
                                        .setChronoUnit(fluxRateCommonHolder.getChronoUnit())));
    }
}
