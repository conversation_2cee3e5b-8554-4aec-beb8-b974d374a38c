package com.wifochina.modules.common.search;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;

import java.util.Map;

/**
 * Created on 2025/2/8 11:32.
 *
 * <AUTHOR>
 */
public abstract class AbstractDifferenceQuery {

    /**
     * 查询 某个范围内的差值, 注意: 这个差值是 传入的某个范围内的差值 即这个 范围内的 last()-first()的差值, 和AbstractRateQuery.
     * getDifferenceMap 不同,曲线的getDifferenceMap是查询大Range范围内根据聚合窗口得到的 每个窗口的 差值, 而这个函数的 我只需要查询某个 range
     * 范围内的差值, 上面的函数用于 如 热力图 把一天按照小时的时间窗口聚合得到差值, 而次函数的用于比如查询 pv/wind等 根据尖峰平谷的时间段去查询 电量差值 ,
     * 因为尖峰平谷时间段不是具体的每个小时 所以无法聚合窗口
     *
     * <p>此函数的范围值 key 是 设备Id value 是 这个设备的 range内的差值 暂时只支持一个属性 如果需要把这些设备比如这些设备是一个类型都是PV电表 , 希望得到这个
     * PV类型电表的 range内的差值合计, 在FluxCommonSearchService 里面处理一下即可 或者 类似把sql 改成 group即可如下
     *
     * <p>from(bucket:"ems_forever") |> range(start:1738857600, stop:1738886460) |> filter(fn: (r)
     * => r["_measurement"] == "meter@7265") |> filter(fn: (r) => (r["_field"] ==
     * "ac_history_positive_power_in_kwh" and r["projectId"] == "8721f6f53b954e75a38ed08a0c288cc1"
     * and r["type"] == "1")) <br>
     * |> difference() <br>
     * |> group(columns: []) // 去掉按 meterId 分组, 这个在这个方法里先不加,为了可能需要 按照每个电表id 或者设备id 去得到差值的情况 <br>
     * |> sum()
     *
     * <p>此方法是因为 重构 PV/WIND/余热发电 等 可再生能源 的 收益计算 , 需要计算尖峰平谷 的电量 , 考虑到可能会有其他场景也会用到
     *
     * <p>只需要在查询 某个range范围内的差值即可用这个方法
     *
     * @return
     */
    public abstract Map<String, Double> getRangeDifferenceEquipmentsMap(
            TimeSeriesQuerySource timeSeriesQuerySource,
            FluxRateCommonHolder fluxRateCommonHolder,
            String field,
            EquipmentQuerySupplier equipmentQuerySupplier);

    public void checkValid(TimeSeriesQuerySource timeSeriesQuerySource) {
        if (StringUtil.isEmpty(timeSeriesQuerySource.getBucket())
                || StringUtil.isEmpty(timeSeriesQuerySource.getMeasurement())
                || StringUtil.isEmpty(timeSeriesQuerySource.getProject())) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }
}
