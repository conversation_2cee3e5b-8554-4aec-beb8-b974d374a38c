package com.wifochina.modules.common.search;

import com.wifochina.modules.diagram.VO.AggregationEquipmentMap;
import com.wifochina.modules.diagram.VO.EquipmentMap;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

/**
 * Created on 2024/7/26 09:51.
 *
 * <AUTHOR>
 */
public interface FluxCommonSearchService {

    AggregationEquipmentMap mapAggregation(FluxSearchContext context);

    EquipmentMap map(FluxSearchContext context);

    Map<Long, Double> mapDifference(FluxSearchContext context);

    /**
     * 只查询一个range 范围内的 差值 会根据 key 是 电表or设备id 作为key, 对应每个设备的 range的差值,并且只支持一个属性
     *
     *
     * @param context :FluxSearchContext
     * @return : key: 电表or设备id , value : 差值
     */
    Map<String, Double> mapRangeDifference(FluxSearchContext context);

    @Data
    @Accessors(chain = true)
    class FluxSearchContext {
        private ProjectEntity projectEntity;
        private List<String> equipmentIds;
        private List<String> equipmentTypes;
        private FluxFilterSearch filterSearch;
    }

    @Data
    @Accessors(chain = true)
    class FluxFilterSearch {

        private String bucket;
        private String measurement;
        private long start;
        private long end;
        private String func = "last";
        private ChronoUnit chronoUnit = ChronoUnit.MINUTES;
        private long period;
        private List<String> fields;

        public boolean needSetTimeZone() {
            return this.getChronoUnit().equals(ChronoUnit.DAYS)
                    || this.getChronoUnit().equals(ChronoUnit.MONTHS);
        }
    }
}
