package com.wifochina.modules.alarm.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * 告警配置请求对象
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@ApiModel(value = "告警配置请求")
public class AlarmConfigRequest {

    @ApiModelProperty(value = "主键ID(更新时必填)")
    private Long id;

    @ApiModelProperty(value = "告警内容")
    @Range(min = 0, max = 5, message = "告警内容不存在")
    private Integer alarmContent;

    @ApiModelProperty(value = "是否启用(0:禁用 1:启用)")
    private Boolean isEnabled;

    @ApiModelProperty(value = "收益偏差系数")
    private Float profitDeviationCoefficient;

    @ApiModelProperty(value = "停机时长阈值(分钟)")
    private Integer downtimeThreshold;

    @ApiModelProperty(value = "离线时长阈值（分钟）")
    private Integer offlineTimeThreshold;

    @ApiModelProperty(value = "效率提醒阈值")
    private Float efficiencyReminderThreshold;
}
