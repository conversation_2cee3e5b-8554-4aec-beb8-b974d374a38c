package com.wifochina.modules.alarm.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * 批量删除告警通知配置请求对象
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@ApiModel(value = "批量删除告警通知配置请求")
public class BatchDeleteAlarmNotificationRequest {

    @ApiModelProperty(value = "项目ID列表", required = true)
    private List<String> projectIds;

    @ApiModelProperty(value = "要删除的用户名称")
    private String userName;

    @ApiModelProperty(value = "要删除的手机号")
    private String phone;

    @ApiModelProperty(value = "要删除的邮箱")
    private String email;
}