package com.wifochina.modules.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;

import java.util.List;

/**
 * 告警配置 服务类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface AlarmConfigService extends IService<AlarmConfigEntity> {

    /**
     * 根据项目ID查询告警配置列表
     *
     * @param projectId 项目ID
     * @return 告警配置列表
     */
    List<AlarmConfigEntity> getByProjectId(String projectId);

    /**
     * 根据项目ID和告警内容查询告警配置
     *
     * @param projectId 项目ID
     * @param alarmContent 告警内容
     * @return 告警配置
     */
    AlarmConfigEntity getByProjectIdAndAlarmContent(String projectId, Integer alarmContent);

    /**
     * 新增|更新告警配置
     *
     * @param alarmConfig 告警配置
     * @return 是否成功
     */
    void switchAlarmConfig(AlarmConfigEntity alarmConfig);

    /**
     * 更新告警配置
     *
     * @param alarmConfig 告警配置
     * @param oldAlarmConfig 旧告警配置
     * @return 是否成功
     */
    void updateAlarmConfig(
            AlarmConfigEntity alarmConfig,
            AlarmConfigEntity oldAlarmConfig,
            String subGroupName,
            String alertTypeName);

    /**
     * 新增告警配置
     *
     * @param alarmConfig 告警配置
     * @return 是否成功
     */
    void addAlarmConfig(AlarmConfigEntity alarmConfig,
                        String subGroupName);
}
