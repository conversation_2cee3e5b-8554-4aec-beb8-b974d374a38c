package com.wifochina.modules.alarm.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * 批量告警通知配置请求对象
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@ApiModel(value = "批量告警通知配置请求")
public class BatchAlarmNotificationRequest {

    @ApiModelProperty(value = "项目ID列表", required = true)
    private List<String> projectIds;

    // ========== 告警配置相关字段 ==========
    @ApiModelProperty(value = "告警内容")
    private Integer alarmContent;

    @ApiModelProperty(value = "是否启用(0:禁用 1:启用)")
    private Boolean isEnabled;

    @ApiModelProperty(value = "收益偏差系数")
    private Float profitDeviationCoefficient;

    @ApiModelProperty(value = "停机时长阈值(分钟)")
    private Integer downtimeThreshold;

    @ApiModelProperty(value = "离线时长阈值（分钟）")
    private Integer offlineTimeThreshold;

    @ApiModelProperty(value = "效率提醒阈值")
    private Float efficiencyReminderThreshold;

    // ========== 告警通知配置相关字段 ==========
    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "告警通知方式(0:邮箱,1:短信,2:电话)")
    private String notificationType;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;
}
