package com.wifochina.modules.alarm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.alert.client.AlertApiClient;
import com.weihengtech.alert.model.dto.AlertRuleDTO;
import com.weihengtech.alert.model.dto.AlertTypeDTO;
import com.weihengtech.alert.model.dto.AlertTypeGroupDTO;
import com.weihengtech.alert.model.dto.RestrainRuleDTO;
import com.weihengtech.alert.model.dto.TypeGroupDTO;
import com.weihengtech.alert.model.entity.AttributeMatchFirstRule;
import com.weihengtech.alert.model.entity.AttributeMatchRule;
import com.weihengtech.alert.model.enums.CompareTypeEnum;
import com.weihengtech.alert.model.enums.EffectiveType;
import com.weihengtech.alert.model.vo.R;
import com.wifochina.common.config.AccountSystemConfiguration;
import com.wifochina.common.config.AlertConfiguration;
import com.wifochina.common.constants.AlarmConstants;
import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmTemplatesConstants;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.alarm.mapper.AlarmConfigMapper;
import com.wifochina.modules.alarm.service.AlarmConfigService;

import com.wifochina.modules.project.service.ProjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

/**
 * 告警配置 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class AlarmConfigServiceImpl extends ServiceImpl<AlarmConfigMapper, AlarmConfigEntity>
        implements AlarmConfigService {

    @Resource private AlertConfiguration alertConfiguration;
    @Resource private AlertApiClient alertApiClient;
    @Resource private AccountSystemConfiguration accountSystemConfiguration;
    @Resource private ProjectService projectService;

    @Override
    public List<AlarmConfigEntity> getByProjectId(String projectId) {
        LambdaQueryWrapper<AlarmConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmConfigEntity::getProjectId, projectId);
        queryWrapper.orderByDesc(AlarmConfigEntity::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public AlarmConfigEntity getByProjectIdAndAlarmContent(String projectId, Integer alarmContent) {
        LambdaQueryWrapper<AlarmConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(AlarmConfigEntity::getProjectId, projectId)
                .eq(AlarmConfigEntity::getAlarmContent, alarmContent);
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void switchAlarmConfig(AlarmConfigEntity alarmConfig) {
        String subGroupName = projectService.getById(alarmConfig.getProjectId()).getProjectName();
        String alertTypeName = AlarmContentEnum.getName(alarmConfig.getAlarmContent());
        // 根据项目ID和告警内容查询是否已存在该配置
        if (alarmConfig.getId() != null) {
            AlarmConfigEntity existsOne = getById(alarmConfig.getId());
            updateAlarmConfig(alarmConfig, existsOne, subGroupName, alertTypeName);
        } else {
            // 检查是否已存在相同项目ID和告警内容的配置
            AlarmConfigEntity existingConfig =
                    getByProjectIdAndAlarmContent(
                            alarmConfig.getProjectId(), alarmConfig.getAlarmContent());
            if (existingConfig != null) {
                throw new ServiceException(
                        ErrorResultCode.CUSTOM_MSG_ERROR.value(), "已存在相同告警内容的配置");
            }
            addAlarmConfig(alarmConfig, subGroupName);
        }
    }

    @Override
    public void updateAlarmConfig(
            AlarmConfigEntity alarmConfig,
            AlarmConfigEntity existsOne,
            String subGroupName,
            String alertTypeName) {
        if (existsOne == null) {
            throw new ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), "不存在该告警内容的配置");
        }
        if (existsOne.getIsEnabled() && !alarmConfig.getIsEnabled()) {
            // 关闭告警 - 更新告警规则的alerting状态为false
            updateAlertRuleAlerting(
                    AlarmConstants.buildAlertRuleName(subGroupName, alertTypeName), false);
        } else if (!existsOne.getIsEnabled() && alarmConfig.getIsEnabled()) {
            // 开启告警 - 更新告警规则的alerting状态为true
            updateAlertRuleAlerting(
                    AlarmConstants.buildAlertRuleName(subGroupName, alertTypeName), true);
        }
        this.updateById(alarmConfig);
    }

    @Override
    public void addAlarmConfig(AlarmConfigEntity alarmConfig,
                               String subGroupName) {
        String sourceName = alertConfiguration.getApi().getAppName();
        // 新增告警配置时，同步创建告警系统中的分组和告警类型，以及告警规则
        createAlarmSystemResources(
                sourceName,
                subGroupName,
                AlarmContentEnum.getAlarmContentEnum(alarmConfig.getAlarmContent()),
                alarmConfig.getIsEnabled());
        this.save(alarmConfig);
    }

    /** 创建告警系统资源 */
    private void createAlarmSystemResources(
            String sourceName,
            String subGroupName,
            AlarmContentEnum alarmContentEnum,
            Boolean isEnabled) {
        try {
            // 1. 创建告警类型分组
            R<List<TypeGroupDTO>> groupTreeRes = alertApiClient.findAlertSourceAndSubGroupTree(sourceName, subGroupName);
            List<TypeGroupDTO> groupTree = AlarmConstants.getData(groupTreeRes);
            String groupId;
            if (CollUtil.isEmpty(groupTree) || CollUtil.isEmpty(groupTree.get(0).getSubGroups())) {
                // 2. 获取分组ID
                AlertTypeGroupDTO groupDTO = new AlertTypeGroupDTO();
                groupDTO.setName(sourceName);
                groupDTO.setSubGroupName(subGroupName);
                R<List<AlertTypeGroupDTO>> groupListRes =
                        alertApiClient.batchCreateOrUpdateAlertTypeGroup(
                                Collections.singletonList(groupDTO));
                List<AlertTypeGroupDTO> data = AlarmConstants.getData(groupListRes);
                AlertTypeGroupDTO alertTypeGroup = data.get(0);
                groupId = alertTypeGroup.getId();
            } else {
                // 2. 获取分组ID
                groupId = groupTree.get(0).getSubGroups().get(0).getGroupId();
            }
            // 3. 创建告警类型
            AlertTypeDTO typeDTO =
                    createAlertTypeDTO(
                            groupId, sourceName, subGroupName, alarmContentEnum.getName());
            R<List<AlertTypeDTO>> typeListRes =
                    alertApiClient.batchCreateOrUpdateAlertType(Collections.singletonList(typeDTO));
            List<AlertTypeDTO> typeList = AlarmConstants.getData(typeListRes);
            AlertTypeDTO alertType = typeList.get(0);
            // 4. 获取告警类型ID
            String typeId = alertType.getId();
            // 5. 创建告警规则
            AlertRuleDTO ruleDTO =
                    createAlertRuleDTO(
                            alarmContentEnum,
                            AlarmConstants.buildAlertRuleName(
                                    subGroupName, alarmContentEnum.getName()),
                            typeId,
                            isEnabled);
            alertApiClient.batchCreateOrUpdateAlertRule(Collections.singletonList(ruleDTO));
            // 6. 如果是项目故障或项目告警，创建抑制规则（30分钟内相同告警类型只发送一次）
            if (AlarmContentEnum.isProjectFaultOrAlarm(alarmContentEnum)) {
                RestrainRuleDTO restrainDTO =
                        createRestrainRuleDTO(
                                alarmContentEnum.getName(),
                                AlarmConstants.buildAlertRetrainRuleName(
                                        subGroupName, alarmContentEnum.getName()),
                                typeId);
                alertApiClient.batchCreateOrUpdateRestrainRule(
                        Collections.singletonList(restrainDTO));
            }
        } catch (Exception e) {
            log.error("创建告警系统资源失败: " + e.getMessage(), e);
            throw new ServiceException("创建告警系统资源失败: " + e.getMessage());
        }
    }

    /** 创建告警类型 */
    private AlertTypeDTO createAlertTypeDTO(
            String groupId, String sourceName, String subGroupName, String alertTypeName) {
        AlertTypeDTO typeDTO = new AlertTypeDTO();
        typeDTO.setGroupId(groupId);
        typeDTO.setName(alertTypeName);
        typeDTO.setChineseName(alertTypeName);
        typeDTO.setSource(sourceName);
        typeDTO.setSubSource(subGroupName);
        AlertTypeDTO.Prop prop = new AlertTypeDTO.Prop();
        prop.setName(AlarmConstants.LABLE_ALERT_NAME);
        prop.setChineseName(AlarmConstants.LABLE_ALERT_NAME);
        prop.setDataType("string");
        typeDTO.setProps(Collections.singletonList(prop));
        return typeDTO;
    }

    /** 创建告警规则DTO */
    private AlertRuleDTO createAlertRuleDTO(
            AlarmContentEnum alarmContentEnum,
            String alertRuleName,
            String typeId,
            Boolean isEnabled) {
        AlertRuleDTO ruleDTO = new AlertRuleDTO();
        ruleDTO.setName(alertRuleName);
        ruleDTO.setTypeIds(Collections.singletonList(typeId));
        ruleDTO.setPriority(1);
        ruleDTO.setUsageStatus(isEnabled != null ? isEnabled : true);
        AttributeMatchFirstRule rule = new AttributeMatchFirstRule();
        AttributeMatchRule matchRule = new AttributeMatchRule();
        matchRule.setKey(AlarmConstants.LABLE_ALERT_NAME);
        matchRule.setCompare(AttributeMatchRule.Compare.eq);
        matchRule.setOperator(AttributeMatchRule.Operator.and);
        matchRule.setValue(alarmContentEnum.getName());
        rule.setAttrRules(Collections.singletonList(matchRule));
        rule.setOperator(AttributeMatchRule.Operator.or);
        ruleDTO.setAttrFirstRules(Collections.singletonList(rule));
        // 策略过期时间 + 10年
        ruleDTO.setExpires(System.currentTimeMillis() + 365 * 10 * 24 * 60 * 60 * 1000L);
        ruleDTO.setTemplate(
                AlarmTemplatesConstants.getTemplateByAlarmContent(
                        alarmContentEnum, accountSystemConfiguration.dataCenter));
        ruleDTO.setRecoverNotify(false);
        ruleDTO.setRecoverTemplate(ruleDTO.getTemplate());
        return ruleDTO;
    }

    /** 创建抑制规则DTO */
    private RestrainRuleDTO createRestrainRuleDTO(
            String alertTypeName, String restrainRuleName, String typeId) {
        RestrainRuleDTO restrainDTO = new RestrainRuleDTO();
        restrainDTO.setName(restrainRuleName);
        restrainDTO.setTypeIds(Collections.singletonList(typeId));
        restrainDTO.setEffectiveType(EffectiveType.CONTINUOUS);
        restrainDTO.setUsageStatus(true);
        restrainDTO.setCompType(CompareTypeEnum.duration);
        restrainDTO.setTime(30);
        AttributeMatchFirstRule rule = new AttributeMatchFirstRule();
        AttributeMatchRule matchRule = new AttributeMatchRule();
        matchRule.setKey(AlarmConstants.LABLE_ALERT_NAME);
        matchRule.setCompare(AttributeMatchRule.Compare.eq);
        matchRule.setOperator(AttributeMatchRule.Operator.and);
        matchRule.setValue(alertTypeName);
        rule.setAttrRules(Collections.singletonList(matchRule));
        rule.setOperator(AttributeMatchRule.Operator.or);
        restrainDTO.setRestrainRules(Collections.singletonList(rule));
        return restrainDTO;
    }

    /** 更新告警规则的alerting状态 */
    private void updateAlertRuleAlerting(String alertRuleName, boolean usageStatus) {
        try {
            R<AlertRuleDTO> res = alertApiClient.findByName(alertRuleName);
            AlertRuleDTO data = AlarmConstants.getData(res);
            if (data != null) {
                data.setUsageStatus(usageStatus);
                alertApiClient.batchCreateOrUpdateAlertRule(Collections.singletonList(data));
            }
        } catch (Exception e) {
            throw new ServiceException("更新告警规则状态失败: " + e.getMessage());
        }
    }
}
