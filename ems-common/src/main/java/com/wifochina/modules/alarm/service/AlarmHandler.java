package com.wifochina.modules.alarm.service;

import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.weihengtech.alert.client.AlertApiClient;
import com.weihengtech.alert.model.base.BaseAlert;
import com.weihengtech.alert.model.dto.AlertCreateDTO;
import com.wifochina.common.config.AlertConfiguration;
import com.wifochina.common.constants.AlarmConstants;
import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmLevelEnum;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.event.service.AlarmSwitchService;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 告警配置 服务类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Component
public class AlarmHandler {

    private static final String ALARM_SWITCH_REDIS_KEY = "alarm_switch:%s";

    @Resource private AlertConfiguration alertConfiguration;
    @Resource private AlarmConfigService alarmConfigService;
    @Resource private AlarmSwitchService alarmSwitchService;
    @Resource private StringRedisTemplate stringRedisTemplate;
    @Resource private AlertApiClient alertApiClient;

    /**
     * 缓存告警配置
     *
     * @param projectId
     * @return
     */
    public AlarmCacheDTO cacheAlarmInfo(String projectId) {
        if (!stringRedisTemplate.hasKey(buildAlarmSwitchRedisKey(projectId))) {
            String cacheStr = stringRedisTemplate.opsForValue().get(projectId);
            if (StrUtil.isBlank(cacheStr)) {
                return AlarmCacheDTO.builder().build();
            }
            return JSON.parseObject(cacheStr, AlarmCacheDTO.class);
        }
        AlarmCacheDTO res = AlarmCacheDTO.builder().build();
        List<AlarmConfigEntity> alarmConfigList = alarmConfigService.getByProjectId(projectId);
        if (CollectionUtils.isEmpty(alarmConfigList)) {
            return res;
        }
        res.setAlarmConfigList(alarmConfigList);
        AlarmCacheDTO cacheItem = alarmSwitchService.listAlarmSwitch(projectId);
        res.setAllEmsKernelAlarmEnabled(cacheItem.getAllEmsKernelAlarmEnabled());
        res.setAllEmsSubDeviceAlarmEnabled(cacheItem.getAllEmsSubDeviceAlarmEnabled());
        res.setAllMeterAlarmEnabled(cacheItem.getAllMeterAlarmEnabled());
        res.setAlarmSwitchList(cacheItem.getAlarmSwitchList());
        stringRedisTemplate
                .opsForValue()
                .set(buildAlarmSwitchRedisKey(projectId), JSON.toJSONString(cacheItem));
        return cacheItem;
    }

    private String buildAlarmSwitchRedisKey(String projectId) {
        return String.format(ALARM_SWITCH_REDIS_KEY, projectId);
    }

    /**
     * 根据配置发送告警通知
     *
     * @param projectId
     * @param alarmContentEnum
     * @param alarmLevelEnum
     */
    public void handleAlarm(
            String projectId, AlarmContentEnum alarmContentEnum, AlarmLevelEnum alarmLevelEnum) {
        // 走故障、告警逻辑
        AlertCreateDTO param = getAlertCreate(projectId, alarmContentEnum, alarmLevelEnum);
        alertApiClient.sendAlert(param);
    }

    @NotNull
    private AlertCreateDTO getAlertCreate(
            String projectId, AlarmContentEnum alarmContentEnum, AlarmLevelEnum alarmLevelEnum) {
        AlertCreateDTO param = new AlertCreateDTO();
        param.setSource(alertConfiguration.getApi().getAppName());
        param.setSubGroup(projectId);
        BaseAlert baseAlert = getBaseAlert(alarmContentEnum, alarmLevelEnum);
        param.setAlerts(Collections.singletonList(baseAlert));
        return param;
    }

    @NotNull
    private BaseAlert getBaseAlert(
            AlarmContentEnum alarmContentEnum, AlarmLevelEnum alarmLevelEnum) {
        BaseAlert baseAlert = new BaseAlert();
        ;
        baseAlert.setStatus(BaseAlert.STATUS_FIRING);
        baseAlert.setLabels(
                Map.of(
                        AlarmConstants.LABLE_ALERT_NAME, alarmContentEnum.getName(),
                        AlarmConstants.LABLE_MONITOR,
                                alertConfiguration.getApi().getMonitor(),
                        AlarmConstants.LABLE_LEVEL, alarmLevelEnum.name()));
        //        baseAlert.setAnnotations(Map.of(
        //                AlarmConstants.ANNOTATION_DESCRIPTION, alarmContentEnum.getName(),
        //                AlarmConstants.ANNOTATION_VALUE, alarmContentEnum.getName()));
        return baseAlert;
    }
}
