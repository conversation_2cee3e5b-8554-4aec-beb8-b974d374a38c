package com.wifochina.modules.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.alarm.entity.AlarmNotificationEntity;
import com.wifochina.modules.alarm.request.BatchAlarmNotificationRequest;
import com.wifochina.modules.alarm.request.BatchDeleteAlarmNotificationRequest;
import com.wifochina.modules.alarm.request.BatchEditAlarmNotificationRequest;

import java.util.List;

/**
 * 告警通知配置 服务类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface AlarmNotificationService extends IService<AlarmNotificationEntity> {

    /**
     * 根据告警ID查询通知配置列表
     *
     * @param alarmId 告警ID
     * @return 通知配置列表
     */
    List<AlarmNotificationEntity> getByAlarmId(String alarmId);

    /**
     * 新增告警通知配置
     *
     * @param alarmNotification 告警通知配置
     * @return 是否成功
     */
    void addAlarmNotification(AlarmNotificationEntity alarmNotification);

    /**
     * 更新告警通知配置
     *
     * @param alarmNotification 告警通知配置
     * @return 是否成功
     */
    void updateAlarmNotification(AlarmNotificationEntity alarmNotification);

    /**
     * 删除告警通知配置
     *
     * @param id 主键ID
     * @return 是否成功
     */
    void deleteAlarmNotification(String id);

    void batchAddAlarmNotification(BatchAlarmNotificationRequest request);

    void batchEditAlarmNotification(BatchEditAlarmNotificationRequest request);

    void batchDeleteAlarmNotification(BatchDeleteAlarmNotificationRequest request);
}
