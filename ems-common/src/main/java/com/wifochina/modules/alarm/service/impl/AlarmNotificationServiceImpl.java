package com.wifochina.modules.alarm.service.impl;

import cn.hutool.core.util.StrUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.alert.client.AlertApiClient;
import com.weihengtech.alert.model.dto.AlertRuleDTO;
import com.weihengtech.alert.model.entity.NotificationContact;
import com.weihengtech.alert.model.vo.R;
import com.wifochina.common.config.AlertConfiguration;
import com.wifochina.common.constants.AlarmConstants;
import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmNotificationEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.alarm.entity.AlarmNotificationEntity;
import com.wifochina.modules.alarm.mapper.AlarmNotificationMapper;
import com.wifochina.modules.alarm.request.BatchAlarmNotificationRequest;
import com.wifochina.modules.alarm.request.BatchDeleteAlarmNotificationRequest;
import com.wifochina.modules.alarm.request.BatchEditAlarmNotificationRequest;
import com.wifochina.modules.alarm.service.AlarmConfigService;
import com.wifochina.modules.alarm.service.AlarmNotificationService;

import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 告警通知配置 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class AlarmNotificationServiceImpl
        extends ServiceImpl<AlarmNotificationMapper, AlarmNotificationEntity>
        implements AlarmNotificationService {

    @Resource private AlarmConfigService alarmConfigService;
    @Resource private AlertApiClient alertApiClient;
    @Resource private ProjectService projectService;

    @Override
    public List<AlarmNotificationEntity> getByAlarmId(String alarmId) {
        LambdaQueryWrapper<AlarmNotificationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmNotificationEntity::getAlarmId, Long.valueOf(alarmId));
        queryWrapper.orderByDesc(AlarmNotificationEntity::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public void addAlarmNotification(AlarmNotificationEntity alarmNotification) {
        validateDuplicate(alarmNotification);
        // 新增告警通知配置时，联动更新告警规则中的通知联系人
        this.save(alarmNotification);
        updateAlertRuleNotificationContacts(alarmNotification.getAlarmId());
    }

    private void validateDuplicate(AlarmNotificationEntity alarmNotification) {
        LambdaQueryWrapper<AlarmNotificationEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(alarmNotification.getUserName())) {
            queryWrapper
                    .eq(AlarmNotificationEntity::getUserName, alarmNotification.getUserName())
                    .eq(AlarmNotificationEntity::getAlarmId, alarmNotification.getAlarmId());
            List<AlarmNotificationEntity> existingRecords = this.list(queryWrapper);
            if (!CollectionUtils.isEmpty(existingRecords)) {
                throw new ServiceException(
                        ErrorResultCode.CUSTOM_MSG_ERROR.value(),
                        "用户名称已存在: " + alarmNotification.getUserName());
            }
        }
        queryWrapper.clear();
        if (StrUtil.isNotBlank(alarmNotification.getPhone())) {
            queryWrapper
                    .eq(AlarmNotificationEntity::getPhone, alarmNotification.getPhone())
                    .eq(AlarmNotificationEntity::getAlarmId, alarmNotification.getAlarmId());
            List<AlarmNotificationEntity> existingRecords = this.list(queryWrapper);
            if (!CollectionUtils.isEmpty(existingRecords)) {
                throw new ServiceException(
                        ErrorResultCode.CUSTOM_MSG_ERROR.value(),
                        "手机号已存在: " + alarmNotification.getPhone());
            }
        }
        queryWrapper.clear();
        if (StrUtil.isNotBlank(alarmNotification.getEmail())) {
            queryWrapper
                    .eq(AlarmNotificationEntity::getEmail, alarmNotification.getEmail())
                    .eq(AlarmNotificationEntity::getAlarmId, alarmNotification.getAlarmId());
            List<AlarmNotificationEntity> existingRecords = this.list(queryWrapper);
            if (!CollectionUtils.isEmpty(existingRecords)) {
                throw new ServiceException(
                        ErrorResultCode.CUSTOM_MSG_ERROR.value(),
                        "邮箱已存在: " + alarmNotification.getEmail());
            }
        }
    }

    @Override
    public void updateAlarmNotification(AlarmNotificationEntity alarmNotification) {
        this.updateById(alarmNotification);
        // 更新告警通知配置时，联动更新告警规则中的通知联系人
        updateAlertRuleNotificationContacts(alarmNotification.getAlarmId());
    }

    @Override
    public void deleteAlarmNotification(String id) {
        // 先获取要删除的通知配置，以便知道对应的告警ID
        AlarmNotificationEntity notification = this.getById(id);
        if (notification == null) {
            return;
        }
        this.removeById(id);
        // 删除告警通知配置时，联动更新告警规则中的通知联系人
        updateAlertRuleNotificationContacts(notification.getAlarmId());
    }

    /** 更新告警规则的通知联系人 */
    private void updateAlertRuleNotificationContacts(Long alarmId) {
        try {
            // 1. 根据告警ID获取告警配置
            AlarmConfigEntity alarmConfig = alarmConfigService.getById(alarmId);
            if (alarmConfig == null) {
                log.error("根据告警ID获取告警配置失败，告警ID: " + alarmId);
                return;
            }

            // 2. 构建告警系统参数
            String subGroupName = projectService.getById(alarmConfig.getProjectId()).getProjectName();
            String alertTypeName = AlarmContentEnum.getName(alarmConfig.getAlarmContent());
            // 3. 查找告警规则
            R<AlertRuleDTO> alertRuleRes =
                    alertApiClient.findByName(
                            AlarmConstants.buildAlertRuleName(subGroupName, alertTypeName));
            AlertRuleDTO ruleDTO = AlarmConstants.getData(alertRuleRes);
            if (ruleDTO == null) {
                return;
            }
            // 4. 获取所有该告警配置的通知人员
            List<AlarmNotificationEntity> notifications = this.getByAlarmId(alarmId.toString());
            // 5. 构建通知联系人列表
            List<NotificationContact> notificationContacts =
                    notifications.stream()
                            .map(this::buildNotificationContact)
                            .collect(Collectors.toList());
            // 6. 更新告警规则的通知联系人
            ruleDTO.setNotificationContacts(notificationContacts);
            alertApiClient.batchCreateOrUpdateAlertRule(Collections.singletonList(ruleDTO));
        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("更新告警规则通知联系人失败: " + e.getMessage());
        }
    }

    /** 构建通知联系人 */
    private NotificationContact buildNotificationContact(AlarmNotificationEntity notification) {
        NotificationContact contact = new NotificationContact();
        contact.setName(notification.getUserName());
        List<NotificationContact.ContactChannel> channels = new java.util.ArrayList<>();
        // 根据通知类型添加联系人渠道
        if (AlarmNotificationEnum.containsEmail(notification.getNotificationType()) && StrUtil.isNotBlank(notification.getEmail())) {
            // 邮箱通知
            NotificationContact.EmailChannel emailChannel =
                    new NotificationContact.EmailChannel();
            emailChannel.setName(notification.getUserName());
            emailChannel.setEmail(notification.getEmail());
            channels.add(emailChannel);
        } else if (AlarmNotificationEnum.containsSms(notification.getNotificationType()) && StrUtil.isNotBlank(notification.getPhone())) {
            // 短信通知
            NotificationContact.SmsChannel smsChannel = new NotificationContact.SmsChannel();
            smsChannel.setName(notification.getUserName());
            smsChannel.setPhoneNumber(notification.getPhone());
            channels.add(smsChannel);
        } else if (AlarmNotificationEnum.containsPhoneCall(notification.getNotificationType()) && StrUtil.isNotBlank(notification.getPhone())) {
            // 电话通知
            NotificationContact.PhoneCallChannel phoneCallChannel =
                    new NotificationContact.PhoneCallChannel();
            phoneCallChannel.setName(notification.getUserName());
            phoneCallChannel.setPhoneNumber(notification.getPhone());
            channels.add(phoneCallChannel);
        }
        contact.setContactChannels(channels);
        return contact;
    }

    @Override
    public void batchAddAlarmNotification(BatchAlarmNotificationRequest request) {
        for (String projectId : request.getProjectIds()) {
            // 1. 根据projectID和alarmContent查询已存在的AlarmConfigEntity
            AlarmConfigEntity existingConfig = alarmConfigService.getByProjectIdAndAlarmContent(projectId, request.getAlarmContent());
            ProjectEntity projectInfo = projectService.getById(projectId);
            if (projectInfo == null) {
                log.error("根据projectID查询项目信息失败，projectID: " + projectId);
                continue;
            }
            String subGroupName = projectInfo.getProjectName();
            String alertTypeName = AlarmContentEnum.getName(request.getAlarmContent());
            AlarmConfigEntity alarmConfig;
            if (existingConfig != null) {
                alarmConfig = new AlarmConfigEntity();
                BeanUtils.copyProperties(request, alarmConfig);
                alarmConfig.setId(existingConfig.getId());
                alarmConfigService.updateAlarmConfig(alarmConfig, existingConfig, subGroupName, alertTypeName);
            } else {
                // 如果不存在则新增
                alarmConfig = new AlarmConfigEntity();
                alarmConfig.setProjectId(projectId);
                alarmConfig.setAlarmContent(request.getAlarmContent());
                alarmConfig.setProfitDeviationCoefficient(request.getProfitDeviationCoefficient());
                alarmConfig.setDowntimeThreshold(request.getDowntimeThreshold());
                alarmConfig.setOfflineTimeThreshold(request.getOfflineTimeThreshold());
                alarmConfig.setEfficiencyReminderThreshold(
                        request.getEfficiencyReminderThreshold());
                alarmConfig.setIsEnabled(
                        request.getIsEnabled() != null ? request.getIsEnabled() : true);
                alarmConfigService.addAlarmConfig(alarmConfig, subGroupName);
            }

            // 2. 根据AlarmConfigEntity的ID和userName查询通知人员是否已存在
            LambdaQueryWrapper<AlarmNotificationEntity> notificationQueryWrapper =
                    new LambdaQueryWrapper<>();
            notificationQueryWrapper
                    .eq(AlarmNotificationEntity::getAlarmId, alarmConfig.getId())
                    .eq(AlarmNotificationEntity::getUserName, request.getUserName());

            AlarmNotificationEntity existingNotification =
                    this.getOne(notificationQueryWrapper);

            if (existingNotification != null) {
                // 已存在则更新
                existingNotification.setNotificationType(request.getNotificationType());
                existingNotification.setPhone(request.getPhone());
                existingNotification.setEmail(request.getEmail());
                this.updateAlarmNotification(existingNotification);
            } else {
                // 不存在则新增该人员
                AlarmNotificationEntity notification = new AlarmNotificationEntity();
                notification.setAlarmId(alarmConfig.getId());
                notification.setUserName(request.getUserName());
                notification.setNotificationType(request.getNotificationType());
                notification.setPhone(request.getPhone());
                notification.setEmail(request.getEmail());
                this.addAlarmNotification(notification);
            }
        }
    }

    @Override
    public void batchEditAlarmNotification(BatchEditAlarmNotificationRequest request) {
        for (String projectId : request.getProjectIds()) {
            // 处理告警内容配置更新
            if (request.getAlarmContent() != null) {
                ProjectEntity projectInfo = projectService.getById(projectId);
                if (projectInfo == null) {
                    log.error("根据projectID查询项目信息失败，projectID: " + projectId);
                    continue;
                }
                String subGroupName = projectInfo.getProjectName();
                String alertTypeName = AlarmContentEnum.getName(request.getAlarmContent());
                // 1. 根据projectID和alarmContent查询已存在的AlarmConfigEntity
                AlarmConfigEntity existingConfig =
                        alarmConfigService.getByProjectIdAndAlarmContent(
                                projectId, request.getAlarmContent());
                if (existingConfig == null) {
                    continue;
                }
                AlarmConfigEntity alarmConfig = new AlarmConfigEntity();
                BeanUtils.copyProperties(request, alarmConfig);
                alarmConfig.setId(existingConfig.getId());
                alarmConfigService.updateAlarmConfig(
                        alarmConfig, existingConfig, subGroupName, alertTypeName);
            }

            // 根据用户名替换
            LambdaQueryWrapper<AlarmNotificationEntity> notificationQueryWrapper =
                    new LambdaQueryWrapper<>();
            // 处理通知人员信息替换
            if (StrUtil.isNotBlank(request.getOriginalUserName())
                    && StrUtil.isNotBlank(request.getNewUserName())) {
                notificationQueryWrapper.eq(
                        AlarmNotificationEntity::getUserName, request.getOriginalUserName());
            } else if (StrUtil.isNotBlank(request.getOriginalPhone())
                    && StrUtil.isNotBlank(request.getNewPhone())) {
                notificationQueryWrapper.eq(
                        AlarmNotificationEntity::getPhone, request.getOriginalPhone());
            } else if (StrUtil.isNotBlank(request.getOriginalEmail())
                    && StrUtil.isNotBlank(request.getNewEmail())) {
                notificationQueryWrapper.eq(
                        AlarmNotificationEntity::getEmail, request.getOriginalEmail());
            } else {
                continue;
            }
            List<AlarmConfigEntity> alarmConfigList = alarmConfigService.getByProjectId(projectId);
            if (CollectionUtils.isEmpty(alarmConfigList)) {
                continue;
            }
            List<Long> alarmIdList =
                    alarmConfigList.stream()
                            .map(AlarmConfigEntity::getId)
                            .collect(Collectors.toList());
            notificationQueryWrapper.in(AlarmNotificationEntity::getAlarmId, alarmIdList);
            List<AlarmNotificationEntity> notifications = this.list(notificationQueryWrapper);
            if (CollectionUtils.isEmpty(notifications)) {
                continue;
            }
            for (AlarmNotificationEntity notification : notifications) {
                notification.setUserName(request.getNewUserName());
                notification.setPhone(request.getNewPhone());
                notification.setEmail(request.getNewEmail());
                this.updateAlarmNotification(notification);
            }
        }
    }

    @Override
    public void batchDeleteAlarmNotification(BatchDeleteAlarmNotificationRequest request) {
        // 验证至少提供一个删除条件
        validateDeleteConditions(request);

        // 记录是否有找到匹配的通知
        AtomicBoolean hasFoundNotifications = new AtomicBoolean(false);

        // 遍历项目ID进行批量删除
        request.getProjectIds()
                .forEach(
                        projectId -> {
                            // 获取该项目的所有告警配置ID
                            Set<Long> projectAlarmIds = getProjectAlarmIds(projectId);
                            if (projectAlarmIds.isEmpty()) {
                                return; // 如果项目没有告警配置，跳过
                            }

                            // 执行删除操作并记录结果
                            boolean projectHasNotifications =
                                    deleteNotificationsByConditions(request, projectAlarmIds);
                            hasFoundNotifications.compareAndSet(false, projectHasNotifications);
                        });

        // 如果没有找到任何匹配的通知，抛出异常
        Assert.isTrue(hasFoundNotifications.get(), "在指定的项目中未找到匹配的通知人员信息");
    }

    /** 验证删除条件 */
    private void validateDeleteConditions(BatchDeleteAlarmNotificationRequest request) {
        boolean hasDeleteCondition =
                StrUtil.isNotBlank(request.getUserName())
                        || StrUtil.isNotBlank(request.getPhone())
                        || StrUtil.isNotBlank(request.getEmail());
        Assert.isTrue(hasDeleteCondition, "至少需要提供一个删除条件（用户名称、手机号或邮箱）");
    }

    /** 获取项目的所有告警配置ID */
    private Set<Long> getProjectAlarmIds(String projectId) {
        LambdaQueryWrapper<AlarmConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(AlarmConfigEntity::getProjectId, projectId)
                .select(AlarmConfigEntity::getId);

        List<AlarmConfigEntity> configs = alarmConfigService.list(queryWrapper);
        return configs.stream().map(AlarmConfigEntity::getId).collect(Collectors.toSet());
    }

    /** 根据条件删除通知并返回是否找到匹配的通知 */
    private boolean deleteNotificationsByConditions(
            BatchDeleteAlarmNotificationRequest request, Set<Long> projectAlarmIds) {
        AtomicBoolean hasNotifications = new AtomicBoolean(false);

        // 根据用户名删除
        if (StrUtil.isNotBlank(request.getUserName())) {
            hasNotifications.set(
                    deleteNotificationsByField(
                            AlarmNotificationEntity::getUserName,
                            request.getUserName(),
                            projectAlarmIds)
                            || hasNotifications.get());
        }

        // 根据手机号删除
        if (StrUtil.isNotBlank(request.getPhone())) {
            hasNotifications.set(
                    deleteNotificationsByField(
                            AlarmNotificationEntity::getPhone,
                            request.getPhone(),
                            projectAlarmIds)
                            || hasNotifications.get());
        }

        // 根据邮箱删除
        if (StrUtil.isNotBlank(request.getEmail())) {
            hasNotifications.set(
                    deleteNotificationsByField(
                            AlarmNotificationEntity::getEmail,
                            request.getEmail(),
                            projectAlarmIds)
                            || hasNotifications.get());
        }

        return hasNotifications.get();
    }

    /** 根据指定字段删除通知 */
    private boolean deleteNotificationsByField(
            SFunction<AlarmNotificationEntity, String> fieldGetter,
            String fieldValue,
            Set<Long> projectAlarmIds) {
        LambdaQueryWrapper<AlarmNotificationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(fieldGetter, fieldValue)
                .in(AlarmNotificationEntity::getAlarmId, projectAlarmIds);

        List<AlarmNotificationEntity> notifications = this.list(queryWrapper);
        if (!notifications.isEmpty()) {
            // 批量删除，提升性能
            List<Long> notificationIds =
                    notifications.stream()
                            .map(AlarmNotificationEntity::getId)
                            .collect(Collectors.toList());
            this.removeByIds(notificationIds);
            // 联动更新告警系统
            notifications.forEach(
                    notification -> updateAlertRuleNotificationContacts(notification.getAlarmId()));
            return true;
        }
        return false;
    }
}
