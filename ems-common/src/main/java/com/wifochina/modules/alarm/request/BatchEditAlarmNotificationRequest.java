package com.wifochina.modules.alarm.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * 批量编辑告警通知配置请求对象
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@ApiModel(value = "批量编辑告警通知配置请求")
public class BatchEditAlarmNotificationRequest {

    @ApiModelProperty(value = "项目ID列表", required = true)
    private List<String> projectIds;

    // ========== 告警内容配置相关字段 ==========
    @ApiModelProperty(value = "告警内容（如果提供，则更新该告警内容的配置）")
    private Integer alarmContent;

    @ApiModelProperty(value = "是否启用(0:禁用 1:启用)")
    private Boolean isEnabled;

    @ApiModelProperty(value = "收益偏差系数")
    private Float profitDeviationCoefficient;

    @ApiModelProperty(value = "停机时长阈值(分钟)")
    private Integer downtimeThreshold;

    @ApiModelProperty(value = "离线时长阈值（分钟）")
    private Integer offlineTimeThreshold;

    @ApiModelProperty(value = "效率提醒阈值")
    private Float efficiencyReminderThreshold;

    // ========== 告警通知人员替换相关字段 ==========
    @ApiModelProperty(value = "要替换的用户名称（原始值）")
    private String originalUserName;

    @ApiModelProperty(value = "新的用户名称")
    private String newUserName;

    @ApiModelProperty(value = "要替换的手机号（原始值）")
    private String originalPhone;

    @ApiModelProperty(value = "新的手机号")
    private String newPhone;

    @ApiModelProperty(value = "要替换的邮箱（原始值）")
    private String originalEmail;

    @ApiModelProperty(value = "新的邮箱")
    private String newEmail;
}
