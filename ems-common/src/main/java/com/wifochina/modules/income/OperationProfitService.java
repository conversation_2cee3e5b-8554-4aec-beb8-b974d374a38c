package com.wifochina.modules.income;

import com.wifochina.common.request.RangeRequest;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.request.BenefitRequest;

import java.util.Map;

/**
 * operationProfitService
 *
 * <p>1.4.2 之后 已经把 pv wind 相关拆出去了 现在这个老的运营收益相关的service 只有 最后的 demand , 后面找机会拆解出去
 *
 * <p>目前 有 ElectricProfitService 和 RenewableProfitService
 *
 * <AUTHOR>
 * @version 1.0
 * @since 4/14/2022 11:24 AM
 */
public interface OperationProfitService {

    ProfitVO getDemandProfitVO(BenefitRequest benefitRequest, String projectId);

    /**
     * 这个是查询 需量收益power的 origin - meter 就是所谓的 降低需量值 用于 计算需量收益的
     *
     * @param startDate : startData
     * @param endDate : endDate
     * @param projectId : projectId
     * @return : key是groupId , Map<Long, Double> 是 应该只有一个 用的是当月的最大的 origin - meter 算出来的
     */
    Map<String, Map<Long, Double>> getDemandIncomePower(
            Long startDate, Long endDate, String projectId);

    Map<String, Map<Long, Double>> getDemandIncomePowerFromDb(
            RangeRequest request, String projectId);
}
