package com.wifochina.modules.income.controller.rerun;

import com.wifochina.common.constants.ReRunTypeEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.income.controller.rerun.service.CacheDataReRunService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.operation.request.CacheDataReRunRequest;
import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.AllArgsConstructor;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created on 2024/6/24 19:23.
 *
 * <AUTHOR>
 */
@RequestMapping("/cacheDataReRun")
@RestController
@Api(tags = "444-重跑缓存数据")
@AllArgsConstructor
public class CacheDataReRunController {

    private final CacheDataReRunService cacheDataReRunService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 1.4.2 新增 余热发电的重跑数据 的接口
     *
     * @param request : request
     * @return : Result<Void>
     */
    @PostMapping("/reRunWaster")
    @ApiOperation("数据重跑waster余热发电")
    @PreAuthorize("hasAuthority('/manage/function/reRun') or hasAuthority('/system/reRun')")
    @Log(module = "RERUN", methods = "RERUN_WASTER", type = OperationType.ADD_SIMPLE)
    public Result<Void> reRunWaster(@RequestBody CacheDataReRunRequest request) {
        String type = request.getType();
        if (type.equals(ReRunTypeEnum.WASTER.name())) {
            threadPoolTaskExecutor.submit(() -> cacheDataReRunService.reRunRenewable(request));
        } else {
            throw new ServiceException("该方法只支持 Waster的重跑");
        }
        return Result.success();
    }

    @PostMapping("/reRunPv")
    @ApiOperation("数据重跑pv")
    @Log(module = "RERUN", methods = "RERUN_PV", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/manage/function/reRun') or hasAuthority('/system/reRun')")
    public Result<Void> reRunPv(@RequestBody CacheDataReRunRequest request) {
        String type = request.getType();
        if (type.equals(ReRunTypeEnum.PV.name())) {
            threadPoolTaskExecutor.submit(() -> cacheDataReRunService.reRunRenewable(request));
        } else {
            throw new ServiceException("该方法只支持 PV的重跑");
        }
        return Result.success();
    }

    @PostMapping("/reRunWind")
    @ApiOperation("数据重跑wind")
    @Log(module = "RERUN", methods = "RERUN_WIND", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/manage/function/reRun') or hasAuthority('/system/reRun')")
    public Result<Void> reRunWind(@RequestBody CacheDataReRunRequest request) {
        String type = request.getType();
        if (type.equals(ReRunTypeEnum.WIND.name())) {
            threadPoolTaskExecutor.submit(() -> cacheDataReRunService.reRunRenewable(request));
        } else {
            throw new ServiceException("该方法只支持 WIND的重跑");
        }
        return Result.success();
    }

    @PostMapping("/reRunBattery")
    @ApiOperation("数据重跑电收益")
    @Log(module = "RERUN", methods = "RERUN_BATTERY", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/manage/function/reRun') or hasAuthority('/system/reRun')")
    public Result<Void> reRunBattery(@RequestBody CacheDataReRunRequest request) {
        String type = request.getType();
        if (type.equals(ReRunTypeEnum.BATTERY.name())) {
            cacheDataReRunService.reRunBattery(request);
        } else {
            throw new ServiceException("该方法只支持 Battery 的重跑");
        }
        return Result.success();
    }

    @PostMapping("/reRunDemand")
    @ApiOperation("数据重跑需量数据")
    @Log(module = "RERUN", methods = "RERUN_DEMAND", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/manage/function/reRun') or hasAuthority('/system/reRun')")
    public Result<Void> reRunDemand(@RequestBody CacheDataReRunRequest request) {
        String type = request.getType();
        if (type.equals(ReRunTypeEnum.DEMAND.name())) {
            cacheDataReRunService.reRunDemand(request);
        } else {
            throw new ServiceException("该方法只支持 Demand 的重跑");
        }
        return Result.success();
    }

    @PostMapping("/reDayReport")
    @ApiOperation("数据重跑日报表")
    @Log(module = "RERUN", methods = "RERUN_DAYREPORT", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/manage/function/reRun') or hasAuthority('/system/reRun')")
    public Result<Void> reRunDayReport(@RequestBody CacheDataReRunRequest request) {
        String type = request.getType();
        if (type.equals(ReRunTypeEnum.DAY_REPORT.name())) {
            cacheDataReRunService.reRunDayReport(request);
        } else {
            throw new ServiceException("该方法只支持 DayReport 的重跑");
        }
        return Result.success();
    }
}
