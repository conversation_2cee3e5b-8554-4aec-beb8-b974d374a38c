package com.wifochina.modules.income.request;

import com.wifochina.modules.group.service.EquipmentSearchService;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * profitRequest
 *
 * @date 4/14/2022 11:17 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "按日期查询收益")
@Accessors(chain = true)
public class ProfitRequest {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;

    @ApiModelProperty(value = "UTC与客户端的时区差,东8区，传入-8", required = true)
    private Integer duration;

    @ApiModelProperty(value = "所属设备id，默认为all，all代表全部，不能传空或者空字符串")
    private String deviceId;

    @ApiModelProperty(value = "分组id，默认为all，all代表全部，不能传空或者空字符串")
    private String groupId;

    private String projectId;

    private EquipmentSearchService.DeviceIdsAndControl deviceIdsAndControl;
}
