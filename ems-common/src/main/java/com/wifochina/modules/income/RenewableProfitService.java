package com.wifochina.modules.income;

import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.request.BenefitRequest;

/**
 * Created on 2024/9/9 09:37.
 *
 * <AUTHOR>
 */
public interface RenewableProfitService {

    ProfitVO getRenewableProfitVo(
            BenefitRequest benefitRequest, String projectId, CalculateTypeEnum calculateTypeEnum);

    /**
     * 查询 今天的收益 支持分组
     *
     * @param profitRequest : profitRequest
     * @return : ElectricProfitVo
     */
    ProfitVO getTodayRenewableProfitVo(
            ProfitRequest profitRequest, CalculateTypeEnum calculateTypeEnum);
}
