package com.wifochina.modules.income.query.pvwind;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.renewable.request.RenewableProfitOneRequest;
import com.wifochina.modules.renewable.request.RenewableProfitRequest;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
public interface RenewableIncomeQueryService extends IService<RenewableProfitEntity> {

    RenewableProfitEntity queryRenewableTotalProfit(RenewableProfitRequest renewableProfitRequest);

    RenewableProfitEntity queryRenewableData(RenewableProfitOneRequest request);
}
