package com.wifochina.modules.income;

import com.wifochina.common.constants.TimePointEnum;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.income.vo.OperationProfitVo;
import com.wifochina.modules.operation.VO.GroupElectricProfit;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.project.entity.ProjectEntity;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * ||||||| constructed merge base package com.wifochina.modules.income;/** ======= package
 * com.wifochina.modules.income;
 *
 * <p>import com.wifochina.common.constants.TimePointEnum; import
 * com.wifochina.modules.income.vo.OperationProfitVo; import
 * com.wifochina.modules.operation.VO.ElectricProfitVO; import
 * com.wifochina.modules.project.entity.ProjectEntity;
 *
 * <p>import java.util.Map;
 *
 * <p>/** >>>>>>> Stashed changes Created on 2024/9/9 14:12.
 *
 * <p>这个类是 用于 提供有些 聚合 或者说 运营收益 它的结果的收益 包括了 pv wind 和 电池等等.. <br>
 * 和以前的OperationProfitService 不一样
 *
 * <AUTHOR> <<<<<<< Updated upstream
 */
public interface NewOperationProfitService {

    /**
     * 获取 renewable 相关的3个 future
     * @param benefitRequest
     * @param projectEntity
     * @param systemGroupEntity
     * @return
     */
    Map<String, CompletableFuture<ProfitVO>> getRenewableFutureMaps(
            BenefitRequest benefitRequest,
            ProjectEntity projectEntity,
            GroupEntity systemGroupEntity);

    /**
     * 查询 根据TimePointEnum...得到 对应的 收益
     *
     * @see com.wifochina.modules.operation.controller.OperationController#getAllTotalProfitVO 三项页收益
     * @see com.wifochina.modules.largescreen.controller.LargeScreenController#getProfitVO 大屏的收益
     * @param todayWhetherFromCache : 今日的数据是否走缓存
     * @param projectId : projectId
     * @param timePointEnum : timePointEnum
     * @return : map
     */
    Map<String, Object> getTotalOperationProfit(
            boolean todayWhetherFromCache, String projectId, TimePointEnum... timePointEnum);

    /**
     * 今天的实时的 运营收益 包括电 pv 和 wind 相关 电收益那边这个方法默认是 不分组查询的
     *
     * @return : 今天的实时的收益 OperationProfitVo
     */
    OperationProfitVo getTodayOperationProfit(ProjectEntity projectEntity);

    /**
     * TODO refactor 按天返回系统收益查询
     *
     * @param benefitRequest request
     * @return 总收益情况
     */
    ProfitVO getProfitVO(BenefitRequest benefitRequest, String projectId, boolean calGroupFlag)
            throws InterruptedException;

    GroupElectricProfit getGroupProfitVo(
            BenefitRequest benefitRequest, ProjectEntity projectEntity, GroupEntity group);

    Map<String, Map<Long, Double>> getDemandPower(Long startDate, Long endDate, String projectId);
}
