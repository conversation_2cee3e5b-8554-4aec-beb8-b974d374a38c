package com.wifochina.modules.income;

import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.operation.VO.ElectricProfitVO;

import java.util.Map;

/**
 * Created on 2024/9/9 09:37. <br>
 * 电相关收益 的 Service , 从以前的 OperationProfitService 抽取出来的 只包含电相关的, 并且改了方法名称和 合并了相关方法
 *
 * <AUTHOR>
 */
public interface ElectricProfitService {

    /**
     * 按天返回每日收益情况
     *
     * @param profitRequest request
     * @return 每日收益情况
     */
    Map<String, Object> getElectricProfitCollect(ProfitRequest profitRequest);

    /**
     * 查询 今天的收益 支持分组
     *
     * @param profitRequest : profitRequest
     * @return : ElectricProfitVo
     */
    ElectricProfitVO getTodayRealTimeElectricProfitVo(
            ProfitRequest profitRequest, boolean groupController);

    /**
     * 获取 收益Total
     *
     * @param profitRequest : profitRequest
     * @return : ElectricProfitVo
     */
    ElectricProfitVO getElectricTotalProfit(ProfitRequest profitRequest);

    /**
     * 获取 收益按照 period 时段(天)
     *
     * @param profitRequest : profitRequest
     * @return : Map<Long, ElectricProfitVO>
     */
    Map<Long, ElectricProfitVO> getElectricPeriodProfit(ProfitRequest profitRequest);

    /**
     * 查询 今天的收益 支持分组
     *
     * @param profitRequest : profitRequest
     * @return : ElectricProfitVo
     */
    ElectricProfitVO getTodayElectricProfitVo(ProfitRequest profitRequest, boolean groupController);
}
