package com.wifochina.modules.income.caculate.pvwind;

import com.wifochina.modules.renewable.entity.RenewableProfitEntity;

import java.time.LocalDate;
import java.time.ZoneId;

/**
 * Created on 2024/4/12 13:46.
 *
 * <AUTHOR>
 */
public interface IPvWindIncomeCalculateService {

    RenewableProfitEntity calculatePvWindProfit(
            LocalDate time, String pvOrWindType, ZoneId zoneId, String projectId);
}
