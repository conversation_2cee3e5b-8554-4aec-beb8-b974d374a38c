package com.wifochina.modules.income;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.*;
import com.wifochina.common.price.RenewableModelPriceFormulaComponent;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.CacheUtils;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.TimeContext;
import com.wifochina.modules.electric.ElectricAdapterChooser;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.ElectricDynamicPeriodService;
import com.wifochina.modules.electric.ElectricService;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.income.caculate.electric.IElectricIncomeCalculateService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.income.timer.TaskOperationTimer;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.VO.*;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.operation.util.OperationUtil;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.renewable.RenewableAdapterChooser;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-05-15 3:53 PM
 */
@Slf4j
@Service
public abstract class AbstractProfitService implements OperationProfitService {

    @Resource protected DeviceService deviceService;

    @Resource protected AmmeterService ammeterService;

    @Resource protected ProjectService projectService;

    @Resource protected GroupService groupService;

    @Resource protected ElectricPriceService electricPriceService;

    @Resource protected ElectricService electricService;
    @Resource protected ElectricAdapterChooser electricAdapterChooser;

    @Resource protected RedisTemplate<String, String> redisTemplate;

    @Resource private GroupDeviceService groupDeviceService;

    @Resource private GroupAmmeterService groupAmmeterService;

    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource private RenewableAdapterChooser chooser;

    @Resource private CacheUtils cacheUtils;

    @Resource private RenewableModelPriceFormulaComponent renewableModelPriceFormulaComponent;

    @Resource private EquipmentSearchService equipmentSearchService;

    @Resource private ElectricDynamicPeriodService electricDynamicPeriodService;

    @Resource private IElectricIncomeCalculateService IElectricIncomeCalculateService;
    @Resource private ElectricProfitService electricProfitService;

    public abstract Double getPvOrWindPower(
            Long startDate, Long endDate, String column, String type, String projectId);

    public abstract ElectricProfitVO getTodayBatteryProfit(String projectId);

    public abstract ElectricProfitVO getTodayGroupBatteryProfit(String projectId, String groupId);

    private void fillTodayDynamicPeriodDetails(
            ProjectEntity projectEntity, List<ElectricDynamicPeriodEntity> results) {
        String projectId = projectEntity.getId();
        long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        // 找到 那个匹配到的 电价配置策略
        ElectricPriceEntity electricPriceEntity =
                electricPriceService.getElectricPriceMatch(projectId, todayZero);
        // 查询今天的则要 处理一下
        List<DeviceEntity> incomeDevices = deviceService.findIncomeDevices(projectId);
        List<AmmeterEntity> incomeAmmeters = ammeterService.findIncomeAmmeter(projectId);
        TimeContext timeContext = TimeContext.getContext(projectEntity.getTimezone(), todayZero);
        if (electricPriceEntity != null) {
            ElectricAdapterService.ElectricAdapterContext context =
                    new ElectricAdapterService.ElectricAdapterContext() {
                        @Override
                        public List<ElectricPriceEntity> periodPriceList() {
                            return electricPriceEntity.getPeriodPriceList();
                        }

                        @Override
                        public long timePoint() {
                            return todayZero;
                        }

                        @Override
                        public ProjectEntity project() {
                            return projectEntity;
                        }
                    };
            Map<Long, ElectricDynamicPeriodEntity> electricDynamicPeriodEntityMap = new HashMap<>();
            incomeDevices.forEach(
                    device -> {
                        List<ElectricDynamicPeriodEntity> toDayResults = new ArrayList<>();
                        electricDynamicPeriodService.commonPeriodList(
                                context,
                                timeContext,
                                toDayResults,
                                IElectricIncomeCalculateService.getElectricEmsSql(
                                        context.project()),
                                device);
                        toDayResults.forEach(
                                electricDynamicPeriodEntity -> {
                                    ElectricDynamicPeriodEntity exist =
                                            electricDynamicPeriodEntityMap.get(
                                                    electricDynamicPeriodEntity
                                                            .getPeriodStartTime());
                                    if (exist != null) {
                                        // 如果存在了 把数值加进去
                                        setSum(exist, electricDynamicPeriodEntity);
                                    } else {
                                        electricDynamicPeriodEntityMap.put(
                                                electricDynamicPeriodEntity.getPeriodStartTime(),
                                                electricDynamicPeriodEntity);
                                    }
                                });
                    });
            incomeAmmeters.forEach(
                    ammeter -> {
                        List<ElectricDynamicPeriodEntity> toDayResults = new ArrayList<>();
                        electricDynamicPeriodService.commonPeriodList(
                                context,
                                timeContext,
                                toDayResults,
                                IElectricIncomeCalculateService.getElectricMeterSql(
                                        context.project()),
                                ammeter);
                        toDayResults.forEach(
                                electricDynamicPeriodEntity -> {
                                    ElectricDynamicPeriodEntity exist =
                                            electricDynamicPeriodEntityMap.get(
                                                    electricDynamicPeriodEntity
                                                            .getPeriodStartTime());
                                    if (exist != null) {
                                        // 如果存在了 把数值加进去
                                        setSum(exist, electricDynamicPeriodEntity);
                                    } else {
                                        electricDynamicPeriodEntityMap.put(
                                                electricDynamicPeriodEntity.getPeriodStartTime(),
                                                electricDynamicPeriodEntity);
                                    }
                                });
                    });
            List<ElectricDynamicPeriodEntity> toDayResults =
                    new ArrayList<>(electricDynamicPeriodEntityMap.values());
            results.addAll(toDayResults);
        }
    }

    /**
     * // 1.3.7 当 实时电价or 固定电价 的时候 需要把每个段的 详细数据返回 供前端导出
     *
     * @param profitRequest : request
     * @param projectId: projectId
     * @return : list
     */
    private List<ElectricDynamicPeriodEntity> getDynamicPeriodDetailProfit(
            ProfitRequest profitRequest, String projectId) {
        List<String> types =
                List.of(
                        ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.name(),
                        ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name());
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<ElectricDynamicPeriodEntity> results = new ArrayList<>();
        // 固定电价 or 实时电价 把详情返回
        if (projectEntity.getElectricPriceType() != null
                && types.contains(projectEntity.getElectricPriceType())) {
            // 真正查询 list 列表
            results = getDynamicDetailEntityList(profitRequest, projectId, projectEntity);
            long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            if (profitRequest.getEndDate() > todayZero) {
                // 查询今天的 部分
                // 处理一下 今天的 时段, 这个时段 只是获取到 列表 不进行数据库的缓存
                fillTodayDynamicPeriodDetails(projectEntity, results);
            }
        }
        return results;
    }

    private void setSum(
            ElectricDynamicPeriodEntity exist,
            ElectricDynamicPeriodEntity electricDynamicPeriodEntity) {
        exist.setChargeQuantity(
                exist.getChargeQuantity() + electricDynamicPeriodEntity.getChargeQuantity());
        exist.setChargeCost(exist.getChargeCost() + electricDynamicPeriodEntity.getChargeCost());
        exist.setDischargeQuantity(
                exist.getDischargeQuantity() + electricDynamicPeriodEntity.getDischargeQuantity());
        exist.setDischargeBenefit(
                exist.getDischargeBenefit() + electricDynamicPeriodEntity.getDischargeBenefit());
        exist.setTotalBenefit(
                exist.getTotalBenefit() + electricDynamicPeriodEntity.getTotalBenefit());
    }

    /** 查询 数据库的 动态分段的 entity 列表 此方法支持 分时缓存条件下来查询 也支持 固定电价or实时电价下前端导出需要的数据 */
    private List<ElectricDynamicPeriodEntity> getDynamicDetailEntityList(
            ProfitRequest profitRequest, String projectId, ProjectEntity projectEntity) {
        List<ElectricDynamicPeriodEntity> timeSharingProfits;
        EquipmentSearchService.SearchContext context =
                new EquipmentSearchService.SearchContext()
                        .setProject(projectEntity)
                        .setSearchGroupId(profitRequest.getGroupId());
        boolean groupController = equipmentSearchService.groupController(context);
        List<String> deviceIds =
                equipmentSearchService.equipmentIdsSupportGroup(context, groupController);
        ElectricRequest timeSharingRequest =
                new ElectricRequest()
                        .setProjectId(projectId)
                        // 这个 deviceIds 是 设备和电表的合计 也是上面支持了 分组的
                        .setDeviceIdList(deviceIds)
                        .setStart(profitRequest.getStartDate())
                        // +1 是因为前端传的是 23:59:59  查询里面 是 < endTIme
                        .setEnd(profitRequest.getEndDate() + 1);
        timeSharingProfits =
                ((ElectricDynamicPeriodService)
                                electricAdapterChooser.choose(
                                        ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name()))
                        .queryElectricTimeSharingProfit(timeSharingRequest);
        return timeSharingProfits;
    }

    /** 峰谷平尖收益 需要加上 补充数据收益 */
    private Map<Long, ElectricProfitVO> getPeriodProfit(
            ProfitRequest profitRequest, String projectId) {

        ProjectEntity projectEntity = projectService.getById(projectId);
        EquipmentSearchService.SearchContext context =
                new EquipmentSearchService.SearchContext()
                        .setProject(projectEntity)
                        .setSearchGroupId(profitRequest.getGroupId());
        boolean groupController = equipmentSearchService.groupController(context);
        List<String> deviceIds =
                equipmentSearchService.equipmentIdsSupportGroup(context, groupController);
        deviceIds.addAll(
                groupService.queryGroup(projectId).stream()
                        .map(GroupEntity::getId)
                        .collect(Collectors.toList()));
        Map<Long, ElectricProfitVO> map = new HashMap<>();
        if (!deviceIds.isEmpty()) {
            ElectricRequest electricRequest = new ElectricRequest();
            electricRequest.setProjectId(projectId);
            electricRequest.setStart(profitRequest.getStartDate());
            electricRequest.setEnd(profitRequest.getEndDate());
            electricRequest.setDeviceIdList(deviceIds);
            // 1.3.7 add 超过31天 则按照月份统计
            boolean differenceMoreThan31Days =
                    MyTimeUtil.isDifferenceMoreThan31Days(
                            electricRequest.getStart(), electricRequest.getEnd());
            electricRequest.setTimeRangeWithin31Day(differenceMoreThan31Days);
            electricAdapterChooser
                    .choose(projectEntity.getElectricPriceType())
                    .queryElectricEveryDayProfit(
                            electricRequest,
                            new ElectricAdapterService.QueryElectricsResult() {
                                @Override
                                public void peakValleysPeriodPostProcessor(List<ElectricEntity> t) {
                                    for (ElectricEntity electricEntity : t) {
                                        ElectricProfitVO electricProfitVO =
                                                OperationUtil.getPeakValleysPeriodElectricProfitVo(
                                                        Optional.ofNullable(electricEntity)
                                                                .orElse(new ElectricEntity()));
                                        assert electricEntity != null;
                                        if (!differenceMoreThan31Days) {
                                            // TODO ems 数据校准
                                            map.put(electricEntity.getTime(), electricProfitVO);
                                        } else {
                                            // 如果找过了3天 则这个不会有time,  需要根据year month 构建一个 月份的 key
                                            long monthKey =
                                                    MyTimeUtil.getEveryMonthKey(
                                                            projectEntity.getTimezone(),
                                                            electricEntity.getYear(),
                                                            electricEntity.getMonth());
                                            map.put(monthKey, electricProfitVO);
                                        }
                                    }
                                }

                                @Override
                                public void dynamicPeriodPostProcessor(
                                        List<ElectricDynamicPeriodEntity> t) {

                                    for (ElectricDynamicPeriodEntity electricDynamicPeriodEntity :
                                            t) {
                                        ElectricProfitVO electricProfitVO =
                                                OperationUtil.getDynamicPeriodElectricProfitVo(
                                                        Optional.ofNullable(
                                                                        electricDynamicPeriodEntity)
                                                                .orElse(
                                                                        new ElectricDynamicPeriodEntity()));
                                        assert electricDynamicPeriodEntity != null;
                                        if (!differenceMoreThan31Days) {
                                            // 根据 年月日去 确定 key 时间戳 因为这个查询是把时段按照天都合并了  不会有具体天的时间戳 key
                                            long dayKey =
                                                    MyTimeUtil.getEveryDayKey(
                                                            projectEntity.getTimezone(),
                                                            electricDynamicPeriodEntity.getYear(),
                                                            electricDynamicPeriodEntity.getMonth(),
                                                            electricDynamicPeriodEntity.getDay());
                                            map.put(dayKey, electricProfitVO);
                                        } else {
                                            // 根据 月份进行分组
                                            long monthKey =
                                                    MyTimeUtil.getEveryMonthKey(
                                                            projectEntity.getTimezone(),
                                                            electricDynamicPeriodEntity.getYear(),
                                                            electricDynamicPeriodEntity.getMonth());
                                            map.put(monthKey, electricProfitVO);
                                        }
                                    }
                                }
                            });
            long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            if (profitRequest.getEndDate() > todayZero) {
                if (groupController) { // 新增的支持 分组查询今日的储能收益
                    ElectricProfitVO todayElectricProfitVO =
                            getTodayGroupBatteryProfit(projectId, profitRequest.getGroupId());
                    map.put(todayZero, todayElectricProfitVO);
                } else {
                    // 添加今日的收益
                    ElectricProfitVO todayElectricProfitVO = getTodayBatteryProfit(projectId);
                    map.put(todayZero, todayElectricProfitVO);
                }
            }
        }
        return map;
    }

    /** 查询 分时缓存的 */
    private List<ElectricDynamicPeriodEntity> getTimeSharingProfit(
            ProfitRequest profitRequest, String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);

        List<ElectricDynamicPeriodEntity> results = new ArrayList<>();
        // 分时缓存 . 开启了分时换成的开关 并且 是中国的 , 并且 当前查询的时间段 是 单日的
        if (Boolean.TRUE.equals(projectEntity.getTimeSharingCache())
                && cacheUtils.isChina(projectEntity.getCountry())) {
            // 判断是 查询的单日的
            if (profitRequest.getStartDate() + EmsConstants.ONE_DAY_SECOND
                    == profitRequest.getEndDate() + 1) {
                // 判断是 查询的单日的
                results = getDynamicDetailEntityList(profitRequest, projectId, projectEntity);
            }
        }
        return results;
    }

    protected List<String> getIncomeDeviceIdsForGroup(String projectId, String groupId) {
        List<String> allDeviceIds = new ArrayList<>();
        List<GroupDeviceEntity> groupDeviceEntitys =
                groupDeviceService.lambdaQuery().eq(GroupDeviceEntity::getGroupId, groupId).list();
        List<String> deviceIds =
                groupDeviceEntitys.stream()
                        .map(GroupDeviceEntity::getDeviceId)
                        .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            List<DeviceEntity> deviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getIncome, true)
                            .eq(DeviceEntity::getProjectId, projectId)
                            .in(DeviceEntity::getId, deviceIds)
                            .list();
            deviceEntities.forEach(e -> allDeviceIds.add(e.getId()));
        }
        return allDeviceIds;
    }

    protected List<String> getIncomeAmmeterIdsForGroup(String projectId, String groupId) {
        List<String> allAmmeterIds = new ArrayList<>();
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService
                        .lambdaQuery()
                        .eq(GroupAmmeterEntity::getGroupId, groupId)
                        .list();
        List<String> ammeterIds =
                groupAmmeterEntities.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(ammeterIds)) {
            List<AmmeterEntity> ammeterEntities =
                    ammeterService
                            .lambdaQuery()
                            .eq(AmmeterEntity::getIncome, true)
                            .eq(AmmeterEntity::getProjectId, projectId)
                            .in(AmmeterEntity::getId, ammeterIds)
                            .list();
            ammeterEntities.forEach(e -> allAmmeterIds.add(e.getId()));
        }
        return allAmmeterIds;
    }

    private static void setProfitVoFromElectricProfitVo(
            ProfitVO profitVO, ElectricProfitVO electricProfitVoAll) {
        profitVO.setElectric_benefit(String.valueOf(electricProfitVoAll.getTotalBenefit()));
        profitVO.setFlat_charge_cost(String.valueOf(electricProfitVoAll.getFlatChargeCost()));
        profitVO.setFlat_charge_quantity(
                String.valueOf(electricProfitVoAll.getFlatChargeQuantity()));
        profitVO.setFlat_discharge_benefit(
                String.valueOf(electricProfitVoAll.getFlatDischargeBenefit()));
        profitVO.setFlat_discharge_quantity(
                String.valueOf(electricProfitVoAll.getFlatDischargeQuantity()));
        profitVO.setPeak_charge_cost(String.valueOf(electricProfitVoAll.getPeakChargeCost()));
        profitVO.setPeak_charge_quantity(
                String.valueOf(electricProfitVoAll.getPeakChargeQuantity()));
        profitVO.setPeak_discharge_benefit(
                String.valueOf(electricProfitVoAll.getPeakDischargeBenefit()));
        profitVO.setPeak_discharge_quantity(
                String.valueOf(electricProfitVoAll.getPeakDischargeQuantity()));
        profitVO.setVally_charge_cost(String.valueOf(electricProfitVoAll.getVallyChargeCost()));
        profitVO.setVally_charge_quantity(
                String.valueOf(electricProfitVoAll.getVallyChargeQuantity()));
        profitVO.setVally_discharge_benefit(
                String.valueOf(electricProfitVoAll.getVallyDischargeBenefit()));
        profitVO.setVally_discharge_quantity(
                String.valueOf(electricProfitVoAll.getVallyDischargeQuantity()));
        profitVO.setDeep_vally_charge_cost(
                String.valueOf(electricProfitVoAll.getDeepVallyChargeCost()));
        profitVO.setDeep_vally_charge_quantity(
                String.valueOf(electricProfitVoAll.getDeepVallyChargeQuantity()));
        profitVO.setDeep_vally_discharge_benefit(
                String.valueOf(electricProfitVoAll.getDeepVallyDischargeBenefit()));
        profitVO.setDeep_vally_discharge_quantity(
                String.valueOf(electricProfitVoAll.getDeepVallyDischargeQuantity()));
        profitVO.setTip_charge_cost(String.valueOf(electricProfitVoAll.getTipChargeCost()));
        profitVO.setTip_charge_quantity(String.valueOf(electricProfitVoAll.getTipChargeQuantity()));
        profitVO.setTip_discharge_benefit(
                String.valueOf(electricProfitVoAll.getTipDischargeBenefit()));
        profitVO.setTip_discharge_quantity(
                String.valueOf(electricProfitVoAll.getTipDischargeQuantity()));
        profitVO.setTotal_charge_cost(String.valueOf(electricProfitVoAll.getTotalChargeCost()));
        profitVO.setTotal_charge_quantity(
                String.valueOf(electricProfitVoAll.getTotalChargeQuantity()));
        profitVO.setTotal_discharge_benefit(
                String.valueOf(electricProfitVoAll.getTotalDischargeBenefit()));
        profitVO.setTotal_discharge_quantity(
                String.valueOf(electricProfitVoAll.getTotalDischargeQuantity()));
    }

    /**
     * 计算分组收益
     *
     * @param benefitRequest : 查询条件
     * @param projectEntity : 项目信息
     * @return : Map<String, GroupElectricProfit>
     */
    private Map<String, GroupElectricProfit> calGroupProfitVo(
            GroupEntity systemGroupEntity,
            List<GroupEntity> groupsNotSystems,
            BenefitRequest benefitRequest,
            ProjectEntity projectEntity,
            CountDownLatch countDownLatch) {
        Map<String, GroupElectricProfit> groupElectricProfitMap = new ConcurrentHashMap<>();
        groupsNotSystems.forEach(
                group ->
                        threadPoolTaskExecutor.submit(
                                () -> {
                                    ProfitRequest profitRequest = new ProfitRequest();
                                    profitRequest.setStartDate(benefitRequest.getStartDate());
                                    profitRequest.setEndDate(benefitRequest.getEndDate());
                                    profitRequest.setDeviceId("all");
                                    profitRequest.setGroupId(group.getId());
                                    // core 计算单个分组的收益
                                    //                                    ElectricProfitVO
                                    // groupElectricProfitVoAll =
                                    //                                            getBatteryProfit(
                                    //
                                    // profitRequest,
                                    //
                                    // projectEntity.getId(),
                                    //
                                    // systemGroupEntity);
                                    profitRequest.setProjectId(projectEntity.getId());
                                    ElectricProfitVO groupElectricProfitVoAll =
                                            electricProfitService.getElectricTotalProfit(
                                                    profitRequest);
                                    Double groupTotalBenefit =
                                            groupElectricProfitVoAll.getTotalBenefit();
                                    groupElectricProfitMap.put(
                                            group.getId(),
                                            new GroupElectricProfit()
                                                    .setGroupName(group.getName())
                                                    .setGroupElectricBenefit(groupTotalBenefit));
                                    countDownLatch.countDown();
                                }));
        return groupElectricProfitMap;
    }

    protected ProfitVO getDemandBenefit(
            Map<String, Map<Long, Double>> controlPowerMap, List<ElectricPriceEntity> list) {
        ProfitVO profitVO = new ProfitVO();
        if (controlPowerMap.isEmpty() || list == null || list.isEmpty()) {
            profitVO.setDemand_control_power("0");
            profitVO.setDemand_control_benefit("0");
            profitVO.setDemand_control_price("0");
        } else {
            Set<String> groupIds = controlPowerMap.keySet();
            List<GroupEntity> groupEntities =
                    groupService.lambdaQuery().in(GroupEntity::getId, groupIds).list();
            Map<String, String> groupNameMap =
                    groupEntities.stream()
                            .collect(Collectors.toMap(GroupEntity::getId, GroupEntity::getName));
            double controlPower = 0;
            double controlBenefit = 0;
            Map<String, GroupDemandProfit> demandMap = new HashMap<>(16);
            for (String groupId : controlPowerMap.keySet()) {
                double totalPower = 0;
                double totalProfit = 0;
                GroupDemandProfit groupDemandProfit = new GroupDemandProfit();
                Map<Long, Double> dMap = controlPowerMap.get(groupId);
                for (Long time : dMap.keySet()) {
                    double power = dMap.get(time);
                    for (ElectricPriceEntity electricPriceEntity : list) {
                        boolean isToNow =
                                (time >= electricPriceEntity.getStartDate()
                                        && electricPriceEntity.getEndDate() == null);
                        boolean isBetween =
                                isToNow
                                        || (time >= electricPriceEntity.getStartDate()
                                                && time <= electricPriceEntity.getEndDate());
                        if (isBetween) {
                            totalPower += power;
                            controlPower += power;
                            totalProfit += power * electricPriceEntity.getDemandPrice();
                            controlBenefit += power * electricPriceEntity.getDemandPrice();
                            groupDemandProfit.setDemandControlPrice(
                                    electricPriceEntity.getDemandPrice());
                            break;
                        }
                    }
                }
                groupDemandProfit.setGroupName(groupNameMap.get(groupId));
                groupDemandProfit.setDemandControlPower(totalPower);
                groupDemandProfit.setDemandControlBenefit(totalProfit);
                demandMap.put(groupId, groupDemandProfit);
            }
            profitVO.setDemandProfitMap(demandMap);
            profitVO.setDemand_control_power(String.valueOf(controlPower));
            profitVO.setDemand_control_benefit(String.valueOf(controlBenefit));
        }
        return profitVO;
    }

    /**
     * 1.4.2 想把这个给去掉 使用 系统运营收益的接口来代替 当前月份的 收益 <br>
     * 1.电收益 + pv + wind + waster + demand
     *
     * <p>*
     *
     * @return : 当前月份的收益 TotalProfitVO
     */
    @Deprecated
    public TotalProfitVO monthProfit(ElectricProfitVO todayElectricProfitVo, String projectId) {
        TotalProfitVO monthVO = new TotalProfitVO();
        ProfitVO profitVO = new ProfitVO();
        if (todayElectricProfitVo == null) {
            todayElectricProfitVo = new ElectricProfitVO();
            todayElectricProfitVo.init();
            //            return monthVO;
        }
        // 查询到系统分组
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectId);
        // 好像是项目刚启动 可能会导致electricProfitVoAll 为空 应该没来得及生成数据
        ElectricProfitVO electricMonthProfitVo =
                TaskOperationTimer.monthElectricMap.get(projectId) == null
                        ? new ElectricProfitVO().init()
                        : TaskOperationTimer.monthElectricMap.get(projectId);
        profitVO.setElectric_benefit(
                String.valueOf(
                        electricMonthProfitVo.getTotalBenefit()
                                + todayElectricProfitVo.getTotalBenefit()));
        profitVO.setTotal_charge_cost(
                String.valueOf(
                        electricMonthProfitVo.getTotalChargeCost()
                                + todayElectricProfitVo.getTotalChargeCost()));
        profitVO.setTotal_charge_quantity(
                String.valueOf(
                        electricMonthProfitVo.getTotalChargeQuantity()
                                + todayElectricProfitVo.getTotalChargeQuantity()));
        profitVO.setTotal_discharge_benefit(
                String.valueOf(
                        electricMonthProfitVo.getTotalDischargeBenefit()
                                + todayElectricProfitVo.getTotalDischargeBenefit()));
        profitVO.setTotal_discharge_quantity(
                String.valueOf(
                        electricMonthProfitVo.getTotalDischargeQuantity()
                                + todayElectricProfitVo.getTotalDischargeQuantity()));
        ProfitVO monthProfitPvVO = TaskOperationTimer.monthPvMap.get(projectId);
        if (monthProfitPvVO == null) {
            monthProfitPvVO = new ProfitVO();
            monthProfitPvVO.setPv_discharge_quantity("0");
            monthProfitPvVO.setPv_discharge_benefit("0");
        }
        ProfitVO monthWindProfitVo = TaskOperationTimer.monthWindMap.get(projectId);
        if (monthWindProfitVo == null) {
            monthWindProfitVo = new ProfitVO();
            monthWindProfitVo.setWind_discharge_benefit("0");
            monthWindProfitVo.setWind_discharge_quantity("0");
        }
        ProfitVO monthWasterProfitVo = TaskOperationTimer.monthWasterMap.get(projectId);
        if (monthWasterProfitVo == null) {
            monthWasterProfitVo = new ProfitVO();
            monthWasterProfitVo.setWaster_discharge_benefit("0");
            monthWasterProfitVo.setWaster_discharge_quantity("0");
        }
        ProfitVO monthDemandProfitVo = TaskOperationTimer.monthDemandControlMap.get(projectId);
        if (monthDemandProfitVo == null) {
            monthDemandProfitVo = new ProfitVO();
            monthDemandProfitVo.setDemand_control_benefit("0");
            monthDemandProfitVo.setDemand_control_power("0");
        }
        Double totalBenefit =
                Double.parseDouble(profitVO.getElectric_benefit())
                        + Double.parseDouble(monthDemandProfitVo.getDemand_control_benefit())
                        + Double.parseDouble(
                                monthWindProfitVo.getWind_discharge_benefit()
                                        + Double.parseDouble(
                                                monthWasterProfitVo.getWaster_discharge_benefit()));
        // 2023-11-17 10:24:55 add 根据pv收益开关去 判断是否要把 pv收益计算进去
        if (systemGroupEntity != null) {
            if (systemGroupEntity.getPvProfitController()) {
                // 虽然这个 月份总收益目前前端还没地方展示 但是这里还是要和其他保持统一 getPvProfitController
                totalBenefit += Double.parseDouble(monthProfitPvVO.getPv_discharge_benefit());
            }
        }
        monthVO.setDemand_control_benefit(monthDemandProfitVo.getDemand_control_benefit());
        monthVO.setDemand_control_power(monthDemandProfitVo.getDemand_control_power());
        monthVO.setPv_discharge_benefit(monthProfitPvVO.getPv_discharge_benefit());
        monthVO.setPv_discharge_quantity(monthProfitPvVO.getPv_discharge_quantity());
        monthVO.setWind_discharge_benefit(monthWindProfitVo.getWind_discharge_benefit());
        monthVO.setWind_discharge_quantity(monthWindProfitVo.getWind_discharge_quantity());
        monthVO.setTotal_charge_quantity(profitVO.getTotal_charge_quantity());
        monthVO.setTotal_charge_cost(profitVO.getTotal_charge_cost());
        monthVO.setTotal_discharge_benefit(profitVO.getTotal_discharge_benefit());
        monthVO.setTotal_discharge_quantity(profitVO.getTotal_discharge_quantity());
        monthVO.setElectric_benefit(profitVO.getElectric_benefit());
        monthVO.setTotal_benefit(String.valueOf(totalBenefit));
        log.info("项目名称:{},monthVo :{}", projectId, monthVO);
        return monthVO;
    }

    /**
     * 累计的 收益
     *
     * @return : 累计收益 TotalProfitVO
     */
    public TotalProfitVO totalProfit(ElectricProfitVO todayElectricProfitVo, String projectId) {
        TotalProfitVO totalVO = new TotalProfitVO();
        ProfitVO profitVO = new ProfitVO();
        if (todayElectricProfitVo == null) {
            return totalVO;
        }
        // 查询到系统分组
        GroupEntity groupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                                .eq(GroupEntity::getWhetherSystem, true));
        // 项目初始时间到昨天的数据
        ElectricProfitVO electricProfitVoAll = TaskOperationTimer.totalElectricMap.get(projectId);
        if (TaskOperationTimer.totalElectricMap.get(projectId) == null) {
            electricProfitVoAll = new ElectricProfitVO();
            electricProfitVoAll.init();
        }
        profitVO.setElectric_benefit(
                String.valueOf(
                        electricProfitVoAll.getTotalBenefit()
                                + todayElectricProfitVo.getTotalBenefit()));
        profitVO.setFlat_charge_cost(
                String.valueOf(
                        electricProfitVoAll.getFlatChargeCost()
                                + todayElectricProfitVo.getFlatChargeCost()));
        profitVO.setFlat_charge_quantity(
                String.valueOf(
                        electricProfitVoAll.getFlatChargeQuantity()
                                + todayElectricProfitVo.getFlatChargeQuantity()));
        profitVO.setFlat_discharge_benefit(
                String.valueOf(
                        electricProfitVoAll.getFlatDischargeBenefit()
                                + todayElectricProfitVo.getFlatDischargeBenefit()));
        profitVO.setFlat_discharge_quantity(
                String.valueOf(
                        electricProfitVoAll.getFlatDischargeQuantity()
                                + todayElectricProfitVo.getFlatDischargeBenefit()));
        profitVO.setPeak_charge_cost(
                String.valueOf(
                        electricProfitVoAll.getPeakChargeCost()
                                + todayElectricProfitVo.getPeakChargeCost()));
        profitVO.setPeak_charge_quantity(
                String.valueOf(
                        electricProfitVoAll.getPeakChargeQuantity()
                                + todayElectricProfitVo.getPeakChargeQuantity()));
        profitVO.setPeak_discharge_benefit(
                String.valueOf(
                        electricProfitVoAll.getPeakDischargeBenefit()
                                + todayElectricProfitVo.getPeakDischargeBenefit()));
        profitVO.setPeak_discharge_quantity(
                String.valueOf(
                        electricProfitVoAll.getPeakDischargeQuantity()
                                + todayElectricProfitVo.getPeakDischargeQuantity()));
        profitVO.setVally_charge_cost(
                String.valueOf(
                        electricProfitVoAll.getVallyChargeCost()
                                + todayElectricProfitVo.getVallyChargeCost()));
        profitVO.setVally_charge_quantity(
                String.valueOf(
                        electricProfitVoAll.getVallyChargeQuantity()
                                + todayElectricProfitVo.getVallyChargeQuantity()));
        profitVO.setVally_discharge_benefit(
                String.valueOf(
                        electricProfitVoAll.getVallyDischargeBenefit()
                                + todayElectricProfitVo.getVallyDischargeBenefit()));
        profitVO.setVally_discharge_quantity(
                String.valueOf(
                        electricProfitVoAll.getVallyDischargeQuantity()
                                + todayElectricProfitVo.getVallyDischargeQuantity()));
        profitVO.setDeep_vally_charge_cost(
                String.valueOf(
                        electricProfitVoAll.getDeepVallyChargeCost()
                                + todayElectricProfitVo.getDeepVallyChargeCost()));
        profitVO.setDeep_vally_charge_quantity(
                String.valueOf(
                        electricProfitVoAll.getDeepVallyChargeQuantity()
                                + todayElectricProfitVo.getDeepVallyChargeQuantity()));
        profitVO.setDeep_vally_discharge_benefit(
                String.valueOf(
                        electricProfitVoAll.getDeepVallyDischargeBenefit()
                                + todayElectricProfitVo.getDeepVallyDischargeBenefit()));
        profitVO.setDeep_vally_discharge_quantity(
                String.valueOf(
                        electricProfitVoAll.getDeepVallyDischargeQuantity()
                                + todayElectricProfitVo.getDeepVallyDischargeQuantity()));
        profitVO.setTip_charge_cost(
                String.valueOf(
                        electricProfitVoAll.getTipChargeCost()
                                + todayElectricProfitVo.getTipChargeCost()));
        profitVO.setTip_charge_quantity(
                String.valueOf(
                        electricProfitVoAll.getTipChargeQuantity()
                                + todayElectricProfitVo.getTipChargeQuantity()));
        profitVO.setTip_discharge_benefit(
                String.valueOf(
                        electricProfitVoAll.getTipDischargeBenefit()
                                + todayElectricProfitVo.getTipDischargeBenefit()));
        profitVO.setTip_discharge_quantity(
                String.valueOf(
                        electricProfitVoAll.getTipDischargeQuantity()
                                + todayElectricProfitVo.getTipDischargeQuantity()));
        profitVO.setTotal_charge_cost(
                String.valueOf(
                        electricProfitVoAll.getTotalChargeCost()
                                + todayElectricProfitVo.getTotalChargeCost()));
        profitVO.setTotal_charge_quantity(
                String.valueOf(
                        electricProfitVoAll.getTotalChargeQuantity()
                                + todayElectricProfitVo.getTotalChargeQuantity()));
        profitVO.setTotal_discharge_benefit(
                String.valueOf(
                        electricProfitVoAll.getTotalDischargeBenefit()
                                + todayElectricProfitVo.getTotalDischargeBenefit()));
        profitVO.setTotal_discharge_quantity(
                String.valueOf(
                        electricProfitVoAll.getTotalDischargeQuantity()
                                + todayElectricProfitVo.getTotalDischargeQuantity()));
        ProfitVO profitPvVO = TaskOperationTimer.totalPvMap.get(projectId);
        ProfitVO profitDemandVO = TaskOperationTimer.demandControlMap.get(projectId);
        if (TaskOperationTimer.totalPvMap.get(projectId) == null) {
            profitPvVO = new ProfitVO();
            profitPvVO.setPv_discharge_quantity("0");
            profitPvVO.setPv_discharge_benefit("0");
        }
        ProfitVO profitWindVO = TaskOperationTimer.totalWindMap.get(projectId);
        if (TaskOperationTimer.totalWindMap.get(projectId) == null) {
            profitWindVO = new ProfitVO();
            profitWindVO.setWind_discharge_quantity("0");
            profitWindVO.setWind_discharge_benefit("0");
        }
        if (TaskOperationTimer.demandControlMap.get(projectId) == null) {
            profitDemandVO = new ProfitVO();
            profitDemandVO.setDemand_control_benefit("0");
            profitDemandVO.setDemand_control_power("0");
        }
        Double totalBenefit =
                Double.parseDouble(profitVO.getElectric_benefit())
                        + Double.parseDouble(profitDemandVO.getDemand_control_benefit())
                        + Double.parseDouble(profitWindVO.getWind_discharge_benefit());
        // 2023-11-17 10:24:55 add 根据pv收益开关去 判断是否要把 pv收益计算进去
        if (groupEntity != null) {
            if (groupEntity.getPvProfitController()) {
                totalBenefit += Double.parseDouble(profitPvVO.getPv_discharge_benefit());
            }
        }
        totalVO.setDemand_control_benefit(profitDemandVO.getDemand_control_benefit());
        totalVO.setDemand_control_power(profitDemandVO.getDemand_control_power());
        totalVO.setPv_discharge_benefit(profitPvVO.getPv_discharge_benefit());
        totalVO.setPv_discharge_quantity(profitPvVO.getPv_discharge_quantity());
        totalVO.setWind_discharge_benefit(profitWindVO.getWind_discharge_benefit());
        totalVO.setWind_discharge_quantity(profitWindVO.getWind_discharge_quantity());
        totalVO.setTotal_charge_quantity(profitVO.getTotal_charge_quantity());
        totalVO.setTotal_charge_cost(profitVO.getTotal_charge_cost());
        totalVO.setTotal_discharge_benefit(profitVO.getTotal_discharge_benefit());
        totalVO.setTotal_discharge_quantity(profitVO.getTotal_discharge_quantity());
        totalVO.setElectric_benefit(String.valueOf(profitVO.getElectric_benefit()));
        totalVO.setTotal_benefit(String.valueOf(totalBenefit));
        return totalVO;
    }

    //    @Override
    //    @SuppressWarnings("unchecked")
    //    public Map<String, Object> getAllTotalProfitVO() throws InterruptedException {
    //        String key = "profit:" + WebUtils.projectId.get();
    //        Map<String, Object> oldMap = new HashMap<>();
    //        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
    //        Map<String, Object> map =
    //                getAllTotalProfitMap(
    //                        projectEntity,
    //                        TimePointEnum.TODAY,
    //                        TimePointEnum.YESTERDAY,
    //                        TimePointEnum.TOTAL);
    //        redisTemplate.opsForValue().set(key, JSON.toJSONString(map), 10, TimeUnit.SECONDS);
    //        return map;
    //    }

    //    @Override
    //    @SuppressWarnings("unchecked")
    //    public Map<String, Object> getAllTotalProfitVoForLargeScreen(TimePointEnum timePointEnum)
    //            throws InterruptedException {
    //        String key = "profit:" + timePointEnum.getValue() + ":" + WebUtils.projectId.get();
    //        Map<String, Object> oldMap =
    //                JSON.parseObject(redisTemplate.opsForValue().get(key), Map.class);
    //        if (oldMap == null || oldMap.isEmpty()) {
    //            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
    //            Map<String, Object> map = getAllTotalProfitMapFromCache(projectEntity,
    // timePointEnum);
    //            redisTemplate.opsForValue().set(key, JSON.toJSONString(map), 10,
    // TimeUnit.SECONDS);
    //            return map;
    //        } else {
    //            return oldMap;
    //        }
    //    }

    //    @Override
    public ProfitVO getPvProfitVO(BenefitRequest benefitRequest, String projectId) {
        //        ProfitVO profitVO = new ProfitVO();
        //        profitVO.setPv_discharge_quantity("0.0");
        //        profitVO.setPv_discharge_benefit("0.0");
        //        profitVO.setPvProfitVo(new RenewableProfitVo());
        //        boolean pvFlag = true;
        //        boolean hasDcdc;
        //        boolean dcdcProfit;
        //        boolean dcdcFlag;
        //        GroupEntity groupEntity =
        //                groupService.getOne(
        //                        Wrappers.lambdaQuery(GroupEntity.class)
        //                                .eq(GroupEntity::getWhetherSystem, 1)
        //                                .eq(GroupEntity::getProjectId, projectId));
        //        // 2024-01-17 09:25:04 add 得到 是否开启dcdc收益开关
        //        dcdcProfit = groupEntity.getDcdcProfitController();
        //        // 得到是否开启dcdc
        //        List<DeviceEntity> deviceEntityListlist =
        //                deviceService
        //                        .lambdaQuery()
        //                        .eq(DeviceEntity::getUnreal, false)
        //                        .eq(DeviceEntity::getProjectId, projectId)
        //                        .list();
        //        hasDcdc = deviceEntityListlist.stream().anyMatch(DeviceEntity::getHasDcdc);
        //        dcdcFlag = hasDcdc && dcdcProfit;
        //        if (groupEntity.getPhotovoltaicController() == null
        //                || !groupEntity.getPhotovoltaicController()) {
        //            pvFlag = false;
        //        }
        //        if (groupEntity.getPvProfitController() == null ||
        // !groupEntity.getPvProfitController()) {
        //            pvFlag = false;
        //        }
        //        // 既没有 pv光伏模块 也没有dcdc
        //        if (!pvFlag && !dcdcFlag) {
        //            return profitVO;
        //        }
        //        ProjectEntity projectEntity = projectService.getById(projectId);
        //        // 如果开启了pv
        //        RenewableProfitVo pvProfitVo =
        //                chooser.choose(projectEntity.getElectricPriceType())
        //                        .calculateRenewableProfitVo(
        //                                benefitRequest, CalculateTypeEnum.PV.name(),
        // projectEntity);
        //        profitVO.setPvProfitVo(pvProfitVo);
        //        if (groupEntity.getPhotovoltaicModel() != null) {
        //            PvWindModelEnum model =
        // PvWindModelEnum.getModel(groupEntity.getPhotovoltaicModel());
        //            // 把收益汇聚一下 根据 不同的 model 去取不同的 收益字段放入 profitVO的 setXxx_discharge_benefit字段
        //            renewableModelPriceFormulaComponent.modelBenefitCollect(
        //                    CalculateTypeEnum.PV,
        //                    model,
        //                    pvProfitVo,
        //                    new
        // RenewableModelPriceFormulaComponent.ModelBenefitCollectPostProcessor() {
        //                        @Override
        //                        public void pvPostProcessor(double benefit) {
        //                            profitVO.setPv_discharge_benefit(String.valueOf(benefit));
        //                        }
        //                    });
        //        }
        //
        // profitVO.setPv_discharge_quantity(String.valueOf(pvProfitVo.getTotalDischargeQuantity()));
        //        return profitVO;
        return null;
    }

    //    @Override
    public ProfitVO getWindProfitVO(BenefitRequest benefitRequest, String projectId) {
        //        ProfitVO profitVO = new ProfitVO();
        //        profitVO.setWind_discharge_quantity("0");
        //        profitVO.setWind_discharge_benefit("0");
        //        profitVO.setWindProfitVo(new RenewableProfitVo());
        //        GroupEntity groupEntity =
        //                groupService.getOne(
        //                        Wrappers.lambdaQuery(GroupEntity.class)
        //                                .eq(GroupEntity::getWhetherSystem, 1)
        //                                .eq(GroupEntity::getProjectId, projectId));
        //        if (groupEntity == null
        //                || groupEntity.getEnableWindPowerGeneration() == null
        //                || !groupEntity.getEnableWindPowerGeneration()) {
        //            return profitVO;
        //        }
        //        ProjectEntity projectEntity = projectService.getById(projectId);
        //        //        long yesterdayZero =
        //        //                MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone())
        //        //                        - EmsConstants.ONE_DAY_SECOND;
        //        //        // 找到 那个匹配到的 电价配置策略
        //        //        ElectricPriceEntity electricPriceParent =
        //        //                electricPriceService.getElectricPriceMatch(projectId,
        // yesterdayZero);
        //        //        if (electricPriceParent == null) {
        //        //            return profitVO;
        //        //            // throw new
        // ServiceException(ErrorResultCode.ELECTRIC_NOT_CONFIG.value());
        //        //        }
        //        RenewableProfitVo windProfitVo =
        //                chooser.choose(projectEntity.getElectricPriceType())
        //                        .calculateRenewableProfitVo(
        //                                benefitRequest, CalculateTypeEnum.WIND.name(),
        // projectEntity);
        //        profitVO.setWindProfitVo(windProfitVo);
        //        if (groupEntity.getPhotovoltaicModel() != null) {
        //            // 注意这是根据 getWindPowerModel
        //            PvWindModelEnum model =
        // PvWindModelEnum.getModel(groupEntity.getWindPowerModel());
        //            // 把收益汇聚一下 根据 不同的 model 去取不同的 收益字段放入 profitVO的 setWind_discharge_benefit字段
        //            renewableModelPriceFormulaComponent.modelBenefitCollect(
        //                    CalculateTypeEnum.WIND,
        //                    model,
        //                    windProfitVo,
        //                    new
        // RenewableModelPriceFormulaComponent.ModelBenefitCollectPostProcessor() {
        //                        @Override
        //                        public void renewableBenefitPostProcessor(double benefit) {
        //                            profitVO.setWind_discharge_benefit(String.valueOf(benefit));
        //                        }
        //                    });
        //        }
        //        profitVO.setWind_discharge_quantity(
        //                String.valueOf(windProfitVo.getTotalDischargeQuantity()));
        //        return profitVO;
        return null;
    }

    @Override
    public ProfitVO getDemandProfitVO(BenefitRequest benefitRequest, String projectId) {
        // 需量功率与收益
        // 这个地方慢 那么就可以从 db 里面查询
        //        Map<String, Map<Long, Double>> controlPowerMap =
        //                getDemandIncomePower(
        //                        benefitRequest.getStartDate(), benefitRequest.getEndDate(),
        // projectId);
        Map<String, Map<Long, Double>> controlPowerMap =
                getDemandIncomePowerFromDb(
                        new RangeRequest()
                                .setStartDate(benefitRequest.getStartDate())
                                .setEndDate(benefitRequest.getEndDate()),
                        projectId);
        ProfitVO tempProfitVo = new ProfitVO();
        tempProfitVo.setDemand_control_power("0");
        tempProfitVo.setDemand_control_benefit("0");
        if (controlPowerMap.isEmpty()) {
            return tempProfitVo;
        }
        // 1.3.7 如果是实时电价 直接没有 需量收益 去计算  直接返回
        //        if
        // (ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name().equals(byId.getElectricPriceType())) {
        //            return tempProfitVo;
        //        }
        // 需量收益这里 还是直接查询以前的
        List<ElectricPriceEntity> list = electricPriceService.getPriceList(projectId);
        // List<ElectricPriceEntity> list = electricPriceService.getPriceList(projectId);
        return getDemandBenefit(controlPowerMap, list);
    }

    protected static void calcPeriodProfit(
            ElectricPriceEntity price,
            Integer type,
            ElectricProfitVO electricProfitVO,
            double outDiff,
            double inDiff) {
        switch (type) {
                // 电价时段（谷1 平2 峰3 尖4）
            case 1:
                // 充电成本
                electricProfitVO.setVallyChargeQuantity(
                        electricProfitVO.getVallyChargeQuantity() + inDiff);
                double vallyChargeCost =
                        price.getBuyPrice() * electricProfitVO.getVallyChargeQuantity();
                //                double vallyChargeCost =
                //                        price.getPrice() *
                // electricProfitVO.getVally_charge_quantity();
                electricProfitVO.setVallyChargeCost(vallyChargeCost);
                // 放电收益
                electricProfitVO.setVallyDischargeQuantity(
                        electricProfitVO.getVallyDischargeQuantity() + outDiff);
                //                double vallyDischargeBenefit =
                //                        price.getPrice() *
                // electricProfitVO.getVally_discharge_quantity();
                double vallyDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getVallyDischargeQuantity();
                electricProfitVO.setVallyDischargeBenefit(vallyDischargeBenefit);
                break;
                // 电价时段（谷1 平2 峰3 尖4）
            case 2:
                // 充电成本
                electricProfitVO.setFlatChargeQuantity(
                        electricProfitVO.getFlatChargeQuantity() + inDiff);
                //                double flatChargeCost =
                //                        price.getPrice() *
                // electricProfitVO.getFlat_charge_quantity();

                double flatChargeCost =
                        price.getBuyPrice() * electricProfitVO.getFlatChargeQuantity();
                electricProfitVO.setFlatChargeCost(flatChargeCost);
                // 放电收益
                electricProfitVO.setFlatDischargeQuantity(
                        electricProfitVO.getFlatDischargeQuantity() + outDiff);
                //                double flatDischargeBenefit =
                //                        price.getPrice() *
                // electricProfitVO.getFlat_discharge_quantity();
                double flatDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getFlatDischargeQuantity();
                electricProfitVO.setFlatDischargeBenefit(flatDischargeBenefit);
                break;
                // 电价时段（谷1 平2 峰3 尖4）
            case 3:
                // 充电成本
                electricProfitVO.setPeakChargeQuantity(
                        electricProfitVO.getPeakChargeQuantity() + inDiff);
                //                double peakChargeCost =
                //                        price.getPrice() *
                // electricProfitVO.getPeak_charge_quantity();
                double peakChargeCost =
                        price.getBuyPrice() * electricProfitVO.getPeakChargeQuantity();
                electricProfitVO.setPeakChargeCost(peakChargeCost);
                // 放电收益
                electricProfitVO.setPeakDischargeQuantity(
                        electricProfitVO.getPeakDischargeQuantity() + outDiff);
                //                double peakDischargeBenefit =
                //                        price.getPrice() *
                // electricProfitVO.getPeak_discharge_quantity();
                double peakDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getPeakDischargeQuantity();
                electricProfitVO.setPeakDischargeBenefit(peakDischargeBenefit);
                break;
                // 电价时段（谷1 平2 峰3 尖4）
            case 4:
                // 充电成本
                electricProfitVO.setTipChargeQuantity(
                        electricProfitVO.getTipChargeQuantity() + inDiff);
                double tipChargeCost =
                        price.getBuyPrice() * electricProfitVO.getTipChargeQuantity();
                electricProfitVO.setTipChargeCost(tipChargeCost);
                // 放电收益
                electricProfitVO.setTipDischargeQuantity(
                        electricProfitVO.getTipDischargeQuantity() + outDiff);
                double tipDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getTipDischargeQuantity();
                electricProfitVO.setTipDischargeBenefit(tipDischargeBenefit);
                break;
            case 5:
                // 充电成本
                electricProfitVO.setDeepVallyChargeQuantity(
                        electricProfitVO.getDeepVallyChargeQuantity() + inDiff);
                double deepVallyChargeCost =
                        price.getBuyPrice() * electricProfitVO.getDeepVallyChargeQuantity();
                electricProfitVO.setDeepVallyChargeCost(deepVallyChargeCost);
                // 放电收益
                electricProfitVO.setDeepVallyDischargeQuantity(
                        electricProfitVO.getDeepVallyDischargeQuantity() + outDiff);
                double deepVallyDischargeBenefit =
                        price.getSellPrice() * electricProfitVO.getDeepVallyDischargeQuantity();
                electricProfitVO.setDeepVallyDischargeBenefit(deepVallyDischargeBenefit);
                break;
            default:
        }
    }

    protected static void setElectricProfitVoTotal(ElectricProfitVO electricProfitVO) {
        // 总充电成本
        double totalChargeCost =
                electricProfitVO.getFlatChargeCost()
                        + electricProfitVO.getVallyChargeCost()
                        + electricProfitVO.getPeakChargeCost()
                        + electricProfitVO.getTipChargeCost()
                        + electricProfitVO.getDeepVallyChargeCost();
        electricProfitVO.setTotalChargeCost(totalChargeCost);
        double totalChargeQuantity =
                electricProfitVO.getFlatChargeQuantity()
                        + electricProfitVO.getVallyChargeQuantity()
                        + electricProfitVO.getPeakChargeQuantity()
                        + electricProfitVO.getTipChargeQuantity()
                        + electricProfitVO.getDeepVallyChargeQuantity();
        electricProfitVO.setTotalChargeQuantity(totalChargeQuantity);
        // 总放电收益
        double totalDischargeQuantity =
                electricProfitVO.getFlatDischargeQuantity()
                        + electricProfitVO.getVallyDischargeQuantity()
                        + electricProfitVO.getPeakDischargeQuantity()
                        + electricProfitVO.getTipDischargeQuantity()
                        + electricProfitVO.getDeepVallyDischargeQuantity();
        electricProfitVO.setTotalDischargeQuantity(totalDischargeQuantity);
        double totalDischargeBenefit =
                electricProfitVO.getFlatDischargeBenefit()
                        + electricProfitVO.getVallyDischargeBenefit()
                        + electricProfitVO.getPeakDischargeBenefit()
                        + electricProfitVO.getTipDischargeBenefit()
                        + electricProfitVO.getDeepVallyDischargeBenefit();
        electricProfitVO.setTotalDischargeBenefit(totalDischargeBenefit);
        // 总收益
        electricProfitVO.setTotalBenefit(totalDischargeBenefit - totalChargeCost);
    }

    public void calculateTotal(ElectricEntity electricEntity) {
        double totalChargeCost =
                electricEntity.getFlatChargeCost()
                        + electricEntity.getVallyChargeCost()
                        + electricEntity.getPeakChargeCost()
                        + electricEntity.getTipChargeCost()
                        + electricEntity.getDeepVallyChargeCost();
        electricEntity.setTotalChargeCost(totalChargeCost);
        double totalChargeQuantity =
                electricEntity.getFlatChargeQuantity()
                        + electricEntity.getVallyChargeQuantity()
                        + electricEntity.getPeakChargeQuantity()
                        + electricEntity.getTipChargeQuantity()
                        + electricEntity.getDeepVallyChargeQuantity();
        electricEntity.setTotalChargeQuantity(totalChargeQuantity);
        // 总放电收益
        double totalDischargeQuantity =
                electricEntity.getFlatDischargeQuantity()
                        + electricEntity.getVallyDischargeQuantity()
                        + electricEntity.getPeakDischargeQuantity()
                        + electricEntity.getTipDischargeQuantity()
                        + electricEntity.getDeepVallyDischargeQuantity();
        electricEntity.setTotalDischargeQuantity(totalDischargeQuantity);
        double totalDischargeBenefit =
                electricEntity.getFlatDischargeBenefit()
                        + electricEntity.getVallyDischargeBenefit()
                        + electricEntity.getPeakDischargeBenefit()
                        + electricEntity.getTipDischargeBenefit()
                        + electricEntity.getDeepVallyDischargeBenefit();
        electricEntity.setTotalDischargeBenefit(totalDischargeBenefit);
        // 总收益
        double totalBenefit = totalDischargeBenefit - totalChargeCost;
        electricEntity.setTotalBenefit(totalBenefit);
    }

    @NotNull
    public ElectricEntity getElectricEntity(
            int offsetSeconds, long oneDayZero, String projectId, String deviceId) {
        ElectricEntity electricEntity = new ElectricEntity();
        electricEntity.setTime(oneDayZero);
        electricEntity.setProjectId(projectId);
        LocalDateTime localDateTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(oneDayZero),
                        ZoneOffset.ofTotalSeconds(offsetSeconds));
        electricEntity.setYear(localDateTime.getYear());
        electricEntity.setMonth(localDateTime.getMonthValue());
        electricEntity.setDay(localDateTime.getDayOfMonth());
        electricEntity.setDeviceId(deviceId);
        return electricEntity;
    }

    public abstract @NotNull String getYesterdayProfitQueryString(
            long offsetSeconds, String projectId);

    private boolean exist(String projectId, String deviceId, long oneDayZero) {
        ElectricEntity electricEntityExist =
                electricService
                        .lambdaQuery()
                        .eq(ElectricEntity::getProjectId, projectId)
                        .eq(ElectricEntity::getDeviceId, deviceId)
                        .eq(ElectricEntity::getTime, oneDayZero)
                        .one();
        return electricEntityExist != null;
    }
}
