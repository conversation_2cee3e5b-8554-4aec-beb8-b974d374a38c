package com.wifochina.modules.income.timer;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.carbon.service.CarbonService;
import com.wifochina.modules.collect.CacheElectricCollectVo;
import com.wifochina.modules.collect.ElectricCollectVo;
import com.wifochina.modules.demand.service.GroupDemandMonthIncomeService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.ElectricProfitService;
import com.wifochina.modules.income.IIncomeMemoryCacheServiceKt;
import com.wifochina.modules.income.NewOperationProfitService;
import com.wifochina.modules.income.cache.memorycache.IIncomeMemoryCacheService;
import com.wifochina.modules.income.caculate.pvwind.IPvWindIncomeCalculateService;
import com.wifochina.modules.income.query.pvwind.RenewableIncomeQueryService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.income.vo.OperationProfitVo;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.VO.TotalProfitVO;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.modules.report.service.DayReportCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @since 9/23/2022 9:51 AM
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskOperationTimer {

    private final ProjectService projectService;
    private final IIncomeMemoryCacheService incomeMemoryCacheService;
    private final IIncomeMemoryCacheServiceKt incomeMemoryCacheServiceKt;
    private final GroupService groupService;
    private final GroupDemandMonthIncomeService groupDemandMonthIncomeService;
    private final ElectricProfitService electricProfitService;
    private final NewOperationProfitService newOperationProfitService;
    public static Map<String, ElectricProfitVO> yesElectricMap = new HashMap<>();
    public static Map<String, ElectricProfitVO> totalElectricMap = new HashMap<>();
    public static Map<String, ElectricProfitVO> monthElectricMap = new HashMap<>();
    public static Map<String, ElectricProfitVO> todayElectricMap = new ConcurrentHashMap<>();
    public static Map<String, ProfitVO> yesPvMap = new HashMap<>();
    public static Map<String, ProfitVO> totalPvMap = new HashMap<>();
    public static Map<String, ProfitVO> yesWindMap = new HashMap<>();
    public static Map<String, ProfitVO> yesWasterMap = new HashMap<>();
    public static Map<String, ProfitVO> totalWindMap = new HashMap<>();
    public static Map<String, ProfitVO> totalWasterMap = new HashMap<>();
    public static Map<String, OperationProfitVo> newTodayProfitMap = new ConcurrentHashMap<>();

    /** 这个是totalDemandControlMap */
    public static Map<String, ProfitVO> demandControlMap = new HashMap<>();

    public static Map<String, ProfitVO> monthDemandControlMap = new HashMap<>();
    public static Map<String, ProfitVO> monthPvMap = new HashMap<>();
    public static Map<String, ProfitVO> monthWindMap = new HashMap<>();
    public static Map<String, ProfitVO> monthWasterMap = new HashMap<>();

    /** 近一周的 收益 */
    public static Map<String, List<ProfitVO>> recentWeek = new ConcurrentHashMap<>();

    private static final Map<String, Long> DEMAND_MAP = new HashMap<>(50);
    public static Map<String, ElectricCollectVo> todayElectricCollectMap = new HashMap<>();

    @Scheduled(cron = "0 10/30 * * * ?")
    @Async("asyncServiceExecutor")
    public void updateHistory() {
        log.info("{} -->正在执行", Thread.currentThread().getName() + "updateHistory#");
        List<ProjectEntity> list =
                projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false));

        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                continue;
            }
            GroupEntity groupEntity =
                    groupService.getOne(
                            Wrappers.lambdaQuery(GroupEntity.class)
                                    .eq(GroupEntity::getWhetherSystem, 1)
                                    .eq(GroupEntity::getProjectId, projectEntity.getId()));

            // do update
            long startDate =
                    MyTimeUtil.getOneDayZeroTime(
                            projectEntity.getCreateTime(), projectEntity.getTimezone());
            try {

                incomeMemoryCacheService.updateTodayJustElectricVo(projectEntity);
                incomeMemoryCacheService.updateElectricProfitVO(
                        projectEntity.getId(),
                        projectEntity.getProjectName(),
                        startDate,
                        projectEntity.getTimezone());
                incomeMemoryCacheServiceKt.updateRenewableIncomeMemoryCache(
                        projectEntity, CalculateTypeEnum.PV);
                if (groupEntity.getEnableWindPowerGeneration() != null
                        && groupEntity.getEnableWindPowerGeneration()) {
                    incomeMemoryCacheServiceKt.updateRenewableIncomeMemoryCache(
                            projectEntity, CalculateTypeEnum.WIND);
                }
                if (groupEntity.getEnableWastePowerGeneration() != null
                        && groupEntity.getEnableWastePowerGeneration()) {
                    incomeMemoryCacheServiceKt.updateRenewableIncomeMemoryCache(
                            projectEntity, CalculateTypeEnum.WASTER);
                }
            } catch (Exception e) {
                log.error(
                        "project profit don't update=====>id: {}, name :{}",
                        projectEntity.getId(),
                        projectEntity.getProjectName());
            }
        }
    }

    @Scheduled(cron = "0 20/30 * * * *")
    @Async("asyncServiceExecutor")
    public void updateDemandVo() {
        List<ProjectEntity> list =
                projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false));
        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                continue;
            }
            long firstDayOfMonth = MyTimeUtil.getCurrentMonthZeroTime(projectEntity.getTimezone());
            String projectId = projectEntity.getId();
            if (DEMAND_MAP.get(projectId) != null && DEMAND_MAP.get(projectId) == firstDayOfMonth) {
                continue;
            }
            GroupEntity groupEntity =
                    groupService
                            .lambdaQuery()
                            .eq(GroupEntity::getProjectId, projectId)
                            .eq(GroupEntity::getWhetherSystem, true)
                            .one();
            if (groupEntity.getCalcEarningsController() == null
                    || !groupEntity.getCalcEarningsController()) {
                continue;
            }
            List<GroupEntity> groupEntities = groupService.queryEnableDemandIncome(projectId);
            if (!groupEntities.isEmpty()) {
                incomeMemoryCacheService.updateDemandVoAsync(projectEntity);
                DEMAND_MAP.put(projectId, firstDayOfMonth);
            }
        }
    }

    /**
     * 每5分钟执行一次 缓存今天的收益
     *
     * <p>// * @see AbstractProfitService#todayProfit(long, ProjectEntity)
     *
     * @see ElectricProfitService#getTodayRealTimeElectricProfitVo(ProfitRequest, boolean)
     * @see CarbonService#getSiteByProjectId(String)
     */
    @Scheduled(cron = "0 5/30 * * * ?")
    @Async("asyncServiceExecutor")
    public void cacheTodayProfit() {
        log.info("{} -->正在执行", "TaskOperationTimer # cacheTodayProfit");
        List<ProjectEntity> list =
                projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false));
        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                continue;
            }
            OperationProfitVo operationProfitVo = new OperationProfitVo();
            ElectricProfitVO todayElectricProfitVo = new ElectricProfitVO();
            String projectId = projectEntity.getId();
            // 只会有 云版本才会执行
            try {
                // 这里的todayProfit 是实时计算的 所以需要缓存起来, 不像 monthProfit或者yesProfit一样
                todayElectricProfitVo =
                        electricProfitService.getTodayRealTimeElectricProfitVo(
                                new ProfitRequest().setProjectId(projectId), false);
                operationProfitVo =
                        newOperationProfitService.getTodayOperationProfit(projectEntity);
            } catch (Exception e) {
                log.error(
                        "project profit don't update=====>id: {}, e :{}",
                        projectEntity.getId(),
                        e.getMessage());
            }
            // 缓存今天的收益 5分钟更新一次的
            newTodayProfitMap.put(projectId, operationProfitVo);
            log.info("项目名称:{}, 今日缓存收益:{}", projectEntity.getProjectName(), operationProfitVo);
            // 保存今天的电量相关 用于计算
            todayElectricMap.put(
                    projectId,
                    todayElectricProfitVo == null ? new ElectricProfitVO() : todayElectricProfitVo);
        }
        log.info("{} -->执行完成", " TaskOperationTimer # cacheTodayProfit");
    }

    @Scheduled(cron = "0 20 0/6 * * ?")
    @Async("asyncServiceExecutor")
    public void cacheOneWeekProfit() {
        log.info("{} -->正在执行", " TaskOperationTimer # cacheOneWeekProfit");

        List<ProjectEntity> list =
                projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false));

        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                continue;
            }
            List<ProfitVO> recentWeekProfits = new LinkedList<>();
            // 根据时区 获取到 最近一周的 DayHolder (包括每天的零点到结束时间)
            List<MyTimeUtil.DayHolder> oneWeekTimes =
                    MyTimeUtil.getOneWeekTime(projectEntity.getTimezone());
            try {
                oneWeekTimes.forEach(
                        dayHolder -> {
                            BenefitRequest benefitRequest = new BenefitRequest();
                            // 设置查询某天的 零点到23:59:59的数据
                            benefitRequest.setStartDate(
                                    dayHolder.getStartZonedDateTime().toEpochSecond());
                            benefitRequest.setEndDate(
                                    dayHolder.getEndZonedDateTime().toEpochSecond());
                            // 近7天的某一天的 收益
                            try {
                                recentWeekProfits.add(
                                        newOperationProfitService.getProfitVO(
                                                benefitRequest, projectEntity.getId(), false));
                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                        });
                recentWeek.put(projectEntity.getId(), recentWeekProfits);
            } catch (Exception e) {
                log.error("cacheOneWeekProfit oneWeekTimes error :{}", e.getMessage());
            }
        }
        log.info("-->执行完成 TaskOperationTimer # cacheOneWeekProfit");
    }

    public void cacheDemandMonth() {
        List<ProjectEntity> list =
                projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false));
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                countDownLatch.countDown();
                continue;
            }
            List<GroupEntity> groupEntities =
                    groupService.queryEnableDemandControl(projectEntity.getId());
            if (CollectionUtil.isNotEmpty(groupEntities)) {
                Long createTime = projectEntity.getCreateTime();
                List<RangeRequest> startMonthZeroAndPreLast =
                        MyTimeUtil.getStartMonthZeroAndPreLast(
                                createTime, projectEntity.getTimezone());
                log.info(
                        "项目:{} 需要执行的周期:{}",
                        projectEntity.getProjectName(),
                        startMonthZeroAndPreLast);
                for (RangeRequest rangeRequest : startMonthZeroAndPreLast) {
                    groupDemandMonthIncomeService.saveGroupDemandMonthIncome(
                            projectEntity, rangeRequest);
                }
                countDownLatch.countDown();
                log.info(
                        "项目:{} 月度需量数据缓存完毕 还剩下:{}个项目正在执行",
                        projectEntity.getProjectName(),
                        countDownLatch.getCount());
            } else {
                log.info("项目:{} 未开需量控制分组 不需要执行 月度需量数据缓存", projectEntity.getProjectName());
                countDownLatch.countDown();
            }
        }
        try {
            countDownLatch.await();
            log.info("所有的项目 月度需量数据缓存完毕 ");
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
