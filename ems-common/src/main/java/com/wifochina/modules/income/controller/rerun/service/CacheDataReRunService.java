package com.wifochina.modules.income.controller.rerun.service;

import com.wifochina.modules.operation.request.CacheDataReRunRequest;

/**
 * Created on 2024/6/24 19:27.
 *
 * <AUTHOR>
 */
public interface CacheDataReRunService {

    void reRunRenewable(CacheDataReRunRequest request);

    void reRunBattery(CacheDataReRunRequest request);

    void reRunDemand(CacheDataReRunRequest request);

    void reRunDemandMonthCache(CacheDataReRunRequest request);

    void reRunDayReport(CacheDataReRunRequest request);
}
