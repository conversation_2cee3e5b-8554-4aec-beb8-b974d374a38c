package com.wifochina.modules.income.request.aop;

import com.wifochina.modules.group.service.EquipmentSearchService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 * Created on 2024/9/9 10:44.
 *
 * <AUTHOR>
 */
@Aspect
@Component
@AllArgsConstructor
public class ElectricProfitAspect {

    private final ProjectService projectService;
    private final EquipmentSearchService equipmentSearchService;

    // 定义前置操作，拦截 ElectricProfitService 的 getElectricProfitCollect 方法
    @Before("@annotation(com.wifochina.modules.income.request.aop.GroupControlPre)")
    public void beforeGetElectricProfitCollect(JoinPoint joinPoint) {
        // 获取方法的参数
        Object[] args = joinPoint.getArgs();
        // 假设 profitRequest 是方法的第一个参数
        if (args.length > 0 && args[0] instanceof ProfitRequest) {
            ProfitRequest profitRequest = (ProfitRequest) args[0];
            String projectId = profitRequest.getProjectId();
            ProjectEntity projectEntity = projectService.getById(projectId);
            EquipmentSearchService.DeviceIdsAndControl deviceIdsAndControl =
                    equipmentSearchService.getDeviceIdsAndControl(
                            new EquipmentSearchService.SearchContext()
                                    .setProject(projectEntity)
                                    .setSearchGroupId(profitRequest.getGroupId()));
            profitRequest.setDeviceIdsAndControl(deviceIdsAndControl);
        }
    }
}
