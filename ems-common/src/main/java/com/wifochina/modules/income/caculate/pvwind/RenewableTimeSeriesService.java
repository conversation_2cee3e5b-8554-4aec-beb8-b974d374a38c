package com.wifochina.modules.income.caculate.pvwind;

import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

import java.time.LocalDate;

/**
 * Created on 2024/5/24 10:13.
 *
 * <AUTHOR>
 */
public interface RenewableTimeSeriesService {
    double getPower(
            String calculateType,
            LocalDate time,
            ProjectEntity projectEntity,
            ElectricPriceEntity price);

    //    /**
    //     * 获取到 pv or wind 的 power sql
    //     *
    //     * @param pv
    //     * @param projectId
    //     * @return
    //     */
    //    String getPvWindPowerSql(String baseSql, boolean pv);
    //
    //    /**
    //     * 获取到 上网电量 power 的sql
    //     *
    //     * @param sql
    //     * @return : sql
    //     */
    //    String getOnLinePowerSql(String sql);
    //
    //    /**
    //     * 获取到 dcdc power 的 sql
    //     *
    //     * @param projectId : 项目id
    //     * @param deviceIds : 设备ids
    //     * @return : sql
    //     */
    //    String getDcdcPowerSql(String projectId, List<String> deviceIds);
    //
    //    /**
    //     * 填充上面的 sql 的时间戳 start 和 end
    //     *
    //     * @param sql : 上面方法的 返回的sql
    //     * @param time : 当前 的时间日期
    //     * @param projectEntity : 项目
    //     * @param price : 电价时段的配置
    //     * @return : 填充后的sql
    //     */
    //    String fillTempStampForSql(
    //            String sql, LocalDate time, ProjectEntity projectEntity, ElectricPriceEntity
    // price);
}
