package com.wifochina.modules.income.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @date 4/14/2022 10:25 AM
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value = "运营收益统计结果")
@Data
@Accessors(chain = true)
@ToString
public class OperationProfitVo {

    // 用于标注这个数据是是否从缓存中的 尤其是用在今日的
    @JsonIgnore private boolean fromCache = false;

    // TODO refactor 暂时不好优化 怕有移动端的影响 后期可以改成 double类型或者其他类型
    public OperationProfitVo() {
        this.setPvDischargeQuantity("0");
        this.setPvDischargeBenefit("0");
        this.setWindDischargeQuantity("0");
        this.setWindDischargeBenefit("0");
        this.setWasterDischargeQuantity("0");
        this.setWasterDischargeBenefit("0");
        this.setDemandControlBenefit("0");
        this.setDemandControlPower("0");
        this.setTotalChargeQuantity("0");
        this.setTotalChargeCost("0");
        this.setTotalBenefit("0");
        this.setTotalDischargeQuantity("0");
        this.setTotalDischargeBenefit("0");
    }

    @ApiModelProperty(value = "储能充放电总利润")
    @JsonProperty("electric_benefit")
    private String electricBenefit;

    @ApiModelProperty(value = "总利润")
    @JsonProperty("total_benefit")
    private String totalBenefit;

    @ApiModelProperty(value = "降低需量功率")
    @JsonProperty("demand_control_power")
    private String demandControlPower;

    @ApiModelProperty(value = "需量控制收益")
    @JsonProperty("demand_control_benefit")
    private String demandControlBenefit;

    @ApiModelProperty(value = "PV总发电电量")
    @JsonProperty("pv_discharge_quantity")
    private String pvDischargeQuantity;

    @ApiModelProperty(value = "PV总发电收益")
    @JsonProperty("pv_discharge_benefit")
    private String pvDischargeBenefit;

    @ApiModelProperty(value = "风电总发电电量")
    @JsonProperty("wind_discharge_quantity")
    private String windDischargeQuantity;

    @ApiModelProperty(value = "风电总发电收益")
    @JsonProperty("wind_discharge_benefit")
    private String windDischargeBenefit;

    @ApiModelProperty(value = "余热发电总发电电量")
    @JsonProperty("waster_discharge_quantity")
    private String wasterDischargeQuantity;

    @ApiModelProperty(value = "余热发电总发电收益")
    @JsonProperty("waster_discharge_benefit")
    private String wasterDischargeBenefit;

    @ApiModelProperty(value = "总充电电量KWH")
    @JsonProperty("total_charge_quantity")
    private String totalChargeQuantity;

    @ApiModelProperty(value = "总充电成本")
    @JsonProperty("total_charge_cost")
    private String totalChargeCost;

    @ApiModelProperty(value = "总放电电量")
    @JsonProperty("total_discharge_quantity")
    private String totalDischargeQuantity;

    @ApiModelProperty(value = "总放电收益")
    @JsonProperty("total_discharge_benefit")
    private String totalDischargeBenefit;
}
