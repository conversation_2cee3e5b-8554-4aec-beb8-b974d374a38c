package com.wifochina.modules.income.cache.memorycache;

import com.wifochina.modules.project.entity.ProjectEntity;

/**
 * Created on 2024/9/6 14:29.
 *
 * <AUTHOR>
 */
public interface IIncomeMemoryCacheService {

    void updateElectricProfitVO(
            String projectId, String projectName, Long projectInitTime, String timeZoneCode);

    void updatePvVo(
            String projectId, String projectName, Long projectInitTime, String timeZoneCode);

    void updateWindVo(
            String projectId, String projectName, Long projectInitTime, String timeZoneCode);

    void updateDemandVoAsync(ProjectEntity projectEntity);

    void updateTodayJustElectricVo(ProjectEntity projectEntity);
}
