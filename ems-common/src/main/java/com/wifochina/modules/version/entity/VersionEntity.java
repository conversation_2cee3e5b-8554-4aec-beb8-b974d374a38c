package com.wifochina.modules.version.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_version")
@ApiModel(value = "VersionEntity对象")
public class VersionEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "android版本")
    private String androidVersion;

    @ApiModelProperty(value = "ios版本")
    private String iosVersion;

    @ApiModelProperty(value = "0: 提醒一次 1: 每次提醒 2: 强制更新")
    private Integer flag;

    @ApiModelProperty(value = "中国")
    private String zhCn;

    @ApiModelProperty(value = "美国")
    private String enUs;

}
