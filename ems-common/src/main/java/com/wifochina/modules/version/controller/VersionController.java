package com.wifochina.modules.version.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.page.Result;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.VersionLogDetailService;
import com.wifochina.modules.user.service.LogService;
import com.wifochina.modules.version.entity.VersionEntity;
import com.wifochina.modules.version.request.VersionRequest;
import com.wifochina.modules.version.service.VersionService;
import com.wifochina.modules.version.vo.VersionVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@RestController
@RequestMapping("/version")
@Api(tags = "21-版本接口")
public class VersionController {

    @Resource private VersionService versionService;

    @Resource private LogService logService;

    @GetMapping("/latest")
    @ApiOperation(value = "最新App版本")
    public Result<VersionVo> getLatestAppVersion(HttpServletRequest request) {
        VersionVo versionVo = new VersionVo();
        VersionEntity latestVersion =
                versionService
                        .lambdaQuery()
                        .orderByDesc(VersionEntity::getCreateTime)
                        .last("limit 1")
                        .one();
        if (null == latestVersion) {
            return Result.success(versionVo);
        }
        BeanUtils.copyProperties(latestVersion, versionVo);
        VersionEntity lastForceUpdateVersion =
                versionService
                        .lambdaQuery()
                        .eq(VersionEntity::getFlag, 2)
                        .orderByDesc(VersionEntity::getCreateTime)
                        .last("limit 1")
                        .one();
        if (null == lastForceUpdateVersion) {
            versionVo.setPreForceAndroidVersion("");
            versionVo.setPreForceIosVersion("");
        } else {
            versionVo.setPreForceAndroidVersion(lastForceUpdateVersion.getAndroidVersion());
            versionVo.setPreForceIosVersion(lastForceUpdateVersion.getIosVersion());
        }
        String language = request.getHeader("Accept-Language");
        versionVo.setContent(latestVersion.getEnUs());
        if (language.startsWith("zh")) {
            versionVo.setContent(latestVersion.getZhCn());
        }
        return Result.success(versionVo);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增版本号")
    @PreAuthorize("hasAuthority('/version')")
    @Log(module = "VERSION", type = OperationType.ADD)
    public Result<Object> addHubVersion(@RequestBody VersionRequest versionRequest) {
        VersionEntity versionEntity = new VersionEntity();
        BeanUtils.copyProperties(versionRequest, versionEntity);
        versionService.save(versionEntity);
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("VERSION")
        //                        .method("VERSION_ADD")
        //                        .object(versionRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.VERSION_TRACE_FORMAT,
        // versionEntity.getId()))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/del")
    @ApiOperation(value = "删除版本号")
    @PreAuthorize("hasAuthority('/version')")
    @Log(
            module = "VERSION",
            type = OperationType.DEL,
            logDetailServiceClass = VersionLogDetailService.class)
    public Result<Object> delClientVersion(@RequestBody VersionRequest versionRequest) {
        VersionEntity versionEntity = versionService.getById(versionRequest.getId());
        versionService.removeById(versionRequest.getId());
        //        logService.logDeleteDetail(
        //                LogInfo.builder()
        //                        .module("VERSION")
        //                        .method("VERSION_DEL")
        //                        .object(versionEntity.getId())
        //                        .delDetail(
        //                                Map.of(
        //                                        String.valueOf(versionEntity.getId()),
        //                                        "ISO:"
        //                                                + versionEntity.getIosVersion()
        //                                                + ";ANDROID:"
        //                                                + versionEntity.getAndroidVersion()))
        //                        .traceId(
        //                                String.format(
        //                                        LogService.VERSION_TRACE_FORMAT,
        // versionEntity.getId()))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改版本号")
    @PreAuthorize("hasAuthority('/version')")
    @Log(module = "VERSION", type = OperationType.UPDATE)
    public Result<Object> updateHubVersion(@RequestBody VersionRequest versionRequest) {
        VersionEntity versionEntity = new VersionEntity();
        BeanUtils.copyProperties(versionRequest, versionEntity);
        versionService.saveOrUpdate(versionEntity);
        //        logService.logUpdateDetail(
        //                LogInfo.builder()
        //                        .module("VERSION")
        //                        .method("VERSION_UPDATE")
        //                        .object(versionRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.VERSION_TRACE_FORMAT,
        // versionRequest.getId()))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询版本号")
    @PreAuthorize("hasAuthority('/version')")
    public Result<IPage<VersionEntity>> pageClientVersion(@RequestBody PageBean pageBean) {
        IPage<VersionEntity> versionPage =
                new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        IPage<VersionEntity> list =
                versionService.page(versionPage, Wrappers.lambdaQuery(VersionEntity.class));
        return Result.success(list);
    }

    @PostMapping("/get")
    @ApiOperation(value = "查询指定id版本号")
    @PreAuthorize("hasAuthority('/version')")
    public Result<VersionEntity> getClientVersion(@RequestBody VersionRequest versionRequest) {
        VersionEntity versionEntity = versionService.getById(versionRequest.getId());
        return Result.success(versionEntity);
    }
}
