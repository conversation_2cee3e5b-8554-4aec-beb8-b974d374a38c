package com.wifochina.modules.version.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@Data
public class VersionVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "android版本")
    private String androidVersion;

    @ApiModelProperty(value = "ios版本")
    private String iosVersion;

    @ApiModelProperty(value = "上一个强制更新的ios版本")
    private String preForceIosVersion;

    @ApiModelProperty(value = "上一个强制更新的android版本")
    private String preForceAndroidVersion;

    @ApiModelProperty(value = "0: 提醒一次 1: 每次提醒 2: 强制更新")
    private Integer flag;

    @ApiModelProperty(value = "更新说明")
    private String content;

}
