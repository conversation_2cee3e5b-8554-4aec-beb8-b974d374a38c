package com.wifochina.modules.investor.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.investor.entity.InvestorEntity;
import com.wifochina.modules.investor.entity.InvestorProjectEntity;
import com.wifochina.modules.investor.mapper.InvestorProjectMapper;
import com.wifochina.modules.investor.service.InvestorProjectService;
import com.wifochina.modules.investor.service.InvestorService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 分组与电表关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public abstract class InvestorProjectServiceImpl
        extends ServiceImpl<InvestorProjectMapper, InvestorProjectEntity>
        implements InvestorProjectService {

    @Resource protected InvestorService investorService;

    @Override
    @Transactional
    public void saveInvestorForProject(List<String> investorIds, String projectId) {
        this.baseMapper.delete(
                Wrappers.lambdaQuery(InvestorProjectEntity.class)
                        .eq(InvestorProjectEntity::getProjectId, projectId));
        investorIds.forEach(
                investorId -> {
                    InvestorProjectEntity investorProject =
                            new InvestorProjectEntity(investorId, projectId);
                    this.baseMapper.insert(investorProject);
                });
    }

    @Override
    public List<String> queryInvestIdsByProjectId(String projectId) {
        return this.baseMapper
                .selectList(
                        Wrappers.lambdaQuery(InvestorProjectEntity.class)
                                .eq(InvestorProjectEntity::getProjectId, projectId))
                .stream()
                .map(InvestorProjectEntity::getInvestorId)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvestorEntity> queryInvestorsByProjectId(String projectId) {
        List<String> investorIds = queryInvestIdsByProjectId(projectId);
        if (investorIds.isEmpty()) {
            return List.of();
        }
        return investorService.lambdaQuery().in(InvestorEntity::getId, investorIds).list();
    }

    @Override
    public List<String> queryProjectIdsByInvestorIds(List<String> investorIds) {
        return this.baseMapper
                .selectList(
                        Wrappers.lambdaQuery(InvestorProjectEntity.class)
                                .in(InvestorProjectEntity::getInvestorId, investorIds))
                .stream()
                .map(InvestorProjectEntity::getProjectId)
                .collect(Collectors.toList());
    }
}
