package com.wifochina.modules.investor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.investor.entity.InvestorEntity;
import com.wifochina.modules.investor.entity.InvestorProjectEntity;

import java.util.List;

/**
 * 分组与电表关联表 服务类
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface InvestorProjectService extends IService<InvestorProjectEntity> {
    void saveInvestorForProject(List<String> investorIds, String projectId);

    List<String> queryInvestIdsByProjectId(String projectId);

    List<String> queryInvestIdsByUserId(String userId);

    List<InvestorProjectEntity> queryInvestorProjectByUserId(String userId);

    List<InvestorEntity> queryInvestorsByUserId(String userId);

    List<String> queryProjectIdsByInvestorIds(List<String> investorIds);

    List<InvestorEntity> queryInvestorsByProjectId(String projectId);
}
