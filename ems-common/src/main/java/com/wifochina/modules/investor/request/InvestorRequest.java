package com.wifochina.modules.investor.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 分组
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Getter
@Setter
@ApiModel(value = "Investor请求对象", description = "分组")
public class InvestorRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("资方id")
    private String id;

    @ApiModelProperty("资方名称")
    private String name;

    @ApiModelProperty("资方地址")
    private String address;
}
