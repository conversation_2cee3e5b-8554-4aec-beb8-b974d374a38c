package com.wifochina.modules.investor.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 分组
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Getter
@Setter
@ApiModel(value = "Investor分页对象", description = "分组")
public class InvestorPageRequest extends PageBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分组名称")
    private String name;

    @ApiModelProperty("外部控制器（0关闭）（1打不开）")
    private String address;
}
