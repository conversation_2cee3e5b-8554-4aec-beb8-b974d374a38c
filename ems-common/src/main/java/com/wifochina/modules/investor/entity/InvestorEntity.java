package com.wifochina.modules.investor.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 分组
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Getter
@Setter
@TableName("t_investor")
@ApiModel(value = "InvestorEntity对象", description = "InvestorEntity对象")
public class InvestorEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("资方id")
    private String id;

    @ApiModelProperty("投资方姓名")
    private String name;

    @ApiModelProperty("投资方地址")
    private String address;
}
