package com.wifochina.modules.investor.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.investor.entity.InvestorEntity;
import com.wifochina.modules.investor.request.InvestorPageRequest;
import com.wifochina.modules.investor.request.InvestorRequest;
import com.wifochina.modules.investor.service.InvestorProjectService;
import com.wifochina.modules.investor.service.InvestorService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.InvestorLogDetailService;
import com.wifochina.modules.oauth.util.SecurityUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 分组 前端控制器
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@RestController
@Api(tags = "29-资方管理")
@RequestMapping("/investor")
@RequiredArgsConstructor
public class InvestorController {

    @Resource private InvestorService investorService;
    @Resource private InvestorProjectService investorProjectService;

    @PostMapping("/list")
    @ApiOperation("查询资方列表")
    @PreAuthorize(
            "hasAuthority('/manage/investor/list') or hasAuthority('/manage/project/create') or hasAuthority('/manage/project/update')")
    public Result<Object> list(@RequestBody InvestorPageRequest investorPageRequest) {
        return Result.success(
                investorService.page(
                        Page.of(
                                investorPageRequest.getPageNum(),
                                investorPageRequest.getPageSize()),
                        Wrappers.lambdaQuery(InvestorEntity.class)
                                .like(
                                        StringUtils.hasLength(investorPageRequest.getAddress()),
                                        InvestorEntity::getAddress,
                                        investorPageRequest.getAddress())
                                .like(
                                        StringUtils.hasLength(investorPageRequest.getName()),
                                        InvestorEntity::getName,
                                        investorPageRequest.getName())
                                .orderByDesc(InvestorEntity::getCreateTime)));
    }

    @PostMapping("/currentUserList")
    @ApiOperation("当前用户的资方列表")
    @PreAuthorize("hasAuthority('/manage/project/showInvestor')")
    public Result<Object> list() {
        //        UserEntity user = userService.getById(SecurityUtil.getUserId());
        // 因为账号系统对接 所以改成这个 上线后可能以前的关联查询不到 但是不会抛错 上面的注释了
        return Result.success(
                investorProjectService.queryInvestorsByUserId(SecurityUtil.getUserId()));
    }

    @PostMapping("/add")
    @ApiOperation("增加资方")
    @Log(module = "INVESTOR", type = OperationType.ADD)
    @PreAuthorize("hasAuthority('/manage/investor/add')")
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> add(@RequestBody InvestorRequest investorRequest) {
        InvestorEntity investorEntity = new InvestorEntity();
        BeanUtils.copyProperties(investorRequest, investorEntity);
        investorEntity.setId(StringUtil.uuid());
        investorService.save(investorEntity);
        return Result.success();
    }

    @PostMapping("/update")
    @ApiOperation("修改资方")
    @Log(module = "INVESTOR", type = OperationType.UPDATE)
    @PreAuthorize("hasAuthority('/manage/investor/edit')")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody InvestorRequest investorRequest) {
        InvestorEntity investorEntity = new InvestorEntity();
        BeanUtils.copyProperties(investorRequest, investorEntity);
        investorService.updateById(investorEntity);
        return Result.success();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除资方")
    @Log(
            module = "INVESTOR",
            type = OperationType.DEL,
            logDetailServiceClass = InvestorLogDetailService.class)
    @PreAuthorize("hasAuthority('/manage/investor/delete')")
    public Result<String> delete(@PathVariable("id") String id) {
        investorService.removeById(id);
        return Result.success();
    }
}
