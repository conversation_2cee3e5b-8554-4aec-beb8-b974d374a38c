package com.wifochina.modules.investor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 分组与电表关联表
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Getter
@Setter
@TableName("t_investor_project")
@ApiModel(value = "InvestorProjectEntity对象", description = "InvestorProjectEntity对象")
public class InvestorProjectEntity extends BaseEntity {

    public InvestorProjectEntity(String investorId, String projectId) {
        this.investorId = investorId;
        this.projectId = projectId;
    }

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("关系id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("分组id")
    private String investorId;

    @ApiModelProperty("电表id")
    private String projectId;
}
