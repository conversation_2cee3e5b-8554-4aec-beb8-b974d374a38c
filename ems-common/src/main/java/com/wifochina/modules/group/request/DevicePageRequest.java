package com.wifochina.modules.group.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备分页请求
 * 
 * @date 2022/3/16 16:54
 * <AUTHOR>
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "设备分页请求")
public class DevicePageRequest extends PageBean {

    @ApiModelProperty(value = "设备名称")
    private String name;

    @ApiModelProperty(value = "设备状态")
    private Integer status;
}
