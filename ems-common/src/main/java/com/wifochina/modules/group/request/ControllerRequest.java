package com.wifochina.modules.group.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;

/**
 * 协调控制器
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data
public class ControllerRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "ip")
    private String ip;

    @ApiModelProperty(value = "端口")
    private Integer port;

    @ApiModelProperty(value = "序列号")
    private String sn;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;
}
