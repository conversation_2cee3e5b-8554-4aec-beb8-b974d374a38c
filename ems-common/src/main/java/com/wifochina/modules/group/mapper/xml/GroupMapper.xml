<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.group.mapper.GroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GroupResultMap" type="com.wifochina.modules.group.vo.GroupVO">
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="external_controller" property="externalController"/>
        <result column="calc_earnings_controller" property="calcEarningsController"/>
        <result column="wind_earnings_controller" property="windEarningsController"/>
        <result column="waster_earnings_controller" property="wasterEarningsController"/>
        <result column="group_earnings_controller" property="groupEarningsController"/>
        <result column="demand_controller" property="demandController"/>
        <result column="demand_control" property="demandControl"/>
        <result column="photovoltaic_controller" property="photovoltaicController"/>
        <result column="whether_system" property="whetherSystem"/>
        <result column="demandControllerModel" property="demand_controller_model"/>
        <result column="photovoltaicModel" property="photovoltaic_model"/>
        <result column="waster_power_model" property="wasterPowerModel"/>
        <result column="wind_power_model" property="windPowerModel"/>
        <result column="priority" property="priority"/>
        <result column="antiReflux" property="anti_reflux"/>
        <result column="demandController" property="demand_controller"/>
        <result column="slip_time" property="slipTime"/>
        <result column="demand_control_adjust_model" property="demandControlAdjustModel"/>
        <result column="demand_period" property="demandPeriod"/>
        <result column="demand_control_auto_rate" property="demandControlAutoRate"/>
        <result column="demand_control_auto_up_limit" property="demandControlAutoUpLimit"/>
        <result column="demand_remind_controller" property="demandRemindController"/>
        <result column="powerFactorControl" property="power_factor_control"/>
        <result column="project_id" property="projectId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="demand_income" property="demandIncome"/>
        <result column="demand_alarm_threshold" property="demandAlarmThreshold"/>
        <result column="demand_control_rate" property="demandControlRate"/>
        <result column="demand_calc_model" property="demandCalcModel"/>
        <result column="capacity_controller" property="capacityController"/>
        <result column="capacity_remind_controller" property="capacityRemindController"/>
        <result column="capacity_alarm_threshold" property="capacityAlarmThreshold"/>
        <result column="controllable_strategies" property="controllableStrategies"/>
        <result column="enable_waste_power_generation" property="enableWastePowerGeneration"/>
        <result column="ems_strategies" property="emsStrategies"/>
        <result column="avc_monitor_controller" property="avcMonitorController"/>
        <result column="diagram_of_system" property="diagramOfSystem"/>
        <result column="diagram_of_load" property="diagramOfLoad"/>
        <result column="order_index" property="orderIndex"/>
        <result column="open_vpp" property="openVpp"/>
        <result column="wave_record" property="waveRecord"/>
        <result column="agc_controller" property="agcController"/>
        <result column="avc_controller" property="avcController"/>
        <result column="mg_soc_balance" property="mgSocBalance"/>
        <result column="mg_soc_balance_off_grid_power_limit_ratio" property="mgSocBalanceOffGridPowerLimitRatio"/>
        <result column="home_page_reactive_power_controller" property="homePageReactivePowerController"/>
        <result column="home_page_zero_power_controller" property="homePageZeroPowerController"/>
        <result column="day_report_data_interval" property="dayReportDataInterval"/>
        <result column="time_sharing_back_flow_limit_controller" property="timeSharingBackFlowLimitController"/>
        <result column="time_sharing_demand_controller" property="timeSharingDemandController"/>
        <collection property="ammeters" javaType="java.util.List" ofType="com.wifochina.modules.group.vo.AmmeterVo"
                    column="id" select="selectAmmeterMap">
        </collection>
        <collection property="devices" ofType="map" column="id" select="selectDeviceMap">
        </collection>
        <collection property="controllables" javaType="java.util.List"
                    ofType="com.wifochina.modules.group.vo.ControllableVo" column="id" select="selectControllableMap">
        </collection>
    </resultMap>

    <select id="queryGroupVO" resultMap="GroupResultMap">
        select g.*,s.anti_reflux,s.power_factor_control
        from t_group g,t_strategy s where
        g.project_id=#{projectId} and s.project_id=#{projectId}
        and g.id = s.group_id
        and s.week_day = 0
        <if test="name!=null and name.trim()!=''">
            and name like CONCAT(CONCAT('%',#{name}),'%')
        </if>
        order by order_index, create_time
    </select>

    <select id="selectAmmeterMap" resultType="com.wifochina.modules.group.vo.AmmeterVo">
        select a.id, a.name, a.type
        from t_ammeter a,
             t_group_ammeter ga
        where a.id = ga.ammeterId
          and ga.groupId = #{id}
    </select>

    <select id="selectDeviceMap" resultType="map">
        select d.id, d.name
        from t_device d,
             t_group_device gd
        where d.id = gd.deviceId
          and gd.groupId = #{id}
    </select>

    <select id="selectControllableMap" resultType="com.wifochina.modules.group.vo.ControllableVo">
        select c.id, c.name, c.vendor
        from t_controllable c,
             t_group_controllable gc
        where c.id = gc.controllable_id
          and gc.group_id = #{id}
    </select>

    <resultMap id="GroupGOResultMap" type="com.wifochina.modules.group.vo.GroupGoVo">
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="external_controller" property="externalController"/>
        <result column="calc_earnings_controller" property="calcEarningsController"/>
        <result column="wind_earnings_controller" property="windEarningsController"/>
        <result column="waster_earnings_controller" property="wasterEarningsController"/>
        <result column="group_earnings_controller" property="groupEarningsController"/>
        <result column="demand_controller" property="demandController"/>
        <result column="demand_control" property="demandControl"/>
        <result column="slip_time" property="slipTime"/>
        <result column="demand_control_adjust_model" property="demandControlAdjustModel"/>
        <result column="demand_period" property="demandPeriod"/>
        <result column="demand_control_auto_rate" property="demandControlAutoRate"/>
        <result column="demand_control_auto_up_limit" property="demandControlAutoUpLimit"/>
        <result column="demand_remind_controller" property="demandRemindController"/>
        <result column="photovoltaic_controller" property="photovoltaicController"/>
        <result column="whether_system" property="whetherSystem"/>
        <result column="demandControllerModel" property="demand_controller_model"/>
        <result column="photovoltaicModel" property="photovoltaic_model"/>
        <result column="priority" property="priority"/>
        <result column="project_id" property="projectId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="agc_controller" property="agcController"/>
        <result column="avc_controller" property="avcController"/>

        <result column="capacity_controller" property="capacityController"/>
        <result column="capacity_remind_controller" property="capacityRemindController"/>
        <result column="capacity_alarm_threshold" property="capacityAlarmThreshold"/>
        <result column="mg_soc_balance" property="mgSocBalance"/>
        <result column="mg_soc_balance_off_grid_power_limit_ratio" property="mgSocBalanceOffGridPowerLimitRatio"/>
        <result column="home_page_reactive_power_controller" property="homePageReactivePowerController"/>
        <result column="home_page_zero_power_controller" property="homePageZeroPowerController"/>
        <result column="day_report_data_interval" property="dayReportDataInterval"/>
        <collection property="ammeters" ofType="list" column="id" select="selectAmmeter">
        </collection>
        <collection property="devices" ofType="list" column="id" select="selectDevice">
        </collection>
        <collection property="controllables" ofType="list" column="id" select="selectControllable">
        </collection>
    </resultMap>


    <select id="queryGroup" resultMap="GroupGOResultMap">
        select *
        from t_group
        where project_id = #{projectId}
        order by create_time
    </select>

    <select id="selectAmmeter" resultType="java.lang.String">
        select a.id
        from t_ammeter a,
             t_group_ammeter ga
        where a.id = ga.ammeterId
          and ga.groupId = #{id}
    </select>

    <select id="selectDevice" resultType="java.lang.String">
        select d.id
        from t_device d,
             t_group_device gd
        where d.id = gd.deviceId
          and gd.groupId = #{id}
    </select>

    <select id="selectControllable" resultType="java.lang.String">
        select c.id
        from t_controllable c,
             t_group_controllable gc
        where c.id = gc.controllable_id
          and gc.group_id = #{id}
    </select>

    <!-- sql的意思就是 查询 非系统分组下的 , 绑定了 收益设备或者收益电表的 分组List-->
    <select id="queryGroupsNotSystemAndBindIncomeDeviceOrMeter"
            resultType="com.wifochina.modules.group.entity.GroupEntity">
        SELECT *
        FROM t_group
        WHERE id IN (SELECT DISTINCT gd.groupId
                     FROM t_group_device gd
                              LEFT JOIN t_device d ON d.id = gd.deviceId
                     WHERE gd.groupId IN (SELECT id
                                          FROM t_group
                                          WHERE whether_system = false
                                            AND project_id = #{projectId})
                       AND d.income = true

                     UNION

                     SELECT DISTINCT ga.groupId
                     FROM t_group_ammeter ga
                              LEFT JOIN t_ammeter a ON a.id = ga.ammeterId
                     WHERE ga.groupId IN (SELECT id
                                          FROM t_group
                                          WHERE whether_system = false
                                            AND project_id = #{projectId})
                       AND a.income = true)
    </select>

    <select id="queryEnableDemandProjectIds" resultType="java.lang.String">
        select distinct g.project_id
        from t_group g
        where g.demand_control != 'disable'
        <if test="projectIds != null  and projectIds.size() > 0">
            and g.project_id in
            <foreach collection="projectIds" open="(" separator="," close=")" item="projectId">
                #{projectId}
            </foreach>
        </if>
    </select>

    <select id="queryEnableCapacityProjectIds" resultType="java.lang.String">
        select distinct g.project_id
        from t_group g
        where g.capacity_controller = 1
        <if test="projectIds != null  and projectIds.size() > 0">
            and g.project_id in
            <foreach collection="projectIds" open="(" separator="," close=")" item="projectId">
                #{projectId}
            </foreach>
        </if>
    </select>

</mapper>
