package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 摄像头
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Getter
@Setter
@TableName("t_camera")
@ApiModel(value = "CameraEntity对象", description = "摄像头")
public class CameraEntity extends BaseEquipEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("摄像头销售商")
    private String vendor;

    @ApiModelProperty("摄像头型号")
    private String type;

    @ApiModelProperty("摄像头用户名")
    private String username;

    @ApiModelProperty("摄像头密码")
    private String password;

    @ApiModelProperty("摄像头认证类型(basic、digest)")
    private String authType;
}
