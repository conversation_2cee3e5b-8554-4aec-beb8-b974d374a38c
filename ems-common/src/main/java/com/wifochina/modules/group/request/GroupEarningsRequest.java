package com.wifochina.modules.group.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;

/**
 * 分组
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
public class GroupEarningsRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组id")
    private String id;

    @ApiModelProperty(value = "分组储能收益开关(false关闭)(true打开）")
    private Boolean groupEarningsController;
}
