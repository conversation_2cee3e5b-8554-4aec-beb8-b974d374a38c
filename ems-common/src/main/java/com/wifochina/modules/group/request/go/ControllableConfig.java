package com.wifochina.modules.group.request.go;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-08-07 1:50 PM
 */
@Data
public class ControllableConfig {
    @ApiModelProperty("soc上限")
    @JsonProperty("soc_high")
    private Double socHigh;

    @ApiModelProperty("soc下限")
    @JsonProperty("soc_low")
    private Double socLow;

    @ApiModelProperty("离网启动soc")
    @JsonProperty("grid_disconnect_start_soc")
    private Double gridDisconnectStartSoc;

    @ApiModelProperty("离网关闭soc")
    @JsonProperty("grid_disconnect_stop_soc")
    private Double gridDisconnectStopSoc;

    @ApiModelProperty("功率(kw)")
    private Double power;

    @ApiModelProperty("速度(kw/s)")
    private Double speed;

    @ApiModelProperty("soc上限操作（high,low）")
    @JsonProperty("operate_high")
    private String operateHigh;

    @ApiModelProperty("soc下限操作  (high,low)")
    @JsonProperty("operate_low")
    private String operateLow;
}
