package com.wifochina.modules.group.service;

import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * Created on 2024/6/27 11:47.
 *
 * <AUTHOR>
 */
public interface EquipmentSearchService {
    DeviceIdsAndControl getDeviceIdsAndControl(SearchContext context);

    boolean groupController(SearchContext context);

    /**
     * 支持分组的 设备(这个设备是 包括device 和 ammeter)
     *
     * @return : equipment ids
     */
    List<String> equipmentIdsSupportGroup(SearchContext context, Boolean groupController);

    List<String> getIncomeDeviceAmmeterIdsForGroup(String projectId, String groupId);

    Map<String, List<String>> getIncomeDeviceAmmeterByProjectIds(List<String> projectIds);

    @Data
    @Accessors(chain = true)
    class SearchContext {
        ProjectEntity project;
        GroupEntity systemGroup;

        String searchGroupId;
    }

    @Data
    class DeviceIdsAndControl {
        boolean groupController;
        List<String> deviceIds;
    }
}
