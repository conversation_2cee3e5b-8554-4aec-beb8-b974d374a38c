package com.wifochina.modules.group.request.go;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * @since 4/20/2022 8:19 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GroupUnitRequest {

    @JsonProperty("group_strategy")
    private GroupGoRequest groupStrategy;

    private String uuid;

    @JsonProperty("uuid_of_ems")
    private List<String> uuidOfEms;

    @JsonProperty("uuid_of_meter")
    private List<String> uuidOfMeter;

    @JsonProperty("uuid_of_controllable")
    private List<String> uuidOfControllable;

    /** 1.4.2 added 分组的name */
    @JsonProperty("name")
    private String name;
}
