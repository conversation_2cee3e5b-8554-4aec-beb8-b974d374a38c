package com.wifochina.modules.group.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.group.entity.AmmeterEntity;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface AmmeterService extends IService<AmmeterEntity> {

    List<AmmeterEntity> findIncomeOrReadingAmmeter(String projectId);

    List<AmmeterEntity> findIncomeAmmeter(String projectId);

    List<AmmeterEntity> findDemandMetersByGroupId(String groupId);

    List<String> meterIdsPrepare(RequestWithGroupId requestWithGroupId, String type);

    List<String> findMeterIdsByGroupIdAndItemIdAndType(
            String projectId, String groupId, String itemId, Integer type);

    List<String> findAllMeterIdsByProjectIdAndType(String projectId, Integer type);

    List<String> findMeterIdsByGroupIdAndType(String groupId, Integer type);

    List<AmmeterEntity> getAmmeterByGid(String projectId, String groupId);

    public List<String> findDemandMeterIdsByGroupIdAndItemId(
            String projectId, String groupId, String itemId);

    List<AmmeterEntity> findAllMeters(String projectId);
}
