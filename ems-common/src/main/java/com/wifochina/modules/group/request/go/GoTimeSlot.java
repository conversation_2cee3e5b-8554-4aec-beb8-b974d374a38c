package com.wifochina.modules.group.request.go;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2025/7/24 14:59.
 *
 * <AUTHOR>
 */
@Data
public class GoTimeSlot implements Cloneable {

    @ApiModelProperty(value = "功率")
    private Double power;

    @ApiModelProperty(value = "开始分钟")
    private Integer start_minute;

    @ApiModelProperty(value = "结束分钟")
    private Integer end_minute;

    @Override
    public GoTimeSlot clone() throws CloneNotSupportedException {
        return (GoTimeSlot) super.clone();
    }
}
