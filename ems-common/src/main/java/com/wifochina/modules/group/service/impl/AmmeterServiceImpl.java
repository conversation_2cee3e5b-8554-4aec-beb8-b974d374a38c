package com.wifochina.modules.group.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.GroupAmmeterEntity;
import com.wifochina.modules.group.mapper.AmmeterMapper;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.oauth.util.WebUtils;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
@RequiredArgsConstructor
public class AmmeterServiceImpl extends ServiceImpl<AmmeterMapper, AmmeterEntity>
        implements AmmeterService {

    private final GroupAmmeterServiceImpl groupAmmeterService;

    @Override
    public List<AmmeterEntity> findIncomeOrReadingAmmeter(String projectId) {
        LambdaQueryWrapper<AmmeterEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(AmmeterEntity::getProjectId, projectId);
        lambdaQueryWrapper.and(
                i ->
                        i.eq(AmmeterEntity::getIncome, true)
                                .or()
                                .eq(AmmeterEntity::getMeterReading, true));
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<AmmeterEntity> findIncomeAmmeter(String projectId) {
        return this.lambdaQuery()
                .eq(AmmeterEntity::getIncome, true)
                .eq(AmmeterEntity::getProjectId, projectId)
                .list();
    }

    @Override
    public List<AmmeterEntity> findDemandMetersByGroupId(String groupId) {
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService.list(
                        Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                                .eq(GroupAmmeterEntity::getGroupId, groupId));
        if (CollectionUtil.isNotEmpty(groupAmmeterEntities)) {
            return this.list(
                    Wrappers.lambdaQuery(AmmeterEntity.class)
                            .in(
                                    AmmeterEntity::getId,
                                    groupAmmeterEntities.stream()
                                            .map(GroupAmmeterEntity::getAmmeterId)
                                            .collect(Collectors.toList()))
                            .eq(AmmeterEntity::getMaxDemandMeasurement, true));
        }
        return List.of();
    }

    @Override
    public List<String> meterIdsPrepare(RequestWithGroupId requestWithGroupId, String type) {
        return findMeterIdsByGroupIdAndItemIdAndType(
                WebUtils.projectId.get(),
                requestWithGroupId.getGroupId(),
                requestWithGroupId.getItemId(),
                StringUtils.hasLength(type) ? Integer.parseInt(type) : null);
    }

    @Override
    public List<String> findMeterIdsByGroupIdAndItemIdAndType(
            String projectId, String groupId, String itemId, Integer type) {
        List<String> meterIds;
        if (EmsConstants.ALL.equals(itemId)) {
            if (!StringUtils.hasLength(groupId) || EmsConstants.ALL.equals(groupId)) {
                meterIds = findAllMeterIdsByProjectIdAndType(projectId, type);
            } else {
                meterIds = findMeterIdsByGroupIdAndType(groupId, type);
            }
        } else {
            meterIds = List.of(itemId);
        }
        return meterIds;
    }

    @Override
    public List<String> findDemandMeterIdsByGroupIdAndItemId(
            String projectId, String groupId, String itemId) {
        List<String> meterIds;
        if (EmsConstants.ALL.equals(itemId)) {
            if (!StringUtils.hasLength(groupId) || EmsConstants.ALL.equals(groupId)) {
                meterIds =
                        this.lambdaQuery()
                                .eq(AmmeterEntity::getProjectId, projectId)
                                .eq(AmmeterEntity::getMaxDemandMeasurement, true)
                                .list()
                                .stream()
                                .map(AmmeterEntity::getId)
                                .collect(Collectors.toList());

            } else {
                List<String> groupAmmeterIds =
                        groupAmmeterService
                                .lambdaQuery()
                                .eq(GroupAmmeterEntity::getGroupId, groupId)
                                .list()
                                .stream()
                                .map(GroupAmmeterEntity::getAmmeterId)
                                .collect(Collectors.toList());
                meterIds =
                        this.lambdaQuery()
                                .in(AmmeterEntity::getId, groupAmmeterIds)
                                .eq(AmmeterEntity::getMaxDemandMeasurement, true)
                                .list()
                                .stream()
                                .map(AmmeterEntity::getId)
                                .collect(Collectors.toList());
            }
        } else {
            meterIds = List.of(itemId);
        }
        return meterIds;
    }

    @Override
    public List<AmmeterEntity> findAllMeters(String projectId) {
        return this.list(
                Wrappers.lambdaQuery(AmmeterEntity.class)
                        .eq(AmmeterEntity::getProjectId, projectId)
                        .orderByAsc(AmmeterEntity::getOrderIndex)
                        .orderByAsc(AmmeterEntity::getCreateTime));
    }

    @Override
    public List<String> findAllMeterIdsByProjectIdAndType(String projectId, Integer type) {
        // 确保只查询必要的字段，提高查询效率
        return this.list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .eq(type != null, AmmeterEntity::getType, type)
                                .eq(AmmeterEntity::getProjectId, projectId))
                .stream()
                .map(AmmeterEntity::getId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findMeterIdsByGroupIdAndType(String groupId, Integer type) {
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService.list(
                        Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                                .eq(GroupAmmeterEntity::getGroupId, groupId));
        List<String> meterIds =
                groupAmmeterEntities.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .map(String::valueOf) // 确保转换的必要性
                        .collect(Collectors.toList());
        if (meterIds.isEmpty()) {
            return List.of();
        } else {
            // 如果已经存在设备ID，查询对应的设备信息
            return this.list(
                            Wrappers.lambdaQuery(AmmeterEntity.class)
                                    .in(AmmeterEntity::getId, meterIds)
                                    .eq(type != null, AmmeterEntity::getType, type))
                    .stream()
                    .map(AmmeterEntity::getId)
                    .collect(Collectors.toList());
        }
    }

    @Override
    public List<AmmeterEntity> getAmmeterByGid(String projectId, String groupId) {
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService
                        .lambdaQuery()
                        .eq(GroupAmmeterEntity::getGroupId, groupId)
                        .list();
        List<String> ammeterIds =
                groupAmmeterEntities.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        List<AmmeterEntity> ammeterEntities = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(ammeterIds)) {
            ammeterEntities =
                    this.getBaseMapper()
                            .selectList(
                                    new LambdaQueryWrapper<AmmeterEntity>()
                                            .eq(AmmeterEntity::getIncome, true)
                                            .eq(AmmeterEntity::getProjectId, projectId)
                                            .in(AmmeterEntity::getId, ammeterIds));
        }
        return ammeterEntities;
    }
}
