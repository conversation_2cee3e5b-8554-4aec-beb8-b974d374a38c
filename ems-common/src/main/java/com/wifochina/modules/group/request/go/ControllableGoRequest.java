package com.wifochina.modules.group.request.go;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 协调控制器
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Getter
@Setter
public class ControllableGoRequest {

    @ApiModelProperty(value = "uuid")
    @JsonProperty("uuid")
    private String id;

    @ApiModelProperty("类型：Load、IO、Power")
    private String type;

    @ApiModelProperty("型号")
    private String vendor;

    @ApiModelProperty("ip")
    private String host;

    @ApiModelProperty("端口")
    private Integer port;

    @ApiModelProperty(value = "从机号 默认 1")
    @JsonProperty("slave_id")
    private Integer slaveId;

    @ApiModelProperty("可控设备配置")
    private Map<String, Object> config;

    @ApiModelProperty("设计功率")
    @JsonProperty("design_power")
    private Double designPower;

    @ApiModelProperty("监听端口")
    @JsonProperty("listen_port")
    private Integer listenPort;

    @ApiModelProperty("功率斜率")
    @JsonProperty("power_step_percentage")
    private Double powerStepPercentage;

    /** 1.4.2 added send name */
    @ApiModelProperty("name")
    @JsonProperty("name")
    private String name;

    // 2025-03-07 09:18:38 1.4.2 临时新增 可控设备的支持
    // 串口名称
    @JsonProperty("interface_name")
    private String interfaceName;

    @ApiModelProperty(value = "波特率")
    // 波特率
    @JsonProperty("bitrate")
    private Integer bitrate;

    @ApiModelProperty("连接方式(TCPCAN/SocketCAN/ModbusTCP")
    @JsonProperty("connection_type")
    private String connectionType;

    @ApiModelProperty(value = "设备名称")
    private String dev;

    @ApiModelProperty(value = "波特率")
    @JsonProperty("baud_rate")
    private Integer baudRate;

    @ApiModelProperty(value = "数据位")
    @JsonProperty("byte_size")
    private Integer byteSize;

    @ApiModelProperty(value = "停止位")
    @JsonProperty("stop_bits")
    private Integer stopBits;

    @ApiModelProperty(value = "奇偶校验")
    private String parity;

    @ApiModelProperty(value = "最高温度")
    @JsonProperty("max_temp")
    private Float maxTemp;

    @ApiModelProperty(value = "最低温度")
    @JsonProperty("min_temp")
    private Float minTemp;

    @ApiModelProperty(value = "控制模式")
    @JsonProperty("control_mode")
    private String controlMode;

    @ApiModelProperty(value = "通道Id")
    @JsonProperty("channel_ids")
    private List<Integer> channelIds;

    @ApiModelProperty(value = "设备ID")
    @JsonProperty("device_ids")
    private List<String> deviceIds;
}
