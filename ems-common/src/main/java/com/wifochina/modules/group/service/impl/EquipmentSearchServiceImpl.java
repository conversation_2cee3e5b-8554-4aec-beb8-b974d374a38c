package com.wifochina.modules.group.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.operation.util.OperationUtil;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created on 2024/6/27 11:51.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class EquipmentSearchServiceImpl implements EquipmentSearchService {

    private final GroupService groupService;

    private final DeviceService deviceService;
    private final AmmeterService ammeterService;
    private final GroupAmmeterService groupAmmeterService;
    private final GroupDeviceService groupDeviceService;

    @Override
    public DeviceIdsAndControl getDeviceIdsAndControl(SearchContext context) {
        DeviceIdsAndControl deviceIdsAndControl = new DeviceIdsAndControl();
        ProjectEntity project = context.getProject();
        String searchGroupId = context.getSearchGroupId();
        if (project != null) {
            if (!StringUtil.isEmpty(searchGroupId) && searchGroupId.equals(EmsConstants.ALL)) {
                // 查询所有
                List<String> allDeviceIds = this.equipmentIdsSupportGroup(context, false);
                deviceIdsAndControl.setDeviceIds(allDeviceIds);
                deviceIdsAndControl.setGroupController(false);
            } else {
                boolean groupController = this.groupController(context);
                List<String> deviceIds = this.equipmentIdsSupportGroup(context, groupController);
                deviceIds.addAll(
                        groupService.queryGroup(project.getId()).stream()
                                .map(GroupEntity::getId)
                                .collect(Collectors.toList()));

                deviceIdsAndControl.setDeviceIds(deviceIds);
                deviceIdsAndControl.setGroupController(groupController);
            }
        } else {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        return deviceIdsAndControl;
    }

    @Override
    public boolean groupController(SearchContext context) {
        boolean groupController = false;
        GroupEntity systemGroupEntity = context.getSystemGroup();
        if (context.getSystemGroup() == null) {
            systemGroupEntity =
                    groupService.getOne(
                            Wrappers.lambdaQuery(GroupEntity.class)
                                    .eq(GroupEntity::getProjectId, context.getProject().getId())
                                    .eq(GroupEntity::getWhetherSystem, true));
        }
        boolean groupEarningFlag =
                groupService.groupEarningCondition(context.getProject().getId(), systemGroupEntity);
        if (systemGroupEntity != null
                && groupEarningFlag
                && !StringUtil.isEmpty(context.getSearchGroupId())
                && !context.getSearchGroupId().equals(EmsConstants.ALL)) {
            // 满足 开启计算收益和分组储能收益开关
            groupController = true;
        }
        return groupController;
    }

    @Override
    public List<String> equipmentIdsSupportGroup(SearchContext context, Boolean groupController) {
        if (groupController == null) {
            groupController = groupController(context);
        }
        String projectId = context.getProject().getId();
        List<String> deviceIds;
        if (groupController) {
            deviceIds = getIncomeDeviceAmmeterIdsForGroup(projectId, context.getSearchGroupId());
        } else {
            deviceIds = getIncomeDeviceIds(projectId);
        }
        return deviceIds;
    }

    @Override
    public List<String> getIncomeDeviceAmmeterIdsForGroup(String projectId, String groupId) {
        List<String> allIds = new ArrayList<>();
        // 这里代表 查询 全部的分组, (查询非系统分组并且是绑定了收入设备或者表计的分组)
        if (groupId.equals(EmsConstants.ALL)) {
            groupService
                    .queryGroupsNotSystemAndBindIncomeDeviceOrMeter(projectId)
                    .forEach(
                            e -> {
                                allIds.addAll(getIncomeDeviceIdsForGroup(projectId, e.getId()));
                                allIds.addAll(getIncomeAmmeterIdsForGroup(projectId, e.getId()));
                            });
        } else {
            allIds.addAll(getIncomeDeviceIdsForGroup(projectId, groupId));
            allIds.addAll(getIncomeAmmeterIdsForGroup(projectId, groupId));
        }
        return allIds;
    }

    /**
     * 根据projectIds 查询出 这些项目 下面开了 收益的设备or电表的 id列表集合
     *
     * <p>TODO 可以优化成 返回一个 list holder 对象 holder 里面包裹了 deviceEntity 和 ammeterEntity 列表
     *
     * @param projectIds :projectIds
     * @return :Map<String, List<String>>
     */
    @Override
    public Map<String, List<String>> getIncomeDeviceAmmeterByProjectIds(List<String> projectIds) {
        Map<String, List<String>> resultMap = new HashMap<>();
        if (projectIds.isEmpty()) {
            return new HashMap<>();
        }
        List<DeviceEntity> deviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getIncome, true)
                        .in(DeviceEntity::getProjectId, projectIds)
                        .list();
        List<AmmeterEntity> ammeterEntities =
                ammeterService
                        .lambdaQuery()
                        .eq(AmmeterEntity::getIncome, true)
                        .in(AmmeterEntity::getProjectId, projectIds)
                        .list();

        Map<String, List<DeviceEntity>> deviceMap =
                deviceEntities.stream().collect(Collectors.groupingBy(DeviceEntity::getProjectId));
        Map<String, List<AmmeterEntity>> ammterMap =
                ammeterEntities.stream()
                        .collect(Collectors.groupingBy(AmmeterEntity::getProjectId));
        for (String projectId : projectIds) {
            List<String> equipmentIds = new ArrayList<>();
            List<DeviceEntity> devices =
                    deviceMap.computeIfAbsent(projectId, k -> new ArrayList<>());
            List<AmmeterEntity> ammeters =
                    ammterMap.computeIfAbsent(projectId, k -> new ArrayList<>());
            equipmentIds.addAll(
                    devices.stream().map(DeviceEntity::getId).collect(Collectors.toList()));
            equipmentIds.addAll(
                    ammeters.stream().map(AmmeterEntity::getId).collect(Collectors.toList()));
            resultMap.put(projectId, equipmentIds);
        }
        return resultMap;
    }

    /** 计算某段时间的差值 需要考虑差值（可用来计算昨日） */
    protected List<String> getIncomeDeviceIds(String projectId) {
        return OperationUtil.getIncomeDeviceIds(projectId, deviceService, ammeterService);
    }

    protected List<String> getIncomeDeviceIdsForGroup(String projectId, String groupId) {
        List<String> allDeviceIds = new ArrayList<>();
        List<GroupDeviceEntity> groupDeviceEntitys =
                groupDeviceService.lambdaQuery().eq(GroupDeviceEntity::getGroupId, groupId).list();
        List<String> deviceIds =
                groupDeviceEntitys.stream()
                        .map(GroupDeviceEntity::getDeviceId)
                        .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            List<DeviceEntity> deviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getIncome, true)
                            .eq(DeviceEntity::getProjectId, projectId)
                            .in(DeviceEntity::getId, deviceIds)
                            .list();
            deviceEntities.forEach(e -> allDeviceIds.add(e.getId()));
        }
        return allDeviceIds;
    }

    protected List<String> getIncomeAmmeterIdsForGroup(String projectId, String groupId) {
        List<String> allAmmeterIds = new ArrayList<>();
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService
                        .lambdaQuery()
                        .eq(GroupAmmeterEntity::getGroupId, groupId)
                        .list();
        List<String> ammeterIds =
                groupAmmeterEntities.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(ammeterIds)) {
            List<AmmeterEntity> ammeterEntities =
                    ammeterService
                            .lambdaQuery()
                            .eq(AmmeterEntity::getIncome, true)
                            .eq(AmmeterEntity::getProjectId, projectId)
                            .in(AmmeterEntity::getId, ammeterIds)
                            .list();
            ammeterEntities.forEach(e -> allAmmeterIds.add(e.getId()));
        }
        return allAmmeterIds;
    }
}
