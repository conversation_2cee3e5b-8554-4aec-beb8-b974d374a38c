package com.wifochina.modules.group.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.vo.GroupGoVo;
import com.wifochina.modules.group.vo.GroupVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 分组 服务类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface GroupService extends IService<GroupEntity> {

    /**
     * 分页查询接口
     *
     * @return : IPage<GroupVo>
     */
    IPage<GroupVO> queryGroupVO(IPage<GroupVO> groupPage, String name);

    /**
     * 查询某个项目 id 的所有分组 , projectId = WebUtils.projectId.get()
     *
     * @return : List<GroupEntity>
     */
    List<GroupGoVo> queryGroup(String projectId);

    /**
     * 查询系统分组
     *
     * @return : GroupEntity
     */
    GroupEntity systemGroupEntity(String projectId);

    List<GroupEntity> systemGroupEntitys(List<String> projectIds);

    /**
     * 查询非系统分组列表
     *
     * @return : List<GroupEntity>
     */
    List<GroupEntity> queryGroupsNotSystem();

    List<GroupEntity> queryGroupsNotSystemAndBindIncomeDeviceOrMeter(String projectId);

    boolean groupEarningCondition(String projectId, GroupEntity systemGroupEntity);

    List<String> getIncomeDeviceAmmeterIdsForGroup(String projectId, String groupId);

    List<String> getIncomeDeviceIdsForGroup(String project, String groupId);

    List<String> getIncomeAmmeterIdsForGroup(String project, String groupId);

    Map<String, GroupEntity> querySystemGroupPcsCodes(List<String> projectIds);

    List<GroupEntity> queryCapacityGroup(String projectId);


    List<GroupEntity> queryEarningGroup(String projectId);

    void removeControllableStrategy(String controllableId);

    void fillGroupName(String projectId, List<ElectricEntity> records);

    List<GroupEntity> queryGroupEntitiesByProjectId(String projectId);

    List<String> queryGroupIdsByProjectId(String projectId);

    List<GroupEntity> queryEnableDemandIncome(String projectId);

    List<GroupEntity> queryEnableDemandControl(String projectId);

    /**
     * 查询开启控需的项目ID
     *
     * @param projectIds 查询范围
     * @return
    */
    Set<String> queryEnableDemandProjectIds(Collection<String> projectIds);

    /**
     * 查询开启控容的项目ID
     *
     * @param projectIds 查询范围
     * @return
     */
    Set<String> queryEnableCapacityProjectIds(Collection<String> projectIds);
}
