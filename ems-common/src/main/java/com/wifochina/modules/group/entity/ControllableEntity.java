package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 可控设备 类
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Getter
@Setter
@TableName("t_controllable")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Controllable对象")
public class ControllableEntity extends BaseEquipEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("类型：Load、IO、Power")
    private String type;

    @ApiModelProperty("型号")
    private String vendor;

    @ApiModelProperty("配置")
    private String config;

    @TableField(exist = false)
    @ApiModelProperty(value = "是否维护,默认不维护false")
    private Boolean maintain = false;

    @ApiModelProperty("设计功率")
    private Double designPower;

    @ApiModelProperty("监听端口")
    private Integer listenPort;

    @ApiModelProperty("功率斜率")
    private Double powerStepPercentage;

    @ApiModelProperty(value = "从机号 默认 1")
    private Integer slaveId;

    @ApiModelProperty(value = "串口名称")

    // 2025-03-07 09:18:38 1.4.2 临时新增 可控设备的支持
    // 串口名称
    private String interfaceName;

    @ApiModelProperty(value = "波特率")
    // 波特率
    private Integer bitrate;

    @ApiModelProperty("连接方式(TCPCAN/SocketCAN/ModbusTCP")
    private String connectionType;

    @ApiModelProperty(value = "设备名称")
    private String dev;

    @ApiModelProperty(value = "波特率")
    private Integer baudRate;

    @ApiModelProperty(value = "数据位")
    private Integer byteSize;

    @ApiModelProperty(value = "停止位")
    private Integer stopBits;

    @ApiModelProperty(value = "奇偶校验")
    private String parity;

    @ApiModelProperty(value = "最高温度")
    private Float maxTemp;

    @ApiModelProperty(value = "最低温度")
    private Float minTemp;

    @ApiModelProperty(value = "控制模式")
    private String controlMode;

    @ApiModelProperty(value = "通道Id")
    private String channelIds;

    @ApiModelProperty(value = "设备ID")
    private String deviceIds;
}
