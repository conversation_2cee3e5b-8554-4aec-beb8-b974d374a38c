package com.wifochina.modules.group.vo;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
public class AmmeterVo {

    @ApiModelProperty(value = "电表id")
    private String id;

    @ApiModelProperty(value = "电表名称")
    private String name;

    @ApiModelProperty(value = "1 PV电表、 2并网点电表、3负载电表")
    private Integer type;

    @ApiModelProperty(value = "分时计量")
    private Boolean timeSharingMeasurement;
}
