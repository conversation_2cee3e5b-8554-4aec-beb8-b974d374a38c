package com.wifochina.modules.group.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.group.entity.GroupAmmeterEntity;
import com.wifochina.modules.group.mapper.GroupAmmeterMapper;
import com.wifochina.modules.group.service.GroupAmmeterService;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Service;

/**
 * 分组与电表关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
@AllArgsConstructor
public class GroupAmmeterServiceImpl extends ServiceImpl<GroupAmmeterMapper, GroupAmmeterEntity>
        implements GroupAmmeterService {}
