package com.wifochina.modules.group.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.StrategyTypeEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.AuthUtil;
import com.wifochina.common.util.DemandControlEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.demand.job.DemandCalcHolder;
import com.wifochina.modules.demand.service.DemandQuartzService;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.request.GroupEarningsRequest;
import com.wifochina.modules.group.request.GroupPageRequest;
import com.wifochina.modules.group.request.GroupRequest;
import com.wifochina.modules.group.request.go.GroupGoRequest;
import com.wifochina.modules.group.request.go.GroupUnitRequest;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.group.vo.GroupGoVo;
import com.wifochina.modules.group.vo.GroupVO;
import com.wifochina.modules.income.job.CustomProjectQuartzService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.GroupLogDetailService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 分组 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@RestController
@RequestMapping("/group")
@Api(tags = "07-分组管理")
@RequiredArgsConstructor
@Slf4j
public class GroupController {

    private final AmmeterService ammeterService;

    private final DeviceService deviceService;

    private final GroupService groupService;

    private final ControllableService controllableService;

    private final GroupAmmeterService groupAmmeterService;

    private final GroupDeviceService groupDeviceService;

    private final GroupControllableService groupControllableService;

    private final StrategyService strategyService;

    private final RestTemplate restTemplate;

    private final ProjectService projectService;

    private final ControllerService controllerService;

    private final DemandQuartzService demandQuartzService;

    private final GroupServiceKt groupServiceKt;

    private final LogService logService;

    @Resource(name = "demandIncomeQuartzServiceImpl")
    private CustomProjectQuartzService demandIncomeQuartzServiceImpl;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    @GetMapping("/listNotSystemIncomeGroups")
    @ApiOperation("获取所有 非系统分组且绑定了收益的设备or收益的电表的 分组列表")
    public Result<List<GroupVO>> listNotSystemIncomeGroups() {
        String projectId = WebUtils.projectId.get();
        List<GroupEntity> groupEntities =
                groupService.queryGroupsNotSystemAndBindIncomeDeviceOrMeter(projectId);
        List<GroupVO> groupVos = new ArrayList<>();
        groupEntities.forEach(
                groupEntity -> {
                    GroupVO groupVO = new GroupVO();
                    BeanUtils.copyProperties(groupEntity, groupVO);
                    groupVos.add(groupVO);
                });
        return Result.success(groupVos);
    }

    /** 获取所有的电表和分组 */
    @GetMapping("/listAllAmmeterAndDevice")
    @ApiOperation("获取所有电表、EMS和可控设备")
    @PreAuthorize("hasAuthority('/group/group')")
    public Result<Map<String, Object>> listAllAmmeterAndDevice() {
        Map<String, Object> map = new HashMap<>(2);
        Map<String, String> ammeterMap =
                ammeterService
                        .lambdaQuery()
                        .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                        .orderByAsc(AmmeterEntity::getOrderIndex)
                        .list()
                        .stream()
                        .collect(Collectors.toMap(AmmeterEntity::getId, AmmeterEntity::getName));
        map.put("ammeter", ammeterMap);
        Map<String, String> deviceMap =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                        .orderByAsc(DeviceEntity::getOrderIndex)
                        .list()
                        .stream()
                        .collect(Collectors.toMap(DeviceEntity::getId, DeviceEntity::getName));
        map.put("device", deviceMap);
        Map<String, String> controllableMap =
                controllableService
                        .lambdaQuery()
                        .eq(ControllableEntity::getProjectId, WebUtils.projectId.get())
                        .orderByAsc(ControllableEntity::getOrderIndex)
                        .list()
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ControllableEntity::getId, ControllableEntity::getName));
        map.put("controllable", controllableMap);
        return Result.success(map);
    }

    /** 增加分组 */
    @PostMapping("/add")
    @ApiOperation("增加分组")
    @Transactional(rollbackFor = Exception.class)
    @Log(module = "GROUP", type = OperationType.ADD)
    @PreAuthorize("hasAuthority('/group/group/add')")
    public Result<String> add(@RequestBody GroupRequest groupRequest) {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        // 添加分组
        GroupEntity groupEntity = new GroupEntity();
        BeanUtils.copyProperties(groupRequest, groupEntity);
        groupEntity.setId(StringUtil.uuid());
        groupEntity.setProjectId(WebUtils.projectId.get());
        groupEntity.setWhetherSystem(false);
        groupService.save(groupEntity);
        // 1.4.1 检验如果有开启的需要把其它的关闭
        groupServiceKt.controlAgcAndAvc(groupEntity);
        // 添加默认策略控制
        StrategyEntity strategyEntity = new StrategyEntity();
        strategyEntity.setPriority(groupRequest.getPriority());
        strategyEntity.setSoc(EmsConstants.SOC_DEFAULT);
        strategyEntity.setGroupId(groupEntity.getId());
        strategyEntity.setWeekDay(0);
        strategyEntity.setProjectId(WebUtils.projectId.get());
        strategyEntity.setStrategyType(
                StrategyTypeEnum.getStrategyType(projectEntity.getElectricPriceType()));
        strategyService.save(strategyEntity);
        // 添加分组和电表关系
        saveGroupAmmeter(groupRequest, groupEntity.getId());
        // 添加分组和设备关系
        saveGroupDevice(groupRequest, groupEntity.getId());
        // 添加分组和可控设备关系
        saveGroupControllable(groupRequest, groupEntity.getId());
        doGoControllerSync();
        if (Boolean.TRUE.equals(groupRequest.getCapacityController())) {
            updateCapacityJob(WebUtils.projectId.get());
        }
        // 1.3.9 改成了这个字段 demandControl 不再是 demandController了 , 新字段是结合了控制和收益
        if (DemandControlEnum.enableDemandControl(groupRequest.getDemandControl())) {
            //        if (Boolean.TRUE.equals(groupRequest.getDemandController())) {
            updateDemandJob(WebUtils.projectId.get(), false);
        }
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("GROUP")
        //                        .method("GROUP_ADD")
        //                        .object(groupRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.GROUP_INFO_TRACE_FORMAT,
        // groupEntity.getId()))
        //                        .build());
        return Result.success();
    }

    private void saveGroupDevice(GroupRequest groupRequest, String id) {
        Optional.ofNullable(groupRequest.getDeviceIds())
                .ifPresent(
                        e -> {
                            List<GroupDeviceEntity> groupDeviceEntityList =
                                    groupRequest.getDeviceIds().stream()
                                            .map(
                                                    deviceId -> {
                                                        GroupDeviceEntity groupDeviceEntity =
                                                                new GroupDeviceEntity();
                                                        groupDeviceEntity.setDeviceId(deviceId);
                                                        groupDeviceEntity.setGroupId(id);
                                                        return groupDeviceEntity;
                                                    })
                                            .collect(Collectors.toList());
                            groupDeviceService.saveBatch(groupDeviceEntityList);
                        });
    }

    private void saveGroupAmmeter(GroupRequest groupRequest, String id) {
        Optional.ofNullable(groupRequest.getAmmeterIds())
                .ifPresent(
                        e -> {
                            List<GroupAmmeterEntity> groupAmmeterEntityList =
                                    e.stream()
                                            .map(
                                                    ammeterId -> {
                                                        GroupAmmeterEntity groupAmmeterEntity =
                                                                new GroupAmmeterEntity();
                                                        groupAmmeterEntity.setAmmeterId(ammeterId);
                                                        groupAmmeterEntity.setGroupId(id);
                                                        return groupAmmeterEntity;
                                                    })
                                            .collect(Collectors.toList());
                            groupAmmeterService.saveBatch(groupAmmeterEntityList);
                        });
    }

    private void saveGroupControllable(GroupRequest groupRequest, String id) {
        Optional.ofNullable(groupRequest.getControllableIds())
                .ifPresent(
                        e -> {
                            List<GroupControllableEntity> groupControllableEntityList =
                                    e.stream()
                                            .map(
                                                    controllableId -> {
                                                        GroupControllableEntity
                                                                groupControllableEntity =
                                                                        new GroupControllableEntity();
                                                        groupControllableEntity.setControllableId(
                                                                controllableId);
                                                        groupControllableEntity.setGroupId(id);
                                                        return groupControllableEntity;
                                                    })
                                            .collect(Collectors.toList());
                            groupControllableService.saveBatch(groupControllableEntityList);
                        });
    }

    // TODO remove
    //    @PostMapping("/update_inner")
    //    @ApiOperation("内部修改分组")
    //    @Log(module = "GROUP", methods = "GROUP_UPDATE", logDetail = true)
    //    @Transactional(rollbackFor = Exception.class)
    //    public Result<String> updateInner(@RequestBody GroupRequest groupRequest) {
    //        HttpServletRequest servletRequest =
    //                ((ServletRequestAttributes)
    //
    // Objects.requireNonNull(RequestContextHolder.getRequestAttributes()))
    //                        .getRequest();
    //        String wAuth = servletRequest.getHeader(EmsConstants.GO_AUTH_HEADER);
    //        wAuthIsValid(wAuth);
    //        return update(groupRequest);
    //    }

    @PostMapping("/updateOrderIndex")
    @ApiOperation("排序")
    //    @Log(module = "GROUP", methods = "GROUP_UPDATE")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/group/group/edit')")
    public Result<String> update(@RequestBody List<String> ids) {
        List<GroupEntity> entities = groupService.listByIds(ids);
        // 2. 按照传入的 ids 顺序设置每个 DeviceEntity 的 index 字段
        List<GroupEntity> orderedEntities =
                ids.stream()
                        .map(
                                id ->
                                        entities.stream()
                                                .filter(
                                                        deviceEntity ->
                                                                deviceEntity.getId().equals(id))
                                                .findFirst()
                                                .orElseThrow(
                                                        () ->
                                                                new ServiceException(
                                                                        "ID " + id + " not found")))
                        .collect(Collectors.toList());

        // 设置 index
        for (int i = 0; i < orderedEntities.size(); i++) {
            GroupEntity entity = orderedEntities.get(i);
            entity.setOrderIndex(i); // 假设 setIndex 方法存在
        }
        groupService.updateBatchById(orderedEntities);
        return Result.success();
    }

    private static void wAuthIsValid(String wAuth) {
        if (!AuthUtil.validateAuth(wAuth)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ACCESS.value());
        }
    }

    /** 修改分组 */
    @PostMapping("/updateGroupEarnings")
    @ApiOperation("修改分组储能收益")
    @Log(module = "GROUP", methods = "GROUP_UPDATE_EARNINGS", type = OperationType.UPDATE_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/operation/updateGroupEarnings')")
    public Result<String> updateGroupEarnings(
            @RequestBody GroupEarningsRequest groupEarningsRequest) {
        GroupEntity group = groupService.getById(groupEarningsRequest.getId());
        group.setGroupEarningsController(groupEarningsRequest.getGroupEarningsController());
        groupService.updateById(group);
        //        logService.logUpdateDetail(
        //                LogInfo.builder()
        //                        .module("GROUP")
        //                        .method("GROUP_UPDATE")
        //                        .object(groupEarningsRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.GROUP_EARNINGS_TRACE_FORMAT,
        //                                        groupEarningsRequest.getId()))
        //                        .build());
        return Result.success();
    }

    /** 修改分组 */
    @PostMapping("/update")
    @ApiOperation("修改分组")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/group/group/edit')")
    @Log(module = "GROUP", type = OperationType.UPDATE)
    public Result<String> update(@RequestBody GroupRequest groupRequest) {
        Integer newDemandPeriod = groupRequest.getDemandPeriod();
        GroupEntity group = groupService.getById(groupRequest.getId());
        Integer oldDemandPeriod = group.getDemandPeriod();
        GroupEntity systemGroup = groupService.systemGroupEntity(WebUtils.projectId.get());
        BeanUtils.copyProperties(groupRequest, group);
        if (!Objects.equals(group.getId(), systemGroup.getId())) {
            group.setWhetherSystem(false);
            group.setWaveRecord(false);
        }
        groupService.updateById(group);
        // 1.4.1 检验如果有开启的需要把其它的关闭
        groupServiceKt.controlAgcAndAvc(group);
        updateStrategyPriority(groupRequest, group);
        updateGroupMeter(groupRequest);
        updateGroupDevice(groupRequest);
        updateGroupControllable(groupRequest);
        // 1.3.9 改成了这个demandControl 判断
        if (DemandControlEnum.enableDemandControl(groupRequest.getDemandControl())) {
            log.info("update group:{} oldDemandPeriod:{}", group.getId(), oldDemandPeriod);
            log.info("update group:{} newDemandPeriod:{}", group.getId(), newDemandPeriod);
            // 2025-05-08 19:21:24 fix bug just check enable control to update demandJob
            updateDemandJob(WebUtils.projectId.get(), true);
        }
        if (Boolean.TRUE.equals(groupRequest.getCapacityController())) {
            updateCapacityJob(WebUtils.projectId.get());
        }
        // 1.3.10 同步demand 相关的参数 保证每个分组保持一致
        groupServiceKt.syncUpdateDemandParams(
                group.getDemandCalcModel(), group.getDemandPeriod(), group.getSlipTime());
        doGoControllerSync();
        return Result.success();
    }

    private void updateGroupControllable(GroupRequest groupRequest) {
        Optional.ofNullable(groupRequest.getControllableIds())
                .ifPresent(
                        e -> {
                            groupControllableService.remove(
                                    Wrappers.lambdaQuery(GroupControllableEntity.class)
                                            .eq(
                                                    GroupControllableEntity::getGroupId,
                                                    groupRequest.getId()));
                            saveGroupControllable(groupRequest, groupRequest.getId());
                        });
    }

    private void updateGroupDevice(GroupRequest groupRequest) {
        Optional.ofNullable(groupRequest.getDeviceIds())
                .ifPresent(
                        e -> {
                            groupDeviceService.remove(
                                    Wrappers.lambdaQuery(GroupDeviceEntity.class)
                                            .eq(
                                                    GroupDeviceEntity::getGroupId,
                                                    groupRequest.getId()));
                            saveGroupDevice(groupRequest, groupRequest.getId());
                        });
    }

    private void updateGroupMeter(GroupRequest groupRequest) {
        Optional.ofNullable(groupRequest.getAmmeterIds())
                .ifPresent(
                        e -> {
                            groupAmmeterService.remove(
                                    Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                                            .eq(
                                                    GroupAmmeterEntity::getGroupId,
                                                    groupRequest.getId()));
                            saveGroupAmmeter(groupRequest, groupRequest.getId());
                        });
    }

    private void updateStrategyPriority(GroupRequest groupRequest, GroupEntity group) {
        Optional.ofNullable(groupRequest.getPriority())
                .ifPresent(
                        function ->
                                strategyService.update(
                                        Wrappers.lambdaUpdate(StrategyEntity.class)
                                                .eq(StrategyEntity::getGroupId, group.getId())
                                                .set(
                                                        StrategyEntity::getPriority,
                                                        group.getPriority())));
    }

    public void updateDemandJob(String projectId, boolean updateFlag) {
        if (updateFlag) {
            // 这里可能周期变化了 改变一下 job的执行周期
            //            demandQuartzService.rescheduleDemandCalcJob(projectId);
            ProjectEntity project = projectService.getById(projectId);
            DemandCalcHolder.Companion.getHolder()
                    .computeIfAbsent(
                            project.getTimezone(),
                            s -> Collections.synchronizedSet(new HashSet<>()))
                    .add(project);
        } else {
            //
            // demandQuartzService.addDemandCalcJob(projectId);
            // 改了 1.4.1 已经改成了通过timezone 每个timezone 一个job去执行, 只需把需要执行的项目放到 set里面即可
            ProjectEntity project = projectService.getById(projectId);
            DemandCalcHolder.Companion.getHolder()
                    .computeIfAbsent(
                            project.getTimezone(),
                            s -> Collections.synchronizedSet(new HashSet<>()))
                    .add(project);
            // 这个是月度的不需要变化
            demandIncomeQuartzServiceImpl.addJob(projectId);
        }
    }

    public void updateCapacityJob(String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        demandQuartzService.addCapacityJob(projectEntity.getTimezone());
    }

    /** 删除分组 */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除分组")
    @Transactional(rollbackFor = Exception.class)
    @Log(
            module = "GROUP",
            type = OperationType.DEL,
            logDetailServiceClass = GroupLogDetailService.class)
    @PreAuthorize("hasAuthority('/group/group/delete')")
    public Result<String> delete(@PathVariable("id") String id) {
        // 系统分组不允许删除
        GroupEntity groupEntity = groupService.getById(id);
        if (groupEntity != null && groupEntity.getWhetherSystem()) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        groupService.removeById(id);
        strategyService.remove(
                Wrappers.lambdaQuery(StrategyEntity.class).eq(StrategyEntity::getGroupId, id));
        groupDeviceService.remove(
                Wrappers.lambdaQuery(GroupDeviceEntity.class)
                        .eq(GroupDeviceEntity::getGroupId, id));
        groupAmmeterService.remove(
                Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                        .eq(GroupAmmeterEntity::getGroupId, id));
        groupControllableService.remove(
                Wrappers.lambdaQuery(GroupControllableEntity.class)
                        .eq(GroupControllableEntity::getGroupId, id));
        doGoControllerSync();
        //        logService.logDeleteDetail(
        //                LogInfo.builder()
        //                        .module("GROUP")
        //                        .method("GROUP_DEL")
        //                        .object(id)
        //                        .delDetail(Map.of(id, groupEntity.getName()))
        //                        .traceId(String.format(LogService.GROUP_INFO_TRACE_FORMAT, id))
        //                        .build());
        return Result.success();
    }

    /** 查询分组 */
    @PostMapping("/query")
    @ApiOperation("查询分组")
    @PreAuthorize("hasAuthority('/group/group/query')")
    public Result<IPage<GroupVO>> query(@RequestBody GroupPageRequest groupPageRequest) {
        IPage<GroupVO> list =
                groupService.queryGroupVO(
                        Page.of(groupPageRequest.getPageNum(), groupPageRequest.getPageSize()),
                        groupPageRequest.getName());
        return Result.success(list);
    }

    /** 查询分组 */
    @PostMapping("/inner_query")
    @ApiOperation("内部查询分组")
    public Result<IPage<GroupVO>> innerQuery(@RequestBody GroupPageRequest groupPageRequest) {
        return query(groupPageRequest);
    }

    /** 同步分组情况到协同控制器 */
    public void doGoControllerSync() {
        List<GroupGoVo> groupGoVOList = groupService.queryGroup(WebUtils.projectId.get());
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        List<GroupUnitRequest> list = new ArrayList<>();
        groupGoVOList.forEach(
                (groupGoVO) -> {
                    GroupUnitRequest groupUnitRequest = new GroupUnitRequest();
                    GroupGoRequest groupGoRequest =
                            strategyService.getGroupGoRequest(projectEntity, groupGoVO.getId());
                    // 策略优先级
                    groupGoRequest.setStrategyPriority(groupGoVO.getPriority());
                    groupUnitRequest.setGroupStrategy(groupGoRequest);
                    groupUnitRequest.setUuid(groupGoVO.getId());
                    groupUnitRequest.setUuidOfEms(groupGoVO.getDevices());
                    groupUnitRequest.setUuidOfMeter(groupGoVO.getAmmeters());
                    groupUnitRequest.setUuidOfControllable(groupGoVO.getControllables());
                    groupUnitRequest.setName(groupGoVO.getName());
                    list.add(groupUnitRequest);
                });
        Map<String, List<GroupUnitRequest>> paraMap = new HashMap<>(1);
        paraMap.put("group", list);
        JSONObject jsonObject;
        try {
            //            ProjectEntity projectEntity =
            // projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/update_group",
                                paraMap,
                                JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/update_group", paraMap, JSONObject.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ServiceException(ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
        }
        assert jsonObject != null;
        Optional.of(jsonObject)
                .ifPresent(
                        json -> {
                            if (!(boolean) jsonObject.get(EmsConstants.SUCCESS)) {
                                log.error(jsonObject.toString());
                                throw new ServiceException(
                                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                            }
                        });
    }
}
