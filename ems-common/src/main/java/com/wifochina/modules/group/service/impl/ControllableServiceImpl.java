package com.wifochina.modules.group.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.wifochina.common.exception.ServiceException;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.group.entity.ControllableEntity;
import com.wifochina.modules.group.mapper.ControllableMapper;
import com.wifochina.modules.group.service.ControllableService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 协调控制器 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Service
@Transactional
public class ControllableServiceImpl extends ServiceImpl<ControllableMapper, ControllableEntity>
        implements ControllableService {

    /**
     * 大致意思就是 把 ids 这个列表 这个是有顺序的 列表 对应设置到 实体 的 order index 上
     *
     * @param ids : 更新的ids ,按照 这个 ids 的 index 索引顺序设置
     */
    @Override
    public void updateOrderIndex(List<String> ids) {

        if (CollectionUtil.isNotEmpty(ids)) {
            List<ControllableEntity> entities = this.listByIds(ids);
            Map<String, ControllableEntity> entityMap =
                    entities.stream()
                            .collect(
                                    Collectors.toMap(
                                            ControllableEntity::getId, Function.identity()));

            List<ControllableEntity> orderedEntities = new ArrayList<>();
            for (int i = 0; i < ids.size(); i++) {
                ControllableEntity entity = entityMap.get(ids.get(i));
                if (entity == null) {
                    throw new ServiceException("ID " + ids.get(i) + " not found");
                }
                // core 设置 ids 列表的 index 索引顺序 到 对应的 实体上
                entity.setOrderIndex(i);
                orderedEntities.add(entity);
            }
            this.updateBatchById(orderedEntities);
        }
    }
}
