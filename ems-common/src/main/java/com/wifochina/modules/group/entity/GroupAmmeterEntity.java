package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 分组与电表关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode()
@TableName("t_group_ammeter")
@ApiModel(value = "GroupAmmeterEntity对象", description = "分组与电表关联表")
public class GroupAmmeterEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关系id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "分组id")
    @TableField("groupId")
    private String groupId;

    @ApiModelProperty(value = "电表id")
    @TableField("ammeterId")
    private String ammeterId;

}
