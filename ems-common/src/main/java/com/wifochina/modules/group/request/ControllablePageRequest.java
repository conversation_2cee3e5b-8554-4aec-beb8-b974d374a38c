package com.wifochina.modules.group.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ControllablePageRequest extends PageBean {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型：Load、IO、Power")
    private String type;

    @ApiModelProperty("型号")
    private String vendor;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

}
