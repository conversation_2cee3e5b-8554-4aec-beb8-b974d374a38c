package com.wifochina.modules.group.controller;

import cn.hutool.core.bean.BeanUtil;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.NetworkHelper;
import com.wifochina.common.util.RegexUtil;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.group.entity.CameraEntity;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.request.CameraPageRequest;
import com.wifochina.modules.group.request.CameraRequest;
import com.wifochina.modules.group.request.InternetIpRequest;
import com.wifochina.modules.group.request.ProjectRequest;
import com.wifochina.modules.group.request.go.CameraGoDetailRequest;
import com.wifochina.modules.group.request.go.CameraGoRequest;
import com.wifochina.modules.group.service.CameraService;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.vo.StatusVo;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.CameraLogDetailService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 摄像头 前端控制器
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Slf4j
@RestController
@Api(tags = "25-摄像头管理")
@RequestMapping("/camera")
public class CameraController {
    @Resource private CameraService cameraService;

    @Resource private RestTemplate restTemplate;

    @Resource private ProjectService projectService;

    @Resource private ControllerService controllerService;

    @Resource private LogService logService;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    /** 测试电表连通性 */
    @PostMapping("/connect")
    @ApiOperation("测试摄像头连通性")
    @PreAuthorize("hasAuthority('/group/camera')")
    public Result<String> isConnect(@RequestBody InternetIpRequest internetIpRequest) {
        return NetworkHelper.isConnect(internetIpRequest);
    }

    /** 增加电表 */
    @PostMapping("/add")
    @ApiOperation("增加摄像头")
    @PreAuthorize("hasAuthority('/group/camera/add')")
    @Log(module = "CAMERA", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> add(@RequestBody CameraRequest cameraRequest) {
        if (RegexUtil.isNotIp(cameraRequest.getIp())) {
            throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
        }
        CameraEntity cameraEntity = new CameraEntity();
        BeanUtils.copyProperties(cameraRequest, cameraEntity);
        cameraEntity.setId(StringUtil.uuid());
        cameraEntity.setProjectId(WebUtils.projectId.get());
        cameraService.save(cameraEntity);
        // 同步到协调控制器
        doGoControllerSync();
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("CAMERA")
        //                        .method("CAMERA_ADD")
        //                        .object(cameraRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.CAMERA_INFO_TRACE_FORMAT,
        // cameraEntity.getId()))
        //                        .build());
        return Result.success();
    }

    /** 修改电表 */
    @PostMapping("/update")
    @ApiOperation("修改摄像头")
    @PreAuthorize("hasAuthority('/group/camera/edit')")
    @Log(module = "CAMERA", type = OperationType.UPDATE)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody CameraRequest cameraRequest) {
        if (cameraRequest.getIp() != null && RegexUtil.isNotIp(cameraRequest.getIp())) {
            throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
        }
        CameraEntity cameraEntity = new CameraEntity();
        BeanUtils.copyProperties(cameraRequest, cameraEntity);
        cameraService.updateById(cameraEntity);
        // 同步到协调控制器
        doGoControllerSync();
        //        logService.logUpdateDetail(
        //                LogInfo.builder()
        //                        .module("CAMERA")
        //                        .method("CAMERA_UPDATE")
        //                        .object(cameraRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.CAMERA_INFO_TRACE_FORMAT,
        // cameraRequest.getId()))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/updateOrderIndex")
    @ApiOperation("排序")
    @Log(module = "CAMERA", methods = "CAMERA_UPDATE")
    @PreAuthorize("hasAuthority('/group/ammeter/edit')")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody List<String> ids) {
        List<CameraEntity> entities = cameraService.listByIds(ids);
        // 2. 按照传入的 ids 顺序设置每个 DeviceEntity 的 index 字段
        List<CameraEntity> orderedEntities =
                ids.stream()
                        .map(
                                id ->
                                        entities.stream()
                                                .filter(
                                                        deviceEntity ->
                                                                deviceEntity.getId().equals(id))
                                                .findFirst()
                                                .orElseThrow(
                                                        () ->
                                                                new ServiceException(
                                                                        "ID " + id + " not found")))
                        .collect(Collectors.toList());

        // 设置 index
        for (int i = 0; i < orderedEntities.size(); i++) {
            CameraEntity entity = orderedEntities.get(i);
            entity.setOrderIndex(i); // 假设 setIndex 方法存在
        }
        cameraService.updateBatchById(orderedEntities);
        return Result.success();
    }

    /** 删除电表 */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除摄像头")
    @PreAuthorize("hasAuthority('/group/camera/delete')")
    @Log(
            module = "CAMERA",
            type = OperationType.DEL,
            logDetailServiceClass = CameraLogDetailService.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> delete(@PathVariable("id") String id) {
        CameraEntity cameraEntity = cameraService.getById(id);
        cameraService.removeById(id);
        // 同步到协调控制器
        doGoControllerSync();
        //        logService.logDeleteDetail(
        //                LogInfo.builder()
        //                        .module("CAMERA")
        //                        .method("CAMERA_DEL")
        //                        .object(id)
        //                        .delDetail(Map.of(cameraEntity.getId(), cameraEntity.getName()))
        //                        .traceId(String.format(LogService.CAMERA_INFO_TRACE_FORMAT, id))
        //                        .build());
        return Result.success();
    }

    /**
     * 2025-02-06 11:51:41 看上去实现 和 可控设备的实现 基本一致 不知道是否是 一类东西 , 只是CameraEntity 比 ControllableEntity
     * 多了个 厂商字段
     */
    /** 查询电表 */
    @PostMapping("/query")
    @ApiOperation("查询摄像头")
    @PreAuthorize("hasAuthority('/group/camera/query')")
    public Result<IPage<CameraEntity>> query(@RequestBody CameraPageRequest cameraPageRequest) {
        Page<CameraEntity> page =
                Page.of(cameraPageRequest.getPageNum(), cameraPageRequest.getPageSize());
        JSONObject jsonObject;
        List<String> ids = new ArrayList<>();
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/get_online", null, JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/get_online", null, JSONObject.class);
            }
            assert jsonObject != null;
            List<StatusVo> tmpList = jsonObject.getJSONArray("camera").toJavaList(StatusVo.class);
            if (StringUtil.notEmpty(cameraPageRequest.getStatus())) {
                boolean status = cameraPageRequest.getStatus() == 1;
                ids =
                        tmpList.stream()
                                .filter(e -> e.getOnline() == status)
                                .map(StatusVo::getUuid)
                                .collect(Collectors.toList());
            }
            Map<String, Boolean> map =
                    tmpList.stream()
                            .collect(Collectors.toMap(StatusVo::getUuid, StatusVo::getOnline));
            IPage<CameraEntity> list =
                    cameraService.page(
                            page,
                            Wrappers.lambdaQuery(CameraEntity.class)
                                    .eq(CameraEntity::getProjectId, WebUtils.projectId.get())
                                    .in(!ids.isEmpty(), CameraEntity::getId, ids)
                                    .eq(
                                            cameraPageRequest.getStatus() != null && ids.isEmpty(),
                                            CameraEntity::getId,
                                            "0")
                                    .like(
                                            StringUtil.notEmpty(cameraPageRequest.getName()),
                                            CameraEntity::getName,
                                            cameraPageRequest.getName())
                                    .eq(
                                            StringUtil.notEmpty(cameraPageRequest.getVendor()),
                                            CameraEntity::getVendor,
                                            cameraPageRequest.getVendor())
                                    .like(
                                            StringUtils.hasLength(cameraPageRequest.getType()),
                                            CameraEntity::getType,
                                            cameraPageRequest.getType())
                                    .orderByAsc(CameraEntity::getOrderIndex)
                                    .orderByAsc(CameraEntity::getCreateTime));
            for (CameraEntity cameraEntity : list.getRecords()) {
                boolean status =
                        map.get(cameraEntity.getId()) != null && map.get(cameraEntity.getId());
                cameraEntity.setStatus(status ? 1 : 0);
            }
            return Result.success(list);
        } catch (Exception e) {
            log.error("get camera list -------> cannot connect to site control");
            IPage<CameraEntity> pageList =
                    cameraService.page(
                            page,
                            Wrappers.lambdaQuery(CameraEntity.class)
                                    .eq(CameraEntity::getProjectId, WebUtils.projectId.get())
                                    .like(
                                            StringUtil.notEmpty(cameraPageRequest.getName()),
                                            CameraEntity::getName,
                                            cameraPageRequest.getName())
                                    .eq(
                                            StringUtil.notEmpty(cameraPageRequest.getVendor()),
                                            CameraEntity::getVendor,
                                            cameraPageRequest.getVendor())
                                    .like(
                                            StringUtils.hasLength(cameraPageRequest.getType()),
                                            CameraEntity::getType,
                                            cameraPageRequest.getType())
                                    .orderByAsc(CameraEntity::getCreateTime));
            pageList.getRecords().forEach(re -> re.setStatus(0));
            return Result.success(pageList);
        }
    }

    public void doGoControllerSync() {
        List<CameraEntity> cameraEntities =
                cameraService.list(
                        Wrappers.lambdaQuery(CameraEntity.class)
                                .eq(CameraEntity::getProjectId, WebUtils.projectId.get()));
        List<CameraGoRequest> list =
                cameraEntities.stream()
                        .map(
                                camera -> {
                                    CameraGoRequest cameraGoRequest = new CameraGoRequest();
                                    BeanUtils.copyProperties(camera, cameraGoRequest);
                                    return cameraGoRequest;
                                })
                        .collect(Collectors.toList());
        Map<String, List<CameraGoRequest>> paraMap = new HashMap<>(1);
        paraMap.put("camera", list);
        JSONObject jsonObject;
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/update_camera",
                                paraMap,
                                JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/update_camera", paraMap, JSONObject.class);
            }
        } catch (Exception e) {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() != 1) {
                log.error("update camera gatewayUrl-->" + gatewayUrl);
            }
            throw new ServiceException(ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
        }
        assert jsonObject != null;
        String result = "success";
        Optional.of(jsonObject)
                .ifPresent(
                        json -> {
                            if (!(boolean) jsonObject.get(result)) {
                                throw new ServiceException(
                                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                            }
                        });
    }

    /** 查询设备 */
    @PostMapping("/list")
    @ApiOperation("一次查询摄像头")
    public Result<List<CameraEntity>> queryInner(@RequestBody ProjectRequest projectRequest) {
        return Result.success(
                cameraService
                        .lambdaQuery()
                        .eq(CameraEntity::getProjectId, projectRequest.getProjectId())
                        .orderByAsc(CameraEntity::getOrderIndex)
                        .orderByAsc(CameraEntity::getCreateTime)
                        .list());
    }

    @PostMapping("/detail")
    @ApiOperation("查询摄像头详情")
    @PreAuthorize("hasAuthority('/group/camera') or hasAuthority('/monitor/camera')")
    public Result<String> getDetail(@RequestBody CameraGoDetailRequest cameraGoDetailRequest) {
        JSONObject jsonObject;
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.getForObject(
                                outerControllerUrl
                                        + "/api/v1/get_camera_screenshot?uuid={uuid}&preview={preview}&height={height}&width={width}",
                                JSONObject.class,
                                BeanUtil.beanToMap(cameraGoDetailRequest));
            } else {
                jsonObject =
                        restTemplate.getForObject(
                                gatewayUrl
                                        + "/api/v1/get_camera_screenshot?uuid={uuid}&preview={preview}&height={height}&width={width}",
                                JSONObject.class,
                                BeanUtil.beanToMap(cameraGoDetailRequest));
            }
        } catch (Exception e) {
            log.error("get camera detail error -->" + e.getMessage());
            throw new ServiceException(ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
        }
        assert jsonObject != null;
        String result = "Success";
        Optional.of(jsonObject)
                .ifPresent(
                        json -> {
                            if (!(boolean) json.get(result)) {
                                log.error("get camera detail-->" + json.get("Message").toString());
                                throw new ServiceException(
                                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                            }
                        });
        return Result.success(jsonObject.get("Data").toString());
    }
}
