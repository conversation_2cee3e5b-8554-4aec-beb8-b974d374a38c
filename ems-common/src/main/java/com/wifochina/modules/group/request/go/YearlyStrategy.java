package com.wifochina.modules.group.request.go;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2025/4/29 09:29.
 *
 * <AUTHOR>
 */
@Data
public class YearlyStrategy implements Cloneable {

    @ApiModelProperty(value = "策略类型  0 充电 1放电 2自发自用")
    private Integer function;

    @ApiModelProperty(value = "功率")
    private Integer power;

    @ApiModelProperty(value = "开始分钟")
    private Integer start_minute;

    @ApiModelProperty(value = "结束分钟")
    private Integer end_minute;

    @ApiModelProperty(value = "soc")
    private Integer soc;

    @Override
    public YearlyStrategy clone() throws CloneNotSupportedException {
        return (YearlyStrategy) super.clone();
    }
}
