package com.wifochina.modules.group.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
public class DeviceRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty(value = "设备名称")
    private String name;

    @ApiModelProperty(value = "设备ip")
    private String ip;

    @ApiModelProperty(value = "设备端口")
    private Integer port;

    @ApiModelProperty(value = "0未连接1已连接2未连接")
    private Integer status;

    @ApiModelProperty(value = "设备二维码")
    private String code;

    @ApiModelProperty(value = "电量输出初始值")
    private Double outputHistoryInit;

    @ApiModelProperty(value = "电量输入初始值")
    private Double inputHistoryInit;

    @ApiModelProperty(value = "是否虚拟,默认不虚拟false")
    private Boolean unreal;

    @ApiModelProperty(value = "虚拟设备的pcs序号,0,1,2,3,4")
    private Integer pcsIndex;

    @ApiModelProperty(value = "功率分配百分比")
    private Float powerCapacityPercent;

    @ApiModelProperty(value = "是否维护,默认不维护false")
    private Boolean maintain;

    @ApiModelProperty(value = "是否显示gps，默认不显示false")
    private Boolean showGps;

    @ApiModelProperty(value = "是否计算收益,默认不维护false")
    private Boolean income;

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "是否有STS")
    private Boolean hasSts;

    @ApiModelProperty(value = "是否有DCDC")
    private Boolean hasDcdc;

    @ApiModelProperty(value = "pv表初始放电量初始值")
    private Double pvOutputHistoryInit;

    @ApiModelProperty(value = "pv表初始充电量初始值")
    private Double pvInputHistoryInit;

    @ApiModelProperty(value = "排序")
    private Integer orderIndex;

    @ApiModelProperty(value = "是否主机")
    private Boolean isHost = false;

    @ApiModelProperty(value = "水冷预开启时间")
    private Integer coolerPreOpenTime;

    @ApiModelProperty(value = "设备的控制模式")
    private String deviceControlModel;
}
