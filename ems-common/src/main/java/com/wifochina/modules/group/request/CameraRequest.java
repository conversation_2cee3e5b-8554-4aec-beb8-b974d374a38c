package com.wifochina.modules.group.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
public class CameraRequest {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty(value = "摄像头名称")
    private String name;

    @ApiModelProperty(value = "摄像头ip")
    private String ip;

    @ApiModelProperty(value = "摄像头端口")
    private Integer port;

    @ApiModelProperty(value = "摄像头类型")
    private String type;

    @ApiModelProperty(value = "摄像头型号，厂商名称")
    private String vendor;

    @ApiModelProperty("摄像头用户名")
    private String username;

    @ApiModelProperty("摄像头密码")
    private String password;

    @ApiModelProperty("摄像头认证类型(basic、digest)")
    private String authType;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private Integer orderIndex;

}
