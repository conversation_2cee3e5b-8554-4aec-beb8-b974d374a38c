package com.wifochina.modules.group.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.NetworkHelper;
import com.wifochina.common.util.RegexUtil;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.entity.GroupAmmeterEntity;
import com.wifochina.modules.group.enums.AmmeterConnectTypeEnum;
import com.wifochina.modules.group.request.AmmeterPageRequest;
import com.wifochina.modules.group.request.AmmeterRequest;
import com.wifochina.modules.group.request.InternetIpRequest;
import com.wifochina.modules.group.request.ProjectRequest;
import com.wifochina.modules.group.request.go.AmmeterGoRequest;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.service.GroupAmmeterService;
import com.wifochina.modules.group.vo.AmmeterVo;
import com.wifochina.modules.group.vo.StatusVo;
import com.wifochina.modules.initelectric.InitElectricService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.AmmeterLogDetailService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.modules.user.service.LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 电表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Slf4j
@RestController
@Api(tags = "06-电表管理")
@RequestMapping("/ammeter")
@RequiredArgsConstructor
public class AmmeterController {

    private final AmmeterService ammeterService;

    private final GroupAmmeterService groupAmmeterService;

    private final RestTemplate restTemplate;

    private final ProjectService projectService;

    private final ControllerService controllerService;

    private final InitElectricService initElectricService;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    /** 测试电表连通性 */
    @PostMapping("/connect")
    @ApiOperation("测试电表连通性")
    @PreAuthorize("hasAuthority('/group/ammeter')")
    public Result<String> isConnect(@RequestBody InternetIpRequest internetIpRequest) {
        return NetworkHelper.isConnect(internetIpRequest);
    }

    /** 增加电表 */
    @PostMapping("/add")
    @ApiOperation("增加电表")
    @PreAuthorize("hasAuthority('/group/ammeter/add')")
    @Log(module = "METER", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> add(@RequestBody AmmeterRequest ammeterRequest) {
        checkIp(ammeterRequest);
        checkIndexExist(ammeterRequest);
        AmmeterEntity ammeterEntity = new AmmeterEntity();
        BeanUtils.copyProperties(ammeterRequest, ammeterEntity);
        ammeterEntity.setId(StringUtil.uuid());
        ammeterEntity.setProjectId(WebUtils.projectId.get());
        ammeterService.save(ammeterEntity);
        // 同步到协调控制器
        doGoControllerSync();
        return Result.success();
    }

    /** 修改电表 */
    @PostMapping("/update")
    @ApiOperation("修改电表")
    @PreAuthorize("hasAuthority('/group/ammeter/edit')")
    @Log(module = "METER", type = OperationType.UPDATE)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody AmmeterRequest ammeterRequest) {
        checkIp(ammeterRequest);
        checkIndexExist(ammeterRequest);
        AmmeterEntity ammeterEntity = new AmmeterEntity();
        BeanUtils.copyProperties(ammeterRequest, ammeterEntity);
        ammeterService.updateById(ammeterEntity);
        // 同步到协调控制器
        doGoControllerSync();
        // update init electric
        initElectricService.getInitElectric(
                projectService.getById(WebUtils.projectId.get()), ammeterEntity);
        return Result.success();
    }

    /** 修改设备 */
    @PostMapping("/updateOrderIndex")
    @ApiOperation("排序")
    @Log(module = "METER", methods = "METER_UPDATE")
    @PreAuthorize("hasAuthority('/group/ammeter/edit')")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody List<String> ids) {
        List<AmmeterEntity> entities = ammeterService.listByIds(ids);
        // 2. 按照传入的 ids 顺序设置每个 DeviceEntity 的 index 字段
        List<AmmeterEntity> orderedEntities =
                ids.stream()
                        .map(
                                id ->
                                        entities.stream()
                                                .filter(
                                                        deviceEntity ->
                                                                deviceEntity.getId().equals(id))
                                                .findFirst()
                                                .orElseThrow(
                                                        () ->
                                                                new ServiceException(
                                                                        "ID " + id + " not found")))
                        .collect(Collectors.toList());

        // 设置 index
        for (int i = 0; i < orderedEntities.size(); i++) {
            AmmeterEntity entity = orderedEntities.get(i);
            // 假设 setIndex 方法存在
            entity.setOrderIndex(i);
        }
        ammeterService.updateBatchById(orderedEntities);
        return Result.success();
    }

    private static void checkIp(AmmeterRequest ammeterRequest) {
        Optional.ofNullable(ammeterRequest.getIp())
                .ifPresent(
                        (ip) -> {
                            if (RegexUtil.isNotIp(ip)) {
                                throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
                            }
                        });
    }

    private void checkIndexExist(AmmeterRequest ammeterRequest) {
        if (ammeterRequest.getIndex() != null && 0 != ammeterRequest.getIndex()) {
            AmmeterEntity tempAmmeterEntity =
                    ammeterService
                            .lambdaQuery()
                            .ne(AmmeterEntity::getId, ammeterRequest.getId())
                            .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                            .eq(AmmeterEntity::getIndex, ammeterRequest.getIndex())
                            .one();
            if (tempAmmeterEntity != null) {
                throw new ServiceException(ErrorResultCode.INDEX_EXIST.value());
            }
        }
        // 需要额外2个参数
        if (ammeterRequest.getConnectType() != null
                && AmmeterConnectTypeEnum.web_socket
                        .getCode()
                        .equalsIgnoreCase(ammeterRequest.getConnectType())) {
            // 需要2个额外的参数
            if (StringUtil.isEmpty(ammeterRequest.getWsDeviceType())
                    || StringUtil.isEmpty(ammeterRequest.getWsDeviceName())) {
                throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
            }
        }
    }

    /** 删除电表 */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除电表")
    @PreAuthorize("hasAuthority('/group/ammeter/delete')")
    @Log(
            module = "METER",
            type = OperationType.DEL,
            logDetailServiceClass = AmmeterLogDetailService.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> delete(@PathVariable("id") String id) {
        ammeterService.removeById(id);
        groupAmmeterService.remove(
                Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                        .eq(GroupAmmeterEntity::getAmmeterId, id));
        // 同步到协调控制器
        doGoControllerSync();
        return Result.success();
    }

    /** 查询电表 */
    @PostMapping("/query")
    @ApiOperation("查询电表")
    @PreAuthorize("hasAuthority('/group/ammeter/query')")
    public Result<IPage<AmmeterEntity>> query(@RequestBody AmmeterPageRequest ammeterPageRequest) {
        Page<AmmeterEntity> page =
                Page.of(ammeterPageRequest.getPageNum(), ammeterPageRequest.getPageSize());
        JSONObject jsonObject;
        List<String> ids = new ArrayList<>();
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/get_online", null, JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/get_online", null, JSONObject.class);
            }
            assert jsonObject != null;
            List<StatusVo> tmpList = jsonObject.getJSONArray("meter").toJavaList(StatusVo.class);
            if (StringUtil.notEmpty(ammeterPageRequest.getStatus())) {
                boolean status = ammeterPageRequest.getStatus() == 1;
                ids =
                        tmpList.stream()
                                .filter(e -> e.getOnline() == status)
                                .map(StatusVo::getUuid)
                                .collect(Collectors.toList());
            }
            Map<String, Boolean> map =
                    tmpList.stream()
                            .collect(Collectors.toMap(StatusVo::getUuid, StatusVo::getOnline));
            IPage<AmmeterEntity> list =
                    ammeterService.page(
                            page,
                            Wrappers.lambdaQuery(AmmeterEntity.class)
                                    .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                                    .in(!ids.isEmpty(), AmmeterEntity::getId, ids)
                                    .eq(
                                            ammeterPageRequest.getStatus() != null && ids.isEmpty(),
                                            AmmeterEntity::getId,
                                            "0")
                                    .like(
                                            StringUtil.notEmpty(ammeterPageRequest.getName()),
                                            AmmeterEntity::getName,
                                            ammeterPageRequest.getName())
                                    .eq(
                                            StringUtil.notEmpty(ammeterPageRequest.getVendor()),
                                            AmmeterEntity::getVendor,
                                            ammeterPageRequest.getVendor())
                                    .eq(
                                            ammeterPageRequest.getType() != null,
                                            AmmeterEntity::getType,
                                            ammeterPageRequest.getType())
                                    .orderByAsc(AmmeterEntity::getOrderIndex)
                                    .orderByAsc(AmmeterEntity::getType)
                                    .orderByAsc(AmmeterEntity::getCreateTime));
            for (AmmeterEntity ammeterEntity : list.getRecords()) {
                boolean status =
                        map.get(ammeterEntity.getId()) != null && map.get(ammeterEntity.getId());
                ammeterEntity.setStatus(status ? 1 : 0);
            }
            // 2023-11-07 10:07:52 add 从go同步电表大类型 直流还是交流类型 (只同步未同步过的并且在线的电表设备)
            // 2023-11-08 13:12:38 最新 : 不需要同步的电表类型 人工新增编辑的时候维护
            // ammeterService.updateMeterDcOrAc(list.getRecords());
            return Result.success(list);
        } catch (Exception e) {
            log.error("get ammeter list -------> cannot connect to site control");
            IPage<AmmeterEntity> pageList =
                    ammeterService.page(
                            page,
                            Wrappers.lambdaQuery(AmmeterEntity.class)
                                    .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                                    .like(
                                            StringUtil.notEmpty(ammeterPageRequest.getName()),
                                            AmmeterEntity::getName,
                                            ammeterPageRequest.getName())
                                    .eq(
                                            StringUtil.notEmpty(ammeterPageRequest.getVendor()),
                                            AmmeterEntity::getVendor,
                                            ammeterPageRequest.getVendor())
                                    .eq(
                                            ammeterPageRequest.getType() != null,
                                            AmmeterEntity::getType,
                                            ammeterPageRequest.getType())
                                    .orderByAsc(AmmeterEntity::getType)
                                    .orderByAsc(AmmeterEntity::getCreateTime));
            pageList.getRecords().forEach(re -> re.setStatus(0));
            return Result.success(pageList);
        }
    }

    public void doGoControllerSync() {
        List<AmmeterEntity> ammeterEntities =
                ammeterService.list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get()));
        List<AmmeterGoRequest> list =
                ammeterEntities.stream()
                        .map(
                                ammeter -> {
                                    AmmeterGoRequest ammeterGoRequest = new AmmeterGoRequest();
                                    ammeterGoRequest.setProtocol(ammeter.getProtocol());
                                    ammeterGoRequest.setPort(ammeter.getPort());
                                    ammeterGoRequest.setDevice(ammeter.getDevice());
                                    ammeterGoRequest.setBaudRate(ammeter.getBaudRate());
                                    ammeterGoRequest.setDataBits(ammeter.getDataBits());
                                    ammeterGoRequest.setStopBits(ammeter.getStopBits());
                                    ammeterGoRequest.setParity(ammeter.getParity());
                                    ammeterGoRequest.setName(ammeter.getName());
                                    ammeterGoRequest.setIsBackup(ammeter.getIsBackup());
                                    // websocket 需要额外的2个方式
                                    if (AmmeterConnectTypeEnum.web_socket
                                            .getCode()
                                            .equalsIgnoreCase(ammeter.getConnectType())) {
                                        ammeterGoRequest.setWsDeviceType(ammeter.getWsDeviceType());
                                        ammeterGoRequest.setWsDeviceName(ammeter.getWsDeviceName());
                                    }
                                    if (!"nil".equals(ammeter.getFrequencyRegulation())) {
                                        ammeterGoRequest.setAux(ammeter.getFrequencyRegulation());
                                    } else {
                                        ammeterGoRequest.setAux("nil");
                                    }
                                    ammeterGoRequest.setHost(ammeter.getIp());
                                    if (ammeter.getOutputHistoryInit() != null) {
                                        ammeterGoRequest.setOutputHistoryInit(
                                                ammeter.getOutputHistoryInit());
                                    }
                                    if (ammeter.getInputHistoryInit() != null) {
                                        ammeterGoRequest.setInputHistoryInit(
                                                ammeter.getInputHistoryInit());
                                    }
                                    // 1PV电表;2并网点电表;3负载电表;4风电;5柴油;6充电桩;7通用
                                    switch (Objects.requireNonNull(
                                            MeterTypeEnum.getMeterType(ammeter.getType()))) {
                                        case PV:
                                            ammeterGoRequest.setType("PV");
                                            if ("EMSMeter".equals(ammeter.getVendor())) {
                                                ammeterGoRequest.setAux("230");
                                            }
                                            break;
                                        case GRID:
                                            ammeterGoRequest.setType("Grid");
                                            if ("EMSMeter".equals(ammeter.getVendor())) {
                                                ammeterGoRequest.setAux("210");
                                            }
                                            break;
                                        case LOAD:
                                            ammeterGoRequest.setType("Load");
                                            if ("EMSMeter".equals(ammeter.getVendor())) {
                                                ammeterGoRequest.setAux("220");
                                            }
                                            break;
                                        case WIND:
                                            ammeterGoRequest.setType("Wind");
                                            break;
                                        case DIESEL:
                                            // 柴油
                                            ammeterGoRequest.setType("Diesel");
                                            break;
                                        case PILE:
                                            // 充电桩
                                            ammeterGoRequest.setType("Pile");
                                            break;
                                        case COMMON:
                                            ammeterGoRequest.setType("Common");
                                            break;
                                        case GAS:
                                            ammeterGoRequest.setType("Gas");
                                            break;
                                        case EMS:
                                            // 2023-11-08 10:48:29 add 储能电表同步到 go
                                            ammeterGoRequest.setType("Ems");
                                            break;
                                        case WASTER:
                                            // 2023-11-08 10:48:29 add 余热发电类型的电表同步到 go
                                            ammeterGoRequest.setType("Waster");
                                            break;
                                        default:
                                    }
                                    ammeterGoRequest.setUuid(ammeter.getId());
                                    BeanUtils.copyProperties(ammeter, ammeterGoRequest);
                                    return ammeterGoRequest;
                                })
                        .collect(Collectors.toList());
        Map<String, List<AmmeterGoRequest>> paraMap = new HashMap<>(1);
        paraMap.put("meter", list);
        JSONObject jsonObject;
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/update_meter",
                                paraMap,
                                JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/update_meter", paraMap, JSONObject.class);
            }
        } catch (Exception e) {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() != 1) {
                log.error("update meter gatewayUrl-->" + gatewayUrl);
            }
            throw new ServiceException(ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
        }
        assert jsonObject != null;
        String result = "success";
        Optional.of(jsonObject)
                .ifPresent(
                        json -> {
                            if (!(boolean) jsonObject.get(result)) {
                                throw new ServiceException(
                                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                            }
                        });
    }

    /** 查询设备 */
    @PostMapping("/list")
    @ApiOperation("一次查询电表")
    public Result<List<AmmeterEntity>> queryInner(@RequestBody ProjectRequest projectRequest) {
        return Result.success(
                ammeterService
                        .lambdaQuery()
                        .eq(AmmeterEntity::getProjectId, projectRequest.getProjectId())
                        .orderByAsc(AmmeterEntity::getOrderIndex)
                        .orderByAsc(AmmeterEntity::getCreateTime)
                        .list());
    }

    @PostMapping("/listReadingMeter")
    @ApiOperation("查询抄表电表")
    public Result<List<AmmeterVo>> listReadingMeter(@RequestBody ProjectRequest projectRequest) {
        return Result.success(
                ammeterService
                        .lambdaQuery()
                        .eq(AmmeterEntity::getProjectId, projectRequest.getProjectId())
                        .eq(AmmeterEntity::getMeterReading, true)
                        .orderByAsc(AmmeterEntity::getOrderIndex)
                        .orderByAsc(AmmeterEntity::getCreateTime)
                        .list()
                        .stream()
                        .map(
                                e -> {
                                    AmmeterVo ammeterVo = new AmmeterVo();
                                    BeanUtils.copyProperties(e, ammeterVo);
                                    return ammeterVo;
                                })
                        .collect(Collectors.toList()));
    }
}
