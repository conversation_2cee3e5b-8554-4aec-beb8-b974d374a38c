package com.wifochina.modules.group.controller;

import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ControllableConnectionTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.NetworkHelper;
import com.wifochina.common.util.RegexUtil;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.group.entity.ControllableEntity;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.entity.GroupControllableEntity;
import com.wifochina.modules.group.request.ControllablePageRequest;
import com.wifochina.modules.group.request.ControllableRequest;
import com.wifochina.modules.group.request.InternetIpRequest;
import com.wifochina.modules.group.request.go.ControllableConfig;
import com.wifochina.modules.group.request.go.ControllableGoRequest;
import com.wifochina.modules.group.service.ControllableService;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.service.GroupControllableService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.group.vo.StatusVo;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.ControllableLogDetailService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 协调控制器 前端控制器
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "23-可控设备管理")
@RequestMapping("/controllable")
public class ControllableController {

    private final ControllableService controllableService;
    private final GroupControllableService groupControllableService;
    private final RestTemplate restTemplate;
    private final ProjectService projectService;
    private final ControllerService controllerService;
    private final GroupService groupService;
    private final LogService logService;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    /** 测试可控设备连通性 */
    @PostMapping("/connect")
    @ApiOperation("测试可控设备连通性")
    @PreAuthorize("hasAuthority('/group/controllable')")
    public Result<String> isConnect(@RequestBody InternetIpRequest internetIpRequest) {
        return NetworkHelper.isConnect(internetIpRequest);
    }

    @PostMapping("/add")
    @ApiOperation("增加可控设备")
    @PreAuthorize("hasAuthority('/group/controllable/add')")
    @Log(module = "CONTROLLABLE", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> add(@RequestBody ControllableRequest controllableRequest) {
        // HUAWEI_CHARGER充电桩 无ip 所以不校验
        // checkControllablePre(controllableRequest);
        ControllableEntity controllableEntity = new ControllableEntity();
        BeanUtils.copyProperties(controllableRequest, controllableEntity);
        controllableEntity.setId(StringUtil.uuid());
        controllableEntity.setProjectId(WebUtils.projectId.get());
        controllableService.save(controllableEntity);
        doGoControllerSync();
        return Result.success();
    }

    private void checkControllablePre(ControllableRequest controllableRequest) {
        // 以前不是华为充电桩的 都有 ip , 现在是 还有个条件 , 如果connectionType 是 SocketCAN 那么也没有 ip port
        if (ControllableConnectionTypeEnum.SocketCAN.name()
                .equals(controllableRequest.getConnectionType())) {
            // 需要有 interfaceName 和 bitrate
            if (StringUtil.isEmpty(controllableRequest.getInterfaceName())
                    || controllableRequest.getBitrate() == null) {
                throw new ServiceException(ErrorResultCode.CONTROLLABLE_SOCKET_CAN_PARAMS.value());
            }
        } else {
            if (!EmsConstants.HUAWEI_CHARGER.equals(controllableRequest.getVendor())
                    && RegexUtil.isNotIp(controllableRequest.getIp())) {
                throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
            }
        }
    }

    /** 修改电表 */
    @PostMapping("/update")
    @ApiOperation("修改可控设备")
    @PreAuthorize("hasAuthority('/group/controllable/edit')")
    @Log(module = "CONTROLLABLE", type = OperationType.UPDATE)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody ControllableRequest controllableRequest) {
        // checkControllablePre(controllableRequest);
        ControllableEntity controllableEntity = new ControllableEntity();
        BeanUtils.copyProperties(controllableRequest, controllableEntity);
        controllableService.updateById(controllableEntity);
        doGoControllerSync();
        return Result.success();
    }

    @PostMapping("/updateOrderIndex")
    @ApiOperation("排序")
    @Log(module = "CONTROLLABLE", methods = "CONTROLLABLE_UPDATE")
    @PreAuthorize("hasAuthority('/group/controllable/edit')")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody List<String> ids) {
        controllableService.updateOrderIndex(ids);
        return Result.success();
    }

    /** 删除电表 */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除可控设备")
    @PreAuthorize("hasAuthority('/group/controllable/delete')")
    @Log(
            module = "CONTROLLABLE",
            type = OperationType.DEL,
            // delete 需要指定 因为参数是 tm id 拿不到泛型 自动匹配不到 logDetailService
            logDetailServiceClass = ControllableLogDetailService.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> delete(@PathVariable("id") String id) {
        ControllableEntity controllableEntity = controllableService.getById(id);
        controllableService.removeById(id);
        groupControllableService.remove(
                Wrappers.lambdaQuery(GroupControllableEntity.class)
                        .eq(GroupControllableEntity::getControllableId, id));
        // 更新可控设备策略
        groupService.removeControllableStrategy(id);
        doGoControllerSync();
        return Result.success();
    }

    @PostMapping("/query")
    @ApiOperation("查询可控设备")
    @PreAuthorize("hasAuthority('/group/controllable/query') or hasAuthority('/monitor/steerable')")
    public Result<IPage<ControllableEntity>> query(
            @RequestBody ControllablePageRequest controllablePageRequest) {
        Page<ControllableEntity> page =
                Page.of(
                        controllablePageRequest.getPageNum(),
                        controllablePageRequest.getPageSize());
        JSONObject jsonObject;
        List<String> ids = new ArrayList<>();
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                // 注意 这里是 协调控制气对象  Controller Entity 不是 Controllable Entity..
                // 也就是场站项目 正常只会有一个 协调控制器对象 找到这个控制器对象的 ip端口去查询 可控设备的 状态信息
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/get_online", null, JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/get_online", null, JSONObject.class);
            }
            assert jsonObject != null;
            List<StatusVo> tmpList =
                    jsonObject.getJSONArray("controllable").toJavaList(StatusVo.class);
            if (StringUtil.notEmpty(controllablePageRequest.getStatus())) {
                boolean status = controllablePageRequest.getStatus() == 1;
                ids =
                        tmpList.stream()
                                .filter(e -> e.getOnline() == status)
                                .map(StatusVo::getUuid)
                                .collect(Collectors.toList());
            }
            IPage<ControllableEntity> list =
                    controllableService.page(
                            page,
                            Wrappers.lambdaQuery(ControllableEntity.class)
                                    .eq(ControllableEntity::getProjectId, WebUtils.projectId.get())
                                    .in(!ids.isEmpty(), ControllableEntity::getId, ids)
                                    // 如果有查询状态的筛选, 并且根据 gateway 返回的设备状态进行匹配 没有对应的设备, 就故让查询id = 0
                                    // 表示查询不到
                                    .eq(
                                            controllablePageRequest.getStatus() != null
                                                    && ids.isEmpty(),
                                            ControllableEntity::getId,
                                            "0")
                                    .like(
                                            StringUtils.hasLength(
                                                    controllablePageRequest.getName()),
                                            ControllableEntity::getName,
                                            controllablePageRequest.getName())
                                    .like(
                                            StringUtils.hasLength(
                                                    controllablePageRequest.getVendor()),
                                            ControllableEntity::getVendor,
                                            controllablePageRequest.getVendor())
                                    .eq(
                                            StringUtils.hasLength(
                                                    controllablePageRequest.getType()),
                                            ControllableEntity::getType,
                                            controllablePageRequest.getType())
                                    .orderByAsc(ControllableEntity::getOrderIndex)
                                    .orderByAsc(ControllableEntity::getCreateTime));
            Map<String, Boolean> map =
                    tmpList.stream()
                            .collect(Collectors.toMap(StatusVo::getUuid, StatusVo::getOnline));
            for (ControllableEntity controllableEntity : list.getRecords()) {
                boolean status =
                        map.get(controllableEntity.getId()) != null
                                && map.get(controllableEntity.getId());
                controllableEntity.setStatus(status ? 1 : 0);
            }
            return Result.success(list);
        } catch (Exception e) {
            log.error("get controllable list -------> cannot connect to site control");
            IPage<ControllableEntity> pageList =
                    controllableService.page(
                            page,
                            Wrappers.lambdaQuery(ControllableEntity.class)
                                    .eq(ControllableEntity::getProjectId, WebUtils.projectId.get())
                                    .like(
                                            StringUtils.hasLength(
                                                    controllablePageRequest.getName()),
                                            ControllableEntity::getName,
                                            controllablePageRequest.getName())
                                    .like(
                                            StringUtils.hasLength(
                                                    controllablePageRequest.getVendor()),
                                            ControllableEntity::getVendor,
                                            controllablePageRequest.getVendor())
                                    .eq(
                                            StringUtils.hasLength(
                                                    controllablePageRequest.getType()),
                                            ControllableEntity::getType,
                                            controllablePageRequest.getType())
                                    .orderByAsc(ControllableEntity::getCreateTime));
            // 这里就表示连接不到 site control  , 全部定义为离线 未连接状态
            pageList.getRecords().forEach(re -> re.setStatus(0));
            return Result.success(pageList);
        }
    }

    public void doGoControllerSync() {
        List<ControllableEntity> controllableEntities =
                controllableService.list(
                        Wrappers.lambdaQuery(ControllableEntity.class)
                                .eq(ControllableEntity::getProjectId, WebUtils.projectId.get()));
        List<ControllableGoRequest> list =
                controllableEntities.stream()
                        .map(
                                controllable -> {
                                    ControllableGoRequest controllableGoRequest =
                                            new ControllableGoRequest();
                                    BeanUtils.copyProperties(controllable, controllableGoRequest);
                                    controllableGoRequest.setName(controllable.getName());
                                    controllableGoRequest.setHost(controllable.getIp());
                                    ControllableConfig controllableConfig =
                                            new ControllableConfig();
                                    BeanUtils.copyProperties(controllable, controllableConfig);
                                    controllableGoRequest.setConfig(
                                            JSON.parseObject(
                                                    controllable.getConfig(),
                                                    new TypeReference<>() {}));
                                    if (StrUtil.isNotBlank(controllable.getChannelIds())) {
                                        controllableGoRequest.setChannelIds(
                                                Arrays.stream(
                                                                controllable
                                                                        .getChannelIds()
                                                                        .split(","))
                                                        .map(Integer::parseInt)
                                                        .collect(Collectors.toList()));
                                    }
                                    if (StrUtil.isNotBlank(controllable.getDeviceIds())) {
                                        controllableGoRequest.setDeviceIds(
                                                Arrays.asList(
                                                        controllable.getDeviceIds().split(",")));
                                    }
                                    return controllableGoRequest;
                                })
                        .collect(Collectors.toList());
        Map<String, List<ControllableGoRequest>> paraMap = new HashMap<>(1);
        paraMap.put("controllable", list);
        JSONObject jsonObject;
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/update_controllable",
                                paraMap,
                                JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/update_controllable",
                                paraMap,
                                JSONObject.class);
            }
        } catch (Exception e) {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() != 1) {
                log.error("update controllable gatewayUrl-->" + gatewayUrl);
            }
            throw new ServiceException(ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
        }
        assert jsonObject != null;
        String result = "success";
        Optional.of(jsonObject)
                .ifPresent(
                        json -> {
                            if (!(boolean) jsonObject.get(result)) {
                                throw new ServiceException(
                                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                            }
                        });
    }

    @PostMapping("/inner_query")
    @ApiOperation("内部查询可控设备")
    public Result<IPage<ControllableEntity>> innerQuery(
            @RequestBody ControllablePageRequest controllablePageRequest) {
        return query(controllablePageRequest);
    }
}
