package com.wifochina.modules.group.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 协调控制器
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Getter
@Setter
public class ControllableRequest {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型：Load、IO、Power")
    private String type;

    @ApiModelProperty("连接方式(TCPCAN/SocketCAN/ModbusTCP")
    private String connectionType;

    @ApiModelProperty("型号")
    private String vendor;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

    @ApiModelProperty("ip")
    private String ip;

    @ApiModelProperty("端口")
    private Integer port;

    @ApiModelProperty("端口")
    private String config;

    @ApiModelProperty("设计功率")
    private Double designPower;

    @ApiModelProperty("监听端口")
    private Integer listenPort;

    @ApiModelProperty("功率斜率")
    private Double powerStepPercentage;

    @ApiModelProperty(value = "从机号 默认 1")
    private Integer slaveId;

    @ApiModelProperty(value = "排序")
    private Integer orderIndex;

    @ApiModelProperty(value = "串口名称")
    // 串口名称
    private String interfaceName;

    @ApiModelProperty(value = "波特率")
    // 波特率
    private Integer bitrate;

    @ApiModelProperty(value = "设备名称")
    private String dev;

    @ApiModelProperty(value = "波特率")
    private Integer baudRate;

    @ApiModelProperty(value = "数据位")
    private Integer byteSize;

    @ApiModelProperty(value = "停止位")
    private Integer stopBits;

    @ApiModelProperty(value = "奇偶校验")
    private String parity;

    @ApiModelProperty(value = "最高温度")
    private Float maxTemp;

    @ApiModelProperty(value = "最低温度")
    private Float minTemp;

    @ApiModelProperty(value = "控制模式")
    private String controlMode;

    @ApiModelProperty(value = "通道Id")
    private String channelIds;

    @ApiModelProperty(value = "设备ID")
    private String deviceIds;
}
