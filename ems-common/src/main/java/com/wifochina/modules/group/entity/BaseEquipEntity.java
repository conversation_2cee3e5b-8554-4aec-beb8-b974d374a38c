package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * @since 2024-03-28 3:50 PM
 * <AUTHOR>
 */
@Data
public class BaseEquipEntity extends BaseEntity {

    @ApiModelProperty(value = "标识符id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "ip")
    private String ip;

    @ApiModelProperty(value = "端口")
    private Integer port;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    // 看前端接口好像 0 表示未连接 1表示 已连接, 暂不清楚 ApiModelProperty里面的描述是否正确
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "排序")
    private Integer orderIndex;
}
