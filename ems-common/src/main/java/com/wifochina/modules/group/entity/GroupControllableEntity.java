package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 分组与电表关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Getter
@Setter
@TableName("t_group_controllable")
@ApiModel(value = "GroupControllableEntity对象", description = "分组与电表关联表")
public class GroupControllableEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("关系id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("分组id")
    private String groupId;

    @ApiModelProperty("可控设备id")
    private String controllableId;
}
