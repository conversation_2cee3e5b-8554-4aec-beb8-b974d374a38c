package com.wifochina.modules.group.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.wifochina.common.util.DemandControlEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.mapper.GroupMapper;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.group.vo.GroupGoVo;
import com.wifochina.modules.group.vo.GroupVO;
import com.wifochina.modules.oauth.util.WebUtils;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分组 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Slf4j
@Service
@AllArgsConstructor
public class GroupServiceImpl extends ServiceImpl<GroupMapper, GroupEntity>
        implements GroupService {

    private final GroupDeviceService groupDeviceService;
    private final GroupAmmeterService groupAmmeterService;
    private final GroupControllableService groupControllableService;
    private final DeviceService deviceService;
    private final AmmeterService ammeterService;

    @Override
    public IPage<GroupVO> queryGroupVO(IPage<GroupVO> groupPage, String name) {
        return this.baseMapper.queryGroupVO(groupPage, name, WebUtils.projectId.get());
    }

    @Override
    public List<GroupGoVo> queryGroup(String projectId) {
        return this.baseMapper.queryGroup(projectId);
    }

    /**
     * 查询系统分组 根据projectId
     *
     * @param projectId : 项目id
     * @return : GroupEntity
     */
    @Override
    public GroupEntity systemGroupEntity(String projectId) {
        return this.getOne(
                Wrappers.lambdaQuery(GroupEntity.class)
                        .eq(GroupEntity::getProjectId, projectId)
                        .eq(GroupEntity::getWhetherSystem, true));
    }

    @Override
    public List<GroupEntity> systemGroupEntitys(List<String> projectIds) {
        if (CollectionUtil.isEmpty(projectIds)) {
            return List.of();
        }
        return this.list(
                new LambdaQueryWrapper<GroupEntity>()
                        .in(GroupEntity::getProjectId, projectIds)
                        .eq(GroupEntity::getWhetherSystem, true));
    }

    @Override
    public List<GroupEntity> queryGroupsNotSystem() {
        return this.list(
                Wrappers.lambdaQuery(GroupEntity.class)
                        .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                        .eq(GroupEntity::getWhetherSystem, false));
    }

    /**
     * 查询非系统分组并且是绑定了收入设备或者表计的分组
     *
     * @param projectId : 项目id
     * @return : List<GroupEntity>
     */
    @Override
    public List<GroupEntity> queryGroupsNotSystemAndBindIncomeDeviceOrMeter(String projectId) {
        return this.baseMapper.queryGroupsNotSystemAndBindIncomeDeviceOrMeter(projectId);
    }

    @Override
    public boolean groupEarningCondition(String projectId, GroupEntity systemGroupEntity) {
        if (systemGroupEntity == null) {
            systemGroupEntity =
                    baseMapper.selectOne(
                            Wrappers.lambdaQuery(GroupEntity.class)
                                    .eq(GroupEntity::getProjectId, projectId)
                                    .eq(GroupEntity::getWhetherSystem, true));
        }
        List<GroupEntity> groupEntities =
                this.queryGroupsNotSystemAndBindIncomeDeviceOrMeter(projectId);
        return Boolean.TRUE.equals(systemGroupEntity.getGroupEarningsController())
                && CollectionUtil.isNotEmpty(groupEntities);
    }

    @Override
    public List<String> getIncomeDeviceAmmeterIdsForGroup(String projectId, String groupId) {
        List<String> allIds = new ArrayList<>();
        // 这里代表 查询 全部的分组, (查询非系统分组并且是绑定了收入设备或者表计的分组)
        if (groupId.equals(EmsConstants.ALL)) {
            allIds.addAll(
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getIncome, true)
                            .eq(DeviceEntity::getProjectId, projectId)
                            .eq(DeviceEntity::getUnreal, false)
                            .list()
                            .stream()
                            .map(DeviceEntity::getId)
                            .collect(Collectors.toList()));
            allIds.addAll(
                    ammeterService
                            .lambdaQuery()
                            .eq(AmmeterEntity::getIncome, true)
                            .eq(AmmeterEntity::getProjectId, projectId)
                            .list()
                            .stream()
                            .map(AmmeterEntity::getId)
                            .collect(Collectors.toList()));
        } else {
            allIds.addAll(getIncomeDeviceIdsForGroup(projectId, groupId));
            allIds.addAll(getIncomeAmmeterIdsForGroup(projectId, groupId));
        }
        return allIds;
    }

    @Override
    public Map<String, GroupEntity> querySystemGroupPcsCodes(List<String> projectIds) {
        // 查询 项目ids 的 每个项目的系统分组的 pcsCode
        Map<String, GroupEntity> result = new HashMap<>();
        if (!projectIds.isEmpty()) {
            List<GroupEntity> groupEntities =
                    this.baseMapper.selectList(
                            new LambdaQueryWrapper<GroupEntity>()
                                    .in(GroupEntity::getProjectId, projectIds)
                                    .eq(GroupEntity::getWhetherSystem, true));

            return groupEntities.stream()
                    .collect(Collectors.toMap(GroupEntity::getProjectId, v -> v));
        }
        return result;
    }

    @Override
    public List<String> getIncomeDeviceIdsForGroup(String projectId, String groupId) {
        List<String> allDeviceIds = new ArrayList<>();
        List<GroupDeviceEntity> groupDeviceEntitys =
                groupDeviceService.lambdaQuery().eq(GroupDeviceEntity::getGroupId, groupId).list();
        List<String> deviceIds =
                groupDeviceEntitys.stream()
                        .map(GroupDeviceEntity::getDeviceId)
                        .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            List<DeviceEntity> deviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getIncome, true)
                            .eq(DeviceEntity::getProjectId, projectId)
                            .in(DeviceEntity::getId, deviceIds)
                            .list();
            deviceEntities.forEach(e -> allDeviceIds.add(e.getId()));
        }
        return allDeviceIds;
    }

    @Override
    public List<String> getIncomeAmmeterIdsForGroup(String projectId, String groupId) {
        List<String> allAmmeterIds = new ArrayList<>();
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService
                        .lambdaQuery()
                        .eq(GroupAmmeterEntity::getGroupId, groupId)
                        .list();
        List<String> ammeterIds =
                groupAmmeterEntities.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(ammeterIds)) {
            List<AmmeterEntity> ammeterEntities =
                    ammeterService
                            .lambdaQuery()
                            .eq(AmmeterEntity::getIncome, true)
                            .eq(AmmeterEntity::getProjectId, projectId)
                            .in(AmmeterEntity::getId, ammeterIds)
                            .list();
            ammeterEntities.forEach(e -> allAmmeterIds.add(e.getId()));
        }
        return allAmmeterIds;
    }

    @Override
    public List<GroupEntity> queryCapacityGroup(String projectId) {
        return this.baseMapper.selectList(
                Wrappers.lambdaQuery(GroupEntity.class)
                        .eq(GroupEntity::getCapacityController, true)
                        .eq(!StringUtil.isEmpty(projectId), GroupEntity::getProjectId, projectId));
    }

    @Override
    public List<GroupEntity> queryEarningGroup(String projectId) {
        return this.baseMapper.selectList(
                Wrappers.lambdaQuery(GroupEntity.class)
                        .eq(GroupEntity::getCalcEarningsController, true)
                        .eq(GroupEntity::getProjectId, projectId));
    }

    @Override
    public void removeControllableStrategy(String controllableId) {
        List<String> groupIds =
                groupControllableService
                        .lambdaQuery()
                        .eq(GroupControllableEntity::getControllableId, controllableId)
                        .list()
                        .stream()
                        .map(GroupControllableEntity::getGroupId)
                        .collect(Collectors.toList());
        if (groupIds.isEmpty()) {
            return;
        }
        List<GroupEntity> groupEntities =
                this.lambdaQuery().in(GroupEntity::getId, groupIds).list();
        ObjectMapper mapper = new ObjectMapper();
        groupEntities.forEach(
                g -> {
                    String strategy = g.getControllableStrategies();
                    if (!StringUtils.hasLength(strategy)) {
                        return;
                    }
                    try {
                        ObjectNode rootNode = (ObjectNode) mapper.readTree(strategy);
                        rootNode.remove(controllableId);
                        String updatedJsonStr = mapper.writeValueAsString(rootNode);
                        g.setControllableStrategies(updatedJsonStr);
                        this.baseMapper.updateById(g);
                    } catch (JsonProcessingException e) {
                        log.error("删除可控设备时策略转换失败", e);
                        throw new RuntimeException(e);
                    }
                });
    }

    @Override
    public void fillGroupName(String projectId, List<ElectricEntity> records) {
        List<GroupEntity> groupEntities =
                this.lambdaQuery().eq(GroupEntity::getProjectId, projectId).list();
        Map<String, String> groupNameMap =
                groupEntities.stream()
                        .collect(Collectors.toMap(GroupEntity::getId, GroupEntity::getName));
        records.forEach(e -> e.setGroupName(groupNameMap.get(e.getDeviceId())));
    }

    @Override
    public List<GroupEntity> queryGroupEntitiesByProjectId(String projectId) {
        if (StringUtils.hasLength(projectId)) {
            return this.lambdaQuery().eq(GroupEntity::getProjectId, projectId).list();
        }
        return List.of();
    }

    @Override
    public List<String> queryGroupIdsByProjectId(String projectId) {
        if (StringUtils.hasLength(projectId)) {
            return this.lambdaQuery().eq(GroupEntity::getProjectId, projectId).list().stream()
                    .map(GroupEntity::getId)
                    .collect(Collectors.toList());
        }
        return List.of();
    }

    /**
     * 1.4.0 开启了需量控制和需量收益的
     *
     * @param projectId : projectId
     * @return : List
     */
    @Override
    public List<GroupEntity> queryEnableDemandIncome(String projectId) {
        return this.getBaseMapper()
                .selectList(
                        new LambdaQueryWrapper<GroupEntity>()
                                .eq(GroupEntity::getProjectId, projectId)
                                .eq(
                                        GroupEntity::getDemandControl,
                                        DemandControlEnum.enable_show_demand_income.name()));
    }

    /**
     * 1.4.0 开启了需量控制
     *
     * @param projectId : projectId
     * @return : List
     */
    @Override
    public List<GroupEntity> queryEnableDemandControl(String projectId) {
        return this.getBaseMapper()
                .selectList(
                        new LambdaQueryWrapper<GroupEntity>()
                                .eq(
                                        // 这样可以如果是传null 进来 就是查询所有的项目的 开启需量控制的 分组列表
                                        !StringUtil.isEmpty(projectId),
                                        GroupEntity::getProjectId,
                                        projectId)
                                .isNotNull(GroupEntity::getDemandPeriod)
                                .in(
                                        GroupEntity::getDemandControl,
                                        DemandControlEnum.enable_show_demand_income.name(),
                                        DemandControlEnum.enable_hide_demand_income.name()));
    }

    @Override
    public Set<String> queryEnableDemandProjectIds(Collection<String> projectIds) {
        return this.baseMapper.queryEnableDemandProjectIds(projectIds);
    }

    @Override
    public Set<String> queryEnableCapacityProjectIds(Collection<String> projectIds) {
        return this.baseMapper.queryEnableCapacityProjectIds(projectIds);
    }
}
