package com.wifochina.modules.group.vo;

import com.wifochina.modules.group.entity.GroupEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 分组
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GroupVO extends GroupEntity {

    @ApiModelProperty(value = "关联的电表id")
    private List<AmmeterVo> ammeters;

    @ApiModelProperty(value = "关联的设备id")
    private List<Map<String, Object>> devices;

    @ApiModelProperty(value = "关联的可控设备id")
    private List<ControllableVo> controllables;

    @ApiModelProperty(value = "功率因数控制模式开关")
    private Boolean powerFactorControl;
}
