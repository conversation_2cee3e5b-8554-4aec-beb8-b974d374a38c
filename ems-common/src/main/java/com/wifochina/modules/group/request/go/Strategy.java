package com.wifochina.modules.group.request.go;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
public class Strategy implements Cloneable {

    @ApiModelProperty(value = "策略类型  0 充电 1放电 2自发自用")
    private Integer function;

    @ApiModelProperty(value = "星期几 1代表星期一 2代表星期二 ... 7代表星期日")
    private Integer week;

    @ApiModelProperty(value = "功率")
    private Integer power;

    @ApiModelProperty(value = "开始分钟")
    private Integer start_minute;

    @ApiModelProperty(value = "结束分钟")
    private Integer end_minute;

    @ApiModelProperty(value = "soc")
    private Integer soc;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
