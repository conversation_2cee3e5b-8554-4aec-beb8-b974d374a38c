package com.wifochina.modules.group.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 分组
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GroupPageRequest extends PageBean {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组名称")
    private String name;

}
