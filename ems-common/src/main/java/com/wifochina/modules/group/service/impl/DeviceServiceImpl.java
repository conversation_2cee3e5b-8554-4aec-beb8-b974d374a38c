package com.wifochina.modules.group.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupDeviceEntity;
import com.wifochina.modules.group.mapper.DeviceMapper;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupDeviceService;
import com.wifochina.modules.oauth.util.WebUtils;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, DeviceEntity>
        implements DeviceService {
    @Resource private GroupDeviceService groupDeviceService;

    @Override
    public List<DeviceEntity> getDevicesByPid(String projectId) {
        return this.lambdaQuery()
                .eq(DeviceEntity::getUnreal, false)
                .eq(DeviceEntity::getProjectId, projectId)
                .list();
    }

    @Override
    public List<DeviceEntity> getDeviceByGid(String projectId, String groupId) {
        List<GroupDeviceEntity> groupDeviceEntities =
                groupDeviceService.lambdaQuery().eq(GroupDeviceEntity::getGroupId, groupId).list();
        List<String> deviceIds =
                groupDeviceEntities.stream()
                        .map(GroupDeviceEntity::getDeviceId)
                        .collect(Collectors.toList());
        List<DeviceEntity> deviceEntities = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(deviceIds)) {
            deviceEntities =
                    getBaseMapper()
                            .selectList(
                                    new LambdaQueryWrapper<DeviceEntity>()
                                            .eq(DeviceEntity::getIncome, true)
                                            .eq(DeviceEntity::getProjectId, projectId)
                                            .in(DeviceEntity::getId, deviceIds));
        }

        return deviceEntities;
    }

    @Override
    public List<DeviceEntity> findIncomeDevices(String projectId) {
        return this.lambdaQuery()
                .eq(DeviceEntity::getIncome, true)
                .eq(DeviceEntity::getProjectId, projectId)
                .list();
    }

    @Override
    public List<String> deviceIdsPrepare(String projectId, String deviceIdParams) {
        List<String> deviceIds = new ArrayList<>();
        if (EmsConstants.ALL.equals(deviceIdParams)) {
            deviceIds =
                    this.lambdaQuery()
                            .eq(DeviceEntity::getProjectId, projectId)
                            .eq(DeviceEntity::getUnreal, false)
                            .list()
                            .stream()
                            .map(DeviceEntity::getId)
                            .collect(Collectors.toList());
        } else if (deviceIdParams.contains(EmsConstants.SPILT_COMMA)) {
            String[] ids = deviceIdParams.split(EmsConstants.SPILT_COMMA);
            deviceIds = Arrays.asList(ids);
        } else {
            deviceIds.add(deviceIdParams);
        }
        return deviceIds;
    }

    @Override
    public List<String> getGroupDeviceList(RequestWithGroupId requestWithGroupId) {
        return getGroupDeviceIdList(
                requestWithGroupId.getGroupId(), requestWithGroupId.getItemId(), false);
    }

    @Override
    public List<String> getGroupDcdcDeviceList(RequestWithGroupId requestWithGroupId) {
        return getGroupDeviceIdList(
                requestWithGroupId.getGroupId(), requestWithGroupId.getItemId(), true);
    }

    @Override
    public List<String> getGroupDeviceIdList(String groupId, String itemId, Boolean queryDcdc) {
        List<String> deviceIds;
        if (EmsConstants.ALL.equals(itemId)) {
            if (StringUtil.isEmpty(groupId)) {
                deviceIds = fetchAllDevices(queryDcdc);
            } else {
                if (EmsConstants.ALL.equals(groupId)) {
                    deviceIds = fetchAllDevices(queryDcdc);
                } else {
                    deviceIds = fetchDeviceIdsByGroupId(groupId, queryDcdc);
                }
            }
        } else {
            deviceIds = List.of(itemId);
        }
        return deviceIds;
    }

    /**
     * 查询 分组下面的 非虚拟设备的 设备ids 列表
     *
     * @param groupId
     * @return
     */
    @Override
    public List<String> getGroupDeviceNotUnrealIds(String groupId) {
        List<GroupDeviceEntity> groupDeviceEntities =
                groupDeviceService.list(
                        Wrappers.lambdaQuery(GroupDeviceEntity.class)
                                .eq(GroupDeviceEntity::getGroupId, groupId));
        List<DeviceEntity> unRealDeviceEntities =
                this.getBaseMapper()
                        .selectList(
                                new LambdaQueryWrapper<DeviceEntity>()
                                        .eq(DeviceEntity::getUnreal, true)
                                        .eq(DeviceEntity::getProjectId, WebUtils.projectId.get()));
        List<String> unRealDeviceIds =
                unRealDeviceEntities.stream().map(DeviceEntity::getId).collect(Collectors.toList());
        List<String> systemDeviceIds =
                groupDeviceEntities.stream()
                        .map(GroupDeviceEntity::getDeviceId)
                        .collect(Collectors.toList());
        return systemDeviceIds.stream()
                .filter(item -> !unRealDeviceIds.contains(item))
                .collect(Collectors.toList());
    }

    @Transactional
    public void newIsHost(DeviceEntity deviceEntity) {
        // 如果添加的设备是主机
        if (Boolean.TRUE.equals(deviceEntity.getIsHost())) {
            String projectId = WebUtils.projectId.get();
            // 更新同一项目下其他设备的isHost状态为false
            this.lambdaUpdate()
                    .eq(DeviceEntity::getProjectId, projectId)
                    // 排除刚刚插入的设备
                    .ne(DeviceEntity::getId, deviceEntity.getId())
                    .set(DeviceEntity::getIsHost, 0)
                    .update();
        }
    }

    @Override
    @Transactional
    public void add(DeviceEntity deviceEntity) {
        baseMapper.insert(deviceEntity);
        // 如果添加的设备是主机
        newIsHost(deviceEntity);
    }

    @Override
    @Transactional
    public void edit(DeviceEntity deviceEntity) {
        baseMapper.updateById(deviceEntity);
        // 如果修改成主机
        newIsHost(deviceEntity);
    }

    @Override
    public boolean isIpPortConflict(String ip, Integer port) {
        String projectId = WebUtils.projectId.get();
        if (StringUtil.isEmpty(projectId)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        return this.lambdaQuery()
                .eq(DeviceEntity::getIp, ip)
                .eq(DeviceEntity::getPort, port)
                .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                .exists(); // 返回 true 表示存在重复
    }

    private List<String> fetchAllDevices(Boolean queryDcdc) {
        // 确保只查询必要的字段，提高查询效率
        return this.list(
                        Wrappers.lambdaQuery(DeviceEntity.class)
                                .eq(queryDcdc, DeviceEntity::getHasDcdc, true)
                                .eq(DeviceEntity::getUnreal, false)
                                .eq(DeviceEntity::getProjectId, WebUtils.projectId.get()))
                .stream()
                .map(DeviceEntity::getId)
                .collect(Collectors.toList());
    }

    private List<String> fetchDeviceIdsByGroupId(String groupId, Boolean queryDcdc) {
        List<GroupDeviceEntity> deviceList =
                groupDeviceService.list(
                        Wrappers.lambdaQuery(GroupDeviceEntity.class)
                                .eq(GroupDeviceEntity::getGroupId, groupId));
        List<String> deviceIds =
                deviceList.stream()
                        .map(GroupDeviceEntity::getDeviceId)
                        .map(String::valueOf) // 确保转换的必要性
                        .collect(Collectors.toList());
        if (deviceIds.isEmpty()) {
            return List.of();
        } else {
            // 如果已经存在设备ID，查询对应的设备信息
            return this.list(
                            Wrappers.lambdaQuery(DeviceEntity.class)
                                    .in(DeviceEntity::getId, deviceIds)
                                    .eq(queryDcdc, DeviceEntity::getHasDcdc, true)
                                    .eq(DeviceEntity::getUnreal, false))
                    .stream()
                    .map(DeviceEntity::getId)
                    .collect(Collectors.toList());
        }
    }
}
