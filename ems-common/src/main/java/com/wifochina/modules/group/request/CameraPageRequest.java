package com.wifochina.modules.group.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CameraPageRequest extends PageBean {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(value = "摄像头名称")
    private String name;

    @ApiModelProperty(value = "摄像头型号")
    private String type;

    @ApiModelProperty(value = "摄像头开发商")
    private String vendor;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

}
