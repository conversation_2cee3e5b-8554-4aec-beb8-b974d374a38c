package com.wifochina.modules.group.request.go;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-15 11:14 AM
 */
@Data
public class CameraGoRequest {
    @JsonProperty("uuid")
    private String id;

    @ApiModelProperty(value = "摄像头名称")
    private String name;

    @ApiModelProperty(value = "摄像头ip")
    private String ip;

    @ApiModelProperty(value = "摄像头端口")
    private Integer port;

    @JsonProperty("model")
    @ApiModelProperty(value = "摄像头类型")
    private String type;

    @ApiModelProperty(value = "摄像头型号，厂商名称")
    private String vendor;

    @ApiModelProperty("摄像头用户名")
    private String username;

    @ApiModelProperty("摄像头密码")
    private String password;

    @ApiModelProperty("摄像头认证类型(basic、digest)")
    @JsonProperty("auth_type")
    private String authType;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;
}
