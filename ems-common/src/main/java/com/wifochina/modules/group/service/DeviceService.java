package com.wifochina.modules.group.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.group.entity.DeviceEntity;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface DeviceService extends IService<DeviceEntity> {
    List<String> getGroupDeviceList(RequestWithGroupId requestWithGroupId);

    List<String> getGroupDcdcDeviceList(RequestWithGroupId requestWithGroupId);

    List<DeviceEntity> getDevicesByPid(String projectId);

    List<DeviceEntity> getDeviceByGid(String projectId, String groupId);

    List<DeviceEntity> findIncomeDevices(String projectId);

    List<String> deviceIdsPrepare(String projectId, String deviceIdParams);

    List<String> getGroupDeviceIdList(String groupId, String itemId, Boolean queryDcdc);

    // 查询系统分组下 非虚拟设备的列表
    List<String> getGroupDeviceNotUnrealIds(String groupId);

    // 新增设备
    void add(DeviceEntity deviceEntity);

    // 编辑设备
    void edit(DeviceEntity deviceEntity);

    boolean isIpPortConflict(String ip, Integer port);
}
