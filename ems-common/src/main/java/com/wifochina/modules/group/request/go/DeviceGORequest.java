package com.wifochina.modules.group.request.go;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AmmeterGORequest
 *
 * @since 4/20/2022 11:38 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class DeviceGORequest {

    @ApiModelProperty(value = "设备ip")
    private String host;

    @ApiModelProperty(value = "设备端口")
    private Integer port;

    @ApiModelProperty(value = "type must be EMS100")
    private String type = "EMS100";

    @ApiModelProperty(value = "vendor must be Weiheng")
    private String vendor = "Weiheng";

    @ApiModelProperty(value = "设备标识符uuid")
    private String uuid;

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "电量输出初始值")
    @JsonProperty("ac_history_positive_initial_power_in_kwh")
    private Double outputHistoryInit;

    @ApiModelProperty(value = "电量输入初始值")
    @JsonProperty("ac_history_negative_initial_power_in_kwh")
    private Double inputHistoryInit;

    @ApiModelProperty(value = "虚拟设备的pcs序号,0,1,2,3,4")
    @JsonProperty("pcs_index")
    private Integer pcsIndex;

    @ApiModelProperty(value = "功率分配百分比")
    @JsonProperty("power_capacity_percent")
    private Float powerCapacityPercent;

    @ApiModelProperty(value = "pv表初始放电量初始值")
    @JsonProperty("dcdc_history_positive_initial_power_in_kwh")
    private Double pvOutputHistoryInit;

    @ApiModelProperty(value = "pv表初始充电量初始值")
    @JsonProperty("dcdc_history_negative_initial_power_in_kwh")
    private Double pvInputHistoryInit;

    /** 1.4.2 added 需要下发name */
    @ApiModelProperty(value = "设备name")
    private String name;

    @ApiModelProperty(value = "水冷预开启时间")
    @JsonProperty("cooler_pre_open_time")
    private Integer coolerPreOpenTime;

    @ApiModelProperty(value = "控制模式")
    @JsonProperty("direct_pcs_power_control")
    private Boolean directPcsPowerControl = false;
}
