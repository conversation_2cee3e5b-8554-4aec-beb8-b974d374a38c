package com.wifochina.modules.group.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.request.ControllerRequest;
import com.wifochina.modules.group.request.InternetIpRequest;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 协调控制器 前端控制器
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@RestController
@Api(tags = "08-控制器管理")
@RequestMapping("/controller")
@RequiredArgsConstructor
public class ControllerController {

    private final ControllerService controllerService;

    private final RestTemplate restTemplate;

    private final ProjectService projectService;

    private final LogService logService;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    /** 测试控制器连通性 */
    @PostMapping("/connect")
    @ApiOperation("测试控制器通性")
    @PreAuthorize("hasAuthority('/group/controller')")
    public String isConnect(@RequestBody InternetIpRequest internetIpRequest) {
        // if (RegexUtil.isIp(internetIpRequest.getIp())) {
        // throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
        // }
        Map<String, String> resultMap;
        try {
            resultMap = testConnect(internetIpRequest);
            return JSON.toJSONString(Result.success(resultMap));
        } catch (Exception e) {
            return JSON.toJSONString(Result.success(ErrorResultCode.FAIL_CONNECT_GOCONTROL));
        }
    }

    /** 增加控制器 */
    @PostMapping("/add")
    @ApiOperation("增加控制器")
    @Log(module = "CONTROL", type = OperationType.ADD)
    @PreAuthorize("hasAuthority('/group/controller')")
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> add(@RequestBody ControllerRequest controllerRequest) {
        ControllerEntity controllerEntity = new ControllerEntity();
        BeanUtils.copyProperties(controllerRequest, controllerEntity);
        controllerEntity.setId(StringUtil.uuid());
        controllerService.save(controllerEntity);
        return Result.success();
    }

    /** 修改控制器 */
    @PostMapping("/update")
    @ApiOperation("修改控制器")
    @PreAuthorize("hasAuthority('/group/controller/edit')")
    @Log(module = "CONTROL", type = OperationType.UPDATE)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody ControllerRequest controllerRequest) {
        ControllerEntity controllerEntity = new ControllerEntity();
        BeanUtils.copyProperties(controllerRequest, controllerEntity);
        controllerService.updateById(controllerEntity);
        //        logService.logUpdateDetail(
        //                LogInfo.builder()
        //                        .module("CONTROL")
        //                        .method("CONTROL_UPDATE")
        //                        .object(controllerRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.CONTROLLER_INFO_TRACE_FORMAT,
        //                                        controllerRequest.getId()))
        //                        .build());
        return Result.success();
    }

    /** 查询控制器 */
    @PostMapping("/query")
    @ApiOperation("查询控制器")
    @PreAuthorize("hasAuthority('/group/controller/query')")
    public Result<List<ControllerEntity>> query() {
        List<ControllerEntity> list =
                controllerService.list(
                        Wrappers.lambdaQuery(ControllerEntity.class)
                                .eq(ControllerEntity::getProjectId, WebUtils.projectId.get()));
        return Result.success(list);
    }

    /** 测试调控制器连通性 */
    public Map<String, String> testConnect(InternetIpRequest internetIpRequest) {

        Map<String, String> resultMap = new HashMap<>(2);
        JSONObject jsonObject;
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        if (projectEntity.getProjectModel() == 1) {
            jsonObject =
                    restTemplate.postForObject(
                            "http://"
                                    + internetIpRequest.getIp()
                                    + ":"
                                    + internetIpRequest.getPort()
                                    + "/api/v1/ping",
                            null,
                            JSONObject.class);
        } else {
            jsonObject =
                    restTemplate.postForObject(gatewayUrl + "/api/v1/ping", null, JSONObject.class);
        }

        assert jsonObject != null;
        String sn = "SN";
        Optional.of(jsonObject)
                .ifPresent(
                        json -> {
                            if (Objects.isNull(jsonObject.get(sn))) {
                                throw new ServiceException(
                                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                            } else {
                                resultMap.put("SN", (String) jsonObject.get("SN"));
                                resultMap.put("version", (String) jsonObject.get("version"));
                            }
                        });
        return resultMap;
    }
}
