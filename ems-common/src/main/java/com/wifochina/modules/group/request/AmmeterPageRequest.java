package com.wifochina.modules.group.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AmmeterPageRequest extends PageBean {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty(value = "电表名称")
    private String name;

    @ApiModelProperty(value = "0电网电表、1 PV电表、 2并网点电表、3负载电表")
    private Integer type;

    @ApiModelProperty(value = "电表型号，厂商名称 只支持 EMSMeter 和 DTSD3125")
    private String vendor;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

}
