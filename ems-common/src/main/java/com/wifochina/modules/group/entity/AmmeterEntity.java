package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.common.util.EmsConstants;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * t_ammeter
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_ammeter")
@ApiModel(value = "AmmeterEntity对象", description = "AmmeterEntity对象")
public class AmmeterEntity extends BaseEquipEntity implements IdSearchSupport {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "1 PV电表、 2并网点电表、3负载电表")
    private Integer type;

    @ApiModelProperty(value = "电表型号，只支持 EMSMeter,DTSD3125,CEM9000")
    private String vendor;

    @ApiModelProperty(value = "CEM9000的测控号，1或者2")
    private Integer meterNum;

    @ApiModelProperty(value = "是否反击，默认false")
    private Boolean reverse;

    @ApiModelProperty(value = "电量输出初始值")
    private Double outputHistoryInit;

    @ApiModelProperty(value = "电量输入初始值")
    private Double inputHistoryInit;

    @ApiModelProperty(value = "是否测控电表，默认为false 不是测控电表")
    private Boolean controllable;

    @ApiModelProperty(value = "是否维护,默认不维护false")
    private Boolean maintain;

    @ApiModelProperty(value = "是否计算收益,默认不维护false")
    private Boolean income;

    @ApiModelProperty(value = "CEM9000是否使用低压")
    private Boolean useLowSide;

    @ApiModelProperty(value = "高压侧 CT比")
    private Double highCtRatio;

    @ApiModelProperty(value = "高压侧 PT比")
    private Double highPtRatio;

    @ApiModelProperty(value = "高压侧保护CT比")
    private Double highProtectCtRatio;

    @ApiModelProperty(value = "低压侧 CT比")
    private Double lowCtRatio;

    @ApiModelProperty(value = "低压侧 PT比")
    private Double lowPtRatio;

    @ApiModelProperty(value = "是否抄表,默认不维护false")
    private Boolean meterReading;

    @ApiModelProperty(value = "序号")
    @TableField(value = "`index`")
    private Integer index;

    @ApiModelProperty(value = "是否是直流电表")
    private Boolean dcMeter;

    @ApiModelProperty(value = "dlt645设备地址")
    private String dlt645DeviceAddress;

    @ApiModelProperty(value = "dlt645前置cmd")
    private String dlt645PrefixCmd;

    @ApiModelProperty(value = "分时计量")
    private Boolean timeSharingMeasurement;

    @ApiModelProperty(value = "最大需量计量")
    private Boolean maxDemandMeasurement;

    @ApiModelProperty(value = "从机号 默认 1")
    private Integer slaveId;

    @ApiModelProperty(value = "调频，如果不设置,值为nil")
    private String frequencyRegulation;

    @ApiModelProperty(value = "协议类型")
    private String protocol;

    @ApiModelProperty(value = "设备名称")
    private String device;

    @ApiModelProperty(value = "波特率")
    private Integer baudRate;

    @ApiModelProperty(value = "数据位")
    private Integer dataBits;

    @ApiModelProperty(value = "停止位")
    private Integer stopBits;

    @ApiModelProperty(value = "奇偶校验")
    private String parity;

    // 2025-04-22 09:46:29 add isBackup
    @ApiModelProperty(value = "是否backup")
    private Boolean isBackup;

    @ApiModelProperty(value = "投运初始时间InData")
    private Double projectRunInitInData;

    @ApiModelProperty(value = "投运初始时间OutData")
    private Double projectRunInitOutData;

    @ApiModelProperty(value = "连接类型(串口/websocket)")
    private String connectType;

    @ApiModelProperty(value = "设备类型(WS-TCP/IP或者WS-RS485的时候需要)")
    private String wsDeviceType;

    @ApiModelProperty(value = "设备名称(WS-TCP/IP或者WS-RS485的时候需要)")
    private String wsDeviceName;

    @Override
    public String type() {
        return EmsConstants.AMMETER;
    }

    @Override
    public String id() {
        return this.getId();
    }
}
