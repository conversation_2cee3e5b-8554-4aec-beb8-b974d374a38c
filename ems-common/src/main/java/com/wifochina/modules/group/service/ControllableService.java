package com.wifochina.modules.group.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.group.entity.ControllableEntity;

import java.util.List;

/**
 * 协调控制器 服务类
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
public interface ControllableService extends IService<ControllableEntity> {

    /**
     * 更新调整 order index
     *
     * @param ids : 更新的ids ,按照 这个 ids 的 index 索引顺序设置
     */
    void updateOrderIndex(List<String> ids);
}
