package com.wifochina.modules.group.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.RegexUtil;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.group.enums.DeviceControlModelEnums;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.request.*;
import com.wifochina.modules.group.request.go.DeviceGORequest;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupDeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.group.vo.StatusVo;
import com.wifochina.modules.initelectric.InitElectricService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.DeviceLogDetailService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Slf4j
@RestController
@RequestMapping("/device")
@Api(tags = "05-设备管理")
@RequiredArgsConstructor
public class DeviceController {

    @Value("${ems.gateway}")
    private String gatewayUrl;

    @Value("${ems.collect.data.url}")
    private String url;

    private final RestTemplate restTemplate;

    private final ProjectService projectService;

    private final DataService dataService;

    private final DeviceService deviceService;

    private final GroupService groupService;

    private final GroupDeviceService groupDeviceService;

    private final ControllerService controllerService;
    private final InitElectricService initElectricService;

    /** 测试设备连通性 */
    @PostMapping("/connect")
    @ApiOperation("测试设备连通性")
    @PreAuthorize("hasAuthority('/group/device')")
    @SuppressWarnings("unchecked")
    public Result<String> isConnect(@RequestBody InternetIpRequest internetIpRequest) {
        ServiceAssert.isEmpty(internetIpRequest.getIp());
        if (RegexUtil.isNotIp(internetIpRequest.getIp())) {
            throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
        }
        ServiceAssert.isNull(internetIpRequest.getPort());
        try {
            String tempUrl = url;
            tempUrl =
                    tempUrl.replace("{ip}", internetIpRequest.getIp())
                            .replace("{port}", String.valueOf(internetIpRequest.getPort() + 1));
            // 获取最新的数据
            Map<String, Object> params = new HashMap<>(3);
            params.put("start", 20);
            params.put("count", 16);
            params.put("type", "input_registers");
            JSONObject jsonObject = restTemplate.getForObject(tempUrl, JSONObject.class, params);
            // 判断是否正确读取到数据
            assert jsonObject != null;
            List<Integer> list = (List<Integer>) jsonObject.get("data");
            // 获取到数据
            int[] data = list.stream().mapToInt(Integer::valueOf).toArray();
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < EmsConstants.CHAR_SIZE; i++) {
                if (data[i] == 0) {
                    continue;
                }
                value.append((char) data[i]);
            }
            return Result.success(value.toString());
        } catch (Exception e) {
            return Result.success(ErrorResultCode.CONNECT_FAILED.value());
        }
    }

    /** 增加设备 */
    @PostMapping("/add")
    @ApiOperation("增加设备")
    @PreAuthorize("hasAuthority('/group/device/add')")
    @Log(module = "DEVICE", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> add(@RequestBody DeviceRequest deviceRequest) {
        checkIp(deviceRequest);
        checkIndexExist(deviceRequest);
        DeviceEntity deviceEntity = new DeviceEntity();
        BeanUtils.copyProperties(deviceRequest, deviceEntity);
        deviceEntity.setId(StringUtil.uuid());
        deviceEntity.setProjectId(WebUtils.projectId.get());
        deviceService.add(deviceEntity);
        // 同步到协调控制器
        doGoControllerSync();
        return Result.success();
    }

    /** 修改设备 */
    @PostMapping("/update")
    @ApiOperation("修改设备")
    @PreAuthorize("hasAuthority('/group/device/edit')")
    @Log(module = "DEVICE", type = OperationType.UPDATE)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody DeviceRequest deviceRequest) {
        checkIp(deviceRequest);
        checkIndexExist(deviceRequest);
        DeviceEntity deviceEntity = new DeviceEntity();
        BeanUtils.copyProperties(deviceRequest, deviceEntity);
        deviceEntity.setCreateBy(null);
        deviceService.edit(deviceEntity);
        // 同步到协调控制器
        doGoControllerSync();
        // update init electric
        initElectricService.getInitElectric(
                projectService.getById(WebUtils.projectId.get()), deviceEntity);
        return Result.success();
    }

    /** 修改设备 */
    @PostMapping("/updateOrderIndex")
    @ApiOperation("排序")
    @Log(module = "DEVICE", methods = "DEVICE_UPDATE")
    @PreAuthorize("hasAuthority('/group/device/edit')")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(@RequestBody List<String> ids) {
        List<DeviceEntity> entities = deviceService.listByIds(ids);
        // 2. 按照传入的 ids 顺序设置每个 DeviceEntity 的 index 字段
        List<DeviceEntity> orderedEntities =
                ids.stream()
                        .map(
                                id ->
                                        entities.stream()
                                                .filter(
                                                        deviceEntity ->
                                                                deviceEntity.getId().equals(id))
                                                .findFirst()
                                                .orElseThrow(
                                                        () ->
                                                                new ServiceException(
                                                                        "ID " + id + " not found")))
                        .collect(Collectors.toList());

        // 设置 index
        for (int i = 0; i < orderedEntities.size(); i++) {
            DeviceEntity entity = orderedEntities.get(i);
            // 假设 setIndex 方法存在
            entity.setOrderIndex(i);
        }
        deviceService.updateBatchById(orderedEntities);
        return Result.success();
    }

    private static void checkIp(DeviceRequest deviceRequest) {
        Optional.ofNullable(deviceRequest.getIp())
                .ifPresent(
                        (ip) -> {
                            if (RegexUtil.isNotIp(ip)) {
                                throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
                            }
                        });
    }

    private void checkIndexExist(DeviceRequest deviceRequest) {
        if (deviceRequest.getIndex() != null && deviceRequest.getIndex() != 0) {
            DeviceEntity tempDeviceEntity =
                    deviceService
                            .lambdaQuery()
                            .ne(DeviceEntity::getId, deviceRequest.getId())
                            .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                            .eq(DeviceEntity::getIndex, deviceRequest.getIndex())
                            .one();
            if (tempDeviceEntity != null) {
                throw new ServiceException(ErrorResultCode.INDEX_EXIST.value());
            }
        }
    }

    /** 删除设备 */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除设备")
    @PreAuthorize("hasAuthority('/group/device/delete')")
    @Log(
            module = "DEVICE",
            type = OperationType.DEL,
            logDetailServiceClass = DeviceLogDetailService.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> delete(@PathVariable("id") String id) {
        DeviceEntity deviceEntity = deviceService.getById(id);
        deviceService.removeById(id);
        groupDeviceService.remove(
                Wrappers.lambdaQuery(GroupDeviceEntity.class)
                        .eq(GroupDeviceEntity::getDeviceId, id));
        // 同步到协调控制器
        doGoControllerSync();
        return Result.success();
    }

    /** 查询设备 */
    @PostMapping("/query")
    @ApiOperation("查询设备")
    @PreAuthorize("hasAuthority('/group/device/query')")
    public Result<IPage<DeviceEntity>> queryEms(@RequestBody DevicePageRequest devicePageRequest) {
        return query(devicePageRequest, false);
    }

    public Result<IPage<DeviceEntity>> query(
            @RequestBody DevicePageRequest devicePageRequest, boolean noUnrealEms) {
        Page<DeviceEntity> page =
                Page.of(devicePageRequest.getPageNum(), devicePageRequest.getPageSize());
        JSONObject jsonObject;
        List<String> ids = new ArrayList<>();
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/get_online", null, JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/get_online", null, JSONObject.class);
            }
            assert jsonObject != null;
            List<StatusVo> tmpList = jsonObject.getJSONArray("ems").toJavaList(StatusVo.class);
            if (StringUtil.notEmpty(devicePageRequest.getStatus())) {
                boolean status = devicePageRequest.getStatus() == 1;
                ids =
                        tmpList.stream()
                                .filter(e -> e.getOnline() == status)
                                .map(StatusVo::getUuid)
                                .collect(Collectors.toList());
            }
            IPage<DeviceEntity> list =
                    deviceService.page(
                            page,
                            Wrappers.lambdaQuery(DeviceEntity.class)
                                    .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                                    .eq(noUnrealEms, DeviceEntity::getUnreal, false)
                                    .in(!ids.isEmpty(), DeviceEntity::getId, ids)
                                    .eq(
                                            devicePageRequest.getStatus() != null && ids.isEmpty(),
                                            DeviceEntity::getId,
                                            "0")
                                    .like(
                                            StringUtil.notEmpty(devicePageRequest.getName()),
                                            DeviceEntity::getName,
                                            devicePageRequest.getName())
                                    .orderByAsc(DeviceEntity::getOrderIndex)
                                    .orderByAsc(DeviceEntity::getCreateTime));
            final Map<String, Integer> statusMap =
                    tmpList.stream()
                            .collect(
                                    Collectors.toMap(
                                            StatusVo::getUuid, e -> e.getOnline() ? 1 : 0));
            GroupEntity groupEntity;
            if (!noUnrealEms) {
                // 证明是手动查询
                groupEntity =
                        groupService
                                .lambdaQuery()
                                .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                                .eq(GroupEntity::getWhetherSystem, true)
                                .one();
            } else {
                groupEntity = null;
            }
            list.getRecords()
                    .forEach(
                            e -> {
                                e.setStatus(statusMap.get(e.getId()));
                                if (!noUnrealEms) {
                                    // 证明是手动查询
                                    //                                    if
                                    // (!StringUtils.hasLength(groupEntity.getPcsCode())) {
                                    int[] data =
                                            dataService.get(e.getId(), WebUtils.projectId.get());
                                    Optional.ofNullable(data)
                                            .ifPresent(
                                                    da -> {
                                                        if (da[42] == 1 && da[2] != 0) {
                                                            groupEntity.setPcsCode(
                                                                    String.valueOf(da[2]));
                                                            groupService.updateById(groupEntity);
                                                        } else if (da[42] == 2 && data[500] != 0) {
                                                            // data[500]是 ems200 第一个 pcs 的 code
                                                            groupEntity.setPcsCode(
                                                                    String.valueOf(da[500]));
                                                            groupService.updateById(groupEntity);
                                                        }
                                                    });
                                    //                                    }
                                }
                            });
            return Result.success(list);
        } catch (Exception e) {
            log.error("get device list -------> cannot connect to site control");
            IPage<DeviceEntity> pageList =
                    deviceService.page(
                            page,
                            Wrappers.lambdaQuery(DeviceEntity.class)
                                    .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                                    .eq(noUnrealEms, DeviceEntity::getUnreal, false)
                                    .like(
                                            StringUtil.notEmpty(devicePageRequest.getName()),
                                            DeviceEntity::getName,
                                            devicePageRequest.getName())
                                    .orderByAsc(DeviceEntity::getCreateTime));
            pageList.getRecords().forEach(re -> re.setStatus(0));
            return Result.success(pageList);
        }
    }

    /** 查询设备 */
    @PostMapping("/query_inner")
    @ApiOperation("内部查询设备")
    public Result<IPage<DeviceEntity>> queryInner(
            @RequestBody DevicePageRequest devicePageRequest) {
        return query(devicePageRequest, true);
    }

    public void doGoControllerSync() {
        List<DeviceEntity> deviceEntities =
                deviceService.list(
                        Wrappers.lambdaQuery(DeviceEntity.class)
                                .eq(DeviceEntity::getProjectId, WebUtils.projectId.get()));
        List<DeviceGORequest> list =
                deviceEntities.stream()
                        .map(
                                device -> {
                                    DeviceGORequest deviceGoRequest = new DeviceGORequest();
                                    deviceGoRequest.setHost(device.getIp());
                                    deviceGoRequest.setPort(device.getPort());
                                    deviceGoRequest.setUuid(device.getId());
                                    deviceGoRequest.setIndex(device.getIndex());
                                    deviceGoRequest.setName(device.getName());
                                    if (device.getUnreal()) {
                                        deviceGoRequest.setType("EMS100_FAKE");
                                        deviceGoRequest.setPcsIndex(device.getPcsIndex());
                                        deviceGoRequest.setPowerCapacityPercent(
                                                device.getPowerCapacityPercent());
                                    }
                                    if (device.getOutputHistoryInit() != null) {
                                        deviceGoRequest.setOutputHistoryInit(
                                                device.getOutputHistoryInit());
                                    }
                                    if (device.getInputHistoryInit() != null) {
                                        deviceGoRequest.setInputHistoryInit(
                                                device.getInputHistoryInit());
                                    }
                                    // 2024-01-12 17:08:10 add 同步dcdc pv初始值
                                    if (device.getPvInputHistoryInit() != null) {
                                        deviceGoRequest.setPvInputHistoryInit(
                                                device.getPvInputHistoryInit());
                                    }
                                    if (device.getPvOutputHistoryInit() != null) {
                                        deviceGoRequest.setPvOutputHistoryInit(
                                                device.getPvOutputHistoryInit());
                                    }
                                    if (device.getCoolerPreOpenTime() != null) {
                                        deviceGoRequest.setCoolerPreOpenTime(
                                                device.getCoolerPreOpenTime());
                                    }
                                    if (device.getDeviceControlModel() != null) {
                                        if (device.getDeviceControlModel()
                                                .equals(
                                                        DeviceControlModelEnums.PCS_CONTROL
                                                                .name())) {
                                            deviceGoRequest.setDirectPcsPowerControl(true);
                                        }
                                    }
                                    return deviceGoRequest;
                                })
                        .collect(Collectors.toList());
        Map<String, List<DeviceGORequest>> paraMap = new HashMap<>(1);
        paraMap.put("ems", list);
        JSONObject jsonObject;
        try {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            if (projectEntity.getProjectModel() == 1) {
                ControllerEntity controllerEntity =
                        controllerService.getOne(
                                Wrappers.lambdaQuery(ControllerEntity.class)
                                        .eq(
                                                ControllerEntity::getProjectId,
                                                WebUtils.projectId.get()));
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                jsonObject =
                        restTemplate.postForObject(
                                outerControllerUrl + "/api/v1/update_ems",
                                paraMap,
                                JSONObject.class);
            } else {
                jsonObject =
                        restTemplate.postForObject(
                                gatewayUrl + "/api/v1/update_ems", paraMap, JSONObject.class);
            }
        } catch (Exception e) {
            throw new ServiceException(ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
        }
        assert jsonObject != null;
        Optional.of(jsonObject)
                .ifPresent(
                        json -> {
                            if (!(boolean) jsonObject.get(EmsConstants.SUCCESS)) {
                                throw new ServiceException(
                                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                            }
                        });
    }

    /** 查询设备 */
    @PostMapping("/list")
    @ApiOperation("一次查询设备")
    public Result<List<DeviceEntity>> queryInner(@RequestBody ProjectRequest projectRequest) {
        return Result.success(
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, projectRequest.getProjectId())
                        .orderByAsc(DeviceEntity::getOrderIndex)
                        .list());
    }

    /** 检查IP端口冲突job zi */
    @PostMapping("/checkIpOrPort")
    @ApiOperation("检查IP端口冲突")
    @PreAuthorize("hasAuthority('/group/device/add')")
    public Result<String> checkIpOrPort(@RequestBody DeviceIpPortPreCheck deviceIpPortPreCheck) {
        if (RegexUtil.isNotIp(deviceIpPortPreCheck.getIp())) {
            throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
        }
        boolean conflict =
                deviceService.isIpPortConflict(
                        deviceIpPortPreCheck.getIp(), deviceIpPortPreCheck.getPort());
        if (conflict) {
            return Result.failure(ErrorResultCode.IP_PORT_EXIST.value());
        } else {
            return Result.success();
        }
    }
}
