package com.wifochina.modules.group.request.go;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * GroupGoRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 4/20/2022 6:46 PM
 */
@Data
public class GroupGoRequest implements Cloneable {

    /** 防逆流功率 默认传10 */
    @JsonProperty("back_flow_limit_power")
    private Double backFlowLimitPower = 10d;

    /** 1.4.4 added 分时防逆流 */
    @JsonProperty("back_flow_time_slot")
    private List<GoTimeSlot> backFlowTimeSlot;

    /** future added 分时容量 */
    @JsonProperty("required_time_slot")
    private List<GoTimeSlot> requireTimeSlot;

    /** 1.4.4 added 分时需量 */
    @JsonProperty("gird_control_time_slot")
    private List<GoTimeSlot> girdControlTimeSlot;

    /** 防逆流开 默认不开 */
    @JsonProperty("back_flow_preventing")
    private boolean backFlowPreventing = false;

    /** 仅在规定时间内充电 */
    @JsonProperty("back_flow_preventing_not_allow_charge")
    private boolean backFlowPreventingNotAllowCharge = false;

    /** 仅在规定时间内放电 */
    @JsonProperty("required_power_preventing_not_allow_discharge")
    private boolean backFlowPreventingNotAllowDischarge = false;

    /** 默认120 */
    @JsonProperty("direct_power_beat")
    private Integer directPowerBeat = 120;

    /** 默认true 超时停机 */
    @JsonProperty("direct_power_beat_protect_enable")
    private boolean directPowerBeatProtectEnable = true;

    /** "10000-20000之间,隔100的整数值" */
    @JsonProperty("direct_power_control_offset")
    private Integer directPowerControlOffset;

    @JsonProperty("direct_power_readonly")
    private boolean directPowerReadonly = true;

    /** 默认0 */
    @JsonProperty("direct_required_active_power")
    private Double directRequiredActivePower = 0d;

    /** 默认0 */
    @JsonProperty("direct_required_reactive_power")
    private Double directRequiredReactivePower = 0d;

    /** 放电soc下限 */
    @JsonProperty("discharge_soc_low_limit")
    private Integer dischargeSocLowLimit = 10;

    /** 离网soc下限 */
    @JsonProperty("off_grid_soc_low_limit")
    private Integer offGridSocLimit = 10;

    /** 并网soc上限 */
    @JsonProperty("charge_soc_high_limit")
    private Integer chargeSocHighLimit = 100;

    /** 离网soc上限 */
    @JsonProperty("off_grid_soc_high_limit")
    private Integer offGridSocHighLimit = 100;

    /** 容量控制 */
    @JsonProperty("required_power")
    private Double requiredPower = 0d;

    /** 申报需量 */
    @JsonProperty("demand_power")
    private Double demandPower = 0d;

    /** 需量控制 */
    @JsonProperty("grid_control_power")
    private Double gridControlPower = 0d;

    /** 是否系统 */
    private boolean system = false;

    /** 开关 */
    private boolean start = true;

    private List<Strategy> strategies;

    @JsonProperty("yearly_strategies")
    private Map<String, List<YearlyStrategy>> yearlyStrategies;

    /** "策略优先级" */
    @JsonProperty("strategy_priority")
    private Integer strategyPriority = 0;

    /** "是否设置soc边界" */
    @JsonProperty("enable_stop_soc")
    private Integer enableStopSoc = 0;

    @JsonProperty("total_strategies")
    private Integer totalStrategies = 0;

    /** 功率因数控制模式开关 */
    @JsonProperty("power_factor_control")
    private Boolean powerFactorControl;

    /** 功率因数优先模式-关闭时为有功功率优先 */
    @JsonProperty("power_factor_first")
    private Boolean powerFactorFirst;

    /** 功率因数控制值，0.9 - 1 */
    @JsonProperty("power_factor_control_value")
    private Double powerFactorControlValue;

    /** 外部控制器模式开关 */
    @JsonProperty("direct_power_control")
    private boolean directPowerControl = false;

    /** 外部控制模式切换：是否根据远端控制自动切换，false为手动 */
    @JsonProperty("direct_power_auto_control")
    private Boolean directPowerAutoControl;

    /** 外部控制模式自动时，切换开关的104遥控点位 */
    @JsonProperty("direct_power_control_iec104_enable_ioa")
    private Integer directPowerControlIec104EnableIoa;

    /** 外部控制模式下，104 协议中，公共地址 */
    @JsonProperty("direct_power_control_iec104_common_addr")
    private Integer directPowerControlIec104CommonAddr;

    /** 外部控制模式下，104 协议中，有功功率摇调地址 */
    @JsonProperty("direct_power_control_iec104_active_ioa")
    private Integer directPowerControlIec104ActiveIoa;

    /** 外部控制模式下，104 协议中，无功功率摇调地址 */
    @JsonProperty("direct_power_control_iec104_reactive_ioa")
    private Integer directPowerControlIec104ReactiveIoa;

    /** modbus协议中，有功功率寄存器地址(两个寄存器) */
    @JsonProperty("direct_power_control_modbus_active_addr")
    private Integer directPowerControlModbusActiveAddr;

    /** modbus协议中，无功功率寄存器地址(两个寄存器) */
    @JsonProperty("direct_power_control_modbus_reactive_addr")
    private Integer directPowerControlModbusReactiveAddr;

    @ApiModelProperty(value = "可控设备运行策略")
    @JsonProperty("controllable_strategies")
    private Map<String, Object> controllableStrategies;

    @ApiModelProperty(value = "ems soc上限")
    @JsonProperty("ems_strategies")
    private Map<String, Object> emsStrategies;

    @ApiModelProperty(value = "是否开启vpp")
    private Boolean vpp;

    @ApiModelProperty(value = "微网储能SOC被动均衡")
    @JsonProperty("mg_soc_balance")
    private Boolean mgSocBalance;

    @ApiModelProperty(value = "微网储能SOC被动均衡 功率限制比例0-1 默认0.8")
    @JsonProperty("mg_soc_balance_off_grid_power_limit_ratio")
    private Double mgSocBalanceOffGridPowerLimitRatio;

    @Override
    protected Object clone() throws CloneNotSupportedException {
        GroupGoRequest groupGoRequest = (GroupGoRequest) super.clone();
        List<Strategy> strategies = new ArrayList<>();
        groupGoRequest.setStrategies(strategies);
        return groupGoRequest;
    }
}
