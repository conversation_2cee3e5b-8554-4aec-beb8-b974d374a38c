package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分组和设备关联表
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode()
@TableName("t_group_device")
@ApiModel(value = "GroupDeviceEntity对象", description = "分组和设备关联表")
public class GroupDeviceEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "分组id")
    @TableField("groupId")
    private String groupId;

    @ApiModelProperty(value = "设备id")
    @TableField("deviceId")
    private String deviceId;
}
