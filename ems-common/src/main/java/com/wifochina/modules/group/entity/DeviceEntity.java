package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.common.util.EmsConstants;

import com.wifochina.modules.group.enums.DeviceControlModelEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_device")
@ApiModel(value = "DeviceEntity对象", description = "DeviceEntity对象")
public class DeviceEntity extends BaseEquipEntity implements IdSearchSupport {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @ApiModelProperty(value = "运行状态。true为运行。false为停止")
    private Boolean runStatus;

    @ApiModelProperty(value = "设备二维码code")
    private String code;

    @ApiModelProperty(value = "电量输出初始值")
    private Double outputHistoryInit;

    @ApiModelProperty(value = "电量输入初始值")
    private Double inputHistoryInit;

    @ApiModelProperty(value = "是否虚拟,默认不虚拟false")
    private Boolean unreal;

    @ApiModelProperty(value = "虚拟设备的pcs序号,0,1,2,3,4")
    private Integer pcsIndex;

    @ApiModelProperty(value = "功率分配百分比")
    private Float powerCapacityPercent;

    @ApiModelProperty(value = "是否维护,默认不维护false")
    private Boolean maintain;

    @ApiModelProperty(value = "是否显示gps，默认不显示false")
    private Boolean showGps;

    @ApiModelProperty(value = "是否计算收益,默认不维护false")
    private Boolean income;

    @ApiModelProperty(value = "序号")
    @TableField(value = "`index`")
    private Integer index;

    // ------ add for dcdc 2024-01-10 16:04:21------

    @ApiModelProperty(value = "是否有STS")
    private Boolean hasSts;

    @ApiModelProperty(value = "是否有DCDC")
    private Boolean hasDcdc;

    @ApiModelProperty(value = "pv表初始放电量初始值")
    private Double pvOutputHistoryInit;

    @ApiModelProperty(value = "pv表初始充电量初始值")
    private Double pvInputHistoryInit;

    @ApiModelProperty(value = "是否主机")
    private Boolean isHost;

    @ApiModelProperty(value = "future 使用 厂商和type")
    @TableField(exist = false)
    private String vendorType;

    @ApiModelProperty(value = "投运初始时间InData")
    private Double projectRunInitInData;

    @ApiModelProperty(value = "投运初始时间OutData")
    private Double projectRunInitOutData;

    @ApiModelProperty(value = "水冷预开启时间")
    private Integer coolerPreOpenTime;

    @ApiModelProperty(value = "设备的控制模式")
    /**
     * @see DeviceControlModelEnums
     */
    private String deviceControlModel;

    @Override
    public String type() {
        return EmsConstants.DEVICE;
    }

    @Override
    public String id() {
        return this.getId();
    }
}
