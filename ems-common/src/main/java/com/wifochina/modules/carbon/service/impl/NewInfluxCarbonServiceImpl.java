package com.wifochina.modules.carbon.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.influxdb.query.dsl.functions.FillFlux;
import com.influxdb.query.dsl.functions.FilterFlux;
import com.influxdb.query.dsl.functions.PivotFlux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.influx.FluxAdapter;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.NewInfluxTimeCalcEnum;
import com.wifochina.modules.carbon.VO.Carbon;
import com.wifochina.modules.carbon.VO.CarbonTotal;
import com.wifochina.modules.carbon.request.CarbonRequest;
import com.wifochina.modules.carbon.service.CarbonService;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.data.entity.MeterContentData;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 4/14/2022 11:25 AM
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NewInfluxCarbonServiceImpl implements CarbonService {

    private final AmmeterService ammeterService;

    private final DeviceService deviceService;

    private final GroupService groupService;

    private final GroupAmmeterService groupAmmeterService;

    private final GroupDeviceService groupDeviceService;

    private final ProjectService projectService;

    private final DataService dataService;

    private final InfluxClientService influxClient;

    private final PointListHolder pointListHolder;

    @Value("${carbon.load.emission}")
    private double carbonLoadEmission;

    @Value("${carbon.pv.reduction}")
    private double carbonPvReduction;

    @Value("${carbon.pv.power}")
    private double carbonPvPower;

    @Value("${carbon.ems.reduction}")
    private double carbonEmsReduction;

    @Value("${carbon.ems.power}")
    private double carbonEmsPower;

    @Override
    public Map<String, Object> getCarbon(CarbonRequest reportRequest, String type) {
        List<Carbon> carbons = new ArrayList<>();
        double totalPv = 0d;
        double totalWind = 0d;
        double totalWaster = 0d;
        double totalEmsOut = 0d;
        double totalGridOut = 0d;
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        long start = adjustStartDate(reportRequest.getStartDate(), type);
        long end = reportRequest.getEndDate() + 1;
        String timeZoneCode = projectEntity.getTimezone();
        // Retrieve data maps
        Map<Long, Double> pvMap =
                fetchPowerData(
                        projectId,
                        start,
                        end,
                        type,
                        MeterTypeEnum.PV,
                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH);
        Map<Long, Double> gridOutMap =
                fetchPowerData(
                        projectId,
                        start,
                        end,
                        type,
                        MeterTypeEnum.GRID,
                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH);
        Map<Long, Double> windMap =
                fetchPowerData(
                        projectId,
                        start,
                        end,
                        type,
                        MeterTypeEnum.WIND,
                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH);

        Map<Long, Double> wasterMap =
                fetchPowerData(
                        projectId,
                        start,
                        end,
                        type,
                        MeterTypeEnum.WASTER,
                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH);
        Map<Long, Double> emsInMap =
                fetchEmsData(projectId, start, end, type, EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY);
        Map<Long, Double> emsOutMap =
                fetchEmsData(projectId, start, end, type, EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY);
        Map<Long, Double> dcdcMap =
                fetchEmsData(
                        projectId, start, end, type, EmsFieldEnum.DCDC_METER_HISTORY_ENERGY_POS);
        // Combine and calculate carbon data
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        Set<Long> keySet =
                getNonNullKeySet(emsOutMap, emsInMap, gridOutMap, windMap, dcdcMap, wasterMap);
        if (keySet != null) {
            long todayZero = MyTimeUtil.getTodayZeroTime(timeZoneCode);
            keySet = keySet.stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new));
            for (Long time : keySet) {
                Carbon carbon = new Carbon();

                double emsOut = getValueFromMap(emsOutMap, time);
                double pv = getValueFromMap(pvMap, time) + getValueFromMap(dcdcMap, time);
                double gridOut = getValueFromMap(gridOutMap, time);
                double wind = getValueFromMap(windMap, time);
                double waster = getValueFromMap(wasterMap, time);

                if (time >= todayZero) {
                    totalEmsOut += emsOut;
                    totalPv += pv;
                    totalGridOut += gridOut;
                    totalWind += wind;
                    totalWaster += waster;
                }
                log.debug(
                        "time:{} , emsOut: {} , pv:{} , gridOut:{} , wind:{}, waster:{}",
                        LocalDateTime.ofInstant(
                                Instant.ofEpochSecond(time), ZoneId.of(timeZoneCode)),
                        emsOut,
                        pv,
                        gridOut,
                        wind,
                        waster);
                carbon.setCarbonEmission(calculateCarbonEmission(gridOut));
                carbon.setCarbonReduction(calculateCarbonReduction(emsOut, pv, wind, waster));
                if (type.equals(EmsConstants.MONTH)) {
                    carbon.setTime(time - ChronoUnit.DAYS.getDuration().getSeconds());
                } else {
                    carbon.setTime(time);
                }
                carbons.add(carbon);
            }
        }
        carbons.sort(Comparator.comparing(Carbon::getTime));
        // 准备result 返回
        return prepareReportResult(
                carbons, totalGridOut, totalEmsOut, totalWind, totalPv, totalWaster, type);
    }

    // Helper methods
    private long adjustStartDate(long startDate, String type) {
        if (Objects.equals(type, EmsConstants.DAILY)) {
            return startDate - ChronoUnit.HOURS.getDuration().getSeconds();
        } else if (Objects.equals(type, EmsConstants.MONTH)) {
            return startDate - ChronoUnit.DAYS.getDuration().getSeconds();
        } else if (Objects.equals(type, EmsConstants.YEAR)) {
            return startDate - 31 * ChronoUnit.DAYS.getDuration().getSeconds();
        }
        return startDate;
    }

    private Map<Long, Double> fetchPowerData(
            String projectId,
            long start,
            long end,
            String searchType,
            MeterTypeEnum meterType,
            MeterFieldEnum meterFieldEnum) {
        return getPowerNew(
                start,
                end,
                influxClient.getMeterTable(projectId),
                meterFieldEnum.field(),
                NewInfluxTimeCalcEnum.getTimeFromType(searchType),
                meterType.meterType().toString());
    }

    private Map<Long, Double> fetchEmsData(
            String projectId, long start, long end, String searchType, EmsFieldEnum emsField) {
        // 执行PowerNew
        return getPowerNew(
                start,
                end,
                influxClient.getEmsTable(projectId),
                emsField.field(),
                NewInfluxTimeCalcEnum.getTimeFromType(searchType),
                null);
    }

    @SafeVarargs
    private Set<Long> getNonNullKeySet(Map<Long, Double>... maps) {
        for (Map<Long, Double> map : maps) {
            if (map != null && !map.isEmpty()) {
                return map.keySet();
            }
        }
        return null;
    }

    private double getValueFromMap(Map<Long, Double> map, Long key) {
        return map != null ? map.getOrDefault(key, 0.0) : 0.0;
    }

    private double calculateCarbonEmission(double gridOut) {
        return gridOut * carbonLoadEmission;
    }

    private double calculateCarbonReduction(double emsOut, double pv, double wind, double waster) {
        return emsOut * carbonEmsReduction + (pv + wind + waster) * carbonPvReduction;
    }

    private Map<String, Object> prepareReportResult(
            List<Carbon> carbons,
            double totalGridOut,
            double totalEmsOut,
            double totalWind,
            double totalPv,
            double totalWaster,
            String type) {
        Map<String, Object> result = new HashMap<>(6);
        result.put("carbons", carbons);

        if (Objects.equals(type, EmsConstants.DAILY)) {
            result.put("daily_emission", totalGridOut * carbonLoadEmission);
            result.put(
                    "daily_reduction",
                    totalEmsOut * carbonEmsReduction
                            + (totalWind + totalPv + totalWaster) * carbonPvReduction);

            CarbonTotal carbonTotal = getSiteByProjectId(WebUtils.projectId.get());
            result.put("total_reduction", carbonTotal.getCarbonReduction());
            result.put("total_emission", carbonTotal.getCarbonEmission());
            result.put("reduction_power", carbonTotal.getCarbonReductionPower());
        }
        return result;
    }

    /**
     * 能量
     *
     * @param type type代表实时。daily 日报；month 月报；年报year
     * @return 总收益情况
     */
    //    @Override
    //    public Map<String, Object> getCarbon(CarbonRequest reportRequest, String type) {
    //        List<Carbon> carbons = new ArrayList<>();
    //        double totalPv = 0d;
    //        double totalWind = 0d;
    //        double totalEmsOut = 0d;
    //        double totalGridOut = 0d;
    //        Map<Long, Double> pvMap;
    //        Map<Long, Double> gridOutMap;
    //        Map<Long, Double> emsInMap;
    //        Map<Long, Double> emsOutMap;
    //        Map<Long, Double> windMap;
    //        Map<Long, Double> dcdcMap;
    //        String projectId = WebUtils.projectId.get();
    //        ProjectEntity projectEntity = projectService.getById(projectId);
    //        long start = reportRequest.getStartDate();
    //        long end = reportRequest.getEndDate() + 1;
    //        String timeZoneCode = projectEntity.getTimezone();
    //        if (Objects.equals(type, EmsConstants.DAILY)) {
    //            start = start - ChronoUnit.HOURS.getDuration().getSeconds();
    //        } else if (Objects.equals(type, EmsConstants.MONTH)) {
    //            start = start - ChronoUnit.DAYS.getDuration().getSeconds();
    //        } else if (Objects.equals(type, EmsConstants.YEAR)) {
    //            start = start - 31 * ChronoUnit.DAYS.getDuration().getSeconds();
    //        }
    //        pvMap =
    //                getPowerNew(
    //                        start,
    //                        end,
    //                        timeZoneCode,
    //                        influxClient.getMeterTable(projectId),
    //                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
    //                        InfluxTimeCalcEnum.getTimeFromType(type),
    //                        MeterTypeEnum.PV.meterType().toString());
    //        gridOutMap =
    //                getPowerNew(
    //                        start,
    //                        end,
    //                        timeZoneCode,
    //                        influxClient.getMeterTable(projectId),
    //                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field(),
    //                        InfluxTimeCalcEnum.getTimeFromType(type),
    //                        MeterTypeEnum.GRID.meterType().toString());
    //        windMap =
    //                getPowerNew(
    //                        start,
    //                        end,
    //                        timeZoneCode,
    //                        influxClient.getMeterTable(projectId),
    //                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
    //                        InfluxTimeCalcEnum.getTimeFromType(type),
    //                        MeterTypeEnum.WIND.meterType().toString());
    //        emsInMap =
    //                getPowerNew(
    //                        start,
    //                        end,
    //                        timeZoneCode,
    //                        influxClient.getEmsTable(projectId),
    //                        EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field(),
    //                        InfluxTimeCalcEnum.getTimeFromType(type),
    //                        null);
    //        emsOutMap =
    //                getPowerNew(
    //                        start,
    //                        end,
    //                        timeZoneCode,
    //                        influxClient.getEmsTable(projectId),
    //                        EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field(),
    //                        InfluxTimeCalcEnum.getTimeFromType(type),
    //                        null);
    //        dcdcMap =
    //                getPowerNew(
    //                        start,
    //                        end,
    //                        timeZoneCode,
    //                        influxClient.getEmsTable(projectId),
    //                        EmsFieldEnum.DCDC_METER_HISTORY_ENERGY_POS.field(),
    //                        InfluxTimeCalcEnum.getTimeFromType(type),
    //                        null);
    //        // EMS净出 电池放电电量 - 电池充电电量
    //        // 电网净出 电网取电 - 馈网电量
    //        // 总消耗 = 电网馈 + pv发电 +EMS放 - ems充- grid馈
    //        Set<Long> keySet = null;
    //        assert emsOutMap != null;
    //        if (!emsOutMap.isEmpty()) {
    //            keySet = emsOutMap.keySet();
    //        } else if (!emsInMap.isEmpty()) {
    //            keySet = emsInMap.keySet();
    //        } else if (!gridOutMap.isEmpty()) {
    //            keySet = gridOutMap.keySet();
    //        } else if (!windMap.isEmpty()) {
    //            keySet = windMap.keySet();
    //        } else if (!dcdcMap.isEmpty()) {
    //            keySet = dcdcMap.keySet();
    //        }
    //        if (keySet != null) {
    //            long todayZero = MyTimeUtil.getTodayZeroTime(timeZoneCode);
    //            for (Long time : keySet) {
    //                Carbon carbon = new Carbon();
    //                carbon.setTime(time);
    //                double emsOut = 0;
    //                if (!emsOutMap.isEmpty()) {
    //                    emsOut = emsOutMap.get(time);
    //                    if (time >= todayZero) {
    //                        totalEmsOut += emsOutMap.get(time);
    //                    }
    //                }
    //                double pv = 0;
    //                if (!pvMap.isEmpty()) {
    //                    pv = pvMap.get(time);
    //                    if (time >= todayZero) {
    //                        totalPv += pv;
    //                    }
    //                }
    //                double dcdcPower = 0;
    //                if (!dcdcMap.isEmpty() && time != null) {
    //                    dcdcPower = dcdcMap.get(time) == null ? 0 : dcdcMap.get(time);
    //                    // 把dcdc的pv 添加到 总光伏上
    //                    if (time >= todayZero) {
    //                        totalPv += dcdcPower;
    //                    }
    //                    // 把dcdc的pv 添加到 光伏上, 后期可能会拆开
    //                    pv += dcdcPower;
    //                }
    //                double gridOut = 0;
    //                if (!gridOutMap.isEmpty()) {
    //                    gridOut = gridOutMap.get(time);
    //                    if (time >= todayZero) {
    //                        totalGridOut += gridOut;
    //                    }
    //                }
    //                double wind = 0;
    //                if (!windMap.isEmpty()) {
    //                    wind = windMap.get(time) == null ? 0 : windMap.get(time);
    //                    if (time >= todayZero) {
    //                        totalWind += wind;
    //                    }
    //                }
    //                // 碳排放量：负载系数（0.6）
    //                // 碳减排量：储能放电量0.35+PV放电量*0.997
    //                carbon.setCarbonEmission(gridOut * carbonLoadEmission);
    //                carbon.setCarbonReduction(
    //                        emsOut * carbonEmsReduction + (pv + wind) * carbonPvReduction);
    //                carbon.setTime(time);
    //                carbons.add(carbon);
    //            }
    //        }
    //        carbons.sort(comparing(Carbon::getTime));
    //        Map<String, Object> map = new HashMap<>(6);
    //        map.put("carbons", carbons);
    //        if (Objects.equals(type, EmsConstants.DAILY)) {
    //            map.put("daily_emission", totalGridOut * carbonLoadEmission);
    //            map.put(
    //                    "daily_reduction",
    //                    totalEmsOut * carbonEmsReduction + (totalWind + totalPv) *
    // carbonPvReduction);
    //            CarbonTotal carbonTotal = getSite();
    //            map.put("total_reduction", carbonTotal.getCarbonReduction());
    //            map.put("total_emission", carbonTotal.getCarbonEmission());
    //            map.put("reduction_power", carbonTotal.getCarbonReductionPower());
    //        }
    //        return map;
    //    }

    Map<Long, Double> getPowerNew(
            long start,
            long end,
            String tableName,
            String column,
            NewInfluxTimeCalcEnum influxTimeCalcEnum,
            String type) {

        FilterFlux tempFilterFlux =
                FluxAdapter.builder()
                        .projectId(WebUtils.projectId.get())
                        .timeZoneSet(
                                ChronoUnit.DAYS.equals(influxTimeCalcEnum.getUnit())
                                        || ChronoUnit.MONTHS.equals(influxTimeCalcEnum.getUnit()))
                        .from(influxClient.getBucketForever())
                        .range(start, end)
                        .measurement(tableName)
                        .fields(List.of(column))
                        .toFlux();
        if (type != null) {
            tempFilterFlux =
                    tempFilterFlux.filter(
                            Restrictions.and(
                                    Restrictions.column("system").equal("1"),
                                    Restrictions.column("type").equal(type)));
        }
        PivotFlux tempPivotFlux =
                tempFilterFlux
                        .aggregateWindow(
                                influxTimeCalcEnum.getTime(),
                                influxTimeCalcEnum.getUnit(),
                                EmsConstants.INFLUX_MAX_FUNC)
                        .pivot(
                                List.of(EmsConstants.INFLUX_TIME),
                                List.of(EmsConstants.INFLUX_FIELD),
                                EmsConstants.INFLUX_VALUE);
        FillFlux fill;
        if (influxTimeCalcEnum.getType().equals(NewInfluxTimeCalcEnum.YEAR.getType())) {
            fill = tempPivotFlux.fill(0.0);
        } else {
            fill = tempPivotFlux.fill().withUsePrevious(true);
        }
        return FluxAdapter.query(
                        fill.difference(List.of(column), true)
                                .fill(0.0)
                                .groupBy(List.of(EmsConstants.INFLUX_TIME))
                                .sum(column)
                                //                                .timeShift(
                                //                                        -1L,
                                //                                        ChronoUnit.SECONDS,
                                //                                        List.of("_start", "_stop",
                                // "_time"))
                                .toString())
                .valueKey(column)
                .handleResult()
                .toDouble();
    }

    //    Map<Long, Double> getPower(
    //            long start,
    //            long end,
    //            String timeZoneCode,
    //            String tableName,
    //            String column,
    //            String period,
    //            String type) {
    //        String condition = "";
    //        if (type != null) {
    //            condition = "and r.system == \"1\" and r.type ==\"" + type + "\"";
    //        }
    //        String queryString =
    //                "import \"timezone\"\n"
    //                        + "option location = timezone.fixed(offset: "
    //                        + MyTimeUtil.getOffsetSecondsFromZoneCode(timeZoneCode)
    //                        + "s)\n"
    //                        + "       from(bucket: \"forever\")\n"
    //                        + "        |> range(start: {start}, stop: {end} )\n"
    //                        + "        |> filter(fn: (r) => r._measurement == \"{tableName}\")\n"
    //                        + "        |> filter(fn: (r) => r.projectId == \""
    //                        + WebUtils.projectId.get()
    //                        + "\")\n"
    //                        + "        |> filter(fn: (r) => r[\"_field\"] == \"{column}\"
    // {condition} )\n"
    //                        + "        |> aggregateWindow(every: {period}, fn: max)\n"
    //                        + "        |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"],
    // valueColumn: \"_value\")\n";
    //        if (EmsConstants.PERIOD_1_MO.equals(period)) {
    //            queryString = queryString + "        |> fill(column: \"{column}\", value: 0.0)\n";
    //        } else {
    //            queryString =
    //                    queryString + "        |> fill(column: \"{column}\", usePrevious:
    // true)\n";
    //        }
    //        queryString =
    //                queryString
    //                        + "        |> difference(columns:[\"{column}\"], nonNegative: true)\n"
    //                        + "        |> fill(column: \"{column}\", value: 0.0)\n"
    //                        + "        |> group(columns: [\"_time\"], mode:\"by\")\n"
    //                        + "        |> sum(column:\"{column}\")\n"
    //                        + "        |> timeShift(duration: -1s, columns: [\"_start\",
    // \"_stop\", \"_time\"])\n";
    //        // 将query进行兑换
    //        queryString =
    //                queryString
    //                        .replace("{start}", String.valueOf(start))
    //                        .replace("{end}", String.valueOf(end));
    //        queryString = queryString.replace("{column}", column);
    //        queryString = queryString.replace("{condition}", condition);
    //        queryString = queryString.replace("{tableName}", tableName);
    //        queryString = queryString.replace("{period}", period);
    //        log.debug(queryString);
    //        List<FluxTable> tables = influxClient.queryApi.query(queryString);
    //        Map<Long, Double> map = new HashMap<>();
    //        for (FluxTable table : tables) {
    //            for (FluxRecord record : table.getRecords()) {
    //                Double value = (Double) record.getValueByKey(column);
    //                Instant time = (Instant) record.getValueByKey("_time");
    //                assert time != null;
    //                map.put(time.getEpochSecond(), Objects.requireNonNullElse(value, 0d));
    //            }
    //        }
    //        return map;
    //    }

    @Override
    public CarbonTotal getSiteByProjectId(String projectId) {
        List<AmmeterEntity> ammeterEntities =
                ammeterService.list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .eq(AmmeterEntity::getProjectId, projectId));
        GroupEntity systemGroupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, projectId)
                                .eq(GroupEntity::getWhetherSystem, true));
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService.list(
                        Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                                .eq(GroupAmmeterEntity::getGroupId, systemGroupEntity.getId()));
        List<String> sysAmmeterIds =
                groupAmmeterEntities.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        List<GroupDeviceEntity> groupDeviceEntities =
                groupDeviceService.list(
                        Wrappers.lambdaQuery(GroupDeviceEntity.class)
                                .eq(GroupDeviceEntity::getGroupId, systemGroupEntity.getId()));
        List<String> sysDeviceIds =
                groupDeviceEntities.stream()
                        .map(GroupDeviceEntity::getDeviceId)
                        .collect(Collectors.toList());
        double pvPower = 0;
        double pvTotal = 0;
        double gridOutTotal = 0;
        double windPower = 0;
        double windTotal = 0;
        double wasterPower = 0;
        double wasterTotal = 0;
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            if (!sysAmmeterIds.contains(ammeterEntity.getId())) {
                continue;
            }
            MeterContentData meterContentData =
                    dataService.collectMeterDataPoint(projectId).get(ammeterEntity.getId());
            if (meterContentData != null) {
                if (MeterTypeEnum.PV.meterType().equals(ammeterEntity.getType())) {
                    // Pv发电
                    pvTotal += meterContentData.getAc_history_positive_power_in_kwh();
                    pvPower += meterContentData.getAc_active_power();
                } else if (MeterTypeEnum.GRID.meterType().equals(ammeterEntity.getType())) {
                    // 电网取点
                    gridOutTotal += meterContentData.getAc_history_negative_power_in_kwh();
                } else if (MeterTypeEnum.WIND.meterType().equals(ammeterEntity.getType())) {
                    windTotal += meterContentData.getAc_history_positive_power_in_kwh();
                    windPower += meterContentData.getAc_active_power();
                } else if (MeterTypeEnum.WASTER.meterType().equals(ammeterEntity.getType())) {
                    wasterTotal += meterContentData.getAc_history_positive_power_in_kwh();
                    wasterPower += meterContentData.getAc_active_power();
                }
            }
        }
        double emsOutTotal = 0;
        double emsOutPower = 0;
        double dcdcOutTotal = 0;
        double dcdcOutPower = 0;
        List<DeviceEntity> deviceEntities =
                deviceService.list(
                        Wrappers.lambdaQuery(DeviceEntity.class)
                                .eq(DeviceEntity::getProjectId, projectId));
        for (DeviceEntity deviceEntity : deviceEntities) {
            if (!sysDeviceIds.contains(deviceEntity.getId())) {
                continue;
            }
            int[] data = dataService.get(deviceEntity.getId(), projectId);
            if (data == null) {
                continue;
            }
            int emsType = data[42];
            int highOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType)];
            int lowOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType) + 1];
            long out = ((long) highOut << 16) + lowOut;
            emsOutTotal += (out * 1.0 / 10);
            double point =
                    ((double) (short) data[pointListHolder.getEmsAcActivePower(emsType)]) / 10;
            if (point > 0) {
                // 放电
                emsOutPower += point;
            }
            // 2024-01-17 14:20:17 add dcdc
            // 这个要去累计到 pv电量上 不需要开dcdc也要累加上 所以放在外面
            int high = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType)];
            int low = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType) + 1];
            dcdcOutTotal += (high << 16) + low;
            dcdcOutTotal = Double.parseDouble(String.format("%.2f", dcdcOutTotal / 10));
            dcdcOutPower += (short) data[pointListHolder.getDcdcMeterPower(emsType)];
            dcdcOutPower = Double.parseDouble(String.format("%.2f", dcdcOutPower / 10));
        }
        // 如果没开 pv 模式 则直接把 pv相关的变成0
        if (Boolean.FALSE.equals(systemGroupEntity.getPhotovoltaicController())) {
            pvTotal = 0;
            pvPower = 0;
        }
        if (Boolean.FALSE.equals(systemGroupEntity.getEnableWindPowerGeneration())) {
            windTotal = 0;
            windPower = 0;
        }
        // 1.4.2 added waster 余热发电 关联碳减排k
        if (Boolean.FALSE.equals(systemGroupEntity.getEnableWastePowerGeneration())) {
            wasterTotal = 0;
            wasterPower = 0;
        }
        // 如果开dcdc则吧 dcdc的添加上去
        if (Boolean.TRUE.equals(systemGroupEntity.getDcdcProfitController())) {
            // 把dcdc的添加到 pv上
            // 电量
            pvTotal += dcdcOutTotal;
            // 功率
            pvPower += dcdcOutPower;
        }
        /*
             碳排放量：日用电量（从电网）*区域排放因子
             碳减排量： PV发电量*（区域排放因子-0.05）+储能放电量*0.2*（区域排放因子*0.5）
             碳减排强度（kg/h）：PV功率（kW）*（区域排放因子-0.05）+储能放电功率（kW）0.2*（区域排放因子*0.5）
             碳排放量：电网（0.6435）
             碳减排量：储能放电量0.06435+PV放电量*0.5935
             碳减排强度（kg/h）：PV实时功率（kW）*0.5935+储能放电功率（kW）*0.06435
        */
        CarbonTotal carbonTotal = new CarbonTotal();
        carbonTotal.setCarbonEmission(gridOutTotal * carbonLoadEmission);
        if (Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())
                || Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())
                || Boolean.TRUE.equals(systemGroupEntity.getDcdcProfitController())
                || Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration())) {
            carbonTotal.setCarbonReduction(
                    emsOutTotal * carbonEmsReduction
                            + (pvTotal + windTotal + wasterTotal) * carbonPvReduction);
            carbonTotal.setCarbonReductionPower(
                    (pvPower + windPower + wasterPower) * carbonPvPower / 1000
                            + emsOutPower * carbonEmsPower);
        } else {
            carbonTotal.setCarbonReduction(0.0);
            carbonTotal.setCarbonReductionPower(0.0);
        }
        return carbonTotal;
    }
}
