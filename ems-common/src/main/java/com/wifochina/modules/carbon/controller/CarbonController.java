package com.wifochina.modules.carbon.controller;

import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.carbon.request.CarbonRequest;
import com.wifochina.modules.carbon.service.CarbonService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.Map;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@RequestMapping("/carbon")
@RestController
@Api(tags = "14-碳排放")
@RequiredArgsConstructor
public class CarbonController {

    private final CarbonService carbonService;

    /** 电站日报 */
    @PostMapping("/realtime")
    @ApiOperation("实时排放")
    @PreAuthorize("hasAuthority('/carbon/reduction')")
    public Result<Map<String, Object>> lisDailyReport(@RequestBody CarbonRequest reportRequest) {
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        Map<String, Object> map = carbonService.getCarbon(reportRequest, EmsConstants.DAILY);
        return Result.success(map);
    }

    /** TODO 这里 还是以前的 逻辑 去直接 月年都是 直接去influxdb 查询会比较慢 , 像 电表月年/整站月年 都已经改从 DayReport 缓存查询了 这里以后看吧 */
    /** 电站月报 */
    @PostMapping("/month")
    @ApiOperation("月减排")
    @PreAuthorize("hasAuthority('/carbon/track')")
    public Result<Map<String, Object>> lisMonthReport(@RequestBody CarbonRequest reportRequest) {
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        Map<String, Object> map = carbonService.getCarbon(reportRequest, EmsConstants.MONTH);
        return Result.success(map);
    }

    /** TODO 这里 还是以前的 逻辑 去直接 月年都是 直接去influxdb 查询会比较慢 , 像 电表月年/整站月年 都已经改从 DayReport 缓存查询了 这里以后看吧 */
    /** 电站月报 */
    /** 电站年报 */
    @PostMapping("/year")
    @ApiOperation("年减排")
    @PreAuthorize("hasAuthority('/carbon/track')")
    public Result<Map<String, Object>> lisYearReport(@RequestBody CarbonRequest reportRequest) {
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        Map<String, Object> map = carbonService.getCarbon(reportRequest, EmsConstants.YEAR);
        return Result.success(map);
    }
}
