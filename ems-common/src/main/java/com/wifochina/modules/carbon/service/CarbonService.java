package com.wifochina.modules.carbon.service;

import com.wifochina.modules.carbon.VO.CarbonTotal;
import com.wifochina.modules.carbon.request.CarbonRequest;

import java.util.Map;

/**
 * operationProfitService
 *
 * @date 4/14/2022 11:24 AM
 * <AUTHOR>
 * @version 1.0
 */
public interface CarbonService {

    /**
     * 报表，
     *
     * @param type type代表类型。daily 日报；month 月报；
     * @param reportRequest: reportRequest
     * @return 总收益情况
     */
    Map<String, Object> getCarbon(CarbonRequest reportRequest, String type);

    default CarbonTotal getSiteByProjectId(String projectId) {
        return null;
    }
}
