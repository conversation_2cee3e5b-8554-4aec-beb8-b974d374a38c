package com.wifochina.modules.carbon.timer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.modules.carbon.VO.CarbonTotal;
import com.wifochina.modules.carbon.service.CarbonService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 9/23/2022 9:51 AM
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskCarbonTimer {

    private final ThreadPoolTaskExecutor asyncServiceExecutor;
    private final CarbonService carbonService;
    private final ProjectService projectService;

    public static Map<String, CarbonTotal> carbonTotalMap = new HashMap<>();

    @EventListener(ApplicationReadyEvent.class)
    void init() {
        // 项目启动的时候就立即执行一次,后续按照每5分钟执行一次, 保证项目启动5分钟job执行前,缓存数据已经存在
        asyncServiceExecutor.submit(this::cacheCarbon);
    }

    /** 每5分钟执行一次 缓存 碳减排 */
    @Scheduled(cron = "0 0/5 * * * ?")
    @Async("asyncServiceExecutor")
    public void cacheCarbon() {
        List<ProjectEntity> list =
                projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false));
        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                continue;
            }
            String projectId = projectEntity.getId();
            // 只会有 云版本才会执行
            // 缓存 碳减排量
            CarbonTotal carbonTotal = carbonService.getSiteByProjectId(projectId);
            carbonTotalMap.put(projectId, carbonTotal);
        }
    }
}
