package com.wifochina.modules.largescreen.VO;

import com.wifochina.modules.diagram.VO.ValueVO;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * Created on 2023/10/8 15:31. 大屏 功率曲线 Vo
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class LargeScreenRateVo {

    /** chargeOneRate 充放电功率 曲线 */
    private Map<String, List<ValueVO>> chargeOneRate;

    /** 负载功率 曲线 */
    private Map<String, List<ValueVO>> loadRate;

    /** 电网功率 曲线 */
    private Map<String, List<ValueVO>> gridRate;

    private List<ValueVO> pvRate;
    private List<ValueVO> wasterRate;
    private List<ValueVO> windRate;

    private List<ValueVO> dieselRate;
}
