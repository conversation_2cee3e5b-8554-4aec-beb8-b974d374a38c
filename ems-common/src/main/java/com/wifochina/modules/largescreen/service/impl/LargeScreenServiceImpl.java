package com.wifochina.modules.largescreen.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndGroupId;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.NewDiagramService;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.largescreen.VO.LargeScreenRateVo;
import com.wifochina.modules.largescreen.service.LargeScreenService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectScreenService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.project.vo.SiteVO;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Created on 2023/10/8 15:37.
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LargeScreenServiceImpl implements LargeScreenService {

    private final NewDiagramService newDiagramService;
    private final ProjectService projectService;
    private final GroupService groupService;
    private final StrategyService strategyService;
    private final DeviceService deviceService;
    private final InfluxClientService influxClientService;
    private final ProjectScreenService projectScreenService;
    private final InfluxClientService influxClient;

    /**
     * 会根据系统分组的 是否接入 pv 电站 ems 负载 等来判断
     *
     * @param requestWithDeviceIdAndGroupId : requestWithDeviceIdAndGroupId
     * @return : LargeScreenRateVo
     */
    @Override
    public LargeScreenRateVo getRateForLargeScreen(
            RequestWithDeviceIdAndGroupId requestWithDeviceIdAndGroupId) {
        GroupEntity groupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                                .eq(GroupEntity::getWhetherSystem, true));
        LargeScreenRateVo result = new LargeScreenRateVo();
        if (groupEntity == null) {
            return result;
        }

        RequestWithDeviceId requestWithDeviceId = new RequestWithDeviceId();
        BeanUtils.copyProperties(requestWithDeviceIdAndGroupId, requestWithDeviceId);
        // 这里..为什么可以 因为 requestWithDeviceId 的 period是 Integer类型 和
        // requestWithDeviceIdAndGroupId
        // 的类型一致 .....
        requestWithDeviceId.setProjectId(WebUtils.projectId.get());

        RequestWithGroupId requestWithGroupId = new RequestWithGroupId();
        BeanUtils.copyProperties(requestWithDeviceIdAndGroupId, requestWithGroupId);
        // ma de integer long period copy不过去! 早晚给换掉 换成 mapstruct
        requestWithGroupId.setItemId(requestWithDeviceIdAndGroupId.getDeviceId());
        requestWithGroupId.setPeriod(Long.valueOf(requestWithDeviceIdAndGroupId.getPeriod()));

        // 需要根据 系统分组是否开启了开关去 控制查询显示
        String activeField = MeterFieldEnum.AC_ACTIVE_POWER.field();
        String reactiveField = MeterFieldEnum.AC_REACTIVE_POWER.field();
        // 优先async 处理一下 电表的相关曲线查询
        Map<Integer, CompletableFuture<List<ValueVO>>> allFutureMaps = new HashMap<>();
        Set.of(
                        MeterTypeEnum.WASTER.meterType(),
                        MeterTypeEnum.WIND.meterType(),
                        MeterTypeEnum.DIESEL.meterType(),
                        MeterTypeEnum.PV.meterType())
                .forEach(
                        meterType -> {
                            CompletableFuture<List<ValueVO>> completableFuture =
                                    CompletableFuture.supplyAsync(
                                            () -> {
                                                WebUtils.projectId.set(
                                                        requestWithDeviceId.getProjectId());
                                                return newDiagramService.getMeterRate(
                                                        requestWithGroupId,
                                                        activeField,
                                                        meterType.toString());
                                            });
                            // add future
                            allFutureMaps.put(meterType, completableFuture);
                        });
        if (groupEntity.getEnableEms()) {
            // 1.ems_ac_active_power ems的有功功率 曲线查询
            Map<String, List<ValueVO>> chargeRateMap =
                    newDiagramService.getDeviceAggregationRate(
                            requestWithDeviceId,
                            influxClientService.getBucketMean(),
                            List.of(
                                    EmsFieldEnum.EMS_AC_ACTIVE_POWER.field(),
                                    EmsFieldEnum.EMS_AC_REACTIVE_POWER.field()));
            result.setChargeOneRate(chargeRateMap);
        }
        if (groupEntity.getEnableLoad()) {
            // 2.负载功率 曲线查询
            Map<String, List<ValueVO>> loadRateMap =
                    newDiagramService.getLoadRate(requestWithGroupId);
            result.setLoadRate(loadRateMap);
        }
        if (groupEntity.getEnableElectricGrid()) {
            // 3.电网功率
            Map<String, List<ValueVO>> gridRateMap = new HashMap<>();
            List<ValueVO> activeList =
                    newDiagramService.getMeterRate(
                            requestWithGroupId,
                            activeField,
                            MeterTypeEnum.GRID.meterType().toString());

            List<ValueVO> reactiveList =
                    newDiagramService.getMeterRate(
                            requestWithGroupId,
                            reactiveField,
                            MeterTypeEnum.GRID.meterType().toString());
            gridRateMap.put(activeField, activeList);
            gridRateMap.put(reactiveField, reactiveList);
            result.setGridRate(gridRateMap);
        }
        // 处理一下 电表rate的结果  2025-03-24 16:25:27 optmized code use async search meter rates
        CompletableFuture<Void> allOf =
                CompletableFuture.allOf(allFutureMaps.values().toArray(new CompletableFuture[0]));
        allOf.join();
        allFutureMaps.forEach(
                (meterType, future) -> {
                    try {
                        List<ValueVO> rates = future.get(1, TimeUnit.MINUTES);
                        MeterTypeEnum meterTypeEnum = MeterTypeEnum.getMeterType(meterType);
                        switch (meterTypeEnum) {
                            case PV:
                                result.setPvRate(rates);
                                break;
                            case WASTER:
                                result.setWasterRate(rates);
                                break;

                            case WIND:
                                result.setWindRate(rates);
                                break;
                            case DIESEL:
                                result.setDieselRate(rates);
                                break;
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
        // 特殊处理一下pv 要把pv 和 dcdc的集成一下
        List<ValueVO> pvRateMap = result.getPvRate();
        resolvePvIntegrationDcdc(requestWithGroupId, pvRateMap, result);

        //        List<ValueVO> wasterRateMap;
        //        if (Boolean.TRUE.equals(groupEntity.getWasterEarningsController())) {
        //            // 4. 光伏功率曲线
        //            wasterRateMap =
        //                    newDiagramService.getMeterRate(
        //                            requestWithGroupId,
        //                            activeField,
        //                            MeterTypeEnum.WASTER.meterType().toString());
        //            // 1.4.2 added waster rate
        //            result.setWasterRate(wasterRateMap);
        //        }
        //        List<ValueVO> windRateMap;
        //        if (Boolean.TRUE.equals(groupEntity.getWindEarningsController())) {
        //            //  wind功率曲线
        //            windRateMap =
        //                    newDiagramService.getMeterRate(
        //                            requestWithGroupId,
        //                            activeField,
        //                            MeterTypeEnum.WIND.meterType().toString());
        //            // 1.4.2 added wind rate
        //            result.setWindRate(windRateMap);
        //        }
        //        List<ValueVO> pvRateMap = new ArrayList<>();
        //        if (groupEntity.getPhotovoltaicController()) {
        //            // 4. 光伏功率曲线
        //            pvRateMap =
        //                    newDiagramService.getMeterRate(
        //                            requestWithGroupId,
        //                            activeField,
        //                            MeterTypeEnum.PV.meterType().toString());
        //        }

        return result;
    }

    private void resolvePvIntegrationDcdc(
            RequestWithGroupId requestWithGroupId,
            List<ValueVO> pvRateMap,
            LargeScreenRateVo result) {
        List<DeviceEntity> deviceEntities =
                deviceService.list(
                        Wrappers.lambdaQuery(DeviceEntity.class)
                                .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                                .eq(DeviceEntity::getUnreal, false));

        boolean hasDcdc = deviceEntities.stream().anyMatch(DeviceEntity::getHasDcdc);
        List<ValueVO> valueDcdcList = new ArrayList<>();
        if (hasDcdc) {
            requestWithGroupId.setItemId(EmsConstants.ALL);
            requestWithGroupId.setGroupId(EmsConstants.ALL);
            String field = EmsFieldEnum.DCDC_METER_POWER.field();
            valueDcdcList =
                    newDiagramService
                            .getGroupDeviceAggregationRate(
                                    requestWithGroupId,
                                    influxClient.getBucketMean(),
                                    List.of(field))
                            .get(field);
        }
        // 2024-01-18 15:33:45 add dcdc 的 把dcdc的光伏添加到光伏的曲线中 按照时间点 相加
        if (pvRateMap == null || pvRateMap.isEmpty()) {
            if (valueDcdcList != null && !valueDcdcList.isEmpty()) {
                result.setPvRate(valueDcdcList);
            }
        }
        if (pvRateMap != null && !pvRateMap.isEmpty()) {
            if (CollectionUtil.isEmpty(valueDcdcList)) {
                result.setPvRate(pvRateMap);
            } else {
                for (ValueVO valueVO : pvRateMap) {
                    for (ValueVO dcdcValueVo : valueDcdcList) {
                        if (valueVO.getTime().equals(dcdcValueVo.getTime())) {
                            valueVO.setValue(valueVO.getValue() + dcdcValueVo.getValue());
                        }
                    }
                }
                result.setPvRate(pvRateMap);
            }
        }
    }

    /**
     * 获取 轮播的 充放电量等数据 直接使用 Site 直接调用 projectScreenService.getSite 获取数据
     *
     * @see ProjectScreenService#getSite(Long)
     */
    @Override
    public SiteVO getCarouselQuantity() {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        return projectScreenService.getSite(todayZero);
    }

    @Override
    public Map<Integer, StrategyEntity> getStrategyByGroupId(String groupId) {
        Map<Integer, StrategyEntity> map = new HashMap<>(2);
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        LocalDate currentDate =
                LocalDate.now(MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
        // 带时区的 localTime
        LocalTime currentTime =
                LocalTime.now(MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
        // 获取今天是星期几
        DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
        Optional.ofNullable(
                        groupService.getOne(
                                Wrappers.lambdaQuery(GroupEntity.class)
                                        .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                                        .eq(GroupEntity::getId, groupId)))
                .ifPresent(
                        groupEntity -> {
                            Boolean externalController = groupEntity.getExternalController();
                            if (externalController) {
                                // 外部控制
                                log.debug("当前系统 projectId: {} 运行策略由外部控制", WebUtils.projectId.get());
                                map.put(0, null);
                                map.put(dayOfWeek.getValue(), null);
                                return;
                            }
                            // 系统的 分组策略
                            StrategyEntity sysStrategyEntity =
                                    strategyService.getOuterStrategyByGroupId(groupEntity.getId());
                            // 当前日期 当前时间的 策略 系统分组策略下面的
                            StrategyEntity timeStrategyEntity =
                                    strategyService.getTimeRangeStrategyByGroupId(
                                            groupEntity.getId(), dayOfWeek, currentTime);
                            map.put(0, sysStrategyEntity);
                            map.put(dayOfWeek.getValue(), timeStrategyEntity);
                        });
        return map;
    }

    /**
     * 获取pv 光伏功率曲线
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : 光伏功率曲线
     * @see com.wifochina.modules.diagram.controller.DiagramController#getPvRate(RateRequest)
     */
    // TODO refactor remove code

    // private List<ValueVO> getPvRate(RequestWithGroupId requestWithGroupId) {
    // String field = "ac_active_power";
    // return diagramService.getMeterRate(requestWithGroupId, field, "1");
    // }

    /**
     * 获取 电网功率曲线
     *
     * @param requestWithGroupId : requestWithGroupId
     * @return : 电网功率曲线
     * @see com.wifochina.modules.diagram.controller.DiagramController#getGridRate(RateRequest)
     */
    // TODO refactor remove code
    // private Map<String, List<ValueVO>> getGridRate(RequestWithGroupId
    // requestWithGroupId) {
    // Map<String, List<ValueVO>> map = new HashMap<>(2);
    // String activeField = "ac_active_power";
    // String reactiveField = "ac_reactive_power";
    // List<ValueVO> activeList =
    // diagramService.getMeterRate(requestWithGroupId, activeField, "2");
    // List<ValueVO> reactiveList =
    // diagramService.getMeterRate(requestWithGroupId, reactiveField, "2");
    // map.put(activeField, activeList);
    // map.put(reactiveField, reactiveList);
    // return map;
    // }

    /**
     * 获取负载功率 曲线
     *
     * @param requestWithGroupId: requestWithGroupId
     * @return : 负载功率 曲线
     * @see com.wifochina.modules.diagram.controller.DiagramController#getLoadRate(RateRequest)
     */
    // TODO refactor remove code
    // private Map<String, List<ValueVO>> getLoadRate(RequestWithGroupId
    // requestWithGroupId) {
    // // 负载 结果
    // Map<String, List<ValueVO>> map = null;
    // map = diagramService.getLoadRate(requestWithGroupId);
    // return map;
    // }

    /**
     * 获取充放电功率 曲线 注意 这个是查询所有设备的 不是 单个设备分开的 充放电功率
     *
     * @param requestWithDeviceId : requestWithDeviceId
     * @return : 充放电功率 曲线
     * @see
     *     com.wifochina.modules.diagram.controller.DiagramController#getChargeOneRate(RequestWithDeviceId)
     */
    // TODO refactor remove code
    // private Map<String, List<ValueVO>> getChargeOneRate(RequestWithDeviceId
    // requestWithDeviceId) {
    // List<String> fields = new ArrayList<>(2);
    // // 有功功率 无功功率
    // fields.add("ems_ac_active_power");
    // fields.add("ems_ac_reactive_power");
    // return diagramService.getRateForLargeScreen(
    // influxClientService.getBucketMean(),
    // requestWithDeviceId,
    // fields,
    // influxClientService.getEmsTable(WebUtils.projectId.get()));
    // }
}
