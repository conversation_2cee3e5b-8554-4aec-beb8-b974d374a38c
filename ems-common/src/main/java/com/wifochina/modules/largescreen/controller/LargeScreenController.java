package com.wifochina.modules.largescreen.controller;

import cn.hutool.core.collection.CollectionUtil;

import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.TimePointEnum;
import com.wifochina.common.influx.FluxAdapter;
import com.wifochina.common.page.Result;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndGroupId;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.request.go.GroupGoRequest;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupDeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.heatmap.request.HeatMapRequestWithGroupId;
import com.wifochina.modules.heatmap.service.HeatMapService;
import com.wifochina.modules.heatmap.service.NewHeatMapService;
import com.wifochina.modules.homepage.service.HomePageService;
import com.wifochina.modules.income.NewOperationProfitService;
import com.wifochina.modules.income.timer.TaskOperationTimer;
import com.wifochina.modules.income.vo.OperationProfitVo;
import com.wifochina.modules.largescreen.LargeScreenServiceKt;
import com.wifochina.modules.largescreen.VO.LargeScreenRateVo;
import com.wifochina.modules.largescreen.request.LargeScreenGroupStrategyRequest;
import com.wifochina.modules.largescreen.request.LargeScreenPcsStatusRequest;
import com.wifochina.modules.largescreen.service.LargeScreenService;
import com.wifochina.modules.monitor.service.PcsMonitorService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectDailyService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.project.vo.BasicVO;
import com.wifochina.modules.project.vo.SiteVO;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Created on 2023/10/7 19:14.
 *
 * <AUTHOR>
 */
@Api(tags = "27-大屏接口")
@RestController
@RequestMapping("/largeScreen")
@RequiredArgsConstructor
@Slf4j
public class LargeScreenController {

    private final HomePageService homePageService;
    private final HeatMapService heatMapService;
    private final NewHeatMapService newHeatMapService;
    private final LargeScreenService largeScreenService;
    private final PcsMonitorService pcsMonitorService;
    private final NewOperationProfitService newOperationProfitService;
    private final StrategyService strategyService;
    private final TaskOperationTimer taskOperationTimer;
    private final DeviceService deviceService;
    private final AmmeterService ammeterService;
    private final ProjectDailyService projectDailyService;
    private final ProjectService projectService;
    private final GroupDeviceService groupDeviceService;
    private final GroupService groupService;
    private final InfluxClientService influxClientService;

    private final LargeScreenServiceKt largeScreenServiceKt;

    /** 电站配置 page */
    @PostMapping("/basicInfoForLargeScreen")
    @ApiOperation("电站配置基本情况")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<BasicVO> basicInfoForLargeScreen() {
        BasicVO basicVo = homePageService.getBasicInfo();
        return Result.success(basicVo);
    }

    /** 集装箱温度 */
    @PostMapping("/temperatureForLargeScreen")
    @ApiOperation("集装箱温度")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<Map<String, Map<String, Map<String, Map<String, Double>>>>> getBatteryTemperature(
            @RequestParam @ApiParam(required = true, value = "设备id，all代表所有") String deviceId,
            @RequestParam(required = false) @ApiParam(value = "时间戳") Long time) {
        Map<String, Map<String, Map<String, Map<String, Double>>>> map =
                heatMapService.getBatteryTemperature(deviceId, time);
        return Result.success(map);
    }

    /** 查询策略 */
    @PostMapping("/strategyForLargeScreen")
    @ApiOperation("控制策略")
    @PreAuthorize("hasAuthority('/largeScreen')")
    // TODO 这里要修改
    public Result<Map<String, StrategyEntity>> getStrategyByGroupId(
            @RequestBody LargeScreenGroupStrategyRequest largeScreenGroupStrategyRequest) {
        //        Map<Integer, StrategyEntity> map =
        //                largeScreenService.getStrategyByGroupId(
        //                        largeScreenGroupStrategyRequest.getGroupId());
        Map<String, StrategyEntity> map =
                largeScreenServiceKt.getStrategyByGroupId(
                        largeScreenGroupStrategyRequest.getGroupId());
        return Result.success(map);
    }

    /** 查询策略 */
    @PostMapping("/getRunStrategy")
    @ApiOperation("海外控制策略")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<GroupGoRequest> getRunStrategyByGroupId(
            @RequestBody LargeScreenGroupStrategyRequest largeScreenGroupStrategyRequest) {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        return Result.success(
                strategyService.getGroupGoRequest(
                        projectEntity, largeScreenGroupStrategyRequest.getGroupId()));
    }

    /** 充放电功率单线 */
    @PostMapping("/rateForLargeScreen")
    @ApiOperation("大屏功率曲线")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<LargeScreenRateVo> getRateForLargeScreen(
            @RequestBody RequestWithDeviceIdAndGroupId requestWithDeviceIdAndGroupId) {
        LargeScreenRateVo rateForLargeScreen =
                largeScreenService.getRateForLargeScreen(requestWithDeviceIdAndGroupId);
        return Result.success(rateForLargeScreen);
    }

    /**
     * 查询设备内部所有 pcs 的 电流 电压数据
     *
     * @return :查询设备内部所有 pcs 的 电流 电压数据
     */
    @PostMapping("/pcsMonitorForLargeScreen")
    @ApiOperation("大屏pcs监控")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<Map<Integer, List<PointDataEntity>>> getPcsMonitor(
            @RequestBody LargeScreenPcsStatusRequest request) {
        // 只查询 直流电流current 和 直流电压voltage
        List<String> dcPointColumns = Arrays.asList("pcs_{i}_dc_current", "pcs_{i}_dc_voltage");
        return Result.success(pcsMonitorService.getPcsAssignPointRunData(request, dcPointColumns));
    }

    /** 储能热力图 充电功率 */
    @PostMapping("/chargeForLargeScreen")
    @ApiOperation("储能热力图充电功率")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<Map<Long, Double>> charge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        if (!StringUtils.hasLength(heatMapRequestWithGroupId.getType())) {
            Result.failure(ErrorResultCode.ILLEGAL_ARGUMENT_TYPE.value());
        }
        Map<Long, Double> emsInMap =
                newHeatMapService.getDeviceHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ems_history_input_energy");
        return Result.success(emsInMap);
    }

    /** 储能热力图 放电功率 */
    @PostMapping("/dischargeForLargeScreen")
    @ApiOperation("储能热力图放电功率")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<Map<Long, Double>> discharge(
            @RequestBody HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        if (!StringUtils.hasLength(heatMapRequestWithGroupId.getType())) {
            Result.failure(ErrorResultCode.ILLEGAL_ARGUMENT_TYPE.value());
        }
        Map<Long, Double> emsOutMap =
                newHeatMapService.getDeviceHeatMap(
                        WebUtils.projectId.get(),
                        heatMapRequestWithGroupId,
                        "ems_history_output_energy");
        return Result.success(emsOutMap);
    }

    /**
     * today收益查询 month当月收益查询 total收益查询 //timePoint : today,month,total
     *
     * @see TimePointEnum#values()
     */
    @PostMapping("/profitForLargeScreen/{timePoint}")
    @ApiOperation("今日充电量+收益查询, timePoint可选{today,yesterday,month,total} 不传默认today")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<Map<String, Object>> getProfitVO(
            @PathVariable(value = "timePoint", required = false) String timePointKey)
            throws InterruptedException {
        String projectId = WebUtils.projectId.get();
        TimePointEnum timePointEnum;
        if (!StringUtils.hasLength(timePointKey)) {
            // 默认是查询 Today 当天的
            timePointEnum = TimePointEnum.TODAY;
        } else {
            timePointEnum =
                    Optional.ofNullable(EmsConstants.TIME_POINT_ENUM_MAP.get(timePointKey))
                            .orElse(TimePointEnum.TODAY);
        }
        List<DeviceEntity> incomeDevices =
                deviceService.findIncomeDevices(WebUtils.projectId.get());
        List<AmmeterEntity> incomeAmmeter =
                ammeterService.findIncomeAmmeter(WebUtils.projectId.get());
        Map<String, Object> benefitMap = new HashMap<>();
        if (CollectionUtil.isEmpty(incomeDevices) && CollectionUtil.isEmpty(incomeAmmeter)) {
            ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
            Long now = Instant.now().getEpochSecond();
            // 如果 没有开了收益的设备和收益的电表的情况下 去查询 所有的设备 对应的电量信息
            benefitMap =
                    getTotalEnergyNoProfit(
                            timePointEnum, projectEntity, benefitMap, projectId, now);
        } else {
            // 如果有开了收益的电表和设备的话 则按照以前的查询
            // 注意 这里的 todayWhetherFromCache = true 表示这个今天的也是直接从缓存中查询 不然大屏可能会卡
            benefitMap =
                    newOperationProfitService.getTotalOperationProfit(
                            true, WebUtils.projectId.get(), timePointEnum);
        }
        return Result.success(benefitMap);
    }

    /**
     * 查询 month today 和 total的 累计的 ems 设备的 冲 放电的电量 <br>
     * total 的查询方式是 从数据库里查询 最后一条数据的 ems_history_output_energy 和 ems_history_input_energy <br>
     * month 和 today 是 使用 projectDailyService.getDailyPower 查询的时间范围内的 difference 的sum
     *
     * <p>不涉及收益 只是去数据库实时查询
     *
     * @param timePointEnum
     * @param projectEntity
     * @param benefitMap
     * @param projectId
     * @param now
     * @return
     */
    private Map<String, Object> getTotalEnergyNoProfit(
            TimePointEnum timePointEnum,
            ProjectEntity projectEntity,
            Map<String, Object> benefitMap,
            String projectId,
            Long now) {
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectId);
        if (systemGroupEntity == null) {
            return Map.of();
        }
        List<String> sysDeviceIds =
                deviceService.getGroupDeviceNotUnrealIds(systemGroupEntity.getId());
        // 如果是月度查询 则查询范围
        if (timePointEnum.getValue().equals(TimePointEnum.MONTH.getValue())) {
            Long monthStart = MyTimeUtil.getCurrentMonthZeroTime(projectEntity.getTimezone());
            benefitMap = inOutEnergy(projectId, monthStart, now, sysDeviceIds, timePointEnum);
        }
        if (timePointEnum.getValue().equals(TimePointEnum.TOTAL.getValue())) {
            benefitMap = new HashMap<>();
            long preMonthZeroTime = MyTimeUtil.getPreMonthZeroTime(projectEntity.getTimezone());
            // 查询最后last 的数据
            // 总计的需要查询 每个设备的最后一个点 + sum
            ValueVO emsHistoryOutputEnergy =
                    getEmsHistoryInOutTotalEnergy(
                            projectId,
                            preMonthZeroTime,
                            now,
                            sysDeviceIds,
                            EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field());
            ValueVO emsHistoryInputEnergy =
                    getEmsHistoryInOutTotalEnergy(
                            projectId,
                            preMonthZeroTime,
                            now,
                            sysDeviceIds,
                            EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field());
            Double totalOutEnergy = emsHistoryOutputEnergy.getValue();
            Double totalInEnergy = emsHistoryInputEnergy.getValue();
            OperationProfitVo operationProfitVo = new OperationProfitVo();
            operationProfitVo.setTotalDischargeQuantity(
                    totalOutEnergy == null ? "0.0" : String.valueOf(totalOutEnergy));
            operationProfitVo.setTotalChargeQuantity(
                    totalInEnergy == null ? "0.0" : String.valueOf(totalInEnergy));
            benefitMap.put(timePointEnum.getValue(), operationProfitVo);
        }
        if (timePointEnum.getValue().equals(TimePointEnum.TODAY.getValue())) {
            Long start = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            // 简单的封装一下 支持 today 和 month的查询
            benefitMap = inOutEnergy(projectId, start, now, sysDeviceIds, timePointEnum);
        }
        return benefitMap;
    }

    private ValueVO getEmsHistoryInOutTotalEnergy(
            String projectId,
            long preMonthZeroTime,
            Long now,
            List<String> systemDeviceIds,
            String field) {
        return FluxAdapter.query(
                        FluxAdapter.builder()
                                .projectId(projectId)
                                .from(influxClientService.getBucketForever())
                                .range(preMonthZeroTime, now)
                                .measurement(
                                        influxClientService.getEmsTable(WebUtils.projectId.get()))
                                .fields(List.of(field))
                                .equipmentIds(systemDeviceIds)
                                .toFlux()
                                .aggregateWindow(
                                        1L, ChronoUnit.HOURS, EmsConstants.INFLUX_LAST_FUNC)
                                .withCreateEmpty(false)
                                .sort(List.of(EmsConstants.INFLUX_TIME), true)
                                .limit(1)
                                .groupBy(EmsConstants.INFLUX_TIME)
                                .sum(EmsConstants.INFLUX_VALUE)
                                .toString())
                .handleResult()
                // 新增的 只会有一条数据并且 是只取单个的value 数据
                .onlyOneValue();
    }

    /**
     * 简单封装一下 支持查询 电量的 冲 和 放
     *
     * @param projectId
     * @param start
     * @param now
     * @param sysDeviceIds
     * @param timePointEnum
     * @return
     */
    private Map<String, Object> inOutEnergy(
            String projectId,
            Long start,
            Long now,
            List<String> sysDeviceIds,
            TimePointEnum timePointEnum) {
        Map<String, Object> resultMap = new HashMap<>();
        CompletableFuture<Double> emsDailyOutEnergyFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(projectId);
                            return projectDailyService.getDailyPower(
                                    start,
                                    now,
                                    EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field(),
                                    null,
                                    sysDeviceIds);
                        });
        CompletableFuture<Double> emsDailyInEnergyFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            WebUtils.projectId.set(projectId);
                            return projectDailyService.getDailyPower(
                                    start,
                                    now,
                                    EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field(),
                                    null,
                                    sysDeviceIds);
                        });
        try {
            Double inEnergy = emsDailyInEnergyFuture.get(1, TimeUnit.MINUTES);
            Double outEnergy = emsDailyOutEnergyFuture.get(1, TimeUnit.MINUTES);
            OperationProfitVo operationProfitVo = new OperationProfitVo();
            operationProfitVo.setTotalDischargeQuantity(
                    outEnergy == null ? "0.0" : String.valueOf(outEnergy));
            operationProfitVo.setTotalChargeQuantity(
                    inEnergy == null ? "0.0" : String.valueOf(inEnergy));
            resultMap.put(timePointEnum.getValue(), operationProfitVo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return resultMap;
    }

    @PostMapping("/quantityForLargeScreen")
    @ApiOperation("轮播 查询总览电量(储能,负载,电网,光伏,风机,柴发,充电桩,燃气发电机..)")
    @PreAuthorize("hasAuthority('/largeScreen')")
    public Result<SiteVO> getCarouselQuantity() {
        // TODO 这里前端要根据 是否开启的开关 来显示 储能,负载,电网,光伏,风机,...
        SiteVO siteVO = largeScreenService.getCarouselQuantity();
        return Result.success(siteVO);
    }

    @PostMapping("/operation")
    @ApiOperation("手动执行收益")
    public Result<SiteVO> operation() {
        taskOperationTimer.cacheTodayProfit();
        return Result.success();
    }
}
