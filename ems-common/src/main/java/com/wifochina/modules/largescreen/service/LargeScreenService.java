package com.wifochina.modules.largescreen.service;

import java.util.Map;

import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndGroupId;
import com.wifochina.modules.largescreen.VO.LargeScreenRateVo;
import com.wifochina.modules.project.vo.SiteVO;
import com.wifochina.modules.strategy.entity.StrategyEntity;

/**
 * Created on 2023/10/8 15:28.
 *
 * <AUTHOR>
 */
public interface LargeScreenService {

    /**
     * 获取 大屏 曲线图 充放电功率、电网功率、负载功率、光伏功率曲线
     *
     * @param requestWithDeviceIdAndGroupId device and group id
     * @return LargeScreenRateVo
     */
    LargeScreenRateVo getRateForLargeScreen(
            RequestWithDeviceIdAndGroupId requestWithDeviceIdAndGroupId);

    /**
     * get Carousel Quantity
     *
     * @return SiteVO
     */
    SiteVO getCarouselQuantity();

    /**
     * get Strategy By GroupId
     *
     * @return Map<Integer, StrategyEntity>
     */
    Map<Integer, StrategyEntity> getStrategyByGroupId(String groupId);
}
