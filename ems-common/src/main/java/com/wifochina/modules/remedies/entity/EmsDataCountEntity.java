package com.wifochina.modules.remedies.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 数据状态
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Getter
@Setter
@TableName("t_ems_data_count")
@ApiModel(value = "EmsDataCountEntity对象", description = "数据状态")
public class EmsDataCountEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("状态, true 已确认， false未确认")
    private Boolean state;

    @ApiModelProperty("ems总数")
    private Integer emsCount;

    @ApiModelProperty("当天数据总数")
    private Integer dataCount;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        EmsDataCountEntity that = (EmsDataCountEntity) obj;
        return Objects.equals(id, that.id) && Objects.equals(projectId, that.projectId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, projectId);
    }
}
