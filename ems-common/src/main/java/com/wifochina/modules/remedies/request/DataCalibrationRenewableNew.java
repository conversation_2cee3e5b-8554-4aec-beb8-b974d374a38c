package com.wifochina.modules.remedies.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2025/2/10 10:31.
 *
 * <p>以后再改吧 懒得优化了
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DataCalibrationRenewableNew extends DataCalibrationCommon {

    /** 数据补值 可再生能源类型 */
    private String dcRenewableType;

    private DataCalibrationRenewableFullInternet dcFullInternetData;
    private DataCalibrationRenewableAgreement dcAgreementData;
    private DataCalibrationRenewableSelfPeriodPrice dcSelfPeriodPriceData;
}
