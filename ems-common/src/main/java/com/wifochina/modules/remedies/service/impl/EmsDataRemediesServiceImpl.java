package com.wifochina.modules.remedies.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.remedies.entity.EmsDataRemediesEntity;
import com.wifochina.modules.remedies.mapper.EmsDataRemediesMapper;
import com.wifochina.modules.remedies.request.SumRequest;
import com.wifochina.modules.remedies.service.EmsDataRemediesService;
import com.wifochina.modules.remedies.vo.EmsDataRemediesVo;

/**
 * <p>
 * 数据状态 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Service
public class EmsDataRemediesServiceImpl extends ServiceImpl<EmsDataRemediesMapper, EmsDataRemediesEntity>
    implements EmsDataRemediesService {
    public EmsDataRemediesVo sumEnergy(String projectId, Long start, Long end) {
        EmsDataRemediesVo emsDataRemediesVo = new EmsDataRemediesVo();
        QueryWrapper<EmsDataRemediesEntity> queryWrapper = new QueryWrapper<EmsDataRemediesEntity>();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.ge("time", start);
        queryWrapper.le("time", end);
        queryWrapper.select(
            "ifnull(sum(vally_history_output_energy),0) as vallyHistoryOutputEnergy, ifnull(sum(vally_history_input_energy),0) as vallyHistoryInputEnergy, ifnull(sum(flat_history_output_energy),0) as flatHistoryOutputEnergy,"
                + " ifnull(sum(flat_history_input_energy),0) as flatHistoryInputEnergy, ifnull(sum(peak_history_output_energy),0) as peakHistoryOutputEnergy, ifnull(sum(peak_history_input_energy),0) as peakHistoryInputEnergy,"
                + " ifnull(sum(tip_history_output_energy),0) as tipHistoryOutputEnergy, ifnull(sum(tip_history_input_energy),0) as tipHistoryInputEnergy, ifnull(sum(deep_vally_history_output_energy),0) as deepVallyHistoryOutputEnergy,"
                + " ifnull(sum(deep_vally_history_input_energy),0) as deepVallyHistoryInputEnergy ");
        Map<String, Object> map = this.getMap(queryWrapper);
        Optional.ofNullable(map).ifPresent(m -> {
            emsDataRemediesVo.setPeakHistoryOutputEnergy((Double)m.get("peakHistoryOutputEnergy"));
            emsDataRemediesVo.setPeakHistoryInputEnergy((Double)m.get("peakHistoryInputEnergy"));
            emsDataRemediesVo.setVallyHistoryOutputEnergy((Double)m.get("vallyHistoryOutputEnergy"));
            emsDataRemediesVo.setVallyHistoryInputEnergy((Double)m.get("vallyHistoryInputEnergy"));
            emsDataRemediesVo.setFlatHistoryOutputEnergy((Double)m.get("flatHistoryOutputEnergy"));
            emsDataRemediesVo.setFlatHistoryInputEnergy((Double)m.get("flatHistoryInputEnergy"));
            emsDataRemediesVo.setTipHistoryOutputEnergy((Double)m.get("tipHistoryOutputEnergy"));
            emsDataRemediesVo.setTipHistoryInputEnergy((Double)m.get("tipHistoryInputEnergy"));
            emsDataRemediesVo.setDeepVallyHistoryOutputEnergy((Double)m.get("deepVallyHistoryOutputEnergy"));
            emsDataRemediesVo.setDeepVallyHistoryInputEnergy((Double)m.get("deepVallyHistoryInputEnergy"));
        });
        return emsDataRemediesVo;
    }

    @Override
    public List<EmsDataRemediesEntity> getRemediesSumByMonth(SumRequest sumRequest) {
        return this.baseMapper.getRemediesSumByMonth(sumRequest);
    }
}
