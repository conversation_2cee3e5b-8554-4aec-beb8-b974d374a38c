package com.wifochina.modules.remedies.service;

import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.remedies.request.DataCalibrationCommon;
import com.wifochina.modules.remedies.request.DataCalibrationEms;
import com.wifochina.modules.remedies.request.DataCalibrationRenewable;
import com.wifochina.modules.renewable.entity.RenewablePeriod;

import java.util.List;

/**
 * Created on 2024/6/20 10:59.
 *
 * <AUTHOR>
 */
public interface DataCalibrationService {

    /**
     * ems 的数据校准 接口 实现
     *
     * @param data : 数据校准 ems 的数据 data
     */
    void emsDataCalibration(String projectId, DataCalibrationEms data);

    /**
     * pv or wind 的数据校准 接口 实现
     *
     * @param data : 数据校准 pv wind 的数据 data
     */
    void renewableDataCalibration(String projectId, DataCalibrationRenewable data);

    /**
     * 回显接口 回显 指定时间的 pv or wind的 缓存数据
     *
     * @see com.wifochina.common.constants.CalculateTypeEnum
     * @param date : 指定的日期
     * @param type : CalculateTypeEnum...
     * @return : 尖峰平谷or动态的 list
     * @param <T> :尖峰平谷or动态的 实体
     */
    <T extends RenewablePeriod> List<T> getRenewableData(String date, String type);

    ElectricProfitVO getEmsDataCalibration(String projectId, DataCalibrationCommon common);
}
