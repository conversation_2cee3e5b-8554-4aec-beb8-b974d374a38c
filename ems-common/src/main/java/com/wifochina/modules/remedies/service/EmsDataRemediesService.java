package com.wifochina.modules.remedies.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.remedies.entity.EmsDataRemediesEntity;
import com.wifochina.modules.remedies.request.SumRequest;
import com.wifochina.modules.remedies.vo.EmsDataRemediesVo;

/**
 * <p>
 * 数据状态 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
public interface EmsDataRemediesService extends IService<EmsDataRemediesEntity> {
    EmsDataRemediesVo sumEnergy(String projectId, Long start, Long end);

    List<EmsDataRemediesEntity> getRemediesSumByMonth(SumRequest sumRequest);
}
