package com.wifochina.modules.remedies.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-08-11 4:21 PM
 */
@Data
public class EmsDataRemediesRequest {

    private Integer id;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("谷历史输出能量")
    private Double vallyHistoryOutputEnergy;

    @ApiModelProperty("谷历史输入能量")
    private Double vallyHistoryInputEnergy;

    @ApiModelProperty("平历史输出能量")
    private Double flatHistoryOutputEnergy;

    @ApiModelProperty("平历史输入能量")
    private Double flatHistoryInputEnergy;

    @ApiModelProperty("峰历史输出能量")
    private Double peakHistoryOutputEnergy;

    @ApiModelProperty("峰历史输入能量")
    private Double peakHistoryInputEnergy;

    @ApiModelProperty("尖历史输出能量")
    private Double tipHistoryOutputEnergy;

    @ApiModelProperty("尖历史输入能量")
    private Double tipHistoryInputEnergy;

    @ApiModelProperty("深谷历史输出能量")
    private Double deepVallyHistoryOutputEnergy;

    @ApiModelProperty("深谷历史输入能量")
    private Double deepVallyHistoryInputEnergy;
}
