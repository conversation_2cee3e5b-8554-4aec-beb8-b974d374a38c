package com.wifochina.modules.remedies.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.electric.ElectricService;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.IIncomeMemoryCacheServiceKt;
import com.wifochina.modules.income.cache.memorycache.IIncomeMemoryCacheService;
import com.wifochina.modules.income.query.pvwind.RenewableIncomeQueryService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.request.DataCalibrationCommon;
import com.wifochina.modules.remedies.request.DataCalibrationEms;
import com.wifochina.modules.remedies.request.DataCalibrationRenewable;
import com.wifochina.modules.remedies.request.EmsDataPageRequest;
import com.wifochina.modules.remedies.service.DataCalibrationService;
import com.wifochina.modules.renewable.entity.RenewablePeriod;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;
import com.wifochina.modules.user.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.AllArgsConstructor;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created on 2024/6/20 10:41.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataCalibration")
@Api(tags = "333-数据校准")
@AllArgsConstructor
public class DataCalibrationController {

    private final DataCalibrationService dataCalibrationService;

    private final ElectricService electricService;
    private final RenewableIncomeQueryService renewableIncomeQueryService;
    private final UserService userService;
    private final GroupService groupService;
    private final IIncomeMemoryCacheService incomeMemoryCacheService;
    private final IIncomeMemoryCacheServiceKt incomeMemoryCacheServiceKt;
    private final ProjectService projectService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @PostMapping("/ems/page")
    @ApiOperation("获取ems电的数据校准列表")
    @PreAuthorize("hasAuthority('/remedies/query')")
    public Result<IPage<ElectricEntity>> emsPageQuery(
            @RequestBody EmsDataPageRequest emsDataPageRequest) {
        Page<ElectricEntity> page =
                Page.of(emsDataPageRequest.getPageNum(), emsDataPageRequest.getPageSize());
        IPage<ElectricEntity> list =
                electricService.page(
                        page,
                        Wrappers.lambdaQuery(ElectricEntity.class)
                                .eq(ElectricEntity::getProjectId, WebUtils.projectId.get())
                                .eq(ElectricEntity::getCalibrationType, "3")
                                .ge(ElectricEntity::getTime, emsDataPageRequest.getStart())
                                .le(ElectricEntity::getTime, emsDataPageRequest.getEnd())
                                .orderByDesc(ElectricEntity::getTime));
        userService.fillUserName(list.getRecords());
        groupService.fillGroupName(WebUtils.projectId.get(), list.getRecords());
        return Result.success(list);
    }

    /**
     * 经过前端要求 这几个page 页面是拆开了接口 按说可以统一成一个 接口根据 type 去区分 不知为什么需要拆开
     *
     * @param emsDataPageRequest : emsDataPageRequest
     * @return : Result
     */
    @PostMapping("/pv/page")
    @ApiOperation("获取pv的数据校准列表")
    @PreAuthorize("hasAuthority('/remedies/query')")
    public Result<IPage<RenewableProfitEntity>> pvPageQuery(
            @RequestBody EmsDataPageRequest emsDataPageRequest) {
        Page<RenewableProfitEntity> page =
                Page.of(emsDataPageRequest.getPageNum(), emsDataPageRequest.getPageSize());
        IPage<RenewableProfitEntity> list =
                renewableIncomeQueryService.page(
                        page,
                        Wrappers.lambdaQuery(RenewableProfitEntity.class)
                                .eq(RenewableProfitEntity::getProjectId, WebUtils.projectId.get())
                                .eq(
                                        RenewableProfitEntity::getRenewableType,
                                        CalculateTypeEnum.PV.name())
                                .eq(RenewableProfitEntity::isDataCalibrationFlag, true)
                                .ge(RenewableProfitEntity::getTime, emsDataPageRequest.getStart())
                                .le(RenewableProfitEntity::getTime, emsDataPageRequest.getEnd())
                                .orderByDesc(RenewableProfitEntity::getTime));
        userService.fillUserName(list.getRecords());
        return Result.success(list);
    }

    @PostMapping("/wind/page")
    @ApiOperation("获取wind的数据校准列表")
    @PreAuthorize("hasAuthority('/remedies/query')")
    public Result<IPage<RenewableProfitEntity>> windPageQuery(
            @RequestBody EmsDataPageRequest emsDataPageRequest) {
        Page<RenewableProfitEntity> page =
                Page.of(emsDataPageRequest.getPageNum(), emsDataPageRequest.getPageSize());
        IPage<RenewableProfitEntity> list =
                renewableIncomeQueryService.page(
                        page,
                        Wrappers.lambdaQuery(RenewableProfitEntity.class)
                                .eq(RenewableProfitEntity::getProjectId, WebUtils.projectId.get())
                                .eq(
                                        RenewableProfitEntity::getRenewableType,
                                        CalculateTypeEnum.WIND.name())
                                .eq(RenewableProfitEntity::isDataCalibrationFlag, true)
                                .ge(RenewableProfitEntity::getTime, emsDataPageRequest.getStart())
                                .le(RenewableProfitEntity::getTime, emsDataPageRequest.getEnd())
                                .orderByDesc(RenewableProfitEntity::getTime));
        userService.fillUserName(list.getRecords());
        return Result.success(list);
    }

    @PostMapping("/waster/page")
    @ApiOperation("获取waster的数据校准列表")
    @PreAuthorize("hasAuthority('/remedies/query')")
    public Result<IPage<RenewableProfitEntity>> wasterPageQuery(
            @RequestBody EmsDataPageRequest emsDataPageRequest) {
        Page<RenewableProfitEntity> page =
                Page.of(emsDataPageRequest.getPageNum(), emsDataPageRequest.getPageSize());
        IPage<RenewableProfitEntity> list =
                renewableIncomeQueryService.page(
                        page,
                        Wrappers.lambdaQuery(RenewableProfitEntity.class)
                                .eq(RenewableProfitEntity::getProjectId, WebUtils.projectId.get())
                                .eq(
                                        RenewableProfitEntity::getRenewableType,
                                        CalculateTypeEnum.WASTER.name())
                                .eq(RenewableProfitEntity::isDataCalibrationFlag, true)
                                .ge(RenewableProfitEntity::getTime, emsDataPageRequest.getStart())
                                .le(RenewableProfitEntity::getTime, emsDataPageRequest.getEnd())
                                .orderByDesc(RenewableProfitEntity::getTime));
        userService.fillUserName(list.getRecords());
        return Result.success(list);
    }

    @PostMapping("/addEms")
    @ApiOperation("电池收益数据校准")
    @PreAuthorize("hasAuthority('/remedies/add')")
    @Log(module = "DATA_CALIBRATION", methods = "EMS", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addEms(@RequestBody DataCalibrationEms ems) {
        dataCalibrationService.emsDataCalibration(WebUtils.projectId.get(), ems);
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        threadPoolTaskExecutor.submit(
                () ->
                        incomeMemoryCacheService.updateElectricProfitVO(
                                projectEntity.getId(),
                                projectEntity.getProjectName(),
                                projectEntity.getCreateTime(),
                                projectEntity.getTimezone()));
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("REMEDIES")
        //                        .method("REMEDIES_DATA_ADD")
        //                        .object(ems)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.EMS_REMEDIES_TRACE_FORMAT,
        // ems.getGroupId()))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/getEmsData")
    @ApiOperation("获取电池收益校准数据")
    public Result<ElectricProfitVO> getEmsData(@RequestBody DataCalibrationCommon common) {
        return Result.success(
                dataCalibrationService.getEmsDataCalibration(WebUtils.projectId.get(), common));
    }

    @PostMapping("/getPvOrWindData")
    @ApiOperation("pvwind数据回显")
    public Result<List<RenewablePeriod>> getPvOrWindData(
            @RequestBody DataCalibrationCommon common) {
        // 把对应日期的缓存数据 返回给前端
        return Result.success(
                dataCalibrationService.getRenewableData(common.getDate(), common.getType()));
    }

    /** // TODO 前端会根据不同的model 提交不同的电量值 */
    @PostMapping("/addPv")
    @ApiOperation("数据校准pv")
    @PreAuthorize("hasAuthority('/remedies/add')")
    @Log(module = "DATA_CALIBRATION", methods = "PV", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addPv(@RequestBody DataCalibrationRenewable pvWind) {
        pvWind.setRenewableType(CalculateTypeEnum.PV.name());
        dataCalibrationService.renewableDataCalibration(WebUtils.projectId.get(), pvWind);
        // 2024-08-15 16:28:17 add 在补值后 直接对pv和wind进行缓存的更新
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        long startDate =
                MyTimeUtil.getOneDayZeroTime(
                        projectEntity.getCreateTime(), projectEntity.getTimezone());
        threadPoolTaskExecutor.execute(
                () -> {
                    incomeMemoryCacheServiceKt.updateRenewableIncomeMemoryCache(
                            projectEntity, CalculateTypeEnum.PV);
                });
        return Result.success();
    }

    /** // TODO 前端会根据不同的model 提交不同的电量值 */
    @PostMapping("/addWind")
    @ApiOperation("数据校准wind")
    @PreAuthorize("hasAuthority('/remedies/add')")
    @Log(module = "DATA_CALIBRATION", methods = "WIND", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addWind(@RequestBody DataCalibrationRenewable pvWind) {
        pvWind.setRenewableType(CalculateTypeEnum.WIND.name());
        dataCalibrationService.renewableDataCalibration(WebUtils.projectId.get(), pvWind);
        // 2024-08-15 16:28:17 add 在补值后 直接对pv和wind进行缓存的更新
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        long startDate =
                MyTimeUtil.getOneDayZeroTime(
                        projectEntity.getCreateTime(), projectEntity.getTimezone());
        threadPoolTaskExecutor.execute(
                () -> {
                    incomeMemoryCacheServiceKt.updateRenewableIncomeMemoryCache(
                            projectEntity, CalculateTypeEnum.WIND);
                });
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("REMEDIES")
        //                        .method("REMEDIES_DATA_ADD")
        //                        .object(pvWind)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.WIND_REMEDIES_TRACE_FORMAT,
        // pvWind.getGroupId()))
        //                        .build());
        return Result.success();
    }

    /** // TODO 前端会根据不同的model 提交不同的电量值 */
    @PostMapping("/addWaster")
    @ApiOperation("数据校准Waster余热发电")
    @PreAuthorize("hasAuthority('/remedies/add')")
    @Log(module = "DATA_CALIBRATION", methods = "WASTER", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addWaster(@RequestBody DataCalibrationRenewable pvWind) {
        pvWind.setRenewableType(CalculateTypeEnum.WASTER.name());
        dataCalibrationService.renewableDataCalibration(WebUtils.projectId.get(), pvWind);
        // 2024-08-15 16:28:17 add 在补值后 直接对pv和wind进行缓存的更新
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        threadPoolTaskExecutor.execute(
                () -> {
                    incomeMemoryCacheServiceKt.updateRenewableIncomeMemoryCache(
                            projectEntity, CalculateTypeEnum.WASTER);
                });
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("REMEDIES")
        //                        .method("REMEDIES_DATA_ADD")
        //                        .object(pvWind)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.WASTER_REMEDIES_TRACE_FORMAT,
        //                                        pvWind.getGroupId()))
        //                        .build());
        return Result.success();
    }
}
