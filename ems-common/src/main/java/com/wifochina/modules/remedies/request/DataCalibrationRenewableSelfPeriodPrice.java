package com.wifochina.modules.remedies.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * Created on 2025/2/10 10:39.
 *
 * <AUTHOR>
 */
@Data
public class DataCalibrationRenewableSelfPeriodPrice {

    @ApiModelProperty("尖自用电量 尖峰平谷模式使用")
    private Double tipSelfEnergy = 0.0;

    @ApiModelProperty("峰自用电量 尖峰平谷模式使用")
    private Double peakSelfEnergy = 0.0;

    @ApiModelProperty("平自用电量 尖峰平谷模式使用")
    private Double flatSelfEnergy = 0.0;

    @ApiModelProperty("谷自用电量 尖峰平谷模式使用  ")
    private Double vallySelfEnergy = 0.0;

    @ApiModelProperty("深自用电量 尖峰平谷模式使用")
    private Double deepVallySelfEnergy = 0.0;

    @ApiModelProperty("馈网 上网电量 尖峰平谷模式和协议模式使用")
    private Double onlineEnergy = 0.0;
}
