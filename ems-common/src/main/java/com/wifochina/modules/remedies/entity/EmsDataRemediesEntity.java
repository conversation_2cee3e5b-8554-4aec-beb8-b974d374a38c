package com.wifochina.modules.remedies.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 数据状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Getter
@Setter
@TableName("t_ems_data_remedies")
@ApiModel(value = "EmsDataRemediesEntity对象", description = "数据状态")
public class EmsDataRemediesEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("年度")
    private Integer year;

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("日")
    private Integer day;

    @ApiModelProperty("谷历史输出能量")
    private Double vallyHistoryOutputEnergy;

    @ApiModelProperty("谷历史输入能量")
    private Double vallyHistoryInputEnergy;

    @ApiModelProperty("平历史输出能量")
    private Double flatHistoryOutputEnergy;

    @ApiModelProperty("平历史输入能量")
    private Double flatHistoryInputEnergy;

    @ApiModelProperty("峰历史输出能量")
    private Double peakHistoryOutputEnergy;

    @ApiModelProperty("峰历史输入能量")
    private Double peakHistoryInputEnergy;

    @ApiModelProperty("尖历史输出能量")
    private Double tipHistoryOutputEnergy;

    @ApiModelProperty("尖历史输入能量")
    private Double tipHistoryInputEnergy;

    @ApiModelProperty("深谷历史输出能量")
    private Double deepVallyHistoryOutputEnergy;

    @ApiModelProperty("深谷历史输入能量")
    private Double deepVallyHistoryInputEnergy;
}
