<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.remedies.mapper.EmsDataRemediesMapper">
    <resultMap id="BaseResultMap" type="com.wifochina.modules.remedies.entity.EmsDataRemediesEntity">
        <id column="id" property="id"/>
        <result column="projectId" property="project_id"/>
        <result column="time" property="time"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="vallyHistoryOutputEnergy" property="vally_history_output_energy"/>
        <result column="vallyHistoryInputEnergy" property="vally_history_input_energy"/>
        <result column="flatHistoryOutputEnergy" property="flat_history_output_energy"/>
        <result column="flatHistoryInputEnergy" property="flat_history_input_energy"/>
        <result column="peakHistoryOutputEnergy" property="peak_history_output_energy"/>
        <result column="peakHistoryInputEnergy" property="peak_history_input_energy"/>
        <result column="tipHistoryOutputEnergy" property="tip_history_output_energy"/>
        <result column="tipHistoryInputEnergy" property="tip_history_input_energy"/>
        <result column="deepVallyHistoryOutputEnergy" property="deep_vally_history_output_energy"/>
        <result column="deepVallyHistoryInputEnergy" property="deep_vally_history_input_energy"/>
    </resultMap>

    <select id="getRemediesSumByMonth" parameterType="com.wifochina.modules.remedies.request.SumRequest"
            resultMap="BaseResultMap">
        select year, month, sum(vally_history_output_energy) as vally_history_output_energy,
            sum(vally_history_input_energy) as vally_history_input_energy,
            sum(flat_history_output_energy) as flat_history_output_energy,
            sum(flat_history_input_energy) as flat_history_input_energy,
            sum(peak_history_output_energy) as peak_history_output_energy,
            sum(peak_history_input_energy) as peak_history_input_energy,
            sum(tip_history_output_energy) as tip_history_output_energy,
            sum(tip_history_input_energy) as tip_history_input_energy,
            sum(deep_vally_history_output_energy) as deep_vally_history_output_energy,
            sum(deep_vally_history_input_energy) as deep_vally_history_input_energy
        from t_ems_data_remedies
        where <![CDATA[ time >= #{start}
          and time <= #{end} ]]>
        and project_id = #{projectId}
        group by year, month
        order by year, month
    </select>

</mapper>
