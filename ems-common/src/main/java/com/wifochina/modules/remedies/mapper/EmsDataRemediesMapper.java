package com.wifochina.modules.remedies.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wifochina.modules.remedies.entity.EmsDataRemediesEntity;
import com.wifochina.modules.remedies.request.SumRequest;

import java.util.List;

/**
 * <p>
 * 数据状态 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
public interface EmsDataRemediesMapper extends BaseMapper<EmsDataRemediesEntity> {
    List<EmsDataRemediesEntity> getRemediesSumByMonth(SumRequest sumRequest);
}
