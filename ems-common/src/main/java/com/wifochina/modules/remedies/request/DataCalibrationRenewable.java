package com.wifochina.modules.remedies.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * Created on 2024/6/20 10:33. 数据校准 EMS
 *
 * <AUTHOR>
 */
@Data
public class DataCalibrationRenewable extends DataCalibrationCommon {

    private String renewableType;

    @ApiModelProperty("尖自用电量 尖峰平谷模式使用")
    private Double tipSelfEnergy = 0.0;

    @ApiModelProperty("峰自用电量 尖峰平谷模式使用")
    private Double peakSelfEnergy = 0.0;

    @ApiModelProperty("平自用电量 尖峰平谷模式使用")
    private Double flatSelfEnergy = 0.0;

    @ApiModelProperty("谷自用电量 尖峰平谷模式使用  ")
    private Double vallySelfEnergy = 0.0;

    @ApiModelProperty("深自用电量 尖峰平谷模式使用")
    private Double deepVallySelfEnergy = 0.0;

    @ApiModelProperty("馈网 上网电量 尖峰平谷模式和协议模式使用")
    private Double onlineEnergy = 0.0;

    @ApiModelProperty("自用电量 供协议模式使用")
    private Double totalSelfEnergy = 0.0;

    public Double getEnergy(int period) {
        Double result = 0.0;
        switch (period) {
            case 1:
                result = vallySelfEnergy == null ? 0.0 : vallySelfEnergy;
                break;
            case 2:
                result = flatSelfEnergy == null ? 0.0 : flatSelfEnergy;
                break;
            case 3:
                result = peakSelfEnergy == null ? 0.0 : peakSelfEnergy;
                break;
            case 4:
                result = tipSelfEnergy == null ? 0.0 : tipSelfEnergy;
                break;
            case 5:
                result = deepVallySelfEnergy == null ? 0.0 : deepVallySelfEnergy;
                break;
            default:
                break;
        }
        return result;
    }
}
