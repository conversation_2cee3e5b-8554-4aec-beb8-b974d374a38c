package com.wifochina.modules.remedies.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.income.cache.memorycache.IIncomeMemoryCacheService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.entity.EmsDataCountEntity;
import com.wifochina.modules.remedies.entity.EmsDataRemediesEntity;
import com.wifochina.modules.remedies.request.*;
import com.wifochina.modules.remedies.service.EmsDataCountService;
import com.wifochina.modules.remedies.service.EmsDataRemediesService;
import com.wifochina.modules.remedies.vo.EmsDataRemediesVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.Resource;

/**
 * 数据状态 前端控制器
 *
 * @deprecated @see DataCalibrationController this is a new Class
 * <AUTHOR>
 * @since 2023-08-07
 */
@RestController
@RequestMapping("/remedies")
@Api(tags = "24-补充数据")
@Deprecated
public class EmsDataRemediesController {

    @Resource private EmsDataRemediesService emsDataRemediesService;

    @Resource private EmsDataCountService emsDataCountService;

    @Resource private ProjectService projectService;

    @Resource private IIncomeMemoryCacheService incomeMemoryCacheService;

    @PostMapping("/add")
    @ApiOperation("补充数据")
    @Log(module = "REMEDIES", methods = "REMEDIES_DATA_ADD", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/remedies/add')")
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> add(@RequestBody EmsDataRemediesRequest emsDataRemediesRequest) {
        EmsDataRemediesEntity emsDataRemediesEntity = new EmsDataRemediesEntity();
        BeanUtils.copyProperties(emsDataRemediesRequest, emsDataRemediesEntity);
        emsDataRemediesEntity.setProjectId(WebUtils.projectId.get());
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        LocalDateTime localDateTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(emsDataRemediesRequest.getTime()),
                        MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
        emsDataRemediesEntity.setYear(localDateTime.getYear());
        emsDataRemediesEntity.setMonth(localDateTime.getMonthValue());
        emsDataRemediesEntity.setDay(localDateTime.getDayOfMonth());
        emsDataRemediesService.save(emsDataRemediesEntity);
        emsDataCountService
                .lambdaUpdate()
                .set(EmsDataCountEntity::getState, true)
                .eq(EmsDataCountEntity::getProjectId, WebUtils.projectId.get())
                .eq(EmsDataCountEntity::getTime, emsDataRemediesRequest.getTime())
                .update();
        incomeMemoryCacheService.updateElectricProfitVO(
                projectEntity.getId(),
                projectEntity.getProjectName(),
                projectEntity.getCreateTime(),
                projectEntity.getTimezone());
        return Result.success();
    }

    @PostMapping("/update")
    @ApiOperation("补充数据修改")
    @Log(module = "REMEDIES", methods = "REMEDIES_DATA_UPDATE", type = OperationType.UPDATE_SIMPLE)
    @PreAuthorize("hasAuthority('/remedies/update')")
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> update(@RequestBody EmsDataRemediesRequest emsDataRemediesRequest) {
        EmsDataRemediesEntity emsDataRemediesEntity = new EmsDataRemediesEntity();
        BeanUtils.copyProperties(emsDataRemediesRequest, emsDataRemediesEntity);
        emsDataRemediesEntity.setProjectId(WebUtils.projectId.get());
        emsDataRemediesService.updateById(emsDataRemediesEntity);
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        incomeMemoryCacheService.updateElectricProfitVO(
                projectEntity.getId(),
                projectEntity.getProjectName(),
                projectEntity.getCreateTime(),
                projectEntity.getTimezone());
        return Result.success();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("补充数据删除")
    @Log(module = "REMEDIES", methods = "REMEDIES_DATA_DEL", type = OperationType.DEL_SIMPLE)
    @PreAuthorize("hasAuthority('/remedies/delete')")
    public Result<String> delete(@PathVariable("id") String id) {
        emsDataRemediesService.removeById(id);
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        incomeMemoryCacheService.updateElectricProfitVO(
                projectEntity.getId(),
                projectEntity.getProjectName(),
                projectEntity.getCreateTime(),
                projectEntity.getTimezone());
        return Result.success();
    }

    @PostMapping("/get")
    @ApiOperation("获取补充数据")
    @PreAuthorize("hasAuthority('/remedies/query')")
    public Result<EmsDataRemediesEntity> get(@RequestBody EmsDataTimeRequest emsDataTimeRequest) {
        return Result.success(
                emsDataRemediesService
                        .lambdaQuery()
                        .eq(EmsDataRemediesEntity::getProjectId, WebUtils.projectId.get())
                        .eq(EmsDataRemediesEntity::getTime, emsDataTimeRequest.getTime())
                        .one());
    }

    @PostMapping("/getSum")
    @ApiOperation("获取补充数据和")
    @PreAuthorize("hasAuthority('/remedies/query')")
    public Result<EmsDataRemediesVo> get(@RequestBody SumRequest sumRequest) {
        return Result.success(
                emsDataRemediesService.sumEnergy(
                        WebUtils.projectId.get(), sumRequest.getStart(), sumRequest.getEnd()));
    }

    @PostMapping("/query")
    @ApiOperation("获取补充数据列表")
    @PreAuthorize("hasAuthority('/remedies/query')")
    public Result<IPage<EmsDataRemediesEntity>> query(
            @RequestBody EmsDataPageRequest emsDataPageRequest) {
        Page<EmsDataRemediesEntity> page =
                Page.of(emsDataPageRequest.getPageNum(), emsDataPageRequest.getPageSize());
        IPage<EmsDataRemediesEntity> list =
                emsDataRemediesService.page(
                        page,
                        Wrappers.lambdaQuery(EmsDataRemediesEntity.class)
                                .eq(EmsDataRemediesEntity::getProjectId, WebUtils.projectId.get())
                                .ge(EmsDataRemediesEntity::getTime, emsDataPageRequest.getStart())
                                .le(EmsDataRemediesEntity::getTime, emsDataPageRequest.getEnd())
                                .orderByDesc(EmsDataRemediesEntity::getTime));
        return Result.success(list);
    }

    @PostMapping("/queryCount")
    @ApiOperation("获取待补充数据列表")
    @PreAuthorize("hasAuthority('/remedies/query') or hasAuthority('/report/month/missData')")
    public Result<List<EmsDataCountEntity>> queryCount(
            @RequestBody EmsDataPageRequest emsDataPageRequest) {
        List<EmsDataCountEntity> list =
                emsDataCountService
                        .lambdaQuery()
                        .eq(EmsDataCountEntity::getProjectId, WebUtils.projectId.get())
                        .eq(EmsDataCountEntity::getState, false)
                        .ge(EmsDataCountEntity::getTime, emsDataPageRequest.getStart())
                        .le(EmsDataCountEntity::getTime, emsDataPageRequest.getEnd())
                        .orderByAsc(EmsDataCountEntity::getTime)
                        .list();
        return Result.success(list);
    }

    @PostMapping("/updateStatus")
    @ApiOperation("确认已修改数据")
    @Log(module = "REMEDIES", methods = "REMEDIES_DATA_UPDATE", type = OperationType.UPDATE_SIMPLE)
    @PreAuthorize("hasAuthority('/remedies/update')")
    public Result<Object> add(@RequestBody EmsDataTimeRequest emsDataTimeRequest) {
        emsDataCountService
                .lambdaUpdate()
                .set(EmsDataCountEntity::getState, true)
                .eq(EmsDataCountEntity::getProjectId, WebUtils.projectId.get())
                .eq(EmsDataCountEntity::getTime, emsDataTimeRequest.getTime())
                .update();
        return Result.success();
    }
}
