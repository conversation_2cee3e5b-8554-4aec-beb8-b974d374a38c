package com.wifochina.modules.strategy.request;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

/**
 * BackFlowLimitRequest
 *
 * @since 2025-07-23 16:06:50
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "分时防逆流请求")
@NoArgsConstructor
@AllArgsConstructor
public class TimeSharingBackFlowLimitRequest {

    private Integer id;

    private String projectId;

    private String groupId;

    @ApiModelProperty(value = "系统主策略id")
    private Integer systemStrategyId;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @ApiModelProperty(value = "防逆流 true 为勾选 false 为未勾选")
    private Boolean backFlowLimitController;

    @ApiModelProperty(value = "防逆流最小值")
    private Double backFlowLimitPower;
}
