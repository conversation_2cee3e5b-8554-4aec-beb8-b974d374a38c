package com.wifochina.modules.strategy.service.impl;

import com.wifochina.common.constants.ElectricPriceSpanEnum;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.electric.vo.ElectricPriceSystemData;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.request.go.Strategy;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;
import com.wifochina.modules.strategy.service.TimeStrategy;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@Deprecated
public class PriceDefinedTimeStrategyImpl implements TimeStrategy {

    @Resource private StrategyService strategyService;

    @Resource private ElectricPriceService electricPriceService;

    @Resource private ProjectService projectService;

    @Resource private GroupService groupService;
    @Resource private PriceAreaService priceAreaService;

    @Override
    public List<Strategy> getTimeStrategy(String groupId, int StrategyType) {
        List<Strategy> strategyList = new ArrayList<>();
        GroupEntity groupEntity = groupService.getById(groupId);
        if (groupEntity == null) {
            return strategyList;
        }

        ProjectEntity projectEntity = projectService.getById(groupEntity.getProjectId());
        if (projectEntity == null) {
            return strategyList;
        }

        long start = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        int weekday = MyTimeUtil.getWeekday(start, projectEntity.getTimezone());
        List<ElectricPriceSystemData.Data> electricPrice =
                getElectricPriceSystemData(start, projectEntity);
        if (electricPrice == null || electricPrice.isEmpty()) {
            return strategyList;
        }
        List<StrategyEntity> strategyEntities =
                strategyService.getTimeStrategyByGroupIdAndType(groupId, StrategyType);
        if (strategyEntities == null || strategyEntities.isEmpty()) {
            return strategyList;
        }
        strategyEntities.stream()
                .filter(e -> e.getWeekDay() == weekday)
                .forEach(
                        chargeStrategy ->
                                addStrategy(
                                        chargeStrategy,
                                        electricPrice,
                                        start,
                                        projectEntity,
                                        strategyList));
        // 处理下一日策略
        long startNextDay = start + EmsConstants.ONE_DAY_SECOND;
        int weekdayNextDay = MyTimeUtil.getWeekday(startNextDay, projectEntity.getTimezone());
        strategyEntities.stream()
                .filter(e -> e.getWeekDay() == weekdayNextDay) // 下一日充电策略
                .forEach(
                        chargeStrategyNextDay ->
                                addStrategy(
                                        chargeStrategyNextDay,
                                        electricPrice,
                                        startNextDay,
                                        projectEntity,
                                        strategyList));
        // 排序并返回策略列表
        return strategyList.stream()
                .sorted(
                        Comparator.comparing(Strategy::getWeek)
                                .thenComparing(Strategy::getStart_minute))
                .collect(Collectors.toList());
    }

    private List<ElectricPriceSystemData.Data> getElectricPriceSystemData(
            long start, ProjectEntity projectEntity) {
        if (projectEntity.getPriceProxy()) {
            PriceAreaEntity priceAreaEntity = priceAreaService.queryProxyForDynamic(projectEntity);
            if (priceAreaEntity != null) {
                projectEntity.setElectricPriceArea(priceAreaEntity.getElectricPriceArea());
                projectEntity.setElectricPriceSpan(priceAreaEntity.getElectricPriceSpan());
                projectEntity.setCountry(priceAreaEntity.getCountryId());
            } else {
                return List.of();
            }
        }
        return electricPriceService.getRealTimePriceSystemData(
                start,
                null,
                new ElectricPriceService.RealTimePriceSystemContext() {
                    @Override
                    public Integer countryId() {
                        return projectEntity.getCountry();
                    }

                    @Override
                    public String priceArea() {
                        return projectEntity.getElectricPriceArea();
                    }

                    @Override
                    public String span() {
                        return projectEntity.getElectricPriceSpan();
                    }
                });
    }

    private void addStrategy(
            StrategyEntity strategyEntity,
            List<ElectricPriceSystemData.Data> electricPrices,
            long startTimeOfDay,
            ProjectEntity projectEntity,
            List<Strategy> strategyList) {
        for (ElectricPriceSystemData.Data price : electricPrices) {
            if ((strategyEntity.getType() == 0
                            && price.getAverage() <= strategyEntity.getPriceBenchmark())
                    || (strategyEntity.getType() == 1
                            && price.getAverage() >= strategyEntity.getPriceBenchmark())) {
                if (price.getStartTimeUnix() >= startTimeOfDay
                        && price.getStartTimeUnix()
                                < startTimeOfDay + EmsConstants.ONE_DAY_SECOND) {
                    Strategy strategy = new Strategy();
                    strategy.setWeek(strategyEntity.getWeekDay());
                    strategy.setSoc(
                            strategyEntity.getSoc() == null
                                    ? null
                                    : strategyEntity.getSoc().intValue());
                    strategy.setPower(strategyEntity.getPower());
                    strategy.setStart_minute(
                            MyTimeUtil.getMinutesInDay(
                                    price.getStartTimeUnix(), projectEntity.getTimezone()));
                    int endTimeUnix =
                            price.getStartTimeUnix()
                                    + ElectricPriceSpanEnum.getValue(
                                            projectEntity.getElectricPriceSpan());
                    strategy.setEnd_minute(
                            MyTimeUtil.getMinutesInDay(endTimeUnix, projectEntity.getTimezone()));
                    strategy.setFunction(strategyEntity.getType());
                    strategyList.add(strategy);
                }
            }
        }
    }
}
