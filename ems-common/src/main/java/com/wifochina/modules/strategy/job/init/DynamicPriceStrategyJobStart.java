package com.wifochina.modules.strategy.job.init;

import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.service.StrategyQuartzService;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-15 8:29 PM
 */
@Component
public class DynamicPriceStrategyJobStart {
    @Resource private ProjectService projectService;

    @Resource private StrategyQuartzService strategyQuartzService;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        List<ProjectEntity> projectEntities =
                projectService
                        .lambdaQuery()
                        .ne(
                                ProjectEntity::getElectricPriceType,
                                ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD.getValue())
                        .eq(ProjectEntity::getWhetherDelete, false)
                        .list();
        List<String> timezoneList =
                projectEntities.stream()
                        .map(ProjectEntity::getTimezone)
                        .distinct()
                        .collect(Collectors.toList());
        for (String timezone : timezoneList) {
            strategyQuartzService.addDynamicPriceStrategyJob(timezone);
        }
    }
}
