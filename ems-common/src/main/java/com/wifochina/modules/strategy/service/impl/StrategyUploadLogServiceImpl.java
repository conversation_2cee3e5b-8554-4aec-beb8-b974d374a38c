package com.wifochina.modules.strategy.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.strategy.entity.StrategyUploadLogEntity;
import com.wifochina.modules.strategy.mapper.StrategyUploadLogMapper;
import com.wifochina.modules.strategy.service.StrategyUploadLogService;

import org.springframework.stereotype.Service;

import java.time.Instant;

/**
 * Created on 2024/3/12 14:17.
 *
 * <AUTHOR>
 */
@Service
public class StrategyUploadLogServiceImpl
        extends ServiceImpl<StrategyUploadLogMapper, StrategyUploadLogEntity>
        implements StrategyUploadLogService {

    @Override
    public int saveSuccessLog(String projectId, int type) {
        StrategyUploadLogEntity strategyUploadLogEntity = new StrategyUploadLogEntity();
        strategyUploadLogEntity.setSuccess(true);
        strategyUploadLogEntity.setProjectId(projectId);
        strategyUploadLogEntity.setType(type);
        strategyUploadLogEntity.setTime(Instant.now().getEpochSecond());
        return baseMapper.insert(strategyUploadLogEntity);
    }

    @Override
    public int saveFailLog(String projectId, int type) {
        StrategyUploadLogEntity strategyUploadLogEntity = new StrategyUploadLogEntity();
        strategyUploadLogEntity.setSuccess(false);
        strategyUploadLogEntity.setRetry(0);
        strategyUploadLogEntity.setProjectId(projectId);
        strategyUploadLogEntity.setType(type);
        strategyUploadLogEntity.setTime(Instant.now().getEpochSecond());
        return baseMapper.insert(strategyUploadLogEntity);
    }

    @Override
    public int updateSuccessLog(int i) {
        StrategyUploadLogEntity strategyUploadLogEntity = this.baseMapper.selectById(i);
        strategyUploadLogEntity.setSuccess(true);
        strategyUploadLogEntity.setFixTime(Instant.now().getEpochSecond());
        return baseMapper.updateById(strategyUploadLogEntity);
    }

    @Override
    public int updateFailLog(int i) {
        StrategyUploadLogEntity strategyUploadLogEntity = this.baseMapper.selectById(i);
        strategyUploadLogEntity.setRetry(strategyUploadLogEntity.getRetry() + 1);
        return baseMapper.updateById(strategyUploadLogEntity);
    }
}
