package com.wifochina.modules.strategy.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;

/**
 * Created on 2025/8/13 13:48.
 *
 * <p>1.4.4 分时需量 的 time range
 *
 * <AUTHOR>
 */
@Data
public class TimeRange {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;
}
