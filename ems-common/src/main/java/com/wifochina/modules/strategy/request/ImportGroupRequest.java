package com.wifochina.modules.strategy.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * strategyRequest
 *
 * @since 4/19/2022 2:55 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "分组策略导入")
public class ImportGroupRequest {

    @ApiModelProperty(value = "导入来源分组id", required = true)
    private String fromGroupId;

    @ApiModelProperty(value = "导入目标分组id", required = true)
    private String toGroupId;

    @ApiModelProperty(value = "策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式")
    private Integer strategyType;
}
