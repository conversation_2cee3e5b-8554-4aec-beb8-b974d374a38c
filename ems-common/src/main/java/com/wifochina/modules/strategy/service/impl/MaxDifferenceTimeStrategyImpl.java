package com.wifochina.modules.strategy.service.impl;

import com.wifochina.common.constants.ElectricPriceSpanEnum;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.electric.vo.ElectricPriceSystemData;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.request.go.Strategy;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;
import com.wifochina.modules.strategy.service.TimeStrategy;

import lombok.Getter;

import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@Deprecated
public class MaxDifferenceTimeStrategyImpl implements TimeStrategy {
    @Resource private StrategyService strategyService;

    @Resource private ElectricPriceService electricPriceService;

    @Resource private ProjectService projectService;

    @Resource private GroupService groupService;

    @Resource private PriceAreaService priceAreaService;

    @Override
    public List<Strategy> getTimeStrategy(String groupId, int StrategyType) {
        List<Strategy> strategyList = new ArrayList<>();
        GroupEntity groupEntity = groupService.getById(groupId);
        if (groupEntity == null) {
            return strategyList;
        }

        ProjectEntity projectEntity = projectService.getById(groupEntity.getProjectId());
        if (projectEntity == null) {
            return strategyList;
        }

        long start = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
        int weekday = MyTimeUtil.getWeekday(start, projectEntity.getTimezone());
        List<ElectricPriceSystemData.Data> electricPrice =
                getElectricPriceSystemData(start, projectEntity);
        if (electricPrice == null || electricPrice.isEmpty()) {
            return strategyList;
        }

        List<StrategyEntity> strategyEntities =
                strategyService.getTimeStrategyByGroupIdAndType(groupId, StrategyType);
        if (strategyEntities == null || strategyEntities.isEmpty()) {
            return strategyList;
        }

        // 处理当天策略
        processDayStrategy(
                start, weekday, electricPrice, strategyEntities, strategyList, projectEntity);

        // 处理次日策略
        long startNextDay = start + EmsConstants.ONE_DAY_SECOND;
        int weekdayNextDay = MyTimeUtil.getWeekday(startNextDay, projectEntity.getTimezone());
        processDayStrategy(
                startNextDay,
                weekdayNextDay,
                electricPrice,
                strategyEntities,
                strategyList,
                projectEntity);

        // 排序并返回策略列表
        return strategyList.stream()
                .sorted(
                        Comparator.comparing(Strategy::getWeek)
                                .thenComparing(Strategy::getStart_minute))
                .collect(Collectors.toList());
    }

    private void processDayStrategy(
            long start,
            int weekday,
            List<ElectricPriceSystemData.Data> electricPrice,
            List<StrategyEntity> strategyEntities,
            List<Strategy> strategyList,
            ProjectEntity projectEntity) {
        List<ElectricPriceSystemData.Data> dayElectricPrice =
                filterElectricPriceForDay(start, electricPrice);
        if (dayElectricPrice.isEmpty()) {
            return;
        }
        List<StrategyEntity> dayStrategyEntities =
                filterStrategyEntitiesForWeekday(weekday, strategyEntities);
        if (dayStrategyEntities.isEmpty()) {
            return;
        }

        Boundary boundary = calculateBoundary(dayElectricPrice, dayStrategyEntities);
        dayElectricPrice.forEach(
                price ->
                        addStrategy(
                                price,
                                boundary,
                                findStrategyByType(dayStrategyEntities, 0),
                                findStrategyByType(dayStrategyEntities, 1),
                                strategyList,
                                projectEntity));
    }

    private List<ElectricPriceSystemData.Data> filterElectricPriceForDay(
            long start, List<ElectricPriceSystemData.Data> electricPrice) {
        return electricPrice.stream()
                .filter(
                        e ->
                                e.getStartTimeUnix() >= start
                                        && e.getStartTimeUnix()
                                                < start + EmsConstants.ONE_DAY_SECOND)
                .collect(Collectors.toList());
    }

    private List<StrategyEntity> filterStrategyEntitiesForWeekday(
            int weekday, List<StrategyEntity> strategyEntities) {
        return strategyEntities.stream()
                .filter(e -> e.getWeekDay() == weekday)
                .collect(Collectors.toList());
    }

    private Boundary calculateBoundary(
            List<ElectricPriceSystemData.Data> electricPrice,
            List<StrategyEntity> strategyEntities) {
        double minAverage =
                electricPrice.stream()
                        .mapToDouble(ElectricPriceSystemData.Data::getAverage)
                        .min()
                        .orElse(Double.NaN);

        double maxAverage =
                electricPrice.stream()
                        .mapToDouble(ElectricPriceSystemData.Data::getAverage)
                        .max()
                        .orElse(Double.NaN);

        StrategyEntity minDeltaStrategy = findStrategyByType(strategyEntities, 0);
        Double minDelta = minDeltaStrategy != null ? minDeltaStrategy.getPriceDifference() : null;

        StrategyEntity maxDeltaStrategy = findStrategyByType(strategyEntities, 1);
        Double maxDelta = maxDeltaStrategy != null ? maxDeltaStrategy.getPriceDifference() : null;

        return new Boundary(minAverage, minDelta, maxAverage, maxDelta);
    }

    private StrategyEntity findStrategyByType(List<StrategyEntity> strategies, int type) {
        return strategies.stream()
                .filter(e -> e.getWeekDay() != 0)
                .filter(e -> e.getType() == type)
                .findFirst()
                .orElse(null);
    }

    private List<ElectricPriceSystemData.Data> getElectricPriceSystemData(
            long start, ProjectEntity projectEntity) {
        if (projectEntity.getPriceProxy()) {
            PriceAreaEntity priceAreaEntity = priceAreaService.queryProxyForDynamic(projectEntity);
            if (priceAreaEntity != null) {
                projectEntity.setElectricPriceArea(priceAreaEntity.getElectricPriceArea());
                projectEntity.setElectricPriceSpan(priceAreaEntity.getElectricPriceSpan());
                projectEntity.setCountry(priceAreaEntity.getCountryId());
            } else {
                return List.of();
            }
        }
        return electricPriceService.getRealTimePriceSystemData(
                start,
                null,
                new ElectricPriceService.RealTimePriceSystemContext() {
                    @Override
                    public Integer countryId() {
                        return projectEntity.getCountry();
                    }

                    @Override
                    public String priceArea() {
                        return projectEntity.getElectricPriceArea();
                    }

                    @Override
                    public String span() {
                        return projectEntity.getElectricPriceSpan();
                    }
                });
    }

    private void addStrategy(
            ElectricPriceSystemData.Data electricPrice,
            Boundary boundary,
            StrategyEntity todayChargeStrategyEntity,
            StrategyEntity todayDischargeStrategyEntity,
            List<Strategy> strategyList,
            ProjectEntity projectEntity) {

        int status = decideAction(electricPrice.getAverage(), boundary);
        if (status == 2) {
            return;
        }
        if (status == 0 && todayChargeStrategyEntity != null) {
            Strategy strategy = new Strategy();
            strategy.setWeek(todayChargeStrategyEntity.getWeekDay());
            strategy.setSoc(
                    todayChargeStrategyEntity.getSoc() == null
                            ? null
                            : todayChargeStrategyEntity.getSoc().intValue());
            strategy.setPower(todayChargeStrategyEntity.getPower());
            strategy.setStart_minute(
                    MyTimeUtil.getMinutesInDay(
                            electricPrice.getStartTimeUnix(), projectEntity.getTimezone()));
            int endTimeUnix =
                    electricPrice.getStartTimeUnix()
                            + ElectricPriceSpanEnum.getValue(projectEntity.getElectricPriceSpan());
            strategy.setEnd_minute(
                    MyTimeUtil.getMinutesInDay(endTimeUnix, projectEntity.getTimezone()));
            strategy.setFunction(todayChargeStrategyEntity.getType());
            strategyList.add(strategy);
        }
        if (status == 1 && todayDischargeStrategyEntity != null) {
            Strategy strategy = new Strategy();
            strategy.setWeek(todayDischargeStrategyEntity.getWeekDay());
            strategy.setSoc(
                    todayDischargeStrategyEntity.getSoc() == null
                            ? null
                            : todayDischargeStrategyEntity.getSoc().intValue());
            strategy.setPower(todayDischargeStrategyEntity.getPower());
            strategy.setStart_minute(
                    MyTimeUtil.getMinutesInDay(
                            electricPrice.getStartTimeUnix(), projectEntity.getTimezone()));
            int endTimeUnix =
                    electricPrice.getStartTimeUnix()
                            + ElectricPriceSpanEnum.getValue(projectEntity.getElectricPriceSpan());
            strategy.setEnd_minute(
                    MyTimeUtil.getMinutesInDay(endTimeUnix, projectEntity.getTimezone()));
            strategy.setFunction(todayDischargeStrategyEntity.getType());
            strategyList.add(strategy);
        }
    }

    /**
     * 决定当前价格下的操作：充电、放电或不操作。
     *
     * @param currentPrice 当前价格
     * @return 策略类型 0 充电 1放电 2不操作
     */
    public static int decideAction(double currentPrice, Boundary boundary) {
        double minBoundary = boundary.getMinBoundary();
        double maxBoundary = boundary.getMaxBoundary();
        if (minBoundary > maxBoundary) { // 检查边界是否交叉
            // 如果交叉，定义不操作的价格区间
            if (currentPrice >= minBoundary && currentPrice <= maxBoundary) {
                return 2; // 价格在交叉区间内，不操作
            } else if (currentPrice < minBoundary) {
                return 0; // 低于最小边界，充电
            } else { // currentPrice > maxBoundary
                return 1; // 高于最大边界，放电
            }
        } else { // 边界未交叉的情况
            if (currentPrice < minBoundary) {
                return 0;
            } else if (currentPrice > maxBoundary) {
                return 1;
            } else {
                return 2;
            }
        }
    }

    @Getter
    public static class Boundary {
        // 最小边界
        private final double minBoundary;
        // 最大边界
        private final double maxBoundary;

        public Boundary(double minValue, Double minDelta, double maxValue, Double maxDelta) {
            // 检查delta是否为null，若为null则不改变边界
            this.minBoundary = minDelta != null ? minValue + minDelta : minValue;
            this.maxBoundary = maxDelta != null ? maxValue - maxDelta : maxValue;
        }
    }
}
