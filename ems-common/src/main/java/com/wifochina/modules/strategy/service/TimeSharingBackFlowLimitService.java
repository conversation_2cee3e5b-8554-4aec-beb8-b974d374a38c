package com.wifochina.modules.strategy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity;
import com.wifochina.modules.strategy.request.TimeSharingBackFlowLimitRequest;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2025-07-23 11:22:01
 */
public interface TimeSharingBackFlowLimitService extends IService<TimeSharingBackFlowLimitEntity> {
    List<TimeSharingBackFlowLimitEntity> listBy(String projectId, String groupId);

    void addBackFlowLimitItem(TimeSharingBackFlowLimitRequest timeSharingBackFlowLimitRequest);

    void editBackFlowLimitItem(TimeSharingBackFlowLimitRequest timeSharingBackFlowLimitRequest);

    void refresh(
            String projectId,
            String groupId,
            List<TimeSharingBackFlowLimitRequest> timeSharingBackFlowLimitRequests);
}
