package com.wifochina.modules.strategy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.strategy.entity.StrategyUploadLogEntity;

/**
 * EmailLog 服务类
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
public interface StrategyUploadLogService extends IService<StrategyUploadLogEntity> {
    int saveSuccessLog(String projectId, int type);

    int saveFailLog(String projectId, int type);

    int updateSuccessLog(int i);

    int updateFailLog(int i);
}
