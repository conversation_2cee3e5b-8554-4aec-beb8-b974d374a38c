package com.wifochina.modules.strategy.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_strategy_history")
@ApiModel(value = "StrategyHistoryEntity对象")
public class StrategyHistoryEntity extends StrategyEntity {

    @ApiModelProperty(value = "历史版本号")
    private Integer version;

}
