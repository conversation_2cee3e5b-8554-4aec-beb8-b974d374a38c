package com.wifochina.modules.strategy.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_strategy_upload_log")
@ApiModel(value = "上传策略日志对象")
public class StrategyUploadLogEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("策略下发的场景")
    private Integer type;

    @ApiModelProperty("重试次数（预留）")
    private Integer retry;

    @ApiModelProperty("发送状态")
    private Boolean success;

    @ApiModelProperty("发送时间")
    private Long time;

    @ApiModelProperty("修复时间（预留）")
    private Long fixTime;
}
