package com.wifochina.modules.strategy.job;

import cn.hutool.extra.spring.SpringUtil;

import com.wifochina.common.config.EmailSuperNoticeConfig;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.EmailLogTypeEnum;
import com.wifochina.common.constants.StrategyTypeEnum;
import com.wifochina.common.constants.StrategyUploadTypeEnum;
import com.wifochina.common.util.EmailService;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.demand.entity.DemandEmailEntity;
import com.wifochina.modules.demand.service.DemandEmailService;
import com.wifochina.modules.metrics.ScheduleJobMetrics;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;
import com.wifochina.modules.strategy.service.StrategyUploadLogService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
public class DynamicPriceStrategyJob extends QuartzJobBean {

    private final ProjectService projectService;

    private final StrategyService strategyService;

    private final StrategyUploadLogService strategyUploadLogService;

    private final ScheduleJobMetrics scheduleJobMetrics;

    @Override
    protected void executeInternal(@NotNull JobExecutionContext context)
            throws JobExecutionException {
        String timezone = (String) context.getJobDetail().getJobDataMap().get("timezone");
        log.info("DynamicPriceStrategyJob start execute timezone : {}", timezone);
        List<ProjectEntity> projectEntities =
                projectService.getProjectsByTimeZoneAndPriceType(
                        timezone, ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.getValue());
        for (ProjectEntity projectEntity : projectEntities) {
            List<StrategyEntity> list =
                    strategyService.getOuterStrategyByProjectId(projectEntity.getId());
            boolean notAutoSet =
                    list.stream()
                            .allMatch(
                                    strategyEntity ->
                                            strategyEntity.getStrategyType() == null
                                                    || strategyEntity.getStrategyType()
                                                    != StrategyTypeEnum
                                                    .TIMING_CHARGE_DISCHARGE
                                                    .getType());
            if (notAutoSet) {
                continue;
            }
            boolean uploadSuccess = strategyService.uploadStrategy(projectEntity.getId());
            log.info(
                    "DynamicPriceStrategyJob projectName:{} uploadSuccess : {}",
                    projectEntity.getProjectName(),
                    uploadSuccess);
            if (uploadSuccess) {
                scheduleJobMetrics.recordSuccess(
                        projectEntity.getId(),
                        projectEntity.getProjectName(),
                        this.getClass().getSimpleName());
                strategyUploadLogService.saveSuccessLog(
                        projectEntity.getId(), StrategyUploadTypeEnum.DAY_DYNAMIC_PRICE.getType());
            } else {
                ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
                executor.schedule(
                        () -> strategyService.uploadStrategy(projectEntity.getId()),
                        10,
                        TimeUnit.MINUTES);
                strategyUploadLogService.saveFailLog(
                        projectEntity.getId(), StrategyUploadTypeEnum.DAY_DYNAMIC_PRICE.getType());
                sendFailMessage(projectEntity);
                log.error(
                        "执行动态电价更新策略时失败: projectId:{}, name:{}",
                        projectEntity.getId(),
                        projectEntity.getProjectName());
                scheduleJobMetrics.recordFailure(
                        projectEntity.getId(),
                        projectEntity.getProjectName(),
                        this.getClass().getSimpleName(),
                        "执行动态电价更新策略时失败");
            }
        }
    }

    public void sendFailMessage(ProjectEntity projectEntity) {
        String subject = String.format("%s 动态电价自动下发动态策略失败", projectEntity.getProjectName());
        String message =
                String.format(
                        "%s 在 %s 动态电价自动下发动态策略失败, 请注意查看",
                        projectEntity.getProjectName(),
                        LocalDateTime.now(ZoneId.of(projectEntity.getTimezone())));
        EmailService emailService = SpringUtil.getBean(EmailService.class);
        DemandEmailService demandEmailService = SpringUtil.getBean(DemandEmailService.class);
        EmailSuperNoticeConfig emailSuperNoticeConfig =
                SpringUtil.getBean(EmailSuperNoticeConfig.class);
        Set<String> emailList =
                demandEmailService
                        .lambdaQuery()
                        .eq(DemandEmailEntity::getProjectId, projectEntity.getId())
                        .like(DemandEmailEntity::getEmail, EmsConstants.AD_EMAIL_SUFFIXES)
                        .list()
                        .stream()
                        .map(DemandEmailEntity::getEmail)
                        .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(emailSuperNoticeConfig.getStrategyDynamicPriceUpload())
                && !emailSuperNoticeConfig
                        .getStrategyDynamicPriceUpload()
                        .get(0)
                        .startsWith("${EMS_EMAIL_NOTICE")) {
            emailList.addAll(emailSuperNoticeConfig.getStrategyDynamicPriceUpload());
        }
        emailService.sendMessage(
                new ArrayList<>(emailList),
                subject,
                message,
                () ->
                        // 这里没有分组 所以不传分组
                        new EmailService.ServiceInfo()
                                .setProjectId(projectEntity.getId())
                                .setEmailLogTypeEnum(EmailLogTypeEnum.DYNAMIC_PRICE_STRATEGY));
    }
}
