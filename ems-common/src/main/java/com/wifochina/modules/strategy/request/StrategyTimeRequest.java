package com.wifochina.modules.strategy.request;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.wifochina.modules.strategytemplate.request.StrategyControlRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

/**
 * strategyRequest
 *
 * @since 4/19/2022 2:55 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "时间策略请求")
@NoArgsConstructor
@AllArgsConstructor
public class StrategyTimeRequest {

    @ApiModelProperty(value = "增加时不需要传入")
    private Integer id;

    @ApiModelProperty(value = "策略类型  0充电 1放电 2自发自用")
    private Integer type;

    @ApiModelProperty(value = "星期几 1代表星期一 2代表星期二 ... 7代表星期日")
    @Deprecated
    private Integer weekDay;

    @ApiModelProperty(value = "功率")
    private Integer power;

    @ApiModelProperty(value = "soc")
    private Double soc;

    @ApiModelProperty(value = "离网soc下限")
    private Double offGirdSoc;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @ApiModelProperty(value = "分组id", required = true)
    private String groupId;

    @ApiModelProperty(value = "电价差值")
    private Double priceDifference;

    @ApiModelProperty(value = "电价定值即基准值")
    private Double priceBenchmark;

    @ApiModelProperty(value = "策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式")
    private Integer strategyType;

    @ApiModelProperty(value = "controlRequest控制策略 只有海外有")
    private StrategyControlRequest itemControl;

    private Long strategyControlId;

    @ApiModelProperty(value = "new 策略日期MM-dd")
    private String strategyDate;
}
