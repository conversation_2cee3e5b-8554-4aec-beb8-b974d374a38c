package com.wifochina.modules.strategy.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PredicateStrategyRequest {

    @ApiModelProperty(value = "分组id", required = true)
    private String groupId;

    @ApiModelProperty(
            value = "策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式",
            required = true)
    private Integer strategyType;

    @ApiModelProperty(value = "选择的某天的(不传是今天)", required = true)
    private Long date;
}
