package com.wifochina.modules.strategy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.group.request.go.GroupGoRequest;
import com.wifochina.modules.group.request.go.Strategy;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.request.ImportGroupRequest;
import com.wifochina.modules.strategy.request.ImportStrategyRequest;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
public interface StrategyService extends IService<StrategyEntity> {

    Map<Integer, List<StrategyEntity>> getStrategyByGroupId(String groupId);

    List<StrategyEntity> getTimeStrategyByGroupIdAndType(String groupId, int type);

    StrategyEntity getOuterStrategyByGroupId(String groupId);

    List<StrategyEntity> getOuterStrategyByProjectId(String projectId);

    StrategyEntity getTimeRangeStrategyByGroupId(
            String groupId, DayOfWeek dayOfWeek, LocalTime localTime);

    void importTimeStrategy(ImportStrategyRequest importStrategyRequest);

    GroupGoRequest getGroupGoRequest(ProjectEntity projectEntity, String groupId);

    void importGroupStrategy(ImportGroupRequest importGroupRequest);

    boolean uploadStrategy(String projectId);

}
