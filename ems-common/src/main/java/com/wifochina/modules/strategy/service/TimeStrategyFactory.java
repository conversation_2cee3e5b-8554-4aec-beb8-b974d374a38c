package com.wifochina.modules.strategy.service;

import com.wifochina.common.constants.StrategyTypeEnum;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class TimeStrategyFactory implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext)
            throws BeansException {
        TimeStrategyFactory.applicationContext = applicationContext;
    }

    /**
     * 根据类型获取TimeStrategy实例
     *
     * @param strategyType 类型标识，例如1对应FixTimeStrategyImpl，2对应MaxDifferenceTimeStrategyImpl
     * @return 匹配的TimeStrategy实例
     */
    public static TimeStrategy getTimeStrategy(StrategyTypeEnum strategyType) {
        Map<String, TimeStrategy> beansOfType =
                applicationContext.getBeansOfType(TimeStrategy.class);
        switch (strategyType) {
            case AUTOMATIC_MODE:
                return beansOfType.get("automaticTimeStrategyImpl");
            case MAX_DIFFERENCE:
                return beansOfType.get("maxDifferenceTimeStrategyImpl");
            case PRICE_DEFINED:
                return beansOfType.get("priceDefinedTimeStrategyImpl");
            default:
                return beansOfType.get("fixTimeStrategyImpl");
        }
    }
}
