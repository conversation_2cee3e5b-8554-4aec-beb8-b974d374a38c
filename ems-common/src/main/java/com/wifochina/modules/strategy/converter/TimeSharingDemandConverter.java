//package com.wifochina.modules.strategy.converter;
//
//import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
//
//import org.mapstruct.Mapper;
//import org.mapstruct.Mapping;
//import org.mapstruct.Mappings;
//import org.mapstruct.factory.Mappers;
//
///**
// * Created on 2025/8/13 14:04.
// *
// * <AUTHOR>
// */
//@Mapper
//public interface TimeSharingDemandConverter {
//
//    TimeSharingDemandConverter INSTANCE = Mappers.getMapper(TimeSharingDemandConverter.class);
//
//    @Mappings({
//        @Mapping(target = "parentId", ignore = true),
//        @Mapping(target = "timeRanges", ignore = true)
//    })
//    TimeSharingDemandEntity copy(TimeSharingDemandEntity timeSharingDemandEntity);
//}
