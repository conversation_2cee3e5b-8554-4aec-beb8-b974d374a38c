package com.wifochina.modules.strategy.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.page.Result;
import com.wifochina.modules.strategy.entity.StrategyHistoryEntity;
import com.wifochina.modules.strategy.service.StrategyHistoryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-29
 */
@RestController
@Api(tags = "09-控制策略")
@RequestMapping("/strategy")
public class StrategyHistoryController {

    private StrategyHistoryService strategyHistoryService;

    /**
     * 查询策略
     */
    @PostMapping("/history/{projectId}")
    @ApiOperation("查询历史策略")
    public Result<Object> getStrategyHistoryByGroupId(@RequestBody PageBean pageBean,
        @PathVariable("projectId") String projectId) {
        IPage<StrategyHistoryEntity> page = new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        IPage<StrategyHistoryEntity> list = strategyHistoryService.page(page,
            Wrappers.lambdaQuery(StrategyHistoryEntity.class).eq(StrategyHistoryEntity::getProjectId, projectId));
        return Result.success(list);
    }

    @Autowired
    public void setStrategyHistoryService(StrategyHistoryService strategyHistoryService) {
        this.strategyHistoryService = strategyHistoryService;
    }
}
