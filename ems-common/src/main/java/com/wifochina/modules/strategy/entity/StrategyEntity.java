package com.wifochina.modules.strategy.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wifochina.modules.BaseEntity;

import com.wifochina.modules.strategytemplate.common.YearlyStrategyTransformAble;
import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-19 策略分为总策略 (weekday 0)和时间策略(weekday 1-7) 总策略记录实时电价策略类型(StrategyType)
 *     不同的策略类型对应不同的时间策略，因此时间策略也要记录自己的策略类型(StrategyType) 根据用户电价类型以及策略类型，得到用户的所有策略（包括总和事件策略，再根据
 *     weekday是否为空来判断是否是时间策略）
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_strategy")
@ApiModel(value = "StrategyEntity对象", description = "StrategyEntity对象")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class StrategyEntity extends BaseEntity implements YearlyStrategyTransformAble {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "策略类型  0充电 1放电 2自发自用")
    private Integer type;

    @ApiModelProperty(value = "星期几 1代表星期一 2代表星期二 ... 7代表星期日")
    private Integer weekDay;

    @ApiModelProperty(value = "功率")
    private Integer power;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @ApiModelProperty(value = "申报需量控制功率")
    private Double demandPower;

    @ApiModelProperty(value = "容量控制功率")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Double controlPower;

    @ApiModelProperty(value = "需量控制功率,并网点控制目标功率(kW)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Double gridControlPower;

    @ApiModelProperty(value = "并网点设计容量(kVA)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Double pccDemandPower;

    // 2024-08-13 11:49:02 add 额定电压模式 (自动模式/手动模式 , 默认自动模式)
    @ApiModelProperty(value = "变压器额定电压模式")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String voltageMode;

    // 2024-08-13 11:49:02 add 额定电压值 (手动模式 , 才会设置这个值)
    @ApiModelProperty(value = "变压器额定电压")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Double voltagePower;

    @ApiModelProperty(value = "月初需量")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Double monthControlPower;

    @ApiModelProperty(value = "放电soc下限")
    private Double soc;

    @ApiModelProperty(value = "离网soc下限")
    private Double offGirdSoc;

    @ApiModelProperty(value = "离网soc上限")
    private Double offGridSocHighLimit;

    @ApiModelProperty(value = "并网soc上限")
    private Double chargeSocHighLimit;

    @ApiModelProperty(value = "防逆流 true 为勾选 false 为未勾选")
    private Boolean antiReflux;

    @ApiModelProperty(value = "功率因数控制模式开关")
    private Boolean powerFactorControl;

    @ApiModelProperty(value = "功率因数优先模式，关闭时为有功功率优先")
    private Boolean powerFactorFirst;

    @ApiModelProperty(value = "功率因数控制值，0.9 - 1")
    private Double powerFactorControlValue;

    @ApiModelProperty(value = "防逆流最小值")
    private Double backFlowLimitPower;

    @ApiModelProperty(value = "分组id")
    private String groupId;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @ApiModelProperty(value = "仅在规定时间内充电")
    private Boolean chargeInAppointTime;

    @ApiModelProperty(value = "仅在规定时间内放电")
    private Boolean dischargeInAppointTime;

    @ApiModelProperty(value = "动态电价策略类型 1最大差价 2电价定值 3 定时充放 4全自动模式")
    private Integer strategyType;

    @ApiModelProperty(value = "电价差值")
    private Double priceDifference;

    @ApiModelProperty(value = "电价定值即基准值")
    private Double priceBenchmark;

    // 1.4.3 开始不使用week 来管理策略 使用具体日期, 这个日期存储层面还是 yyyy-MM-dd 格式, 下发层面是 MM-dd 格式
    @ApiModelProperty(value = "策略日期 如果是模版就为空, 用于替代week的, 形式如2025-03-02")
    private String strategyDate;

    @ApiModelProperty(value = "item controlId")
    private Long strategyControlId;

    @TableField(exist = false)
    @ApiModelProperty(value = "control entity")
    private StrategyControlEntity itemControl;

    @ApiModelProperty(value = "backFlowLimitList, 开启了防逆流后才会会有这个列表")
    @TableField(exist = false)
    private List<TimeSharingBackFlowLimitEntity> timeSharingBackFlowLimitEntityList;

    @ApiModelProperty(value = "开启了分时空需后才会会有这个列表")
    @TableField(exist = false)
    private List<TimeSharingDemandEntity> timeSharingDemandEntityList;
}
