package com.wifochina.modules.strategy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wifochina.modules.BaseEntity;
import com.wifochina.modules.strategytemplate.common.GoTimeSlotTransformAble;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalTime;

/**
 * Created on 2025/7/22 17:17.
 *
 * <p>防逆流 记录对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_back_flow_limit")
@ApiModel(value = "BackFlowLimitEntity对象", description = "BackFlowLimitEntity对象")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TimeSharingBackFlowLimitEntity extends BaseEntity implements GoTimeSlotTransformAble {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String projectId;
    private String groupId;

    @ApiModelProperty(value = "系统主策略id")
    private Integer systemStrategyId;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @ApiModelProperty(value = "防逆流 true 为勾选 false 为未勾选")
    private Boolean backFlowLimitController;

    @ApiModelProperty(value = "防逆流最小值")
    private Double backFlowLimitPower;
}
