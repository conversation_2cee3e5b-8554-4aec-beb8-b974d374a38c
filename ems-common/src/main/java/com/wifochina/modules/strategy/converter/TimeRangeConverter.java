package com.wifochina.modules.strategy.converter;

import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import com.wifochina.modules.strategy.request.TimeRange;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Created on 2025/8/13 14:04.
 *
 * <AUTHOR>
 */
@Mapper
public interface TimeRangeConverter {

    TimeRangeConverter INSTANCE = Mappers.getMapper(TimeRangeConverter.class);

    TimeRange timeSharing2Vo(TimeSharingDemandEntity timeSharingDemand);
}
