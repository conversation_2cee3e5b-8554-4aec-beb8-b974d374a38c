package com.wifochina.modules.strategy.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * strategyRequest
 *
 * @since 4/19/2022 2:55 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "时间导入策略")
public class ImportStrategyRequest {

    @ApiModelProperty(value = "分组id", required = true)
    private String groupId;

    @ApiModelProperty(value = "从星期几导入 1代表星期一 2代表星期二 ... 7代表星期日", required = true)
    private Integer fromWeekDay;

    @ApiModelProperty(value = "导入到星期几 1代表星期一 2代表星期二 ... 7代表星期日", required = true)
    //    private Integer toWeekDay;
    private List<Integer> toWeekDays;

    @ApiModelProperty(value = "策略类型 1峰谷策略  3固定时段策略  5动态-最大差价 6动态-电价定值 7动态-定时充放 8动态-全自动模式")
    private Integer strategyType;
}
