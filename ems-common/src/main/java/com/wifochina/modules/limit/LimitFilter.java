package com.wifochina.modules.limit;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.web.filter.OncePerRequestFilter;

import com.alibaba.fastjson.JSON;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.ClientUtil;
import com.wifochina.modules.oauth.util.WebUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * LimitFilter
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 7/18/2022 11:13 AM
 */
@Slf4j
@Order(1)
public class LimitFilter extends OncePerRequestFilter {

    @Value("${limit.key}")
    private String key;

    @Value("${limit.time}")
    private String time;

    @Value("${limit.count}")
    private String count;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Override
    protected void doFilterInternal(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
        @NotNull FilterChain filterChain) throws IOException, ServletException {
        try {
            doBefore(request);
        } catch (ServiceException e) {
            logger.error(e.getMessage());
            Result<String> resultVo = Result.failure(ErrorResultCode.ACCESS_LIMIT_OVER_COUNT.value());
            WebUtils.rendString(response, JSON.toJSONString(resultVo));
            return;
        } catch (Exception e) {
            logger.error(e.getMessage());
            logger.error(e.getCause());
            Result<String> resultVo = Result.failure(ErrorResultCode.ACCESS_LIMIT_SERVER_ERROR.value());
            WebUtils.rendString(response, JSON.toJSONString(resultVo));
            return;
        }
        filterChain.doFilter(request, response);
    }

    public void doBefore(HttpServletRequest request) {
        String combineKey = getCombineKey(request);
        List<Object> keys = Collections.singletonList(combineKey);
        String script = "local key = '" + combineKey + "'\n" + "print(key)\n"
            + "local current = redis.call('get', key)\n" + "if current and tonumber(current) > " + Long.valueOf(count)
            + " then\n" + "    return tonumber(current)\n" + "end\n" + "current = redis.call('incr', key)\n"
            + "if tonumber(current) == 1 then\n" + "    redis.call('expire', key, " + Long.valueOf(time) + ")\n"
            + "end\n" + "return tonumber(current)";
        logger.info(script);
        DefaultRedisScript<Long> limitScript = new DefaultRedisScript<>();
        limitScript.setScriptText(script);
        limitScript.setResultType(Long.class);
        Long number = redisTemplate.execute(limitScript, keys, Integer.parseInt(count), Integer.parseInt(time));

        if (number == null || number.intValue() > Long.parseLong(count)) {
            throw new ServiceException(ErrorResultCode.ACCESS_LIMIT_OVER_COUNT.value());
        }
        log.info("限制请求'{}',当前请求'{}',缓存key'{}'", count, number.intValue(), key);
    }

    public String getCombineKey(HttpServletRequest request) {
        return (key + "_" + ClientUtil.getIp(request) + request.getRequestURI()).replace("/", "_").replace(".", "_");
    }

}
