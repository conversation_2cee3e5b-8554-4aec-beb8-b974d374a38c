package com.wifochina.modules.upload.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.upload.entity.ProjectFileEntity;
import com.wifochina.modules.upload.service.ProjectFileService;
import com.wifochina.modules.upload.vo.FileUploadVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.core.env.Environment;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件上传控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@RestController
@RequestMapping("/file")
@Api(tags = "文件上传管理")
@RequiredArgsConstructor
public class UploadController {

    private final ProjectFileService projectFileService;
    private final Environment environment;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @ApiOperation("上传文件")
    public Result<FileUploadVO> uploadFile(@RequestParam("file") MultipartFile file) {
        // 获取当前项目ID
        String projectId = WebUtils.projectId.get();
        Assert.hasLength(projectId, "projectId cannot be blank");

        // 判断当前服务是否是云端项目
        boolean cloudFlag = Boolean.TRUE.equals(environment.getProperty("ems.cloud", Boolean.class));

        String filePath;
        if (cloudFlag) {
            // 云端项目：走原有的OSS上传逻辑
            filePath = projectFileService.uploadImage(file, projectId);
        } else {
            // 非云端项目：将文件转换为base64保存到数据库
            filePath = projectFileService.uploadFileAsBase64(file, projectId);
        }

        // 构建响应对象
        FileUploadVO uploadVO = new FileUploadVO();
        uploadVO.setFilePath(filePath);
        uploadVO.setFileName(file.getOriginalFilename());
        uploadVO.setFileSize(file.getSize());
        uploadVO.setUploadTime(System.currentTimeMillis());
        return Result.success(uploadVO);
    }

    /**
     * 查询当前项目下所有文件列表
     */
    @GetMapping("/list")
    @ApiOperation("查询项目文件列表")
    public Result<List<ProjectFileEntity>> getProjectFiles() {
        // 获取当前项目ID
        String projectId = WebUtils.projectId.get();
        Assert.hasLength(projectId, "projectId cannot be blank");
        // 查询文件列表
        List<ProjectFileEntity> fileEntities = projectFileService.getFilesByProjectId(projectId);
        return Result.success(fileEntities);
    }
}
