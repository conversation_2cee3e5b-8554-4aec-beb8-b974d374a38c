package com.wifochina.modules.upload.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.config.OssMapper;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.upload.entity.ProjectFileEntity;
import com.wifochina.modules.upload.mapper.ProjectFileMapper;
import com.wifochina.modules.upload.service.ProjectFileService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

import javax.annotation.Resource;

/**
 * 项目文件服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class ProjectFileServiceImpl extends ServiceImpl<ProjectFileMapper, ProjectFileEntity>
        implements ProjectFileService {

    @Resource private OssMapper ossMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadImage(MultipartFile file, String projectId) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "文件不能为空");
        }

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "当前只支持图片文件上传");
        }

        // 验证文件大小（限制为10MB）
        long maxSize = 10 * 1024 * 1024;
        if (file.getSize() > maxSize) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "文件大小不能超过10MB");
        }

        try {
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

            String fileName = projectId + "/images/" + UUID.randomUUID() + fileExtension;

            // 上传到OSS
            ossMapper.putObject(fileName, file.getBytes());

            // 保存文件信息到数据库
            ProjectFileEntity fileEntity = new ProjectFileEntity();
            fileEntity.setProjectId(projectId);
            fileEntity.setFilePath(fileName);
            fileEntity.setFileName(originalFilename);
            fileEntity.setFileSize(file.getSize());
            fileEntity.setFileType(contentType);
            fileEntity.setFileExtension(fileExtension);

            this.save(fileEntity);

            // 返回文件访问路径
            return fileName;

        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "文件上传失败");
        }
    }

    @Override
    public List<ProjectFileEntity> getFilesByProjectId(String projectId) {
        LambdaQueryWrapper<ProjectFileEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectFileEntity::getProjectId, projectId);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadFileAsBase64(MultipartFile file, String projectId) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "文件不能为空");
        }

        // 验证文件大小（限制为10MB）
        long maxSize = 10 * 1024 * 1024;
        if (file.getSize() > maxSize) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "文件大小不能超过10MB");
        }

        try {
            // 获取文件信息
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

            // 将文件转换为base64
            byte[] fileBytes = file.getBytes();
            String base64Content = Base64.getEncoder().encodeToString(fileBytes);

            // 保存文件信息到数据库
            ProjectFileEntity fileEntity = new ProjectFileEntity();
            fileEntity.setProjectId(projectId);
            fileEntity.setFileName(originalFilename);
            fileEntity.setFileSize(file.getSize());
            fileEntity.setFileType(file.getContentType());
            fileEntity.setFileExtension(fileExtension);
            fileEntity.setFileContent(base64Content);

            this.save(fileEntity);

            // 返回文件ID
            return fileEntity.getId();

        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value(), "文件上传失败");
        }
    }
}
