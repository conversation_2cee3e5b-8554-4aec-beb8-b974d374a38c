package com.wifochina.modules.upload.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目文件响应VO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@ApiModel(value = "ProjectFileVO", description = "项目文件响应")
public class ProjectFileVO {

    /**
     * 文件ID
     */
    @ApiModelProperty(value = "文件ID")
    private String id;

    /**
     * 文件访问路径
     */
    @ApiModelProperty(value = "文件访问路径")
    private String filePath;

    /**
     * 原始文件名
     */
    @ApiModelProperty(value = "原始文件名")
    private String fileName;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 文件扩展名
     */
    @ApiModelProperty(value = "文件扩展名")
    private String fileExtension;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
}
