package com.wifochina.modules.upload.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件上传响应VO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@ApiModel(value = "FileUploadVO", description = "文件上传响应")
public class FileUploadVO {

    /**
     * 文件访问路径
     */
    @ApiModelProperty(value = "文件访问路径")
    private String filePath;

    /**
     * 原始文件名
     */
    @ApiModelProperty(value = "原始文件名")
    private String fileName;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Long uploadTime;
}
