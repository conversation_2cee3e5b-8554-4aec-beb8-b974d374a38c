package com.wifochina.modules.upload.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.upload.entity.ProjectFileEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 项目文件服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface ProjectFileService extends IService<ProjectFileEntity> {

    /**
     * 上传图片文件
     *
     * @param file 上传的文件
     * @param projectId 项目ID
     * @return 文件访问路径
     */
    String uploadImage(MultipartFile file, String projectId);

    /**
     * 根据项目ID查询文件列表
     *
     * @param projectId 项目ID
     * @return 文件列表
     */
    List<ProjectFileEntity> getFilesByProjectId(String projectId);
}
