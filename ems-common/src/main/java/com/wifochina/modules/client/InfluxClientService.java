package com.wifochina.modules.client;

import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.modules.group.entity.GroupEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * @since 2024-05-06 12:10 PM
 * <AUTHOR>
 */
public interface InfluxClientService {

    String getBucketGroup();

    String getBucketRealtime();

    String getBucketForever();

    String getBucketMean();

    String getBucketDemand();

    String getTableEms();

    String getTableMeter();

    String getTableDemand1mSliding();

    String getTableDemand15mFixed();

    String getTableDemand30mFixed();

    String getControllableTable(String projectId);

    String getEmsTable(String projectId);

    String getEmsGroupTable(String projectId);

    String getEmsT0Table(String projectId);

    String getMeterTable(String projectId);

    String getDemandTable(String projectId, GroupEntity systemGroup);

    String getCapacityTable(String projectId);

    String getCellTable(String projectId);

    QueryApi getQueryApi();

    WriteApiBlocking getWriteApi();

    String getDeviceKey();

    String getMeterKey();

    String getControllableKey();

    String getOrg();

    default List<Restrictions> createMeterRestrictions(List<String> meterList) {
        List<Restrictions> deviceRestrictions = new ArrayList<>(meterList.size());
        for (String meter : meterList) {
            deviceRestrictions.add(Restrictions.tag(this.getMeterKey()).equal(meter));
        }
        return deviceRestrictions;
    }
}
