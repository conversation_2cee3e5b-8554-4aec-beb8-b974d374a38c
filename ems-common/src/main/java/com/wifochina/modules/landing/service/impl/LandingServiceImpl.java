package com.wifochina.modules.landing.service.impl;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.landing.service.LandingService;
import com.wifochina.modules.monitor.service.MonitorService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.project.request.ZeroPowerContext;
import com.wifochina.modules.project.vo.EmsVO;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import javax.annotation.Resource;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
public class LandingServiceImpl implements LandingService {

    @Resource private DeviceService deviceService;

    @Resource private DataService dataService;

    @Resource private MonitorService monitorService;

    @Resource private PointListHolder pointListHolder;

    @Resource private GroupService groupService;

    @Override
    public Map<String, Object> getEmsByCode(String code) throws ExecutionException {
        DeviceEntity deviceEntity =
                deviceService.lambdaQuery().eq(DeviceEntity::getCode, code).one();
        if (deviceEntity == null) {
            throw new ServiceException(ErrorResultCode.DEVICE_NOT_EXISTS.value());
        }

        GroupEntity systemGroupEntity = groupService.systemGroupEntity(deviceEntity.getProjectId());
        // 获取最新的数据
        int[] data = dataService.get(deviceEntity.getId(), deviceEntity.getProjectId());
        EmsVO emsVO = new EmsVO();
        if (data == null) {
            emsVO.setName(deviceEntity.getName());
            emsVO.setEmsStatus(0);
        } else {
            int emsType = data[42];
            int highOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType)];
            int lowOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType) + 1];
            long out = ((long) highOut << 16) + lowOut;
            double emsOutTotal = (out * 1.0 / 10);
            int highIn = data[pointListHolder.getEmsHistoryInputEnergy(emsType)];
            int lowIn = data[pointListHolder.getEmsHistoryInputEnergy(emsType) + 1];
            long in = ((long) highIn << 16) + lowIn;
            double emsInTotal = in * 1.0 / 10;
            double emsInPower = 0;
            double emsOutPower = 0;
            double emsReactiveInPower = 0;
            double emsReactiveOutPower = 0;
            double point =
                    ((double) (short) data[pointListHolder.getEmsAcActivePower(emsType)]) / 10;
            if (Boolean.TRUE.equals(systemGroupEntity.getHomePageReactivePowerController())) {
                double reActivePoint =
                        ((double) (short) data[pointListHolder.getEmsAcReactivePower(emsType)])
                                / 10;
                if (reActivePoint > 0) {
                    // 放电
                    emsReactiveOutPower += reActivePoint;
                } else {
                    // 充电
                    emsReactiveInPower += Math.abs(reActivePoint);
                }
            }
            boolean hasEmsRun = false;
            if (point > 0) {
                // 放电
                emsOutPower = point;
            } else {
                // 充电
                emsInPower = Math.abs(point);
            }
            // 是否有机器开机
            if (data[pointListHolder.getSystemRunStatus(emsType)] > 0) {
                hasEmsRun = true;
            }
            double designPower = data[pointListHolder.getEmsDesignPower(emsType)];
            // 0离线 1待机 2充电 3放电 4停机
            int status;
            ZeroPowerContext zeroPowerContext = new ZeroPowerContext();
            zeroPowerContext.setOpenZeroController(
                    systemGroupEntity.getHomePageZeroPowerController());
            // 这边都是新增加的 适配 开了首页无功开关
            if (Boolean.TRUE.equals(systemGroupEntity.getHomePageReactivePowerController())) {
                status =
                        EmsUtil.getEmsStatusSupportReactivePower(
                                designPower,
                                emsInPower,
                                emsOutPower,
                                emsReactiveInPower,
                                emsReactiveOutPower,
                                false,
                                hasEmsRun,
                                zeroPowerContext);
            } else {
                status =
                        EmsUtil.getEmsStatus(
                                designPower,
                                emsInPower,
                                emsOutPower,
                                false,
                                hasEmsRun,
                                zeroPowerContext);
            }
            //            int status =
            //                    EmsUtil.getEmsStatus(
            //                            designPower, emsInPower, emsOutPower, false, hasEmsRun,
            // null);
            // ems
            emsVO.setEmsOutPower(emsOutPower);
            emsVO.setEmsOutTotal(emsOutTotal);
            emsVO.setEmsInPower(emsInPower);
            emsVO.setEmsAcReactiveInPower(emsReactiveInPower);
            emsVO.setEmsAcReactiveOutPower(emsReactiveOutPower);
            if (zeroPowerContext.isSetAcReactiveZero()) {
                emsVO.setDiffAcReactivePower(0d);
            } else {
                emsVO.setDiffAcReactivePower(
                        emsVO.getEmsAcReactiveOutPower() - emsVO.getEmsAcReactiveInPower());
            }
            emsVO.setEmsInTotal(emsInTotal);
            emsVO.setEmsStatus(status);
            emsVO.setName(deviceEntity.getName());
            emsVO.setSoc(data[pointListHolder.getSoc(emsType)] * 1.0 / 10);
        }
        emsVO.setId(deviceEntity.getId());
        Map<String, Map<String, Object>> statusMap =
                monitorService.getOneEmsRunStatus(
                        deviceEntity.getProjectId(),
                        deviceEntity.getId(),
                        SecurityUtil.hasAuthority(EmsConstants.MESSAGE_SHOW_HIDE),
                        SecurityUtil.hasAuthority(EmsConstants.DEVICE_MAINTAIN_AUTHORITY));
        Map<String, Object> map = statusMap.get(deviceEntity.getId());
        String[] keys = new String[] {"system", "pcs", "bms", "air", "fire", "water"};
        EventCodeEntity eventCodeEntity = null;
        label:
        for (String key : keys) {
            Map<Integer, Map<String, Object>> resultMap = castMap(map.get(key));
            if (resultMap != null) {
                for (Integer i : resultMap.keySet()) {
                    Map<String, Object> map1 = resultMap.get(i);
                    int fault = (int) map1.get("fault_count");
                    if (fault > 0) {
                        List<EventCodeEntity> list =
                                castList(map1.get("fault"), EventCodeEntity.class);
                        eventCodeEntity = list.get(0);
                        break label;
                    }
                    int alarm = (int) map1.get("alarm_count");
                    if (alarm > 0) {
                        if (eventCodeEntity == null) {
                            List<EventCodeEntity> list =
                                    castList(map1.get("alarm"), EventCodeEntity.class);
                            eventCodeEntity = list.get(0);
                        }
                    }
                }
            }
        }
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("ems", emsVO);
        resultMap.put("error", eventCodeEntity);
        return resultMap;
    }

    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }

    public static Map<Integer, Map<String, Object>> castMap(Object obj) {
        if (obj instanceof Map<?, ?>) {
            Map<?, ?> integerMap = (Map<?, ?>) obj;
            Map<Integer, Map<String, Object>> result = new HashMap<>(integerMap.size());
            for (Object key : integerMap.keySet()) {
                if (key instanceof Integer) {
                    Map<?, ?> stringMap = (Map<?, ?>) integerMap.get(key);
                    Map<String, Object> stringResMap = new HashMap<>(stringMap.size());
                    for (Object stringKey : stringMap.keySet()) {
                        if (stringKey instanceof String) {
                            stringResMap.put((String) stringKey, stringMap.get(stringKey));
                        }
                    }
                    result.put((Integer) key, stringResMap);
                }
            }
            return result;
        }
        return null;
    }
}
