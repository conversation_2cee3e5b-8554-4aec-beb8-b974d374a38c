package com.wifochina.modules.landing.controller;

import java.util.Map;
import java.util.concurrent.ExecutionException;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.page.Result;
import com.wifochina.modules.landing.request.CodeRequest;
import com.wifochina.modules.landing.service.LandingService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2023-02-02 9:36 AM
 */
@RequestMapping("/landing")
@RestController
@Api(tags = "99-落地页")
public class LandingController {

    @Resource
    private LandingService landingService;

    @Resource
    private ProjectService projectService;

    /**
     * welcome page
     */
    @PostMapping("/code")
    @ApiOperation("二维码")
    public Result<Map<String, Object>> getEmsByCode(@RequestBody CodeRequest codeRequest)
        throws CloneNotSupportedException, ExecutionException {
        Map<String, Object> map = landingService.getEmsByCode(codeRequest.getCode());
        return Result.success(map);
    }

    /**
     * get local ip
     */
    @PostMapping("/host")
    @ApiOperation("获取本地项目ip")
    public Result<String> getHostByProjectId() {
        ProjectEntity projectEntity = getProject();
        return Result.success(projectEntity.getProjectHost());
    }

    private ProjectEntity getProject() {
        return projectService.lambdaQuery().one();
    }
}
