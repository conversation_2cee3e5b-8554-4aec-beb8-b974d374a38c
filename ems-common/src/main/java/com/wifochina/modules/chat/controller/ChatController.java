package com.wifochina.modules.chat.controller;


import com.wifochina.common.page.Result;
import com.wifochina.modules.chat.client.ChatClient;
import com.wifochina.modules.chat.dtos.ChatHistoryDTO;
import com.wifochina.modules.chat.request.ChatCompletionsRequest;
import com.wifochina.modules.chat.request.ChatHistoryRequest;
import com.wifochina.modules.chat.service.ChatSessionMessageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/3/19 15:50
 * @version 1.0
 */
@Api(tags = "31-AI对话")
@RestController
@RequestMapping("/chat")
public class ChatController {

    @Resource private ChatClient chatClient;
    @Resource private ChatSessionMessageService chatSessionMessageService;

    @PostMapping("/completion")
    @ApiOperation("对话请求")
    public void chatCompletion(@RequestBody ChatCompletionsRequest request) {
        chatClient.chatCompletion(request);
    }

    @PostMapping("/history")
    @ApiOperation("对话记录")
    public Result<ChatHistoryDTO> chatHistory(@RequestBody @Valid ChatHistoryRequest request) {
        ChatHistoryDTO result = chatSessionMessageService.chatHistory(request);
        return Result.success(result);
    }
}
