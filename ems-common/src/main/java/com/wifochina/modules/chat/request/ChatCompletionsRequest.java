package com.wifochina.modules.chat.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 对话请求参数
 *
 * <AUTHOR>
 * @date 2025/3/19 17:02
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatCompletionsRequest {

    @ApiModelProperty(value = "会话id")
    private Long sessionId;

    @ApiModelProperty(value = "大模型：deepseek-r1|deepseek-v3")
    @NotBlank(message = "model cannot be null")
    private String model;

    @ApiModelProperty(value = "对话内容")
    private String content;

}
