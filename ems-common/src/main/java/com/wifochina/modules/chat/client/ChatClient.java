package com.wifochina.modules.chat.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.chat.platform.openai.chat.entity.ChatCompletion;
import com.weihengtech.chat.platform.openai.chat.entity.ChatMessage;
import com.weihengtech.chat.platform.openai.chat.entity.StreamOptions;
import com.weihengtech.chat.service.IChatService;
import com.weihengtech.chat.service.PlatformType;
import com.weihengtech.chat.service.factor.AiService;
import com.wifochina.common.config.SystemConfiguration;
import com.wifochina.common.constants.TimePointEnum;
import com.wifochina.common.handler.MessageSourceHandler;
import com.wifochina.modules.chat.constants.ChatConstant;
import com.wifochina.modules.chat.constants.ChatRoleEnum;
import com.wifochina.modules.chat.dtos.ChatHistoryDTO;
import com.wifochina.modules.chat.dtos.ChatPromptDTO;
import com.wifochina.modules.chat.entity.ChatSession;
import com.wifochina.modules.chat.request.ChatCompletionsRequest;
import com.wifochina.modules.chat.request.ChatHistoryRequest;
import com.wifochina.modules.chat.service.ChatSessionMessageService;
import com.wifochina.modules.chat.service.ChatSessionService;
import com.wifochina.modules.homepage.service.HomePageService;
import com.wifochina.modules.income.NewOperationProfitService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;

import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectScreenService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.project.vo.BasicVO;
import com.wifochina.modules.project.vo.SiteVO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 对话客户端（业务侧）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/17 18:59
 */
@Service
@Slf4j
public class ChatClient {

    @Resource private AiService aiService;
    @Resource private RedisTemplate<String, Integer> redisTemplate;
    @Resource private ChatSessionService chatSessionService;
    @Resource private ChatSessionMessageService chatSessionMessageService;
    @Resource private SystemConfiguration systemConfiguration;
    @Resource private NewOperationProfitService newOperationProfitService;
    @Resource private HomePageService homePageService;
    @Resource private ProjectService projectService;
    @Resource private ProjectScreenService projectScreenService;

    /**
     * 发起对话请求：参数、请求频率校验，构建大模型参数，发送请求，处理响应
     *
     * @param request request
     */
    public void chatCompletion(ChatCompletionsRequest request) {
        // 校验sessionId合法性，抛出异常
        checkSessionItem(request.getSessionId());
        try {
            // 校验请求长度
            Assert.isTrue(
                    request.getContent().length()
                            <= systemConfiguration.getAi().getChat().getContentLimit(),
                    "content length limit");
            // 校验请求频率
            checkChatNumLimit(SecurityUtil.getUserId());
            // 获取chat服务实例
            IChatService chatService = aiService.getChatService(PlatformType.DEEPSEEK);
            // 构造请求参数
            ChatCompletion chatCompletion =
                    createChatCompletion(request.getModel(), request.getContent());
            // 发送SSE请求
            WebSseListener webSseListener =
                    new WebSseListener(
                            chatSessionService,
                            chatSessionMessageService,
                            request.getContent(),
                            request.getSessionId());
            chatService.chatCompletionStream(chatCompletion, webSseListener);
        } catch (Exception e) {
            // 将异常以流式输出返回
            HttpServletResponse response = getCurrHttpResponse();
            WebSseListener.handleError(response, e);
        }
    }

    /** 获取当前请求中的HttpServletResponse */
    private HttpServletResponse getCurrHttpResponse() {
        // 获取当前线程的ServletRequestAttributes
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        // 返回HttpServletResponse
        return attributes.getResponse();
    }

    /** 获取当前请求中的HttpServletRequest */
    private HttpServletRequest getCurrHttpRequest() {
        // 获取当前线程的ServletRequestAttributes
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        // 返回HttpServletResponse
        return attributes.getRequest();
    }

    /** 构建对话请求对象 */
    private ChatCompletion createChatCompletion(String model, String chatContent) {
        HttpServletRequest currHttpRequest = getCurrHttpRequest();
        String language =
                Optional.ofNullable(currHttpRequest.getHeader("Accept-Language")).orElse("zh_CN");
        String prompt = MessageSourceHandler.getMessageExternalLanguage("CHAT_PROMPT", language);
        Map<String, Object> benefitMap =
                newOperationProfitService.getTotalOperationProfit(
                        false,
                        WebUtils.projectId.get(),
                        TimePointEnum.TODAY,
                        TimePointEnum.YESTERDAY,
                        TimePointEnum.TOTAL);
        ChatPromptDTO chatPromptInfo = buildBasicInfo();
        ChatHistoryDTO chatHistory =
                chatSessionMessageService.chatHistory(
                        ChatHistoryRequest.builder()
                                .messageId(0L)
                                .size(systemConfiguration.getAi().getChat().getContextSize())
                                .build());
        List<ChatMessage> messages = new ArrayList<>();
        messages.add(
                ChatMessage.builder()
                        .role(ChatRoleEnum.USER.getRole())
                        .content(String.format(prompt, JSONUtil.toJsonStr(chatPromptInfo), JSONUtil.toJsonStr(benefitMap)))
                        .build());
        messages.add(
                ChatMessage.builder()
                        .role(ChatRoleEnum.ASSISTANT.getRole())
                        .content(ChatConstant.AI_ANSWER)
                        .build());
        // 添加历史上下文消息
        if (CollUtil.isNotEmpty(chatHistory.getMessages())) {
            for(int i = chatHistory.getMessages().size() - 1; i >= 0; i--) {
                ChatHistoryDTO.ChatMessageDTO message = chatHistory.getMessages().get(i);
                messages.add(ChatMessage.builder()
                                .role(ChatRoleEnum.getRoleByCode(message.getType()))
                                .content(message.getContent())
                        .build());
            }
        }
        messages.add(
                ChatMessage.builder()
                        .role(ChatRoleEnum.USER.getRole())
                        .content(chatContent)
                        .build());
        return ChatCompletion.builder()
                .model(model)
                .streamOptions(new StreamOptions())
                .messages(messages)
                .build();
    }

    /** 构建基础信息 */
    private ChatPromptDTO buildBasicInfo() {
        // 项目信息：投运时间、安全运行天数
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectInfo = projectService.getById(projectId);
        // 基本情况
        BasicVO basicInfo = homePageService.getBasicInfo();
        long dayStartTime = Instant.now()
                .atZone(ZoneId.of(projectInfo.getTimezone()))
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0)
                .toEpochSecond();
        // 大屏信息
        SiteVO siteInfo = projectScreenService.getSite(dayStartTime);
        // 信息汇总
        ChatPromptDTO param = ChatPromptDTO.builder().build();
        BeanUtils.copyProperties(basicInfo, param);
        BeanUtils.copyProperties(siteInfo, param);
        param.setCreateTime(projectInfo.getCreateTime());
        param.setSafeOperationDays((
                Instant.now().getEpochSecond() -
                        projectInfo.getCreateTime())
                / (60 * 60 * 24));
        return param;
    }

    /** 校验会话合法性 */
    private void checkSessionItem(Long sessionId) {
        // 首次请求
        if (ChatConstant.FIRST_SESSION_ID.equals(sessionId)) {
            return;
        }
        ChatSession session = chatSessionService.getById(sessionId);
        Assert.notNull(session, "session not exists");
    }

    /** 校验请求频率 */
    private void checkChatNumLimit(String userId) {
        String redisKey = "chat:" + userId;
        Long currentCount = redisTemplate.opsForValue().increment(redisKey);
        if (currentCount != null && currentCount == 1) {
            redisTemplate.expire(redisKey, 24, TimeUnit.HOURS);
        }
        if (currentCount != null
                && currentCount > systemConfiguration.getAi().getChat().getRequestTimesLimit()) {
            throw new RuntimeException("The number of daily chats has exceeded the limit");
        }
    }
}
