package com.wifochina.modules.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.chat.constants.ChatRoleEnum;
import com.wifochina.modules.chat.dtos.ChatHistoryDTO;
import com.wifochina.modules.chat.entity.ChatSessionMessage;
import com.wifochina.modules.chat.request.ChatHistoryRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:09
 * @version 1.0
 */
public interface ChatSessionMessageService extends IService<ChatSessionMessage> {

    ChatSessionMessage queryLastMessageBySessionId(Long sessionId);

    void handleMessages(
            Long sessionId,
            String question,
            Long reasoningTime,
            String reasoningContent,
            String answer);

    ChatSessionMessage createMessage(
            Long sessionId,
            ChatRoleEnum role,
            String content,
            Long reasoningTime,
            String reasoningContent,
            Long parentId);

    ChatHistoryDTO chatHistory(ChatHistoryRequest request);
}
