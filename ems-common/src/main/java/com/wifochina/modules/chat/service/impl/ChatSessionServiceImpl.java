package com.wifochina.modules.chat.service.impl;

import cn.hutool.core.util.StrUtil;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.util.SnowFlakeUtil;
import com.wifochina.modules.chat.constants.ChatConstant;
import com.wifochina.modules.chat.entity.ChatSession;
import com.wifochina.modules.chat.mapper.ChatSessionMapper;
import com.wifochina.modules.chat.service.ChatSessionService;
import com.wifochina.modules.oauth.util.SecurityUtil;

import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:09
 * @version 1.0
 */
@Service
public class ChatSessionServiceImpl extends ServiceImpl<ChatSessionMapper, ChatSession>
        implements ChatSessionService {

    @Resource private SnowFlakeUtil snowFlakeUtil;

    @Override
    public ChatSession queryChatSessionByChatId(String chatId) {
        if (StrUtil.isBlank(chatId)) {
            return null;
        }
        // 通过chatId查询会话
        return getOne(Wrappers.<ChatSession>lambdaQuery().eq(ChatSession::getChatId, chatId));
    }

    @Override
    public List<ChatSession> queryChatSessionByUserId() {
        // 通过userId查询总会话，根据给updateTime排序
        return list(
                Wrappers.<ChatSession>lambdaQuery()
                        .eq(ChatSession::getUserId, SecurityUtil.getUserId())
                        .orderByDesc(ChatSession::getUpdateTime));
    }

    @Override
    public Long handleSession(String chatId, String userId, Long sessionId) {
        ChatSession chatSession;
        if (ChatConstant.FIRST_SESSION_ID.equals(sessionId)) {
            chatSession =
                    ChatSession.builder()
                            .id(snowFlakeUtil.generateId())
                            .chatId(chatId)
                            .userId(userId)
                            .createTime(new Date())
                            .updateTime(new Date())
                            .build();
            this.save(chatSession);
        } else {
            chatSession = getById(sessionId);
            chatSession.setUpdateTime(new Date());
            chatSession.setChatId(chatId);
            this.updateById(chatSession);
        }
        return chatSession.getId();
    }
}
