package com.wifochina.modules.chat.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:08
 * @version 1.0
 */
@Getter
public enum ChatRoleEnum {

    /**
     * AI问答角色枚举类
     */
    SYSTEM(0, "system"), ASSISTANT(1, "assistant"), USER(2, "user"),
    TOOL(3, "tool"), FUNCTION(4, "function"), OTHER(5, "other");
    int code;
    String role;

    ChatRoleEnum(int code, String role) {
        this.code = code;
        this.role = role;
    }

    public static String getRoleByCode(int code) {
        for (ChatRoleEnum roleEnum : ChatRoleEnum.values()) {
            if (roleEnum.code == code) {
                return roleEnum.role;
            }
        }
        // 如果没有找到匹配的code，可以返回一个默认值或者抛出异常
        return OTHER.role;
    }
}
