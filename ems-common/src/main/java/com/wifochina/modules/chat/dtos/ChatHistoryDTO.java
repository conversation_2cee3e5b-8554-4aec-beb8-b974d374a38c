package com.wifochina.modules.chat.dtos;

import com.wifochina.modules.chat.entity.ChatSessionMessage;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/19 19:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatHistoryDTO {
    
    @ApiModelProperty(name = "sessionId", value = "会话ID")
    private String sessionId;
    
    @ApiModelProperty(name = "hasNext", value = "是否有下一页")
    private Boolean hasNext;

    @ApiModelProperty(name = "messages", value = "消息列表")
    private List<ChatMessageDTO> messages;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ChatMessageDTO {
        private String id;

        private String sessionId;

        private Integer type;

        private String parentId;

        private BigDecimal reasoningTime;

        private String reasoningContent;

        private String content;

        private Date createTime;
    }

    
}
