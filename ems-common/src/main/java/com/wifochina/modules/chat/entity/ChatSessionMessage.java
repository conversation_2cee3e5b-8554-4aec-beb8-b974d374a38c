package com.wifochina.modules.chat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:09
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("t_chat_session_message")
public class ChatSessionMessage {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private Long sessionId;

    private Integer type;

    private Long parentId;

    private BigDecimal reasoningTime;

    private String reasoningContent;

    private String content;

    private Date createTime;
}
