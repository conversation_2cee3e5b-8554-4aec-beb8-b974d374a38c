package com.wifochina.modules.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.chat.entity.ChatSession;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
public interface ChatSessionService extends IService<ChatSession> {

    /**
     * 根据chatId查询会话
     *
     * @param chatId chatId
     * @return 会话
     */
    ChatSession queryChatSessionByChatId(String chatId);

    /**
     * 通过userId查询总会话，根据给updateTime排序
     *
     * @return 会话列表
     */
    List<ChatSession> queryChatSessionByUserId();

    /**
     * 保存|更新session数据
     *
     * @param chatId 对话Id
     * @param userId 用户Id
     * @param sessionId 会话Id
     * @return 会话Id
     */
    Long handleSession(String chatId, String userId, Long sessionId);
}
