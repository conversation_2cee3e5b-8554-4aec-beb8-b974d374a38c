package com.wifochina.modules.chat.dtos;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/19 19:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatPromptDTO {

    // 基本情况
    @ApiModelProperty(value = "电站额定功率")
    private Double emsDesignPower;

    @ApiModelProperty(value = "电站可充功率")
    private Double emsChargePower;

    @ApiModelProperty(value = "电站可放功率")
    private Double emsDischargePower;

    @ApiModelProperty(value = "电站额定容量")
    private Double emsCapacity;

    @ApiModelProperty(value = "电站pcs数量")
    private Double pscNum;

    @ApiModelProperty(value = "电站电池簇数量")
    private Double bmsCluster;

    @ApiModelProperty(value = "当前可充容量")
    private Double currentChargeCapacity;

    @ApiModelProperty(value = "当前可放容量")
    private Double currentDischargeCapacity;

    @ApiModelProperty(value = "SOH")
    private Double SOH;

    @ApiModelProperty(value = "SOC")
    private Double SOC;

    @ApiModelProperty(value = "Ems状态")
    private Integer emsStatus;

    @ApiModelProperty(value = "ems充电功率")
    private Double emsInPower;

    @ApiModelProperty(value = "ems放电功率")
    private Double emsOutPower;

    // 项目信息
    @ApiModelProperty("投运时间")
    private Long createTime;

    @ApiModelProperty("安全运行天数")
    private Long safeOperationDays;

    @ApiModelProperty("项目时区")
    private String timezone;

    // 大屏信息
    @ApiModelProperty(value = "ems日充电量")
    private Double emsInDaily;

    @ApiModelProperty(value = "ems日放电量")
    private Double emsOutDaily;

    @ApiModelProperty(value = "pv总发电量")
    private Double pvTotal;

    @ApiModelProperty(value = "pv日发电量")
    private Double pvDaily;

    @ApiModelProperty(value = "电网总用电量")
    private Double gridOutTotal;

    @ApiModelProperty(value = "电网总馈网量")
    private Double gridInTotal;

}
