package com.wifochina.modules.chat.client;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONUtil;

import com.weihengtech.chat.listener.SseListener;
import com.weihengtech.chat.platform.openai.usage.Usage;
import com.wifochina.modules.chat.dtos.ChatErrorDTO;
import com.wifochina.modules.chat.service.ChatSessionMessageService;
import com.wifochina.modules.chat.service.ChatSessionService;
import com.wifochina.modules.oauth.util.SecurityUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;

import javax.servlet.http.HttpServletResponse;

/**
 * sse服务监听器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/17 19:16
 */
@Slf4j
public class WebSseListener extends SseListener {

    private final ChatSessionService chatSessionService;
    private final ChatSessionMessageService chatSessionMessageService;
    private final HttpServletResponse rp;
    private final String userId;
    private final Long sessionId;
    private final String question;

    public WebSseListener(
            ChatSessionService chatSessionService,
            ChatSessionMessageService chatSessionMessageService,
            String question,
            Long sessionId) {
        this.chatSessionService = chatSessionService;
        this.chatSessionMessageService = chatSessionMessageService;
        this.rp = getCurrHttpResponse();
        this.userId = SecurityUtil.getUserId();
        this.question = question;
        this.sessionId = sessionId;
    }

    /** 获取当前请求中的HttpServletResponse */
    private HttpServletResponse getCurrHttpResponse() {
        // 获取当前线程的ServletRequestAttributes
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        // 返回HttpServletResponse
        return attributes.getResponse();
    }

    @Override
    protected void initConnection() {
        rp.setContentType("text/event-stream");
        rp.setCharacterEncoding("UTF-8");
        rp.setStatus(200);
    }

    @Override
    protected void sendCurrMsg(String data) {
        try {
            rp.getWriter().write("data:" + data + "\n\n");
            rp.getWriter().flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    protected void handleOutput(
            String chatId,
            Long reasoningTime,
            StringBuilder reasoningOutput,
            StringBuilder output) {
        if (StrUtil.isBlank(chatId)) {
            log.error("chatId is null");
            return;
        }
        String title = "New Session";
        // todo 请求AI生成标题
        // 处理会话的保存逻辑
        Long curSessionId = chatSessionService.handleSession(chatId, userId, this.sessionId);
        // 保存问题和回答进数据库
        chatSessionMessageService.handleMessages(
                curSessionId,
                question,
                reasoningTime,
                reasoningOutput.toString(),
                output.toString());
    }

    @Override
    protected void handleUsage(Usage usage) {
        log.info("usage: {}", usage);
    }

    @Override
    protected void handleError(Throwable exception) {
        handleError(rp, exception);
    }

    /**
     * 流式异常处理
     *
     * @param rp
     * @param exception
     */
    public static void handleError(HttpServletResponse rp, Throwable exception) {
        rp.setContentType(ContentType.EVENT_STREAM.getValue());
        rp.setCharacterEncoding("UTF-8");
        try {
            ChatErrorDTO errItem =
                    ChatErrorDTO.builder().code("error").message(exception.getMessage()).build();
            rp.getWriter().write("data:" + JSONUtil.toJsonStr(errItem) + "\n\n");
            rp.getWriter().flush();
        } catch (IOException e) {
            log.error("sendStreamError error", e);
        }
    }
}
