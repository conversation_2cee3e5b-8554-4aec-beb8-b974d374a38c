package com.wifochina.modules.chat.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 对话请求参数
 *
 * <AUTHOR>
 * @date 2025/3/19 17:02
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatHistoryRequest {

    @ApiModelProperty(value = "会话id")
    private Long sessionId;

    @ApiModelProperty(value = "会话id")
    @NotNull
    private Long messageId;

    @ApiModelProperty(name = "size", value = "查询会话条数，默认10条")
    @NotNull
    private Integer size;

}
