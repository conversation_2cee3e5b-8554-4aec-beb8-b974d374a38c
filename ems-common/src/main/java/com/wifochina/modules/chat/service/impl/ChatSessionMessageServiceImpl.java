package com.wifochina.modules.chat.service.impl;

import cn.hutool.core.collection.CollUtil;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.util.SnowFlakeUtil;
import com.wifochina.modules.chat.constants.ChatRoleEnum;
import com.wifochina.modules.chat.dtos.ChatHistoryDTO;
import com.wifochina.modules.chat.entity.ChatSession;
import com.wifochina.modules.chat.entity.ChatSessionMessage;
import com.wifochina.modules.chat.mapper.ChatSessionMessageMapper;
import com.wifochina.modules.chat.request.ChatHistoryRequest;
import com.wifochina.modules.chat.service.ChatSessionMessageService;
import com.wifochina.modules.chat.service.ChatSessionService;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:09
 * @version 1.0
 */
@Service
public class ChatSessionMessageServiceImpl
        extends ServiceImpl<ChatSessionMessageMapper, ChatSessionMessage>
        implements ChatSessionMessageService {

    @Resource private SnowFlakeUtil snowFlakeUtil;
    @Resource private ChatSessionService chatSessionService;

    @Override
    public ChatSessionMessage queryLastMessageBySessionId(Long sessionId) {
        return getOne(
                Wrappers.<ChatSessionMessage>lambdaQuery()
                        .eq(ChatSessionMessage::getSessionId, sessionId)
                        .ne(ChatSessionMessage::getType, ChatRoleEnum.USER.getCode())
                        .orderByDesc(ChatSessionMessage::getCreateTime)
                        .last("limit 1"));
    }

    @Override
    public void handleMessages(
            Long sessionId,
            String question,
            Long reasoningTime,
            String reasoningContent,
            String answer) {
        // 查询该会话的最后一条消息,得到消息的父ID
        ChatSessionMessage lastMessage = queryLastMessageBySessionId(sessionId);
        Long fatherMsgId = lastMessage != null ? lastMessage.getId() : 0L;
        // 用户问题
        ChatSessionMessage userMessage =
                createMessage(sessionId, ChatRoleEnum.USER, question, null, null, fatherMsgId);
        save(userMessage);
        // AI回答
        ChatSessionMessage aiMessage =
                createMessage(
                        sessionId,
                        ChatRoleEnum.ASSISTANT,
                        answer,
                        reasoningTime,
                        reasoningContent,
                        userMessage.getId());
        save(aiMessage);
    }

    @Override
    public ChatSessionMessage createMessage(
            Long sessionId,
            ChatRoleEnum role,
            String content,
            Long reasoningTime,
            String reasoningContent,
            Long parentId) {
        ChatSessionMessage item =
                ChatSessionMessage.builder()
                        .id(snowFlakeUtil.generateId())
                        .sessionId(sessionId)
                        .content(content)
                        .parentId(parentId)
                        .type(role.getCode())
                        .createTime(new Date())
                        .build();
        if (reasoningTime != null && reasoningContent != null) {
            item.setReasoningTime(new BigDecimal(reasoningTime));
            item.setReasoningContent(reasoningContent);
        }
        return item;
    }

    @Override
    public ChatHistoryDTO chatHistory(ChatHistoryRequest request) {
        Long sessionId = request.getSessionId();
        if (request.getSessionId() == null || request.getSessionId() == 0) {
            List<ChatSession> chatSessions = chatSessionService.queryChatSessionByUserId();
            if (CollUtil.isEmpty(chatSessions)) {
                return ChatHistoryDTO.builder()
                        .hasNext(false)
                        .sessionId(Optional.ofNullable(sessionId).map(String::valueOf).orElse(null))
                        .messages(Collections.emptyList())
                        .build();
            }
            sessionId = chatSessions.get(0).getId();
        }

        // 查询sessionId某个messageID的上面10条数据
        List<ChatSessionMessage> chatMessages = queryLastSizeMessages(request, sessionId);
        // 判断是否有下一页
        boolean hasNext = chatMessages.size() > request.getSize();
        // 如果有多于 size 条记录，移除多余的记录
        if (hasNext) {
            chatMessages.remove(chatMessages.size() - 1);
        }
        List<ChatHistoryDTO.ChatMessageDTO> messageList = chatMessages.stream()
                .map(i -> ChatHistoryDTO.ChatMessageDTO.builder()
                            .id(String.valueOf(i.getId()))
                            .sessionId(String.valueOf(i.getSessionId()))
                            .parentId(String.valueOf(i.getParentId()))
                            .type(i.getType())
                            .reasoningTime(i.getReasoningTime())
                            .reasoningContent(i.getReasoningContent())
                            .content(i.getContent())
                            .createTime(i.getCreateTime())
                            .build())
                .collect(Collectors.toList());
        return ChatHistoryDTO.builder()
                .hasNext(hasNext)
                .sessionId(String.valueOf(sessionId))
                .messages(messageList)
                .build();
    }

    /** 按时间倒排查询最近10条记录 */
    private List<ChatSessionMessage> queryLastSizeMessages(
            ChatHistoryRequest request, Long sessionId) {
        return list(
                Wrappers.<ChatSessionMessage>lambdaQuery()
                        .eq(ChatSessionMessage::getSessionId, sessionId)
                        .lt(
                                request.getMessageId() > 0,
                                ChatSessionMessage::getId,
                                request.getMessageId())
                        .orderByDesc(ChatSessionMessage::getCreateTime)
                        .orderByDesc(ChatSessionMessage::getId)
                        // 取 size + 1 条记录以判断是否有下一页
                        .last("LIMIT " + (request.getSize() + 1)));
    }
}
