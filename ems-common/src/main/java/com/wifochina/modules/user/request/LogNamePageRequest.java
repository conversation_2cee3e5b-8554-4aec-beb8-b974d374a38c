package com.wifochina.modules.user.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 日志分页请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 16:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LogNamePageRequest extends PageBean {

    @ApiModelProperty(value = "用户名或者姓名")
    private String name;

    @ApiModelProperty(value = "用户名或者姓名")
    private String projectName;

    @ApiModelProperty(value = "开始时间")
    private Long start;

    @ApiModelProperty(value = "结束时间")
    private Long end;
}
