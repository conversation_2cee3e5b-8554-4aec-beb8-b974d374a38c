package com.wifochina.modules.user.vo;

import java.util.List;

import com.wifochina.modules.user.entity.AuthorityEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * RoleRequest
 * 
 * @date 3/25/2022 5:33 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "RoleVO", description = "角色权限列表")
public class RoleVO {

    private String id;

    @ApiModelProperty(value = "角色名称", required = true)
    private String roleName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "权限列表", required = true)
    private List<AuthorityEntity> authorities;
}
