package com.wifochina.modules.user.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * UserPhoneRequest
 *
 * @date 10/28/2022 2:28 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class UserSelfPhoneRequest {

    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    @ApiModelProperty(value = "验证码", required = true)
    private String code;

    /** 2024-03-22 09:35:22 add 新增邮箱的绑定 替代以前的默认的 手机号绑定 */
    @ApiModelProperty(value = "邮箱", required = true)
    private String email;
}
