package com.wifochina.modules.user.entity;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "t_authority")
public class AuthorityEntity {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "父id")
    private Long pid;

    @ApiModelProperty(value = "接入点标识（格式   {模块}:{菜单}:{功能}）")
    private String apKey;

    @ApiModelProperty(value = "接入点名称")
    private String name;

    @TableField(exist = false)
    private List<AuthorityEntity> authorityList;

}