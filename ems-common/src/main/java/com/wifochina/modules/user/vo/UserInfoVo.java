package com.wifochina.modules.user.vo;

import java.util.List;

import com.wifochina.modules.user.entity.AuthorityEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel(value = "UserInfoVo", description = "用户详情Vo")
@Data
public class UserInfoVo {

    @ApiModelProperty(value = "用户uuid")
    private String uuid;

    @ApiModelProperty(value = "用户账号")
    private String userName;

    @ApiModelProperty(value = "用户邮箱")
    private String email;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "用户手机号")
    private String phone;

    @ApiModelProperty(value = "国家代码")
    private String countryCode;

    @ApiModelProperty(value = "国家号码")
    private String nationalNumber;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "邮编")
    private String zipCode;

    @ApiModelProperty(value = "语言")
    private Integer language;

    @ApiModelProperty(value = "时区")
    private String timeZone;

    @ApiModelProperty(value = "头像uuid")
    private String avatarUuid;

    @ApiModelProperty(value = "头像路径")
    private String avatar;

    @ApiModelProperty(value = "角色")
    private String role;

    @ApiModelProperty(value = "角色id")
    private String roleId;

    @ApiModelProperty(value = "角色名")
    private String roleName;
    @ApiModelProperty(value = "id")

    private String uid;

    private Long createDate;

    @ApiModelProperty(value = "权限集合")
    private List<AuthorityEntity> authorities;
}
