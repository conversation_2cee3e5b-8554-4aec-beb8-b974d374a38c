package com.wifochina.modules.user.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;

/**
 * @date 10/28/2022 2:28 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class UserUpdatePwdByEmailRequest {

    @ApiModelProperty(value = "邮箱号", required = true)
    @Email(message = "ILLEGAL_EMAIL")
    private String email;

    @ApiModelProperty(value = "验证码", required = true)
    private String code;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "确定密码", required = true)
    private String confirmPassword;
}
