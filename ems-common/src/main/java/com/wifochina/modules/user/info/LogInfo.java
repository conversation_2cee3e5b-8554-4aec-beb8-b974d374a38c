package com.wifochina.modules.user.info;

import com.wifochina.modules.oauth.util.WebUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.Map;

@Builder
@Data
public class LogInfo {
    @ApiModelProperty(value = "所属模块")
    private String module;

    @ApiModelProperty(value = "调用的方法")
    private String method;

    @ApiModelProperty(value = "记录对象")
    private Object object;

    @ApiModelProperty(value = "标识id")
    @Builder.Default
    private String traceId = WebUtils.projectId.get();

    @ApiModelProperty(value = "起始时间")
    @Builder.Default
    private long startTime = Instant.now().getEpochSecond();

    @ApiModelProperty(value = "起始时间 millis")
    @Builder.Default
    private long startTimeMillis = Instant.now().toEpochMilli();

    @ApiModelProperty(value = "删除记录")
    private Map<String, String> delDetail;

    @ApiModelProperty(value = "自定义记录")
    private String detail;
}
