package com.wifochina.modules.user.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.job.JobFlow;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.RegexUtil;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.oauth.AuthUser;
import com.wifochina.modules.oauth.dto.UserDto;
import com.wifochina.modules.oauth.service.LoginService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.job.CancelUserJob;
import com.wifochina.modules.user.request.*;
import com.wifochina.modules.user.service.UserService;
import com.wifochina.modules.user.vo.UserDeleteVo;
import com.wifochina.modules.user.vo.UserInfoVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.Instant;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api(value = "user", tags = "02-用户中心")
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Slf4j
public class UserController {

    private final UserService userService;

    private final PasswordEncoder passwordEncoder;

    private final LoginService loginService;

    private final RedisTemplate<String, String> redisTemplate;

    private final Scheduler scheduler;

    @Value("${user.regret}")
    private String regretTime;

    @PostMapping("/update/password")
    @ApiOperation("修改当前用户密码")
    public Result<Void> updatePassword(
            @RequestBody UserUpdatePasswordRequest updatePasswordRequest) {
        userService.updatePassword(updatePasswordRequest, true);
        return Result.success();
    }

    @PostMapping("/exist")
    @ApiOperation("检查用户是否存在")
    public Result<Void> exist(@RequestBody UserExistRequest userExistRequest) {
        Optional.ofNullable(userExistRequest.getUserName())
                .ifPresent(
                        e -> {
                            UserEntity userEntity =
                                    userService.lambdaQuery().eq(UserEntity::getUserName, e).one();
                            if (userEntity != null) {
                                throw new ServiceException(ErrorResultCode.USER_EXISTS.value());
                            }
                        });
        Optional.ofNullable(userExistRequest.getPhone())
                .ifPresent(
                        e -> {
                            UserEntity userEntity =
                                    userService.lambdaQuery().eq(UserEntity::getPhone, e).one();
                            if (userEntity != null) {
                                throw new ServiceException(
                                        ErrorResultCode.PHONE_IS_REGISTER.value());
                            }
                        });
        if (StringUtils.hasLength(userExistRequest.getEmail())) {
            UserEntity userEntity =
                    userService
                            .lambdaQuery()
                            .eq(UserEntity::getPhone, userExistRequest.getEmail())
                            .one();
            if (userEntity != null) {
                throw new ServiceException(ErrorResultCode.EMAIL_IS_REGISTER.value());
            }
        }
        return Result.success();
    }

    @PostMapping("/registerNew")
    @ApiOperation("注册用户_new接口")
    public Result<Map<String, String>> registerNew(
            @Valid @RequestBody UserSelfRegisterNewRequest userSelfRegisterNewRequest) {
        UserEntity userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getEmail, userSelfRegisterNewRequest.getEmail())
                        .one();
        if (userEntity != null) {
            throw new ServiceException(ErrorResultCode.EMAIL_IS_REGISTER.value());
        }
        Optional.ofNullable(userSelfRegisterNewRequest.getCode())
                .ifPresent(
                        e -> {
                            String code =
                                    JSON.parseObject(
                                            redisTemplate
                                                    .opsForValue()
                                                    .get(userSelfRegisterNewRequest.getEmail()),
                                            String.class);
                            if (!userSelfRegisterNewRequest.getCode().equals(code)) {
                                throw new ServiceException(
                                        ErrorResultCode.CODE_IS_INCORRECT.value());
                            }
                        });
        userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getUserName, userSelfRegisterNewRequest.getUserName())
                        .one();
        Optional.ofNullable(userEntity)
                .ifPresent(
                        (e) -> {
                            if (EmsConstants.USER_ROLE_REGISTER.equals(e.getRole())
                                    && e.getIsDelete()) {
                                int oneDaySecond = 86400;
                                if ((e.getUpdateTime()
                                                + Math.ceil(Float.parseFloat(regretTime))
                                                        * oneDaySecond)
                                        < Instant.now().getEpochSecond()) {
                                    userService.removeUser(e.getId());
                                    e = null;
                                }
                            }
                            if (e != null) {
                                throw new ServiceException(ErrorResultCode.USER_EXISTS.value());
                            }
                        });
        userEntity = new UserEntity();
        userEntity.setId(StringUtil.uuid());
        userEntity.setUserName(userSelfRegisterNewRequest.getUserName());
        userEntity.setEmail(userSelfRegisterNewRequest.getEmail());
        userEntity.setPassword(passwordEncoder.encode(userSelfRegisterNewRequest.getPassword()));
        userEntity.setRole(EmsConstants.USER_ROLE_REGISTER);
        userEntity.setRoleName("访客用户");
        userEntity.setIsDelete(Boolean.FALSE);
        // 2024-03-22 10:07:28 新增保存 国家id  暂时不用 后面看
        userEntity.setCountryId(userSelfRegisterNewRequest.getCountryId());
        userService.save(userEntity);
        UserDto userDto = new UserDto();
        userDto.setUsername(userSelfRegisterNewRequest.getUserName());
        userDto.setPassword(userSelfRegisterNewRequest.getPassword());
        String token = loginService.login(userDto);
        Map<String, String> map = new HashMap<>(1);
        map.put("token", token);
        return Result.success(map);
    }

    @PostMapping("/register")
    @ApiOperation("注册用户")
    public Result<Map<String, String>> register(
            @Valid @RequestBody UserSelfRegisterRequest userRegisterRequest) {
        UserEntity userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getEmail, userRegisterRequest.getPhone())
                        .one();
        if (userEntity != null) {
            throw new ServiceException(ErrorResultCode.PHONE_IS_REGISTER.value());
        }
        Optional.ofNullable(userRegisterRequest.getCode())
                .ifPresent(
                        e -> {
                            String code =
                                    JSON.parseObject(
                                            redisTemplate
                                                    .opsForValue()
                                                    .get(userRegisterRequest.getPhone()),
                                            String.class);
                            if (!userRegisterRequest.getCode().equals(code)) {
                                throw new ServiceException(
                                        ErrorResultCode.CODE_IS_INCORRECT.value());
                            }
                        });
        userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getUserName, userRegisterRequest.getUserName())
                        .one();
        Optional.ofNullable(userEntity)
                .ifPresent(
                        (e) -> {
                            if (EmsConstants.USER_ROLE_REGISTER.equals(e.getRole())
                                    && e.getIsDelete()) {
                                int oneDaySecond = 86400;
                                if ((e.getUpdateTime()
                                                + Math.ceil(Float.parseFloat(regretTime))
                                                        * oneDaySecond)
                                        < Instant.now().getEpochSecond()) {
                                    userService.removeUser(e.getId());
                                    e = null;
                                }
                            }
                            if (e != null) {
                                throw new ServiceException(ErrorResultCode.USER_EXISTS.value());
                            }
                        });
        userEntity = new UserEntity();
        userEntity.setId(StringUtil.uuid());
        userEntity.setUserName(userRegisterRequest.getUserName());
        userEntity.setPhone(userRegisterRequest.getPhone());
        userEntity.setPassword(passwordEncoder.encode(userRegisterRequest.getPassword()));
        userEntity.setRole(EmsConstants.USER_ROLE_REGISTER);
        userEntity.setRoleName("访客用户");
        userEntity.setIsDelete(Boolean.FALSE);
        userService.save(userEntity);
        UserDto userDto = new UserDto();
        userDto.setUsername(userRegisterRequest.getUserName());
        userDto.setPassword(userRegisterRequest.getPassword());
        String token = loginService.login(userDto);
        Map<String, String> map = new HashMap<>(1);
        map.put("token", token);
        return Result.success(map);
    }

    @PostMapping("/cancel/{code}")
    @ApiOperation("注销用户")
    public Result<Object> cancel(@PathVariable("code") String verifyCode) {

        HttpServletRequest request =
                ((ServletRequestAttributes)
                                Objects.requireNonNull(RequestContextHolder.getRequestAttributes()))
                        .getRequest();
        String language = request.getHeader("Accept-Language");
        if (StringUtil.isEmpty(language)) {
            throw new ServiceException(ErrorResultCode.LANGUAGE_NEED.value());
        }
        UserEntity userEntity = userService.getById(SecurityUtil.getUserId());
        if (!EmsConstants.USER_ROLE_REGISTER.equals(userEntity.getRole())) {
            throw new ServiceException(ErrorResultCode.CNA_NOT_CANCEL_NO_REGISTER.value());
        }
        String redisCode;
        if (!StringUtil.isEmpty(userEntity.getEmail())) {
            redisCode =
                    JSON.parseObject(
                            redisTemplate.opsForValue().get(userEntity.getEmail()), String.class);
            if (!verifyCode.equals(redisCode)) {
                throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
            }
        } else if (!StringUtil.isEmpty(userEntity.getPhone())) {
            // 兼容老的版本
            redisCode =
                    JSON.parseObject(
                            redisTemplate.opsForValue().get(userEntity.getPhone()), String.class);
            if (!verifyCode.equals(redisCode)) {
                throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
            }
        } else {
            // 正常不会 没有手机号并且没有邮箱的
            throw new ServiceException(ErrorResultCode.SERVICE_EXCEPTION.value());
        }
        userService
                .lambdaUpdate()
                .set(UserEntity::getIsDelete, true)
                .set(UserEntity::getUpdateTime, Instant.now().getEpochSecond())
                .eq(UserEntity::getId, SecurityUtil.getUserId())
                .update();
        redisTemplate.delete(Objects.requireNonNull(SecurityUtil.getUserId()));

        // 构建 注销的Job定时任务 通过JobFlow 统一流程
        // 有邮箱才去 生产定时任务
        if (!StringUtil.isEmpty(userEntity.getEmail())) {
            // 注销用户
            JobFlow.registerJob(
                    scheduler,
                    new JobFlow.JobInfo() {
                        @Override
                        public void fillData(JobDataMap jobDataMap) {
                            jobDataMap.put("userId", userEntity.getId());
                            jobDataMap.put("userName", userEntity.getUserName());
                            jobDataMap.put("email", userEntity.getEmail());
                            jobDataMap.put("language", language);
                        }

                        @Override
                        public JobDetail jobDetail(JobDataMap jobDataMap) {
                            return JobBuilder.newJob(CancelUserJob.class)
                                    .withIdentity(
                                            CancelUserJob.getJobName(userEntity.getId()),
                                            CancelUserJob.CANCEL_JOB_GROUP)
                                    .usingJobData(jobDataMap)
                                    .storeDurably()
                                    .build();
                        }

                        @Override
                        public Trigger trigger() {
                            return TriggerBuilder.newTrigger()
                                    .withIdentity(
                                            CancelUserJob.getTriggerName(userEntity.getId()),
                                            CancelUserJob.CANCEL_JOB_GROUP)
                                    .startAt(
                                            DateBuilder.futureDate(
                                                    10, DateBuilder.IntervalUnit.MINUTE))
                                    .build();
                        }
                    });
        } else {
            log.error(
                    "The userId: {},userName:{} does not have a email Unable to send a logout email",
                    userEntity.getId(),
                    userEntity.getName());
        }
        return Result.success();
    }

    @PostMapping("/regret/{id}")
    @ApiOperation("取消注销用户")
    public Result<Object> regret(@PathVariable("id") String id) {
        userService
                .lambdaUpdate()
                .set(UserEntity::getIsDelete, false)
                .eq(UserEntity::getId, id)
                .update();
        return Result.success();
    }

    @PostMapping("/checkUserWhetherDelete")
    @ApiOperation("用户是否注销")
    public Result<UserDeleteVo> whetherDelete(
            @RequestBody UserNameRequest userWhetherDeleteRequest) {
        UserEntity userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getPhone, userWhetherDeleteRequest.getUserName())
                        .or()
                        .eq(UserEntity::getEmail, userWhetherDeleteRequest.getUserName())
                        .or()
                        .eq(UserEntity::getUserName, userWhetherDeleteRequest.getUserName().trim())
                        .one();
        UserDeleteVo userDeleteVo = new UserDeleteVo();
        if (userEntity != null) {
            if (Boolean.TRUE.equals(userEntity.getIsDelete())) {
                userDeleteVo.setCancelTime(userEntity.getUpdateTime());
            }
        }
        return Result.success(userDeleteVo);
    }

    @PostMapping("/findPhoneByUserName")
    @ApiOperation("通过用户名查找手机号")
    public Result<String> findPhoneByUserName(
            @RequestBody UserNameRequest userWhetherDeleteRequest) {
        UserEntity userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getUserName, userWhetherDeleteRequest.getUserName().trim())
                        .one();
        if (userEntity == null || ObjectUtils.isEmpty(userEntity.getPhone())) {
            return Result.success(null);
        }
        return Result.success(userEntity.getPhone());
    }

    @PostMapping("/findEmailByUserName")
    @ApiOperation("通过用户名查找邮箱")
    public Result<String> findEmailByUserName(
            @RequestBody UserNameRequest userWhetherDeleteRequest) {
        UserEntity userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getUserName, userWhetherDeleteRequest.getUserName().trim())
                        .one();
        if (userEntity == null || ObjectUtils.isEmpty(userEntity.getEmail())) {
            return Result.success(null);
        }
        return Result.success(userEntity.getEmail());
    }

    /**
     * 获取登录用户详情
     *
     * @return Result<UserInfoVo>
     */
    @GetMapping("/getUserInfo")
    @ApiOperation("获取登录用户详情")
    public Result<UserInfoVo> getUserInfo() {
        String userId = SecurityUtil.getUserId();
        return Result.success(userService.getUserInfo(userId));
    }

    @PostMapping("/updateSelfPhone")
    @ApiOperation("绑定当前用户手机")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateSelfPhone(@RequestBody UserSelfPhoneRequest userSelfPhoneRequest) {
        String code =
                JSON.parseObject(
                        redisTemplate.opsForValue().get(userSelfPhoneRequest.getPhone()),
                        String.class);
        if (userSelfPhoneRequest.getCode().equals(code)) {
            UserEntity user =
                    userService
                            .lambdaQuery()
                            .eq(UserEntity::getPhone, userSelfPhoneRequest.getPhone())
                            .one();
            if (user != null) {
                userService.update(
                        user,
                        Wrappers.lambdaUpdate(UserEntity.class)
                                .set(UserEntity::getPhone, null)
                                .eq(UserEntity::getId, user.getId()));
            }
            UserEntity userEntity = new UserEntity();
            userEntity.setId(SecurityUtil.getUserId());
            userEntity.setPhone(userSelfPhoneRequest.getPhone());
            userService.updateById(userEntity);
            redisTemplate.delete(userSelfPhoneRequest.getPhone());
        } else {
            throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
        }
        return Result.success();
    }

    @PostMapping("/updatePwdByPhone")
    @ApiOperation("手机找回密码")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updatePwdByPhone(
            @RequestBody UserUpdatePwdByPhoneRequest userUpdatePwdByPhoneRequest) {
        String code =
                JSON.parseObject(
                        redisTemplate.opsForValue().get(userUpdatePwdByPhoneRequest.getPhone()),
                        String.class);
        if (userUpdatePwdByPhoneRequest.getCode().equals(code)) {
            ServiceAssert.isTrue(
                    RegexUtil.checkPassword(userUpdatePwdByPhoneRequest.getPassword()),
                    ErrorResultCode.PASSWORD_FORMAT.value());
            if (userUpdatePwdByPhoneRequest.getPassword() == null
                    || userUpdatePwdByPhoneRequest.getConfirmPassword() == null) {
                throw new ServiceException(ErrorResultCode.PASSWORD_IS_EMPTY.value());
            } else if (!userUpdatePwdByPhoneRequest
                    .getPassword()
                    .equals(userUpdatePwdByPhoneRequest.getConfirmPassword())) {
                throw new ServiceException(ErrorResultCode.PASSWORD_INCONSISTENT.value());
            }
            UserEntity user =
                    userService
                            .lambdaQuery()
                            .eq(UserEntity::getPhone, userUpdatePwdByPhoneRequest.getPhone())
                            .one();
            if (user == null) {
                throw new ServiceException(ErrorResultCode.PHONE_IS_NOT_REGISTER.value());
            }
            UserEntity userEntity = new UserEntity();
            userEntity.setId(user.getId());
            userEntity.setPassword(
                    passwordEncoder.encode(userUpdatePwdByPhoneRequest.getPassword()));
            userService.updateById(userEntity);
            redisTemplate.delete(userUpdatePwdByPhoneRequest.getPhone());
        } else {
            throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
        }
        return Result.success();
    }

    @PostMapping("/updateSelfEmail")
    @ApiOperation("绑定当前用户邮箱")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateSelfEmail(
            @Validated @RequestBody UserSelfEmailRequest userSelfEmailRequest) {
        String code =
                JSON.parseObject(
                        redisTemplate.opsForValue().get(userSelfEmailRequest.getEmail()),
                        String.class);
        if (userSelfEmailRequest.getCode().equals(code)) {
            UserEntity user =
                    userService
                            .lambdaQuery()
                            .eq(UserEntity::getEmail, userSelfEmailRequest.getEmail())
                            .one();
            if (user != null) {
                userService.update(
                        user,
                        Wrappers.lambdaUpdate(UserEntity.class)
                                .set(UserEntity::getEmail, null)
                                .eq(UserEntity::getId, user.getId()));
            }
            UserEntity userEntity = new UserEntity();
            userEntity.setId(SecurityUtil.getUserId());
            userEntity.setEmail(userSelfEmailRequest.getEmail());
            userService.updateById(userEntity);
            redisTemplate.delete(userSelfEmailRequest.getEmail());

            // 2024-04-03 15:01:07 add  新增逻辑 把当前的 security的认证的用户 先取消认证
            // 然后生成一个新的UsernamePasswordAuthenticationToken
            // 这里面的UserEntity的邮箱是新绑定的, 然后覆盖 redis 用户缓存
            Authentication oldAuth = SecurityContextHolder.getContext().getAuthentication();
            oldAuth.setAuthenticated(false);
            AuthUser authUser =
                    ((AuthUser)
                            SecurityContextHolder.getContext().getAuthentication().getPrincipal());
            authUser.getUserEntity().setEmail(userSelfEmailRequest.getEmail());
            UsernamePasswordAuthenticationToken authenticationToken =
                    new UsernamePasswordAuthenticationToken(
                            authUser,
                            null,
                            SecurityContextHolder.getContext()
                                    .getAuthentication()
                                    .getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            redisTemplate
                    .opsForValue()
                    .set(String.valueOf(userEntity.getId()), JSON.toJSONString(authUser));
        } else {
            throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
        }
        return Result.success();
    }

    @PostMapping("/updatePwdByEmail")
    @ApiOperation("邮箱找回密码")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updatePwdByEmail(
            @RequestBody UserUpdatePwdByEmailRequest userUpdatePwdByEmailRequest) {
        String code =
                JSON.parseObject(
                        redisTemplate.opsForValue().get(userUpdatePwdByEmailRequest.getEmail()),
                        String.class);
        if (userUpdatePwdByEmailRequest.getCode().equals(code)) {
            ServiceAssert.isTrue(
                    RegexUtil.checkPassword(userUpdatePwdByEmailRequest.getPassword()),
                    ErrorResultCode.PASSWORD_FORMAT.value());
            if (userUpdatePwdByEmailRequest.getPassword() == null
                    || userUpdatePwdByEmailRequest.getConfirmPassword() == null) {
                throw new ServiceException(ErrorResultCode.PASSWORD_IS_EMPTY.value());
            } else if (!userUpdatePwdByEmailRequest
                    .getPassword()
                    .equals(userUpdatePwdByEmailRequest.getConfirmPassword())) {
                throw new ServiceException(ErrorResultCode.PASSWORD_INCONSISTENT.value());
            }
            UserEntity user =
                    userService
                            .lambdaQuery()
                            .eq(UserEntity::getEmail, userUpdatePwdByEmailRequest.getEmail())
                            .one();
            if (user == null) {
                throw new ServiceException(ErrorResultCode.EMAIL_IS_NOT_REGISTER.value());
            }
            UserEntity userEntity = new UserEntity();
            userEntity.setId(user.getId());
            userEntity.setPassword(
                    passwordEncoder.encode(userUpdatePwdByEmailRequest.getPassword()));
            userService.updateById(userEntity);
            redisTemplate.delete(userUpdatePwdByEmailRequest.getEmail());
        } else {
            throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
        }
        return Result.success();
    }
}
