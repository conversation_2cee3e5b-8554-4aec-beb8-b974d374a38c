package com.wifochina.modules.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.OperationConstants;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.entity.LogEntity;
import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.mapper.LogMapper;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.ApiModelProperty;

import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Service
public class LogServiceImpl extends ServiceImpl<LogMapper, LogEntity> implements LogService {

    private static final String LOG_DETAIL_UPDATE_FORMAT = "%s(%s) %s 已改为 %s";
    private static final String LOG_DETAIL_ADD_FORMAT = "%s(%s) 设置为 %s";
    private static final String PC = "PC";
    private static final String MOBILE = "MOBILE";
    private static final String OTHER = "OTHER";

    @Value("${log.client.mobile}")
    private String MobileExpression;

    @Value("${log.client.pc}")
    private String PCExpression;

    @Override
    public void logUpdateDetail(LogInfo logInfo) {
        LogEntity logEntity = createLogEntity(logInfo);
        LogEntity lastSuccessLog = getLastSuccessLog(logInfo);
        logParamsChange(
                logInfo.getObject(),
                lastSuccessLog == null || !StringUtils.hasLength(lastSuccessLog.getParams())
                        ? null
                        : JSON.parseObject(lastSuccessLog.getParams()),
                logEntity);
        this.save(logEntity);
    }

    @Override
    public void logAddDetail(LogInfo logInfo) {
        LogEntity logEntity = createLogEntity(logInfo);
        logParamsNewChange(logInfo.getObject(), logEntity);
        this.save(logEntity);
    }

    @Override
    public void logDeleteDetail(LogInfo logInfo) {
        LogEntity logEntity = createLogEntity(logInfo);
        logEntity.setDetail("删除: " + JSON.toJSONString(logInfo.getDelDetail()));
        this.save(logEntity);
    }

    @Override
    public void logSelfDefinedDetail(LogInfo logInfo) {
        LogEntity logEntity = createLogEntity(logInfo);
        logEntity.setDetail(logInfo.getDetail());
        this.save(logEntity);
    }

    public LogEntity createLogEntity(LogInfo logInfo) {
        LogEntity logEntity = new LogEntity();
        logEntity.setModule(logInfo.getModule());
        logEntity.setMethod(logInfo.getMethod());
        logEntity.setIp(MDC.get("clientIP"));
        logEntity.setRequestTime(logInfo.getStartTime());
        logEntity.setTraceId(logInfo.getTraceId());
        logEntity.setParams(JSON.toJSONString(logInfo.getObject()));
        Optional.ofNullable(WebUtils.projectId.get())
                .ifPresentOrElse(
                        logEntity::setProjectId, () -> logEntity.setProjectId(EmsConstants.COMMON));
        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                        .getRequest();
        String userAgent = request.getHeader("User-Agent");
        if (userAgent.matches(String.format(".*(%s).*", MobileExpression))) {
            // 移动端请求
            logEntity.setClient(MOBILE);
        } else if (userAgent.matches(String.format(".*(%s).*", PCExpression))) {
            // PC端浏览器请求
            logEntity.setClient(PC);
        } else {
            // 其他类型的请求
            logEntity.setClient(OTHER);
        }
        logEntity.setUserId(SecurityUtil.getUserId() == null ? "system" : SecurityUtil.getUserId());
        logEntity.setResult(OperationConstants.SUCCESS.name().toLowerCase());
        logEntity.setCostTime(Instant.now().toEpochMilli() - logInfo.getStartTimeMillis());

        return logEntity;
    }

    private LogEntity getLastSuccessLog(LogInfo logInfo) {
        return this.lambdaQuery()
                .eq(LogEntity::getTraceId, logInfo.getTraceId())
                .eq(LogEntity::getResult, OperationConstants.SUCCESS.name().toLowerCase())
                .orderByDesc(LogEntity::getRequestTime) // 按 createTime 降序排列
                .last("LIMIT 1") // 只取一条记录
                .one();
    }

    private void logParamsChange(Object arg, JSONObject jsonObject, LogEntity logEntity) {
        List<String> keys = new ArrayList<>();
        if (arg != null && jsonObject != null) {
            // 获取参数的类类型
            Class<?> clazz = arg.getClass();
            // 获取类的所有字段
            Field[] fields = clazz.getDeclaredFields();
            // 对每个字段进行比较
            for (Field field : fields) {
                field.setAccessible(true); // 设置字段可访问
                try {
                    if ("serialVersionUID".equals(field.getName())) {
                        continue;
                    }
                    // 获取字段的值
                    Object newValue = field.get(arg);
                    // 获取字段的注解（如果有的话）
                    ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
                    // 获取字段的描述（如果注解存在）
                    String description =
                            apiModelProperty != null ? apiModelProperty.value() : field.getName();
                    // 获取字段的旧值
                    Object oldValue = jsonObject.get(field.getName());

                    if (newValue instanceof Float) {
                        handleFloat(field, (Float) newValue, oldValue, description, keys);
                    } else if (newValue instanceof Double) {
                        handleDouble(field, (Double) newValue, oldValue, description, keys);
                    } else if (newValue == null && oldValue != null) {
                        String log =
                                String.format(
                                        LOG_DETAIL_UPDATE_FORMAT,
                                        description,
                                        field.getName(),
                                        oldValue,
                                        null);
                        keys.add(log);
                    } else if (newValue != null && !newValue.equals(oldValue)) {
                        String log =
                                String.format(
                                        LOG_DETAIL_UPDATE_FORMAT,
                                        description,
                                        field.getName(),
                                        oldValue,
                                        newValue);
                        keys.add(log);
                    }
                } catch (IllegalAccessException ignored) {
                }
            }
        }
        if (!keys.isEmpty()) {
            // 将方法的参数值也记录到日志中
            logEntity.setDetail(JSON.toJSONString(keys));
        }
    }

    private static void handleDouble(
            Field field, Double newValue, Object oldValue, String description, List<String> keys) {
        // 比较 double 类型时使用的误差范围
        double EPSILON_DOUBLE = 1e-8;
        double newDoubleValue = newValue;
        if (oldValue == null) {
            String log =
                    String.format(
                            LOG_DETAIL_UPDATE_FORMAT,
                            description,
                            field.getName(),
                            null,
                            newDoubleValue);
            keys.add(log);
            return;
        }
        double oldDoubleValue = Double.parseDouble(oldValue.toString());
        // 比较两个 double 类型的值是否相等，使用误差范围来判断
        if (Math.abs(newDoubleValue - oldDoubleValue) > EPSILON_DOUBLE) {
            String log =
                    String.format(
                            LOG_DETAIL_UPDATE_FORMAT,
                            description,
                            field.getName(),
                            oldDoubleValue,
                            newDoubleValue);
            keys.add(log);
        }
    }

    private static void handleFloat(
            Field field, Float newValue, Object oldValue, String description, List<String> keys) {
        // 比较 float 类型时使用的误差范围
        float EPSILON_FLOAT = 1e-6f;
        float newFloatValue = newValue;
        if (oldValue == null) {
            String log =
                    String.format(
                            LOG_DETAIL_UPDATE_FORMAT,
                            description,
                            field.getName(),
                            null,
                            newFloatValue);
            keys.add(log);
            return;
        }
        float oldFloatValue = Float.parseFloat(oldValue.toString());
        // 比较两个 float 类型的值是否相等，使用误差范围来判断
        if (Math.abs(newFloatValue - oldFloatValue) > EPSILON_FLOAT) {
            String log =
                    String.format(
                            LOG_DETAIL_UPDATE_FORMAT,
                            description,
                            field.getName(),
                            oldFloatValue,
                            newFloatValue);
            keys.add(log);
        }
    }

    private void logParamsNewChange(Object arg, LogEntity logEntity) {
        List<String> keys = new ArrayList<>();
        if (arg != null) {
            // 获取参数的类类型
            Class<?> clazz = arg.getClass();
            // 获取类的所有字段
            Field[] fields = clazz.getDeclaredFields();
            // 对每个字段进行比较
            for (Field field : fields) {
                field.setAccessible(true); // 设置字段可访问
                try {
                    if ("serialVersionUID".equals(field.getName())) {
                        continue;
                    }
                    // 获取字段的值
                    String log = getLogFiled(arg, field);
                    keys.add(log);
                } catch (IllegalAccessException ignored) {
                }
            }
        }
        // 将方法的参数值也记录到日志中
        logEntity.setDetail(JSON.toJSONString(keys));
    }

    private static @NotNull String getLogFiled(Object arg, Field field)
            throws IllegalAccessException {
        Object newValue = field.get(arg);
        // 获取字段的注解（如果有的话）
        ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
        // 获取字段的描述（如果注解存在）
        String description = apiModelProperty != null ? apiModelProperty.value() : field.getName();
        return String.format(LOG_DETAIL_ADD_FORMAT, description, field.getName(), newValue);
    }
}
