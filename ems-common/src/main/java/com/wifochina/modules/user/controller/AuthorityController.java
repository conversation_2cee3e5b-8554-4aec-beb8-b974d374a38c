package com.wifochina.modules.user.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.page.Result;
import com.wifochina.modules.user.entity.AuthorityEntity;
import com.wifochina.modules.user.service.AuthorityService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/authority")
@Api(tags = "19-权限接口")
public class AuthorityController {

    @Autowired(required = false)
    private AuthorityService authorityService;

    @PostMapping("/getAuthorityPoint")
    @ApiOperation("查询所有权限")
    public Result<List<AuthorityEntity>> getAllAuthority() {
        return Result.success(authorityService.list());
    }

    @PostMapping("/getTreeAllAuthority")
    @ApiOperation("查询所有树形权限")
    public Result<List<AuthorityEntity>> getTreeAllAuthority() {
        return Result.success(authorityService.getTreeAll());
    }

}
