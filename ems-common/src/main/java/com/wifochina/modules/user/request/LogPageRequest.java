package com.wifochina.modules.user.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 日志分页请求
 * 
 * @date 2022/3/16 16:54
 * <AUTHOR>
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LogPageRequest extends PageBean {

    @ApiModelProperty(value = "userId", required = true)
    private String userId;
}
