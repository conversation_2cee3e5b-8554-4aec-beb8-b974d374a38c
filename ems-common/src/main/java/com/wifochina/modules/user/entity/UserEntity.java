package com.wifochina.modules.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "t_user")
@ApiModel(value = "用户完整信息", description = "用户详情")
@EqualsAndHashCode(callSuper = false)
public class UserEntity extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "用户账号")
    private String userName;

    @ApiModelProperty(value = "用户姓名或者备注")
    private String name;

    @ApiModelProperty(value = "用户密码")
    private String password;

    @ApiModelProperty(value = "用户角色类型 client项目普通ad域控admin项目管理super超级")
    private String role;

    @ApiModelProperty(value = "角色id")
    private String roleId;

    @ApiModelProperty(value = "角色名")
    private String roleName;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDelete;

    @ApiModelProperty(value = "能否访问后端:false 不可以,true可以")
    private Boolean visitManage;

    @ApiModelProperty(value = "手机")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "微信")
    private String wechat;

    @ApiModelProperty(value = "是否拥有所有项目")
    private Boolean allProject = false;

    @ApiModelProperty(value = "项目列表")
    @TableField(exist = false)
    private Map<String, String> projectMap;

    /** 登录5次失败后锁定的次数 这里选择int类型是怕后续 锁定时间会根据次数变化 */
    @ApiModelProperty(value = "登录锁定次数")
    private Integer lockCount;

    /** 2024-03-22 10:04:34 新增国家的id 暂时不用 */
    @ApiModelProperty(value = "国家id")
    private Integer countryId;

    /** 下面2个属性 属于 账号系统那边 这边作为载体返回的 云版本才有 场站没有 */
    @TableField(exist = false)
    private Integer authStat;

    @TableField(exist = false)
    private String countryOrRegionName;

    @ApiModelProperty(value = "用户类型;1 普通用户 2 Ldap用户")
    @TableField(exist = false)
    private Integer type;

}
