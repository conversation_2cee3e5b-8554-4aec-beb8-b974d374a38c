package com.wifochina.modules.user.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.user.entity.AuthorityEntity;
import com.wifochina.modules.user.entity.RoleAuthorityEntity;
import com.wifochina.modules.user.entity.UserRoleEntity;
import com.wifochina.modules.user.mapper.AuthorityMapper;
import com.wifochina.modules.user.service.AuthorityService;
import com.wifochina.modules.user.service.RoleAuthorityService;
import com.wifochina.modules.user.service.UserRoleService;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AuthorityServiceImpl extends ServiceImpl<AuthorityMapper, AuthorityEntity> implements AuthorityService {

    private final UserRoleService userRoleService;

    private final RoleAuthorityService roleAuthorityService;

    @Override
    public List<AuthorityEntity> getTreeAll() {

        // 一节权限
        List<AuthorityEntity> authorityRootList =
            this.getBaseMapper().selectList(Wrappers.<AuthorityEntity>lambdaQuery().isNull(AuthorityEntity::getPid));

        if (authorityRootList == null || authorityRootList.isEmpty()) {
            return null;
        }
        if (!"whadmin".equals(SecurityUtil.getUsername())) {
            UserRoleEntity userRoleEntity = userRoleService.getOne(
                Wrappers.lambdaQuery(UserRoleEntity.class).eq(UserRoleEntity::getUserId, SecurityUtil.getUserId()));
            List<RoleAuthorityEntity> roleAuthorityEntities = roleAuthorityService.list(Wrappers
                .lambdaQuery(RoleAuthorityEntity.class).eq(RoleAuthorityEntity::getRoleId, userRoleEntity.getRoleId()));
            List<Long> authIds =
                roleAuthorityEntities.stream().map(RoleAuthorityEntity::getAuthorityId).collect(Collectors.toList());
            authorityRootList = authorityRootList.stream().filter((item) -> authIds.contains(item.getId()))
                .collect(Collectors.toList());
            authorityRootList.forEach(authorityParent -> {
                // 二级权限
                List<AuthorityEntity> accessParentPoints = this.getBaseMapper().selectList(
                    Wrappers.<AuthorityEntity>lambdaQuery().eq(AuthorityEntity::getPid, authorityParent.getId()));
                accessParentPoints = accessParentPoints.stream().filter((item) -> authIds.contains(item.getId()))
                    .collect(Collectors.toList());
                accessParentPoints.forEach(authority -> {
                    // 三级权限
                    List<AuthorityEntity> accessPoint1s = this.getBaseMapper().selectList(
                        Wrappers.<AuthorityEntity>lambdaQuery().eq(AuthorityEntity::getPid, authority.getId()));
                    accessPoint1s = accessPoint1s.stream().filter((item) -> authIds.contains(item.getId()))
                        .collect(Collectors.toList());
                    authority.setAuthorityList(accessPoint1s);
                });
                authorityParent.setAuthorityList(accessParentPoints);
            });
        } else {
            authorityRootList.forEach(authorityParent -> {
                // 二级权限
                List<AuthorityEntity> accessParentPoints = this.getBaseMapper().selectList(
                    Wrappers.<AuthorityEntity>lambdaQuery().eq(AuthorityEntity::getPid, authorityParent.getId()));
                accessParentPoints.forEach(authority -> {
                    // 三级权限
                    List<AuthorityEntity> accessPoint1s = this.getBaseMapper().selectList(
                        Wrappers.<AuthorityEntity>lambdaQuery().eq(AuthorityEntity::getPid, authority.getId()));
                    authority.setAuthorityList(accessPoint1s);
                });
                authorityParent.setAuthorityList(accessParentPoints);
            });
        }

        return authorityRootList;
    }

}
