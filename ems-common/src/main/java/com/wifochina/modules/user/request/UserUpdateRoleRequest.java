package com.wifochina.modules.user.request;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 修改角色
 * 
 * <AUTHOR>
 */
@Data
public class UserUpdateRoleRequest {

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty(value = "用户姓名或者备注")
    private String name;

    @ApiModelProperty("角色id")
    private String roleId;

    @ApiModelProperty("用户id")
    private String id;

    @ApiModelProperty("是否所有项目")
    private Boolean allProject;

    @ApiModelProperty("能否访问后端:false 不可以,true可以")
    private Boolean visitManage;

    @ApiModelProperty("客户端类型: common通用用户，client普通用户，ad域控用户")
    private List<String> projectIds;
}
