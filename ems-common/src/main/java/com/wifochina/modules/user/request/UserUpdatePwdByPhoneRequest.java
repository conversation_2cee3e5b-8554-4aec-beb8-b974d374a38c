package com.wifochina.modules.user.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @date 10/28/2022 2:28 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class UserUpdatePwdByPhoneRequest {

    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    @ApiModelProperty(value = "验证码", required = true)
    private String code;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "确定密码", required = true)
    private String confirmPassword;
}
