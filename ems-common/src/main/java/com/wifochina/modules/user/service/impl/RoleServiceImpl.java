package com.wifochina.modules.user.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.modules.user.entity.RoleAuthorityEntity;
import com.wifochina.modules.user.entity.RoleEntity;
import com.wifochina.modules.user.mapper.RoleMapper;
import com.wifochina.modules.user.request.RoleRequest;
import com.wifochina.modules.user.service.AuthorityService;
import com.wifochina.modules.user.service.RoleAuthorityService;
import com.wifochina.modules.user.service.RoleService;
import com.wifochina.modules.user.vo.RoleVO;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, RoleEntity> implements RoleService {

    private final RoleAuthorityService roleAuthorityService;

    private final AuthorityService authorityService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRole(RoleRequest roleRequest, String projectId) {
        RoleEntity roleEntityTemp =
                this.baseMapper.selectOne(
                        Wrappers.lambdaQuery(RoleEntity.class)
                                .eq(RoleEntity::getProjectId, projectId)
                                .eq(RoleEntity::getRoleName, roleRequest.getRoleName()));
        ServiceAssert.isTrue(roleEntityTemp == null, ErrorResultCode.ROLE_REPEAT.value());
        RoleEntity roleEntity = new RoleEntity();
        roleEntity.setRoleName(roleRequest.getRoleName());
        roleEntity.setRemark(roleRequest.getRemark());
        roleEntity.setProjectId(projectId);
        this.baseMapper.insert(roleEntity);
        List<RoleAuthorityEntity> roleAuthorityEntities =
                roleRequest.getAuthorities().stream()
                        .map(
                                authority -> {
                                    RoleAuthorityEntity roleAuthorityEntity =
                                            new RoleAuthorityEntity();
                                    roleAuthorityEntity.setRoleId(roleEntity.getId());
                                    roleAuthorityEntity.setAuthorityId(authority);
                                    return roleAuthorityEntity;
                                })
                        .collect(Collectors.toList());
        roleAuthorityService.saveBatch(roleAuthorityEntities);
        return roleEntity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(RoleRequest roleRequest, String projectId) {
        ServiceAssert.isTrue(roleRequest.getId() != null, ErrorResultCode.ILLEGAL_ID_NULL.value());
        RoleEntity roleEntityTemp =
                this.baseMapper.selectOne(
                        Wrappers.lambdaQuery(RoleEntity.class)
                                .eq(RoleEntity::getProjectId, projectId)
                                .eq(RoleEntity::getRoleName, roleRequest.getRoleName()));
        ServiceAssert.isTrue(
                roleEntityTemp == null || roleEntityTemp.getId().equals(roleRequest.getId()),
                ErrorResultCode.ROLE_REPEAT.value());
        RoleEntity roleEntity = new RoleEntity();
        roleEntity.setRoleName(roleRequest.getRoleName());
        roleEntity.setId(roleRequest.getId());
        roleEntity.setRemark(roleRequest.getRemark());
        this.baseMapper.updateById(roleEntity);
        roleAuthorityService.remove(
                Wrappers.lambdaQuery(RoleAuthorityEntity.class)
                        .eq(RoleAuthorityEntity::getRoleId, roleRequest.getId()));
        List<RoleAuthorityEntity> roleAuthorityEntities =
                roleRequest.getAuthorities().stream()
                        .map(
                                authority -> {
                                    RoleAuthorityEntity roleAuthorityEntity =
                                            new RoleAuthorityEntity();
                                    roleAuthorityEntity.setRoleId(roleEntity.getId());
                                    roleAuthorityEntity.setAuthorityId(authority);
                                    return roleAuthorityEntity;
                                })
                        .collect(Collectors.toList());
        roleAuthorityService.saveBatch(roleAuthorityEntities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(String roleId) {
        ServiceAssert.isTrue(roleId != null, ErrorResultCode.ILLEGAL_ID_NULL.value());
        this.baseMapper.deleteById(roleId);
        roleAuthorityService.remove(
                Wrappers.lambdaQuery(RoleAuthorityEntity.class)
                        .eq(RoleAuthorityEntity::getRoleId, roleId));
    }

    @Override
    public IPage<RoleVO> getPagesRoles(@RequestBody PageBean pageBean, String projectId) {
        IPage<RoleEntity> rolePage = new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        IPage<RoleEntity> roles =
                this.baseMapper.selectPage(
                        rolePage,
                        Wrappers.lambdaQuery(RoleEntity.class)
                                .eq(RoleEntity::getProjectId, projectId));
        List<RoleVO> roleVOList =
                roles.getRecords().stream()
                        .map(
                                roleEntity -> {
                                    RoleVO roleVO = new RoleVO();
                                    roleVO.setId(roleEntity.getId());
                                    roleVO.setRoleName(roleEntity.getRoleName());
                                    roleVO.setRemark(roleEntity.getRemark());
                                    List<Long> authorityIds =
                                            roleAuthorityService
                                                    .list(
                                                            Wrappers.lambdaQuery(
                                                                            RoleAuthorityEntity
                                                                                    .class)
                                                                    .eq(
                                                                            RoleAuthorityEntity
                                                                                    ::getRoleId,
                                                                            roleEntity.getId()))
                                                    .stream()
                                                    .map(RoleAuthorityEntity::getAuthorityId)
                                                    .collect(Collectors.toList());
                                    if (authorityIds.size() > 0) {
                                        roleVO.setAuthorities(
                                                authorityService.listByIds(authorityIds));
                                    }
                                    return roleVO;
                                })
                        .collect(Collectors.toList());

        IPage<RoleVO> roleVoPage = new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        roleVoPage.setCurrent(roles.getCurrent());
        roleVoPage.setPages(roles.getPages());
        roleVoPage.setSize(roles.getSize());
        roleVoPage.setTotal(roles.getTotal());
        roleVoPage.setRecords(roleVOList);
        return roleVoPage;
    }
}
