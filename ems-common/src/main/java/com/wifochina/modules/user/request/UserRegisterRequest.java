package com.wifochina.modules.user.request;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户注册
 * 
 * <AUTHOR>
 */
@Data
public class UserRegisterRequest {

    @ApiModelProperty(value = "用户姓名或者备注")
    private String name;

    @ApiModelProperty(value = "用户名", required = true)
    private String userName;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "确定密码", required = true)
    private String confirmPassword;

    @ApiModelProperty("角色id")
    private String roleId;

    @ApiModelProperty("客户端类型: common通用用户，client普通用户，ad域控用户")
    private String type;

    @ApiModelProperty("是否所有项目")
    private Boolean allProject;

    @ApiModelProperty("能否访问后端:false 不可以,true可以")
    private Boolean visitManage;

    @ApiModelProperty("客户端类型: common通用用户，client普通用户，ad域控用户")
    private List<String> projectIds;
}
