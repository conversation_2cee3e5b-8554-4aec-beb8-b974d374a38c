package com.wifochina.modules.user.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;

/**
 * UserPhoneRequest
 * 
 * @date 10/28/2022 2:28 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class UserOtherEmailRequest {

    @ApiModelProperty(value = "用户id", required = true)
    private String id;

    @ApiModelProperty(value = "邮箱号", required = true)
    @Email(message = "ILLEGAL_EMAIL")
    private String email;
}
