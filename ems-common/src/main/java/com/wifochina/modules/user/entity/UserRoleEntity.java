package com.wifochina.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "t_user_role")
@ApiModel(value = "UserRole关系", description = "用户角色")
public class UserRoleEntity {

    @ApiModelProperty(value = "记录ID", required = true)
    private Long id;

    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;

    @ApiModelProperty(value = "角色ID", required = true)
    private String roleId;

}