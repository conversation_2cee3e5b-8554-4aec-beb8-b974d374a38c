package com.wifochina.modules.user.service;

import org.springframework.web.bind.annotation.RequestBody;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.common.page.PageBean;
import com.wifochina.modules.user.entity.RoleEntity;
import com.wifochina.modules.user.request.RoleRequest;
import com.wifochina.modules.user.vo.RoleVO;

/**
 * <AUTHOR>
 */
public interface RoleService extends IService<RoleEntity> {

    String createRole(RoleRequest roleRequest, String projectId);

    void updateRole(RoleRequest roleRequest, String projectId);

    IPage<RoleVO> getPagesRoles(@RequestBody PageBean pageBean, String projectId);

    void deleteRole(String roleId);
}
