package com.wifochina.modules.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.page.Result;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.RoleLogDetailService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.entity.RoleEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.request.RoleNameRequest;
import com.wifochina.modules.user.request.RoleRequest;
import com.wifochina.modules.user.service.LogService;
import com.wifochina.modules.user.service.RoleService;
import com.wifochina.modules.user.service.UserService;
import com.wifochina.modules.user.vo.RoleVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/25/2022 5:29 PM
 */
@RestController
@RequestMapping("/role")
@Api(tags = "18-角色管理")
public class RoleController {

    @Resource private RoleService roleService;

    @Resource private UserService userService;

    @Resource private RedisTemplate<String, String> redisTemplate;

    @Resource private LogService logService;

    @PostMapping("/create")
    @ApiOperation("新增角色")
    @PreAuthorize("hasAuthority('/user/role')")
    @Log(module = "ROLE", type = OperationType.ADD)
    public Result<Object> create(@RequestBody RoleRequest roleRequest) {
        String roleId = roleService.createRole(roleRequest, WebUtils.projectId.get());
        return Result.success();
    }

    @PostMapping("/update")
    @ApiOperation("修改角色")
    @PreAuthorize("hasAuthority('/user/role')")
    @Log(module = "ROLE", type = OperationType.UPDATE)
    public Result<Object> update(@RequestBody RoleRequest roleRequest) {
        roleService.updateRole(roleRequest, WebUtils.projectId.get());
        logout(roleRequest.getId());
        return Result.success();
    }

    @GetMapping("/delete/{roleId}")
    @ApiOperation("删除角色")
    @PreAuthorize("hasAuthority('/user/role')")
    @Log(
            module = "ROLE",
            type = OperationType.DEL,
            logDetailServiceClass = RoleLogDetailService.class)
    public Result<Object> delete(@PathVariable("roleId") String roleId) {
        RoleEntity roleEntity = roleService.getById(roleId);
        roleService.deleteRole(roleId);
        logout(roleId);
        return Result.success();
    }

    public void logout(String roleId) {
        List<UserEntity> userEntities =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getRoleId, roleId)
                        .eq(UserEntity::getIsDelete, false)
                        .list();
        for (UserEntity userEntity : userEntities) {
            redisTemplate.delete(userEntity.getId());
        }
    }

    @PostMapping("/getPagesRoles")
    @ApiOperation("分页查询角色")
    @PreAuthorize("hasAuthority('/user/role')")
    public Result<IPage<RoleVO>> getPagesRoles(@RequestBody PageBean pageBean) {
        IPage<RoleVO> roleVoPage = roleService.getPagesRoles(pageBean, WebUtils.projectId.get());
        return Result.success(roleVoPage);
    }

    @PostMapping("/getAllRoles")
    @ApiOperation("查询所有角色")
    @PreAuthorize("hasAuthority('/user/role')")
    public Result<List<RoleEntity>> getAllRoles() {
        return Result.success(
                roleService.list(
                        Wrappers.lambdaQuery(RoleEntity.class)
                                .eq(RoleEntity::getProjectId, WebUtils.projectId.get())));
    }

    @PostMapping("/getRoleByRoleName")
    @ApiOperation("根据角色名称查询角色")
    @PreAuthorize("hasAuthority('/user/role')")
    public Result<RoleEntity> getRoleByRoleName(@RequestBody RoleNameRequest roleNameRequest) {
        RoleEntity roleEntity =
                roleService
                        .lambdaQuery()
                        .eq(RoleEntity::getRoleName, roleNameRequest.getRoleName())
                        .eq(RoleEntity::getProjectId, WebUtils.projectId.get())
                        .one();
        return Result.success(roleEntity);
    }
}
