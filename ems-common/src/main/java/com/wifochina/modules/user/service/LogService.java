package com.wifochina.modules.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.user.entity.LogEntity;
import com.wifochina.modules.user.info.LogInfo;

/**
 * <AUTHOR>
 */
public interface LogService extends IService<LogEntity> {
    String DEVICE_INFO_TRACE_FORMAT = "device:%s";
    String METER_INFO_TRACE_FORMAT = "meter:%s";
    String CAMERA_INFO_TRACE_FORMAT = "camera:%s";
    String GROUP_INFO_TRACE_FORMAT = "group:%s";
    String GROUP_EARNINGS_TRACE_FORMAT = "group:earnings:%s";
    String NOTICE_TRACE_FORMAT = "notice:%s";
    String CONTROLLABLE_INFO_TRACE_FORMAT = "controllable:%s";
    String CONTROLLER_INFO_TRACE_FORMAT = "controller:%s";
    String INCOME_DIVIDE_TRACE_FORMAT = "income:divide:%s";
    String EMS_REMEDIES_TRACE_FORMAT = "ems:remedies:%s";
    String PV_REMEDIES_TRACE_FORMAT = "pv:remedies:%s";
    String WIND_REMEDIES_TRACE_FORMAT = "wind:remedies:%s";
    String WASTER_REMEDIES_TRACE_FORMAT = "waster:remedies:%s";
    String OPERATION_USER_PRICES_TRACE_FORMAT = "operation:user:price:%s";
    String PRICE_TEMPLATE_TRACE_FORMAT = "price:template:%s";
    String TIME_STRATEGY_TRACE_FORMAT = "time:strategy:%s";
    String TIME_STRATEGY_IMPORT_TRACE_FORMAT = "time:strategy:import:%s";
    String GROUP_STRATEGY_TRACE_FORMAT = "group:strategy:%s";
    String GROUP_STRATEGY_IMPORT_TRACE_FORMAT = "group:strategy:import:%s";
    String EVENT_DELETE_TRACE_FORMAT = "event:delete:%s";
    String REPORT_DAILY_REMEDIES_TRACE_FORMAT = "report:daily:remedies:%s";
    String REPORT_MONTH_REMEDIES_TRACE_FORMAT = "report:month:remedies:%s";
    String REPORT_YEAR_REMEDIES_TRACE_FORMAT = "report:year:remedies:%s";
    String REPORT_METER_DAILY_REMEDIES_TRACE_FORMAT = "report:meter:daily:remedies:%s";
    String REPORT_METER_MONTH_REMEDIES_TRACE_FORMAT = "report:meter:month:remedies:%s";
    String REPORT_METER_YEAR_REMEDIES_TRACE_FORMAT = "report:meter:year:remedies:%s";
    String ROLE_AUTHOR_CANCEL_TRACE_FORMAT = "role:author:cancel:%s:%s";
    String ROLE_AUTHOR_TRACE_FORMAT = "role:author:%s:%s";
    String ROLE_CREATE_TRACE_FORMAT = "role:create:%s";
    String ROLE_DELETE_TRACE_FORMAT = "role:delete:%s";
    String ROLE_UPDATE_TRACE_FORMAT = "role:update:%s";
    String USER_CREATE_TRACE_FORMAT = "user:create:%s";
    String USER_UPDATE_TRACE_FORMAT = "user:update:%s";
    String USER_DELETE_TRACE_FORMAT = "user:delete:%s";
    String SYSTEM_SWITCH_TRACE_FORMAT = "system:switch:%s";
    String SYSTEM_RESET_TRACE_FORMAT = "system:reset:%s";
    String DISCONNECT_GRID_TRACE_FORMAT = "grid:disconnect:%s";
    String DEVICE_MAINTAIN_TRACE_FORMAT = "device:maintain:%s";
    String PV_RERUN_TRACE_FORMAT = "pv:rerun";
    String WASTER_RERUN_TRACE_FORMAT = "waster:rerun";
    String WIND_RERUN_TRACE_FORMAT = "wind:rerun";
    String BATTERY_RERUN_TRACE_FORMAT = "battery:rerun";
    String DEMAND_RERUN_TRACE_FORMAT = "demand:rerun";
    String EVENT_EMS_HIDE_TRACE_FORMAT = "event:ems:hide";
    String EVENT_METER_HIDE_TRACE_FORMAT = "event:meter:hide";
    String PROJECT_INFO_TRACE_FORMAT = "project:info:%s";
    String AREA_TRACE_FORMAT = "area:%s";
    String VERSION_TRACE_FORMAT = "version:%s";

    void logUpdateDetail(LogInfo logInfo);

    void logAddDetail(LogInfo logInfo);

    void logDeleteDetail(LogInfo logInfo);

    void logSelfDefinedDetail(LogInfo logInfo);
}
