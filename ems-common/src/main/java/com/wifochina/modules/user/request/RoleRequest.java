package com.wifochina.modules.user.request;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * RoleRequest
 * 
 * @date 3/25/2022 5:33 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class RoleRequest {

    private String id;

    @ApiModelProperty(value = "角色名称", required = true)
    private String roleName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "权限列表", required = true)
    private List<Long> authorities;
}
