package com.wifochina.modules.user.request;

import com.wifochina.common.constants.ErrorCons;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 用户注册
 *
 * <AUTHOR>
 */
@Data
public class UserSelfRegisterRequest {

    @NotEmpty(message = ErrorCons.USERNAME_FORMAT)
    @Pattern(regexp = "^[A-Za-z0-9]{5,24}$", message = ErrorCons.USERNAME_FORMAT)
    @ApiModelProperty(value = "用户名", required = true)
    private String userName;

    @NotNull(message = ErrorCons.PHONE_ILLEGAL)
    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    @ApiModelProperty(value = "验证码", required = true)
    private String code;

    @NotEmpty(message = ErrorCons.PASSWORD_IS_EMPTY)
    @Pattern(regexp = "^[A-Za-z0-9]{6,16}$", message = ErrorCons.PASSWORD_FORMAT)
    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @NotEmpty(message = ErrorCons.PASSWORD_INCONSISTENT)
    @ApiModelProperty(value = "确定密码", required = true)
    private String confirmPassword;
}
