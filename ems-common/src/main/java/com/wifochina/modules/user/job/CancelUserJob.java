package com.wifochina.modules.user.job;

import com.wifochina.common.handler.MessageSourceHandler;
import com.wifochina.common.util.EmailService;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.user.service.UserService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class CancelUserJob extends QuartzJobBean {

    private final UserService userService;

    private final EmailService emailService;

    public static final String CANCEL_JOB_GROUP = "cancel-user-group";

    @Override
    protected void executeInternal(@NotNull JobExecutionContext context)
            throws JobExecutionException {
        log.info("start CancelUserJob...");
        String userId = null;
        try {
            userId = (String) context.getJobDetail().getJobDataMap().get("userId");
            String userName = (String) context.getJobDetail().getJobDataMap().get("userName");
            String email = (String) context.getJobDetail().getJobDataMap().get("email");
            String language = (String) context.getJobDetail().getJobDataMap().get("language");
            log.info(
                    "cancelJob params : userId:{}, userName:{}, email:{}, language:{}",
                    userId,
                    userName,
                    email,
                    language);
            // real 删除用户
            userService.removeUser(userId);
            // 获取到待发送的 文案
            String cancelMessage =
                    MessageSourceHandler.getMessageExternalLanguage("CANCEL_LOG_OUT", language);
            // 替换用户名
            cancelMessage = cancelMessage.replace("%", userName);
            // 获取到主题 名称
            String cancelSubjectMessage =
                    MessageSourceHandler.getMessageExternalLanguage(
                            "CANCEL_LOG_OUT_SUBJECT", language);
            log.info(
                    "cancelMessage : {} , cancelSubjectMessage : {}",
                    cancelMessage,
                    cancelSubjectMessage);
            if (!StringUtil.isEmpty(email)) {
                // 发送销户的邮件
                emailService.sendMessage(
                        Collections.singletonList(email), cancelSubjectMessage, cancelMessage);
            } else {
                log.warn("CancelUserJob cant send email , email is null userId : {}", userId);
            }
        } catch (Exception e) {
            log.error("注销Job失败了 userId:{}, error: {}", userId, e.getMessage());
        }
        log.info("end CancelUserJob...");
    }

    public static String getJobName(String userId) {
        return String.format("cancel-user-job:%s", userId);
    }

    public static String getTriggerName(String userId) {
        return String.format("cancel-user-trigger:%s", userId);
    }
}
