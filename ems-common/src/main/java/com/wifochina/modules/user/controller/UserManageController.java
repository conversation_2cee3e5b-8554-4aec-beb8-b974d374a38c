package com.wifochina.modules.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.handler.MessageSourceHandler;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.UserLogDetailService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.entity.LogEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.entity.UserProjectEntity;
import com.wifochina.modules.user.request.*;
import com.wifochina.modules.user.service.LogService;
import com.wifochina.modules.user.service.UserProjectService;
import com.wifochina.modules.user.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.NoSuchMessageException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(value = "user", tags = "17-用户管理")
@RestController
@RequestMapping("/user")
@Slf4j
@RequiredArgsConstructor
public class UserManageController {

    private final UserService userService;

    private final UserProjectService userProjectService;

    private final LogService logService;

    @PostMapping("/update/other/password")
    @ApiOperation("修改其它用户密码")
    @Log(module = "USER_MANAGE", methods = "USER_MANAGE_CH_OTHER_PWD")
    @PreAuthorize("hasAuthority('/user/account')")
    public Result<Void> updateOtherPassword(
            @RequestBody UserUpdatePasswordRequest updatePasswordRequest) {
        userService.updatePassword(updatePasswordRequest, false);
        return Result.success();
    }

    @PostMapping("/update")
    @ApiOperation("修改用户")
    @PreAuthorize("hasAuthority('/user/account')")
    @Log(module = "USER_MANAGE", type = OperationType.UPDATE_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> update(@RequestBody UserUpdateRoleRequest userUpdateRoleRequest) {
        userService.updateUserRole(userUpdateRoleRequest);
        return Result.success();
    }

    @PostMapping("/add")
    @ApiOperation("添加用户")
    @Log(module = "USER_MANAGE", type = OperationType.ADD)
    @PreAuthorize("hasAuthority('/user/account')")
    public Result<Void> add(@RequestBody UserRegisterRequest userRegisterRequest) {
        userRegisterRequest.setType(EmsConstants.USER_ROLE_CLIENT);
        UserEntity userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getUserName, userRegisterRequest.getUserName())
                        .one();
        if (userEntity != null) {
            throw new ServiceException(ErrorResultCode.USER_EXISTS.value());
        }
        String userId = userService.register(userRegisterRequest);
        return Result.success();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除用户")
    @PreAuthorize("hasAuthority('/user/account')")
    @Log(
            module = "USER_MANAGE",
            type = OperationType.DEL,
            logDetailServiceClass = UserLogDetailService.class)
    public Result<Object> delete(@PathVariable("id") String id) {
        UserEntity userEntity = userService.getById(id);
        userService.removeUser(id);
        return Result.success();
    }

    @PostMapping("/getPagesUsers")
    @ApiOperation("分页查询用户")
    @PreAuthorize("hasAuthority('/user/account')")
    public Result<IPage<UserEntity>> getPagesUsers(@RequestBody PageBean pageBean) {
        IPage<UserEntity> userPageBean = new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        IPage<UserEntity> userEntityPage;
        // 查找分配给该项目的用户
        List<UserProjectEntity> list =
                userProjectService.list(
                        Wrappers.lambdaQuery(UserProjectEntity.class)
                                .eq(UserProjectEntity::getProjectId, WebUtils.projectId.get()));
        // 用户id
        Set<String> userIds =
                list.stream().map(UserProjectEntity::getUserId).collect(Collectors.toSet());
        // 当前登录用户
        UserEntity userEntity = Objects.requireNonNull(SecurityUtil.getPrincipal()).getUserEntity();
        // 查询all用户
        List<UserEntity> allUserList =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getAllProject, true)
                        .orderByDesc(UserEntity::getCreateTime)
                        .list();
        Set<String> allUserIds =
                allUserList.stream().map(UserEntity::getId).collect(Collectors.toSet());
        if (EmsConstants.WH_ADMIN_USERNAME.equals(SecurityUtil.getUsername())) {
            Set<String> superUser = new HashSet<>();
            superUser.add("1");
            // 用户合并(查询所有项目的用户+分配给这个用户)
            superUser.addAll(allUserIds);
            superUser.addAll(userIds);
            userEntityPage =
                    userService.page(
                            userPageBean,
                            Wrappers.lambdaQuery(UserEntity.class)
                                    .in(UserEntity::getId, superUser)
                                    .orderByDesc(UserEntity::getCreateTime));
        } else {
            // AD用户看ALL用户和其它用户
            if (EmsConstants.USER_ROLE_AD.equals(userEntity.getRole())) {
                // 用户合并(查询所有项目的用户+分配给这个用户)
                allUserIds.addAll(userIds);
                userEntityPage =
                        userService.page(
                                userPageBean,
                                Wrappers.lambdaQuery(UserEntity.class)
                                        .in(UserEntity::getId, allUserIds)
                                        .orderByDesc(UserEntity::getCreateTime));
            } else {
                // admin或者client 只看admin和client
                List<UserEntity> adminUserList =
                        userService
                                .lambdaQuery()
                                .eq(UserEntity::getRole, EmsConstants.USER_ROLE_ADMIN)
                                .ne(UserEntity::getId, userEntity.getId())
                                .list();
                Set<String> adminUserIds =
                        adminUserList.stream().map(UserEntity::getId).collect(Collectors.toSet());
                userIds.removeAll(adminUserIds);
                userEntityPage =
                        userService.page(
                                userPageBean,
                                Wrappers.lambdaQuery(UserEntity.class)
                                        .ne(UserEntity::getRole, EmsConstants.USER_ROLE_AD)
                                        .in(UserEntity::getId, userIds)
                                        .orderByDesc(UserEntity::getCreateTime));
            }
        }
        return Result.success(userEntityPage);
    }

    @PostMapping("/getLogs")
    @ApiOperation("查询操作记录")
    @PreAuthorize("hasAuthority('/user/account')")
    public Result<IPage<LogEntity>> getLogs(@RequestBody LogPageRequest logPageRequest) {
        IPage<LogEntity> logPage =
                new Page<>(logPageRequest.getPageNum(), logPageRequest.getPageSize());
        IPage<LogEntity> list =
                logService.page(
                        logPage,
                        new QueryWrapper<LogEntity>()
                                .lambda()
                                .eq(LogEntity::getProjectId, WebUtils.projectId.get())
                                .eq(LogEntity::getUserId, logPageRequest.getUserId())
                                .orderByDesc(LogEntity::getRequestTime));
        list.getRecords()
                .forEach(
                        logEntity -> {
                            try {
                                logEntity.setModule(
                                        MessageSourceHandler.getMessage(logEntity.getModule()));
                                logEntity.setMethod(
                                        MessageSourceHandler.getMessage(logEntity.getMethod()));
                            } catch (NoSuchMessageException e) {
                                log.info(
                                        "[language error]----------------------------->"
                                                + e.getMessage());
                            }
                        });
        return Result.success(list);
    }

    @PostMapping("/updateOtherPhone")
    @ApiOperation("修改其它用户手机")
    @PreAuthorize("hasAuthority('/user/account')")
    @Log(module = "USER_MANAGE", methods = "USER_MANAGE_CH_OTHER_PHONES")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateOtherPhone(@RequestBody UserOtherPhoneRequest userOtherPhoneRequest) {
        userService.updateOtherPhone(userOtherPhoneRequest);
        return Result.success();
    }

    @PostMapping("/getUserByUserName")
    @ApiOperation("根据用户名查询用户")
    @PreAuthorize("hasAuthority('/user/account')")
    public Result<UserEntity> getPagesUsers(@RequestBody UserNameRequest userNameRequest) {
        UserEntity userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getUserName, userNameRequest.getUserName())
                        .one();
        return Result.success(userEntity);
    }
}
