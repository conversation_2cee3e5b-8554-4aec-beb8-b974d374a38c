package com.wifochina.modules.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.RegexUtil;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.request.DataCalibrationOperatorGet;
import com.wifochina.modules.user.entity.*;
import com.wifochina.modules.user.mapper.RoleMapper;
import com.wifochina.modules.user.mapper.UserMapper;
import com.wifochina.modules.user.request.*;
import com.wifochina.modules.user.service.*;
import com.wifochina.modules.user.vo.UserInfoVo;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 用户业务
 *
 * <AUTHOR>
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserEntity> implements UserService {

    @Resource private UserMapper userMapper;

    @Resource private RoleMapper roleMapper;

    @Resource private UserRoleService userRoleService;

    @Resource private RoleAuthorityService roleAuthorityService;

    @Resource private AuthorityService authorityService;

    @Resource private PasswordEncoder passwordEncoder;

    @Resource private UserProjectService userProjectService;

    @Resource private ProjectService projectService;

    @Resource private RedisTemplate<String, String> redisTemplate;

    @Value("${ems.version}")
    private String version;

    @Resource private GroupService groupService;

    @Resource private DeviceService deviceService;
    @Resource private AmmeterService ammeterService;

    /** 修改当前用户密码 */
    @Override
    public void updatePassword(UserUpdatePasswordRequest updatePasswordVo, boolean check) {
        String newPassword = updatePasswordVo.getNewPassword();
        String reenterPassword = updatePasswordVo.getReenterPassword();
        ServiceAssert.isEmpty(newPassword);
        ServiceAssert.isEmpty(reenterPassword);
        boolean equals = newPassword.equals(reenterPassword);
        ServiceAssert.isTrue(equals, ErrorResultCode.PASSWORD_ARE_INCONSISTENT.value());
        ServiceAssert.isTrue(
                RegexUtil.checkPassword(newPassword), ErrorResultCode.PASSWORD_FORMAT.value());
        UserEntity user;
        if (check) {
            String oldPassword = updatePasswordVo.getOldPassword();
            ServiceAssert.isEmpty(oldPassword);
            String userId = SecurityUtil.getUserId();
            user = userMapper.selectById(userId);
            String realPassword = user.getPassword();
            boolean matches = passwordEncoder.matches(oldPassword, realPassword);
            ServiceAssert.isTrue(matches, ErrorResultCode.USER_PASSWORD_MISTYPED.value());
        } else {
            user = userMapper.selectById(updatePasswordVo.getId());
            ServiceAssert.isTrue(null != user, ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        user.setPassword(passwordEncoder.encode(newPassword));
        int count = userMapper.updateById(user);
        ServiceAssert.isTrue(count > 0, ErrorResultCode.SERVICE_EXCEPTION.value());
    }

    /** 用户注册 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String register(UserRegisterRequest userRegisterRequest) {
        ServiceAssert.isTrue(
                userRegisterRequest.getPassword().equals(userRegisterRequest.getConfirmPassword()),
                ErrorResultCode.PASSWORD_ARE_INCONSISTENT.value());
        if (!userRegisterRequest.getType().equals(EmsConstants.USER_ROLE_AD)) {
            ServiceAssert.isTrue(
                    RegexUtil.checkPassword(userRegisterRequest.getPassword()),
                    ErrorResultCode.PASSWORD_FORMAT.value());
            if (Strings.isNullOrEmpty(userRegisterRequest.getUserName())
                    || !RegexUtil.checkUserName(userRegisterRequest.getUserName())) {
                throw new ServiceException(ErrorResultCode.USERNAME_FORMAT.value());
            }
        }
        String password = userRegisterRequest.getPassword();
        String roleId = userRegisterRequest.getRoleId();
        UserEntity user = new UserEntity();
        user.setUserName(userRegisterRequest.getUserName());
        if (StringUtils.hasLength(userRegisterRequest.getName())) {
            user.setName(userRegisterRequest.getName());
        }
        user.setRoleId(userRegisterRequest.getRoleId());
        user.setPassword(passwordEncoder.encode(password));
        user.setRole(userRegisterRequest.getType());
        user.setVisitManage(Boolean.FALSE);
        user.setIsDelete(Boolean.FALSE);
        if (roleId != null) {
            RoleEntity role = roleMapper.selectById(roleId);
            user.setRoleName(role.getRoleName());
            userMapper.insert(user);
            UserRoleEntity userRole = new UserRoleEntity();
            userRole.setUserId(user.getId());
            userRole.setRoleId(roleId);
            userRoleService.save(userRole);
            UserProjectEntity userProjectEntity = new UserProjectEntity();
            userProjectEntity.setProjectId(WebUtils.projectId.get());
            userProjectEntity.setUserId(user.getId());
            userProjectService.save(userProjectEntity);
        } else {
            userMapper.insert(user);
        }
        return user.getId();
    }

    /** 管理用户注册 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registerManage(UserRegisterRequest userRegisterRequest) {
        ServiceAssert.isTrue(
                userRegisterRequest.getPassword().equals(userRegisterRequest.getConfirmPassword()),
                ErrorResultCode.PASSWORD_ARE_INCONSISTENT.value());
        if (!userRegisterRequest.getType().equals(EmsConstants.USER_ROLE_AD)) {
            ServiceAssert.isTrue(
                    RegexUtil.checkPassword(userRegisterRequest.getPassword()),
                    ErrorResultCode.PASSWORD_FORMAT.value());
            if (Strings.isNullOrEmpty(userRegisterRequest.getUserName())
                    || !RegexUtil.checkUserName(userRegisterRequest.getUserName())) {
                throw new ServiceException(ErrorResultCode.USERNAME_FORMAT.value());
            }
        }
        String password = userRegisterRequest.getPassword();
        String roleId = userRegisterRequest.getRoleId();
        UserEntity user = new UserEntity();
        user.setUserName(userRegisterRequest.getUserName());
        user.setRoleId(userRegisterRequest.getRoleId());
        if (StringUtils.hasLength(userRegisterRequest.getName())) {
            user.setName(userRegisterRequest.getName());
        }
        user.setPassword(passwordEncoder.encode(password));
        user.setRole(userRegisterRequest.getType());
        user.setIsDelete(Boolean.FALSE);
        user.setVisitManage(userRegisterRequest.getVisitManage());
        Optional.ofNullable(userRegisterRequest.getAllProject()).ifPresent(user::setAllProject);
        if (roleId != null) {
            RoleEntity role = roleMapper.selectById(roleId);
            user.setRoleName(role.getRoleName());
            userMapper.insert(user);
            UserRoleEntity userRole = new UserRoleEntity();
            userRole.setUserId(user.getId());
            userRole.setRoleId(roleId);
            userRoleService.save(userRole);
            Optional.ofNullable(userRegisterRequest.getProjectIds())
                    .ifPresent(
                            e -> {
                                for (String projectId : userRegisterRequest.getProjectIds()) {
                                    UserProjectEntity userProjectEntity = new UserProjectEntity();
                                    userProjectEntity.setProjectId(projectId);
                                    userProjectEntity.setUserId(user.getId());
                                    userProjectService.save(userProjectEntity);
                                }
                            });
        } else {
            userMapper.insert(user);
        }
    }

    /**
     * 获取用户详情
     *
     * @param userId 用户id
     */
    @Override
    public UserInfoVo getUserInfo(String userId) {
        ServiceAssert.notNull(userId);

        UserEntity userEntity = userMapper.selectById(userId);
        ServiceAssert.notNull(userEntity, ErrorResultCode.SERVICE_EXCEPTION.value());

        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(userEntity, userInfoVo);
        if (EmsConstants.WH_ADMIN_USERNAME.equals(userInfoVo.getUserName())) {
            List<AuthorityEntity> byIds = authorityService.list();
            userInfoVo.setAuthorities(byIds);

        } else {
            UserRoleEntity userRoleEntity =
                    userRoleService.getOne(
                            new QueryWrapper<UserRoleEntity>()
                                    .lambda()
                                    .eq(UserRoleEntity::getUserId, userId));
            Optional.ofNullable(userRoleEntity)
                    .ifPresent(
                            (e) -> {
                                List<RoleAuthorityEntity> accessPointByRoleIds =
                                        roleAuthorityService.list(
                                                new QueryWrapper<RoleAuthorityEntity>()
                                                        .lambda()
                                                        .in(
                                                                RoleAuthorityEntity::getRoleId,
                                                                Collections.singletonList(
                                                                        userRoleEntity
                                                                                .getRoleId())));
                                if (accessPointByRoleIds.isEmpty()) {
                                    userInfoVo.setAuthorities(new ArrayList<>());
                                } else {
                                    List<Long> collect =
                                            accessPointByRoleIds.stream()
                                                    .map(RoleAuthorityEntity::getAuthorityId)
                                                    .collect(Collectors.toList());
                                    List<AuthorityEntity> byIds =
                                            authorityService.list(
                                                    new QueryWrapper<AuthorityEntity>()
                                                            .lambda()
                                                            .in(AuthorityEntity::getId, collect));
                                    userInfoVo.setAuthorities(byIds);
                                }
                            });
        }
        userInfoVo.setUid(userId);
        return userInfoVo;
    }

    /**
     * 获取用户详情
     *
     * @param id 用户id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeUser(String id) {
        userMapper.deleteById(id);
        userRoleService.remove(
                Wrappers.lambdaQuery(UserRoleEntity.class).eq(UserRoleEntity::getUserId, id));
        userProjectService.remove(
                Wrappers.lambdaQuery(UserProjectEntity.class).eq(UserProjectEntity::getUserId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserRole(UserUpdateRoleRequest userUpdateRoleRequest) {
        if (!userUpdateRoleRequest.getUserName().equals(EmsConstants.WH_ADMIN_USERNAME)) {
            RoleEntity roleEntity = roleMapper.selectById(userUpdateRoleRequest.getRoleId());
            UserEntity oldUserEntity = userMapper.selectById(userUpdateRoleRequest.getId());
            UserEntity userEntity = new UserEntity();
            userEntity.setId(userUpdateRoleRequest.getId());
            if (StringUtils.hasLength(userUpdateRoleRequest.getName())) {
                userEntity.setName(userUpdateRoleRequest.getName());
            }
            userEntity.setRoleName(roleEntity.getRoleName());
            userEntity.setRoleId(userUpdateRoleRequest.getRoleId());
            userMapper.updateById(userEntity);
            UserRoleEntity userRoleEntity =
                    userRoleService.getOne(
                            Wrappers.lambdaQuery(UserRoleEntity.class)
                                    .eq(UserRoleEntity::getUserId, userUpdateRoleRequest.getId()));
            Optional.ofNullable(userRoleEntity)
                    .ifPresentOrElse(
                            (e) ->
                                    userRoleService.update(
                                            Wrappers.lambdaUpdate(UserRoleEntity.class)
                                                    .eq(
                                                            UserRoleEntity::getUserId,
                                                            userUpdateRoleRequest.getId())
                                                    .set(
                                                            UserRoleEntity::getRoleId,
                                                            userUpdateRoleRequest.getRoleId())),
                            () -> {
                                UserRoleEntity urEntity = new UserRoleEntity();
                                urEntity.setRoleId(userUpdateRoleRequest.getRoleId());
                                urEntity.setUserId(userUpdateRoleRequest.getId());
                                userRoleService.save(urEntity);
                            });
            if (!Objects.equals(userUpdateRoleRequest.getRoleId(), oldUserEntity.getRoleId())) {
                redisTemplate.delete(userEntity.getId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserManage(UserUpdateRoleRequest userUpdateRoleRequest) {
        if (!userUpdateRoleRequest.getUserName().equals(EmsConstants.WH_ADMIN_USERNAME)) {
            RoleEntity roleEntity = roleMapper.selectById(userUpdateRoleRequest.getRoleId());
            UserEntity userEntity = new UserEntity();
            userEntity.setId(userUpdateRoleRequest.getId());
            UserEntity oldUserEntity = userMapper.selectById(userUpdateRoleRequest.getId());
            if (EmsConstants.ADMIN_USERNAME.equals(oldUserEntity.getRole())) {
                userEntity.setName(userUpdateRoleRequest.getName());
            }
            userEntity.setRoleName(roleEntity.getRoleName());
            userEntity.setRoleId(userUpdateRoleRequest.getRoleId());
            Optional.ofNullable(userUpdateRoleRequest.getVisitManage())
                    .ifPresent(userEntity::setVisitManage);
            Optional.ofNullable(userUpdateRoleRequest.getAllProject())
                    .ifPresent(userEntity::setAllProject);
            userMapper.updateById(userEntity);
            UserRoleEntity userRoleEntity =
                    userRoleService.getOne(
                            Wrappers.lambdaQuery(UserRoleEntity.class)
                                    .eq(UserRoleEntity::getUserId, userUpdateRoleRequest.getId()));
            Optional.ofNullable(userRoleEntity)
                    .ifPresentOrElse(
                            (e) ->
                                    userRoleService.update(
                                            Wrappers.lambdaUpdate(UserRoleEntity.class)
                                                    .eq(
                                                            UserRoleEntity::getUserId,
                                                            userUpdateRoleRequest.getId())
                                                    .set(
                                                            UserRoleEntity::getRoleId,
                                                            userUpdateRoleRequest.getRoleId())),
                            () -> {
                                UserRoleEntity urEntity = new UserRoleEntity();
                                urEntity.setRoleId(userUpdateRoleRequest.getRoleId());
                                urEntity.setUserId(userUpdateRoleRequest.getId());
                                userRoleService.save(urEntity);
                            });
            // 删除旧的项目
            userProjectService.remove(
                    Wrappers.lambdaQuery(UserProjectEntity.class)
                            .eq(UserProjectEntity::getUserId, userEntity.getId()));
            Optional.ofNullable(userUpdateRoleRequest.getProjectIds())
                    .ifPresent(
                            e -> {
                                for (String projectId : userUpdateRoleRequest.getProjectIds()) {
                                    UserProjectEntity userProjectEntity = new UserProjectEntity();
                                    userProjectEntity.setProjectId(projectId);
                                    userProjectEntity.setUserId(userEntity.getId());
                                    userProjectService.save(userProjectEntity);
                                }
                            });
            if (!Objects.equals(userUpdateRoleRequest.getRoleId(), oldUserEntity.getRoleId())) {
                redisTemplate.delete(userEntity.getId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOtherPhone(UserOtherPhoneRequest userOtherPhoneRequest) {
        UserEntity user =
                userMapper.selectOne(
                        Wrappers.lambdaQuery(UserEntity.class)
                                .eq(UserEntity::getPhone, userOtherPhoneRequest.getPhone()));
        if (user != null) {
            userMapper.update(
                    user,
                    Wrappers.lambdaUpdate(UserEntity.class)
                            .set(UserEntity::getPhone, null)
                            .eq(UserEntity::getId, user.getId()));
        }
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userOtherPhoneRequest.getId());
        userEntity.setPhone(userOtherPhoneRequest.getPhone());
        userMapper.updateById(userEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOtherEmail(UserOtherEmailRequest userOtherEmailRequest) {
        UserEntity user =
                userMapper.selectOne(
                        Wrappers.lambdaQuery(UserEntity.class)
                                .eq(UserEntity::getEmail, userOtherEmailRequest.getEmail()));
        if (user != null) {
            userMapper.update(
                    user,
                    Wrappers.lambdaUpdate(UserEntity.class)
                            .set(UserEntity::getEmail, null)
                            .eq(UserEntity::getId, user.getId()));
        }
        UserEntity userEntity = new UserEntity();
        userEntity.setId(userOtherEmailRequest.getId());
        userEntity.setEmail(userOtherEmailRequest.getEmail());
        userMapper.updateById(userEntity);
    }

    @Override
    public List<String> getUserAuthorityByUserId(String userId) {
        UserEntity userEntity = userMapper.selectById(userId);
        return getUserAuthority(userEntity);
    }

    @Override
    public List<String> getUserAuthorityByUserName(String userName) {
        UserEntity userEntity =
                userMapper.selectOne(
                        Wrappers.lambdaQuery(UserEntity.class)
                                .eq(UserEntity::getUserName, userName));
        return getUserAuthority(userEntity);
    }

    public List<String> getUserAuthority(UserEntity userEntity) {
        List<String> authorityList = new ArrayList<>();
        if (!EmsConstants.WH_ADMIN_USERNAME.equalsIgnoreCase(userEntity.getUserName())) {
            UserRoleEntity userRoleEntity =
                    userRoleService.getOne(
                            new LambdaQueryWrapper<UserRoleEntity>()
                                    .eq(UserRoleEntity::getUserId, userEntity.getId()));
            // 获取角色控制点
            Optional.ofNullable(userRoleEntity)
                    .ifPresent(
                            (e) -> {
                                List<RoleAuthorityEntity> roleAuthorityList =
                                        roleAuthorityService.list(
                                                new LambdaQueryWrapper<RoleAuthorityEntity>()
                                                        .in(
                                                                RoleAuthorityEntity::getRoleId,
                                                                Collections.singletonList(
                                                                        userRoleEntity
                                                                                .getRoleId())));
                                if (roleAuthorityList != null && !roleAuthorityList.isEmpty()) {
                                    List<Long> apIds =
                                            roleAuthorityList.stream()
                                                    .map(RoleAuthorityEntity::getAuthorityId)
                                                    .collect(Collectors.toList());
                                    if (!apIds.isEmpty()) {
                                        List<AuthorityEntity> authorities =
                                                authorityService.listByIds(apIds);
                                        if (authorities != null && !authorities.isEmpty()) {
                                            authorityList.addAll(
                                                    authorities.stream()
                                                            .map(AuthorityEntity::getApKey)
                                                            .collect(Collectors.toList()));
                                        }
                                    }
                                }
                            });
        } else {
            // 默认拥有全部权限
            List<AuthorityEntity> authorities = authorityService.list();
            if (authorities != null && !authorities.isEmpty()) {
                authorityList.addAll(
                        authorities.stream()
                                .map(AuthorityEntity::getApKey)
                                .collect(Collectors.toList()));
            }
        }
        return authorityList;
    }

    @Override
    public List<ProjectEntity> getProjectsByUserId(String userId) {
        UserEntity userEntity = userMapper.selectById(userId);
        return getProjectsByUserEntity(userEntity);
    }

    @Override
    public List<ProjectEntity> getProjectsByUserName(String userName) {
        UserEntity userEntity =
                userMapper.selectOne(
                        Wrappers.lambdaQuery(UserEntity.class)
                                .eq(UserEntity::getUserName, userName));
        return getProjectsByUserEntity(userEntity);
    }

    List<ProjectEntity> getProjectsByUserEntity(UserEntity userEntity) {
        List<ProjectEntity> list = new ArrayList<>();
        if (EmsConstants.WH_ADMIN_USERNAME.equals(userEntity.getUserName())
                || userEntity.getAllProject()) {
            list =
                    projectService.list(
                            Wrappers.lambdaQuery(ProjectEntity.class)
                                    .eq(ProjectEntity::getWhetherDelete, false)
                                    .orderByDesc(ProjectEntity::getCreateTime));
            list.forEach(projectEntity -> projectEntity.setVersion(version));
        } else {
            List<UserProjectEntity> userProjectEntities =
                    userProjectService.list(
                            Wrappers.lambdaQuery(UserProjectEntity.class)
                                    .eq(UserProjectEntity::getUserId, userEntity.getId()));
            if (userProjectEntities != null && !userProjectEntities.isEmpty()) {
                List<String> projectIds =
                        userProjectEntities.stream()
                                .map(UserProjectEntity::getProjectId)
                                .collect(Collectors.toList());
                list =
                        projectService.list(
                                Wrappers.lambdaQuery(ProjectEntity.class)
                                        .eq(ProjectEntity::getWhetherDelete, false)
                                        .in(ProjectEntity::getId, projectIds)
                                        .orderByDesc(ProjectEntity::getCreateTime));
                list.forEach(projectEntity -> projectEntity.setVersion(version));
            }
        }
        // 应虎佳要求 在这里把 系统分组的 pcsCode返回
        Map<String, GroupEntity> systemGroupsMap =
                groupService.querySystemGroupPcsCodes(
                        list.stream().map(ProjectEntity::getId).collect(Collectors.toList()));
        // 判断maintain
        List<DeviceEntity> deviceEntities =
                deviceService.lambdaQuery().eq(DeviceEntity::getMaintain, true).list();
        List<AmmeterEntity> ammeterEntities =
                ammeterService.lambdaQuery().eq(AmmeterEntity::getMaintain, true).list();
        Set<String> deviceProjectIds =
                deviceEntities.stream().map(DeviceEntity::getProjectId).collect(Collectors.toSet());
        Set<String> meterProjectIds =
                ammeterEntities.stream()
                        .map(AmmeterEntity::getProjectId)
                        .collect(Collectors.toSet());
        list.forEach(
                projectEntity -> {
                    GroupEntity systemGroup = systemGroupsMap.get(projectEntity.getId());
                    if (systemGroup != null) {
                        projectEntity.setPcsCode(systemGroup.getPcsCode());
                    }
                    projectEntity.setMaintain(
                            deviceProjectIds.contains(projectEntity.getId())
                                    || meterProjectIds.contains(projectEntity.getId()));
                });
        return list;
    }

    public boolean hasAllProject(String userId) {
        UserEntity user = getById(userId);
        return EmsConstants.WH_ADMIN_USERNAME.equals(user.getUserName()) || user.getAllProject();
    }

    @Override
    public List<String> getProjectIdsByUserId(String userId) {
        UserEntity userEntity = userMapper.selectById(userId);
        return getProjectsByUserEntity(userEntity).stream()
                .map(ProjectEntity::getId)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, UserEntity> getUserIdsMaps(List<String> userIds) {
        if (CollectionUtil.isNotEmpty(userIds)) {
            return userMapper.selectBatchIds(userIds).stream()
                    .collect(Collectors.toMap(UserEntity::getId, v -> v));
        } else {
            return new HashMap<>();
        }
    }

    @Override
    public <E extends DataCalibrationOperatorGet> void fillUserName(List<E> list) {
        List<String> userIds = list.stream().map(E::getUserId).collect(Collectors.toList());
        Map<String, UserEntity> userIdsMaps = this.getUserIdsMaps(userIds);
        for (E e : list) {
            UserEntity userEntity = userIdsMaps.get(e.getUserId());
            if (userEntity != null) {
                e.setUserName(userEntity.getUserName());
            }
        }
    }
}
