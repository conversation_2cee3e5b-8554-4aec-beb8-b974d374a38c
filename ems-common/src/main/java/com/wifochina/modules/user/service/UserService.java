package com.wifochina.modules.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.remedies.request.DataCalibrationOperatorGet;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.request.*;
import com.wifochina.modules.user.vo.UserInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 用户业务层
 *
 * <AUTHOR>
 */
public interface UserService extends IService<UserEntity> {
    /**
     * 判断用户是否拥有所有项目
     *
     * @param userId 用户 id
     * @return true
     */
    boolean hasAllProject(String userId);

    /**
     * 注册用户
     *
     * @param userRegisterRequest 用户信息
     */
    String register(UserRegisterRequest userRegisterRequest);

    /**
     * 注册用户
     *
     * @param userRegisterRequest 用户信息
     */
    void registerManage(UserRegisterRequest userRegisterRequest);

    /**
     * 修改密码
     *
     * @param userUpdatePasswordRequest 密码更新
     * @param check 旧密码检查
     */
    void updatePassword(UserUpdatePasswordRequest userUpdatePasswordRequest, boolean check);

    /**
     * 获取用户详情
     *
     * @param userId 用户id
     * @return UserInfoVo
     */
    UserInfoVo getUserInfo(String userId);

    /**
     * 删除用户
     *
     * @param id 用户id
     */
    void removeUser(String id);

    /**
     * 更新用户角色
     *
     * @param userUpdateRoleRequest 用户角色
     */
    void updateUserRole(UserUpdateRoleRequest userUpdateRoleRequest);

    /**
     * 更新用户信息
     *
     * @param userUpdateRoleRequest 用户信息
     */
    void updateUserManage(UserUpdateRoleRequest userUpdateRoleRequest);

    /**
     * 修改手机
     *
     * @param userOtherPhoneRequest 用户手机号
     */
    void updateOtherPhone(UserOtherPhoneRequest userOtherPhoneRequest);

    /**
     * 修改邮箱
     *
     * @param userOtherEmailRequest 用户邮箱号
     */
    void updateOtherEmail(UserOtherEmailRequest userOtherEmailRequest);

    /**
     * 通过用户Id获取用户权限
     *
     * @param userId 用户权限
     * @return 用户权限表
     */
    List<String> getUserAuthorityByUserId(String userId);

    /**
     * 通过用户名获取用户权限
     *
     * @param userName 用户名
     * @return 用户权限表
     */
    List<String> getUserAuthorityByUserName(String userName);

    /**
     * 通过用户id获取当前用户项目列表
     *
     * @param userId 用户id
     * @return 项目列表
     */
    List<ProjectEntity> getProjectsByUserId(String userId);

    /**
     * 通过用户名获取当前用户项目列表
     *
     * @param userName 用户名
     * @return 项目列表
     */
    List<ProjectEntity> getProjectsByUserName(String userName);

    /** 通过用户id获取当前用户项目列表 */
    List<String> getProjectIdsByUserId(String userId);

    Map<String, UserEntity> getUserIdsMaps(List<String> userIds);

    <E extends DataCalibrationOperatorGet> void fillUserName(List<E> list);
}
