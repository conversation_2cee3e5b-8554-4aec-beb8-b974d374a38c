package com.wifochina.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "t_log")
@ApiModel(value = "系统日志表", description = "用户操作日志")
public class LogEntity {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "所属模块")
    private String module;

    @ApiModelProperty(value = "调用的方法")
    private String method;

    @ApiModelProperty(value = "调用的ip")
    private String ip;

    @ApiModelProperty(value = "调用时间")
    private Long requestTime;

    @ApiModelProperty(value = "操作员id")
    private String userId;

    @ApiModelProperty(value = "耗时")
    private Long costTime;

    @ApiModelProperty(value = "操作结果")
    private String result;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @TableField(exist = false)
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "姓名")
    @TableField(exist = false)
    private String name;

    @ApiModelProperty(value = "参数")
    private String params;

    @ApiModelProperty(value = "细节")
    private String detail;

    @ApiModelProperty(value = "客户端")
    private String client;

    @ApiModelProperty(value = "标识id")
    private String traceId;
}
