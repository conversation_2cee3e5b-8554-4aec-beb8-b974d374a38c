package com.wifochina.modules.user.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UserRequestPage
 * 
 * @date 9/16/2022 10:43 AM
 * <AUTHOR>
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "管理端-用户分页查询请求")
public class UserPageRequest extends PageBean {

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "角色名")
    private String role;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
}
