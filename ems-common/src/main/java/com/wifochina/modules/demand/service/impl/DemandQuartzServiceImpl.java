package com.wifochina.modules.demand.service.impl;

import com.wifochina.common.config.SystemConfiguration;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.demand.job.*;
import com.wifochina.modules.demand.service.DemandQuartzService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.extern.slf4j.Slf4j;

import org.quartz.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-15 7:28 PM
 */
@Slf4j
@Component
public class DemandQuartzServiceImpl implements DemandQuartzService {

    private static final String JOB_GROUP = "demand-group";
    private static final String NEW_JOB_GROUP = "new-demand-group";

    private static final String JOB_CAPACITY_GROUP = "capacity-group";

    @Resource private Scheduler scheduler;

    @Resource private GroupService groupService;

    @Resource private ProjectService projectService;

    @Resource private StrategyService strategyService;

    @Resource private SystemConfiguration systemConfiguration;
    public static final String JOB_NAME_PLACEHOLDER = "demand-calc-job:%s";
    public static final String JOB_NAME_TIMEZONE_PLACEHOLDER = "high-freq-demand-calc-job:%s";
    public static final String JOB_TRIGGER_PLACEHOLDER = "demand-calc-trigger:%s";
    public static final String JOB_TRIGGER_TIMEZONE_PLACEHOLDER =
            "high-freq-demand-calc-trigger:%s";

    public void addDemandCalcJobNew(String timezone) {
        try {
            // 查询 projectList 符合要求的项目列表 然后
            List<ProjectEntity> projectEntities = projectService.getProjectsByTimeZone(timezone);
            projectEntities.forEach(
                    project -> {
                        List<GroupEntity> groupEntities =
                                groupService.queryEnableDemandControl(project.getId());
                        if (!groupEntities.isEmpty()) {
                            DemandCalcHolder.Companion.getHolder()
                                    .computeIfAbsent(
                                            timezone,
                                            s -> Collections.synchronizedSet(new HashSet<>()))
                                    .add(project);
                        }
                    });
            JobDetail jobDetail = getDemandCalcJobDetailNew(timezone, false);
            Trigger trigger = getDemandCalcTriggerNew(timezone);
            if (trigger != null) {
                scheduler.scheduleJob(jobDetail, trigger);
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
                String cronExpression = ((CronTrigger) trigger).getCronExpression();
                log.info(
                        "添加 high frequency DemandCalcJob 成功 timezone:{}, trigger cron:{}",
                        timezone,
                        cronExpression);
            }
        } catch (ObjectAlreadyExistsException existsException) {
            log.warn("job exists:{}", existsException.getMessage());
        } catch (SchedulerException e) {
            log.error(e.getMessage());
        }
    }

    /**
     * 添加定时任务 jobName 任务名 自定义 jobGroup 任务组 自定义 cron 时间表达式 jobClass 任务实现类 如 StartUserJob 或
     * CloseUserJob jobDataMap 自定义参数
     */
    @Override
    public void addDemandCalcJobByTimeZone(String timezone) {
        addDemandCalcJobNew(timezone);
        //        try {
        //            ProjectEntity projectEntity = projectService.getById(projectId);
        //            // 构建JobDetail (表示一个具体的可执行的调度程序，Job是这个可执行调度程序所要执行的内容)
        //            JobDetail jobDetail = getDemandCalcJobDetail(projectId, false);
        //            // 构建出发去Trigger （调度参数的配置，代表何时出发该任务)
        //            Trigger trigger = getDemandCalcTrigger(projectId,
        // projectEntity.getTimezone());
        //            if (trigger != null) {
        //                scheduler.scheduleJob(jobDetail, trigger);
        //                if (!scheduler.isStarted()) {
        //                    scheduler.start();
        //                }
        //                String cronExpression = ((CronTrigger) trigger).getCronExpression();
        //                log.info(
        //                        "添加DemandCalcJob 成功 projectId:{}, projectName:{} trigger cron:{}",
        //                        projectId,
        //                        projectEntity.getProjectName(),
        //                        cronExpression);
        //            }
        //        } catch (ObjectAlreadyExistsException existsException) {
        //            log.warn("job exists:{}", existsException.getMessage());
        //        } catch (SchedulerException e) {
        //            log.error(e.getMessage());
        //        }
    }

    public void startNowTest(String projectId, Map<String, Object> initParamsMap) {
        //        JobDetail jobDetail = getDemandCalcJobDetail(projectId, true);
        JobDetail jobDetail =
                getDemandCalcJobDetailNew((String) initParamsMap.get("timezone"), true);
        ProjectEntity projectEntity = projectService.getById(projectId);
        JobDataMap jobDataMap = jobDetail.getJobDataMap();
        // 将initParamsMap中的值放入JobDataMap
        String triggerName = String.format(JOB_TRIGGER_PLACEHOLDER, projectId);
        jobDataMap.putAll(initParamsMap);
        Trigger trigger =
                TriggerBuilder.newTrigger()
                        .withIdentity("test_" + triggerName, "test_" + JOB_GROUP)
                        .startNow()
                        .build();
        try {
            String jobName =
                    String.format(
                            "test_" + JOB_NAME_TIMEZONE_PLACEHOLDER, projectEntity.getTimezone());
            JobKey jobKey = new JobKey(jobName, "test_" + JOB_GROUP);
            if (!scheduler.checkExists(jobKey)) {
                // 创建并调度Job
                scheduler.scheduleJob(jobDetail, trigger);
            } else {
                scheduler.deleteJob(jobKey);
                // Job已经存在，可以选择更新Job或跳过
                JobDetail existingJobDetail = scheduler.getJobDetail(jobKey);
                JobDataMap existingJobDataMap = existingJobDetail.getJobDataMap();
                // 更新已有Job的JobDataMap中的参数
                existingJobDataMap.putAll(initParamsMap);

                // 重新添加或更新Job到调度器 true表示覆盖已有Job
                scheduler.addJob(existingJobDetail, true);
                // 触发现有的Job
                scheduler.triggerJob(jobKey);
                System.out.println("demand cal Job already exists");
            }
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void rescheduleDemandCalcJob(String projectId) {
        //        delDemandCalcJob(projectId);
        //        addDemandCalcJob(projectId);
    }

    @Override
    public void delDemandCalcJob(String projectId) {
        try {
            // TriggerKey 定义了trigger的名称和组别 ，通过任务名和任务组名获取TriggerKey
            String triggerName = getDemandCalcTriggerName(projectId);
            TriggerKey triggerKey = TriggerKey.triggerKey(triggerName, JOB_GROUP);
            // 停止触发器
            scheduler.resumeTrigger(triggerKey);
            // 移除触发器
            scheduler.unscheduleJob(triggerKey);
            // 移除任务
            String jobName = getDemandCalcJobName(projectId);
            scheduler.deleteJob(JobKey.jobKey(jobName, JOB_GROUP));
            log.info("删除定时任务成功");
        } catch (SchedulerException e) {
            log.error("删除DemandCalcJob失败 :{}", e.getMessage());
        }
    }

    @Override
    public void pauseDemandCalcJob(String projectId) {
        try {
            String jobName = getDemandCalcJobName(projectId);
            JobKey jobKey = JobKey.jobKey(jobName, JOB_GROUP);
            // 暂停任务
            scheduler.pauseJob(jobKey);
            log.info("暂停定时任务成功");
        } catch (SchedulerException e) {
            log.error("");
        }
    }

    @Override
    public void resumeDemandCalcJob(String projectId) {
        try {
            // 通过任务名和任务组名获取jobKey
            String jobName = getDemandCalcJobName(projectId);
            JobKey jobKey = JobKey.jobKey(jobName, JOB_GROUP);
            // 继续任务
            scheduler.resumeJob(jobKey);
            log.info("继续定时任务成功");
        } catch (SchedulerException e) {
            log.error("");
        }
    }

    @Override
    public void addDemandMonthInitJob(String projectId) {
        try {
            ProjectEntity projectEntity = projectService.getById(projectId);
            // 构建JobDetail (表示一个具体的可执行的调度程序，Job是这个可执行调度程序所要执行的内容)
            JobDetail jobDetail = getDemandMonthInitJobDetail(projectId);
            // 构建出发去Trigger （调度参数的配置，代表何时出发该任务)
            Trigger trigger = getMonthInitTrigger(projectId, projectEntity.getTimezone());
            if (trigger != null) {
                scheduler.scheduleJob(jobDetail, trigger);
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
            }
        } catch (SchedulerException e) {
            log.error(e.getMessage());
        }
    }

    @Override
    public void delDemandMonthInitJob(String projectId) {
        try {
            // TriggerKey 定义了trigger的名称和组别 ，通过任务名和任务组名获取TriggerKey
            String triggerName = getDemandMonthInitTriggerName(projectId);
            TriggerKey triggerKey = TriggerKey.triggerKey(triggerName, JOB_GROUP);
            // 停止触发器
            scheduler.resumeTrigger(triggerKey);
            // 移除触发器
            scheduler.unscheduleJob(triggerKey);
            // 移除任务
            String jobName = getDemandMonthInitJobName(projectId);
            scheduler.deleteJob(JobKey.jobKey(jobName, JOB_GROUP));
            log.info("删除月初定时任务成功");
        } catch (SchedulerException e) {
            log.error("");
        }
    }

    @Override
    public void rescheduleDemandMonthInitJob(String projectId) {
        delDemandMonthInitJob(projectId);
        addDemandMonthInitJob(projectId);
    }

    public static String getDemandCalcJobNameNew(String projectId) {
        return String.format(JOB_NAME_TIMEZONE_PLACEHOLDER, projectId);
    }

    public static String getDemandCalcJobName(String projectId) {
        return String.format(JOB_NAME_PLACEHOLDER, projectId);
    }

    public static String getDemandMonthInitJobName(String projectId) {
        return String.format("demand-month-init-job:%s", projectId);
    }

    public static String getDemandCalcTriggerName(String projectId) {
        return String.format(JOB_TRIGGER_PLACEHOLDER, projectId);
    }

    public static String getDemandCalcTriggerNameNew(String projectId) {
        return String.format(JOB_TRIGGER_TIMEZONE_PLACEHOLDER, projectId);
    }

    public static String getDemandMonthInitTriggerName(String projectId) {
        return String.format("demand-month-init-trigger:%s", projectId);
    }

    public static JobDetail getDemandCalcJobDetailNew(String timezone, boolean testFlag) {
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = getDemandCalcJobNameNew(timezone);
        jobData.put(EmsConstants.TIME_ZONE_KEY, timezone);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        return JobBuilder.newJob(DemandCalcPlatformJob.class)
                .withIdentity(
                        testFlag ? "test_" + jobName : jobName,
                        testFlag ? "test_" + NEW_JOB_GROUP : NEW_JOB_GROUP)
                .usingJobData(jobDataMap)
                .storeDurably()
                .build();
    }

    public static JobDetail getDemandMonthInitJobDetail(String projectId) {
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = getDemandMonthInitJobName(projectId);
        jobData.put(EmsConstants.PROJECT_ID, projectId);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        return JobBuilder.newJob(DemandMonthInitJob.class)
                .withIdentity(jobName, JOB_GROUP)
                .usingJobData(jobDataMap)
                .storeDurably()
                .build();
    }

    public Trigger getDemandCalcTriggerNew(String timezone) {
        String triggerName = getDemandCalcTriggerNameNew(timezone);
        String jobCron =
                String.format(
                        "0 0/%d * * * ?",
                        // 可配置 高频的评率 默认是1分钟
                        systemConfiguration.getDemand().getDemandJobCalcInterval());
        return TriggerBuilder.newTrigger()
                .withIdentity(triggerName, NEW_JOB_GROUP)
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(jobCron)
                                .inTimeZone(TimeZone.getTimeZone(timezone))
                                .withMisfireHandlingInstructionFireAndProceed())
                .build();
    }

    public Trigger getMonthInitTrigger(String projectId, String timezone) {
        String triggerName = getDemandMonthInitTriggerName(projectId);
        List<StrategyEntity> list =
                strategyService
                        .lambdaQuery()
                        .eq(StrategyEntity::getProjectId, projectId)
                        .eq(StrategyEntity::getWeekDay, 0)
                        .isNotNull(StrategyEntity::getMonthControlPower)
                        .list();
        if (list.isEmpty()) {
            return null;
        }
        String jobCron = "0 0 0 1 * ?";
        return TriggerBuilder.newTrigger()
                .withIdentity(triggerName, JOB_GROUP)
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(jobCron)
                                .inTimeZone(TimeZone.getTimeZone(timezone)))
                .build();
    }

    @Override
    public void addCapacityJob(String timezone) {
        String triggerName = "capacity-one-day-trigger";
        String jobCron = "0 10 0 * * ?";
        Trigger trigger =
                TriggerBuilder.newTrigger()
                        .withIdentity(triggerName, JOB_CAPACITY_GROUP)
                        .withSchedule(
                                CronScheduleBuilder.cronSchedule(jobCron)
                                        .inTimeZone(TimeZone.getTimeZone(timezone)))
                        .build();
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = String.format("capacity-job:%s", timezone);
        jobData.put(EmsConstants.TIME_ZONE_KEY, timezone);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        JobDetail jobDetail =
                JobBuilder.newJob(CapacitySaveJob.class)
                        .withIdentity(jobName, JOB_CAPACITY_GROUP)
                        .usingJobData(jobDataMap)
                        .storeDurably()
                        .build();
        if (trigger != null) {
            try {
                scheduler.scheduleJob(jobDetail, trigger);
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
            } catch (ObjectAlreadyExistsException existsException) {
                log.warn("addCapacityJob job exists:{}", existsException.getMessage());
            } catch (SchedulerException e) {
                log.error(e.getMessage());
            }
        }
    }
}
