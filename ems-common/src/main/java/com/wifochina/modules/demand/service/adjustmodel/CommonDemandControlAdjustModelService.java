package com.wifochina.modules.demand.service.adjustmodel;

import com.wifochina.common.constants.EmailLogTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.retry.IRetryTaskExecutor;
import com.wifochina.common.retry.RetryFlow;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.DemandControlAdjustModelEnum;
import com.wifochina.common.util.EmailService;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import com.wifochina.modules.demand.service.*;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import com.wifochina.modules.strategy.service.TimeSharingDemandService;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.Nullable;
import org.mapstruct.ap.internal.util.RoundContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * Created on 2024/9/25 14:25. 通用的 需量计算逻辑 包含 大体的流程
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public abstract class CommonDemandControlAdjustModelService
        implements DemandControlAdjustModelService {

    @Resource private StrategyService strategyService;
    @Resource private DemandLogService demandLogService;
    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource private RetryTemplate retryTemplate;
    @Resource private DemandAutoUpRetryTaskExecutor demandAutoUpRetryTaskExecutor;
    @Resource private EmailService emailService;
    @Resource private DemandEmailService demandEmailService;
    @Resource private DemandAlarmService demandAlarmService;
    @Resource private NewDemandServiceKt newDemandServiceKt;
    @Resource private InfluxClientService influxClientService;
    @Resource private DemandService demandService;
    @Resource private TimeSharingDemandService timeSharingDemandService;

    protected abstract OaDemandData roundMaxDemand(RoundContext roundContext);

    private CompareResult compareExceeded(RoundContext roundContext) {
        ProjectEntity project = roundContext.project;
        GroupEntity group = roundContext.group;
        Double controlPower = roundContext.controlPower;

        // 获取到数据库里当前最大的 需量
        Map<String, Double> demandAlarmMap =
                demandAlarmService.getDemandMax(
                        project.getId(), MyTimeUtil.getCurrentMonthZeroTime(project.getTimezone()));
        OaDemandData roundMaxDemandData = roundContext.roundMaxDemandData;
        CompareResult compareResult = new CompareResult();
        // 这里要放在前面 不然可能会导致 下面的if通过了后面又四舍五入存入和上一条一样的值了 先4舍5入后进行和数据库内的比较
        roundMaxDemandData.setActual(
                BigDecimal.valueOf(roundMaxDemandData.getActual())
                        .setScale(2, RoundingMode.HALF_UP)
                        .doubleValue());
        // 输出实际最大值
        if (demandAlarmMap.get(group.getId()) != null
                && roundMaxDemandData.getActual() <= demandAlarmMap.get(group.getId())) {
            // 这里意思是如果 这个没有 数据库里的大 那就直接不需要下发 也不需要记录日志
            return compareResult.setExceeded(false);
        }
        Double demandAlarmThreshold = group.getDemandAlarmThreshold();
        double optmizedDemand =
                roundMaxDemandData.getActual() * (group.getDemandControlAutoRate() / 100);

        if (roundMaxDemandData.getActual() > controlPower * (demandAlarmThreshold / 100)) {
            log.info(
                    "demand exceeded by actualDemand > gridControlPower * (demandAlarmThreshold / 100) =  {} > {} * ({} / 100) ",
                    roundMaxDemandData.getActual(),
                    controlPower,
                    demandAlarmThreshold);
            Double demandControlAutoUpLimit = group.getDemandControlAutoUpLimit();
            boolean upLimit = false;
            if (demandControlAutoUpLimit != null && demandControlAutoUpLimit != 0) {
                if (optmizedDemand > demandControlAutoUpLimit) {
                    // 最大需量*自动调整比例(%) 大于 需量调整上限(kW) ，当前需量控制功率(kW) 自动填入 需量调整上限(kW) 并自动保存
                    log.info(
                            "groupId: {} roundMaxDemand over autoUpLimit => "
                                    + "roundMaxDemand:{} * (group.getDemandControlAutoRate():{} / 100)  > demandControlAutoUpLimit:{}",
                            group.getId(),
                            roundMaxDemandData.getActual(),
                            group.getDemandControlAutoRate(),
                            demandControlAutoUpLimit);
                    optmizedDemand = demandControlAutoUpLimit;
                    upLimit = true;
                }
            }
            log.info(
                    "groupId: {} roundMaxDemand over controlPower => "
                            + "roundMaxDemand:{} > controlPower:{} , "
                            + "optmizedDemand:{} by roundMaxDemand:{} * (group.getDemandControlAutoRate():{} / 100) ",
                    group.getId(),
                    roundMaxDemandData.getActual(),
                    controlPower,
                    optmizedDemand,
                    roundMaxDemandData.getActual(),
                    group.getDemandControlAutoRate());
            optmizedDemand =
                    new BigDecimal(optmizedDemand).setScale(2, RoundingMode.HALF_UP).doubleValue();

            return compareResult
                    .setExceeded(true)
                    .setOptimizedExceededDemand(optmizedDemand)
                    .setOverUpLimitFlag(upLimit)
                    .setActualExceededDemand(roundMaxDemandData.getActual())
                    .setTime(roundMaxDemandData.getTime());
        }
        return compareResult.setExceeded(false);
    }

    /**
     * 需量自适应执行
     *
     * @param holder :
     */
    @Override
    public void adjustExecute(HandlerAdjustModelHolder holder) {
        GroupEntity group = holder.group();
        ProjectEntity project = holder.project();
        StrategyEntity strategyEntity = strategyService.getOuterStrategyByGroupId(group.getId());
        DemandMonthContext demandMonthContext = monthPointCheck(project, group);
        // 1.4.4 要支持 分时需量
        Double controlPower;
        RoundContext roundContext = new RoundContext().setProject(project).setGroup(group);
        if (Boolean.TRUE.equals(group.getTimeSharingDemandController())) {
            // 开启了分时控需 把当前时间的 控需的段找到
            TimeSharingDemandEntity timeSharingDemandEntity =
                    timeSharingDemandService.findNowRunControlItem(
                            project.getId(), group.getId(), strategyEntity.getId());
            if (timeSharingDemandEntity != null) {
                controlPower = timeSharingDemandEntity.getDemandControlPower();
                roundContext.setTimeSharingFlag(true);
                roundContext.setTimeSharingDemandEntity(timeSharingDemandEntity);
            } else {
                // 没有找到符合的 时段的
                controlPower = null;
            }
        } else {
            if (demandMonthContext.isMonthPointFlag()) {
                controlPower = strategyEntity.getMonthControlPower();
            } else {
                // 控制需量值
                controlPower = strategyEntity.getGridControlPower();
            }
        }
        // 如果没有设置  直接返回
        if (controlPower == null) {
            log.warn(
                    "项目Id: {} 项目名称: {} 分组:Id: {} 需量控制没有设置 无法进行需量计算",
                    project.getId(),
                    project.getProjectName(),
                    group.getId());
            return;
        }
        roundContext.setControlPower(controlPower);
        // 获取平台的计算的 最大需量 或者 电表的最大需量
        OaDemandData roundMaxDemandData = roundMaxDemand(roundContext);
        // 获取平台的计算的 需量数据 + 保存 influxdb
        if (roundMaxDemandData == null) {
            log.warn(
                    "项目Id:{} 项目名称:{} 分组Id:{} 平台计算数据为空 无法进行需量计算",
                    project.getId(),
                    project.getProjectName(),
                    group.getId());
            return;
        }
        roundContext.setRoundMaxDemandData(roundMaxDemandData);
        // core 调用子类的 compare 比较方法 不同的方式 比较方法不同, 但是都需要先得到计算的然后保存
        CompareResult compareResult = compareExceeded(roundContext);
        boolean exceeded = compareResult.isExceeded();
        if (exceeded) {

            Set<String> emailList = demandEmailService.getNoticeEmail(project.getId());
            // 需量提醒告警的信息 使用的是 需量实际的值 不是优化过的值
            NoticeEmailInfo noticeEmailInfo =
                    new NoticeEmailInfo()
                            .setTime(compareResult.getTime())
                            .setActualDemand(compareResult.getActualExceededDemand())
                            .setControlPower(controlPower)
                            .setEmailList(emailList);
            // 新增需求 需要根据是否开了 提醒 才去发送邮件
            if (group.getDemandRemindController() != null && group.getDemandRemindController()) {
                // 要根据是否开启了 需量提醒开关 来发送邮件
                sendNoticeMessage(group, noticeEmailInfo, project);
            }
            // 入库 日志 1.4.4 改动 说 日志一定要记 和 上面的提醒开关无关
            saveDemandExceedLog(
                    group,
                    compareResult.getTime(),
                    compareResult.getActualExceededDemand(),
                    controlPower);
            // 需量自动 抬升逻辑
            if (DemandControlAdjustModelEnum.auto(group.getDemandControlAdjustModel())) {
                autoUpDemand(group, project, compareResult, noticeEmailInfo);
            }
        }
    }

    /**
     * 自动抬升逻辑
     *
     * @param group : groupEntity
     * @param project : projectEntity
     * @param compareResult :compareResult
     * @param noticeEmailInfo :noticeEmailInfo
     */
    public void autoUpDemand(
            GroupEntity group,
            ProjectEntity project,
            CompareResult compareResult,
            NoticeEmailInfo noticeEmailInfo) {
        double upDemand = compareResult.optimizedExceededDemand;
        double actualDemand = compareResult.actualExceededDemand;
        if (DemandControlAdjustModelEnum.auto(group.getDemandControlAdjustModel())) {
            // 这里需要 写入最大需量 并且下发
            threadPoolTaskExecutor.submit(
                    () -> {
                        try {
                            // 使用自定义的 RetryFlow 控制流程 实现 retry 机制
                            new RetryFlow()
                                    .retryTemplate(retryTemplate)
                                    .retryExecutor(demandAutoUpRetryTaskExecutor)
                                    .prepareContext(
                                            () ->
                                                    new IRetryTaskExecutor.ContextHolder()
                                                            .setProjectId(project.getId())
                                                            .put(
                                                                    EmsConstants.GROUP_ID,
                                                                    group.getId())
                                                            .put(
                                                                    EmsConstants.EXCEEDED_DEMAND,
                                                                    upDemand)
                                                            .put(
                                                                    EmsConstants.ACTUAL_DEMAND,
                                                                    actualDemand)
                                                            .put(
                                                                    EmsConstants.OVER_UP_LIMIT_FLAG,
                                                                    compareResult.overUpLimitFlag))
                                    .execute();
                        } catch (Exception e) {
                            // 只要下发失败了 先去发送告警邮件  2024-10-09 16:20 pangu内卷群里袁旭和周涛说 必发  和 告警开关无关
                            sendUpErrorMessage(group, noticeEmailInfo, project, upDemand);
                            throw new RuntimeException(e);
                        }
                    });
        }
    }

    /**
     * 计算平台的 需量数据 和 入库
     *
     * @return : Map<Long, OaDemandData>
     */
    protected @Nullable Map<Long, OaDemandData> calcDemandMapAndSaveDb(RoundContext roundContext) {
        ProjectEntity project = roundContext.project;
        GroupEntity group = roundContext.group;
        Double controlPower = roundContext.controlPower;
        Long end = Instant.now().getEpochSecond();
        // 1.3.9 现在和以前的不一样 不是固定的几个 calModel

        // manual 和 auto calc 都是按照以前的逻辑 计算的到的
        Map<Long, OaDemandData> demandMap =
                newDemandServiceKt.platformDemandCal(
                        new RangeRequest(),
                        demandCalContext -> {
                            demandCalContext.setBucket(influxClientService.getBucketRealtime());
                            demandCalContext.setProject(project);
                            demandCalContext.setGroup(group);
                            return null;
                        });

        if (CollectionUtils.isEmpty(demandMap)) {
            return null;
        }
        DemandMonthContext demandMonthContext = monthPointCheck(project, group);

        // 我的想法是 判断一下 如果当前的时间 在月初的  4个周期内 就是 00:00 -> 00:15*3 内 就直接过滤掉 上个月月末的数据, 因为
        // pushForwardStartTime方法会一直往前推时间了
        if (demandMonthContext.isMonthPointFlag()) {
            // 1.4.1 直接把月末的数据不要了 , 防止月初触发的任务计算了月末 然后和月初需量控制 比较 导致超过然后提醒的问题
            demandMap =
                    demandMap.entrySet().stream()
                            .filter(e -> e.getKey() >= demandMonthContext.getCurrentMonthPoint())
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            // 如果是月初时间 则直接把 原始需量和 实际需量 设置为0.0
            demandMap.forEach(
                    (key, oaDemandData) -> {
                        // 为了不影响上个月的数据
                        if (key >= demandMonthContext.getCurrentMonthPoint()) {
                            oaDemandData.setActual(0.0);
                            oaDemandData.setOrigin(0.0);
                            log.error(
                                    "time:{} >= currentPeriodStart:{} 月初数据写0{}",
                                    key,
                                    demandMonthContext.getCurrentMonthPoint(),
                                    oaDemandData);
                        } else {
                            log.error(
                                    "time:{} < currentPeriodStart:{} 保持上个月末原因数据{}",
                                    key,
                                    demandMonthContext.getCurrentMonthPoint(),
                                    oaDemandData);
                        }
                    });
        }
        // 保存需量数据
        Map<Long, OaDemandData> finalDemandMap = demandMap;
        threadPoolTaskExecutor.submit(
                () -> {
                    // 保存需量数据
                    demandService.saveOriginAndActualDemandToInfluxDb(
                            controlPower, finalDemandMap, group);
                    // 1.3.9 因为以前有覆盖 controlPower问题所以单独把controlPower 放在最后
                    // 上面的saveOriginAndActualDemandToDb方法里面不去写controlPower了

                    demandService.saveControlPowerDemandToInfluxDb(
                            controlPower,
                            group,
                            end,
                            roundContext.getTimeSharingFlag()
                                    ? roundContext.getTimeSharingDemandEntity()
                                    : null);
                });
        return demandMap;
    }

    protected void saveDemandExceedLog(
            GroupEntity groupEntity, Long time, double actualDemand, Double controlPower) {
        DemandLogEntity demandLog = new DemandLogEntity();
        Double demandAlarmThreshold = groupEntity.getDemandAlarmThreshold();
        String logMessage =
                String.format(
                        "%s实际需量(%skW)超出当前控制需量(%skW),阈值为%s%%",
                        groupEntity.getName(),
                        String.format("%.2f", actualDemand),
                        String.format("%.2f", controlPower),
                        String.format("%.2f", demandAlarmThreshold));
        String logMessageEn =
                String.format(
                        "actual demand(%skW) of %s exceeds current control demand (%skW), the"
                                + " threshold is %s%%",
                        String.format("%.2f", actualDemand),
                        groupEntity.getName(),
                        String.format("%.2f", controlPower),
                        String.format("%.2f", demandAlarmThreshold));
        demandLog.setTime(time);
        demandLog.setType(1);
        demandLog.setGroupId(groupEntity.getId());
        demandLog.setProjectId(groupEntity.getProjectId());
        demandLog.setActualDemand(actualDemand);
        demandLog.setDescription(logMessage);
        demandLog.setDescriptionEn(logMessageEn);
        demandLogService.save(demandLog);
    }

    @Data
    @Accessors(chain = true)
    public static class NoticeEmailInfo {
        Long time;
        double actualDemand;
        double controlPower;
        Set<String> emailList;
    }

    protected void sendUpErrorMessage(
            GroupEntity groupEntity,
            NoticeEmailInfo noticeEmailInfo,
            ProjectEntity projectEntity,
            Double upDemand) {
        Double demandAlarmThreshold = groupEntity.getDemandAlarmThreshold();
        String subject;
        String message;
        if (projectEntity.getCountry() == 226) {
            subject = String.format("%s控制需量自动调整下发失败", projectEntity.getProjectName());
            message =
                    String.format(
                            "%s %s 在 %s 实际需量(%skW)超出当前控制需量(%skW),阈值为%s%%, 当前控制需量自动调整为%skW 下发失败, 请注意查看",
                            projectEntity.getProjectName(),
                            groupEntity.getName(),
                            LocalDateTime.ofEpochSecond(
                                    noticeEmailInfo.getTime(),
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())),
                            String.format("%.2f", noticeEmailInfo.getActualDemand()),
                            String.format("%.2f", noticeEmailInfo.getControlPower()),
                            String.format("%.2f", demandAlarmThreshold),
                            String.format("%.2f", upDemand));
        } else {
            subject =
                    String.format(
                            "actual demand for %s has exceeded", projectEntity.getProjectName());
            message =
                    String.format(
                            "Please note: at %s the actual demand(%s kW) of %s within %s exceeds"
                                    + " its current controlled demand (%s kW) and the threshold is %s. current controlled demand auto up to %s Failed to deliver",
                            LocalDateTime.ofEpochSecond(
                                    noticeEmailInfo.getTime(),
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())),
                            String.format("%.2f", noticeEmailInfo.getActualDemand()),
                            groupEntity.getName(),
                            projectEntity.getProjectName(),
                            String.format("%.2f", noticeEmailInfo.getControlPower()),
                            String.format("%.2f", demandAlarmThreshold),
                            String.format("%.2f", upDemand));
        }
        emailService.sendMessage(
                new ArrayList<>(noticeEmailInfo.getEmailList()),
                subject,
                message,
                () ->
                        // type = 1 需量超了 @see EmailLogTypeEnum
                        new EmailService.ServiceInfo()
                                .setProjectId(projectEntity.getId())
                                .setGroupId(groupEntity.getId())
                                .setEmailLogTypeEnum(EmailLogTypeEnum.STRATEGY_DELIVER_ERROR));
    }

    protected void sendNoticeMessage(
            GroupEntity groupEntity, NoticeEmailInfo noticeEmailInfo, ProjectEntity projectEntity) {
        Double demandAlarmThreshold = groupEntity.getDemandAlarmThreshold();
        String subject;
        String message;
        if (projectEntity.getCountry() == 226) {
            subject = String.format("%s实际需量超出控制需量", projectEntity.getProjectName());
            message =
                    String.format(
                            "%s %s 在 %s 实际需量(%skW)超出当前控制需量(%skW),阈值为%s%%, 请注意查看",
                            projectEntity.getProjectName(),
                            groupEntity.getName(),
                            LocalDateTime.ofEpochSecond(
                                    noticeEmailInfo.getTime(),
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())),
                            String.format("%.2f", noticeEmailInfo.getActualDemand()),
                            String.format("%.2f", noticeEmailInfo.getControlPower()),
                            String.format("%.2f", demandAlarmThreshold));
        } else {
            subject =
                    String.format(
                            "actual demand for %s has exceeded", projectEntity.getProjectName());
            message =
                    String.format(
                            "Please note: at %s the actual demand(%s kW) of %s within %s exceeds"
                                    + " its current controlled demand (%s kW) and the threshold is %s.",
                            LocalDateTime.ofEpochSecond(
                                    noticeEmailInfo.getTime(),
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())),
                            String.format("%.2f", noticeEmailInfo.getActualDemand()),
                            groupEntity.getName(),
                            projectEntity.getProjectName(),
                            String.format("%.2f", noticeEmailInfo.getControlPower()),
                            String.format("%.2f", demandAlarmThreshold));
        }
        // 发送邮件
        emailService.sendMessage(
                new ArrayList<>(noticeEmailInfo.getEmailList()),
                subject,
                message,
                () ->
                        // type = 1 需量超了 @see EmailLogTypeEnum
                        new EmailService.ServiceInfo()
                                .setProjectId(projectEntity.getId())
                                .setGroupId(groupEntity.getId())
                                .setEmailLogTypeEnum(EmailLogTypeEnum.COUNTRY_OVER));
    }

    protected DemandMonthContext monthPointHourCheckForTest(
            ProjectEntity project, GroupEntity group) {
        return null;
        // 获取当前小时的零点时间
        //        long currentHourZeroTime =
        // Instant.now().truncatedTo(ChronoUnit.HOURS).getEpochSecond();
        //
        //        // 根据需求模型设置倍数
        //        int multiple;
        //        if (group.getDemandControlAdjustModel() != null
        //                && group.getDemandControlAdjustModel()
        //                        .equals(DemandControlAdjustModelEnum.auto_meter.name())) {
        //            // 电表按照2倍
        //            multiple = 1;
        //        } else {
        //            // 手动和 auto_calc 就算 3 倍
        //            multiple = 1;
        //        }
        //
        //        // 计算 demandPeriod
        //        int demandPeriod = group.getDemandPeriod() * 60 * multiple;
        //        long time = currentHourZeroTime + demandPeriod + (60 * 3);
        //
        //        // 获取当前时间戳
        //        long epochSecond = Instant.now().getEpochSecond() + 10;
        //
        //        // 判断当前时间是否在区间内
        //        boolean flag = epochSecond > currentHourZeroTime && epochSecond < time;
        //        return new DemandMonthContext()
        //                .setMonthPointFlag(flag)
        //                .setCurrentMonthPoint(currentHourZeroTime)
        //                .setIgnorePeriod(multiple);
    }

    protected DemandMonthContext monthPointCheck(ProjectEntity project, GroupEntity group) {
        // 这里是for 测试 把每个小时当做月初
        //   return monthPointHourCheckForTest(project, group);
        long currentMonthZeroTime = MyTimeUtil.getCurrentMonthZeroTime(project.getTimezone());
        // 根据需求模型设置倍数 , 以前是不同的计算方式不同的比例 现在统一了都是1
        int multiple = 1;
        // 计算 demandPeriod
        int demandPeriod = group.getDemandPeriod() * 60 * multiple;
        // + 3分钟保险一点
        long time = currentMonthZeroTime + demandPeriod + (60 * 3);
        // 获取当前时间戳
        long epochSecond = Instant.now().getEpochSecond();
        // 判断当前时间是否在区间内
        boolean flag = epochSecond > currentMonthZeroTime && epochSecond < time;
        return new DemandMonthContext()
                .setMonthPointFlag(flag)
                .setCurrentMonthPoint(currentMonthZeroTime)
                .setIgnorePeriod(multiple);
    }

    /**
     * 判断是否在月初时间 : 月初的前几个周期内 可能会直接不执行job 防止需量抬升了
     *
     * @param project : project
     * @param group : group
     * @return : flag
     */
    protected boolean monthPoint(ProjectEntity project, GroupEntity group) {
        long currentMonthZeroTime = MyTimeUtil.getCurrentMonthZeroTime(project.getTimezone());
        // 得到月初时间如果月初时间是在3个周期内
        int multiple;
        if (group.getDemandControlAdjustModel() != null
                && group.getDemandControlAdjustModel()
                        .equals(DemandControlAdjustModelEnum.auto_meter.name())) {
            // 电表按照2倍
            multiple = 2;
        } else {
            // 手动和auto_calc 就算3倍
            multiple = 3;
        }
        int demandPeriod = group.getDemandPeriod() * 60 * multiple;
        long time = currentMonthZeroTime + demandPeriod;
        long epochSecond = Instant.now().getEpochSecond();
        return epochSecond > currentMonthZeroTime && epochSecond < time;
    }

    @Data
    @Accessors(chain = true)
    protected static class RoundContext {
        GroupEntity group;
        ProjectEntity project;
        Double controlPower;
        OaDemandData roundMaxDemandData;

        // 1.4.4
        Boolean timeSharingFlag = false;
        TimeSharingDemandEntity timeSharingDemandEntity;
    }

    @Data
    @Accessors(chain = true)
    public static class CompareResult {
        boolean isExceeded;
        Double optimizedExceededDemand;
        Double actualExceededDemand;
        Long time;
        boolean overUpLimitFlag;
    }
}
