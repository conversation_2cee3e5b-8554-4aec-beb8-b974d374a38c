package com.wifochina.modules.demand.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024-01-12
 */
@Getter
@Setter
@TableName("t_demand_log")
@ApiModel(value = "DemandLogEntity对象")
public class DemandLogEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("分组id")
    private String groupId;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("1需量超了 2设置控制需量 3设置月初需量 4月初需量恢复 5自动调整需量")
    private Integer type;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("英文描述")
    private String descriptionEn;

    @ApiModelProperty("发生时间")
    private Long time;

    @ApiModelProperty("实际需量")
    private Double actualDemand;
}
