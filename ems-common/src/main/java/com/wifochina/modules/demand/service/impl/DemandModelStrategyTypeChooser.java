package com.wifochina.modules.demand.service.impl;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.demand.service.DemandModelStrategy;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * Created on 2024/9/24 11:27.
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DemandModelStrategyTypeChooser {
    @Resource private List<DemandModelStrategy> strategies;

    public DemandModelStrategy chooseStrategy(Integer calMode) {
        if (calMode != null) {
            DemandModelStrategy demandModelStrategy =
                    strategies.stream()
                            .filter((strategy) -> strategy.type().equals(calMode))
                            .findFirst()
                            .orElse(null);
            if (demandModelStrategy != null) {
                return demandModelStrategy;
            } else {
                throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
            }
        } else {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }
}
