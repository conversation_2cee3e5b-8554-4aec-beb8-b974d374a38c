package com.wifochina.modules.demand.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-02-05 11:19 AM
 */
@Data
public class GroupDemandAlarmVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分组id")
    private String groupId;

    @ApiModelProperty("分组名称")
    private String name;

    @ApiModelProperty("发生时间")
    private Long time;

    @ApiModelProperty("实际需量")
    private Double actualDemand;

    @ApiModelProperty("预设需量")
    private Double presetDemand;

    @ApiModelProperty(value = "需量告警阈值(超出多少就告警)")
    private Double demandAlarmThreshold;
}
