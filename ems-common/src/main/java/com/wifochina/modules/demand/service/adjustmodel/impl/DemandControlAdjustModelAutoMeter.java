package com.wifochina.modules.demand.service.adjustmodel.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.DemandControlAdjustModelEnum;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.data.entity.MeterContentData;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.demand.service.NewDemandServiceKt;
import com.wifochina.modules.demand.service.adjustmodel.CommonDemandControlAdjustModelService;
import com.wifochina.modules.demand.service.adjustmodel.DemandMonthContext;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.event.VO.MeterContentDataVo;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.monitor.service.MonitorService;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Created on 2024/9/24 16:32. auto meter 自动(电表)
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class DemandControlAdjustModelAutoMeter extends CommonDemandControlAdjustModelService {
    private final AmmeterService ammeterService;
    private final MonitorService monitorService;
    private final DataService dataService;
    private final DemandService demandService;
    private InfluxClientService influxClientService;

    /**
     * 前推 时间 手动和自动计算方式都是 按照2倍去推 就是把 15 / 30 这个 * 2 返回
     *
     * @param startTime : startTime
     * @param groupEntity : groupEntity
     * @return : push forward time
     */
    private Long pushForwardStartTime(Long startTime, GroupEntity groupEntity) {
        Integer demandPeriod = groupEntity.getDemandPeriod();
        if (demandPeriod != null) {
            return startTime - TimeUnit.MINUTES.toSeconds(demandPeriod * 3L + 1) - startTime % 60;
        } else {
            // 异常
            throw new ServiceException(
                    "demandCalJob getStartTimeNew method  cant find demandPeriod for groupId : {}",
                    groupEntity.getId());
        }
    }

    @Override
    protected OaDemandData roundMaxDemand(RoundContext roundContext) {
        Map<Long, OaDemandData> calcDemandMap = calcDemandMapAndSaveDb(roundContext);
        log.info("电表需量也 记录一下 平台计算的数据... success:{} ", calcDemandMap);
        GroupEntity group = roundContext.getGroup();
        ProjectEntity project = roundContext.getProject();
        // 电表的比较就是 把当前的页面上的 控制需量拿到 和 电表读取的max的 比较
        List<AmmeterEntity> ammeterEntities =
                ammeterService.findDemandMetersByGroupId(group.getId());
        if (CollectionUtil.isEmpty(ammeterEntities)) {
            // 没有找到这个分组下的 开了最大需量计量的 关口电表
            return null;
        }
        DemandMonthContext demandMonthContext = monthPointCheck(project, group);
        //        boolean monthPointFlag = monthPoint(project, group);
        Double maxMeterDemand = 0.0;
        // 如果不是月初时间 则去处理一下 如果是月初则直接按照0.0 来
        if (!demandMonthContext.isMonthPointFlag()) {
            // 读取 所有的 周涛说 要把所有的电表的 最大需量+起来 用这个和去比较
            // 这里和孙勇确认了 直接查询 断网就断网 需要走influxdb 数据库查询 还是实时读取 如果实时读取查询不到呢 或者 断网了
            // 断网就肯定算不出来 就直接实时查询即可
            Map<String, MeterContentData> meterContentDataMap =
                    dataService.collectMeterDataPoint(project.getId());
            // 直接把电表的最大需量的数据取出来 实时的 因为这个值也是 电表自带的功能 滑差15分钟算更新到这个点位的
            for (AmmeterEntity ammeterEntity : ammeterEntities) {
                MeterContentDataVo meterContentDataVo =
                        monitorService.realTimeMeter(ammeterEntity, meterContentDataMap);
                if (meterContentDataVo == null) {
                    continue;
                }
                // 获取到电表里的 最大需量的数据
                Double maxNegativeActivePowerDemand =
                        meterContentDataVo.getMax_negative_active_power_demand();
                maxMeterDemand += maxNegativeActivePowerDemand;
            }
        }
        return new OaDemandData()
                .setActual(maxMeterDemand)
                .setOrigin(maxMeterDemand)
                .setTime(Instant.now().getEpochSecond());
    }

    @Override
    public Set<String> type() {
        return Set.of(DemandControlAdjustModelEnum.auto_meter.getName());
    }
}
