package com.wifochina.modules.demand.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import com.wifochina.modules.demand.service.DemandAlarmService;
import com.wifochina.modules.demand.service.DemandLogService;
import com.wifochina.modules.demand.vo.GroupDemandAlarmVo;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-02-05 11:32 AM
 */
@Component
public class DemandAlarmServiceImpl implements DemandAlarmService {
    @Resource private GroupService groupService;

    @Resource private StrategyService strategyService;

    @Resource private DemandLogService demandLogService;

    @Resource private ProjectService projectService;

    @Override
    public List<GroupDemandAlarmVo> getDemandAlarm(String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        if (projectEntity == null) {
            return new ArrayList<>();
        }
        Long time = MyTimeUtil.getCurrentMonthZeroTime(projectEntity.getTimezone());
        List<GroupEntity> groupEntities =
                groupService.queryEnableDemandControl(projectEntity.getId());
        List<StrategyEntity> strategyEntities =
                strategyService
                        .lambdaQuery()
                        .eq(StrategyEntity::getProjectId, projectId)
                        .eq(StrategyEntity::getWeekDay, 0)
                        .isNotNull(StrategyEntity::getGridControlPower)
                        .list();
        Map<String, Double> strategyControlPowerMap =
                strategyEntities.stream()
                        .collect(
                                Collectors.toMap(
                                        StrategyEntity::getGroupId,
                                        StrategyEntity::getGridControlPower));
        Map<String, Double> demandLogMap = getDemandMax(projectId, time);
        List<GroupDemandAlarmVo> list = new ArrayList<>(groupEntities.size());
        groupEntities.forEach(
                e -> {
                    if (e.getDemandRemindController() != null && e.getDemandRemindController()) {
                        String groupId = e.getId();
                        GroupDemandAlarmVo groupDemandAlarmVo = new GroupDemandAlarmVo();
                        groupDemandAlarmVo.setGroupId(groupId);
                        groupDemandAlarmVo.setPresetDemand(strategyControlPowerMap.get(groupId));
                        groupDemandAlarmVo.setActualDemand(demandLogMap.get(groupId));
                        groupDemandAlarmVo.setName(e.getName());
                        groupDemandAlarmVo.setDemandAlarmThreshold(e.getDemandAlarmThreshold());
                        if (groupDemandAlarmVo.getActualDemand() == null
                                || groupDemandAlarmVo.getPresetDemand() == null
                                || e.getDemandAlarmThreshold() == null) {
                            return;
                        }
                        if (groupDemandAlarmVo.getActualDemand()
                                > groupDemandAlarmVo.getPresetDemand()
                                        * (e.getDemandAlarmThreshold() / 100)) {
                            list.add(groupDemandAlarmVo);
                        }
                    }
                });
        return list;
    }

    @Override
    public Map<String, Double> getDemandMax(String projectId, Long time) {
        QueryWrapper<DemandLogEntity> qw = new QueryWrapper<>();
        qw.select("max(actual_demand) as actual_demand, group_id ")
                .eq("project_id", projectId)
                .ge("time", time)
                .groupBy("group_id");
        List<DemandLogEntity> demandLogEntities = demandLogService.list(qw);
        return demandLogEntities.stream()
                .collect(
                        Collectors.toMap(
                                DemandLogEntity::getGroupId, DemandLogEntity::getActualDemand));
    }
}
