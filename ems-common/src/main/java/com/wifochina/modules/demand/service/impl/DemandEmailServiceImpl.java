package com.wifochina.modules.demand.service.impl;

import cn.hutool.extra.spring.SpringUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.config.EmailSuperNoticeConfig;
import com.wifochina.modules.demand.entity.DemandEmailEntity;
import com.wifochina.modules.demand.mapper.DemandEmailMapper;
import com.wifochina.modules.demand.service.DemandEmailService;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Service
public class DemandEmailServiceImpl extends ServiceImpl<DemandEmailMapper, DemandEmailEntity>
        implements DemandEmailService {

    @Override
    public Set<String> getNoticeEmail(String projectId) {
        EmailSuperNoticeConfig emailSuperNotice = SpringUtil.getBean(EmailSuperNoticeConfig.class);
        Set<String> emailList =
                this.baseMapper
                        .selectList(
                                new LambdaQueryWrapper<DemandEmailEntity>()
                                        .eq(DemandEmailEntity::getProjectId, projectId))
                        .stream()
                        .map(DemandEmailEntity::getEmail)
                        .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(emailSuperNotice.getDemandExceed())
                && !emailSuperNotice.getDemandExceed().get(0).startsWith("${EMS_EMAIL_NOTICE")) {
            emailList.addAll(emailSuperNotice.getDemandExceed());
        }
        return emailList;
    }
}
