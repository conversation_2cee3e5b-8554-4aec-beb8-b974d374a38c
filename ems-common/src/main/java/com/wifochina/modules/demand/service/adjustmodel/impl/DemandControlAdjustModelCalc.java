package com.wifochina.modules.demand.service.adjustmodel.impl;

import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.DemandControlAdjustModelEnum;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.demand.service.NewDemandServiceKt;
import com.wifochina.modules.demand.service.adjustmodel.CommonDemandControlAdjustModelService;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.group.entity.GroupEntity;

import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

/**
 * Created on 2024/9/24 16:32. <br>
 * 计算模式 支持 manual 和 auto_calc 都需要执行这个
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DemandControlAdjustModelCalc extends CommonDemandControlAdjustModelService {

    @Resource private DemandService demandService;
    @Resource private InfluxClientService influxClientService;

    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource private NewDemandServiceKt newDemandServiceKt;

    /**
     * 前推 时间 手动和自动计算方式都是 按照2倍去推 就是把 15 / 30 这个 * 2 返回
     *
     * @param startTime : startTime
     * @param groupEntity : groupEntity
     * @return : push forward time
     */
    private Long pushForwardStartTime(Long startTime, GroupEntity groupEntity) {
        Integer demandPeriod = groupEntity.getDemandPeriod();
        if (demandPeriod != null) {
            return startTime - TimeUnit.MINUTES.toSeconds(demandPeriod * 2 + 1) - startTime % 60;
        } else {
            // 异常
            throw new ServiceException(
                    "demandCalJob getStartTimeNew method  cant find demandPeriod for groupId : {}",
                    groupEntity.getId());
        }
    }

    /**
     * 平台计算的 这一轮最大需量
     *
     * @param roundContext : context
     * @return : OaDemandData
     */
    @Override
    protected OaDemandData roundMaxDemand(RoundContext roundContext) {
        // 获取平台的计算的 需量数据 + 保存 influxdb
        Map<Long, OaDemandData> calcDemandMap = calcDemandMapAndSaveDb(roundContext);
        if (calcDemandMap != null && !calcDemandMap.isEmpty()) {
            // 这里逻辑稍微改了一下 只取这一个周期里最大的那个需量
            Map.Entry<Long, OaDemandData> maxOaDemandEntry =
                    calcDemandMap.entrySet().stream()
                            .max(Comparator.comparing(entry -> entry.getValue().getActual()))
                            .orElseThrow();
            Long time = maxOaDemandEntry.getKey();
            OaDemandData maxOaDemandData = maxOaDemandEntry.getValue();
            maxOaDemandData.setTime(time);
            return maxOaDemandData;
        }
        return null;
    }

    @Override
    public Set<String> type() {
        // 手动和auto_calc 都执行这个
        return Set.of(
                DemandControlAdjustModelEnum.manual.getName(),
                DemandControlAdjustModelEnum.auto_calc.getName());
    }
}
