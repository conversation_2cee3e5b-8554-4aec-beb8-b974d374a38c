package com.wifochina.modules.demand.service;

import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.group.entity.GroupEntity;

import org.springframework.data.util.Pair;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-01-16 5:19 PM
 */
public interface DemandService {

    /**
     * 获取原需量和实际需量
     *
     * @param groupEntity 分组
     * @return Pair<Double,Double>
     */
    Map<Long, OaDemandData> getOriginAndActualDemand(
            String bucket, Long start, Long end, String timezone, GroupEntity groupEntity);

    void saveOriginAndActualDemandToInfluxDb(
            Double controlPower, Map<Long, OaDemandData> demandMap, GroupEntity groupEntity);

    void saveControlPowerDemandToInfluxDb(
            Double controlPower, GroupEntity groupEntity, Long endTime);

    void saveCapacityDemandToDb(Double controlPower, String projectId, String groupId);

    /** 需量计算模式:1固定时段15分钟;2固定时段30分钟;3一分钟滑窗" */
    default Long getStartTime(Long start, Integer calModel) {
        Long time = null;
        switch (calModel) {
            case 1:
            case 3:
            case 4:
                time = start - TimeUnit.MINUTES.toSeconds(61) - start % 60;
                break;
            case 2:
                time = start - TimeUnit.MINUTES.toSeconds(121) - start % 60;
                break;
            default:
        }
        return time;
    }

    default Map<Long, Pair<Double, Double>> getFixedDemandMap(
            Map<String, Map<Long, Double>> meterDataMap,
            Map<String, Map<Long, Double>> deviceDataMap,
            List<String> deviceList,
            List<String> meterList) {
        Map<Long, Pair<Double, Double>> resultMap = new HashMap<>(16);
        // 首先获取所有时间点，并确保它们是有序且不重复的
        List<Long> commonTimeList =
                meterDataMap.values().stream()
                        .flatMap(map -> map.keySet().stream())
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
        for (long time : commonTimeList) {
            // 丢弃最后一条数据，因为不一定满15分钟
            if (time == commonTimeList.get(commonTimeList.size() - 1)) {
                break;
            }
            double deviceDiffValue = 0d;
            double meterDiffValue = 0d;
            boolean dataValid = true;
            for (String deviceId : deviceList) {
                Map<Long, Double> deviceData = deviceDataMap.get(deviceId);
                if (deviceData == null || deviceData.get(time) == null) {
                    // 如果设备数据缺失或指定时间的数据不存在，则跳过此次循环
                    dataValid = false;
                    break;
                }
                Double originDeviceValue = deviceData.get(time);
                deviceDiffValue += originDeviceValue;
            }
            for (String meterId : meterList) {
                Map<Long, Double> meterData = meterDataMap.get(meterId);
                if (meterData == null || meterData.get(time) == null) {
                    // 如果设备数据缺失或指定时间的数据不存在，则跳过此次循环
                    dataValid = false;
                    break;
                }
                Double originMeterValue = meterData.get(time);
                meterDiffValue += originMeterValue;
            }
            if (dataValid) {
                double actualDemand = -meterDiffValue / 1000;
                double originDemand = actualDemand + deviceDiffValue;
                resultMap.put(time, Pair.of(originDemand, actualDemand));
            }
        }
        return resultMap;
    }

    default Map<Long, Pair<Double, Double>> getSlidingDemandMap(
            Map<String, Map<Long, Double>> meterDataMap,
            Map<String, Map<Long, Double>> deviceDataMap,
            List<String> deviceList,
            List<String> meterList,
            Long interval) {
        Map<Long, Pair<Double, Double>> resultMap = new HashMap<>(16);
        // 首先获取所有时间点，并确保它们是有序且不重复的
        List<Long> commonTimeList =
                meterDataMap.values().stream()
                        .flatMap(map -> map.keySet().stream())
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
        for (int i = 0; i < commonTimeList.size(); i++) {
            if (i + interval == commonTimeList.size()) {
                break;
            }
            boolean dataValid = true;
            double deviceDiffValue = 0d;
            double meterDiffValue = 0d;

            int count = 0;
            for (int j = 0; j < interval && dataValid; j++) {
                int point = i + j;
                long time = commonTimeList.get(point);
                for (String deviceId : deviceList) {
                    Map<Long, Double> deviceData = deviceDataMap.get(deviceId);
                    if (deviceData == null || deviceData.get(time) == null) {
                        // 如果设备数据缺失或指定时间的数据不存在，则跳过此次循环
                        dataValid = false;
                        break;
                    }
                    Double originDeviceValue = deviceData.get(time);
                    deviceDiffValue += originDeviceValue;
                }
                for (String meterId : meterList) {
                    Map<Long, Double> meterData = meterDataMap.get(meterId);
                    if (meterData == null || meterData.get(time) == null) {
                        // 如果设备数据缺失或指定时间的数据不存在，则跳过此次循环
                        dataValid = false;
                        break;
                    }
                    Double originMeterValue = meterData.get(time);
                    meterDiffValue += originMeterValue;
                }
                count++;
            }
            if (dataValid && count != 0) {
                double actualDemand = -meterDiffValue / 1000;
                double originDemand = actualDemand + deviceDiffValue;
                resultMap.put(
                        commonTimeList.get((int) (i + interval - 1)),
                        Pair.of(originDemand / count, actualDemand / count));
            }
        }
        return resultMap;
    }
}
