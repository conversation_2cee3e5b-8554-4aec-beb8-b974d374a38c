package com.wifochina.modules.demand.service;

import com.wifochina.modules.demand.vo.OaDemandData;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * Created on 2024/9/24 10:56.
 *
 * <AUTHOR>
 */
public interface DemandModelStrategy {

    Map<Long, OaDemandData> modelCal(OaDemandCalParams oaDemandCalParams);

    Integer type();

    //    DemandModelStrategy chooseStrategy(Integer calModel);

    @Data
    @Accessors(chain = true)
    class OaDemandCalParams {
        private Map<String, Map<Long, Double>> meterDataMap;
        private Map<String, Map<Long, Double>> deviceDataMap;
        private List<String> deviceList;
        private List<String> meterList;
        private Long interval;
    }
}
