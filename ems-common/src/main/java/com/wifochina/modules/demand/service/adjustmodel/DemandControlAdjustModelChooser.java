package com.wifochina.modules.demand.service.adjustmodel;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * Created on 2024/9/27 14:20.
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DemandControlAdjustModelChooser {
    @Resource private List<DemandControlAdjustModelService> demandControlAdjustModelServices;

    public DemandControlAdjustModelService chooseAdjustModel(String type) {
        DemandControlAdjustModelService demandControlAdjustModelService =
                demandControlAdjustModelServices.stream()
                        .filter((service) -> service.type().contains(type))
                        .findFirst()
                        .orElse(null);
        if (demandControlAdjustModelService != null) {
            return demandControlAdjustModelService;
        } else {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }
}
