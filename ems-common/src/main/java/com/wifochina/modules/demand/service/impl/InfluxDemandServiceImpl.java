package com.wifochina.modules.demand.service.impl;

import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.util.DemandCalcModeEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.demand.service.DemandModelStrategy;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.GroupAmmeterService;
import com.wifochina.modules.group.service.GroupDeviceService;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-25 1:35 PM
 */
@Component
public class InfluxDemandServiceImpl implements DemandService {

    @Resource private InfluxClientService influxClient;
    @Resource private GroupAmmeterService groupAmmeterService;

    @Resource private AmmeterService ammeterService;

    @Resource private GroupDeviceService groupDeviceService;
    @Resource private DemandModelStrategyTypeChooser demandModelStrategyTypeChooser;

    @Override
    public Map<Long, OaDemandData> getOriginAndActualDemand(
            String bucket, Long start, Long end, String timezone, GroupEntity groupEntity) {
        String projectId = groupEntity.getProjectId();
        Long interval;
        //        if
        // (DemandCalcModeEnum.SLIDING.getModel().equals(groupEntity.getDemandCalcModel())) {
        if (groupEntity.getDemandPeriod() != null) {
            // TODO 确认一下 是否DemandPeriod 应该有 不管是滑差还是固定下
            interval = Long.valueOf(groupEntity.getDemandPeriod());
        } else {
            return null;
        }
        //        }
        //        Long interval =
        // DemandCalcModeEnum.getIntervalByCalcModel(groupEntity.getDemandCalcModel());
        //        if (interval == null) {
        //            return null;
        //        }
        List<String> deviceList =
                EmsUtil.getDeviceIdsByGroupId(groupEntity.getId(), groupDeviceService);
        List<String> meterList =
                EmsUtil.getGridMeterIdsByGroupId(
                        groupEntity.getId(), ammeterService, groupAmmeterService);
        if (CollectionUtils.isEmpty(meterList)) {
            return null;
        }
        Map<String, Map<Long, Double>> meterDataMap = new HashMap<>(meterList.size());
        List<Restrictions> meterRestrictions = influxClient.createMeterRestrictions(meterList);
        Restrictions meterTagFilter =
                Restrictions.or(meterRestrictions.toArray(new Restrictions[0]));
        for (String meterId : meterList) {
            meterDataMap.put(meterId, new HashMap<>(16));
        }
        // 填充电表值
        setDataMap(
                influxClient.getBucketMean(),
                influxClient.getMeterTable(projectId),
                MeterFieldEnum.AC_ACTIVE_POWER.field(),
                start,
                end,
                groupEntity,
                meterDataMap,
                meterTagFilter);
        // 填充ems值
        Map<String, Map<Long, Double>> deviceDataMap = new HashMap<>(deviceList.size());
        if (!CollectionUtils.isEmpty(deviceList)) {
            List<Restrictions> deviceRestrictions = new ArrayList<>(deviceList.size());
            for (String deviceId : deviceList) {
                deviceRestrictions.add(
                        Restrictions.tag(influxClient.getDeviceKey()).equal(deviceId));
                deviceDataMap.put(deviceId, new HashMap<>(16));
            }
            Restrictions deviceTagFilter =
                    Restrictions.or(deviceRestrictions.toArray(new Restrictions[0]));
            setDataMap(
                    influxClient.getBucketMean(),
                    influxClient.getEmsTable(projectId),
                    EmsFieldEnum.EMS_AC_ACTIVE_POWER.field(),
                    start,
                    end,
                    groupEntity,
                    deviceDataMap,
                    deviceTagFilter);
        }
        // 选择一个model strategy 执行 sliding or  fixed  maybe There's more...
        return demandModelStrategyTypeChooser
                .chooseStrategy(groupEntity.getDemandCalcModel())
                .modelCal(
                        new DemandModelStrategy.OaDemandCalParams()
                                .setMeterDataMap(meterDataMap)
                                .setDeviceDataMap(deviceDataMap)
                                .setDeviceList(deviceList)
                                .setMeterList(meterList)
                                .setInterval(interval));
    }

    private void setDataMap(
            String bucket,
            String table,
            String field,
            Long start,
            Long end,
            GroupEntity groupEntity,
            Map<String, Map<Long, Double>> dataMap,
            Restrictions tagFilter) {
        Flux flux =
                Flux.from(bucket)
                        .range(start, end)
                        .filter(Restrictions.measurement().equal(table))
                        .filter(Restrictions.field().equal(field))
                        .filter(tagFilter)
                        .timeShift(-2L, ChronoUnit.MINUTES);

        Integer calcModel = groupEntity.getDemandCalcModel();
        if (Boolean.TRUE.equals(DemandCalcModeEnum.isSliding(calcModel))) {
            flux =
                    flux.aggregateWindow(
                            Long.valueOf(groupEntity.getSlipTime()), ChronoUnit.MINUTES, "mean");
            // 以前的 滑差按照1分钟聚合 现在按照配置的 滑差时间
            //            flux = flux.aggregateWindow(1L, ChronoUnit.MINUTES, "mean");
        } else {
            flux =
                    flux.aggregateWindow(
                            Long.valueOf(Objects.requireNonNull(groupEntity.getDemandPeriod())),
                            ChronoUnit.MINUTES,
                            "mean");

            //            flux =
            //                    flux.aggregateWindow(
            //                            Objects.requireNonNull(
            //
            // DemandCalcModeEnum.getIntervalByCalcModel(calcModel)),
            //                            ChronoUnit.MINUTES,
            //                            "mean");
        }
        String queryString = flux.toString();
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        // T15s是设备表
        String tagKey =
                field.contains(EmsConstants.EMS)
                        ? influxClient.getDeviceKey()
                        : influxClient.getMeterKey();
        for (FluxTable fluxTable : tables) {
            for (FluxRecord fluxRecord : fluxTable.getRecords()) {
                Double value = (Double) fluxRecord.getValueByKey(EmsConstants.INFLUX_VALUE);
                Instant time = (Instant) fluxRecord.getValueByKey(EmsConstants.INFLUX_TIME);
                String id = (String) fluxRecord.getValueByKey(tagKey);
                assert time != null;
                dataMap.computeIfAbsent(id, k -> new HashMap<>()).put(time.getEpochSecond(), value);
            }
        }
    }

    @Override
    public void saveOriginAndActualDemandToInfluxDb(
            Double controlPower, Map<Long, OaDemandData> demandMap, GroupEntity groupEntity) {
        if (CollectionUtils.isEmpty(demandMap)) {
            return;
        }
        List<Point> points = new ArrayList<>();
        for (Map.Entry<Long, OaDemandData> entry : demandMap.entrySet()) {
            Point point =
                    Point.measurement(
                            influxClient.getDemandTable(groupEntity.getProjectId(), groupEntity));
            OaDemandData oaDemandData = entry.getValue();
            // 设置tags值Sd
            point.addTag("groupId", groupEntity.getId());
            point.addTag("projectId", groupEntity.getProjectId());
            //            point.addField("control_power", controlPower);
            point.addField("meter_power", oaDemandData.getActual());
            point.addField("original_Demand", oaDemandData.getOrigin());
            point.time(Instant.ofEpochSecond(entry.getKey()).toEpochMilli(), WritePrecision.MS);
            points.add(point);
        }
        influxClient
                .getWriteApi()
                .writePoints(influxClient.getBucketDemand(), influxClient.getOrg(), points);
    }

    @Override
    public void saveControlPowerDemandToInfluxDb(
            Double controlPower, GroupEntity groupEntity, Long endTime) {
        // 单独写control_power
        Point point =
                Point.measurement(
                        influxClient.getDemandTable(groupEntity.getProjectId(), groupEntity));
        // 设置tags值Sd
        point.addTag("groupId", groupEntity.getId());
        point.addTag("projectId", groupEntity.getProjectId());
        point.addField("control_power", controlPower);
        point.time(Instant.ofEpochSecond(endTime).toEpochMilli(), WritePrecision.MS);
        List<Point> points = new ArrayList<>();
        points.add(point);
        influxClient
                .getWriteApi()
                .writePoints(influxClient.getBucketDemand(), influxClient.getOrg(), points);
    }

    @Override
    public void saveCapacityDemandToDb(Double controlPower, String projectId, String groupId) {
        Point point = Point.measurement(influxClient.getCapacityTable(projectId));
        // 设置tags值Sd
        point.addTag("groupId", groupId);
        point.addTag("projectId", projectId);
        point.addField("control_power", controlPower);
        point.time(Instant.now().toEpochMilli(), WritePrecision.MS);
        influxClient
                .getWriteApi()
                .writePoint(influxClient.getBucketDemand(), influxClient.getOrg(), point);
    }
}
