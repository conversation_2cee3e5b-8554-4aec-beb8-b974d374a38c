package com.wifochina.modules.demand.job.init;

import com.wifochina.modules.demand.service.DemandQuartzService;
import com.wifochina.modules.income.job.CustomProjectQuartzService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-15 8:29 PM
 */
@Component
public class DemandJobStart implements CommandLineRunner {
    @Resource private ProjectService projectService;

    @Resource private DemandQuartzService demandQuartzService;

    @Qualifier("demandIncomeQuartzServiceImpl")
    @Resource
    private CustomProjectQuartzService demandIncomeQuartzServiceImpl;

    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public void run(String... args) {
        List<ProjectEntity> projectEntities =
                projectService.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list();

        List<String> timezoneList =
                projectEntities.stream()
                        .map(ProjectEntity::getTimezone)
                        .distinct()
                        .collect(Collectors.toList());
//        timezoneList.clear();
//        timezoneList.add("Asia/Shanghai");
        for (String timezone : timezoneList) {
            demandQuartzService.addDemandCalcJobByTimeZone(timezone);
        }
        for (ProjectEntity projectEntity : projectEntities) {
            // 如果是场站版，直接添加任务。如果>1，证明是云版本，排除场站项目
            if (projectEntities.size() > 1 && (projectEntity.getProjectModel() == 1)) {
                continue;
            }
            threadPoolTaskExecutor.submit(
                    () -> {
                        demandQuartzService.addDemandMonthInitJob(projectEntity.getId());
                        demandIncomeQuartzServiceImpl.addJob(projectEntity.getId());
                    });
        }
    }
}
