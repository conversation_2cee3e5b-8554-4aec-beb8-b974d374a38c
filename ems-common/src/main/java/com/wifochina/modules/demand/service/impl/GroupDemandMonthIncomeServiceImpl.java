package com.wifochina.modules.demand.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.demand.entity.GroupDemandInfoEntity;
import com.wifochina.modules.demand.mapper.GroupDemandMonthIncomeMapper;
import com.wifochina.modules.demand.service.GroupDemandMonthIncomeService;
import com.wifochina.modules.diagram.VO.MaxDemandVo;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.DemandDiagramService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.OperationProfitService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;

import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import com.wifochina.modules.strategy.service.TimeSharingDemandService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-14 15:06:59
 */
@Service
@Slf4j
@AllArgsConstructor
public class GroupDemandMonthIncomeServiceImpl
        extends ServiceImpl<GroupDemandMonthIncomeMapper, GroupDemandInfoEntity>
        implements GroupDemandMonthIncomeService {
    private final GroupService groupService;
    @Resource private DemandDiagramService demandDiagramService;
    @Resource private OperationProfitService operationProfitService;

    @Resource private TimeSharingDemandService timeSharingDemandService;

    @Override
    @Transactional
    public void saveGroupDemandMonthIncome(ProjectEntity projectEntity, RangeRequest request) {
        try {
            log.info("demandIncomeJob projectId:{} start", projectEntity.getId());
            // 这里是获取开启了需量控制的的分组 (注意是需量控制 , 收益是否显示 在收益显示接口部分做控制)
            List<GroupEntity> groupEntities =
                    groupService.queryEnableDemandControl(projectEntity.getId());
            long preMonthZeroTime;
            long preMonthLastTime;
            if (request.getStartDate() == null || request.getEndDate() == null) {
                // 如果不是手动执行 就是定时任务执行 就是执行上个月的
                preMonthZeroTime = MyTimeUtil.getPreMonthZeroTime(projectEntity.getTimezone());
                preMonthLastTime = MyTimeUtil.getPreMonthLastTime(projectEntity.getTimezone());
            } else {
                preMonthZeroTime = request.getStartDate();
                preMonthLastTime = request.getEndDate();
            }
            // 历史遗留问题 这里直接查询 开启了需量收益的分组的所有的 所以没有放在 saveDemandIncome里面 以后有机会改
            // 获取到的是 用于计算需量收益的 需量值  就是ems提供的 ,  原需量-电表的 就是 ems提供的
            Map<String, Map<Long, Double>> demandIncomePowerMap =
                    operationProfitService.getDemandIncomePower(
                            preMonthZeroTime, preMonthLastTime, projectEntity.getId());
            for (GroupEntity groupEntity : groupEntities) {
                boolean groupCalFlag = false;
                Map<Long, Double> incomeMap = null;
                try {
                    if (demandIncomePowerMap == null) {
                        demandIncomePowerMap = new HashMap<>();
                    }
                    Double demandIncomeValue = 0.0;
                    incomeMap = demandIncomePowerMap.get(groupEntity.getId());
                    if (incomeMap == null || incomeMap.isEmpty()) {
                        incomeMap = new HashMap<>();
                    } else {
                        // 获取到 用于计算需量收益的 需量值
                        demandIncomeValue = incomeMap.values().iterator().next();
                    }
                    demandIncomeValue = demandIncomeValue == null ? 0.0 : demandIncomeValue;
                    // 如果上面没有 用于计算需量收益的demandIncomeValue 则不会去计算
                    // 每个分组要执行的逻辑
                    groupCalFlag = true;
                    // 执行需量缓存逻辑 单个分组的
                    RequestWithGroupId requestWithGroupId =
                            new RequestWithGroupId()
                                    .setStartDate(preMonthZeroTime)
                                    .setEndDate(preMonthLastTime)
                                    .setGroupId(groupEntity.getId())
                                    .setProjectId(projectEntity.getId());
                    // old 按照整个月去 查询需量
                    saveGroupDemandInfo(projectEntity, demandIncomeValue, requestWithGroupId);
                    // 1.4.4 added timeSharing 分时去缓存
                    saveGroupDemandTimeSharingInfo(projectEntity, groupEntity, requestWithGroupId);
                } catch (Exception e) {
                    log.error("saveDemandIncome exception msg: {}", e.getMessage());
                }
                if (!groupCalFlag) {
                    // 应该是异常情况 记录一下
                    log.error(
                            "Don't calc DemandIncome groupId:{} demandIncomePowerMap:{}, incomeMap:{}",
                            groupEntity.getId(),
                            demandIncomePowerMap,
                            incomeMap);
                }
            }
            log.info("demandIncomeJob projectId:{} end", projectEntity.getId());
        } catch (Exception e) {
            log.error("项目 {} 保存月度需量收益收益异常 {}", projectEntity.getProjectName(), e.getMessage());
        }
    }

    /**
     * 缓存 分组的 最大需量 按照分时时段去缓存, 收益暂时不考虑 因后续可能会根据不同时段定价格
     *
     * @param projectEntity : project
     * @param groupEntity : group
     * @param requestWithGroupId : request
     * @throws InterruptedException : exception
     */
    @Transactional
    public void saveGroupDemandTimeSharingInfo(
            ProjectEntity projectEntity,
            GroupEntity groupEntity,
            RequestWithGroupId requestWithGroupId)
            throws InterruptedException {
        // 记录 分时的
        if (Boolean.TRUE.equals(groupEntity.getTimeSharingDemandController())) {
            // 根据有时间段去查询到分时的数据
            Map<Long, List<MaxDemandVo>> timeSharingMaxDemandInfoMap =
                    demandDiagramService.getTimeSharingMaxDemandNew(requestWithGroupId);
            if (CollUtil.isNotEmpty(timeSharingMaxDemandInfoMap)) {
                timeSharingMaxDemandInfoMap.forEach(
                        (time, maxDemanVos) -> {
                            List<Long> timeSharingDemandIds =
                                    maxDemanVos.stream()
                                            .map(MaxDemandVo::getTimeSharingMaxDemandVo)
                                            .map(
                                                    MaxDemandVo.TimeSharingMaxDemandVo
                                                            ::getTimeSharingDemandId)
                                            .distinct()
                                            .collect(Collectors.toList());
                            Map<Long, TimeSharingDemandEntity> timeSharingDemandEntityMap =
                                    timeSharingDemandService
                                            .listByIds(timeSharingDemandIds)
                                            .stream()
                                            .collect(
                                                    Collectors.toMap(
                                                            TimeSharingDemandEntity::getId,
                                                            v -> v));

                            List<GroupDemandInfoEntity> groupDemandInfoEntityList =
                                    new ArrayList<>();
                            maxDemanVos.forEach(
                                    (maxDemandVo) -> {
                                        TimeSharingDemandEntity timeSharingDemandEntity =
                                                timeSharingDemandEntityMap.get(
                                                        maxDemandVo
                                                                .getTimeSharingMaxDemandVo()
                                                                .getTimeSharingDemandId());
                                        if (timeSharingDemandEntity != null) {
                                            GroupDemandInfoEntity groupDemandInfoEntity =
                                                    new GroupDemandInfoEntity()
                                                            .setGroupId(groupEntity.getId())
                                                            .setProjectId(projectEntity.getId())
                                                            // 2025-07
                                                            .setDateStr(
                                                                    MyTimeUtil
                                                                            .formatTimestampWithTimezone(
                                                                                    requestWithGroupId
                                                                                            .getStartDate(),
                                                                                    projectEntity
                                                                                            .getTimezone(),
                                                                                    MyTimeUtil
                                                                                            .DATE_FORMATTER_MONTH))
                                                            .setStartTime(
                                                                    requestWithGroupId
                                                                            .getStartDate())
                                                            .setEndTime(
                                                                    requestWithGroupId.getEndDate())
                                                            .setTimeSharingFlag(true)
                                                            // 策略-name(00:00-08:00)
                                                            .setTimeSharingDemandName(
                                                                    timeSharingDemandEntity
                                                                                    .getName()
                                                                            + "-("
                                                                            + timeSharingDemandEntity
                                                                                    .getStartTime()
                                                                            + "-"
                                                                            + timeSharingDemandEntity
                                                                                    .getEndTime()
                                                                            + ")")
                                                            .setDemandMaxPower(
                                                                    maxDemandVo
                                                                                            .getTimeSharingMaxDemandVo()
                                                                                            .getValueVO()
                                                                                    != null
                                                                            ? maxDemandVo
                                                                                    .getTimeSharingMaxDemandVo()
                                                                                    .getValueVO()
                                                                                    .getValue()
                                                                            : 0.0);

                                            groupDemandInfoEntityList.add(groupDemandInfoEntity);
                                        }
                                    });
                            // 删除 old 数据
                            remove(
                                    new LambdaQueryWrapper<GroupDemandInfoEntity>()
                                            .eq(
                                                    GroupDemandInfoEntity::getProjectId,
                                                    projectEntity.getId())
                                            .eq(
                                                    GroupDemandInfoEntity::getGroupId,
                                                    groupEntity.getId())
                                            .eq(
                                                    GroupDemandInfoEntity::getStartTime,
                                                    requestWithGroupId.getStartDate())
                                            .eq(GroupDemandInfoEntity::getTimeSharingFlag, true));
                            this.saveBatch(groupDemandInfoEntityList);
                        });
            }
        }
    }

    /** 保存需量收益 根据分组 和 月份的最大需量 */
    @Transactional
    public void saveGroupDemandInfo(
            ProjectEntity project,
            Double demandIncomeValue,
            RequestWithGroupId requestWithGroupId) {
        String groupId = requestWithGroupId.getGroupId();
        String projectId = requestWithGroupId.getProjectId();
        // 1.主要就是把上个月的需量最大值 查询出来 有一个现成的接口
        long preMonthZeroTime = requestWithGroupId.getStartDate();
        long preMonthLastTime = requestWithGroupId.getEndDate();
        //  这里要缓存2个 一个是 最大的 平台计算出来的 一个是计量电表的
        Map<String, Map<String, List<ValueVO>>> maxDemandRate =
                demandDiagramService.getMaxDemand(requestWithGroupId);
        Map<String, List<ValueVO>> maxDemandFromMeterMap =
                demandDiagramService.getMaxDemandMeterMeasurement(requestWithGroupId);
        Map<String, List<ValueVO>> demandMap = maxDemandRate.get(requestWithGroupId.getGroupId());
        double maxDemandPower = 0.0;
        if (demandMap != null && !demandMap.isEmpty()) {
            List<ValueVO> list = demandMap.get(EmsConstants.REAL_DEMAND);
            if (CollectionUtil.isNotEmpty(list)) {
                ValueVO valueVO = list.get(0);
                // 这个就是月度最大的需量
                maxDemandPower = valueVO.getValue();
            }
        } else {
            log.warn(
                    "The maximum maxDemandPower was not queried in saveDemandIncome groupId:{}",
                    groupId);
        }
        double maxDemandMeterPower = 0.0;
        if (maxDemandFromMeterMap != null && !maxDemandFromMeterMap.isEmpty()) {
            //  取的是 AC_MAX_NEGATIVE_ACTIVE_POWER
            List<ValueVO> list =
                    maxDemandFromMeterMap.get(
                            MeterFieldEnum.MAX_NEGATIVE_ACTIVE_POWER_DEMAND.field());
            if (CollectionUtil.isNotEmpty(list)) {
                ValueVO valueVO = list.get(0);
                // 这个就是月度最大的需量
                maxDemandMeterPower = valueVO.getValue();
            }
        } else {
            log.warn(
                    "The maximum maxDemandMeterPower was not queried in saveDemandIncome groupId:{}",
                    groupId);
        }

        // 删除 old 数据
        remove(
                new LambdaQueryWrapper<GroupDemandInfoEntity>()
                        .eq(GroupDemandInfoEntity::getProjectId, projectId)
                        .eq(GroupDemandInfoEntity::getGroupId, groupId)
                        .eq(GroupDemandInfoEntity::getStartTime, preMonthZeroTime)
                        .eq(GroupDemandInfoEntity::getTimeSharingFlag, false));

        GroupDemandInfoEntity groupDemandInfoEntity = new GroupDemandInfoEntity();
        // 2.看一下现在的需量收益 是怎么计算的 计算出需量的收益
        // 直接入库 缓存月度的 需量收益
        groupDemandInfoEntity.setProjectId(projectId);
        groupDemandInfoEntity.setGroupId(groupId);
        // 这里有个问题就是 如果没有查询到 maxDemandPower 应该不太会
        groupDemandInfoEntity.setDemandMaxPower(maxDemandPower);
        // 计量的 电表的
        groupDemandInfoEntity.setDemandMaxPowerMeter(maxDemandMeterPower);
        groupDemandInfoEntity.setStartTime(preMonthZeroTime);
        groupDemandInfoEntity.setEndTime(preMonthLastTime);
        groupDemandInfoEntity.setDemandIncomePower(demandIncomeValue);
        groupDemandInfoEntity.setDateStr(
                MyTimeUtil.formatTimestampWithTimezone(
                        preMonthZeroTime, project.getTimezone(), MyTimeUtil.DATE_FORMATTER_MONTH));
        // 3.保存到数据库
        log.info("save month demand income :{}", groupDemandInfoEntity);
        this.saveOrUpdate(groupDemandInfoEntity);
    }
}
