package com.wifochina.modules.demand.job;

import cn.hutool.extra.spring.SpringUtil;

import com.wifochina.common.config.EmailSuperNoticeConfig;
import com.wifochina.common.constants.EmailLogTypeEnum;
import com.wifochina.common.util.EmailService;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.demand.entity.DemandEmailEntity;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import com.wifochina.modules.demand.service.DemandEmailService;
import com.wifochina.modules.demand.service.DemandLogService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity;
import com.wifochina.modules.strategy.entity.TimeSharingDemandEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import com.wifochina.modules.strategy.service.TimeSharingDemandService;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-01-11 10:49 AM
 */
@Slf4j
@Component
public class DemandMonthInitJob extends QuartzJobBean {

    @Resource private TimeSharingDemandService timeSharingDemandService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeInternal(@NotNull JobExecutionContext context) {
        String projectId = (String) context.getJobDetail().getJobDataMap().get("projectId");
        ProjectService projectService = SpringUtil.getBean(ProjectService.class);
        ProjectEntity projectEntity = projectService.getById(projectId);
        LocalDateTime startDateTime = LocalDateTime.now(ZoneId.of(projectEntity.getTimezone()));
        GroupService groupService = SpringUtil.getBean(GroupService.class);
        StrategyService strategyService = SpringUtil.getBean(StrategyService.class);
        DemandLogService demandLogService = SpringUtil.getBean(DemandLogService.class);
        List<GroupEntity> groupEntities =
                groupService.queryEnableDemandControl(projectEntity.getId());
        List<StrategyEntity> strategyEntities =
                strategyService.getOuterStrategyByProjectId(projectId).stream()
                        .filter(strategyEntity -> strategyEntity.getMonthControlPower() != null)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(strategyEntities)) {
            return;
        }
        log.info(
                "{} DemandMonthInitJob执行开始时间: {}, name:{} ",
                projectEntity.getProjectName(),
                startDateTime,
                context.getJobDetail().getKey().getName());
        Map<String, StrategyEntity> stringStrategyEntityMap =
                strategyEntities.stream()
                        .collect(Collectors.toMap(StrategyEntity::getGroupId, e -> e));
        for (GroupEntity groupEntity : groupEntities) {
            StrategyEntity strategyEntity = stringStrategyEntityMap.get(groupEntity.getId());
            if (strategyEntity == null) {
                continue;
            }
            // 1.4.4 月初 对 分段控需的支持
            if (Boolean.TRUE.equals(groupEntity.getTimeSharingDemandController())) {
                List<TimeSharingDemandEntity> superTimeSharingDemandEntities =
                        timeSharingDemandService.listParentBy(
                                projectEntity.getId(), groupEntity.getId());
                superTimeSharingDemandEntities.forEach(
                        timeSharingDemand -> {
                            timeSharingDemand.setDemandControlPower(
                                    timeSharingDemand.getDemandMonthControlPower());
                        });
                // 把月初的需量 更新到 control 上 下发接口会使用
                timeSharingDemandService.updateBatchById(superTimeSharingDemandEntities);
            } else {
                // old 不分时的时候
                strategyEntity.setGridControlPower(strategyEntity.getMonthControlPower());
            }
            strategyService.updateById(strategyEntity);
            // 1 记录日志
            DemandLogEntity demandLog = new DemandLogEntity();
            String logMessage =
                    String.format(
                            "月初控制需量生效, %s当前控制需量为%skW",
                            groupEntity.getName(), strategyEntity.getMonthControlPower());
            String logMessageEn =
                    String.format(
                            "The monthly beginning controlled demand has in effect, the controlled demand for %s is %s kW.",
                            groupEntity.getName(),
                            String.format("%.2f", strategyEntity.getMonthControlPower()));
            demandLog.setTime(Instant.now().getEpochSecond());
            demandLog.setGroupId(groupEntity.getId());
            demandLog.setProjectId(groupEntity.getProjectId());
            demandLog.setType(4);
            demandLog.setActualDemand(0d);
            demandLog.setDescription(logMessage);
            demandLog.setDescriptionEn(logMessageEn);
            demandLogService.save(demandLog);
            log.info(
                    "DemandMonthInitJob执行时间: {},projectId:{}, name:{}, groupId:{}, groupName:{} ",
                    startDateTime,
                    projectEntity.getId(),
                    projectEntity.getProjectName(),
                    groupEntity.getId(),
                    groupEntity.getName());
        }
        boolean uploaded = strategyService.uploadStrategy(projectId);
        if (!uploaded) {
            ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
            executor.schedule(
                    () -> strategyService.uploadStrategy(projectId), 10, TimeUnit.MINUTES);
            addFailLog(projectEntity.getId(), projectEntity.getProjectName(), demandLogService);
            sendFailMessage(projectEntity.getId(), projectEntity.getProjectName());
            log.error(
                    "DemandMonthInitJob执行更新策略时失败: projectId:{}, name:{}",
                    projectEntity.getId(),
                    projectEntity.getProjectName());
        }
        log.info(
                "{} DemandMonthInitJob执行完成时间: {}, name:{} ",
                projectEntity.getProjectName(),
                startDateTime,
                context.getJobDetail().getKey().getName());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void addFailLog(String prjectId, String projectName, DemandLogService demandLogService) {
        // 1 记录日志
        DemandLogEntity demandLog = new DemandLogEntity();
        String logMessage = String.format("%s 月初控制需量失败 ", projectName);
        String logMessageEn =
                String.format(
                        "The monthly beginning controlled demand setting of %s failed",
                        projectName);
        demandLog.setTime(Instant.now().getEpochSecond());
        demandLog.setGroupId(null);
        demandLog.setProjectId(prjectId);
        demandLog.setType(4);
        demandLog.setActualDemand(0d);
        demandLog.setDescription(logMessage);
        demandLog.setDescriptionEn(logMessageEn);
        demandLogService.save(demandLog);
    }

    public void sendFailMessage(String projectId, String projectName) {
        String subject = String.format("%s 月初需量初始化失败", projectName);
        String message =
                String.format("%s 在 %s 月初需量初始化失败, 请注意查看", projectName, LocalDateTime.now());
        EmailService emailService = SpringUtil.getBean(EmailService.class);
        EmailSuperNoticeConfig emailSuperNotice = SpringUtil.getBean(EmailSuperNoticeConfig.class);
        DemandEmailService demandEmailService = SpringUtil.getBean(DemandEmailService.class);
        Set<String> emailList =
                demandEmailService
                        .lambdaQuery()
                        .eq(DemandEmailEntity::getProjectId, projectId)
                        .like(DemandEmailEntity::getEmail, EmsConstants.AD_EMAIL_SUFFIXES)
                        .list()
                        .stream()
                        .map(DemandEmailEntity::getEmail)
                        .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(emailSuperNotice.getDemandMonthInit())
                && !emailSuperNotice.getDemandMonthInit().get(0).startsWith("${EMS_EMAIL_NOTICE")) {
            emailList.addAll(emailSuperNotice.getDemandMonthInit());
        }
        emailService.sendMessage(
                new ArrayList<>(emailList),
                subject,
                message,
                () ->
                        // 这里没有分组 所以不传分组
                        new EmailService.ServiceInfo()
                                .setProjectId(projectId)
                                .setEmailLogTypeEnum(EmailLogTypeEnum.COUNTRY_INIT_ERROR));
    }
}
