package com.wifochina.modules.demand.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.demand.entity.DemandEmailEntity;
import com.wifochina.modules.demand.request.DemandEmailPageRequest;
import com.wifochina.modules.demand.request.DemandEmailRequest;
import com.wifochina.modules.demand.service.DemandEmailService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.DemandEmailLogDetailService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Slf4j
@RestController
@Api(tags = "28-需量任务以及通知")
@RequestMapping("/demand")
public class DemandEmailController {

    @Resource private DemandEmailService demandEmailService;

    @Resource private UserService userService;

    @PostMapping("/addEmail")
    @ApiOperation("增加通知")
    @Log(module = "Demand_Email", type = OperationType.ADD)
    @PreAuthorize("hasAuthority('/demand/email/add')")
    public Result<Object> addEmail(@RequestBody DemandEmailRequest demandEmailRequest) {
        DemandEmailEntity demandEmailEntity = new DemandEmailEntity();
        BeanUtils.copyProperties(demandEmailRequest, demandEmailEntity);
        demandEmailEntity.setId(StringUtil.uuid());
        demandEmailEntity.setProjectId(WebUtils.projectId.get());
        demandEmailService.save(demandEmailEntity);
        return Result.success();
    }

    /** 修改电表 */
    @PostMapping("/updateEmail")
    @ApiOperation("修改通知")
    @Log(module = "Demand_Email", type = OperationType.UPDATE)
    @PreAuthorize("hasAuthority('/demand/email/edit')")
    public Result<String> updateEmail(@RequestBody DemandEmailRequest demandEmailRequest) {
        DemandEmailEntity demandEmailEntity = new DemandEmailEntity();
        BeanUtils.copyProperties(demandEmailRequest, demandEmailEntity);
        demandEmailService.updateById(demandEmailEntity);
        return Result.success();
    }

    /** 删除电表 */
    @PostMapping("/deleteEmail/{id}")
    @ApiOperation("删除通知")
    @Log(
            module = "Demand_Email",
            type = OperationType.DEL,
            logDetailServiceClass = DemandEmailLogDetailService.class)
    @PreAuthorize("hasAuthority('/demand/email/delete')")
    public Result<String> deleteEmail(@PathVariable("id") String id) {
        demandEmailService.removeById(id);
        return Result.success();
    }

    /** 查询电表 */
    @PostMapping("/queryEmail")
    @ApiOperation("查询通知")
    @PreAuthorize("hasAuthority('/demand/email/query')")
    public Result<IPage<DemandEmailEntity>> queryEmail(
            @RequestBody DemandEmailPageRequest demandEmailPageRequest) {
        Page<DemandEmailEntity> page =
                Page.of(demandEmailPageRequest.getPageNum(), demandEmailPageRequest.getPageSize());
        boolean containAdEmail = EmsUtil.isContainAd(SecurityUtil.getUserId(), userService);
        // 用户角色类型 client项目普通ad域控admin项目管理super超级
        IPage<DemandEmailEntity> list =
                demandEmailService.page(
                        page,
                        Wrappers.lambdaQuery(DemandEmailEntity.class)
                                .eq(DemandEmailEntity::getProjectId, WebUtils.projectId.get())
                                .like(
                                        StringUtil.notEmpty(demandEmailPageRequest.getEmail()),
                                        DemandEmailEntity::getEmail,
                                        demandEmailPageRequest.getEmail())
                                .notLike(
                                        !containAdEmail,
                                        DemandEmailEntity::getEmail,
                                        EmsConstants.AD_EMAIL_SUFFIXES)
                                .orderByAsc(DemandEmailEntity::getCreateTime));
        return Result.success(list);
    }
}
