package com.wifochina.modules.demand.job;

import cn.hutool.extra.spring.SpringUtil;

import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.List;
import java.util.stream.Collectors;

public class CapacitySaveJob extends QuartzJobBean {
    @Override
    protected void executeInternal(@NotNull JobExecutionContext context) {
        String timezone = (String) context.getJobDetail().getJobDataMap().get("timezone");
        GroupService groupService = SpringUtil.getBean(GroupService.class);
        ProjectService projectService = SpringUtil.getBean(ProjectService.class);
        StrategyService strategyService = SpringUtil.getBean(StrategyService.class);
        DemandService demandService = SpringUtil.getBean(DemandService.class);
        List<GroupEntity> groupList = groupService.queryCapacityGroup(null);
        List<ProjectEntity> projectEntities = projectService.getProjectsByTimeZone(timezone);
        List<String> projectIds =
                projectEntities.stream().map(ProjectEntity::getId).collect(Collectors.toList());
        groupList =
                groupList.stream()
                        .filter(group -> projectIds.contains(group.getProjectId()))
                        .collect(Collectors.toList());
        groupList.forEach(
                group -> {
                    StrategyEntity strategyEntity =
                            strategyService.getOuterStrategyByGroupId(group.getId());
                    Double controlPower = strategyEntity.getControlPower();
                    if (controlPower == null) {
                        return;
                    }
                    demandService.saveCapacityDemandToDb(
                            controlPower, group.getProjectId(), group.getId());
                });
    }
}
