package com.wifochina.modules.demand.request;

import com.wifochina.common.page.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-02 3:13 PM
 */
@Data
public class DemandLogPageRequest extends PageBean {
    @ApiModelProperty("1需量超了 2设置控制需量 3设置月初需量 4月初需量恢复")
    private Integer type;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty(value = "开始时间")
    private Long startDate;

    @ApiModelProperty(value = "结束时间")
    private Long endDate;
}
