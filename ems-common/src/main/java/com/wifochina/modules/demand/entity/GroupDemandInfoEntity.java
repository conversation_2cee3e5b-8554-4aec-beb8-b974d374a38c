package com.wifochina.modules.demand.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-09-14 14:45:40
 */
@Getter
@Setter
@TableName("t_group_demand_info")
@ApiModel(value = "GroupDemandInfo对象")
@ToString
@Accessors(chain = true)
public class GroupDemandInfoEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("分组id")
    private String groupId;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty(value = "最大需量功率")
    private double demandMaxPower;

    @ApiModelProperty(value = "最大需量功率(电表)")
    private double demandMaxPowerMeter;

    @ApiModelProperty(value = "降低需量功率(用于计算需量收益的  原需量-电表的)")
    private double demandIncomePower;

    // 1.4.4 分时控需策略名称
    @ApiModelProperty("分时控需策略的名称")
    private String timeSharingDemandName;

    @ApiModelProperty("年-月 2025-08")
    private String dateStr;

    @ApiModelProperty("是否是分时控需info记录")
    private Boolean timeSharingFlag;

    private Long startTime;

    private Long endTime;
}
