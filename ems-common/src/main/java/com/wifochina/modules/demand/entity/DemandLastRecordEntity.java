package com.wifochina.modules.demand.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * Created on 2025/1/3 16:41.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("t_demand_last_record")
@ApiModel(value = "DemandLastRecordEntity对象")
public class DemandLastRecordEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("通知识符uuid")
    private Integer id;

    @ApiModelProperty("分组id")
    private String groupId;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("需量最后计算时间")
    private Long time;
}
