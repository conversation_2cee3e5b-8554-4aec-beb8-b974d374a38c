package com.wifochina.modules.demand.job.init;

import com.wifochina.modules.demand.service.DemandQuartzService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
public class CapacityJobStart {
    @Resource private ProjectService projectService;

    @Resource private DemandQuartzService demandQuartzService;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        List<ProjectEntity> projectEntities =
                projectService.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list();
        List<String> timezoneList =
                projectEntities.stream()
                        .map(ProjectEntity::getTimezone)
                        .distinct()
                        .collect(Collectors.toList());
        for (String timezone : timezoneList) {
            demandQuartzService.addCapacityJob(timezone);
        }
    }
}
