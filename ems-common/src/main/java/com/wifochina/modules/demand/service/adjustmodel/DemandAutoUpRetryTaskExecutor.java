package com.wifochina.modules.demand.service.adjustmodel;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.retry.IRetryTaskExecutor;
import com.wifochina.common.retry.data.RetryTaskContext;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import com.wifochina.modules.demand.service.DemandLogService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.retrytask.mapper.RetryFailTaskMapper;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.time.Instant;

import javax.annotation.Resource;

/**
 * <AUTHOR> Created on 2024-09-25 15:50:01
 */
@Slf4j
@Component
public class DemandAutoUpRetryTaskExecutor extends IRetryTaskExecutor {

    @Resource private StrategyService strategyService;
    @Resource private GroupService groupService;
    @Resource private ProjectService projectService;
    @Resource private DemandLogService demandLogService;

    public DemandAutoUpRetryTaskExecutor(RetryFailTaskMapper retryFailTaskMapper) {
        super(retryFailTaskMapper);
    }

    @Override
    public String taskLabel() {
        // 任务的标记
        return this.getClass().getSimpleName();
    }

    /**
     * 需要先调用
     *
     * @param context :RetryTaskContext
     * @param contextHolder :contextHolder
     */
    @Override
    protected void doRetry(RetryTaskContext context, ContextHolder contextHolder) {
        String projectId;
        String groupId;
        Double exceededDemand;
        Double actualDemand;
        Boolean overUpLimitFlag;
        // 当这个不为空 说明 启动了并且从数据库恢复出来了失败任务再执行
        if (context.getDbFailTask() != null) {
            // 可以从 dbFailTask 里面获取到 一些数据
            groupId = (String) contextHolder.get(EmsConstants.GROUP_ID);
            projectId = context.getDbFailTask().getProjectId();
            exceededDemand = (Double) contextHolder.get(EmsConstants.EXCEEDED_DEMAND);
            actualDemand = (Double) contextHolder.get(EmsConstants.ACTUAL_DEMAND);
            overUpLimitFlag = (Boolean) contextHolder.get(EmsConstants.OVER_UP_LIMIT_FLAG);
        } else {
            // 这里就应该是业务执行的时候 触发
            projectId = contextHolder.getProjectId();
            groupId = (String) contextHolder.get(EmsConstants.GROUP_ID);
            exceededDemand = (Double) contextHolder.get(EmsConstants.EXCEEDED_DEMAND);
            actualDemand = (Double) contextHolder.get(EmsConstants.ACTUAL_DEMAND);
            overUpLimitFlag = (Boolean) contextHolder.get(EmsConstants.OVER_UP_LIMIT_FLAG);
        }
        if (exceededDemand != null && projectId != null && groupId != null) {
            GroupEntity group = groupService.getById(groupId);
            // 拿到 平台计算得出的最大需量 写入当前 需量控制功率 输入框
            // 这个是 对应分组的策略的主体
            StrategyEntity strategyEntity =
                    strategyService
                            .getBaseMapper()
                            .selectOne(
                                    new LambdaQueryWrapper<StrategyEntity>()
                                            .eq(StrategyEntity::getGroupId, groupId)
                                            .eq(StrategyEntity::getWeekDay, 0));
            if (strategyEntity != null) {
                Double oldControlPower = strategyEntity.getGridControlPower();
                log.info(
                        "adjustExecute groupId: {}  demand value : {} exceed control:{}  start Deliver demand data",
                        groupId,
                        exceededDemand,
                        oldControlPower);
                // 把目前超过的 需量控制 设置上去
                strategyEntity.setGridControlPower(exceededDemand);
                // 更新数据库
                strategyService.updateById(strategyEntity);
                // 下发 策略 具体下发还是 exceededDemand * 控制系数==如下
                // exceededDemand *(groupEntity.getDemandControlRate() / 100)
                boolean uploaded = strategyService.uploadStrategy(projectId);
                if (!uploaded) {
                    throw new ServiceException(ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                }
                // 这里需要 再加一个 日志记录 记录 System 当前控制需量自动调整为... 直接使用 exceededDemand 框里面的 已经确定了
                // 新增的需求 如果下发成功 记录日志 日志记录 记录 System 当前控制需量自动调整为...
                upDemandLogAdd(
                        groupService.getById(groupId),
                        projectService.getById(projectId),
                        exceededDemand,
                        actualDemand,
                        group.getDemandControlAutoRate(),
                        overUpLimitFlag);
                log.info(
                        "adjustExecute groupId: {} exceed demand control:{}  start Deliver demand data success",
                        groupId,
                        exceededDemand);
            } else {
                log.error("can't find strategy by groupId :{} and weekDay = 0", groupId);
            }
        }
    }

    private void upDemandLogAdd(
            GroupEntity group,
            ProjectEntity project,
            double upDemand,
            double actualDemand,
            double autoRate,
            Boolean overUpLimitFlag) {
        // 记录日志 当前控制需量自动调整为 ... (group.getDemandControlAutoRate() / 100);
        String logMessage;
        String logMessageEn;
        if (overUpLimitFlag) {
            logMessage =
                    String.format(
                            "%s 当前控制需量自动调整为 %skW(自动抬升上限:%skW)",
                            group.getName(), upDemand, upDemand);
            logMessageEn =
                    String.format(
                            "project %s group %s current control demand auto adjustment %skW(Automatically raise the upper limit: %skW)",
                            project.getProjectName(), group.getName(), upDemand, upDemand);
        } else {
            logMessage =
                    String.format(
                            "%s 当前控制需量自动调整为 %skW(%s)",
                            group.getName(), upDemand, actualDemand + "kW*" + autoRate / 100);
            logMessageEn =
                    String.format(
                            "project %s group %s current control demand auto adjustment %skW(%s)",
                            project.getProjectName(),
                            group.getName(),
                            upDemand,
                            actualDemand + "kW*" + autoRate / 100);
        }

        DemandLogEntity demandLog = new DemandLogEntity();
        demandLog.setTime(Instant.now().getEpochSecond());
        demandLog.setGroupId(group.getId());
        demandLog.setProjectId(project.getId());
        demandLog.setType(5);
        demandLog.setActualDemand(upDemand);
        demandLog.setDescription(logMessage);
        demandLog.setDescriptionEn(logMessageEn);
        demandLogService.save(demandLog);
    }
}
