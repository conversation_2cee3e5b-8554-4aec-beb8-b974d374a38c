package com.wifochina.modules.demand.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Getter
@Setter
@TableName("t_demand_email")
@ApiModel(value = "DemandEmailEntity对象")
public class DemandEmailEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通知识符uuid")
    private String id;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("项目id")
    private String projectId;

}
