package com.wifochina.modules.demand.controller;

import javax.annotation.Resource;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.page.Result;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import com.wifochina.modules.demand.request.DemandLogPageRequest;
import com.wifochina.modules.demand.service.DemandLogService;
import com.wifochina.modules.oauth.util.WebUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-12
 */
@Slf4j
@RestController
@RequestMapping("/demand")
@Api(tags = "28-需量任务以及通知")
public class DemandLogController {

    @Resource
    private DemandLogService demandLogService;

    @PostMapping("/queryLog")
    @ApiOperation("查询日志")
    @PreAuthorize("hasAuthority('/demand/log/query')")
    public Result<IPage<DemandLogEntity>> queryLog(@RequestBody DemandLogPageRequest demandLogPageRequest) {
        Page<DemandLogEntity> page = Page.of(demandLogPageRequest.getPageNum(), demandLogPageRequest.getPageSize());
        IPage<DemandLogEntity> list = demandLogService.page(page,
            Wrappers.lambdaQuery(DemandLogEntity.class).eq(DemandLogEntity::getProjectId, WebUtils.projectId.get())
                .eq(demandLogPageRequest.getType() != null, DemandLogEntity::getType, demandLogPageRequest.getType())
                .ge(DemandLogEntity::getTime, demandLogPageRequest.getStartDate())
                .le(DemandLogEntity::getTime, demandLogPageRequest.getEndDate())
                .like(StringUtils.hasLength(demandLogPageRequest.getDescription()), DemandLogEntity::getDescription,
                    demandLogPageRequest.getDescription())
                .orderByDesc(DemandLogEntity::getCreateTime));
        return Result.success(list);
    }
}
