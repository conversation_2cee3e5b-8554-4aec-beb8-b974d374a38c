package com.wifochina.modules.demand.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.modules.demand.entity.GroupDemandInfoEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-09-14 15:05:56
 */
public interface GroupDemandMonthIncomeService extends IService<GroupDemandInfoEntity> {

    void saveGroupDemandMonthIncome(ProjectEntity projectEntity, RangeRequest request);
}
