package com.wifochina.modules.data.entity;

import lombok.Data;

import java.util.List;

/**
 * @since 4/25/2022 3:57 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MeterContentData {

    private Boolean online;

    private Boolean data_invalid;

    private Integer device_type_code;

    private Boolean is_dc;

    /** 直流电压 */
    private Double dc_voltage;

    /** 直流电流 */
    private Double dc_current;

    /** 直流 有功 */
    private Double dc_power;

    private Double dc_history_positive_power_in_kwh;

    private Double dc_history_negative_power_in_kwh;

    /** 交流电压总和 */
    private Double ac_voltage;

    private Double ac_current;

    /** 总交流有功功率 */
    private Double ac_active_power;

    /** 交流无功功率 */
    private Double ac_reactive_power;

    /** 交流电压 */
    private List<Double> ac_voltages;

    /** 交流电流 */
    private List<Double> ac_currents;

    private List<Double> ac_line_voltages;

    private List<Double> ac_active_powers;

    private List<Double> ac_reactive_powers;

    /** 差值算运营收益 */
    private Double ac_history_positive_power_in_kwh;

    /** 差值算运营收益 */
    private Double ac_history_negative_power_in_kwh;

    /** 频率 */
    private Double frequency;

    private List<Integer> aux_digital;

    private List<Double> aux_analog;

    private Double ct;

    private Double pt;

    /** 测控设备运行监控里面取的数据 */
    private McInfo mc_info;

    private List<Double> transformer_temperature;

    private ElectricityUsage positiveElectricityUsage;

    private ElectricityUsage negativeElectricityUsage;

    private Double max_positive_active_power_demand; // kw

    private Long max_positive_active_power_demand_happened_time;

    private Double max_positive_reactive_power_demand; // kw

    private Long max_positive_reactive_power_demand_happened_time;

    private Double max_negative_active_power_demand; // kw

    private Long max_negative_active_power_demand_happened_time;

    private Double max_negative_reactive_power_demand; // kw

    private Long max_negative_reactive_power_demand_happened_time;
}
