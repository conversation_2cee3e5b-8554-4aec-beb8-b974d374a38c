package com.wifochina.modules.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @since 2024-03-14 7:40 PM
 * <AUTHOR>
 */
@Data
public class SteerableContentData {
    @JsonProperty("Online")
    private Boolean online;

    @JsonProperty("DataInvalid")
    private Boolean dataInvalid;

    @JsonProperty("DeviceTypeCode")
    private Integer deviceTypeCode;

    @JsonProperty("OnOff")
    private Boolean onOff; // 开关

    @JsonProperty("Data")
    private Map<String, Double> data; // 运行状态数据

    @JsonProperty("ControllableDebugInfo")
    private DebugInfo controllableDebugInfo; // 调试数据

    @JsonProperty("StateCode")
    private Integer[] stateCode; // 运行状态码

    @JsonProperty("AlarmCode")
    private Integer[] alarmCode; // 运行告警码

    @JsonProperty("FaultCode")
    private Integer[] faultCode; // 运行故障码

    @JsonProperty("StateEventCode")
    private String[] stateEventCode; // 运行状态码

    @JsonProperty("AlarmEventCode")
    private String[] alarmEventCode; // 运行告警码

    @JsonProperty("FaultEventCode")
    private String[] faultEventCode; // 运行故障码

    @JsonProperty("StateEvent")
    private String[] stateEvent; // 运行状态事件

    @JsonProperty("AlarmEvent")
    private String[] alarmEvent; // 运行告警事件

    @JsonProperty("FaultEvent")
    private String[] faultEvent; // 运行故障事件

    @JsonProperty("StateEventEN")
    private String[] stateEventEN;

    @JsonProperty("AlarmEventEN")
    private String[] alarmEventEN;

    @JsonProperty("FaultEventEN")
    private String[] faultEventEN;

    @JsonProperty("SupportControlActions")
    private Integer[] supportControlActions; // 支持的控制动作

    @JsonProperty("SupportCmd")
    private List<SteerableAction> supportCmd; // 支持的控制动作

    @JsonProperty("Fault")
    private Boolean fault;

    @JsonProperty("Alarm")
    private Boolean alarm;
}
