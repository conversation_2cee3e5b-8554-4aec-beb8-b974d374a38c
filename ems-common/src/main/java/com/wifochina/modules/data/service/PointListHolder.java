package com.wifochina.modules.data.service;

import com.wifochina.common.constants.TypeCodeIndexEnum;
import com.wifochina.modules.data.entity.*;

import com.wifochina.realtimemodel.common.VendorTypeConstants;
import lombok.Getter;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @since 2024-03-20 2:51 PM
 * <AUTHOR>
 */
@Service
@Getter
public class PointListHolder {

    private final PointDataService100 pointDataService100;
    private final PointDataService200 pointDataService200;

    private static final String PCS_COLUMN_100 = "500+PCSi*100";
    private static final String PCS_COLUMN_200 = "500+PCSi*150";
    private static final String CLUSTER_COLUMN_100 = "1100+CLUSTERi*50";
    private static final String CLUSTER_COLUMN_200 = "7500+CLUSTERi*150";
    private static final String AIR_COLUMN_100 = "100+AIRi*10";
    private static final String AIR_COLUMN_200 = "12000+AIRi*100";
    private static final String FIRE_COLUMN_100 = "150+FIREi*10";
    private static final String FIRE_COLUMN_200 = "1600+FIREi*100";
    private static final String WATER_COLUMN_100 = "250+WATERi*10";
    private static final String WATER_COLUMN_200 = "14000+WATERi*100";
    private static final String DCDC_COLUMN_100 = "20100+DCDCi*100";
    private static final String DCDC_COLUMN_200 = "3500+DCDCi*150";
    private static final String STATES_COLUMN_100 = "21100+STATESi*50";
    private static final String STATES_COLUMN_200 = "18000+STATESi*100";

    private List<PointDataEntity> fixedPointList100;
    private List<PointDataEntity> fixedPointList200;

    private List<PointDataEntity> systemBitPointList100;
    private List<PointDataEntity> airBitPointList100;
    private List<PointDataEntity> waterBitPointList100;
    private List<PointDataEntity> fireBitPointList100;
    private List<PointDataEntity> bmsClusterBitPointList100;
    private List<PointDataEntity> bmsCommonBitList100;
    private List<PointDataEntity> pcsBitPointList100;
    private List<PointDataEntity> dcdcBitPointList100;
    private List<PointDataEntity> statesBitPointList100;

    private List<PointDataEntity> systemBitPointList200;
    private List<PointDataEntity> airBitPointList200;
    private List<PointDataEntity> waterBitPointList200;
    private List<PointDataEntity> fireBitPointList200;
    private List<PointDataEntity> bmsClusterBitPointList200;
    private List<PointDataEntity> bmsCommonBitList200;
    private List<PointDataEntity> pcsBitPointList200;
    private List<PointDataEntity> dcdcBitPointList200;
    private List<PointDataEntity> statesBitPointList200;

    private List<PointDataEntity> systemDataPointList100;
    private List<PointDataEntity> airDataPointList100;
    private List<PointDataEntity> waterDataPointList100;
    private List<PointDataEntity> fireDataPointList100;
    private List<PointDataEntity> bmsClusterDataPointList100;
    private List<PointDataEntity> bmsCommonDataList100;
    private List<PointDataEntity> pcsDataPointList100;
    private List<PointDataEntity> dcdcDataPointList100;
    private List<PointDataEntity> statesDataPointList100;

    private List<PointDataEntity> systemDataPointList200;
    private List<PointDataEntity> airDataPointList200;
    private List<PointDataEntity> waterDataPointList200;
    private List<PointDataEntity> fireDataPointList200;
    private List<PointDataEntity> bmsClusterDataPointList200;
    private List<PointDataEntity> bmsCommonDataList200;
    private List<PointDataEntity> pcsDataPointList200;
    private List<PointDataEntity> dcdcDataPointList200;
    private List<PointDataEntity> statesDataPointList200;

    public Map<String, Integer> equipCountIndexMap100 = new HashMap<>();
    public Map<String, Integer> equipCountIndexMap200 = new HashMap<>();
    public List<PointDataEntity> pointDataEventList100;
    public List<PointDataEntity> pointDataEventList200;
    public List<PointDataEntity> pointDefinitionLevelT0List100;
    public List<PointDataEntity> pointDefinitionLevelT0List200;
    public List<PointDataEntity> pointDefinitionLevelT1List100;
    public List<PointDataEntity> pointDefinitionLevelT1List200;
    public List<PointDataEntity> pointDefinitionLevelT2List100;
    public List<PointDataEntity> pointDefinitionLevelT2List200;
    public List<PointDataEntity> asciiPointDataList100;
    public List<PointDataEntity> asciiPointDataList200;

    private int emsSerial100;
    private int emsSerial200;
    private int emsDesignChargePower100;
    private int emsDesignChargePower200;
    private int emsDesignDischargePower100;
    private int emsDesignDischargePower200;
    private int emsCapacity100;
    private int emsCapacity200;
    private int emsDesignPower100;
    private int emsDesignPower200;
    private int emsHistoryOutputEnergy100;
    private int emsHistoryOutputEnergy200;
    private int emsHistoryInputEnergy100;
    private int emsHistoryInputEnergy200;
    private int emsAcActivePower100;
    private int emsAcActivePower200;
    private int emsAcReactivePower100;
    private int emsAcReactivePower200;
    private int gpsOnline100;
    private int gpsOnline200;
    private int gpsLatitude100;
    private int gpsLatitude200;
    private int gpsLongitude100;
    private int gpsLongitude200;
    private int soc100;
    private int soc200;
    private int soh100;
    private int soh200;
    public int dcdcMeterVoltage100;
    public int dcdcMeterVoltage200;
    public int dcdcMeterCurrent100;
    public int dcdcMeterCurrent200;
    public int dcdcMeterPower100;
    public int dcdcMeterPower200;
    public int dcdcMeterHistoryEnergyPos100;
    public int dcdcMeterHistoryEnergyPos200;
    public int dcdcMeterHistoryEnergyEng100;
    public int dcdcMeterHistoryEnergyEng200;
    public int systemRunStatus100;
    public int systemRunStatus200;
    public int emsState100;
    public int emsState200;
    public int emsSerialLength100;
    public int emsSerialLength200;
    public int bmsNum100;
    public int bmsNum200;
    public int bmsClusterNum100;
    public int bmsClusterNum200;
    public int bmsStackNum100;
    public int bmsStackNum200;
    public int bmsCellNum100;
    public int bmsCellNum200;
    public int bmsCellTNum100;
    public int bmsCellTNum200;
    public int pcsNum100;
    public int pcsNum200;
    public int airNum100;
    public int airNum200;
    public int fireNum100;
    public int fireNum200;
    public int waterNum100;
    public int waterNum200;
    public int dcdcNum100;
    public int dcdcNum200;
    public int statesNum100;
    public int statesNum200;
    public int emsChargePowerLimit100;
    public int emsChargePowerLimit200;
    public int emsDisChargePowerLimit100;
    public int emsDischargePowerLimit200;
    public int bmsDischargeableEnergy100;
    public int bmsDischargeableEnergy200;
    public int bmsChargeableEnergy100;
    public int bmsChargeableEnergy200;
    private String statesPointOffset100;
    private String statesPointOffset200;
    private int directPowerControlCounter100;
    private int directPowerControlCounter200;
    public int systemOffGrid100;
    public int systemOffGrid200;

    @Autowired
    public PointListHolder(
            PointDataService100 pointData100Service, PointDataService200 pointData200Service) {
        this.pointDataService100 = pointData100Service;
        this.pointDataService200 = pointData200Service;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        List<PointDataEntity100> pointDataEntity100s = pointDataService100.list();
        List<PointDataEntity200> pointDataEntity200s = pointDataService200.list();

        fixedPointList100 =
                pointDataEntity100s.stream()
                        .filter(p -> !p.getPointColumn().contains("{i}"))
                        .collect(Collectors.toList());
        fixedPointList200 =
                pointDataEntity200s.stream()
                        .filter(p -> !p.getPointColumn().contains("{i}"))
                        .collect(Collectors.toList());
        List<PointDataEntity> pointDataEntityList100 =
                pointDataEntity100s.stream()
                        .map(PointDataEntity.class::cast)
                        .collect(Collectors.toList());
        List<PointDataEntity> pointDataEntityList200 =
                pointDataEntity200s.stream()
                        .map(PointDataEntity.class::cast)
                        .collect(Collectors.toList());

        Map<String, PointDataEntity> pointDataMap100 =
                pointDataEntityList100.stream()
                        .collect(
                                Collectors.toMap(
                                        PointDataEntity::getPointColumn, Function.identity()));
        Map<String, PointDataEntity> pointDataMap200 =
                pointDataEntityList200.stream()
                        .collect(
                                Collectors.toMap(
                                        PointDataEntity::getPointColumn, Function.identity()));
        statesPointOffset100 = pointDataMap100.get("states_{i}_type_code").getPointOffset();
        statesPointOffset200 = pointDataMap200.get("states_{i}_type_code").getPointOffset();

        initEmsDataList100(pointDataEntityList100);
        initEmsBitList100(pointDataEntityList100);
        initEmsDataList200(pointDataEntityList200);
        initEmsBitList200(pointDataEntityList200);
        initEquipCountMap(pointDataMap100, pointDataMap200);
        initTaskAnalysis100(pointDataEntityList100);
        initTaskAnalysis200(pointDataEntityList200);

        initAddressIndex(pointDataMap100, pointDataMap200);
    }

    private void initAddressIndex(
            Map<String, PointDataEntity> pointDataMap100,
            Map<String, PointDataEntity> pointDataMap200) {
        emsSerial100 = getAddress(pointDataMap100.get("ems_serial"));
        emsSerial200 = getAddress(pointDataMap200.get("ems_serial"));
        emsSerialLength100 =
                pointDataMap100.get("ems_serial") == null
                        ? 0
                        : pointDataMap100.get("ems_serial").getPointMul();
        emsSerialLength200 =
                pointDataMap200.get("ems_serial") == null
                        ? 0
                        : pointDataMap200.get("ems_serial").getPointMul();
        emsDesignChargePower100 = getAddress(pointDataMap100.get("ems_design_charge_power"));
        emsDesignChargePower200 = getAddress(pointDataMap200.get("ems_design_charge_power"));
        emsDesignDischargePower100 = getAddress(pointDataMap100.get("ems_design_discharge_power"));
        emsDesignDischargePower200 = getAddress(pointDataMap200.get("ems_design_discharge_power"));
        emsCapacity100 = getAddress(pointDataMap100.get("ems_design_storage_energy"));
        emsCapacity200 = getAddress(pointDataMap200.get("ems_design_storage_energy"));
        emsDesignPower100 = getAddress(pointDataMap100.get("ems_design_power"));
        emsDesignPower200 = getAddress(pointDataMap200.get("ems_design_power"));
        emsHistoryOutputEnergy100 = getAddress(pointDataMap100.get("ems_history_output_energy"));
        emsHistoryOutputEnergy200 = getAddress(pointDataMap200.get("ems_history_output_energy"));
        emsHistoryInputEnergy100 = getAddress(pointDataMap100.get("ems_history_input_energy"));
        emsHistoryInputEnergy200 = getAddress(pointDataMap200.get("ems_history_input_energy"));
        emsAcActivePower100 = getAddress(pointDataMap100.get("ems_ac_active_power"));
        emsAcActivePower200 = getAddress(pointDataMap200.get("ems_ac_active_power"));
        emsAcReactivePower100 = getAddress(pointDataMap100.get("ems_ac_reactive_power"));
        emsAcReactivePower200 = getAddress(pointDataMap200.get("ems_ac_reactive_power"));
        gpsOnline100 = getAddress(pointDataMap100.get("gps_online"));
        gpsOnline200 = getAddress(pointDataMap200.get("gps_online"));
        gpsLatitude100 = getAddress(pointDataMap100.get("gps_latitude"));
        gpsLatitude200 = getAddress(pointDataMap200.get("gps_latitude"));
        gpsLongitude100 = getAddress(pointDataMap100.get("gps_longitude"));
        gpsLongitude200 = getAddress(pointDataMap200.get("gps_longitude"));
        soc100 = getAddress(pointDataMap100.get("ems_soc"));
        soc200 = getAddress(pointDataMap200.get("ems_soc"));
        soh100 = getAddress(pointDataMap100.get("ems_soh"));
        soh200 = getAddress(pointDataMap200.get("ems_soh"));
        dcdcMeterVoltage100 = getAddress(pointDataMap100.get("dcdc_meter_voltage"));
        dcdcMeterVoltage200 = getAddress(pointDataMap200.get("dcdc_meter_voltage"));
        dcdcMeterCurrent100 = getAddress(pointDataMap100.get("dcdc_meter_current"));
        dcdcMeterCurrent200 = getAddress(pointDataMap200.get("dcdc_meter_current"));
        dcdcMeterPower100 = getAddress(pointDataMap100.get("dcdc_meter_power"));
        dcdcMeterPower200 = getAddress(pointDataMap200.get("dcdc_meter_power"));
        dcdcMeterHistoryEnergyPos100 =
                getAddress(pointDataMap100.get("dcdc_meter_history_energy_pos"));
        dcdcMeterHistoryEnergyPos200 =
                getAddress(pointDataMap200.get("dcdc_meter_history_energy_pos"));
        dcdcMeterHistoryEnergyEng100 =
                getAddress(pointDataMap100.get("dcdc_meter_history_energy_neg"));
        dcdcMeterHistoryEnergyEng200 =
                getAddress(pointDataMap200.get("dcdc_meter_history_energy_neg"));
        systemRunStatus100 = getAddress(pointDataMap100.get("system_run_status"));
        systemRunStatus200 = getAddress(pointDataMap200.get("system_run_status"));
        emsState100 = getAddress(pointDataMap100.get("ems_state"));
        emsState200 = getAddress(pointDataMap200.get("ems_state"));
        bmsNum100 = getAddress(pointDataMap100.get("bms_count"));
        bmsNum200 = getAddress(pointDataMap200.get("bms_count"));
        bmsClusterNum100 = getAddress(pointDataMap100.get("bms_cluster_count"));
        bmsClusterNum200 = getAddress(pointDataMap200.get("bms_cluster_count"));
        bmsStackNum100 = getAddress(pointDataMap100.get("bms_stack_per_cluster_count"));
        bmsStackNum200 = getAddress(pointDataMap200.get("bms_stack_per_cluster_count"));
        bmsCellNum100 = getAddress(pointDataMap100.get("bms_cell_per_stack_count"));
        bmsCellNum200 = getAddress(pointDataMap200.get("bms_cell_per_stack_count"));
        bmsCellTNum100 = getAddress(pointDataMap100.get("bms_temperature_per_stack_count"));
        bmsCellTNum200 = getAddress(pointDataMap200.get("bms_temperature_per_stack_count"));
        pcsNum100 = getAddress(pointDataMap100.get("pcs_count"));
        pcsNum200 = getAddress(pointDataMap200.get("pcs_count"));
        airNum100 = getAddress(pointDataMap100.get("air_conditioner_count"));
        airNum200 = getAddress(pointDataMap200.get("air_conditioner_count"));
        fireNum100 = getAddress(pointDataMap100.get("firefighting_count"));
        fireNum200 = getAddress(pointDataMap200.get("firefighting_count"));
        waterNum100 = getAddress(pointDataMap100.get("water_cooler_count"));
        waterNum200 = getAddress(pointDataMap200.get("water_cooler_count"));
        dcdcNum100 = getAddress(pointDataMap100.get("dcdc_count"));
        dcdcNum200 = getAddress(pointDataMap200.get("dcdc_count"));
        statesNum100 = getAddress(pointDataMap100.get("states_count"));
        statesNum200 = getAddress(pointDataMap200.get("states_count"));
        emsChargePowerLimit100 = getAddress(pointDataMap100.get("ems_charge_power_limit"));
        emsChargePowerLimit200 = getAddress(pointDataMap200.get("ems_charge_power_limit"));
        emsDisChargePowerLimit100 = getAddress(pointDataMap100.get("ems_discharge_power_limit"));
        emsDischargePowerLimit200 = getAddress(pointDataMap200.get("ems_discharge_power_limit"));
        bmsDischargeableEnergy100 = getAddress(pointDataMap100.get("bms_dischargeable_energy"));
        bmsDischargeableEnergy200 = getAddress(pointDataMap200.get("bms_dischargeable_energy"));
        bmsChargeableEnergy100 = getAddress(pointDataMap100.get("bms_chargeable_energy"));
        bmsChargeableEnergy200 = getAddress(pointDataMap200.get("bms_chargeable_energy"));
        directPowerControlCounter100 =
                getAddress(pointDataMap100.get("direct_power_control_counter"));
        directPowerControlCounter200 =
                getAddress(pointDataMap200.get("direct_power_control_counter"));
        systemOffGrid100 = getAddress(pointDataMap100.get("system_off_grid"));
        systemOffGrid200 = getAddress(pointDataMap200.get("system_off_grid"));
    }

    public int getAddress(PointDataEntity pointDataEntity) {
        return pointDataEntity.getPointAddress()
                + Integer.parseInt(pointDataEntity.getPointOffset());
    }

    private void initTaskAnalysis200(List<PointDataEntity> list) {
        // 【采集级别T0】单设备点位具体描述信息列表
        pointDefinitionLevelT0List200 = getPointDefinitionLevelT0List(list);
        // 【采集级别T1或者T1+】单设备点位具体描述信息列表
        pointDefinitionLevelT1List200 = getPointDefinitionLevelT1List(list);
        // 【采集级别T2】单设备点位具体描述信息列表
        pointDefinitionLevelT2List200 = getPointDefinitionLevelT2List(list);
        asciiPointDataList200 = getAsciiPointDataList(list);
        pointDataEventList200 = getEmsDataEventList(list);
    }

    private void initTaskAnalysis100(List<PointDataEntity> list) {
        // 【采集级别T0】单设备点位具体描述信息列表
        pointDefinitionLevelT0List100 = getPointDefinitionLevelT0List(list);
        // 【采集级别T1或者T1+】单设备点位具体描述信息列表
        pointDefinitionLevelT1List100 = getPointDefinitionLevelT1List(list);
        // 【采集级别T2】单设备点位具体描述信息列表
        pointDefinitionLevelT2List100 = getPointDefinitionLevelT2List(list);
        asciiPointDataList100 = getAsciiPointDataList(list);
        pointDataEventList100 = getEmsDataEventList(list);
    }

    private @NotNull List<PointDataEntity> getPointDefinitionLevelT0List(
            List<PointDataEntity> list) {
        return list.stream()
                .filter(
                        point ->
                                point.getPointLevel().equals("T0")
                                        && !point.getPointType().equals("ASCII"))
                .collect(Collectors.toList());
    }

    private @NotNull List<PointDataEntity> getPointDefinitionLevelT1List(
            List<PointDataEntity> list) {
        return list.stream()
                .filter(
                        point ->
                                point.getPointLevel().equals("T1")
                                        || point.getPointLevel().equals("T1+"))
                .collect(Collectors.toList());
    }

    private @NotNull List<PointDataEntity> getPointDefinitionLevelT2List(
            List<PointDataEntity> list) {
        return list.stream()
                .filter(point -> point.getPointLevel().equals("T2"))
                .collect(Collectors.toList());
    }

    private @NotNull List<PointDataEntity> getAsciiPointDataList(List<PointDataEntity> list) {
        return list.stream()
                .filter(point -> point.getPointType().equals("ASCII"))
                .collect(Collectors.toList());
    }

    private List<PointDataEntity> getEmsDataEventList(List<PointDataEntity> list) {
        return list.stream()
                .filter(point -> point.getPointType().equals("BIT"))
                .collect(Collectors.toList());
    }

    private void initEquipCountMap(
            Map<String, PointDataEntity> pointDataMap100,
            Map<String, PointDataEntity> pointDataMap200) {
        for (OffsetTypeEnum offsetType : OffsetTypeEnum.values()) {
            PointDataEntity pointDataEntity100 = pointDataMap100.get(offsetType.getColumn());
            Optional.ofNullable(pointDataEntity100)
                    .ifPresent(
                            e ->
                                    equipCountIndexMap100.put(
                                            offsetType.getCode(),
                                            e.getPointAddress()
                                                    + Integer.parseInt(e.getPointOffset())));
            PointDataEntity pointDataEntity200 = pointDataMap200.get(offsetType.getColumn());
            Optional.ofNullable(pointDataEntity200)
                    .ifPresent(
                            e ->
                                    equipCountIndexMap200.put(
                                            offsetType.getCode(),
                                            e.getPointAddress()
                                                    + Integer.parseInt(e.getPointOffset())));
        }
    }

    private void initEmsBitList200(List<PointDataEntity> list) {
        this.systemBitPointList200 = getSystemBitPointList(list);
        this.airBitPointList200 = initBitPointList(list, AIR_COLUMN_200);
        this.waterBitPointList200 = initBitPointList(list, WATER_COLUMN_200);
        this.fireBitPointList200 = initBitPointList(list, FIRE_COLUMN_200);
        this.bmsClusterBitPointList200 = initBitPointList(list, CLUSTER_COLUMN_200);
        this.bmsCommonBitList200 = getBmsCommonBitList(list);
        this.pcsBitPointList200 = initBitPointList(list, PCS_COLUMN_200);
        this.dcdcBitPointList200 = initBitPointList(list, DCDC_COLUMN_200);
        this.statesBitPointList200 = initBitPointList(list, STATES_COLUMN_200);
    }

    private void initEmsDataList200(List<PointDataEntity> list) {
        this.systemDataPointList200 = getSystemDataPointList(list);
        this.airDataPointList200 = initDataPointList(list, AIR_COLUMN_200);
        this.waterDataPointList200 = initDataPointList(list, WATER_COLUMN_200);
        this.fireDataPointList200 = initDataPointList(list, FIRE_COLUMN_200);
        this.bmsClusterDataPointList200 = initDataPointList(list, CLUSTER_COLUMN_200);
        this.bmsCommonDataList200 = getBmsCommonDataList(list);
        this.pcsDataPointList200 = initDataPointList(list, PCS_COLUMN_200);
        this.dcdcDataPointList200 = initDataPointList(list, DCDC_COLUMN_200);
        this.statesDataPointList200 = initDataPointList(list, STATES_COLUMN_200);
    }

    private void initEmsBitList100(List<PointDataEntity> list) {
        this.systemBitPointList100 = getSystemBitPointList(list);
        this.airBitPointList100 = initBitPointList(list, AIR_COLUMN_100);
        this.waterBitPointList100 = initBitPointList(list, WATER_COLUMN_100);
        this.fireBitPointList100 = initBitPointList(list, FIRE_COLUMN_100);
        this.bmsClusterBitPointList100 = initBitPointList(list, CLUSTER_COLUMN_100);
        this.bmsCommonBitList100 = getBmsCommonBitList(list);
        this.pcsBitPointList100 = initBitPointList(list, PCS_COLUMN_100);
        this.dcdcBitPointList100 = initBitPointList(list, DCDC_COLUMN_100);
        this.statesBitPointList100 = initBitPointList(list, STATES_COLUMN_100);
    }

    private void initEmsDataList100(List<PointDataEntity> list) {
        this.systemDataPointList100 = getSystemDataPointList(list);
        this.airDataPointList100 = initDataPointList(list, AIR_COLUMN_100);
        this.waterDataPointList100 = initDataPointList(list, WATER_COLUMN_100);
        this.fireDataPointList100 = initDataPointList(list, FIRE_COLUMN_100);
        this.bmsClusterDataPointList100 = initDataPointList(list, CLUSTER_COLUMN_100);
        this.bmsCommonDataList100 = getBmsCommonDataList(list);
        this.pcsDataPointList100 = initDataPointList(list, PCS_COLUMN_100);
        this.dcdcDataPointList100 = initDataPointList(list, DCDC_COLUMN_100);
        this.statesDataPointList100 = initDataPointList(list, STATES_COLUMN_100);
    }

    public List<PointDataEntity> getSystemBitPointList(int type) {
        if (type == 1) {
            return systemBitPointList100;
        } else if (type == 2) {
            return systemBitPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getAirBitPointList(int type) {
        if (type == 1) {
            return airBitPointList100;
        } else if (type == 2) {
            return airBitPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getWaterBitPointList(int type) {
        if (type == 1) {
            return waterBitPointList100;
        } else if (type == 2) {
            return waterBitPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getFireBitPointList(int type) {
        if (type == 1) {
            return fireBitPointList100;
        } else if (type == 2) {
            return fireBitPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getBmsClusterBitPointList(int type) {
        if (type == 1) {
            return bmsClusterBitPointList100;
        } else if (type == 2) {
            return bmsClusterBitPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getBmsCommonBitList(int type) {
        if (type == 1) {
            return bmsCommonBitList100;
        } else if (type == 2) {
            return bmsCommonBitList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getPcsBitPointList(int type) {
        if (type == 1) {
            return pcsBitPointList100;
        } else if (type == 2) {
            return pcsBitPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getDcdcBitPointList(int type) {
        if (type == 1) {
            return dcdcBitPointList100;
        } else if (type == 2) {
            return dcdcBitPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getStatesBitPointList(int type) {
        if (type == 1) {
            return statesBitPointList100;
        } else if (type == 2) {
            return statesBitPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getSystemDataPointList(int type) {
        if (type == 1) {
            return systemDataPointList100;
        } else if (type == 2) {
            return systemDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getAirDataPointList(int type) {
        if (type == 1) {
            return airDataPointList100;
        } else if (type == 2) {
            return airDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getWaterDataPointList(int type) {
        if (type == 1) {
            return waterDataPointList100;
        } else if (type == 2) {
            return waterDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getFireDataPointList(int type) {
        if (type == 1) {
            return fireDataPointList100;
        } else if (type == 2) {
            return fireDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getBmsClusterDataPointList(String vendor) {
        if (vendor.equalsIgnoreCase(VendorTypeConstants.WEIHENG_EMS_100)) {
            return bmsClusterDataPointList100;
        } else if (vendor.equalsIgnoreCase(VendorTypeConstants.WEIHENG_EMS_200)) {
            return bmsClusterDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + vendor);
        }
    }

    public List<PointDataEntity> getBmsClusterDataPointList(int type) {
        if (type == 1) {
            return bmsClusterDataPointList100;
        } else if (type == 2) {
            return bmsClusterDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getBmsCommonDataList(String vendor) {
        if (vendor.equals(VendorTypeConstants.WEIHENG_EMS_100)) {
            return bmsCommonDataList100;
        } else if (vendor.equals(VendorTypeConstants.WEIHENG_EMS_200)) {
            return bmsCommonDataList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + vendor);
        }
    }

    public List<PointDataEntity> getBmsCommonDataList(int type) {
        if (type == 1) {
            return bmsCommonDataList100;
        } else if (type == 2) {
            return bmsCommonDataList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getPcsDataPointList(String vendor) {
        if (vendor.equals(VendorTypeConstants.WEIHENG_EMS_100)) {
            return pcsDataPointList100;
        } else if (vendor.equals(VendorTypeConstants.WEIHENG_EMS_200)) {
            return pcsDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + vendor);
        }
    }

    public List<PointDataEntity> getPcsDataPointList(int type) {
        if (type == 1) {
            return pcsDataPointList100;
        } else if (type == 2) {
            return pcsDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getDcdcDataPointList(int type) {
        if (type == 1) {
            return dcdcDataPointList100;
        } else if (type == 2) {
            return dcdcDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getStatesDataPointList(int type) {
        if (type == 1) {
            return statesDataPointList100;
        } else if (type == 2) {
            return statesDataPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public Map<String, Integer> getEquipCountIndexMap(int type) {
        if (type == 1) {
            return equipCountIndexMap100;
        } else if (type == 2) {
            return equipCountIndexMap200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getPointDataEventList(int type) {
        if (type == 1) {
            return pointDataEventList100;
        } else if (type == 2) {
            return pointDataEventList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getPointDefinitionLevelT0List(int subtype) {
        if (subtype == 1) {
            return pointDefinitionLevelT0List100;
        } else if (subtype == 2) {
            return pointDefinitionLevelT0List200;
        } else {
            throw new IllegalArgumentException("Unsupported subtype: " + subtype);
        }
    }

    public List<PointDataEntity> getPointDefinitionLevelT1List(int subtype) {
        if (subtype == 1) {
            return pointDefinitionLevelT1List100;
        } else if (subtype == 2) {
            return pointDefinitionLevelT1List200;
        } else {
            throw new IllegalArgumentException("Unsupported subtype: " + subtype);
        }
    }

    public List<PointDataEntity> getPointDefinitionLevelT2List(int subtype) {
        if (subtype == 1) {
            return pointDefinitionLevelT2List100;
        } else if (subtype == 2) {
            return pointDefinitionLevelT2List200;
        } else {
            throw new IllegalArgumentException("Unsupported subtype: " + subtype);
        }
    }

    public List<PointDataEntity> getAsciiPointDataList(int subtype) {
        if (subtype == 1) {
            return asciiPointDataList100;
        } else if (subtype == 2) {
            return asciiPointDataList200;
        } else {
            throw new IllegalArgumentException("Unsupported subtype: " + subtype);
        }
    }

    public int getEmsChargePowerLimit(int type) {
        if (type == 1) {
            return emsChargePowerLimit100;
        } else if (type == 2) {
            return emsChargePowerLimit200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsDischargePowerLimit(int type) {
        if (type == 1) {
            return emsDisChargePowerLimit100;
        } else if (type == 2) {
            return emsDischargePowerLimit200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public List<PointDataEntity> getSystemBitPointList(List<PointDataEntity> list) {
        return list.stream()
                .filter(
                        point ->
                                point.getPointColumn().startsWith("system_")
                                        && point.getPointType().equals("BIT"))
                .collect(Collectors.toList());
    }

    public List<PointDataEntity> initBitPointList(List<PointDataEntity> list, String column) {
        return list.stream()
                .filter(
                        point ->
                                point.getPointOffset().equals(column)
                                        && point.getPointType().equals("BIT"))
                .collect(Collectors.toList());
    }

    public List<PointDataEntity> getBmsCommonBitList(List<PointDataEntity> list) {
        return list.stream()
                .filter(
                        point ->
                                point.getPointColumn().startsWith("bms_")
                                        && point.getPointType().equals("BIT")
                                        && !point.getPointColumn().contains("_{i}"))
                .collect(Collectors.toList());
    }

    public List<PointDataEntity> getSystemDataPointList(List<PointDataEntity> list) {
        return list.stream()
                .filter(
                        point ->
                                !point.getPointType().equals("BIT")
                                        && point.getPointColumn().startsWith("system_"))
                .collect(Collectors.toList());
    }

    public List<PointDataEntity> getBmsCommonDataList(List<PointDataEntity> list) {
        return list.stream()
                .filter(
                        point ->
                                point.getPointColumn().startsWith("bms_")
                                        && !point.getPointColumn().contains("_{i}")
                                        && !point.getPointType().equals("BIT"))
                .collect(Collectors.toList());
    }

    public List<PointDataEntity> initDataPointList(List<PointDataEntity> list, String column) {
        return list.stream()
                .filter(
                        point ->
                                point.getPointOffset().equals(column)
                                        && !point.getPointType().equals("BIT"))
                .collect(Collectors.toList());
    }

    public int getEmsSerial(int type) {
        if (type == 1) {
            return emsSerial100;
        } else if (type == 2) {
            return emsSerial200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsDesignChargePower(int type) {
        if (type == 1) {
            return emsDesignChargePower100;
        } else if (type == 2) {
            return emsDesignChargePower200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsDesignDischargePower(int type) {
        if (type == 1) {
            return emsDesignDischargePower100;
        } else if (type == 2) {
            return emsDesignDischargePower200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsCapacity(int type) {
        if (type == 1) {
            return emsCapacity100;
        } else if (type == 2) {
            return emsCapacity200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsDesignPower(int type) {
        if (type == 1) {
            return emsDesignPower100;
        } else if (type == 2) {
            return emsDesignPower200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsHistoryOutputEnergy(int type) {
        if (type == 1) {
            return emsHistoryOutputEnergy100;
        } else if (type == 2) {
            return emsHistoryOutputEnergy200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsHistoryInputEnergy(int type) {
        if (type == 1) {
            return emsHistoryInputEnergy100;
        } else if (type == 2) {
            return emsHistoryInputEnergy200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsAcActivePower(int type) {
        if (type == 1) {
            return emsAcActivePower100;
        } else if (type == 2) {
            return emsAcActivePower200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsAcReactivePower(int type) {
        if (type == 1) {
            return emsAcReactivePower100;
        } else if (type == 2) {
            return emsAcReactivePower200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getGpsOnline(int type) {
        if (type == 1) {
            return gpsOnline100;
        } else if (type == 2) {
            return gpsOnline200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getGpsLatitude(int type) {
        if (type == 1) {
            return gpsLatitude100;
        } else if (type == 2) {
            return gpsLatitude200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getGpsLongitude(int type) {
        if (type == 1) {
            return gpsLongitude100;
        } else if (type == 2) {
            return gpsLongitude200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getSoc(int type) {
        if (type == 1) {
            return soc100;
        } else if (type == 2) {
            return soc200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getSoh(int type) {
        if (type == 1) {
            return soh100;
        } else if (type == 2) {
            return soh200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getDcdcMeterVoltage(int type) {
        if (type == 1) {
            return dcdcMeterVoltage100;
        } else if (type == 2) {
            return dcdcMeterVoltage200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getDcdcMeterCurrent(int type) {
        if (type == 1) {
            return dcdcMeterCurrent100;
        } else if (type == 2) {
            return dcdcMeterCurrent200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getDcdcMeterPower(int type) {
        if (type == 1) {
            return dcdcMeterPower100;
        } else if (type == 2) {
            return dcdcMeterPower200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getDcdcMeterHistoryEnergyPos(int type) {
        if (type == 1) {
            return dcdcMeterHistoryEnergyPos100;
        } else if (type == 2) {
            return dcdcMeterHistoryEnergyPos200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getDcdcMeterHistoryEnergyEng(int type) {
        if (type == 1) {
            return dcdcMeterHistoryEnergyEng100;
        } else if (type == 2) {
            return dcdcMeterHistoryEnergyEng200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getBmsDischargeableEnergy(int type) {
        if (type == 1) {
            return bmsDischargeableEnergy100;
        } else if (type == 2) {
            return bmsDischargeableEnergy200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getDirectPowerControlCounter(int type) {
        if (type == 1) {
            return directPowerControlCounter100;
        } else if (type == 2) {
            return directPowerControlCounter200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getSystemRunStatus(int type) {
        if (type == 1) {
            return systemRunStatus100;
        } else if (type == 2) {
            return systemRunStatus200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsState(int type) {
        if (type == 1) {
            return emsState100;
        } else if (type == 2) {
            return emsState200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsSerialLength(int type) {
        if (type == 1) {
            return emsSerialLength100;
        } else if (type == 2) {
            return emsSerialLength200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getBmsNum(int type) {
        if (type == 1) {
            return bmsNum100;
        } else if (type == 2) {
            return bmsNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getBmsClusterNum(int type) {
        if (type == 1) {
            return bmsClusterNum100;
        } else if (type == 2) {
            return bmsClusterNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getPcsNum(int type) {
        if (type == 1) {
            return pcsNum100;
        } else if (type == 2) {
            return pcsNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getAirNum(int type) {
        if (type == 1) {
            return airNum100;
        } else if (type == 2) {
            return airNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getFireNum(int type) {
        if (type == 1) {
            return fireNum100;
        } else if (type == 2) {
            return fireNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getWaterNum(int type) {
        if (type == 1) {
            return waterNum100;
        } else if (type == 2) {
            return waterNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getDcDcNum(int type) {
        if (type == 1) {
            return dcdcNum100;
        } else if (type == 2) {
            return dcdcNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getStatesNum(int type) {
        if (type == 1) {
            return statesNum100;
        } else if (type == 2) {
            return statesNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getBmsStackNum(int type) {
        if (type == 1) {
            return bmsStackNum100;
        } else if (type == 2) {
            return bmsStackNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getBmsCellNum(int type) {
        if (type == 1) {
            return bmsCellNum100;
        } else if (type == 2) {
            return bmsCellNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getBmsCellTNum(int type) {
        if (type == 1) {
            return bmsCellTNum100;
        } else if (type == 2) {
            return bmsCellTNum200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getSystemOffGrid(int type) {
        if (type == 1) {
            return systemOffGrid100;
        } else if (type == 2) {
            return systemOffGrid200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getEmsTypeIndex() {
        return 42;
    }

    public int getCellInitIndex(int emsType) {
        if (emsType == 1) {
            return 4000;
        } else if (emsType == 2) {
            return 20000;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + emsType);
        }
    }

    public int getBmsChargeableEnergy(int type) {
        if (type == 1) {
            return bmsChargeableEnergy100;
        } else if (type == 2) {
            return bmsChargeableEnergy200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    public int getTypeCodeIndex(int emsType, String column, Integer index) {
        if (column.contains(TypeCodeIndexEnum.SYSTEM.getCode())) {
            if (emsType == 1) {
                return 0;
            } else if (emsType == 2) {
                return 100;
            }
        }
        if (column.contains(TypeCodeIndexEnum.PCS.getCode())) {
            if (emsType == 1) {
                return 2;
            } else if (emsType == 2) {
                // 500+PCSi*150
                return 500 + 150 * index;
            }
        } else if (column.contains(TypeCodeIndexEnum.BMS.getCode())) {
            if (emsType == 1) {
                return 4;
            } else if (emsType == 2) {
                return 6500;
            }
        } else if (column.contains(TypeCodeIndexEnum.AIR.getCode())) {
            if (emsType == 1) {
                return 6;
            } else if (emsType == 2) {
                // 12000+AIRi*100
                return 12000 + 100 * index;
            }
        } else if (column.contains(TypeCodeIndexEnum.FIRE.getCode())) {
            if (emsType == 1) {
                return 8;
            } else if (emsType == 2) {
                // 16000+FIREi*100
                return 16000 + 100 * index;
            }
        } else if (column.contains(TypeCodeIndexEnum.WATER_COOLER.getCode())) {
            if (emsType == 1) {
                return 15;
            } else if (emsType == 2) {
                // 14000+WATERi*100
                return 14000 + 100 * index;
            }
        } else if (column.contains(TypeCodeIndexEnum.DCDC.getCode())) {
            if (emsType == 1) {
                return 20001;
            } else if (emsType == 2) {
                // 3500+DCDCi*150
                return 3500 + 150 * index;
            }
        }
        if (column.contains(TypeCodeIndexEnum.STATES.getCode())) {
            if (emsType == 1) {
                // 21100+STATESi*50
                return 21100 + index * 50;
            } else if (emsType == 2) {
                // 18000+STATESi*100
                return 18000 + index * 100;
            }
        }
        throw new IllegalArgumentException("Unsupported type: " + column);
    }

    public List<PointDataEntity> getFixedPointList(String vendor) {
        if (vendor.equals(VendorTypeConstants.WEIHENG_EMS_100)) {
            return fixedPointList100;
        } else if (vendor.equals(VendorTypeConstants.WEIHENG_EMS_200)) {
            return fixedPointList200;
        } else {
            throw new IllegalArgumentException("Unsupported type: " + vendor);
        }
    }
}
