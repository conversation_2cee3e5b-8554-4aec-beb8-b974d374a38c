package com.wifochina.modules.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;

import lombok.Getter;
import lombok.Setter;

/**
 * 点位数据对象
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Getter
@Setter
@TableName("t_point_data_200")
@ApiModel(value = "PointData200Entity对象", description = "点位数据对象")
public class PointDataEntity200 extends PointDataEntity {}
