package com.wifochina.modules.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 点位数据对象
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_point_data")
@ApiModel(value = "PointDataEntity对象", description = "点位数据对象")
public class PointDataEntity100 extends PointDataEntity {}
