package com.wifochina.modules.data.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-18 10:16 AM
 */
@Data
public class McInfo {

    private Double high_side_voltage;

    private Double high_side_current;

    private Double high_side_active_power;

    private Double high_side_reactive_power;

    private List<Double> high_side_voltages;

    private List<Double> high_side_line_voltages;

    private List<Double> high_side_active_powers;

    private List<Double> high_side_reactive_powers;

    private Double high_side_history_positive_power_in_kwh;

    private Double high_side_history_negative_power_in_kwh;

    private Double high_side_frequency;

    private List<Double> high_side_protect_currents;

    private Double high_side_protect_current;

    private Double high_side_zero_sequence_protect_current;

    private Double low_side_voltage;

    private Double low_side_current;

    private Double low_side_active_power;

    private Double low_side_reactive_power;

    private List<Double> low_side_voltages;

    private List<Double> low_side_currents;

    private List<Double> low_side_line_voltages;

    private List<Double> low_side_active_powers;

    private List<Double> low_side_reactive_powers;

    private Double low_side_history_positive_power_in_kwh;

    private Double low_side_history_negative_power_in_kwh;

    private Double low_side_frequency;
}
