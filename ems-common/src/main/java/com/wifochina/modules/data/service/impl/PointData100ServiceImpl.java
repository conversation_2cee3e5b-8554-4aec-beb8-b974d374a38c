package com.wifochina.modules.data.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.data.entity.PointDataEntity100;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.mapper.PointData100Mapper;
import com.wifochina.modules.data.service.PointDataService100;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PointData100ServiceImpl extends ServiceImpl<PointData100Mapper, PointDataEntity100>
        implements PointDataService100 {
    @Override
    public List<PointDataEntity> getSystemBitPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointType, "BIT")
                .like(PointDataEntity::getPointColumn, "system_")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getAirBitPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.AIR_COLUMN)
                .eq(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getWaterBitPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.WATER_COLUMN)
                .eq(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getFireBitPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.FIRE_COLUMN)
                .eq(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getBmsClusterBitPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.CLUSTER_COLUMN)
                .eq(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getBmsCommonBitList() {
        return this.lambdaQuery()
                .like(PointDataEntity::getPointColumn, "bms_")
                .eq(PointDataEntity::getPointType, "BIT")
                .notLike(PointDataEntity::getPointColumn, "_{i}")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getPcsBitPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.PCS_COLUMN)
                .eq(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getDcdcBitPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.DCDC_COLUMN)
                .eq(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getStatesBitPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.STATES_COLUMN)
                .eq(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getSystemDataPointList() {
        return this.lambdaQuery()
                .ne(PointDataEntity::getPointType, "BIT")
                .like(PointDataEntity::getPointColumn, "system_")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getAirDataPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.AIR_COLUMN)
                .ne(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getWaterDataPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.WATER_COLUMN)
                .eq(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getFireDataPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.FIRE_COLUMN)
                .ne(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getBmsClusterDataPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.CLUSTER_COLUMN)
                .ne(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getBmsCommonDataList() {
        return this.lambdaQuery()
                .like(PointDataEntity::getPointColumn, "bms_")
                .ne(PointDataEntity::getPointType, "BIT")
                .notLike(PointDataEntity::getPointColumn, "_{i}")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getPcsDataPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.PCS_COLUMN)
                .ne(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getDcdcDataPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.DCDC_COLUMN)
                .ne(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public List<PointDataEntity> getStatesDataPointList() {
        return this.lambdaQuery()
                .eq(PointDataEntity::getPointOffset, EmsConstants.DCDC_COLUMN)
                .ne(PointDataEntity::getPointType, "BIT")
                .list()
                .stream()
                .map(PointDataEntity.class::cast)
                .collect(Collectors.toList());
    }
}
