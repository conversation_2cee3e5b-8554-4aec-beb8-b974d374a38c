package com.wifochina.modules.data.listener;

import com.wifochina.modules.event.entity.HideEventCodeEntity;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * @since 3/30/2022 4:34 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString
public class StateChange {
    /** 数据状态发生变化后的位运算结果 */
    private String binaryString;

    /** 地址 */
    private int address;

    /** 状态当前值 */
    private int value;

    /** 状态旧值 */
    private int lastValue;

    /** 所属设备 */
    private String deviceId;

    /** 所属设备名称 */
    private String deviceName;

    /** 装备名称 */
    private String equipName;

    /** 事件column值 */
    private String column;

    /** 设备索引，系统默认为null，不设值 */
    private Integer equipIndex;

    /** 设备类型code */
    private Integer typeCode;

    /** 设备是否维护 */
    private Boolean maintain;

    private List<String> hideEventCodes = new ArrayList<>();
}
