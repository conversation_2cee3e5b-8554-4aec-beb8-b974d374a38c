package com.wifochina.modules.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * @since 2024-03-14 7:50 PM
 * <AUTHOR>
 */
@Data
public class DebugInfo {

    @JsonProperty("DesignPower")
    private Double designPower;

    @JsonProperty("CurrentPower")
    private Double currentPower;

    @JsonProperty("GroupId")
    private String groupId;

    @JsonProperty("groupPriority")
    private Integer groupPriority;

    // 限制功率不需要根据分组优先级，直接取限制的最低者或最高者，肯定能达到所有效果
    @JsonProperty("LimitPower")
    private Double limitPower;
}
