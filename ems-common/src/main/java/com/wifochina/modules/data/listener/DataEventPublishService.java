package com.wifochina.modules.data.listener;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

/**
 * MessageService
 *
 * @since 3/30/2022 5:12 PM
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class DataEventPublishService implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext)
            throws BeansException {
        this.applicationContext = applicationContext;
    }

    public void publishStateChangeEvent(StateChange stateChange) {
        applicationContext.publishEvent(stateChange);
    }

    public void publishStateChangeEvent(MeterStateChange meterStateChange) {
        applicationContext.publishEvent(meterStateChange);
    }

    public void publishStateChangeEvent(ControllableStateChange controllableStateChange) {
        applicationContext.publishEvent(controllableStateChange);
    }
}
