package com.wifochina.modules.data.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wifochina.common.constants.DeviceOnlineStatusEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.util.ConversionUtil;
import com.wifochina.common.util.RestGoUtils;
import com.wifochina.modules.data.entity.*;
import com.wifochina.modules.data.service.PointDataService100;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * DataUtil
 *
 * <AUTHOR>
 * @version 1.0
 * @since 7/25/2022 9:27 AM
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataService {

    @Value("${ems.gateway}")
    private String gatewayUrl;

    private final DeviceService deviceService;

    private final RestTemplate restTemplate;

    private final ProjectService projectService;

    private final ControllerService controllerService;

    private final PointDataService100 pointDataService;

    private final RestGoUtils restGoUtils;

    /** 设备数目所在索引集合，以OffsetTypeEnum中CLUSTER，PCS，AiR，FIRE，STACK，CELL作为key进行存储。 */
    private static final Map<String, MeterDataCache> METER_DATA_CACHE_MAP = new HashMap<>();

    private static final Map<String, SteerableDataCache> STEERABLE_DATA_CACHE_MAP = new HashMap<>();

    private final Map<String, String> deviceOffStatusMap = new ConcurrentHashMap<>();
    private final Map<String, String> meterOffStatusMap = new ConcurrentHashMap<>();
    private final Map<String, String> steerableOffStatusMap = new ConcurrentHashMap<>();
    public static final Map<String, EmsDataCache> EMS_DATA_CACHE_MAP = new HashMap<>();

    public int[] get(String deviceId) {
        return get(deviceId, WebUtils.projectId.get());
    }

    public int[] get(String deviceId, String projectId) {
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        ProjectEntity projectEntity = projectService.getById(projectId);
        WebUtils.projectId.set(projectId);
        // 实时数据
        int[] data = null;
        EmsDataCache emsDataCache = EMS_DATA_CACHE_MAP.get(deviceId);
        if (emsDataCache != null) {
            long now = Instant.now().getEpochSecond();
            int updatePeriod = 5;
            long updateTime =
                    emsDataCache.getUpdateTime() == null ? 0 : (long) emsDataCache.getUpdateTime();
            long diff = now - updateTime;
            if (diff < updatePeriod) {
                if (emsDataCache.getData().length > 0) {
                    return emsDataCache.getData();
                } else {
                    return null;
                }
            }
        }
        try {
            JSONObject jsonObject;
            if (projectEntity.getProjectModel() == 1) {
                // 场站版状态 多个设备离线可能导致反复查 从而导致接口慢
                ControllerEntity controllerEntity =
                        controllerService
                                .lambdaQuery()
                                .eq(ControllerEntity::getProjectId, projectId)
                                .one();
                String url =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort()
                                + "/api/v1/get_all_ems_modbus";
                jsonObject = restTemplate.postForObject(url, null, JSONObject.class);
            } else {
                String url = gatewayUrl + "/api/v1/get_ems_modbus";
                jsonObject = restTemplate.getForObject(url, JSONObject.class);
            }
            assert jsonObject != null;
            List<DeviceEntity> deviceEntityListlist =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getUnreal, false)
                            .eq(DeviceEntity::getProjectId, projectId)
                            .list();
            Long time = Instant.now().getEpochSecond();
            for (DeviceEntity device : deviceEntityListlist) {
                List<Integer> dataList =
                        ConversionUtil.objToList(
                                jsonObject.getJSONObject(device.getId()).get("data"),
                                Integer.class);
                int[] dataTemp = dataList.stream().mapToInt(Integer::valueOf).toArray();
                EmsDataCache emsDataCacheTemp = new EmsDataCache();
                emsDataCacheTemp.setData(dataTemp);
                emsDataCacheTemp.setUpdateTime(time);
                EMS_DATA_CACHE_MAP.put(device.getId(), emsDataCacheTemp);
            }
            data = EMS_DATA_CACHE_MAP.get(deviceId).getData();
            if (DeviceOnlineStatusEnum.OFF.getValue().equals(deviceOffStatusMap.get(deviceId))) {
                log.error(
                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value()
                                + "----> "
                                + projectEntity.getProjectName()
                                + ": name="
                                + deviceEntity.getName()
                                + " ;id="
                                + deviceId
                                + " online");
            }
            deviceOffStatusMap.put(deviceId, DeviceOnlineStatusEnum.ON.getValue());
        } catch (Exception e) {
            // 有异常证明数据采集不成功，则不处理
            if (DeviceOnlineStatusEnum.ON.getValue().equals(deviceOffStatusMap.get(deviceId))) {
                log.error(
                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value()
                                + "---->"
                                + projectEntity.getProjectName()
                                + ": name:="
                                + deviceEntity.getName()
                                + " ;id="
                                + deviceId
                                + " offline");
            }
            deviceOffStatusMap.put(deviceId, DeviceOnlineStatusEnum.OFF.getValue());
        }
        if (data != null && data.length == 0) {
            data = null;
        }
        return data;
    }

    public Map<String, MeterContentData> collectMeterDataPoint(String projectId) {
        MeterDataCache meterDataCache = METER_DATA_CACHE_MAP.get(projectId);
        if (meterDataCache != null) {
            long now = Instant.now().getEpochSecond();
            int updatePeriod = 5;
            long updateTime =
                    meterDataCache.getUpdateTime() == null
                            ? 0
                            : (long) meterDataCache.getUpdateTime();
            long diff = now - updateTime;
            if (diff < updatePeriod) {
                return meterDataCache.getData();
            }
        }
        ProjectEntity projectEntity = projectService.getById(projectId);
        try {
            WebUtils.projectId.set(projectId);
            JSONObject jsonObject =
                    restGoUtils.sendPost(() -> "/api/v1/get_meter_information", JSONObject.class);
            assert jsonObject != null;
            String arrayData = JSON.toJSONString(jsonObject.get("meters"));
            List<MeterData> list = JSON.parseArray(arrayData, MeterData.class);
            Map<String, MeterContentData> meterContentDataMap =
                    new HashMap<>(list == null ? 1 : list.size());
            Optional.ofNullable(list)
                    .ifPresent(
                            e -> {
                                for (MeterData meterData : list) {
                                    meterContentDataMap.put(
                                            meterData.getUuid(), meterData.getInformation());
                                }
                                MeterDataCache meterDataCacheTmp = new MeterDataCache();
                                meterDataCacheTmp.setData(meterContentDataMap);
                                meterDataCacheTmp.setUpdateTime(Instant.now().getEpochSecond());
                                METER_DATA_CACHE_MAP.put(projectId, meterDataCacheTmp);
                            });
            if (DeviceOnlineStatusEnum.OFF.getValue().equals(meterOffStatusMap.get(projectId))) {
                log.error(
                        "collectMeterDataPoint#--->{},{}/api/v1/get_meter_information ;name:={} ;id={} online",
                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value(),
                        gatewayUrl,
                        projectEntity.getProjectName(),
                        projectEntity.getId());
            }
            meterOffStatusMap.put(projectId, DeviceOnlineStatusEnum.ON.getValue());
            return meterContentDataMap;
        } catch (Exception e) {
            // 有异常证明数据采集不成功，则不处理
            if (DeviceOnlineStatusEnum.ON.getValue().equals(meterOffStatusMap.get(projectId))) {
                log.error(
                        "collectMeterDataPoint#--->{},{}/api/v1/get_meter_information ;name:={} ;id={} offline",
                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value(),
                        gatewayUrl,
                        projectEntity.getProjectName(),
                        projectEntity.getId());
            }
            meterOffStatusMap.put(projectId, DeviceOnlineStatusEnum.OFF.getValue());
            return new HashMap<>(0);
        }
    }

    public Map<String, SteerableContentData> collectSteerableDataPoint(String projectId) {
        SteerableDataCache steerableDataCache = STEERABLE_DATA_CACHE_MAP.get(projectId);
        if (steerableDataCache != null) {
            long now = Instant.now().getEpochSecond();
            int updatePeriod = 5;
            long updateTime =
                    steerableDataCache.getUpdateTime() == null
                            ? 0
                            : (long) steerableDataCache.getUpdateTime();
            long diff = now - updateTime;
            if (diff < updatePeriod) {
                return steerableDataCache.getData();
            }
        }
        ProjectEntity projectEntity = projectService.getById(projectId);
        try {
            WebUtils.projectId.set(projectId);
            JSONObject jsonObject =
                    restGoUtils.sendPost(
                            () -> "/api/v1/get_controllable_information", JSONObject.class);
            assert jsonObject != null;
            String arrayData = JSON.toJSONString(jsonObject.get("controllables"));
            List<SteerableData> list = JSON.parseArray(arrayData, SteerableData.class);
            Map<String, SteerableContentData> steerableContentDataMap =
                    new HashMap<>(list == null ? 1 : list.size());
            Optional.ofNullable(list)
                    .ifPresent(
                            (e) -> {
                                for (SteerableData steerableData : list) {
                                    steerableContentDataMap.put(
                                            steerableData.getUuid(),
                                            steerableData.getInformation());
                                }
                                SteerableDataCache steerableDataCacheTemp =
                                        new SteerableDataCache();
                                steerableDataCacheTemp.setData(steerableContentDataMap);
                                steerableDataCacheTemp.setUpdateTime(
                                        Instant.now().getEpochSecond());
                                STEERABLE_DATA_CACHE_MAP.put(projectId, steerableDataCacheTemp);
                            });
            if (DeviceOnlineStatusEnum.OFF
                    .getValue()
                    .equals(steerableOffStatusMap.get(projectId))) {
                log.error(
                        "collectSteerableDataPoint#--->{},{}/api/v1/get_controllable_information ;name:={} ;id={} online",
                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value(),
                        gatewayUrl,
                        projectEntity.getProjectName(),
                        projectEntity.getId());
            }
            steerableOffStatusMap.put(projectId, DeviceOnlineStatusEnum.ON.getValue());
            return steerableContentDataMap;
        } catch (Exception e) {
            // 有异常证明数据采集不成功，则不处理
            if (DeviceOnlineStatusEnum.ON.getValue().equals(steerableOffStatusMap.get(projectId))) {
                log.error(
                        "collectSteerableDataPoint#--->{},{}/api/v1/get_meter_information ;name:={} ;id={} offline",
                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value(),
                        gatewayUrl,
                        projectEntity.getProjectName(),
                        projectEntity.getId());
            }
            steerableOffStatusMap.put(projectId, DeviceOnlineStatusEnum.OFF.getValue());
            return new HashMap<>(0);
        }
    }
}
