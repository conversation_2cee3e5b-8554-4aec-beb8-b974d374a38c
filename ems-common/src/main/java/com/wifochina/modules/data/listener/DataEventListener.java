package com.wifochina.modules.data.listener;

import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.service.EmsEventCodeService;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.event.service.MeterEventCodeService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * EventListener
 *
 * <AUTHOR>
 * @version 1.0
 * @since 3/30/2022 4:29 PM
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataEventListener {
    private final EmsEventCodeService eventCodeService;
    private final EventMessageService eventMessageService;
    private final MeterEventCodeService meterEventCodeService;

    @Value("${ems.collect.project.id}")
    private String projectId;

    /** 事件的集合，以point_column作为key进行存储。 */
    public static Map<String, Map<Integer, EventCodeEntity>> emsEventCodeMap = new HashMap<>();

    protected Map<String, MeterEventCodeEntity> meterEventCodeMap;

    private Map<Integer, List<EventCodeEntity>> controllableEventCodeMap;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        List<EventCodeEntity> eventCodeEntityList =
                eventCodeService
                        .lambdaQuery()
                        .like(EventCodeEntity::getPointColumn, "controllable")
                        .list();
        this.controllableEventCodeMap =
                eventCodeEntityList.stream()
                        .collect(Collectors.groupingBy(EventCodeEntity::getDeviceTypeCode));
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initPointDataEventMap() {
        List<EventCodeEntity> eventCodeEntityList = eventCodeService.list();
        // 将对象按照column分组
        Map<String, List<EventCodeEntity>> codeMap =
                eventCodeEntityList.stream()
                        .collect(
                                Collectors.groupingBy(
                                        item ->
                                                item.getDeviceTypeCode()
                                                        + "_"
                                                        + item.getPointColumn()));

        for (Map.Entry<String, List<EventCodeEntity>> entry : codeMap.entrySet()) {
            List<EventCodeEntity> list = entry.getValue();
            Map<Integer, EventCodeEntity> map =
                    list.stream()
                            .collect(
                                    Collectors.toMap(
                                            EventCodeEntity::getBitValue,
                                            eventCodeEntity -> eventCodeEntity));
            emsEventCodeMap.put(entry.getKey(), map);
        }
        List<MeterEventCodeEntity> meterEventCodeEntities =
                meterEventCodeService
                        .lambdaQuery()
                        .eq(MeterEventCodeEntity::getDigital0Analog1Control2, 0)
                        .list();

        meterEventCodeMap =
                meterEventCodeEntities.stream()
                        .collect(
                                Collectors.toMap(
                                        e ->
                                                e.getProjectId()
                                                        + "_"
                                                        + e.getMeterType()
                                                        + "_"
                                                        + e.getBitOffset()
                                                        + "_"
                                                        + e.getBitValue(),
                                        code -> code));
    }

    @EventListener
    @Async
    public void handleEvent(StateChange stateChange) {
        log.info(stateChange.toString());
        List<String> hideEventCodes = stateChange.getHideEventCodes();

        // 具体的某个事件
        Map<Integer, EventCodeEntity> eventBitMap =
                emsEventCodeMap.get(stateChange.getTypeCode() + "_" + stateChange.getColumn());
        // i=15 代表16位数据最大位
        for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
            // 事件对象
            EventMessageEntity eventMessageEntity = new EventMessageEntity();
            // 设备id
            eventMessageEntity.setDeviceId(stateChange.getDeviceId());
            // 装备名称
            eventMessageEntity.setEquipName(stateChange.getEquipName());
            // 是否维护
            eventMessageEntity.setMaintain(stateChange.getMaintain());
            if ((stateChange.getValue() & (1 << i)) != (stateChange.getLastValue() & (1 << i))) {
                int finalI = i;
                Optional.ofNullable(eventBitMap)
                        .ifPresent(
                                e -> {
                                    EventCodeEntity eventCodeEntity = eventBitMap.get(finalI);
                                    Optional.ofNullable(eventCodeEntity)
                                            .ifPresent(
                                                    a -> {
                                                        // 事件描述 Fault、State、Alarm
                                                        // (stateChange.getValue()&(1<<i))==0?0:1)
                                                        // 如果位运算结果不等于0，证明改位为1
                                                        if (eventCodeEntity.getBitStand()
                                                                == ((stateChange.getValue()
                                                                                        & (1
                                                                                                << finalI))
                                                                                == 0
                                                                        ? 0
                                                                        : 1)) {
                                                            eventMessageEntity.setEventOnOff("on");
                                                            if (EventLevelEnum.FAULT
                                                                            .getLevel()
                                                                            .equals(
                                                                                    eventCodeEntity
                                                                                            .getEventLevel())
                                                                    || EventLevelEnum.ALARM
                                                                            .getLevel()
                                                                            .equals(
                                                                                    eventCodeEntity
                                                                                            .getEventLevel())) {
                                                                // 如果发生了警告和错误，让用户确认
                                                                eventMessageEntity.setStatus(0);
                                                            }
                                                        } else {
                                                            eventMessageEntity.setEventOnOff("off");
                                                        }
                                                        eventMessageEntity.setEventDescription(
                                                                eventCodeEntity
                                                                        .getEventDescription()
                                                                        .trim());
                                                        eventMessageEntity.setEventDescriptionEn(
                                                                eventCodeEntity
                                                                                        .getEventDescriptionEn()
                                                                                == null
                                                                        ? ""
                                                                        : eventCodeEntity
                                                                                .getEventDescriptionEn()
                                                                                .trim());
                                                        // 事件编码=偏移量+bit码
                                                        eventMessageEntity.setEventCode(
                                                                eventCodeEntity.getBitOffset()
                                                                        + eventCodeEntity
                                                                                .getBitValue());
                                                        // 装备类型：派能高压电池箱、汇川630kW PCS
                                                        eventMessageEntity.setEquipType(
                                                                eventCodeEntity.getDeviceType());
                                                        // 事件类型State、Fault、Alarm
                                                        eventMessageEntity.setEventType(
                                                                eventCodeEntity.getEventLevel());
                                                        eventMessageEntity.setProjectId(projectId);
                                                        long time = Instant.now().toEpochMilli();
                                                        eventMessageEntity.setEventKey(
                                                                eventCodeEntity.getEventCode());
                                                        eventMessageEntity.setCreateTime(time);
                                                        eventMessageEntity.setUpdateTime(time);
                                                        // 保存事件数据
                                                        // 这里没有关于 hide event的逻辑代码 后续可能要设置
                                                        // 2025-05-14 14:23:13 added  设置 whetherHide
                                                        eventMessageEntity.setWhetherHide(
                                                                hideEventCodes.contains(
                                                                        eventCodeEntity
                                                                                .getEventCode()));

                                                        eventMessageService.save(
                                                                eventMessageEntity);
                                                    });
                                });
            }
        }
    }

    @EventListener
    @Async
    public void handleMeterEvent(MeterStateChange meterStateChange) {
        log.info(meterStateChange.toString());
        List<String> hideMeterEventCodes = meterStateChange.getHideEventCodes();
        // i=15 代表16位数据最大位
        for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
            // 事件对象
            EventMessageEntity eventMessageEntity = new EventMessageEntity();
            // 设备id
            eventMessageEntity.setDeviceId(meterStateChange.getMeterId());
            // 装备名称
            eventMessageEntity.setEquipName("METER");
            // 是否维护
            eventMessageEntity.setMaintain(meterStateChange.getMaintain());
            if ((meterStateChange.getValue() & (1 << i))
                    != (meterStateChange.getLastValue() & (1 << i))) {
                MeterEventCodeEntity meterEventCodeEntity =
                        meterEventCodeMap.get(
                                meterStateChange.getProjectId()
                                        + "_"
                                        + meterStateChange.getMeterType()
                                        + "_"
                                        + meterStateChange.getIndex()
                                        + "_"
                                        + i);
                int finalI = i;
                Optional.ofNullable(meterEventCodeEntity)
                        .ifPresent(
                                meterCodeEntity -> {
                                    // 事件描述 Fault、State、Alarm
                                    // (stateChange.getValue()&(1<<i))==0?0:1) 如果位运算结果不等于0，证明改位为1
                                    if ((meterStateChange.getValue() & (1 << finalI)) > 0) {
                                        eventMessageEntity.setEventOnOff("on");
                                        if (EventLevelEnum.FAULT
                                                        .getLevel()
                                                        .equals(meterCodeEntity.getEventLevel())
                                                || EventLevelEnum.ALARM
                                                        .getLevel()
                                                        .equals(meterCodeEntity.getEventLevel())) {
                                            // 如果发生了警告和错误，让用户确认
                                            eventMessageEntity.setStatus(0);
                                        }
                                    } else {
                                        eventMessageEntity.setEventOnOff("off");
                                    }
                                    eventMessageEntity.setEventDescription(
                                            meterCodeEntity.getDescription().trim());
                                    Optional.ofNullable(meterCodeEntity.getDescriptionEn())
                                            .ifPresent(
                                                    descriptionEn ->
                                                            eventMessageEntity
                                                                    .setEventDescriptionEn(
                                                                            descriptionEn.trim()));
                                    // 事件编码=偏移量+bit码
                                    eventMessageEntity.setEventCode(
                                            meterCodeEntity.getBitOffset() * 100
                                                    + meterCodeEntity.getBitValue());
                                    // 装备类型：派能高压电池箱、汇川630kW PCS
                                    eventMessageEntity.setEquipType(meterCodeEntity.getMeterType());
                                    // 事件类型State、Fault、Alarm
                                    eventMessageEntity.setEventType(
                                            meterCodeEntity.getEventLevel());
                                    eventMessageEntity.setProjectId(projectId);
                                    long time = Instant.now().toEpochMilli();
                                    eventMessageEntity.setEventKey(meterCodeEntity.getEventCode());
                                    eventMessageEntity.setCreateTime(time);
                                    eventMessageEntity.setUpdateTime(time);
                                    // 保存事件数据
                                    // 2025-05-14 14:29:13 added meter whetherHide
                                    eventMessageEntity.setWhetherHide(
                                            hideMeterEventCodes.contains(
                                                    meterCodeEntity.getEventCode()));
                                    eventMessageService.save(eventMessageEntity);
                                });
            }
        }
    }

    @EventListener
    @Async
    public void handleControllableEvent(ControllableStateChange controllableStateChange) {
        List<EventCodeEntity> eventCodeEntityList =
                controllableEventCodeMap.get(controllableStateChange.getTypeCode());
        List<String> hideEventCodes = controllableStateChange.getHideEventCodes();
        Map<Integer, Map<Integer, EventCodeEntity>> stateEventCodeMap = null;
        switch (controllableStateChange.getLevel()) {
            case "State":
                stateEventCodeMap =
                        getEventMap(
                                eventCodeEntityList, EmsConstants.CONTROLLABLE_STATE_BIT_PATTERN);
                break;
            case "Fault":
                stateEventCodeMap =
                        getEventMap(
                                eventCodeEntityList, EmsConstants.CONTROLLABLE_FAULT_BIT_PATTERN);
                break;
            case "Alarm":
                stateEventCodeMap =
                        getEventMap(
                                eventCodeEntityList, EmsConstants.CONTROLLABLE_ALARM_BIT_PATTERN);
                break;
        }
        if (stateEventCodeMap == null) {
            return;
        }
        // i=15 代表16位数据最大位
        for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
            // 事件对象
            EventMessageEntity eventMessageEntity = new EventMessageEntity();
            // 设备id
            eventMessageEntity.setDeviceId(controllableStateChange.getControllableId());
            // 装备名称
            eventMessageEntity.setEquipName("CONTROLLABLE");
            // 是否维护
            eventMessageEntity.setMaintain(controllableStateChange.getMaintain());
            if ((controllableStateChange.getValue() & (1 << i))
                    != (controllableStateChange.getLastValue() & (1 << i))) {
                EventCodeEntity eventCodeEntity =
                        stateEventCodeMap.get(controllableStateChange.getIndex()).get(i);
                int finalI = i;
                Optional.ofNullable(eventCodeEntity)
                        .ifPresent(
                                eventCode -> {
                                    // 事件描述 Fault、State、Alarm
                                    // (stateChange.getValue()&(1<<i))==0?0:1) 如果位运算结果不等于0，证明改位为1
                                    if ((controllableStateChange.getValue() & (1 << finalI)) > 0) {
                                        eventMessageEntity.setEventOnOff("on");
                                        if (EventLevelEnum.FAULT
                                                        .getLevel()
                                                        .equals(eventCodeEntity.getEventLevel())
                                                || EventLevelEnum.ALARM
                                                        .getLevel()
                                                        .equals(eventCodeEntity.getEventLevel())) {
                                            // 如果发生了警告和错误，让用户确认
                                            eventMessageEntity.setStatus(0);
                                        }
                                    } else {
                                        eventMessageEntity.setEventOnOff("off");
                                    }
                                    eventMessageEntity.setEventDescription(
                                            eventCodeEntity.getEventDescription().trim());
                                    Optional.ofNullable(eventCodeEntity.getEventDescriptionEn())
                                            .ifPresent(
                                                    descriptionEn ->
                                                            eventMessageEntity
                                                                    .setEventDescriptionEn(
                                                                            descriptionEn.trim()));
                                    // 事件编码=偏移量+bit码
                                    eventMessageEntity.setEventCode(
                                            eventCodeEntity.getBitOffset() * 100
                                                    + eventCodeEntity.getBitValue());
                                    // 装备类型：派能高压电池箱、汇川630kW PCS
                                    eventMessageEntity.setEquipType(
                                            controllableStateChange.getVendor());
                                    // 事件类型State、Fault、Alarm
                                    eventMessageEntity.setEventType(
                                            eventCodeEntity.getEventLevel());
                                    eventMessageEntity.setProjectId(projectId);
                                    // 保存事件数据
                                    long time = Instant.now().toEpochMilli();
                                    eventMessageEntity.setEventKey(eventCodeEntity.getEventCode());
                                    eventMessageEntity.setCreateTime(time);
                                    eventMessageEntity.setUpdateTime(time);

                                    // 2025-05-14 15:32:16 added check whetherHide
                                    eventMessageEntity.setWhetherHide(
                                            hideEventCodes.contains(eventCode.getEventCode()));
                                    // 可控设备
                                    eventMessageService.save(eventMessageEntity);
                                });
            }
        }
    }

    private static Map<Integer, Map<Integer, EventCodeEntity>> getEventMap(
            List<EventCodeEntity> eventCodeEntityList, String s) {
        Map<Integer, Map<Integer, EventCodeEntity>> offsetValueMap = new HashMap<>();
        eventCodeEntityList.stream()
                .filter(e -> e.getPointColumn().contains(s))
                .forEach(
                        entity -> {
                            int key1 = entity.getBitOffset() / 16;
                            int key2 = entity.getBitValue();
                            if (!offsetValueMap.containsKey(key1)) {
                                offsetValueMap.put(key1, new HashMap<>());
                            }
                            offsetValueMap.get(key1).put(key2, entity);
                        });
        return offsetValueMap;
    }
}
