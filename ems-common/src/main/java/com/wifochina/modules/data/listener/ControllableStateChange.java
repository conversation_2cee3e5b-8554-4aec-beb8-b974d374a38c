package com.wifochina.modules.data.listener;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * @since 7/19/2023 4:34 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString
public class ControllableStateChange {
    /** 数据状态发生变化后的位运算结果 */
    private String binaryString;

    /** 状态当前值 */
    private int value;

    /** 状态旧值 */
    private int lastValue;

    /** 所属设备 */
    private String controllableId;

    /** 所属设备名称 */
    private String controllableName;

    /** 所属设备类型 */
    private String vendor;

    /** 设备是否维护 */
    private Boolean maintain;

    /** 项目id */
    private String projectId;

    /** code偏移量 */
    private Integer index;

    /** 时间类型 */
    private String level;

    /** 可控type code */
    private Integer typeCode;


    private List<String> hideEventCodes = new ArrayList<>();
}

