package com.wifochina.modules.data.service;

import com.wifochina.modules.data.entity.PointDataEntity200;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.data.entity.PointDataEntity;

import java.util.List;

/**
 * 点位数据对象 服务类
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
public interface PointDataService200 extends IService<PointDataEntity200> {
    List<PointDataEntity> getSystemBitPointList();

    /** 查询出空调状态位数据 */
    List<PointDataEntity> getAirBitPointList();

    /** 查询出水冷空调状态位数据 */
    List<PointDataEntity> getWaterBitPointList();

    /** 查询出消防状态位数据 */
    List<PointDataEntity> getFireBitPointList();

    /** 查询bms点位 */
    List<PointDataEntity> getBmsClusterBitPointList();


    List<PointDataEntity> getBmsCommonBitList();

    /** 查询pcs点位 */
    List<PointDataEntity> getPcsBitPointList();

    /** 查询dcdc */
    List<PointDataEntity> getDcdcBitPointList();

    /** 查询states */
    List<PointDataEntity> getStatesBitPointList();

    List<PointDataEntity> getSystemDataPointList();

    /** 查询出空调状态位数据 */
    List<PointDataEntity> getAirDataPointList();

    /** 查询出水冷空调状态位数据 */
    List<PointDataEntity> getWaterDataPointList();

    /** 查询出消防状态位数据 */
    List<PointDataEntity> getFireDataPointList();

    /** 查询bms点位 */
    List<PointDataEntity> getBmsClusterDataPointList();

    List<PointDataEntity> getBmsCommonDataList();

    /** 查询pcs点位 */
    List<PointDataEntity> getPcsDataPointList();

    /** 查询dcdc */
    List<PointDataEntity> getDcdcDataPointList();

    /** 查询states */
    List<PointDataEntity> getStatesDataPointList();
}
