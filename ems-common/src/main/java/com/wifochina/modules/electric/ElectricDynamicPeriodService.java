package com.wifochina.modules.electric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.common.util.TimeContext;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.group.entity.IdSearchSupport;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-05-24 17:54:20
 */
public interface ElectricDynamicPeriodService extends IService<ElectricDynamicPeriodEntity> {
    List<ElectricDynamicPeriodEntity> queryElectricSumGroupByDeviceId(
            ElectricRequest electricRequest);

    List<ElectricDynamicPeriodEntity> queryElectricMonthGroupByDeviceId(
            ElectricRequest electricRequest);

    List<ElectricDynamicPeriodEntity> queryElectricYear(ElectricRequest electricRequest);

    List<ElectricDynamicPeriodEntity> queryElectricTimeSharingProfit(
            ElectricRequest electricRequest);

    void commonPeriodList(
            ElectricAdapterService.ElectricAdapterContext context,
            TimeContext timeContext,
            List<ElectricDynamicPeriodEntity> results,
            String sql,
            IdSearchSupport idEntity);
}
