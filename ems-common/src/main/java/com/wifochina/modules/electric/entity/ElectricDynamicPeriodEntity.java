package com.wifochina.modules.electric.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Created on 2024/5/23 16:42. 动态时间段的 收益实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("t_electric_dynamic_period")
@ApiModel(value = "ElectricDynamicPeriodEntity 对象")
@Accessors(chain = true)
public class ElectricDynamicPeriodEntity implements Serializable, ElectricPeriod {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电量识符id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("设备Id")
    private String deviceId;

    @ApiModelProperty("数据段结束时间")
    private Long periodStartTime;

    @ApiModelProperty("数据段结束时间")
    private Long periodEndTime;

    @ApiModelProperty("年度")
    private Integer year;

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("日")
    private Integer day;

    @ApiModelProperty("动态段充电量")
    private double chargeQuantity;

    @ApiModelProperty("动态段充电成本")
    private double chargeCost;

    @ApiModelProperty("动态段放电量")
    private double dischargeQuantity;

    @ApiModelProperty("动态段放电收益")
    private double dischargeBenefit;

    @ApiModelProperty("动态段买入价格")
    private double buyPrice;

    @ApiModelProperty("动态段卖出价格")
    private double sellPrice;

    @ApiModelProperty("总收益(减去成本的)")
    private double totalBenefit;
}
