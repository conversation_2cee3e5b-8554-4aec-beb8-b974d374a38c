package com.wifochina.modules.electric.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ElectricPriceSystemRegionsIntervalData implements Serializable {
    private String msg;

    private Integer code;

    private List<Data> data;

    @lombok.Data
    public static class Data implements Serializable {
        private String country;

        @JsonProperty("interval_seconds")
        private Integer intervalSeconds;

        private String region;

        @JsonProperty("data_source")
        private String dataSource;
    }
}
