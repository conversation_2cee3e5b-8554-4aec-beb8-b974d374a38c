package com.wifochina.modules.electric.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import com.wifochina.modules.strategytemplate.common.YearlyStrategyTransformAble;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/** 电价系统 的 实时电价数据类 */
@Data
public class ElectricPriceSystemData implements Serializable {
    private String msg;

    private Integer code;

    private List<Data> data;

    @lombok.Data
    @Accessors(chain = true)
    public static class Data implements Serializable, YearlyStrategyTransformAble {
        private String country;

        private Double average;

        private Integer std;

        private Integer refreshTimeUnix;

        @JsonProperty("Max")
        private Integer max;

        private Boolean hasMinMax;

        private String type;

        private Boolean hasStd;

        private Integer supply;

        private Integer demand;

        private Integer min;

        private Integer startTimeUnix;

        private Integer dataTimeUnix;

        private String currency;

        private String region;

        private String dataSource;

        private Integer intervalSeconds;
    }
}
