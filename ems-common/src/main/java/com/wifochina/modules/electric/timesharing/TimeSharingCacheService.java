package com.wifochina.modules.electric.timesharing;

import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

/**
 * Created on 2024/6/26 16:32.
 *
 * <AUTHOR>
 */
public interface TimeSharingCacheService {

    /**
     * 分时 缓存接口
     *
     * @param projectEntity : 项目
     * @param electricPriceEntity : 匹配到的 尖峰平谷的 的 电价配置super , 要根据这个去 把24个小时内的 对应段的 电价信息填充
     */
    void timeSharingCacheBattery(
            long timePoint, ProjectEntity projectEntity, ElectricPriceEntity electricPriceEntity);
}
