package com.wifochina.modules.electric.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.remedies.request.DataCalibrationOperatorGet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-10-11
 */
@Getter
@Setter
@TableName("t_electric")
@ApiModel(value = "ElectricEntity对象")
public class ElectricEntity implements ElectricPeriod, Serializable, DataCalibrationOperatorGet {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电量识符id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("设备Id")
    private String deviceId;

    @ApiModelProperty("数据时间")
    private Long time;

    @ApiModelProperty("年度")
    private Integer year;

    @ApiModelProperty("月份")
    private Integer month;

    @ApiModelProperty("日")
    private Integer day;

    @ApiModelProperty("峰段充电量")
    private double peakChargeQuantity;

    @ApiModelProperty("谷段充电量")
    private double vallyChargeQuantity;

    @ApiModelProperty("平段充电量")
    private double flatChargeQuantity;

    @ApiModelProperty("尖峰充电量")
    private double tipChargeQuantity;

    @ApiModelProperty("深谷充电量")
    private double deepVallyChargeQuantity;

    @ApiModelProperty("峰段成本")
    private double peakChargeCost;

    @ApiModelProperty("谷段成本")
    private double vallyChargeCost;

    @ApiModelProperty("平段成本")
    private double flatChargeCost;

    @ApiModelProperty("尖峰成本")
    private double tipChargeCost;

    @ApiModelProperty("深谷成本")
    private double deepVallyChargeCost;

    @ApiModelProperty("尖峰放电量")
    private double peakDischargeQuantity;

    @ApiModelProperty("谷段放电量")
    private double vallyDischargeQuantity;

    @ApiModelProperty("平段放电量")
    private double flatDischargeQuantity;

    @ApiModelProperty("尖峰放电量")
    private double tipDischargeQuantity;

    @ApiModelProperty("深谷放电量")
    private double deepVallyDischargeQuantity;

    @ApiModelProperty("峰段收益")
    private double peakDischargeBenefit;

    @ApiModelProperty("谷段收益")
    private double vallyDischargeBenefit;

    @ApiModelProperty("平段收益")
    private double flatDischargeBenefit;

    @ApiModelProperty("尖峰收益")
    private double tipDischargeBenefit;

    @ApiModelProperty("深谷收益")
    private double deepVallyDischargeBenefit;

    @ApiModelProperty("峰买入价格")
    private double peakBuyPrice;

    @ApiModelProperty("谷买入价格")
    private double vallyBuyPrice;

    @ApiModelProperty("平买入价格")
    private double flatBuyPrice;

    @ApiModelProperty("尖买入价格")
    private double tipBuyPrice;

    @ApiModelProperty("深谷买入价格")
    private double deepVallyBuyPrice;

    @ApiModelProperty("峰卖出价格")
    private double peakSellPrice;

    @ApiModelProperty("谷卖出价格")
    private double vallySellPrice;

    @ApiModelProperty("平卖出价格")
    private double flatSellPrice;

    @ApiModelProperty("尖卖出价格")
    private double tipSellPrice;

    @ApiModelProperty("深谷卖出价格")
    private double deepVallySellPrice;

    @ApiModelProperty("总成本")
    private double totalChargeCost;

    @ApiModelProperty("总充电量")
    private double totalChargeQuantity;

    @ApiModelProperty("总放电量")
    private double totalDischargeQuantity;

    @ApiModelProperty("总放电收益")
    private double totalDischargeBenefit;

    @ApiModelProperty("总收益")
    private double totalBenefit;

    @ApiModelProperty("校准类型，默认 1原始值  2校准后失效 3校准后新增")
    private int calibrationType;

    @ApiModelProperty("校准操作人userId")
    private String userId;

    @ApiModelProperty("校准操作人userName 数据库不存在这个字段 通过userId查询出来的")
    @TableField(exist = false)
    private String userName;

    @ApiModelProperty("分组名称userName 数据库不存在这个字段 通过groupId查询出来的")
    @TableField(exist = false)
    private String groupName;
}
