package com.wifochina.modules.electric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.request.ElectricPriceRequest;
import com.wifochina.modules.electric.request.ElectricRequest;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface ElectricService extends IService<ElectricEntity> {
    /** 按照设备进行求一段时间的合，可用于求抄表起始总数 */
    List<ElectricEntity> queryElectricSumGroupByDeviceId(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricMonthGroupByDeviceId(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricYear(ElectricRequest electricRequest);

    ElectricEntity queryElectricTotalProfit(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricEveryDayProfit(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricPrice(ElectricPriceRequest electricPriceRequest);

    List<ElectricEntity> queryElectricProfitByMonth(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricProfitByDay(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricQuantityByDay(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricQuantityByMonth(ElectricRequest electricRequest);

    ElectricEntity queryElectricByGroupId(String groupId, long time);

    Double getTotalProfitByDayAndProjectId(String projectId, int year, int month, int day);
}
