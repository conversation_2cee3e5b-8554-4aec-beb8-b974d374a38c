<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.electric.mapper.ElectricMapper">
    <resultMap id="BaseResultMap" type="com.wifochina.modules.electric.entity.ElectricEntity">
        <id column="id" property="id"/>
        <result column="projectId" property="project_id"/>
        <result column="deviceId" property="device_id"/>
        <result column="time" property="time"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="peakChargeQuantity" property="peak_charge_quantity"/>
        <result column="vallyChargeQuantity" property="vally_charge_quantity"/>
        <result column="flatChargeQuantity" property="flat_charge_quantity"/>
        <result column="tipChargeQuantity" property="tip_charge_quantity"/>
        <result column="deepVallyChargeQuantity" property="deep_vally_charge_quantity"/>
        <result column="peakChargeCost" property="peak_charge_cost"/>
        <result column="vallyChargeCost" property="vally_charge_cost"/>
        <result column="flatChargeCost" property="flat_charge_cost"/>
        <result column="tipChargeCost" property="tip_charge_cost"/>
        <result column="deepVallyChargeCost" property="deep_vally_charge_cost"/>
        <result column="peakDischargeQuantity" property="peak_discharge_quantity"/>
        <result column="vallyDischargeQuantity" property="vally_discharge_quantity"/>
        <result column="flatDischargeQuantity" property="flat_discharge_quantity"/>
        <result column="tipDischargeQuantity" property="tip_discharge_quantity"/>
        <result column="deepVallyDischargeQuantity" property="deep_vally_discharge_quantity"/>
        <result column="peakDischargeBenefit" property="peak_discharge_benefit"/>
        <result column="vallyDischargeBenefit" property="vally_discharge_benefit"/>
        <result column="flatDischargeBenefit" property="flat_discharge_benefit"/>
        <result column="tipDischargeBenefit" property="tip_discharge_benefit"/>
        <result column="deepVallyDischargeBenefit" property="deep_vally_discharge_benefit"/>
        <result column="peak_sell_price" property="peakSellPrice"/>
        <result column="vally_sell_price" property="vallySellPrice"/>
        <result column="flat_sell_price" property="flatSellPrice"/>
        <result column="tip_sell_price" property="tipSellPrice"/>
        <result column="deep_vally_sell_price" property="deepVallySellPrice"/>

        <result column="peak_buy_price" property="peakBuyPrice"/>
        <result column="vally_buy_price" property="vallyBuyPrice"/>
        <result column="flat_buy_price" property="flatBuyPrice"/>
        <result column="tip_buy_price" property="tipBuyPrice"/>
        <result column="deep_vally_buy_price" property="deepVallyBuyPrice"/>

        <result column="totalChargeCost" property="total_charge_cost"/>
        <result column="totalChargeQuantity" property="total_charge_quantity"/>
        <result column="totalDischargeQuantity" property="total_discharge_quantity"/>
        <result column="totalDischargeBenefit" property="total_discharge_benefit"/>
        <result column="totalBenefit" property="total_benefit"/>
        <result column="user_id" property="userId"/>
    </resultMap>

    <select id="queryElectricSumGroupByDeviceId" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select device_id, sum(tip_charge_quantity) as tip_charge_quantity, sum(peak_charge_quantity) as
        peak_charge_quantity,
        sum(flat_charge_quantity) as flat_charge_quantity, sum(vally_charge_quantity) as vally_charge_quantity,
        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, sum(peak_discharge_quantity) as
        peak_discharge_quantity,
        sum(vally_discharge_quantity) as vally_discharge_quantity, sum(flat_discharge_quantity) as
        flat_discharge_quantity,
        sum(tip_discharge_quantity) as tip_discharge_quantity, sum(deep_vally_discharge_quantity) as
        deep_vally_discharge_quantity , sum(total_charge_quantity) as total_charge_quantity,
        sum(total_discharge_quantity) as total_discharge_quantity from t_electric
        where <![CDATA[ time >= #{start} and time < #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId} group by device_id
    </select>

    <select id="queryElectricMonthGroupByDeviceId"
            parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select device_id, year, month, day, tip_charge_quantity,
        peak_charge_quantity, flat_charge_quantity, vally_charge_quantity, deep_vally_charge_quantity,
        peak_discharge_quantity, vally_discharge_quantity, flat_discharge_quantity, tip_discharge_quantity,
        deep_vally_discharge_quantity, total_discharge_quantity, total_charge_quantity
        from t_electric where <![CDATA[ time >= #{start} and time<= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        order by year, month, day
    </select>

    <select id="queryElectricYear" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select device_id, year, month, sum(tip_charge_quantity) as tip_charge_quantity, sum(peak_charge_quantity) as
        peak_charge_quantity, sum(flat_charge_quantity) as flat_charge_quantity, sum(vally_charge_quantity) as
        vally_charge_quantity,
        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, sum(peak_discharge_quantity) as
        peak_discharge_quantity, sum(vally_discharge_quantity) as vally_discharge_quantity, sum(flat_discharge_quantity)
        as
        flat_discharge_quantity, sum(tip_discharge_quantity) as tip_discharge_quantity,
        sum(deep_vally_discharge_quantity) as
        deep_vally_discharge_quantity, sum(total_charge_quantity) as total_charge_quantity,
        sum(total_discharge_quantity) as total_discharge_quantity
        from t_electric where <![CDATA[ time >= #{start} and time<= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        group by device_id, year, month order by year, month
    </select>

    <select id="queryElectricProfit" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select sum(tip_charge_quantity) as tip_charge_quantity, sum(peak_charge_quantity) as peak_charge_quantity,
        sum(flat_charge_quantity) as flat_charge_quantity, sum(vally_charge_quantity) as vally_charge_quantity,
        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, sum(tip_charge_cost) as tip_charge_cost,
        sum(peak_charge_cost) as peak_charge_cost, sum(flat_charge_cost) as flat_charge_cost, sum(vally_charge_cost) as
        vally_charge_cost,
        sum(deep_vally_charge_cost) as deep_vally_charge_cost, sum(peak_discharge_quantity) as peak_discharge_quantity,
        sum(vally_discharge_quantity) as vally_discharge_quantity, sum(flat_discharge_quantity) as
        flat_discharge_quantity,
        sum(tip_discharge_quantity) as tip_discharge_quantity, sum(deep_vally_discharge_quantity) as
        deep_vally_discharge_quantity,
        sum(peak_discharge_benefit) as peak_discharge_benefit, sum(tip_discharge_benefit) as tip_discharge_benefit,
        sum(flat_discharge_benefit) as flat_discharge_benefit, sum(vally_discharge_benefit) as vally_discharge_benefit,
        sum(deep_vally_discharge_benefit) as deep_vally_discharge_benefit
        from t_electric where <![CDATA[ time >= #{start} and time<= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
    </select>

    <select id="queryElectricTotalProfit" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select sum(tip_charge_quantity) as tip_charge_quantity, avg(tip_sell_price) as tip_sell_price,
        avg(tip_buy_price) as tip_buy_price, sum(tip_charge_cost) as
        tip_charge_cost,
        sum(peak_charge_quantity) as peak_charge_quantity, avg(peak_sell_price) as peak_sell_price, avg(peak_buy_price)
        as peak_buy_price, sum(peak_charge_cost) as
        peak_charge_cost,
        sum(flat_charge_quantity) as flat_charge_quantity, avg(flat_sell_price) as flat_sell_price, avg(flat_buy_price)
        as flat_buy_price, sum(flat_charge_cost) as
        flat_charge_cost,
        sum(vally_charge_quantity) as vally_charge_quantity ,avg(vally_sell_price) as vally_sell_price,
        avg(vally_buy_price) as vally_buy_price, sum(vally_charge_cost) as
        vally_charge_cost,
        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, avg(deep_vally_sell_price) as
        deep_vally_sell_price, avg(deep_vally_buy_price) as deep_vally_buy_price,
        sum(deep_vally_charge_cost) as deep_vally_charge_cost,
        sum(tip_discharge_quantity) as tip_discharge_quantity, sum(tip_discharge_benefit) as tip_discharge_benefit,
        sum(peak_discharge_quantity) as peak_discharge_quantity, sum(peak_discharge_benefit) as peak_discharge_benefit,
        sum(flat_discharge_quantity) as flat_discharge_quantity, sum(flat_discharge_benefit) as flat_discharge_benefit,
        sum(vally_discharge_quantity) as vally_discharge_quantity, sum(vally_discharge_benefit) as
        vally_discharge_benefit,
        sum(deep_vally_discharge_quantity) as deep_vally_discharge_quantity, sum(deep_vally_discharge_benefit) as
        deep_vally_discharge_benefit,
        sum(total_charge_quantity) as total_charge_quantity, sum(total_discharge_quantity) as total_discharge_quantity
        from t_electric where <![CDATA[ time >= #{start} and time< #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and calibration_type != 2
        and project_id = #{projectId}
    </select>

    <!--    <select id="queryElectricEveryDayProfit" parameterType="com.wifochina.modules.electric.request.ElectricRequest"-->
    <!--            resultMap="BaseResultMap">-->
    <!--        select sum(tip_charge_quantity) as tip_charge_quantity, avg(tip_sell_price) as tip_sell_price,-->
    <!--        avg(tip_buy_price) as tip_buy_price, sum(tip_charge_cost)-->
    <!--        as tip_charge_cost,-->
    <!--        sum(peak_charge_quantity) as peak_charge_quantity, avg(peak_sell_price) as peak_sell_price, avg(peak_buy_price)-->
    <!--        as peak_buy_price, sum(peak_charge_cost) as-->
    <!--        peak_charge_cost,-->
    <!--        sum(flat_charge_quantity) as flat_charge_quantity, avg(flat_sell_price) as flat_sell_price, avg(flat_buy_price)-->
    <!--        as flat_buy_price, sum(flat_charge_cost) as-->
    <!--        flat_charge_cost,-->
    <!--        sum(vally_charge_quantity) as vally_charge_quantity ,avg(vally_sell_price) as vally_sell_price,-->
    <!--        avg(vally_buy_price) as vally_buy_price, sum(vally_charge_cost) as-->
    <!--        vally_charge_cost,-->
    <!--        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, avg(deep_vally_sell_price) as-->
    <!--        deep_vally_sell_price, avg(deep_vally_buy_price) as deep_vally_buy_price,-->
    <!--        sum(deep_vally_charge_cost) as deep_vally_charge_cost,-->
    <!--        sum(tip_discharge_quantity) as tip_discharge_quantity, sum(tip_discharge_benefit) as tip_discharge_benefit,-->
    <!--        sum(peak_discharge_quantity) as peak_discharge_quantity, sum(peak_discharge_benefit) as peak_discharge_benefit,-->
    <!--        sum(flat_discharge_quantity) as flat_discharge_quantity, sum(flat_discharge_benefit) as flat_discharge_benefit,-->
    <!--        sum(vally_discharge_quantity) as vally_discharge_quantity, sum(vally_discharge_benefit) as-->
    <!--        vally_discharge_benefit,-->
    <!--        sum(deep_vally_discharge_quantity) as deep_vally_discharge_quantity, sum(deep_vally_discharge_benefit) as-->
    <!--        deep_vally_discharge_benefit,-->
    <!--        sum(total_charge_quantity) as total_charge_quantity, sum(total_discharge_quantity) as total_discharge_quantity,-->
    <!--        year, month, day, time-->
    <!--        from t_electric-->
    <!--        where <![CDATA[ time >= #{start} and time < #{end} ]]>-->
    <!--        <if test="deviceIdList!= null and deviceIdList.size()>0">-->
    <!--            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"-->
    <!--                         close=")">device_id = #{item}</foreach>)-->
    <!--        </if>-->
    <!--        and project_id = #{projectId}-->
    <!--        group by year, month, day, time order by year, month, day-->
    <!--    </select>-->


    <select id="queryElectricEveryDayProfit" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        SELECT
        SUM(tip_charge_quantity) AS tip_charge_quantity,
        AVG(tip_sell_price) AS tip_sell_price,
        AVG(tip_buy_price) AS tip_buy_price,
        SUM(tip_charge_cost) AS tip_charge_cost,
        SUM(peak_charge_quantity) AS peak_charge_quantity,
        AVG(peak_sell_price) AS peak_sell_price,
        AVG(peak_buy_price) AS peak_buy_price,
        SUM(peak_charge_cost) AS peak_charge_cost,
        SUM(flat_charge_quantity) AS flat_charge_quantity,
        AVG(flat_sell_price) AS flat_sell_price,
        AVG(flat_buy_price) AS flat_buy_price,
        SUM(flat_charge_cost) AS flat_charge_cost,
        SUM(vally_charge_quantity) AS vally_charge_quantity,
        AVG(vally_sell_price) AS vally_sell_price,
        AVG(vally_buy_price) AS vally_buy_price,
        SUM(vally_charge_cost) AS vally_charge_cost,
        SUM(deep_vally_charge_quantity) AS deep_vally_charge_quantity,
        AVG(deep_vally_sell_price) AS deep_vally_sell_price,
        AVG(deep_vally_buy_price) AS deep_vally_buy_price,
        SUM(deep_vally_charge_cost) AS deep_vally_charge_cost,
        SUM(tip_discharge_quantity) AS tip_discharge_quantity,
        SUM(tip_discharge_benefit) AS tip_discharge_benefit,
        SUM(peak_discharge_quantity) AS peak_discharge_quantity,
        SUM(peak_discharge_benefit) AS peak_discharge_benefit,
        SUM(flat_discharge_quantity) AS flat_discharge_quantity,
        SUM(flat_discharge_benefit) AS flat_discharge_benefit,
        SUM(vally_discharge_quantity) AS vally_discharge_quantity,
        SUM(vally_discharge_benefit) AS vally_discharge_benefit,
        SUM(deep_vally_discharge_quantity) AS deep_vally_discharge_quantity,
        SUM(deep_vally_discharge_benefit) AS deep_vally_discharge_benefit,
        SUM(total_charge_quantity) AS total_charge_quantity,
        SUM(total_discharge_quantity) AS total_discharge_quantity,
        year, month
        <if test="!timeRangeWithin31Day">
            , day, time
        </if>
        FROM t_electric
        WHERE <![CDATA[ time >= #{start} and time < #{end} ]]>
        <if test="deviceIdList != null and deviceIdList.size() > 0">
            AND (
            <foreach collection="deviceIdList" index="index" item="item" open="(" separator="OR" close=")">
                device_id = #{item}
            </foreach>
            )
        </if>
        AND project_id = #{projectId}
        and calibration_type != 2
        GROUP BY year, month
        <if test="!timeRangeWithin31Day">
            , day, time
        </if>
        ORDER BY year, month
        <if test="!timeRangeWithin31Day">
            , day, time
        </if>
    </select>


    <select id="queryElectricPrice" parameterType="com.wifochina.modules.electric.request.ElectricPriceRequest"
            resultMap="BaseResultMap">
        select distinct year, month, day, time, peak_sell_price, tip_sell_price, vally_sell_price, flat_sell_price, deep_vally_sell_price, peak_buy_price, tip_buy_price, vally_buy_price, flat_buy_price, deep_vally_buy_price
        from t_electric
        where <![CDATA[ time >= #{start}
          and time <= #{end} ]]>
        and project_id = #{projectId}
    </select>

    <select id="queryElectricProfitByMonth" parameterType="com.wifochina.modules.electric.request.ElectricPriceRequest"
            resultMap="BaseResultMap">
        select year, month, sum(tip_charge_quantity) as tip_charge_quantity, avg(tip_sell_price) as tip_sell_price,
        avg(tip_buy_price) as tip_buy_price,
        sum(tip_charge_cost) as tip_charge_cost,
        sum(peak_charge_quantity) as peak_charge_quantity, avg(peak_sell_price) as peak_sell_price, avg(peak_buy_price)
        as peak_buy_price, sum(peak_charge_cost) as
        peak_charge_cost,
        sum(flat_charge_quantity) as flat_charge_quantity, avg(flat_sell_price) as flat_sell_price, avg(flat_buy_price)
        as flat_buy_price, sum(flat_charge_cost) as
        flat_charge_cost,
        sum(vally_charge_quantity) as vally_charge_quantity ,avg(vally_sell_price) as vally_sell_price,
        avg(vally_buy_price) as vally_buy_price, sum(vally_charge_cost) as
        vally_charge_cost,
        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, avg(deep_vally_buy_price) as
        deep_vally_buy_price, avg(deep_vally_buy_price) as deep_vally_buy_price,
        sum(deep_vally_charge_cost) as deep_vally_charge_cost,
        sum(tip_discharge_quantity) as tip_discharge_quantity, sum(tip_discharge_benefit) as tip_discharge_benefit,
        sum(peak_discharge_quantity) as peak_discharge_quantity, sum(peak_discharge_benefit) as peak_discharge_benefit,
        sum(flat_discharge_quantity) as flat_discharge_quantity, sum(flat_discharge_benefit) as flat_discharge_benefit,
        sum(vally_discharge_quantity) as vally_discharge_quantity, sum(vally_discharge_benefit) as
        vally_discharge_benefit,
        sum(deep_vally_discharge_quantity) as deep_vally_discharge_quantity, sum(deep_vally_discharge_benefit) as
        deep_vally_discharge_benefit,
        sum(total_charge_quantity) as total_charge_quantity, sum(total_discharge_quantity) as total_discharge_quantity
        from t_electric where <![CDATA[ time >= #{start} and time<= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        and calibration_type != 2
        group by year,month order by year,month
    </select>

    <select id="queryElectricProfitByDay" parameterType="com.wifochina.modules.electric.request.ElectricPriceRequest"
            resultMap="BaseResultMap">
        select time, sum(tip_charge_quantity) as tip_charge_quantity, avg(tip_sell_price) as
        tip_sell_price,avg(tip_buy_price) as tip_buy_price, sum(tip_charge_cost)
        as tip_charge_cost,
        sum(peak_charge_quantity) as peak_charge_quantity, avg(peak_sell_price) as peak_sell_price, avg(peak_buy_price)
        as peak_buy_price, sum(peak_charge_cost) as
        peak_charge_cost,
        sum(flat_charge_quantity) as flat_charge_quantity, avg(flat_sell_price) as flat_sell_price, avg(flat_buy_price)
        as flat_buy_price, sum(flat_charge_cost) as
        flat_charge_cost,
        sum(vally_charge_quantity) as vally_charge_quantity , avg(vally_sell_price) as vally_sell_price,
        avg(vally_buy_price) as vally_buy_price, sum(vally_charge_cost) as
        vally_charge_cost,
        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, avg(deep_vally_sell_price) as
        deep_vally_sell_price, avg(deep_vally_buy_price) as deep_vally_buy_price,
        sum(deep_vally_charge_cost) as deep_vally_charge_cost,
        sum(tip_discharge_quantity) as tip_discharge_quantity, sum(tip_discharge_benefit) as tip_discharge_benefit,
        sum(peak_discharge_quantity) as peak_discharge_quantity, sum(peak_discharge_benefit) as peak_discharge_benefit,
        sum(flat_discharge_quantity) as flat_discharge_quantity, sum(flat_discharge_benefit) as flat_discharge_benefit,
        sum(vally_discharge_quantity) as vally_discharge_quantity, sum(vally_discharge_benefit) as
        vally_discharge_benefit,
        sum(deep_vally_discharge_quantity) as deep_vally_discharge_quantity, sum(deep_vally_discharge_benefit) as
        deep_vally_discharge_benefit,
        sum(total_charge_quantity) as total_charge_quantity, sum(total_discharge_quantity) as total_discharge_quantity
        from t_electric where <![CDATA[ time >= #{start} and time <= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        and calibration_type != 2
        group by time order by time
    </select>

    <select id="queryElectricQuantityByDay" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select time, year, month, day, sum(tip_charge_quantity) as tip_charge_quantity, sum(peak_charge_quantity) as
        peak_charge_quantity, sum(flat_charge_quantity) as flat_charge_quantity, sum(vally_charge_quantity) as
        vally_charge_quantity,
        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, sum(peak_discharge_quantity) as
        peak_discharge_quantity, sum(vally_discharge_quantity) as vally_discharge_quantity, sum(flat_discharge_quantity)
        as
        flat_discharge_quantity, sum(tip_discharge_quantity) as tip_discharge_quantity,
        sum(deep_vally_discharge_quantity) as
        deep_vally_discharge_quantity, sum(total_charge_quantity) as total_charge_quantity,
        sum(total_discharge_quantity) as total_discharge_quantity
        from t_electric where <![CDATA[ time >= #{start} and time<= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        and calibration_type != 2
        group by time, year, month, day order by time
    </select>

    <select id="queryElectricQuantityByMonth" parameterType="com.wifochina.modules.electric.request.ElectricRequest"
            resultMap="BaseResultMap">
        select year, month, sum(tip_charge_quantity) as tip_charge_quantity, sum(peak_charge_quantity) as
        peak_charge_quantity, sum(flat_charge_quantity) as flat_charge_quantity, sum(vally_charge_quantity) as
        vally_charge_quantity,
        sum(deep_vally_charge_quantity) as deep_vally_charge_quantity, sum(peak_discharge_quantity) as
        peak_discharge_quantity, sum(vally_discharge_quantity) as vally_discharge_quantity, sum(flat_discharge_quantity)
        as
        flat_discharge_quantity, sum(tip_discharge_quantity) as tip_discharge_quantity,
        sum(deep_vally_discharge_quantity) as
        deep_vally_discharge_quantity, sum(total_charge_quantity) as total_charge_quantity,
        sum(total_discharge_quantity) as total_discharge_quantity
        from t_electric where <![CDATA[ time >= #{start} and time<= #{end} ]]>
        <if test="deviceIdList!= null and deviceIdList.size()>0">
            and(<foreach collection="deviceIdList" index="index" item="item" open="(" separator="or"
                         close=")">device_id = #{item}</foreach>)
        </if>
        and project_id = #{projectId}
        and calibration_type != 2
        group by year, month order by year, month
    </select>

    <select id="getTotalProfitByDayAndProjectId" resultType="java.lang.Double">
        SELECT SUM(total_benefit)
        from t_electric
        where project_id = #{projectId}
                  and year = #{year} and `month` = #{month} and `day` = #{day}
    </select>
</mapper>
