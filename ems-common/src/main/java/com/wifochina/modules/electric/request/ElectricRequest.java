package com.wifochina.modules.electric.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-12 7:44 PM
 */
@Data
@Accessors(chain = true)
public class ElectricRequest {

    public ElectricRequest() {}

    public ElectricRequest(Long start, Long end, String projectId, List<String> deviceIdList) {
        this.start = start;
        this.end = end;
        this.projectId = projectId;
        this.deviceIdList = deviceIdList;
    }

    @ApiModelProperty(value = "开始时间 时间戳", required = true)
    private Long start;

    @ApiModelProperty(value = "结束时间 时间戳", required = true)
    private Long end;

    @ApiModelProperty(value = "项目id", required = true)
    private String projectId;

    @ApiModelProperty(value = "设备id", required = true)
    private List<String> deviceIdList;

    @ApiModelProperty(value = "查询时间段是否超过一个月", required = false)
    private boolean timeRangeWithin31Day = false;
}
