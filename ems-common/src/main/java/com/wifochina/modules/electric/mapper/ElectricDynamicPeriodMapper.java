package com.wifochina.modules.electric.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.request.ElectricRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-24 17:53:15
 */
public interface ElectricDynamicPeriodMapper extends BaseMapper<ElectricDynamicPeriodEntity> {
    ElectricDynamicPeriodEntity queryElectricTotalProfit(ElectricRequest electricRequest);

    List<ElectricDynamicPeriodEntity> queryElectricEveryDayProfit(ElectricRequest electricRequest);

    List<ElectricDynamicPeriodEntity> queryElectricTimeSharingProfit(
            ElectricRequest electricRequest);

    List<ElectricDynamicPeriodEntity> queryElectricSumGroupByDeviceId(
            ElectricRequest electricRequest);

    List<ElectricDynamicPeriodEntity> queryElectricMonthGroupByDeviceId(
            ElectricRequest electricRequest);

    List<ElectricDynamicPeriodEntity> queryElectricYear(ElectricRequest electricRequest);

    Double getTotalProfitByDayAndProjectId(
            @Param("projectId") String projectId,
            @Param("year") int year,
            @Param("month") int month,
            @Param("day") int day);
}
