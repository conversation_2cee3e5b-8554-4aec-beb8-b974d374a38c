package com.wifochina.modules.electric;

import com.wifochina.common.util.TimeContext;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

/**
 * Created on 2024/5/24 10:13.
 *
 * <AUTHOR>
 */
public interface ElectricTimeSeriesService {

    void calculateTimeChargeDiff(
            TimeContext timeContext,
            ElectricPriceEntity price,
            ProjectEntity projectEntity,
            String sql,
            CalculateResult calculateResult);

    String getElectricEmsSql(ProjectEntity projectEntity);

    String getElectricMeterSql(ProjectEntity projectEntity);

    interface CalculateResult {
        /**
         * @param outDiff
         * @param inDiff
         */
        void result(Double outDiff, Double inDiff);
    }
}
