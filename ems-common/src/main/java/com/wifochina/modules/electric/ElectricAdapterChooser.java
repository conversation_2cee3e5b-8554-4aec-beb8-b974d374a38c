package com.wifochina.modules.electric;

import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.electric.entity.ElectricPeriod;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created on 2024/5/23 19:46. 电计算收益 dapter 适配chooser
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class ElectricAdapterChooser {
    private final List<ElectricAdapterService<? extends ElectricPeriod>> electricAdapterServices;

    /**
     * @param type
     * @return
     */
    public ElectricAdapterService<? extends ElectricPeriod> choose(String type) {
        return electricAdapterServices.stream()
                .filter(electricAdapterService -> electricAdapterService.type().contains(type))
                .findFirst()
                .orElseThrow(() -> new ServiceException(""));
    }
}
