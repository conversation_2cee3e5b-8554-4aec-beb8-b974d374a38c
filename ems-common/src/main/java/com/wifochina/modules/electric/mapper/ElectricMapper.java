package com.wifochina.modules.electric.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.request.ElectricPriceRequest;
import com.wifochina.modules.electric.request.ElectricRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
public interface ElectricMapper extends BaseMapper<ElectricEntity> {
    List<ElectricEntity> queryElectricSumGroupByDeviceId(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricMonthGroupByDeviceId(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricYear(ElectricRequest electricRequest);

    ElectricEntity queryElectricTotalProfit(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricEveryDayProfit(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricPrice(ElectricPriceRequest electricPriceRequest);

    List<ElectricEntity> queryElectricProfitByMonth(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricProfitByDay(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricQuantityByDay(ElectricRequest electricRequest);

    List<ElectricEntity> queryElectricQuantityByMonth(ElectricRequest electricRequest);

    Double getTotalProfitByDayAndProjectId(
            @Param("projectId") String projectId,
            @Param("year") int year,
            @Param("month") int month,
            @Param("day") int day);
}
