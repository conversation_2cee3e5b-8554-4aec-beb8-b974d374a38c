package com.wifochina.modules.altertest;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * Created on 2025/3/21 14:34.
 *
 * <AUTHOR>
 */
@FeignClient(value = "alter-manager")
public interface AlterManagerOpenApiFeign {
    @GetMapping("/getAlterInfo/{id}")
    AlterInfo getAlterInfo(@PathVariable("id") String id);
}
