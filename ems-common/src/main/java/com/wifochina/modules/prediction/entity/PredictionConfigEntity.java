package com.wifochina.modules.prediction.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_prediction_config")
@ApiModel(value = "PredictionConfigEntity对象", description = "")
public class PredictionConfigEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "project_id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "类型: 光伏pv,负载load")
    private String type;

    @ApiModelProperty(value = "星期：1-7")
    private Integer week;

    @ApiModelProperty(value = "文件索引")
    private String fileIndex;

    @ApiModelProperty(value = "24小时模式: deepar,deeptft,deeppv")
    @TableField(value = "model_24hours")
    private String model24hours;

    @ApiModelProperty(value = "24小时配置")
    @TableField(value = "config_24hours")
    private String config24hours;

    @ApiModelProperty(value = "48小时模式: deepar,deeptft,deeppv")
    @TableField(value = "model_48hours")
    private String model48hours;

    @ApiModelProperty(value = "48小时配置")
    @TableField(value = "config_48hours")
    private String config48hours;

}
