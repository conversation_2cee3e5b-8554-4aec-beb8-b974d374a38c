package com.wifochina.modules.prediction.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
public class PredictionConfigRequest {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "类型: 光伏pv,负载load")
    private String type;

    @ApiModelProperty(value = "类型: deepar,deeptft,deeppv")
    private String predictionType;

    @ApiModelProperty(value = "星期")
    private Integer week;

    @ApiModelProperty(value = "文件索引")
    private String fileIndex;

    @ApiModelProperty(value = "24小时模式")
    private String model24hours;

    @ApiModelProperty(value = "24小时配置")
    private String config24hours;

    @ApiModelProperty(value = "48小时模式")
    private String model48hours;

    @ApiModelProperty(value = "48小时配置")
    private String config48hours;

}
