package com.wifochina.modules.prediction.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_prediction_manage")
@ApiModel(value = "PredictionManageEntity对象", description = "")
public class PredictionManageEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "project_id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "类型: 光伏pv,负载load")
    private String type;
}
