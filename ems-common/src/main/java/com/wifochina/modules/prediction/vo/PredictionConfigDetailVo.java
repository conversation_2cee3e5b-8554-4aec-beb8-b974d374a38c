package com.wifochina.modules.prediction.vo;

import java.util.List;

import com.wifochina.modules.prediction.entity.PredictionConfigEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-06-25 7:16 PM
 */
@Data
public class PredictionConfigDetailVo {

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "类型: 光伏pv,负载load")
    private String type;

    @ApiModelProperty(value = "预测的细节")
    private List<PredictionConfigEntity> predictionConfigEntities;
}
