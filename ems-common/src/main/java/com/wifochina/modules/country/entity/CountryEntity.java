package com.wifochina.modules.country.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-12-18
 */
@Getter
@Setter
@TableName("t_country")
@ApiModel(value = "CountryEntity对象")
public class CountryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer datacenter;

    private String phoneCode;

    @JsonProperty("zhCn")
    @TableField(exist = false)
    private String zhCnApp;

    @JsonProperty("enUs")
    @TableField(exist = false)
    private String enUsApp;

    @JsonProperty("deDe")
    @TableField(exist = false)
    private String deDeApp;

    @JsonProperty("nlNl")
    @TableField(exist = false)
    private String nlNlApp;

    @JsonProperty("frFr")
    @TableField(exist = false)
    private String frFrApp;

    @JsonProperty("esEs")
    @TableField(exist = false)
    private String esEsApp;

    @JsonProperty("ptPt")
    @TableField(exist = false)
    private String ptPtApp;

    @JsonProperty("itIt")
    @TableField(exist = false)
    private String itItApp;

    @JsonProperty("plPl")
    @TableField(exist = false)
    private String plPlApp;

    @JsonProperty("zh_CN")
    private String zhCn;

    @JsonProperty("en_US")
    private String enUs;

    @JsonProperty("de_DE")
    private String deDe;

    @JsonProperty("nl_NL")
    private String nlNl;

    @JsonProperty("fr_FR")
    private String frFr;

    @JsonProperty("es_ES")
    private String esEs;

    @JsonProperty("pt_PT")
    private String ptPt;

    @JsonProperty("it_IT")
    private String itIt;

    @JsonProperty("pl_PL")
    private String plPl;

    @JsonProperty("sv_SE")
    private String svSe;

    @JsonProperty("bg_BG")
    private String bgBg;
}
