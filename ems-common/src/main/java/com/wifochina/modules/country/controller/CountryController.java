package com.wifochina.modules.country.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.page.Result;
import com.wifochina.modules.country.entity.CountryEntity;
import com.wifochina.modules.country.request.CountryNameAndPartitionRequest;
import com.wifochina.modules.country.request.CountryRequest;
import com.wifochina.modules.country.service.CountryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-12-18
 */
@Slf4j
@RestController
@RequestMapping("/country")
@Api(tags = "98-国家")
public class CountryController {

    @Resource private CountryService countryService;

    /** 获取国家 */
    @PostMapping("/get")
    @ApiOperation("获取国家详情")
    public Result<CountryEntity> get(@RequestBody CountryRequest countryRequest) {
        CountryEntity countryEntity = countryService.getById(countryRequest.getId());
        resetValue(countryEntity);
        return Result.success(countryEntity);
    }

    /** 获取国家 */
    @PostMapping("/list")
    @ApiOperation("获取国家分页列表")
    public Result<Page<CountryEntity>> list(@RequestBody CountryNameAndPartitionRequest request) {
        Page<CountryEntity> list =
                countryService.page(
                        Page.of(request.getPageNum(), request.getPageSize()),
                        Wrappers.lambdaQuery(CountryEntity.class)
                                .eq(
                                        !Objects.isNull(request.getDatacenter()),
                                        CountryEntity::getDatacenter,
                                        request.getDatacenter())
                                .and(
                                        StringUtils.hasLength(request.getName().trim()),
                                        eq ->
                                                eq.like(
                                                                CountryEntity::getZhCn,
                                                                request.getName().trim())
                                                        .or()
                                                        .like(
                                                                CountryEntity::getEnUs,
                                                                request.getName().trim())));
        list.getRecords().forEach(this::resetValue);
        return Result.success(list);
    }

    public void resetValue(CountryEntity countryEntity) {
        countryEntity.setZhCnApp(countryEntity.getZhCn());
        countryEntity.setEnUsApp(countryEntity.getEnUs());
        countryEntity.setDeDeApp(countryEntity.getDeDe());
        countryEntity.setNlNlApp(countryEntity.getNlNl());
        countryEntity.setFrFrApp(countryEntity.getFrFr());
        countryEntity.setEsEsApp(countryEntity.getEsEs());
        countryEntity.setPtPtApp(countryEntity.getPtPt());
        countryEntity.setItItApp(countryEntity.getItIt());
        countryEntity.setPlPlApp(countryEntity.getPlPl());
    }
}
