package com.wifochina.modules.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.TypeCodeIndexEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.OnOffGridRunEnum;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.listener.DataEventListener;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.service.impl.EventCodeLanguageServiceImpl;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.system.VO.DeviceRunStatusVO;
import com.wifochina.modules.system.request.DeviceMaintainRequest;
import com.wifochina.modules.system.request.DeviceOnOffGoRequest;
import com.wifochina.modules.system.request.DeviceOnOffRequest;
import com.wifochina.modules.system.request.DeviceRestRequest;
import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 电表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@RestController
@Api(tags = "20-系统配置")
@RequestMapping("/system")
@RequiredArgsConstructor
@Slf4j
public class SystemController {

    private final DeviceService deviceService;

    private final AmmeterService ammeterService;

    private final PointListHolder pointListHolder;

    private final RestTemplate restTemplate;

    private final DataService dataService;

    private final ProjectService projectService;

    private final ControllerService controllerService;

    private final LogService logService;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    private static final String ON_OFF_API_URL = "/api/v1/set_ems_on_off";

    private static final String EMS_RESET_API_URL = "/api/v1/set_ems_reset";

    private static final String EMS_OFF_GRID_API_URL = "/api/v1/set_ems_off_grid";

    /** 系统开关 */
    @PostMapping("/statusChange")
    @ApiOperation("系统开关")
    @Log(module = "SYSTEM", methods = "SYSTEM_SWITCH", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/system/control') or hasAuthority('/group/device/change')")
    public Result<Object> statusChange(@RequestBody DeviceOnOffRequest deviceOnOffRequest) {
        setDeviceStatus(deviceOnOffRequest, ON_OFF_API_URL);
        //        logService.logSelfDefinedDetail(
        //                LogInfo.builder()
        //                        .module("SYSTEM")
        //                        .method("SYSTEM_SWITCH")
        //                        .object(deviceOnOffRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.SYSTEM_SWITCH_TRACE_FORMAT,
        //                                        WebUtils.projectId.get()))
        //                        .detail(deviceOnOffRequest.toString())
        //                        .build());
        return Result.success();
    }

    @PostMapping("/rest")
    @ApiOperation("设备复位")
    @Log(module = "SYSTEM", methods = "SYSTEM_REST", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/system/rest')")
    public Result<Object> rest(@RequestBody DeviceRestRequest deviceRestRequest) {
        List<String> list = new ArrayList<>(1);
        list.add(deviceRestRequest.getId());
        DeviceOnOffRequest deviceOnOffRequest = new DeviceOnOffRequest();
        deviceOnOffRequest.setIds(list);
        setDeviceStatus(deviceOnOffRequest, EMS_RESET_API_URL);
        //        logService.logSelfDefinedDetail(
        //                LogInfo.builder()
        //                        .module("SYSTEM")
        //                        .method("SYSTEM_REST")
        //                        .object(deviceRestRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.SYSTEM_RESET_TRACE_FORMAT,
        //                                        WebUtils.projectId.get()))
        //                        .detail(deviceRestRequest.toString())
        //                        .build());
        return Result.success();
    }

    /** 离网开关 */
    @PostMapping("/disconnectGridChange")
    @ApiOperation("系统并离网开关")
    @Log(module = "SYSTEM", methods = "DEVICE_DISCONNECT_GRID", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/system/control') or hasAuthority('/group/device/change')")
    public Result<Object> disconnectGridChange(@RequestBody DeviceOnOffRequest deviceOnOffRequest) {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        if (!StringUtil.isEmpty(projectEntity.getWhetherBackup())) {
            // 1.3.9 不再是以前的开关了 改成了 并/离网切换(手动) 的情况下才能 下发
            if (OnOffGridRunEnum.on_off_grid_run_manual
                    .name()
                    .equals(projectEntity.getWhetherBackup())) {
                setDeviceStatus(deviceOnOffRequest, EMS_OFF_GRID_API_URL);
            }
        } else {
            // 异常情况
            log.error("项目Id:{} 并离网切换字段是空 异常情况, 无法下发并离网开关", projectEntity.getId());
            throw new ServiceException(ErrorResultCode.INTERNAL_SERVER_ERROR.value());
        }
        //        logService.logSelfDefinedDetail(
        //                LogInfo.builder()
        //                        .module("SYSTEM")
        //                        .method("DEVICE_DISCONNECT_GRID")
        //                        .object(deviceOnOffRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.DISCONNECT_GRID_TRACE_FORMAT,
        //                                        WebUtils.projectId.get()))
        //                        .detail(deviceOnOffRequest.toString())
        //                        .build());
        return Result.success();
    }

    public void setDeviceStatus(DeviceOnOffRequest deviceOnOffRequest, String apiPath) {
        // 批量开关机
        List<DeviceEntity> deviceEntities;
        if (deviceOnOffRequest.getIds() == null || deviceOnOffRequest.getIds().isEmpty()) {
            // 批量开关机
            deviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                            .eq(DeviceEntity::getUnreal, false)
                            .list();
        } else {
            deviceEntities =
                    deviceService.list(
                            Wrappers.lambdaQuery(DeviceEntity.class)
                                    .in(DeviceEntity::getId, deviceOnOffRequest.getIds()));
        }
        for (DeviceEntity device : deviceEntities) {
            DeviceOnOffGoRequest deviceOnOffGoRequest = new DeviceOnOffGoRequest();
            deviceOnOffGoRequest.setEms_uuid(device.getId());
            deviceOnOffGoRequest.setOn_off(deviceOnOffRequest.getOnOrOff());
            uploadEmsOnOff(deviceOnOffGoRequest, apiPath);
        }
    }

    public void uploadEmsOnOff(DeviceOnOffGoRequest deviceOnOffGoRequest, String apiPath) {
        JSONObject jsonObject;
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        if (projectEntity.getProjectModel() == 1) {
            ControllerEntity controllerEntity =
                    controllerService.getOne(
                            Wrappers.lambdaQuery(ControllerEntity.class)
                                    .eq(ControllerEntity::getProjectId, WebUtils.projectId.get()));
            String outerControllerUrl =
                    "http://" + controllerEntity.getIp().trim() + ":" + controllerEntity.getPort();
            jsonObject =
                    restTemplate.postForObject(
                            outerControllerUrl + apiPath, deviceOnOffGoRequest, JSONObject.class);
        } else {
            jsonObject =
                    restTemplate.postForObject(
                            gatewayUrl + apiPath, deviceOnOffGoRequest, JSONObject.class);
        }
        assert jsonObject != null;
        Optional.of(jsonObject)
                .ifPresent(
                        json -> {
                            if (!(boolean) jsonObject.get(EmsConstants.SUCCESS)) {
                                throw new ServiceException(
                                        ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
                            }
                        });
    }

    /** 设备开关机状态 */
    @GetMapping("/getAllRunStatus")
    @ApiOperation("所有设备开关机状态")
    @PreAuthorize("hasAuthority('/system') or hasAuthority('/group/device/detail')")
    public Result<List<DeviceRunStatusVO>> getAllRunStatus() {
        List<DeviceEntity> deviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                        .eq(DeviceEntity::getUnreal, false)
                        .orderByAsc(DeviceEntity::getOrderIndex)
                        .orderByAsc(DeviceEntity::getCreateTime)
                        .list();
        List<DeviceRunStatusVO> statusList = new ArrayList<>();
        deviceEntities.forEach(
                deviceEntity -> {
                    DeviceRunStatusVO deviceRunStatusVO = new DeviceRunStatusVO();
                    deviceRunStatusVO.setId(deviceEntity.getId());
                    deviceRunStatusVO.setName(deviceEntity.getName());
                    deviceRunStatusVO.setGridDisconnected(false);
                    deviceRunStatusVO.setMaintain(deviceEntity.getMaintain());
                    int[] data = dataService.get(deviceEntity.getId());
                    int mobileStatus = 0;
                    if (data != null) {
                        int index =
                                pointListHolder.getSystemRunStatus(
                                        data[pointListHolder.getEmsTypeIndex()]);
                        if (data[index] > 0) {
                            deviceRunStatusVO.setStatus(true);
                            mobileStatus = 1;
                        } else {
                            deviceRunStatusVO.setStatus(false);
                            mobileStatus = 2;
                        }
                        if (data[
                                        pointListHolder.getSystemOffGrid(
                                                data[pointListHolder.getEmsTypeIndex()])]
                                > 0) {
                            deviceRunStatusVO.setGridDisconnected(true);
                        }
                    } else {
                        deviceRunStatusVO.setStatus(false);
                    }
                    deviceRunStatusVO.setMobileStatus(mobileStatus);
                    statusList.add(deviceRunStatusVO);
                });
        return Result.success(statusList);
    }

    /** 运行状态 */
    @GetMapping("/getRunStatus")
    @ApiOperation("系统运行状态")
    @PreAuthorize("hasAuthority('/system') or hasAuthority('/group/device/detail')")
    public Result<List<EventCodeEntity>> getRunStatus(
            @RequestParam @ApiParam(required = true, name = "deviceId", value = "设备id")
                    String deviceId)
            throws CloneNotSupportedException {
        int[] data = dataService.get(deviceId);
        if (data == null) {
            return Result.success();
        }
        List<PointDataEntity> list =
                pointListHolder.getSystemBitPointList(data[pointListHolder.getEmsTypeIndex()]);
        List<EventCodeEntity> eventCodeEntityList = new ArrayList<>();
        for (PointDataEntity pointDataEntity : list) {
            // 0代表system
            int typeCodeIndex =
                    pointListHolder.getTypeCodeIndex(
                            data[pointListHolder.getEmsTypeIndex()],
                            TypeCodeIndexEnum.SYSTEM.getCode(),
                            null);
            Map<Integer, EventCodeEntity> eventBitMap =
                    DataEventListener.emsEventCodeMap.get(
                            data[typeCodeIndex] + "_" + pointDataEntity.getPointColumn());
            // 该点位暂未使用
            if (eventBitMap == null) {
                continue;
            }
            int index =
                    pointDataEntity.getPointAddress()
                            + Integer.parseInt(pointDataEntity.getPointOffset().trim());

            for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {

                if (eventBitMap.get(i) == null) {
                    continue;
                }
                EventCodeEntity eventCodeEntity = (EventCodeEntity) eventBitMap.get(i).clone();
                EventCodeLanguageServiceImpl.transEventCodeForLanguage(eventCodeEntity);
                // 证明同为1
                eventCodeEntity.setValue(
                        eventCodeEntity.getBitStand() == ((data[index] & (1 << i)) == 0 ? 0 : 1));
                eventCodeEntityList.add(eventCodeEntity);
            }
        }
        return Result.success(eventCodeEntityList);
    }

    /** 维护所有版本 */
    @PostMapping("/maintain")
    @ApiOperation("维护设备")
    @PreAuthorize("hasAuthority('/system/maintain')")
    @Log(module = "SYSTEM", methods = "DEVICE_MAINTAIN", type = OperationType.ADD_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    public Result<String> maintainAll(@RequestBody DeviceMaintainRequest deviceMaintainRequest) {
        Optional.ofNullable(deviceMaintainRequest)
                .map(DeviceMaintainRequest::getMaintain)
                .ifPresent(
                        status -> {
                            List<DeviceEntity> list =
                                    deviceService
                                            .lambdaQuery()
                                            .eq(
                                                    DeviceEntity::getProjectId,
                                                    WebUtils.projectId.get())
                                            .eq(
                                                    !EmsConstants.ALL.equals(
                                                            deviceMaintainRequest.getDeviceId()),
                                                    DeviceEntity::getId,
                                                    deviceMaintainRequest.getDeviceId())
                                            .eq(DeviceEntity::getUnreal, false)
                                            .list();
                            list.forEach(e -> e.setMaintain(status));
                            deviceService.updateBatchById(list);
                            if (EmsConstants.ALL.equals(deviceMaintainRequest.getDeviceId())) {
                                List<AmmeterEntity> ammeterEntities =
                                        ammeterService
                                                .lambdaQuery()
                                                .eq(
                                                        AmmeterEntity::getProjectId,
                                                        WebUtils.projectId.get())
                                                .eq(AmmeterEntity::getControllable, true)
                                                .list();
                                ammeterEntities.forEach(e -> e.setMaintain(status));
                                ammeterService.updateBatchById(ammeterEntities);
                            }
                            //                            logService.logSelfDefinedDetail(
                            //                                    LogInfo.builder()
                            //                                            .module("SYSTEM")
                            //                                            .method("DEVICE_MAINTAIN")
                            //
                            // .object(deviceMaintainRequest)
                            //                                            .traceId(
                            //                                                    String.format(
                            //
                            // LogService.DEVICE_MAINTAIN_TRACE_FORMAT,
                            //
                            // deviceMaintainRequest.getDeviceId()))
                            //
                            // .detail(deviceMaintainRequest.toString())
                            //                                            .build());
                        });
        return Result.success();
    }
}
