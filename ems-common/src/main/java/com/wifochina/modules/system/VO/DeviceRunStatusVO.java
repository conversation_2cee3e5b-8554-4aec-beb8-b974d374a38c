package com.wifochina.modules.system.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * DeviceRunStausVO
 * 
 * @since 4/26/2022 10:33 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class DeviceRunStatusVO {

    @ApiModelProperty(value = "设备标识uuid")
    private String id;

    @ApiModelProperty(value = "设备名称", required = true)
    private String name;

    @ApiModelProperty(value = "运行状态，开是true，关是false", required = true)
    private Boolean status;

    @ApiModelProperty(value = "0代表离线1运行2停机")
    private Integer mobileStatus;

    @ApiModelProperty(value = "离网状态，开是true，关是false")
    private Boolean gridDisconnected;

    @ApiModelProperty(value = "设备是否维护，维护为true")
    private Boolean maintain;

}
