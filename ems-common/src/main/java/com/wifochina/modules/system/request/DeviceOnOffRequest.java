package com.wifochina.modules.system.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * deviceOnOffRequest
 * 
 * @date 4/24/2022 11:01 AM
 * <AUTHOR>
 * @version 1.0
 */

@Data
@ApiModel(value = "设备启停")
public class DeviceOnOffRequest {

    @ApiModelProperty(value = "设备id, null代表所有设备且开机", required = true)
    private List<String> ids;

    @ApiModelProperty(value = "true代表开，false代表关。不支持all批量关机", required = true)
    private Boolean onOrOff;

}
