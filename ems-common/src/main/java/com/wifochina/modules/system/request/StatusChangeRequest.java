package com.wifochina.modules.system.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * StautsChangeRequest
 * 
 * @date 4/21/2022 3:16 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "开关机请求")
public class StatusChangeRequest {
    @ApiModelProperty(value = "设备ID，如果查询所有则传入all")
    private String deviceId;

    @ApiModelProperty(value = "true代表开机，false代表关机")
    private Boolean status;
}
