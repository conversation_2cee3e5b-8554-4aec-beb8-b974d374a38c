package com.wifochina.modules.homepage.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.service.NewDiagramService;
import com.wifochina.modules.homepage.service.HomePageService;
import com.wifochina.modules.project.vo.BasicVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Api(tags = "04-电站概况")
@RestController
@RequestMapping("/homepage")
@RequiredArgsConstructor
public class HomePageController {

    private final HomePageService homePageService;
    private final NewDiagramService newDiagramService;
    private final InfluxClientService influxClient;

    /** welcome page */
    @PostMapping("/basicInfo")
    @ApiOperation("基本情况")
    @PreAuthorize("hasAuthority('/homePage')")
    public Result<BasicVO> welcome() {
        BasicVO basicVo = homePageService.getBasicInfo();
        return Result.success(basicVo);
    }

    /** 充电曲线查询 */
    @PostMapping("/getChargeRate")
    @ApiOperation("充放电曲线查询")
    @PreAuthorize("hasAuthority('/homePage')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getChargeRate(
            @RequestBody RequestWithDeviceId requestWithDeviceId) {
        Map<String, Map<String, List<ValueVO>>> chargeRateMap =
                newDiagramService.getDeviceRate(
                        requestWithDeviceId,
                        influxClient.getBucketMean(),
                        List.of("ems_ac_active_power_pos", "ems_ac_active_power_neg"));
        //        Map<String, Map<String, List<ValueVO>>> chargeRateMap =
        //                diagramService.getRate(
        //                        influxClientService.getBucketMean(),
        //                        requestWithDeviceId,
        //                        fields,
        //                        null,
        //                        influxClientService.getEmsTable(WebUtils.projectId.get()));
        return Result.success(chargeRateMap);
    }
}
