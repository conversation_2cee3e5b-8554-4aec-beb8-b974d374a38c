package com.wifochina.modules.homepage.controller;

import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.common.request.RequestWithId;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.ProjectUrlLogDetailService;
import com.wifochina.modules.project.entity.ProjectUrlEntity;
import com.wifochina.modules.project.request.ProjectUrlRequest;
import com.wifochina.modules.project.request.SiteDailyRequest;
import com.wifochina.modules.project.service.ProjectScreenService;
import com.wifochina.modules.project.service.ProjectUrlService;
import com.wifochina.modules.project.vo.EmsVO;
import com.wifochina.modules.project.vo.SiteVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-01 1:26 PM
 */
@Api(tags = "03-项目首页")
@RestController
@RequestMapping("/project")
@RequiredArgsConstructor
@Slf4j
public class SiteController {

    private final ProjectUrlService projectUrlService;

    private final ProjectScreenService projectScreenService;

    /** 电站全屏控-整站模式 */
    @PostMapping("/site")
    @ApiOperation("电站全屏控-整站模式")
    public Result<SiteVO> getSite(@RequestBody SiteDailyRequest siteDailyRequest) {
        SiteVO siteVO = projectScreenService.getSite(siteDailyRequest.getStartDate());
        log.debug("test siteVo :{}", siteVO);
        return Result.success(siteVO);
    }

    /** ems-单机模式 */
    @PostMapping("/ems")
    @ApiOperation("ems-单机模式")
    public Result<List<EmsVO>> getEms(@RequestBody SiteDailyRequest siteDailyRequest) {
        List<EmsVO> list = projectScreenService.getEms(siteDailyRequest.getStartDate());
        return Result.success(list);
    }

    @PostMapping("/url/create")
    @ApiOperation("附加配置页面创建")
    @Log(module = "MANAGE_PROJECT", methods = "URL_ADD", type = OperationType.ADD)
    public Result<Object> create(@RequestBody ProjectUrlRequest request) {
        projectUrlService.createUrl(request);
        return Result.success();
    }

    @PostMapping("/url/query")
    @ApiOperation("通过id查询附加配置页面详情")
    public Result<ProjectUrlEntity> query(@RequestBody RequestWithId requestWithId) {
        ProjectUrlEntity projectUrlEntity = projectUrlService.getById(requestWithId.getId());
        return Result.success(projectUrlEntity);
    }

    @PostMapping("/url/update")
    @ApiOperation("附加配置页面修改")
    @Log(module = "MANAGE_PROJECT", methods = "URL_UPDATE", type = OperationType.UPDATE)
    public Result<Object> update(@RequestBody ProjectUrlRequest request) {
        projectUrlService.updateUrl(request);
        return Result.success();
    }

    @PostMapping("/url/delete")
    @ApiOperation("附加配置页面删除")
    @Log(
            module = "MANAGE_PROJECT",
            methods = "URL_DEL",
            type = OperationType.DEL,
            logDetailServiceClass = ProjectUrlLogDetailService.class)
    public Result<Object> delete(@RequestBody RequestWithId requestWithId) {
        projectUrlService.removeById(requestWithId.getId());
        return Result.success();
    }

    @PostMapping("/url/list")
    @ApiOperation("通过项目id查询附加配置页面列表")
    public Result<List<ProjectUrlEntity>> listProjectUrls(
            @RequestBody RequestWithId requestWithId) {
        List<ProjectUrlEntity> list =
                projectUrlService
                        .lambdaQuery()
                        .eq(ProjectUrlEntity::getProjectId, requestWithId.getId())
                        .orderByDesc(ProjectUrlEntity::getOrderNum)
                        .list();
        return Result.success(list);
    }
}
