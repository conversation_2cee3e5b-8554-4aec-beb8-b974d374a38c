package com.wifochina.modules.homepage.service.impl;

import com.wifochina.common.util.EmsUtil;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.homepage.service.HomePageService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.vo.BasicVO;

import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
public class HomePageServiceImpl implements HomePageService {

    @Resource private DeviceService deviceService;

    @Resource private DataService dataService;

    @Resource private PointListHolder pointListHolder;

    @Override
    public BasicVO getBasicInfo() {
        BasicVO basicVO = new BasicVO();
        List<DeviceEntity> deviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                        .eq(DeviceEntity::getUnreal, false)
                        .list();
        double emsDesignPower = 0;
        double emsChargePower = 0;
        double emsDischargePower = 0;
        double emsCapacity = 0;
        double pscNum = 0;
        double bmsCluster = 0;
        double currentChargeCapacity = 0;
        double currentDischargeCapacity = 0;
        double soh = 0;
        double soc = 0;
        double bmsDesignCapacity = 0;
        // 2023-10-30 16:14:02 add
        boolean isOff = true;
        double emsOutPower = 0;
        double emsInPower = 0;
        boolean hasEmsRun = false;

        for (DeviceEntity deviceEntity : deviceEntities) {

            int[] data = dataService.get(deviceEntity.getId());
            if (data == null) {
                continue;
            } else {
                isOff = false;
            }

            pscNum += data[pointListHolder.getPcsNum(data[pointListHolder.getEmsTypeIndex()])];
            emsDesignPower +=
                    data[
                            pointListHolder.getEmsDesignPower(
                                    data[pointListHolder.getEmsTypeIndex()])];
            emsChargePower +=
                    data[
                            pointListHolder.getEmsDesignChargePower(
                                    data[pointListHolder.getEmsTypeIndex()])];
            emsDischargePower +=
                    data[
                            pointListHolder.getEmsDesignDischargePower(
                                    data[pointListHolder.getEmsTypeIndex()])];
            emsCapacity +=
                    data[pointListHolder.getEmsCapacity(data[pointListHolder.getEmsTypeIndex()])];
            bmsCluster +=
                    data[pointListHolder.getBmsClusterNum(data[pointListHolder.getEmsTypeIndex()])];
            currentChargeCapacity +=
                    data[
                            pointListHolder.getBmsChargeableEnergy(
                                    data[pointListHolder.getEmsTypeIndex()])];
            currentDischargeCapacity +=
                    data[
                            pointListHolder.getBmsDischargeableEnergy(
                                    data[pointListHolder.getEmsTypeIndex()])];
            bmsDesignCapacity +=
                    data[pointListHolder.getEmsCapacity(data[pointListHolder.getEmsTypeIndex()])];
            soc +=
                    data[pointListHolder.getSoc(data[pointListHolder.getEmsTypeIndex()])]
                            * data[
                                    pointListHolder.getEmsCapacity(
                                            data[pointListHolder.getEmsTypeIndex()])];
            soh +=
                    data[pointListHolder.getSoh(data[pointListHolder.getEmsTypeIndex()])]
                            * data[
                                    pointListHolder.getEmsCapacity(
                                            data[pointListHolder.getEmsTypeIndex()])];

            double point =
                    ((double)
                                    (short)
                                            data[
                                                    pointListHolder.getEmsAcActivePower(
                                                            data[
                                                                    pointListHolder
                                                                            .getEmsTypeIndex()])])
                            / 10;
            if (point > 0) {
                // 放电
                emsOutPower += point;
            } else {
                // 充电
                emsInPower += Math.abs(point);
            }
            // 是否有机器开机
            if (data[pointListHolder.getSystemRunStatus(data[pointListHolder.getEmsTypeIndex()])]
                    > 0) {
                hasEmsRun = true;
            }
        }
        basicVO.setPscNum(pscNum);
        basicVO.setEmsDesignPower(emsDesignPower);
        basicVO.setEmsChargePower(emsChargePower);
        basicVO.setEmsDischargePower(emsDischargePower);
        basicVO.setEmsCapacity(emsCapacity);
        basicVO.setBmsCluster(bmsCluster);
        basicVO.setCurrentChargeCapacity(currentChargeCapacity);
        basicVO.setCurrentDischargeCapacity(currentDischargeCapacity);
        if (bmsDesignCapacity != 0) {
            basicVO.setSOC(soc / (10 * bmsDesignCapacity));
            basicVO.setSOH(soh / (10 * bmsDesignCapacity));
        }

        // 2023-10-30 16:09:56 添加一个EmsStatus的获取 用于前端显示 @see EmsStatusEnum
        basicVO.setEmsInPower(emsInPower);
        basicVO.setEmsOutPower(emsOutPower);
        int emsStatus =
                EmsUtil.getEmsStatus(
                        emsDesignPower, emsInPower, emsOutPower, isOff, hasEmsRun, null);
        basicVO.setEmsStatus(emsStatus);
        return basicVO;
    }
}
