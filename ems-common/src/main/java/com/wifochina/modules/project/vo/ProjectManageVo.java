package com.wifochina.modules.project.vo;

import com.wifochina.modules.investor.entity.InvestorEntity;
import com.wifochina.modules.operation.ManageProjectOperation;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.user.entity.UserEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-19
 */
@Data
public class ProjectManageVo extends ProjectEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目地点")
    private String area;

    @ApiModelProperty(value = "项目规模")
    private String size;

    @ApiModelProperty(value = "项目详细地址")
    private String address;

    @ApiModelProperty(value = "用电类型id")
    private String powerTypeId;

    @ApiModelProperty(value = "用电类型")
    private String powerType;

    @ApiModelProperty(value = "用电类型英文")
    private String powerTypeEn;

    @ApiModelProperty(value = "电压等级id")
    private String powerLevelId;

    @ApiModelProperty(value = "电压等级")
    private String powerLevel;

    @ApiModelProperty(value = "项目经度")
    private Double longitude;

    @ApiModelProperty(value = "项目维度")
    private Double latitude;

    @ApiModelProperty(value = "项目备注")
    private String remark;

    @ApiModelProperty(value = "ems数量")
    private Integer ems;

    @ApiModelProperty(value = "pcs数量")
    private Integer pcs;

    @ApiModelProperty(value = "bms数量")
    private Integer bms;

    @ApiModelProperty(value = "项目标识")
    private String alias;

    @ApiModelProperty(value = "是否采集光伏数据:false 不采集,true采集")
    private Boolean enableRadiation;

    @ApiModelProperty(value = "是否采集天气数据:false 不采集,true采集")
    private Boolean enableWeather;

    @ApiModelProperty(value = "预测是否采集数据:false 不采集,true采集")
    private Boolean enableEtl;

    @ApiModelProperty(value = "是否维护，true是，false 否")
    private Boolean maintain;

    @ApiModelProperty(value = "用户列表")
    private List<UserEntity> userList;

    @ApiModelProperty(value = "未确认告警和故障")
    private Long unConfirmError;

    @ApiModelProperty(value = "资方")
    private InvestorEntity investor;

    @ApiModelProperty(value = "是否开启vpp")
    private Boolean hasVpp = false;

    @ApiModelProperty(value = "是否开启调频")
    private Boolean hasFr = false;

    @ApiModelProperty(value = "管理端相关项目收益")
    private ManageProjectOperation manageProjectOperation;

    @ApiModelProperty(value = "控容：1、控需：2、其他：0")
    private List<Integer> controlType;

}
