package com.wifochina.modules.project.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SiteVO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 5/7/2022 5:01 PM
 */
@Data
public class SiteVO {

    @ApiModelProperty(value = "外部pv功率")
    private Double pvExternalPower;

    @ApiModelProperty(value = "外部pv日发电量")
    private Double pvExternalDaily;

    @ApiModelProperty(value = "外部pv总发电量")
    private Double pvExternalTotal;

    @ApiModelProperty(value = "pv功率")
    private double pvPower;

    @ApiModelProperty(value = "pv总发电量")
    private Double pvTotal;

    @ApiModelProperty(value = "pv日发电量")
    private Double pvDaily;

    @ApiModelProperty(value = "电网功率")
    private Double gridOutPower;

    @ApiModelProperty(value = "电网总用电量")
    private Double gridOutTotal;

    @ApiModelProperty(value = "电网总馈网量")
    private Double gridInTotal;

    @ApiModelProperty(value = "电网日馈网量")
    private Double gridInDaily;

    @ApiModelProperty(value = "电网日用电量")
    private Double gridOutDaily;

    @ApiModelProperty(value = "ems充电功率")
    private Double emsInPower;

    // 2025-06-05 17:19:05 added for ems 无功
    @ApiModelProperty(value = "ems无功冲功率")
    private Double emsAcReactiveInPower;

    @ApiModelProperty(value = "ems无功放功率")
    private Double emsAcReactiveOutPower;

    // ----------

    @ApiModelProperty(value = "ems日充电量")
    private Double emsInDaily;

    @ApiModelProperty(value = "ems充电总量")
    private Double emsInTotal;

    @ApiModelProperty(value = "ems放电功率")
    private Double emsOutPower;

    @ApiModelProperty(value = "ems日放电量")
    private Double emsOutDaily;

    @ApiModelProperty(value = "ems放电总量")
    private Double emsOutTotal;

    @ApiModelProperty(value = "ems状态 0离线 1待机 2充电 3放电")
    private Integer emsStatus;

    @ApiModelProperty(value = "负载功率")
    private Double loadPower;

    @ApiModelProperty(value = "soc值")
    private Double soc;

    @ApiModelProperty(value = "最后更新时间")
    private Long updateTime;

    @ApiModelProperty(value = "设备运行情况")
    private String totalRunStatus;

    @ApiModelProperty(value = "设备维护情况")
    private String totalMaintainStatus;

    @ApiModelProperty(value = "风电功率")
    private Double windPower;

    @ApiModelProperty(value = "柴油发电功率")
    private Double dieselPower;

    @ApiModelProperty(value = "充电桩充电功率")
    private Double pilePower;

    @ApiModelProperty(value = "燃气发电功率")
    private Double gasPower;

    @ApiModelProperty(value = "风电发电日总量")
    private Double windDailySum;

    @ApiModelProperty(value = "柴油发电日总量")
    private Double dieselDailySum;

    @ApiModelProperty(value = "充电桩充电日总量")
    private Double pileDailySum;

    @ApiModelProperty(value = "燃气发电日总量")
    private Double gasDailySum;

    @ApiModelProperty(value = "风电发电日总量")
    private Double windTotal;

    @ApiModelProperty(value = "柴油发电日总量")
    private Double dieselTotal;

    @ApiModelProperty(value = "充电桩充电总量")
    private Double pileTotal;

    @ApiModelProperty(value = "燃气发电总量")
    private Double gasTotal;

    // ---------- add 2024-01-11 10:09:02 for dcdc

    @ApiModelProperty(value = "dcdc电池放电功率")
    private Double dcdcBatteryOutPower;

    @ApiModelProperty(value = "dcdc电池充电功率")
    private Double dcdcBatteryInPower;

    @ApiModelProperty(value = "dcdc的Pv功率")
    private Double dcdcTotalPv;

    @ApiModelProperty(value = "dcdc的光伏总发电量")
    private Double dcdcOutTotal;

    @ApiModelProperty(value = "dcdc的光伏日发电量")
    private Double dcdcDailyOutTotal;

    @ApiModelProperty(value = "dcdc的电池日放电量")
    private Double dcdcBatteryDailyOutSum;

    @ApiModelProperty(value = "dcdc的电池日充电量")
    private Double dcdcBatteryDailyInSum;

    @ApiModelProperty(value = "dcdc的电池总放电量")
    private Double dcdcBatteryOutTotal;

    @ApiModelProperty(value = "dcdc的电池总充电量")
    private Double dcdcBatteryInTotal;

    @ApiModelProperty(value = "交流功率")
    private Double diffPower;

    @ApiModelProperty(value = "无功功率")
    private Double diffAcReactivePower;

    private Double emsMonthOutSum;
    private Double emsMonthInSum;

    // 余热点相关 2024-03-11 15:35:05
    @ApiModelProperty(value = "余热发电pv功率")
    private Double wasterPower;

    @ApiModelProperty(value = "余热发电日发电量")
    private Double wasterDaily;

    @ApiModelProperty(value = "余热发电总发电量")
    private Double wasterOutTotal;
}
