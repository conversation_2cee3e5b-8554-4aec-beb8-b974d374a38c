package com.wifochina.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.project.entity.ProjectUserRemarkEntity;
import com.wifochina.modules.project.mapper.ProjectUserRemarkMapper;
import com.wifochina.modules.project.service.ProjectUserRemarkService;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-28 15:41:40
 */
@Service
public class ProjectUserRemarkServiceImpl
        extends ServiceImpl<ProjectUserRemarkMapper, ProjectUserRemarkEntity>
        implements ProjectUserRemarkService {
    @Override
    public Map<String, ProjectUserRemarkEntity> findUserRemarkBy(
            String userId, List<String> projectIds) {

        if (projectIds.isEmpty() || StringUtil.isEmpty(userId)) {
            return new HashMap<>();
        }
        List<ProjectUserRemarkEntity> projectUserRemarkEntities =
                this.baseMapper.selectList(
                        new LambdaQueryWrapper<ProjectUserRemarkEntity>()
                                .eq(ProjectUserRemarkEntity::getUserId, userId)
                                .in(ProjectUserRemarkEntity::getProjectId, projectIds));

        return projectUserRemarkEntities.stream()
                .collect(Collectors.toMap(ProjectUserRemarkEntity::getProjectId, v -> v));
    }
}
