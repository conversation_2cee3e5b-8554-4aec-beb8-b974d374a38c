package com.wifochina.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.project.entity.ProjectUserRemarkEntity;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-04-28 15:40:42
 */
public interface ProjectUserRemarkService extends IService<ProjectUserRemarkEntity> {

    Map<String, ProjectUserRemarkEntity> findUserRemarkBy(String userId, List<String> projectIds);
}
