package com.wifochina.modules.project.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.util.OnOffGridRunEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-19
 */
@Data
@ApiModel(value = "管理端-项目创建")
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectManageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id", required = true)
    private String id;

    @ApiModelProperty(value = "项目名称", required = true)
    private String projectName;

    @ApiModelProperty(value = "场站版项目ip", required = true)
    private String projectHost;

    @ApiModelProperty(value = "场站版项目端口", required = true)
    private Integer projectPort;

    @ApiModelProperty(value = "项目模式 1场站模式 0云模式", required = true)
    private Integer projectModel;

    @ApiModelProperty(value = "是否可以访问本地,默认false 不可以访问")
    private Boolean whetherAccessible;

    /**
     * 是否备电 默认false, 开启true
     *
     * @see OnOffGridRunEnum
     */
    @ApiModelProperty(
            value =
                    "并离网切换的4种模式 on_grid_run, off_grid_run, on_off_grid_run_manual, on_off_grid_run_auto")
    private String whetherBackup;

    @ApiModelProperty(value = "本地项目id")
    private String localProjectId;

    @ApiModelProperty(value = "电价托管")
    private Boolean priceProxy;

    @ApiModelProperty(value = "云端大屏展示")
    private Boolean cloudLargeScreen;

    @ApiModelProperty(value = "项目大屏开关")
    private Boolean largeScreen;

    @ApiModelProperty(value = "项目区域id")
    private String projectDeviceAlias;

    @ApiModelProperty(value = "项目版本")
    private String version;

    @ApiModelProperty(value = "项目地点")
    private String area;

    @ApiModelProperty(value = "项目规模")
    private String size;

    @ApiModelProperty(value = "项目详细地址")
    private String address;

    @ApiModelProperty(value = "用电类型")
    private String powerType;

    @ApiModelProperty(value = "电压等级")
    private String powerLevel;

    @ApiModelProperty(value = "项目币种 人民币 rmb 美元 dollar")
    private String currency;

    @ApiModelProperty(value = "项目经度")
    private Double longitude;

    @ApiModelProperty(value = "项目维度")
    private Double latitude;

    @ApiModelProperty(value = "项目备注")
    private String remark;

    @ApiModelProperty(value = "项目标识")
    private String alias;

    @ApiModelProperty(value = "所在省份")
    private Integer province;

    @ApiModelProperty(value = "是否采集光伏数据:false 不采集,true采集")
    private Boolean enableRadiation;

    @ApiModelProperty(value = "是否采集天气数据:false 不采集,true采集")
    private Boolean enableWeather;

    @ApiModelProperty(value = "预测是否采集数据:false 不采集,true采集")
    private Boolean enableEtl;

    @ApiModelProperty("创建时间或者投运时间")
    private Long createTime;

    @ApiModelProperty("国家")
    private Integer country;

    @ApiModelProperty("项目所在分区")
    private Integer dataCenter;

    @ApiModelProperty("项目时区")
    private String timezone;

    @ApiModelProperty("是否开启告警")
    private Boolean whetherAlarm;

    @ApiModelProperty("资方列表")
    private List<String> investorIds;

    @ApiModelProperty(value = "分时缓存")
    /** 分时缓存 */
    private Boolean timeSharingCache;

    /**
     * @see ElectricPriceTypeEnum 电价类型字段
     */
    @ApiModelProperty("电价类型字段")
    private String electricPriceType;

    /** 电价区域字段 */
    @ApiModelProperty("电价区域")
    private String electricPriceArea;

    /** 电价跨度字段 */
    @ApiModelProperty("电价跨度")
    private String electricPriceSpan;

    @ApiModelProperty("策略模版id")
    private Long strategyTemplateId;
}
