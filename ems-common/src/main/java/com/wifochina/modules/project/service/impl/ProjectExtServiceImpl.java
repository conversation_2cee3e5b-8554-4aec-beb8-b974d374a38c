package com.wifochina.modules.project.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.project.vo.ProjectManageVo;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.mapper.ProjectExtMapper;
import com.wifochina.modules.project.request.ProjectManageListRequest;
import com.wifochina.modules.project.service.ProjectExtService;

import cn.hutool.core.collection.CollectionUtil;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
public class ProjectExtServiceImpl extends ServiceImpl<ProjectExtMapper, ProjectExtEntity>
    implements ProjectExtService {

    @Override
    public IPage<ProjectManageVo> queryProjectManageList(IPage<ProjectManageVo> projectPage,
        ProjectManageListRequest projectManageListRequest) {
        return this.baseMapper.queryProjectManageList(projectPage, projectManageListRequest);
    }

    @Override
    public ProjectManageVo queryProjectManageInfo(String id) {
        return this.baseMapper.queryProjectManageInfo(id);
    }

    @Override
    public List<ProjectManageVo> queryAllNotDeleteProjectSize(List<String> projectIds) {
        if (CollectionUtil.isNotEmpty(projectIds)) {
            return this.baseMapper.queryAllNotDeleteProjectSize(projectIds);
        } else {
            return new ArrayList<>();
        }
    }

}
