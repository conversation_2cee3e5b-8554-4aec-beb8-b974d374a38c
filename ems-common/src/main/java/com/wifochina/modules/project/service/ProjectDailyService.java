package com.wifochina.modules.project.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-05-11 4:56 PM
 */
public interface ProjectDailyService {
    Map<String, Double> getEmsPower(Long start, Long end, String column);

    Double getDailyPower(Long start, Long end, String column, String type, List<String> itemIds);

    Double getDailyPowerProject(Long start, Long end, String column, String type, List<String> itemIds, String projectId);

    /**
     * 查询时间范围内的 按照 day 进行 group by 的数据
     * @param start : 开始时间
     * @param end : 结束时间
     * @param column : 查询的列
     * @param tableName : 查询的表
     * @param itemIds : 查询的设备
     * @return : Map<Long, Double> : key 为时间戳，value 为对应的值
     */
    Map<Long, Double> getDaysPowerForEffeciency(Long start, Long end, String column, String tableName, List<String> itemIds, String projectId);

}
