package com.wifochina.modules.project.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created on 2024/4/28 15:36.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode()
@TableName("t_project_user_remark")
public class ProjectUserRemarkEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    private String remark;

    private String userId;

    private String projectId;

    private String userName;

    private String role;
}
