package com.wifochina.modules.project.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-03-19
 */
@Data
public class ProjectUrlRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "附加url")
    private String url;

    @ApiModelProperty(value = "url排序")
    private Integer orderNum;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "字段")
    private String remark;

}
