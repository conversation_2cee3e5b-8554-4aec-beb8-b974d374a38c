package com.wifochina.modules.project.vo;

import com.wifochina.modules.project.entity.ProjectEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023-11-16 3:20 PM
 */
@Setter
@Getter
public class ProjectVo extends ProjectEntity {

    @ApiModelProperty(value = "是否有监控电表")
    private Boolean hasControllable;

    @ApiModelProperty(value = "是否有摄像头")
    private Boolean hasCamera;

    @ApiModelProperty(value = "是否有电表")
    private Boolean hasMeter;

    @ApiModelProperty(value = "是否有Dcdc")
    private Boolean hasDcdc;

    @ApiModelProperty(value = "是否有状态设备")
    private Boolean hasSts;

    @ApiModelProperty(value = "是否有可控设备")
    private Boolean hasSteerable;

    @ApiModelProperty(value = "是否有变压器测控")
    private Boolean hasTransformerTemperatureSteerable;

    @ApiModelProperty(value = "是否调频")
    private Boolean hasFr;

    @ApiModelProperty(value = "是否虚拟电场")
    private Boolean hasVpp;
}
