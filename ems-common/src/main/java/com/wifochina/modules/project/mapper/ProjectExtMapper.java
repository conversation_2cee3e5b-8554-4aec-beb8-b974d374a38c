package com.wifochina.modules.project.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wifochina.modules.project.vo.ProjectManageVo;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.request.ProjectManageListRequest;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface ProjectExtMapper extends BaseMapper<ProjectExtEntity> {

    /**
     * query Project Manage List
     * @param projectPage page
     * @param projectManageListRequest request
     * @return IPage<ProjectManageVo>
     */
    IPage<ProjectManageVo> queryProjectManageList(IPage<ProjectManageVo> projectPage,
        ProjectManageListRequest projectManageListRequest);

    /**
     * query Project Manage Info
     * @param id id
     * @return ProjectManageVo
     */
    ProjectManageVo queryProjectManageInfo(String id);

    /**
     * 根据指定的 项目ids 查询 未删除的项目 并且关联项目的 规模到ProjectManageVo
     * @param projectIds 指定的projectIds
     * @return List<ProjectManageVo>
     */
    List<ProjectManageVo> queryAllNotDeleteProjectSize(List<String> projectIds);

}
