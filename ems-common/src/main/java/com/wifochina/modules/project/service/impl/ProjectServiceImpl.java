package com.wifochina.modules.project.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.util.ApplicationHolder;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.EquipmentSearchService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.mapper.ProjectMapper;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, ProjectEntity>
        implements ProjectService {

    private final GroupService groupService;
    private final EquipmentSearchService equipmentSearchService;

    @Override
    public List<ProjectEntity> getCloudProjectList() {
        return this.lambdaQuery()
                .eq(ProjectEntity::getWhetherDelete, false)
                .ne(ProjectEntity::getProjectModel, 1)
                .list();
    }

    @Override
    public List<ProjectEntity> getProjectsByTimeZone(String timezone) {
        List<ProjectEntity> projectEntities =
                this.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list();
        if (projectEntities.size() > 1) {
            // 云项目
            projectEntities =
                    projectEntities.stream()
                            .filter(projectEntity -> projectEntity.getProjectModel() != 1)
                            .filter(projectEntity -> timezone.equals(projectEntity.getTimezone()))
                            .collect(Collectors.toList());
        }
        return projectEntities;
    }

    @Override
    public List<ProjectEntity> getProjectsByTimeZoneAndPriceType(
            String timezone, String priceType) {
        List<ProjectEntity> projectEntities =
                this.lambdaQuery()
                        .eq(ProjectEntity::getWhetherDelete, false)
                        .eq(ProjectEntity::getElectricPriceType, priceType)
                        .list();
        projectEntities =
                projectEntities.stream()
                        .filter(projectEntity -> timezone.equals(projectEntity.getTimezone()))
                        .collect(Collectors.toList());
        return projectEntities;
    }

    @Override
    public List<String> getAllTimeZone() {
        List<ProjectEntity> projectEntities =
                this.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list();
        return projectEntities.stream()
                .map(ProjectEntity::getTimezone)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 1.4.2 根据项目ids 查询这些项目 是否满足 开启了收益 <br>
     * 这个开启了收益 是值 systemGroup 开启了收益 且 存在 电表or设备打了收益标签的 才属于 开启了收益
     *
     * @param projectIds : projectIds
     * @return : Map<String,Boolean>
     */
    @Override
    public Map<String, Boolean> getIncomeOpenStatusByProjectIds(List<String> projectIds) {
        Map<String, Boolean> incomeOpenStatusMap = new HashMap<>();
        List<GroupEntity> groupEntities = groupService.systemGroupEntitys(projectIds);
        // 这一部分就是 系统收益都没开 系统分组的
        groupEntities.stream()
                .filter(group -> !Boolean.TRUE.equals(group.getCalcEarningsController()))
                .forEach(group -> incomeOpenStatusMap.put(group.getProjectId(), false));

        // 系统分组开了收益开关的 项目 ids
        List<String> systemGroupOpenIncomeProjectIds =
                projectIds.stream()
                        .filter(projectId -> !incomeOpenStatusMap.containsKey(projectId))
                        .collect(Collectors.toList());

        Map<String, List<String>> incomeDeviceAmmeterByProjectIds =
                equipmentSearchService.getIncomeDeviceAmmeterByProjectIds(
                        systemGroupOpenIncomeProjectIds);
        incomeDeviceAmmeterByProjectIds.forEach(
                (projectId, equipments) -> {
                    if (!equipments.isEmpty()) {
                        incomeOpenStatusMap.put(projectId, true);
                    } else {
                        // 如果没有开了收益标签的 电表或者ems设备 则 这个状态标记为 false
                        incomeOpenStatusMap.put(projectId, false);
                    }
                });
        return incomeOpenStatusMap;
    }

    /**
     * 获取项目列表 根据 服务部署状态
     *
     * @return : site or cloud projects
     */
    @Override
    public List<ProjectEntity> getAllProjectByServiceDeployStatus() {
        if (ApplicationHolder.isCloudDeployType()) {
            return this.lambdaQuery()
                    .eq(ProjectEntity::getWhetherDelete, false)
                    .ne(ProjectEntity::getProjectModel, 1)
                    .list();
        } else {
            return this.lambdaQuery()
                    .eq(ProjectEntity::getWhetherDelete, false)
                    .eq(ProjectEntity::getProjectModel, 1)
                    .list();
        }
    }
}
