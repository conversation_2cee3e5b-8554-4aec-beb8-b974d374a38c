package com.wifochina.modules.project.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BasicVo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 5/9/2022 2:21 PM
 */
@Data
public class BasicVO {

    @ApiModelProperty(value = "电站额定功率")
    private Double emsDesignPower;

    @ApiModelProperty(value = "电站可充功率")
    private Double emsChargePower;

    @ApiModelProperty(value = "电站可放功率")
    private Double emsDischargePower;

    @ApiModelProperty(value = "电站额定容量")
    private Double emsCapacity;

    @ApiModelProperty(value = "电站pcs数量")
    private Double pscNum;

    @ApiModelProperty(value = "电站电池簇数量")
    private Double bmsCluster;

    @ApiModelProperty(value = "当前可充容量")
    private Double currentChargeCapacity;

    @ApiModelProperty(value = "当前可放容量")
    private Double currentDischargeCapacity;

    @ApiModelProperty(value = "SOH")
    private Double SOH;

    @ApiModelProperty(value = "SOC")
    private Double SOC;

    @ApiModelProperty(value = "Ems状态")
    private Integer emsStatus;

    @ApiModelProperty(value = "ems充电功率")
    private Double emsInPower;

    @ApiModelProperty(value = "ems放电功率")
    private Double emsOutPower;

    public BasicVO() {
        emsDesignPower = 0d;
        emsChargePower = 0d;
        emsDischargePower = 0d;
        emsCapacity = 0d;
        pscNum = 0d;
        bmsCluster = 0d;
        currentChargeCapacity = 0d;
        currentDischargeCapacity = 0d;
        SOH = 0d;
        SOC = 0d;
        emsStatus = 0;
        emsInPower = 0d;
        emsOutPower = 0d;
    }
}
