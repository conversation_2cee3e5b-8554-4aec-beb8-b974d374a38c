package com.wifochina.modules.project.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.project.vo.ProjectManageVo;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.request.ProjectManageListRequest;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface ProjectExtService extends IService<ProjectExtEntity> {

    /**
     * query Project Manage List
     * @param projectPage page
     * @param projectManageListRequest request
     * @return IPage<ProjectManageVo>
     */
    IPage<ProjectManageVo> queryProjectManageList(IPage<ProjectManageVo> projectPage,
        ProjectManageListRequest projectManageListRequest);

    /**
     * query Project Manage Info
     * @param id id
     * @return ProjectManageVo
     */
    ProjectManageVo queryProjectManageInfo(String id);

    /**
     * query All Not Delete Project Size
     * @param projectIds ids
     * @return List<ProjectManageVo>
     */
    List<ProjectManageVo> queryAllNotDeleteProjectSize(List<String> projectIds);
}
