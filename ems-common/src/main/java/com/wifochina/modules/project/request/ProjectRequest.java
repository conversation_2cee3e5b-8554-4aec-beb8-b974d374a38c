package com.wifochina.modules.project.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Data
public class ProjectRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称", required = true)
    private String projectName;

    @ApiModelProperty(value = "场站版项目ip", required = true)
    private String projectHost;

    @ApiModelProperty(value = "场站版项目端口", required = true)
    private Integer projectPort;

    @ApiModelProperty(value = "项目模式 1场站模式 2云模式", required = true)
    private Integer projectModel;

    @ApiModelProperty(value = "项目区域id")
    private String projectDeviceAlias;

    @ApiModelProperty(value = "项目时区 东区为正，西区为负")
    private Integer duration;

    @ApiModelProperty(value = "项目版本")
    private String version;

}
