package com.wifochina.modules.project.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsStatusEnum;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.modules.data.entity.MeterContentData;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.initelectric.InitElectricService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.VO.ElectricVo;
import com.wifochina.modules.operation.timer.TaskElectricTimer;
import com.wifochina.modules.project.request.ZeroPowerContext;
import com.wifochina.modules.project.service.ProjectDailyService;
import com.wifochina.modules.project.service.ProjectScreenService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.project.vo.EmsVO;
import com.wifochina.modules.project.vo.SiteVO;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.checkerframework.checker.units.qual.A;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-05-11 4:49 PM
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectScreenServiceImpl implements ProjectScreenService {

    private final AmmeterService ammeterService;

    private final DeviceService deviceService;

    private final GroupService groupService;

    private final GroupAmmeterService groupAmmeterService;

    private final DataService dataService;

    private final RedisTemplate<String, String> redisTemplate;

    private final ProjectDailyService projectDailyService;

    private final ProjectService projectservice;
    private final PointListHolder pointListHolder;

    private final InitElectricService initElectricService;

    private static final String DEVICE = "device:";
    private static final String SERIAL = "serial";
    private static final String UNKNOWN = "unknown";

    @Override
    public SiteVO getSite(Long start) {
        String key = "site:" + WebUtils.projectId.get();
        String invalidKey = "invalid:site:" + WebUtils.projectId.get();
        SiteVO invalidSiteVo =
                JSON.parseObject(redisTemplate.opsForValue().get(invalidKey), SiteVO.class);
        if (invalidSiteVo != null) {
            return invalidSiteVo;
        }
        SiteVO lastSiteVO = JSON.parseObject(redisTemplate.opsForValue().get(key), SiteVO.class);
        long now = Instant.now().getEpochSecond();
        int updatePeriod = 5;
        int delayTimes = 3;
        long updateTime;
        long diff;
        updateTime = lastSiteVO == null ? 0 : (long) lastSiteVO.getUpdateTime();
        diff = now - updateTime;
        if (diff < updatePeriod) {
            return lastSiteVO;
        }

        List<AmmeterEntity> ammeterEntities =
                ammeterService.list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get()));
        GroupEntity systemGroupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                                .eq(GroupEntity::getWhetherSystem, true));
        // 可能有些openapi调用穿的项目id 是不存在的
        if (systemGroupEntity == null) {
            log.error(
                    "[system group is null projectId:{} set invalid siteVo ttl 30 minutes]",
                    WebUtils.projectId.get());
            SiteVO siteVO = new SiteVO();
            redisTemplate
                    .opsForValue()
                    .set(invalidKey, JSON.toJSONString(siteVO), 30, TimeUnit.MINUTES);
            return siteVO;
        }
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService.list(
                        Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                                .eq(GroupAmmeterEntity::getGroupId, systemGroupEntity.getId()));
        List<String> sysAmmeterIds =
                groupAmmeterEntities.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        List<String> sysDeviceIds =
                deviceService.getGroupDeviceNotUnrealIds(systemGroupEntity.getId());
        double pvPower;
        double pvExternalPower = 0;
        double windPower = 0;
        double dieselPower = 0;
        double pilePower = 0;
        double gasPower = 0;
        double pvTotal = 0;
        double pvExternalTotal = 0;
        double gridOutPower = 0;
        double gridOutTotal = 0;
        double gridInTotal = 0;
        double windTotal = 0;
        double dieselTotal = 0;
        double pileTotal = 0;
        double gasTotal = 0;
        double wasterOutTotal = 0;
        double wasterPower = 0;
        double emsOutTotal = 0;
        double emsInTotal = 0;

        // true代表 收益电表，false代表ems
        List<String> incomeDeviceIds = new ArrayList<>();
        List<String> incomeMeterIds = new ArrayList<>();
        CompletableFuture<Double> emsDailyOutEnergyFuture = null;
        CompletableFuture<Double> emsDailyInEnergyFuture = null;
        CompletableFuture<Double> ammeterDailyOutEnergyFuture = null;
        CompletableFuture<Double> ammeterDailyInEnergyFuture = null;
        if (systemGroupEntity.getShowType()) {
            List<DeviceEntity> incomeDeviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getIncome, true)
                            .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                            .list();
            if (!incomeDeviceEntities.isEmpty()) {
                incomeDeviceIds =
                        incomeDeviceEntities.stream()
                                .map(DeviceEntity::getId)
                                .collect(Collectors.toList());
                List<String> finalIncomeDeviceIds = incomeDeviceIds;
                String projectId = WebUtils.projectId.get();
                emsDailyOutEnergyFuture =
                        CompletableFuture.supplyAsync(
                                () -> {
                                    WebUtils.projectId.set(projectId);
                                    return projectDailyService.getDailyPower(
                                            start,
                                            now,
                                            EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field(),
                                            null,
                                            finalIncomeDeviceIds);
                                });
                emsDailyInEnergyFuture =
                        CompletableFuture.supplyAsync(
                                () -> {
                                    WebUtils.projectId.set(projectId);
                                    return projectDailyService.getDailyPower(
                                            start,
                                            now,
                                            EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field(),
                                            null,
                                            finalIncomeDeviceIds);
                                });
            }
            List<AmmeterEntity> incomeAmmeterEntities =
                    ammeterService
                            .lambdaQuery()
                            .eq(AmmeterEntity::getIncome, true)
                            .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                            .list();
            if (!incomeAmmeterEntities.isEmpty()) {

                incomeMeterIds =
                        incomeAmmeterEntities.stream()
                                .map(AmmeterEntity::getId)
                                .collect(Collectors.toList());
                List<String> finalIncomeMeterIds = incomeMeterIds;
                String projectId = WebUtils.projectId.get();
                ammeterDailyOutEnergyFuture =
                        CompletableFuture.supplyAsync(
                                () -> {
                                    WebUtils.projectId.set(projectId);
                                    return projectDailyService.getDailyPower(
                                            start,
                                            now,
                                            "ac_history_positive_power_in_kwh",
                                            "meter",
                                            finalIncomeMeterIds);
                                });

                ammeterDailyInEnergyFuture =
                        CompletableFuture.supplyAsync(
                                () -> {
                                    WebUtils.projectId.set(projectId);
                                    return projectDailyService.getDailyPower(
                                            start,
                                            now,
                                            "ac_history_negative_power_in_kwh",
                                            "meter",
                                            finalIncomeMeterIds);
                                });
            }
        } else {
            String projectId = WebUtils.projectId.get();
            emsDailyOutEnergyFuture =
                    CompletableFuture.supplyAsync(
                            () -> {
                                WebUtils.projectId.set(projectId);
                                return projectDailyService.getDailyPower(
                                        start,
                                        now,
                                        EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field(),
                                        null,
                                        sysDeviceIds);
                            });
            emsDailyInEnergyFuture =
                    CompletableFuture.supplyAsync(
                            () -> {
                                WebUtils.projectId.set(projectId);
                                return projectDailyService.getDailyPower(
                                        start,
                                        now,
                                        EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field(),
                                        null,
                                        sysDeviceIds);
                            });
        }

        Map<String, MeterContentData> meterMap =
                dataService.collectMeterDataPoint(WebUtils.projectId.get());
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            MeterContentData meterContentData = meterMap.get(ammeterEntity.getId());
            if (meterContentData != null) {
                if (systemGroupEntity.getShowType()) {
                    if (incomeMeterIds.contains(ammeterEntity.getId())) {
                        // 别问为什么
                        emsOutTotal += meterContentData.getAc_history_positive_power_in_kwh();
                        emsOutTotal =
                                initElectricService.reduceInit(
                                        emsOutTotal, EmsConstants.OUT, ammeterEntity);
                        emsInTotal += meterContentData.getAc_history_negative_power_in_kwh();
                        emsInTotal =
                                initElectricService.reduceInit(
                                        emsInTotal, EmsConstants.IN, ammeterEntity);
                    }
                }
            } else {
                log.error(
                        "meterContentData is null in site  projectId:{},  meterId: {}",
                        WebUtils.projectId.get(),
                        ammeterEntity.getId());
            }
            if (!sysAmmeterIds.contains(ammeterEntity.getId())) {
                continue;
            }
            if (meterContentData != null) {
                switch (Objects.requireNonNull(
                        MeterTypeEnum.getMeterType(ammeterEntity.getType()))) {
                    case PV:
                        // Pv发电
                        pvExternalTotal += meterContentData.getAc_history_positive_power_in_kwh();
                        pvExternalPower += meterContentData.getAc_active_power() / 1000;
                        break;
                    case GRID:
                        // 电网取点
                        gridOutTotal += meterContentData.getAc_history_negative_power_in_kwh();
                        gridOutPower += meterContentData.getAc_active_power() / 1000;
                        // 电网馈电
                        gridInTotal += meterContentData.getAc_history_positive_power_in_kwh();
                        break;
                    case LOAD:
                        // 负载电表
                        break;
                    case WIND:
                        // 风电
                        windTotal += meterContentData.getAc_history_positive_power_in_kwh();
                        windPower += meterContentData.getAc_active_power() / 1000;
                        break;
                    case DIESEL:
                        // 柴油
                        dieselTotal += meterContentData.getAc_history_positive_power_in_kwh();
                        dieselPower += meterContentData.getAc_active_power() / 1000;
                        break;
                    case PILE:
                        // 充电桩
                        pileTotal += meterContentData.getAc_history_positive_power_in_kwh();
                        pilePower += meterContentData.getAc_active_power() / 1000;
                        break;
                    case COMMON:
                        break;
                    case GAS:
                        gasTotal += meterContentData.getAc_history_positive_power_in_kwh();
                        gasPower += meterContentData.getAc_active_power() / 1000;
                        break;
                    case WASTER:
                        // 2024-03-11 15:04:53 add 余热发电的 功率 和 总发电量
                        wasterOutTotal += meterContentData.getAc_history_positive_power_in_kwh();
                        wasterPower += meterContentData.getAc_active_power() / 1000;
                        break;
                    default:
                }
            }
        }
        double emsOutPower = 0;
        double emsInPower = 0;
        double emsReactiveOutPower = 0;
        double emsReactiveInPower = 0;
        double designPower = 0;
        double bmsDesignCapacity = 0;
        double soc = 0;
        double pvExternalDailySum = 0;
        double gridDailyOutSum = 0;
        double gridDailyInSum = 0;
        double windDailySum = 0;
        double dieselDailySum = 0;
        double pileDailySum = 0;
        double gasDailySum = 0;

        // dcdc光伏总发电量
        double dcdcTotalPv = 0;
        double dcdcDailyOutTotal = 0;
        double dcdcOutTotal = 0;
        double diffDcdcBatteryPv = 0;
        double dcdcBatteryOutTotal = 0;
        double dcdcBatteryInTotal = 0;
        double dcdcBatteryDailyOutSum = 0;
        double dcdcBatteryDailyInSum = 0;

        List<DeviceEntity> deviceEntities = new ArrayList<>();
        if (!sysDeviceIds.isEmpty()) {
            deviceEntities =
                    deviceService.list(
                            Wrappers.lambdaQuery(DeviceEntity.class)
                                    .in(DeviceEntity::getId, sysDeviceIds)
                                    .eq(DeviceEntity::getProjectId, WebUtils.projectId.get()));
        }
        boolean isOff = true;
        boolean hasEmsRun = false;
        int runEmsCount = 0;

        // 2024-01-10 19:17:32 add 只要有一个设备开启了dcdc 就算做开启了dcdc
        boolean hasDcdc = deviceEntities.stream().anyMatch(DeviceEntity::getHasDcdc);

        Boolean homePageReactivePowerController =
                systemGroupEntity.getHomePageReactivePowerController();
        for (DeviceEntity deviceEntity : deviceEntities) {
            int[] data = dataService.get(deviceEntity.getId());
            if (data == null) {
                continue;
            } else {
                isOff = false;
            }
            int emsType = data[42];
            if (systemGroupEntity.getShowType()) {
                if (incomeDeviceIds.contains(deviceEntity.getId())) {
                    emsOutTotal = getEmsOutTotal(emsOutTotal, data);
                    emsOutTotal =
                            initElectricService.reduceInit(
                                    emsOutTotal, EmsConstants.OUT, deviceEntity);
                    emsInTotal = getEmsInTotal(emsInTotal, data);
                    emsInTotal =
                            initElectricService.reduceInit(
                                    emsInTotal, EmsConstants.IN, deviceEntity);
                }
            } else {
                emsOutTotal = getEmsOutTotal(emsOutTotal, data);
                emsOutTotal =
                        initElectricService.reduceInit(emsOutTotal, EmsConstants.OUT, deviceEntity);
                emsInTotal = getEmsInTotal(emsInTotal, data);
                emsInTotal =
                        initElectricService.reduceInit(emsInTotal, EmsConstants.IN, deviceEntity);
            }
            // 6. add 2024-01-11 09:55:36 for dcdc 作为电池的总充总放 ,这里只有 ems的 没有上面电表的
            dcdcBatteryOutTotal = getEmsOutTotal(dcdcBatteryOutTotal, data);
            dcdcBatteryInTotal = getEmsInTotal(dcdcBatteryInTotal, data);
            double point =
                    ((double) (short) data[pointListHolder.getEmsAcActivePower(emsType)]) / 10;
            if (Boolean.TRUE.equals(homePageReactivePowerController)) {
                double reActivePoint =
                        ((double) (short) data[pointListHolder.getEmsAcReactivePower(emsType)])
                                / 10;
                if (reActivePoint > 0) {
                    // 放电
                    emsReactiveOutPower += reActivePoint;
                } else {
                    // 充电
                    emsReactiveInPower += Math.abs(reActivePoint);
                }
            }

            if (point > 0) {
                // 放电
                emsOutPower += point;
            } else {
                // 充电
                emsInPower += Math.abs(point);
            }
            designPower += data[pointListHolder.getEmsDesignPower(emsType)];
            bmsDesignCapacity += data[pointListHolder.getEmsCapacity(emsType)];
            soc +=
                    data[pointListHolder.getSoc(emsType)]
                            * data[pointListHolder.getEmsCapacity(emsType)];
            // 是否有机器开机
            if (data[pointListHolder.getSystemRunStatus(emsType)] > 0) {
                runEmsCount++;
                hasEmsRun = true;
            }

            // add 2024-01-10 18:31:18 for dcdc
            // 开启dcdc
            if (hasDcdc) {
                // 只开启了 Ems 其他的全部没开启的情况下
                // 1. dcdc 总功率 230 2 DCDC光伏侧功率(kW) dcdc_meter_power
                // dcdcTotalPv += data[DataConstants.DCDC_METER_POWER];
                double pv = (short) data[pointListHolder.getDcdcMeterPower(emsType)];
                dcdcTotalPv = Double.parseDouble(String.format("%.2f", pv / 10));
                // 2. 光伏总发电量 230 3 DCDC光伏侧历史输出能量(kWh) dcdc_meter_history_energy_pos
            }
            // 这个要去累计到 pv电量上 不需要开dcdc也要累加上 所以放在外面
            int high = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType)];
            int low = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType) + 1];
            dcdcOutTotal += (high << 16) + low;
            dcdcOutTotal = Double.parseDouble(String.format("%.2f", dcdcOutTotal / 10));
        }

        //
        // TODO 计算逻辑 通过 (emsOutPower-emsInPower)交流 - 上面计算得到的 dcdcTotal 得到电池的
        // 20240319 tom say 外部光伏不包含dcdc光伏
        pvPower = dcdcTotalPv + pvExternalPower;
        if (lastSiteVO != null) {
            pvPower = 0.3 * lastSiteVO.getPvPower() + 0.7 * pvPower;
            gridOutPower = 0.3 * lastSiteVO.getGridOutPower() + 0.7 * gridOutPower;
            emsOutPower = 0.3 * lastSiteVO.getEmsOutPower() + 0.7 * emsOutPower;
            emsInPower = 0.3 * lastSiteVO.getEmsInPower() + 0.7 * emsInPower;
            windPower = 0.3 * lastSiteVO.getWindPower() + 0.7 * windPower;
            dieselPower = 0.3 * lastSiteVO.getDieselPower() + 0.7 * dieselPower;
            pilePower = 0.3 * lastSiteVO.getPilePower() + 0.7 * pilePower;
            gasPower = 0.3 * lastSiteVO.getGasPower() + 0.7 * gasPower;
            if (lastSiteVO.getDcdcTotalPv() != null) {
                dcdcTotalPv = 0.3 * lastSiteVO.getDcdcTotalPv() + 0.7 * dcdcTotalPv;
            }
            wasterPower = 0.3 * lastSiteVO.getWasterPower() + 0.7 * wasterPower;
        }
        // dcdcOutTotal 光伏总发电量
        // dcdcTotalPv 光伏总功率
        // emsDailyOutSum 光伏日发电量

        if (hasDcdc) {
            // 是否开启了 show 电池电量开关,如果没开启就不去计算
            if (systemGroupEntity.getShowBatteryElectricity()) {
                // dcdc 不管有没有开启showType都要计算,如果没有开启那上面127行已经算过了
                if (systemGroupEntity.getShowType()) {
                    // 5. 电池的直接拿这个用 当做日充日放
                    dcdcBatteryDailyOutSum =
                            projectDailyService.getDailyPower(
                                    start,
                                    now,
                                    EmsFieldEnum.EMS_HISTORY_OUTPUT_ENERGY.field(),
                                    null,
                                    sysDeviceIds);
                    dcdcBatteryDailyInSum =
                            projectDailyService.getDailyPower(
                                    start,
                                    now,
                                    EmsFieldEnum.EMS_HISTORY_INPUT_ENERGY.field(),
                                    null,
                                    sysDeviceIds);
                } else {
                    // 5. 127行已经计算过
                    try {
                        assert emsDailyOutEnergyFuture != null;
                        dcdcBatteryDailyOutSum = emsDailyOutEnergyFuture.get();
                        dcdcBatteryDailyInSum = emsDailyInEnergyFuture.get();
                    } catch (Exception ignored) {
                    }
                }
            }

            diffDcdcBatteryPv = (emsOutPower - emsInPower) - dcdcTotalPv;
            // 3. 光伏日发电 , //这个要去累计到 pv电量上 不需要开dcdc也要累加上 所以放在外面,累加的地方在 getSiteVO方法里面
            dcdcDailyOutTotal =
                    projectDailyService.getDailyPower(
                            start, now, "dcdc_meter_history_energy_pos", null, sysDeviceIds);
        }
        // 4. 计算 dcdc电池的 充电 放电功率
        // pcs 交流 - dcdcTotalPv
        // 光伏功率

        if (pvPower < 0) {
            pvPower = 0;
        }
        if (windPower < 0) {
            windPower = 0;
        }
        if (dieselPower < 0) {
            dieselPower = 0;
        }
        if (gasPower < 0) {
            gasPower = 0;
        }
        if (pilePower < 0) {
            pilePower = 0;
        }
        if (wasterPower < 0) {
            wasterPower = 0;
        }
        //        double loadPower =
        //                emsOutPower
        //                        + pvExternalPower
        //                        - gridOutPower
        //                        - emsInPower
        //                        + windPower
        //                        + dieselPower
        //                        + gasPower
        //                        - pilePower
        //                        + wasterPower;
        double diffPower = emsOutPower - emsInPower;
        // 无功功率 因为是多个 ems设备 所以按照正负去计算得到 , 前端在 运行状态的时候 需要使用这个区显示能流
        double diffAcReactivePower = emsReactiveOutPower - emsReactiveInPower;
        //        boolean setAcActiveZero = false;
        //        boolean setAcReactiveZero = false;
        ZeroPowerContext zeroPowerContext = new ZeroPowerContext();
        // 默认离线
        zeroPowerContext.setOpenZeroController(systemGroupEntity.getHomePageZeroPowerController());
        int status = 0;
        if (!isOff) {
            if (Boolean.TRUE.equals(systemGroupEntity.getHomePageReactivePowerController())) {
                status =
                        EmsUtil.getEmsStatusSupportReactivePower(
                                designPower,
                                emsInPower,
                                emsOutPower,
                                emsReactiveInPower,
                                emsReactiveOutPower,
                                isOff,
                                hasEmsRun,
                                zeroPowerContext);
            } else {
                status =
                        EmsUtil.getEmsStatus(
                                designPower,
                                emsInPower,
                                emsOutPower,
                                isOff,
                                hasEmsRun,
                                zeroPowerContext);
            }
        } else {
            if (diff <= delayTimes * updatePeriod) {
                return lastSiteVO;
            }
        }
        // 2025-06-05 17:34:49 added 如果ac active zero 0值是true 则设置0 为了让负载的计算正确
        // 由于首页负载功率是按首页各个设备的功率值加加减减算出来的，如果储能有功写死0，那首页负载的公里面，储能用功率0计算负载功率，但是负载曲线里面要求按真实的储能功率得出负载功率曲线的值
        if (zeroPowerContext.isOpenZeroController() && zeroPowerContext.isSetAcActiveZero()) {
            emsOutPower = 0;
            emsInPower = 0;
        }
        double loadPower =
                EmsUtil.loadPower(
                        new EmsUtil.LoadParams()
                                .setEmsOutPower(emsOutPower)
                                .setGridOutPower(gridOutPower)
                                .setEmsInPower(emsInPower)
                                .setPvExternalPower(pvExternalPower)
                                .setWindPower(windPower)
                                .setDieselPower(dieselPower)
                                .setGasPower(gasPower)
                                .setPilePower(pilePower)
                                .setWasterPower(wasterPower));
        //        double loadPower =
        //                emsOutPower
        //                        + pvExternalPower
        //                        - gridOutPower
        //                        - emsInPower
        //                        + windPower
        //                        + dieselPower
        //                        + gasPower
        //                        - pilePower
        //                        + wasterPower;

        FutureResult result =
                getFuture(
                        emsDailyOutEnergyFuture,
                        emsDailyInEnergyFuture,
                        ammeterDailyInEnergyFuture,
                        ammeterDailyOutEnergyFuture);

        SiteVO siteVO =
                getSiteVO(
                        start,
                        now,
                        systemGroupEntity,
                        pvPower,
                        windPower,
                        dieselPower,
                        pilePower,
                        gasPower,
                        pvTotal,
                        gridOutPower,
                        gridOutTotal,
                        gridInTotal,
                        windTotal,
                        dieselTotal,
                        pileTotal,
                        gasTotal,
                        result.getEmsDailyOutSum(),
                        result.getEmsDailyInSum(),
                        emsOutTotal,
                        emsInTotal,
                        bmsDesignCapacity,
                        soc,
                        pvExternalDailySum,
                        gridDailyOutSum,
                        gridDailyInSum,
                        windDailySum,
                        dieselDailySum,
                        pileDailySum,
                        gasDailySum,
                        deviceEntities,
                        runEmsCount,
                        loadPower,
                        diffPower,
                        diffAcReactivePower,
                        status,
                        diffDcdcBatteryPv,
                        dcdcTotalPv,
                        dcdcOutTotal,
                        dcdcDailyOutTotal,
                        dcdcBatteryDailyOutSum,
                        dcdcBatteryDailyInSum,
                        dcdcBatteryInTotal,
                        dcdcBatteryOutTotal,
                        pvExternalTotal,
                        pvExternalPower,
                        result.getEmsMonthOutSum(),
                        result.getEmsMonthInSum(),
                        wasterOutTotal,
                        wasterPower,
                        zeroPowerContext);

        redisTemplate.opsForValue().set(key, JSON.toJSONString(siteVO), 15, TimeUnit.SECONDS);
        return siteVO;
    }

    private double reduceInitOut(double emsOutTotal, DeviceEntity deviceEntity) {
        return emsOutTotal
                - (deviceEntity.getProjectRunInitOutData() == null
                        ? 0.0
                        : deviceEntity.getProjectRunInitOutData());
    }

    private double reduceInitIn(double emsInTotal, DeviceEntity deviceEntity) {
        return emsInTotal
                - (deviceEntity.getProjectRunInitInData() == null
                        ? 0.0
                        : deviceEntity.getProjectRunInitInData());
    }

    private FutureResult getFuture(
            CompletableFuture<Double> emsDailyOutEnergyFuture,
            CompletableFuture<Double> emsDailyInEnergyFuture,
            CompletableFuture<Double> ammeterDailyInEnergyFuture,
            CompletableFuture<Double> ammeterDailyOutEnergyFuture) {

        FutureResult result = new FutureResult();
        double ammeterDailyOutSum = 0;
        double ammeterDailyInSum = 0;

        if (ammeterDailyOutEnergyFuture != null && ammeterDailyInEnergyFuture != null) {
            try {
                ammeterDailyInSum = ammeterDailyInEnergyFuture.get();
                ammeterDailyOutSum = ammeterDailyOutEnergyFuture.get();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        if (emsDailyInEnergyFuture != null && emsDailyOutEnergyFuture != null) {
            try {
                result.setEmsDailyOutSum(emsDailyOutEnergyFuture.get());
                result.setEmsDailyInSum(emsDailyInEnergyFuture.get());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        ElectricVo electricVo = TaskElectricTimer.monthElectricMap.get(WebUtils.projectId.get());
        if (electricVo != null) {
            result.setEmsMonthOutSum(electricVo.getEmsOutSum());
            result.setEmsMonthInSum(electricVo.getEmsInSum());
        }
        result.setEmsDailyOutSum(result.getEmsDailyOutSum() + ammeterDailyOutSum);
        result.setEmsDailyInSum(result.getEmsDailyInSum() + ammeterDailyInSum);
        return result;
    }

    @Data
    static class FutureResult {
        private double emsMonthOutSum = 0;
        private double emsMonthInSum = 0;
        private double emsDailyOutSum = 0;
        private double emsDailyInSum = 0;
    }

    /**
     * 除了 ems 其他的全部关闭
     *
     * @param sg : systemGroupEntity
     * @return : true/false
     */
    private static boolean otherAllClose(GroupEntity sg) {
        return !sg.getEnableLoadGrid()
                && !sg.getEnableLoad()
                && !sg.getPhotovoltaicController()
                && !sg.getEnableWindPowerGeneration()
                && !sg.getEnableWoodPowerGeneration()
                && !sg.getEnableGasPowerGeneration()
                && !sg.getEnableChargingPilePower()
                && !sg.getHasFireFighting();
    }

    private double getEmsInTotal(double emsInTotal, int[] data) {
        int emsType = data[42];
        int highIn = data[pointListHolder.getEmsHistoryInputEnergy(emsType)];
        int lowIn = data[pointListHolder.getEmsHistoryInputEnergy(emsType) + 1];
        long in = ((long) highIn << 16) + lowIn;
        emsInTotal += in * 1.0 / 10;
        return emsInTotal;
    }

    private double getEmsOutTotal(double emsOutTotal, int[] data) {
        int emsType = data[42];
        int highOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType)];
        int lowOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType) + 1];
        long out = ((long) highOut << 16) + lowOut;
        emsOutTotal += (out * 1.0 / 10);
        return emsOutTotal;
    }

    @NotNull
    private SiteVO getSiteVO(
            Long start,
            long now,
            GroupEntity systemGroupEntity,
            double pvPower,
            double windPower,
            double dieselPower,
            double pilePower,
            double gasPower,
            double pvTotal,
            double gridOutPower,
            double gridOutTotal,
            double gridInTotal,
            double windTotal,
            double dieselTotal,
            double pileTotal,
            double gasTotal,
            double emsDailyOutSum,
            double emsDailyInSum,
            double emsOutTotal,
            double emsInTotal,
            double bmsDesignCapacity,
            double soc,
            double pvExternalDailySum,
            double gridDailyOutSum,
            double gridDailyInSum,
            double windDailySum,
            double dieselDailySum,
            double pileDailySum,
            double gasDailySum,
            List<DeviceEntity> deviceEntities,
            int runEmsCount,
            double loadPower,
            double diffPower,
            double diffAcReactivePower,
            int status,
            double diffDcdcBatteryPv,
            double dcdcTotalPvPower,
            double dcdcOutTotal,
            double dcdcDailyOutTotal,
            double dcdcBatteryDailyOutSum,
            double dcdcBatteryDailyInSum,
            double dcdcBatteryInTotal,
            double dcdcBatteryOutTotal,
            double pvExternalTotal,
            double pvExternalPower,
            double emsMonthOutSum,
            double emsMonthInSum,
            double wasterOutTotal,
            double wasterPower,
            ZeroPowerContext zeroPowerContext) {
        SiteVO siteVO = new SiteVO();
        siteVO.setEmsMonthOutSum(emsMonthOutSum);
        siteVO.setEmsMonthInSum(emsMonthInSum);

        double wasterDailySum = 0;
        // 余热发电
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration())) {
            wasterDailySum =
                    projectDailyService.getDailyPower(
                            start,
                            now,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.WASTER.meterType().toString(),
                            null);
        }
        // 光伏日量
        if (Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())) {
            pvExternalDailySum =
                    projectDailyService.getDailyPower(
                            start,
                            now,
                            "ac_history_positive_power_in_kwh",
                            MeterTypeEnum.PV.meterType().toString(),
                            null);
        }
        // 光伏的电量 直接累加上 dcdc的电量
        double pvDailySum = pvExternalDailySum + dcdcDailyOutTotal;
        pvTotal = dcdcOutTotal + pvExternalTotal;
        // 电网日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableElectricGrid())) {
            // 电网日取量
            gridDailyOutSum =
                    projectDailyService.getDailyPower(
                            start, now, "ac_history_negative_power_in_kwh", "2", null);
            // 电网日馈量
            gridDailyInSum =
                    projectDailyService.getDailyPower(
                            start, now, "ac_history_positive_power_in_kwh", "2", null);
        }
        // 风电日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())) {
            windDailySum =
                    projectDailyService.getDailyPower(
                            start, now, "ac_history_positive_power_in_kwh", "4", null);
        }
        // 柴发日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWoodPowerGeneration())) {
            dieselDailySum =
                    projectDailyService.getDailyPower(
                            start, now, "ac_history_positive_power_in_kwh", "5", null);
        }
        // 充电桩日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableChargingPilePower())) {
            pileDailySum =
                    projectDailyService.getDailyPower(
                            start, now, "ac_history_positive_power_in_kwh", "6", null);
        }
        // 燃气日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableGasPowerGeneration())) {
            gasDailySum =
                    projectDailyService.getDailyPower(
                            start, now, "ac_history_positive_power_in_kwh", "8", null);
        }

        siteVO.setPvExternalPower(pvExternalPower);
        siteVO.setPvExternalDaily(pvExternalDailySum);
        siteVO.setPvExternalTotal(pvExternalTotal);
        // 光伏
        siteVO.setPvPower(pvPower);
        siteVO.setPvDaily(pvDailySum);
        siteVO.setPvTotal(pvTotal);
        // 电网
        siteVO.setGridOutPower(gridOutPower);
        siteVO.setGridOutDaily(gridDailyOutSum);
        siteVO.setGridOutTotal(gridOutTotal);
        siteVO.setGridInDaily(gridDailyInSum);
        siteVO.setGridInTotal(gridInTotal);
        // 风电
        siteVO.setWindPower(windPower);
        siteVO.setWindDailySum(windDailySum);
        siteVO.setWindTotal(windTotal);
        // 柴油
        siteVO.setDieselPower(dieselPower);
        siteVO.setDieselDailySum(dieselDailySum);
        siteVO.setDieselTotal(dieselTotal);
        // 充电桩
        siteVO.setPilePower(pilePower);
        siteVO.setPileDailySum(pileDailySum);
        siteVO.setPileTotal(pileTotal);
        // 燃气
        siteVO.setGasPower(gasPower);
        siteVO.setGasDailySum(gasDailySum);
        siteVO.setGasTotal(gasTotal);
        // ems
        siteVO.setEmsOutDaily(emsDailyOutSum);
        siteVO.setEmsOutTotal(emsOutTotal);
        siteVO.setEmsInDaily(emsDailyInSum);
        siteVO.setEmsInTotal(emsInTotal);
        // 余热发电
        siteVO.setWasterDaily(wasterDailySum);
        siteVO.setWasterOutTotal(wasterOutTotal);
        siteVO.setWasterPower(wasterPower);

        if (diffPower > 0) {
            siteVO.setEmsOutPower(diffPower);
            siteVO.setEmsInPower(0d);
        } else {
            siteVO.setEmsOutPower(0d);
            siteVO.setEmsInPower(-diffPower);
        }
        // ---2025-06-05 17:21:30 新增 关于无功
        if (diffAcReactivePower > 0) {
            siteVO.setEmsAcReactiveOutPower(diffAcReactivePower);
            siteVO.setEmsAcReactiveInPower(0d);
        } else {
            siteVO.setEmsAcReactiveOutPower(0d);
            siteVO.setEmsAcReactiveInPower(-diffAcReactivePower);
        }
        // ---- 下面是 关于设置 有功 无功的 0值设置
        if (zeroPowerContext != null && zeroPowerContext.isSetAcActiveZero()) {
            siteVO.setEmsInPower(0d);
            siteVO.setEmsOutPower(0d);
        }
        if (zeroPowerContext != null && zeroPowerContext.isSetAcReactiveZero()) {
            siteVO.setEmsAcReactiveInPower(0d);
            siteVO.setEmsAcReactiveOutPower(0d);
        }
        // -------add 2024-01-11 10:09:46 dcdc
        if (diffDcdcBatteryPv > 0) {
            if (zeroPowerContext != null && zeroPowerContext.isSetAcActiveZero()) {
                // 在设备开启“接入DCDC”开关情况下，由于电池功率是通过交流功率和PV功率计算得出的，
                // 如果交流终默认写死显示0，那电池功率也要求用0计算(单机模式页以及只接入储能的整站模式页，会显示电池功率
                // 原本是 (emsOutPower - emsInPower) - dcdcTotalPvPower 现在认为 (emsOutPower - emsInPower)
                //  为0
                diffDcdcBatteryPv = 0 - dcdcTotalPvPower;
            }
            siteVO.setDcdcBatteryOutPower(diffDcdcBatteryPv);
            siteVO.setDcdcBatteryInPower(0d);
        } else {
            if (zeroPowerContext != null && zeroPowerContext.isSetAcActiveZero()) {
                diffDcdcBatteryPv = 0 - dcdcTotalPvPower;
            }
            siteVO.setDcdcBatteryOutPower(0d);
            siteVO.setDcdcBatteryInPower(-diffDcdcBatteryPv);
        }

        siteVO.setDcdcTotalPv(dcdcTotalPvPower);
        siteVO.setDcdcOutTotal(dcdcOutTotal);
        siteVO.setDcdcDailyOutTotal(dcdcDailyOutTotal);

        siteVO.setDcdcBatteryDailyOutSum(dcdcBatteryDailyOutSum);
        siteVO.setDcdcBatteryDailyInSum(dcdcBatteryDailyInSum);
        siteVO.setDcdcBatteryOutTotal(dcdcBatteryOutTotal);
        siteVO.setDcdcBatteryInTotal(dcdcBatteryInTotal);

        // 返回给前端 交流功率 = 放-充
        siteVO.setDiffPower(diffPower);
        if (zeroPowerContext != null && zeroPowerContext.isSetAcReactiveZero()) {
            siteVO.setDiffAcReactivePower(0d);
        } else {
            siteVO.setDiffAcReactivePower(diffAcReactivePower);
        }
        // ----------end dcdc

        siteVO.setEmsStatus(status);
        if (bmsDesignCapacity != 0) {
            siteVO.setSoc(soc / (bmsDesignCapacity * 10));
        }
        // load
        siteVO.setLoadPower(loadPower);
        siteVO.setUpdateTime(now);
        siteVO.setTotalRunStatus(runEmsCount + "/" + deviceEntities.size());
        long maintainCount = deviceEntities.stream().filter(DeviceEntity::getMaintain).count();
        siteVO.setTotalMaintainStatus(maintainCount + "/" + deviceEntities.size());

        return siteVO;
    }

    @Override
    public List<EmsVO> getEms(Long start) {
        String projectId = WebUtils.projectId.get();
        long now = Instant.now().getEpochSecond();
        int updatePeriod = 5;
        int delayTimes = 3;
        long updateTime;
        long diff;
        List<EmsVO> list = new ArrayList<>();
        Map<String, Double> outMap =
                projectDailyService.getEmsPower(start, now, "ems_history_output_energy");
        Map<String, Double> inMap =
                projectDailyService.getEmsPower(start, now, "ems_history_input_energy");
        List<DeviceEntity> deviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, projectId)
                        .eq(DeviceEntity::getUnreal, false)
                        .orderByAsc(DeviceEntity::getOrderIndex)
                        .orderByAsc(DeviceEntity::getCreateTime)
                        .list();
        GroupEntity systemGroupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                                .eq(GroupEntity::getWhetherSystem, true));
        for (DeviceEntity deviceEntity : deviceEntities) {
            String key = "ems:" + deviceEntity.getId();
            EmsVO laseEmsVO = JSON.parseObject(redisTemplate.opsForValue().get(key), EmsVO.class);
            updateTime = laseEmsVO == null ? 0 : (long) laseEmsVO.getUpdateTime();
            diff = now - updateTime;
            if (diff < updatePeriod) {
                list.add(laseEmsVO);
                continue;
            }
            int[] data = dataService.get(deviceEntity.getId());
            long finalDiff = diff;
            Optional.ofNullable(data)
                    .ifPresentOrElse(
                            e -> {
                                EmsVO emsVO =
                                        getEmsVO(
                                                now,
                                                outMap,
                                                inMap,
                                                deviceEntity,
                                                laseEmsVO,
                                                e,
                                                start,
                                                systemGroupEntity);
                                // 设置故障状态 5
                                int emsType = e[42];
                                emsVO.setProtocol(emsType);
                                emsVO.setOutControl(
                                        e[pointListHolder.getDirectPowerControlCounter(emsType)]);
                                if (e[pointListHolder.getSystemRunStatus(emsType)] != 0
                                        && e[pointListHolder.getEmsState(emsType)] == 2) {
                                    emsVO.setEmsStatus(EmsStatusEnum.Fault.getStatus());
                                }
                                if (emsVO.getEmsSerial() != null) {
                                    redisTemplate
                                            .opsForHash()
                                            .put(
                                                    DEVICE + deviceEntity.getId(),
                                                    SERIAL,
                                                    emsVO.getEmsSerial());
                                    redisTemplate.expire(
                                            DEVICE + deviceEntity.getId(), 30, TimeUnit.DAYS);
                                }
                                redisTemplate
                                        .opsForValue()
                                        .set(key, JSON.toJSONString(emsVO), 15, TimeUnit.SECONDS);
                                list.add(emsVO);
                            },
                            () -> {
                                Map<Object, Object> deviceInfo =
                                        redisTemplate
                                                .opsForHash()
                                                .entries(DEVICE + deviceEntity.getId());
                                if (finalDiff <= delayTimes * updatePeriod) {
                                    if (deviceInfo.get(SERIAL) != null && laseEmsVO != null) {
                                        laseEmsVO.setEmsSerial((String) deviceInfo.get(SERIAL));
                                    }
                                    list.add(laseEmsVO);
                                } else {
                                    EmsVO emsVO = new EmsVO();
                                    emsVO.setName(deviceEntity.getName());
                                    emsVO.setEmsStatus(0);
                                    emsVO.setId(deviceEntity.getId());
                                    emsVO.setMaintain(deviceEntity.getMaintain());
                                    emsVO.setIsHost(deviceEntity.getIsHost());
                                    if (deviceInfo.get(SERIAL) != null) {
                                        emsVO.setEmsSerial((String) deviceInfo.get(SERIAL));
                                    }
                                    list.add(emsVO);
                                }
                            });
        }
        List<String> duplicateNumbers =
                EmsUtil.findDuplicateNumbers(
                        list.stream()
                                .map(EmsVO::getEmsSerial)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        list.forEach(
                e -> {
                    if (StringUtils.hasLength(e.getEmsSerial())
                            && duplicateNumbers.contains(e.getEmsSerial())) {
                        e.setSnConflict(1); // 满足条件，设置为 1
                    } else {
                        e.setSnConflict(0); // 不满足条件，设置为 0
                    }
                });
        return list;
    }

    @NotNull
    private EmsVO getEmsVO(
            long now,
            Map<String, Double> outMap,
            Map<String, Double> inMap,
            DeviceEntity deviceEntity,
            EmsVO laseEmsVO,
            int[] data,
            long start,
            GroupEntity systemGroupEntity) {
        boolean isOff = data == null;
        EmsVO emsVO = new EmsVO();
        StringBuilder value = new StringBuilder();
        assert data != null;
        int emsType = data[42];
        for (int i = 0; i < pointListHolder.getEmsSerialLength(emsType); i++) {
            if (data[pointListHolder.getEmsSerial(emsType) + i] == 0) {
                continue;
            }
            value.append((char) (data[pointListHolder.getEmsSerial(emsType) + i] >> 8));
            value.append((char) (data[pointListHolder.getEmsSerial(emsType) + i] & 0xFF));
        }
        String emsSerial = value.toString();
        emsVO.setEmsSerial(emsSerial.trim());
        // bug ! It is not compatible with the ems v2  protocol
        // emsVO.setEmsSerial(emsSerial.trim().length() < 18 ? null : emsSerial.trim());
        double chargePowerLimit = data[pointListHolder.getEmsChargePowerLimit(emsType)];
        emsVO.setEmsChargePower(chargePowerLimit);
        double dischargePowerLimit = data[pointListHolder.getEmsDischargePowerLimit(emsType)];
        emsVO.setEmsDischargePower(dischargePowerLimit);
        double emsCapacity = data[pointListHolder.getEmsCapacity(emsType)];
        emsVO.setEmsCapacity(emsCapacity);
        double emsDesignPower = data[pointListHolder.getEmsDesignPower(emsType)];
        emsVO.setEmsDesignPower(emsDesignPower);
        int highOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType)];
        int lowOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType) + 1];
        long out = ((long) highOut << 16) + lowOut;
        double emsOutTotal = (out * 1.0 / 10);
        int highIn = data[pointListHolder.getEmsHistoryInputEnergy(emsType)];
        int lowIn = data[pointListHolder.getEmsHistoryInputEnergy(emsType) + 1];
        long in = ((long) highIn << 16) + lowIn;
        double emsInTotal = in * 1.0 / 10;
        double emsInPower = 0;
        double emsOutPower = 0;
        double emsReactiveOutPower = 0;
        double emsReactiveInPower = 0;
        double dcdcTotalPv = 0;
        Boolean homePageReactivePowerController =
                systemGroupEntity.getHomePageReactivePowerController();
        double point = ((double) (short) data[pointListHolder.getEmsAcActivePower(emsType)]) / 10;
        if (Boolean.TRUE.equals(homePageReactivePowerController)) {
            double reActivePoint =
                    ((double) (short) data[pointListHolder.getEmsAcReactivePower(emsType)]) / 10;
            if (reActivePoint > 0) {
                // 放电
                emsReactiveOutPower += reActivePoint;
            } else {
                // 充电
                emsReactiveInPower += Math.abs(reActivePoint);
            }
        }
        boolean hasEmsRun = false;
        if (deviceEntity.getShowGps()) {
            int gpsOnline = data[pointListHolder.getGpsOnline(emsType)];
            if (gpsOnline > 0) {
                emsVO.setShowGps(1);
                long latitude =
                        ((long) data[pointListHolder.getGpsLatitude(emsType)] << 16)
                                + data[pointListHolder.getGpsLatitude(emsType) + 1];
                emsVO.setLatitude((double) latitude / 1000000);
                long longitude =
                        ((long) data[pointListHolder.getGpsLongitude(emsType)] << 16)
                                + data[pointListHolder.getGpsLongitude(emsType) + 1];
                emsVO.setLongitude((double) longitude / 1000000);
            } else {
                emsVO.setShowGps(2);
            }
        } else {
            emsVO.setShowGps(0);
        }
        if (point > 0) {
            // 放电
            emsOutPower = point;
        } else {
            // 充电
            emsInPower = Math.abs(point);
        }
        if (data[pointListHolder.getSystemRunStatus(emsType)] > 0) {
            hasEmsRun = true;
        }
        dcdcTotalPv = (short) data[pointListHolder.getDcdcMeterPower(emsType)];
        dcdcTotalPv = Double.parseDouble(String.format("%.2f", dcdcTotalPv / 10));
        double designPower = data[pointListHolder.getEmsDesignPower(emsType)];
        if (laseEmsVO != null) {
            emsOutPower = laseEmsVO.getEmsOutPower() * 0.3 + emsOutPower * 0.7;
            emsInPower = laseEmsVO.getEmsInPower() * 0.3 + emsInPower * 0.7;
            if (laseEmsVO.getDcdcTotalPv() != null) {
                dcdcTotalPv = laseEmsVO.getDcdcTotalPv() * 0.3 + dcdcTotalPv * 0.7;
            }
        }
        ZeroPowerContext zeroPowerContext = new ZeroPowerContext();
        // 默认离线
        zeroPowerContext.setOpenZeroController(systemGroupEntity.getHomePageZeroPowerController());
        // 0离线 1待机 2充电 3放电 4停机 5故障在单机模式下判断，这个单机模块最后再判断，这个地方多个地方使用
        int status;
        if (Boolean.TRUE.equals(systemGroupEntity.getHomePageReactivePowerController())) {
            status =
                    EmsUtil.getEmsStatusSupportReactivePower(
                            designPower,
                            emsInPower,
                            emsOutPower,
                            emsReactiveInPower,
                            emsReactiveOutPower,
                            isOff,
                            hasEmsRun,
                            zeroPowerContext);
        } else {
            status =
                    EmsUtil.getEmsStatus(
                            designPower,
                            emsInPower,
                            emsOutPower,
                            isOff,
                            hasEmsRun,
                            zeroPowerContext);
        }

        //        int status = EmsUtil.getEmsStatus(designPower, emsInPower, emsOutPower, isOff,
        // hasEmsRun);
        double diffPower = emsOutPower - emsInPower;
        // 无功功率 因为是多个 ems设备 所以按照正负去计算得到 , 前端在 运行状态的时候 需要使用这个区显示能流
        double diffAcReactivePower = emsReactiveOutPower - emsReactiveInPower;
        // 2025-06-05 17:34:49 added 如果ac active zero 0值是true 则设置0 为了让负载的计算正确
        // 由于首页负载功率是按首页各个设备的功率值加加减减算出来的，如果储能有功写死0，那首页负载的公里面，储能用功率0计算负载功率，但是负载曲线里面要求按真实的储能功率得出负载功率曲线的值
        if (zeroPowerContext.isOpenZeroController() && zeroPowerContext.isSetAcActiveZero()) {
            emsOutPower = 0;
            emsInPower = 0;
        }
        //        if (status == EmsStatusEnum.STANDBY.getStatus()) {
        //            emsVO.setEmsOutPower(0d);
        //            emsVO.setEmsInPower(0d);
        //        } else {
        if (diffPower > 0) {
            emsVO.setEmsOutPower(emsOutPower);
            emsVO.setEmsInPower(0d);
        } else {
            emsVO.setEmsOutPower(0d);
            emsVO.setEmsInPower(emsInPower);
        }
        // ---2025-06-05 17:21:30 新增 关于无功
        if (diffAcReactivePower > 0) {
            emsVO.setEmsAcReactiveOutPower(diffPower);
            emsVO.setEmsAcReactiveInPower(0d);
        } else {
            emsVO.setEmsAcReactiveOutPower(0d);
            emsVO.setEmsAcReactiveInPower(-diffPower);
        }
        // }
        // ---- 下面是 关于设置 有功 无功的 0值设置
        if (zeroPowerContext.isSetAcActiveZero()) {
            emsVO.setEmsInPower(0d);
            emsVO.setEmsOutPower(0d);
        }
        if (zeroPowerContext.isSetAcReactiveZero()) {
            emsVO.setEmsAcReactiveInPower(0d);
            emsVO.setEmsAcReactiveOutPower(0d);
        }
        // ems
        if (outMap.get(deviceEntity.getId()) == null) {
            emsVO.setEmsOutDaily(0d);
        } else {
            emsVO.setEmsOutDaily(outMap.get(deviceEntity.getId()));
        }
        emsVO.setEmsOutTotal(emsOutTotal);
        if (inMap.get(deviceEntity.getId()) == null) {
            emsVO.setEmsInDaily(0d);
        } else {
            emsVO.setEmsInDaily(inMap.get(deviceEntity.getId()));
        }
        emsVO.setEmsInTotal(emsInTotal);
        emsVO.setEmsStatus(status);
        emsVO.setMaintain(deviceEntity.getMaintain());
        emsVO.setName(deviceEntity.getName());
        emsVO.setSoc(data[pointListHolder.getSoc(emsType)] * 1.0 / 10);
        emsVO.setId(deviceEntity.getId());
        emsVO.setUpdateTime(now);
        emsVO.setIsHost(deviceEntity.getIsHost());
        // ---------start------ dcdc

        double diffDcdcBatteryPv = 0;
        //        double dcdcBatteryOutPower = 0;
        //        double dcdcBatteryInPower = 0;
        double dcdcOutTotal = 0;
        double dcdcDailyOutTotal = 0;
        double dcdcBatteryOutTotal = 0;
        double dcdcBatteryInTotal = 0;
        // 1.其他信息 设备名称 sn(已经有) 额定功率 额定容量 经度 纬度(已经有)
        // 2.条件 开dcdc的才去操作
        emsVO.setHasDcdc(deviceEntity.getHasDcdc());
        if (deviceEntity.getHasDcdc()) {
            // dcdc的pv
            // dcdcTotalPv = data[DataConstants.DCDC_PV];
            // dcdc的 光伏总发电量
            int high = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType)];
            int low = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType) + 1];
            dcdcOutTotal = (high << 16) + low;
            dcdcOutTotal = Double.parseDouble(String.format("%.2f", dcdcOutTotal / 10));
            // 查询当前这个设备的 查询 光伏日发电量
            dcdcDailyOutTotal =
                    projectDailyService.getDailyPower(
                            start,
                            now,
                            "dcdc_meter_history_energy_pos",
                            null,
                            List.of(deviceEntity.getId()));
            diffDcdcBatteryPv = (emsOutPower - emsInPower) - dcdcTotalPv;
            //            if (diffDcdcBatteryPv > 0) {
            //                dcdcBatteryOutPower = diffDcdcBatteryPv;
            //                dcdcBatteryInPower = 0d;
            //            } else {
            //                dcdcBatteryOutPower = 0d;
            //                dcdcBatteryInPower = -diffDcdcBatteryPv;
            //            }
            // -------add 2024-01-11 10:09:46 dcdc
            if (diffDcdcBatteryPv > 0) {
                if (zeroPowerContext.isSetAcActiveZero()) {
                    // 在设备开启“接入DCDC”开关情况下，由于电池功率是通过交流功率和PV功率计算得出的，
                    // 如果交流终默认写死显示0，那电池功率也要求用0计算(单机模式页以及只接入储能的整站模式页，会显示电池功率
                    // 原本是 (emsOutPower - emsInPower) - dcdcTotalPvPower 现在认为 (emsOutPower -
                    // emsInPower)
                    //  为0
                    diffDcdcBatteryPv = 0 - dcdcTotalPv;
                }
                emsVO.setDcdcBatteryOutPower(diffDcdcBatteryPv);
                emsVO.setDcdcBatteryInPower(0d);
            } else {
                if (zeroPowerContext.isSetAcActiveZero()) {
                    diffDcdcBatteryPv = 0 - dcdcTotalPv;
                }
                emsVO.setDcdcBatteryOutPower(0d);
                emsVO.setDcdcBatteryInPower(-diffDcdcBatteryPv);
            }
        }
        // 总充 总放
        dcdcBatteryOutTotal = getEmsOutTotal(dcdcBatteryOutTotal, data);
        dcdcBatteryInTotal = getEmsInTotal(dcdcBatteryInTotal, data);
        // diffPower作为交流功率
        emsVO.setDiffPower(diffPower);
        //  2025-06-06 10:02:42 added新增
        if (zeroPowerContext.isSetAcReactiveZero()) {
            emsVO.setDiffAcReactivePower(0d);
        } else {
            emsVO.setDiffAcReactivePower(diffAcReactivePower);
        }
        //        emsVO.setDcdcBatteryOutPower(dcdcBatteryOutPower);
        //        emsVO.setDcdcBatteryInPower(dcdcBatteryInPower);
        // dcdc的pv
        emsVO.setDcdcTotalPv(dcdcTotalPv);
        // dcdc的 光伏总发电量
        emsVO.setDcdcOutTotal(dcdcOutTotal);
        emsVO.setDcdcDailyOutTotal(dcdcDailyOutTotal);

        emsVO.setDcdcBatteryOutTotal(dcdcBatteryOutTotal);
        emsVO.setDcdcBatteryInTotal(dcdcBatteryInTotal);

        // 使用ems的 日充日放作为电池的日充日放
        if (outMap.get(deviceEntity.getId()) == null) {
            emsVO.setDcdcBatteryDailyOutSum(0d);
        } else {
            emsVO.setDcdcBatteryDailyOutSum(outMap.get(deviceEntity.getId()));
        }
        emsVO.setEmsOutTotal(emsOutTotal);
        if (inMap.get(deviceEntity.getId()) == null) {
            emsVO.setDcdcBatteryDailyInSum(0d);
        } else {
            emsVO.setDcdcBatteryDailyInSum(inMap.get(deviceEntity.getId()));
        }
        // ---------end------ dcdc
        // 1.4.4 add soft hard version 这个不分 ems100 和 ems200 协议都是这个点位
        int emsSoftwareVersion = data[40];
        int emsHardwareVersion = data[41];
        emsVO.setEmsSoftwareVersion(emsSoftwareVersion);
        emsVO.setEmsHardwareVersion(emsHardwareVersion);
        return emsVO;
    }
}
