package com.wifochina.modules.project.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.util.OnOffGridRunEnum;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-19
 */
@Data
@EqualsAndHashCode()
@TableName("t_project")
public class ProjectEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "项目id")
    private String id;

    /** 系统名 */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /** modbus host地址 */
    @ApiModelProperty(value = "场站版项目ip")
    private String projectHost;

    /** modbus 地址 */
    @ApiModelProperty(value = "场站版项目端口")
    private Integer projectPort;

    /** 项目模式，1场站模式 0云模式 */
    @ApiModelProperty(value = "项目模式 1场站模式 0云模式")
    private Integer projectModel;

    /** 是否可以访问本地 */
    @ApiModelProperty(value = "是否可以访问本地")
    private Boolean whetherAccessible;

    /** 是本地项目id */
    @ApiModelProperty(value = "本地项目id")
    private String localProjectId;

    /** 项目状态 */
    @ApiModelProperty(value = "项目状态")
    private Integer status;

    /** 电价托管 默认false */
    @ApiModelProperty(value = "电价托管")
    private Boolean priceProxy;

    /** 云端大屏展示 默认false */
    @ApiModelProperty(value = "云端大屏展示")
    private Boolean cloudLargeScreen;

    /** 项目大屏展示 默认false */
    @ApiModelProperty(value = "项目大屏展示")
    private Boolean largeScreen;

    /** 项目电站名 */
    @ApiModelProperty(value = "项目区域id")
    private String projectDeviceAlias;

    /** 项目版本 */
    @ApiModelProperty(value = "项目版本")
    private String version;

    /** 项目故障 */
    @ApiModelProperty(value = "项目故障")
    private Integer fault;

    /** 项目告警 */
    @ApiModelProperty(value = "项目告警")
    private Integer alarm;

    /**
     * @see com.wifochina.common.constants.CurrencyEnum 项目币种 人民币 rmb 美元 dollar 欧元 euro
     */
    @ApiModelProperty(value = "项目币种 人民币 rmb 美元 dollar 欧元 euro")
    private String currency;

    /** 项目时区 */
    @ApiModelProperty(value = "时区查")
    private Integer duration;

    /** 项目国家 */
    @ApiModelProperty("国家")
    private Integer country;

    /** 项目时区 */
    @ApiModelProperty("项目时区")
    private String timezone;

    /** 项目所在分区 */
    @ApiModelProperty("项目所在分区")
    private Integer dataCenter;

    /** 是否删除 true已删除，false未删除 */
    @ApiModelProperty("是否删除 true已删除，false未删除")
    private Boolean whetherDelete;

    /**
     * 是否备电 默认false, 开启true
     *
     * @see OnOffGridRunEnum
     */
    @ApiModelProperty(
            value =
                    "并离网切换的4种模式 on_gird_run, off_gird_run, on_off_gird_run_manual, on_off_gird_run_auto")
    private String whetherBackup;

    /** 是否备电 默认false, 开启true */
    @ApiModelProperty(value = "项目省份")
    private Integer province;

    /** 未确认数目 */
    @ApiModelProperty(value = "未确认数目")
    private Integer unConfirmAll;

    /** 未确认不含维护数目 */
    @ApiModelProperty(value = "未确认不含维护数目")
    private Integer unConfirmNotMaintain;

    /** 是否开启告警 */
    @ApiModelProperty("是否开启告警")
    private Boolean whetherAlarm;

    /** 项目排序字段 */
    @TableField(value = "`order`")
    private Integer order;

    /** 项目额外连接列表 */
    @TableField(exist = false)
    private List<ProjectUrlEntity> url;

    @TableField(exist = false)
    @ApiModelProperty(value = "pcsCode (非数据库字段)")
    private String pcsCode;

    @ApiModelProperty(value = "是否维护，true是，false 否 (非数据库字段)")
    @TableField(exist = false)
    private Boolean maintain;

    @ApiModelProperty("项目是否开启ems 故障和停机状态隐藏")
    private Boolean fake;

    /**
     * @see ElectricPriceTypeEnum 电价类型字段
     */
    @ApiModelProperty("电价类型字段")
    private String electricPriceType;

    /** 电价区域字段 */
    @ApiModelProperty("电价区域")
    private String electricPriceArea;

    /** 电价跨度字段 */
    @ApiModelProperty("电价跨度")
    private String electricPriceSpan;

    /** 项目故障 */
    @ApiModelProperty(value = "未屏蔽项目故障")
    private Integer nonHideFault;

    /** 项目告警 */
    @ApiModelProperty(value = "未屏蔽项目告警")
    private Integer nonHideAlarm;

    /** 项目排序字段 */
    @ApiModelProperty(value = "屏蔽告警排序")
    private Integer orderShield;

    /** 项目排序字段 */
    @ApiModelProperty(value = "项目一类排序")
    private Integer orderOne;

    /** 项目排序字段 */
    @ApiModelProperty(value = "屏蔽告警一类排序")
    private Integer orderOneShield;

    @ApiModelProperty(value = "项目排序规则")
    private Integer orderRule;

    /** 分时缓存 */
    @ApiModelProperty(value = "分时缓存")
    private Boolean timeSharingCache;

    @JsonIgnore private Integer hash;
}
