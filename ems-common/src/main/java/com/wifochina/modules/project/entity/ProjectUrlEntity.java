package com.wifochina.modules.project.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Data
@EqualsAndHashCode()
@TableName("t_project_url")
public class ProjectUrlEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 扩展url
     */
    private String url;

    /**
     * 序号
     */
    private Integer orderNum;

    /**
     * url描述
     */
    private String description;

    /**
     * 项目备注
     */
    private String remark;

}
