package com.wifochina.modules.project.service.impl;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.project.request.ProjectUrlRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.project.entity.ProjectUrlEntity;
import com.wifochina.modules.project.mapper.ProjectUrlMapper;
import com.wifochina.modules.project.service.ProjectUrlService;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
public class ProjectUrlServiceImpl extends ServiceImpl<ProjectUrlMapper, ProjectUrlEntity>
        implements ProjectUrlService {

    @Override
    public void createUrl(ProjectUrlRequest request) {
        ProjectUrlEntity item = new ProjectUrlEntity();
        BeanUtils.copyProperties(request, item);
        item.setId(StringUtil.uuid());
        ProjectUrlEntity oldProjectUrlEntity =
                this.lambdaQuery()
                        .eq(ProjectUrlEntity::getProjectId, request.getProjectId())
                        .eq(ProjectUrlEntity::getOrderNum, request.getOrderNum())
                        .one();
        if (oldProjectUrlEntity != null) {
            throw new ServiceException(ErrorResultCode.ORDER_NUM_REPEAT.value());
        }
        save(item);
    }

    @Override
    public void updateUrl(ProjectUrlRequest request) {
        ProjectUrlEntity exitItem = getById(request.getId());
        ProjectUrlEntity oldProjectUrlEntity =
                this.lambdaQuery()
                        .eq(ProjectUrlEntity::getProjectId, exitItem.getProjectId())
                        .eq(ProjectUrlEntity::getOrderNum, request.getOrderNum())
                        .one();
        if (oldProjectUrlEntity != null && !oldProjectUrlEntity.getId().equals(request.getId())) {
            throw new ServiceException(ErrorResultCode.ORDER_NUM_REPEAT.value());
        }
        BeanUtils.copyProperties(request, exitItem);
        updateById(exitItem);
    }
}
