package com.wifochina.modules.project.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.entity.ProjectUrlEntity;
import com.wifochina.modules.project.request.AlarmUpdateRequest;
import com.wifochina.modules.project.request.ProjectInitRequest;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.project.service.ProjectUrlService;
import com.wifochina.modules.project.vo.ProjectVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@RequestMapping("/project")
@RestController
@Api(tags = "03-项目首页")
public class ProjectController {

    @Value("${ems.version}")
    private String version;

    @Resource private ProjectService projectService;
    @Resource private ProjectUrlService projectUrlService;
    @Resource private AmmeterService ammeterService;
    @Resource private CameraService cameraService;
    @Resource private DeviceService deviceService;
    @Resource private ControllableService controllableService;
    @Resource private GroupService groupService;

    /** get project */
    @PostMapping("/list")
    @ApiOperation("获取项目列表")
    public Result<ProjectVo> getProjectInfo() {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        projectEntity.setVersion(version);
        List<ProjectUrlEntity> urlList =
                projectUrlService.list(
                        Wrappers.lambdaQuery(ProjectUrlEntity.class)
                                .eq(ProjectUrlEntity::getProjectId, projectId));
        projectEntity.setUrl(urlList);
        ProjectVo projectVo = new ProjectVo();
        BeanUtils.copyProperties(projectEntity, projectVo);
        List<AmmeterEntity> ammeterEntities =
                ammeterService.lambdaQuery().eq(AmmeterEntity::getProjectId, projectId).list();
        boolean hasControllable = ammeterEntities.stream().anyMatch(AmmeterEntity::getControllable);
        boolean hasCamera =
                cameraService.lambdaQuery().eq(CameraEntity::getProjectId, projectId).count() > 0;
        projectVo.setHasControllable(hasControllable);
        projectVo.setHasCamera(hasCamera);
        projectVo.setHasMeter(!CollectionUtils.isEmpty(ammeterEntities));
        boolean hasFr =
                ammeterEntities.stream().anyMatch(e -> !e.getFrequencyRegulation().equals("nil"));
        projectVo.setHasFr(hasFr);
        // dcdc标识 项目下有一个开启了 dcdc设备即为true
        List<DeviceEntity> deviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, projectId)
                        .eq(DeviceEntity::getUnreal, false)
                        .list();
        boolean hasDcdc = deviceEntities.stream().anyMatch(DeviceEntity::getHasDcdc);
        projectVo.setHasDcdc(hasDcdc);
        boolean hasSts = deviceEntities.stream().anyMatch(DeviceEntity::getHasSts);
        projectVo.setHasSts(hasSts);
        boolean hasSteerable =
                controllableService
                                .lambdaQuery()
                                .eq(ControllableEntity::getProjectId, projectId)
                                .count()
                        > 0;
        projectVo.setHasSteerable(hasSteerable);
        boolean hasTransformerTemperatureSteerable =
                controllableService
                                .lambdaQuery()
                                .eq(ControllableEntity::getProjectId, projectId)
                                .eq(ControllableEntity::getVendor, "LIDE_BWDK326")
                                .count()
                        > 0;
        projectVo.setHasTransformerTemperatureSteerable(hasTransformerTemperatureSteerable);
        List<GroupEntity> groupEntities =
                groupService.lambdaQuery().eq(GroupEntity::getProjectId, projectId).list();
        boolean hasVpp = groupEntities.stream().anyMatch(GroupEntity::getOpenVpp);
        projectVo.setHasVpp(hasVpp);
        return Result.success(projectVo);
    }

    /** get project */
    @PostMapping("/updateCreateTime")
    @ApiOperation("修改项目创建时间")
    public Result<Object> updateCreateTime(@RequestBody ProjectInitRequest projectInitRequest) {
        projectService
                .lambdaUpdate()
                .set(ProjectEntity::getCreateTime, projectInitRequest.getDate())
                .eq(ProjectEntity::getId, projectInitRequest.getProjectId())
                .update();
        return Result.success();
    }

    @PostMapping("/updateAlarm")
    @ApiOperation("修改项目告警状态")
    @PreAuthorize("hasAuthority('/system/alarm')")
    @Log(module = "PROJECT", methods = "ALARM_UPDATE", type = OperationType.UPDATE_SIMPLE)
    public Result<Object> updateAlarm(@RequestBody AlarmUpdateRequest alarmUpdateRequest) {
        projectService
                .lambdaUpdate()
                .set(ProjectEntity::getWhetherAlarm, alarmUpdateRequest.getWhetherAlarm())
                .eq(ProjectEntity::getId, WebUtils.projectId.get())
                .update();
        return Result.success();
    }

    @GetMapping("/getAlarmStatus")
    @ApiOperation("获取项目告警状态")
    public Result<Object> getAlarmStatus() {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        return Result.success(projectEntity.getWhetherAlarm());
    }
}
