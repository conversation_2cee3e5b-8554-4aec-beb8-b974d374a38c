package com.wifochina.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.project.entity.ProjectEntity;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface ProjectService extends IService<ProjectEntity> {
    List<ProjectEntity> getCloudProjectList();

    List<ProjectEntity> getProjectsByTimeZone(String timezone);

    List<ProjectEntity> getProjectsByTimeZoneAndPriceType(String timezone, String priceType);

    List<String> getAllTimeZone();

    Map<String, Boolean> getIncomeOpenStatusByProjectIds(List<String> projectIds);

    /**
     * 内部是根据当前 服务 cloud 还是 site 去查询对应的 项目列表
     *
     * @return : site or cloud projects
     */
    List<ProjectEntity> getAllProjectByServiceDeployStatus();
}
