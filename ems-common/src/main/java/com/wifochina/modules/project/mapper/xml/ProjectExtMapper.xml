<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.project.mapper.ProjectExtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ProjectManageVoMap" type="com.wifochina.modules.project.vo.ProjectManageVo">
        <id column="id" property="id"/>
        <result column="project_name" property="projectName"/>
        <result column="project_host" property="projectHost"/>
        <result column="project_port" property="projectPort"/>
        <result column="project_model" property="projectModel"/>
        <result column="status" property="status"/>
        <result column="project_device_alias" property="projectDeviceAlias"/>
        <result column="whether_accessible" property="whetherAccessible"/>
        <result column="whether_backup" property="whetherBackup"/>
        <result column="local_project_id" property="localProjectId"/>
        <result column="version" property="version"/>
        <result column="price_proxy" property="priceProxy"/>
        <result column="fault" property="fault"/>
        <result column="alarm" property="alarm"/>
        <result column="area" property="area"/>
        <result column="size" property="size"/>
        <result column="address" property="address"/>
        <result column="power_type_id" property="powerTypeId"/>
        <result column="power_type" property="powerType"/>
        <result column="power_type_en" property="powerTypeEn"/>
        <result column="power_level_id" property="powerLevelId"/>
        <result column="power_level" property="powerLevel"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="remark" property="remark"/>
        <result column="ems" property="ems"/>
        <result column="pcs" property="pcs"/>
        <result column="currency" property="currency"/>
        <result column="bms" property="bms"/>
        <result column="province" property="province"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="prediction_path" property="alias"/>
        <result column="enable_radiation" property="enableRadiation"/>
        <result column="enable_weather" property="enableWeather"/>
        <result column="enable_etl" property="enableEtl"/>
        <result column="duration" property="duration"/>
        <result column="nation" property="nation"/>
        <result column="timezone" property="timezone"/>
        <result column="data_partition" property="dataPartition"/>
        <result column="cloud_large_screen" property="cloudLargeScreen"/>
        <result column="large_screen" property="largeScreen"/>
        <result column="whether_alarm" property="whetherAlarm"/>
        <result column="un_confirm_all" property="unConfirmAll"/>
        <result column="un_confirm_not_maintain" property="unConfirmNotMaintain"/>
        <result column="fake" property="fake"/>
    </resultMap>

    <select id="queryAllNotDeleteProjectSize"
            parameterType="list"
            resultMap="ProjectManageVoMap">
        select e.size ,p.* from t_project p left join t_project_ext e on p.id = e.id where p.whether_delete = false
        and p.id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryProjectManageList"
            parameterType="com.wifochina.modules.project.request.ProjectManageListRequest"
            resultMap="ProjectManageVoMap">
        select p.*, e.`area_id`, e.`area`, e.`size`, e.`address`, e.`longitude`, e.`latitude`, e.`remark`, e.`ems`,
        e.`pcs`,
        e.`bms`, e.`prediction_path`, e.`enable_radiation`, e.`enable_weather`, e.`enable_etl`
        from t_project p, t_project_ext e
        where p.id = e.id and p.whether_delete = false

        <if test="projectManageListRequest.projectName!=null and projectManageListRequest.projectName.trim()!=''">
            and p.project_name like CONCAT(CONCAT('%',#{projectManageListRequest.projectName}),'%')
        </if>

        <if test="projectManageListRequest.projectModel!=null">
            and p.project_model = #{projectManageListRequest.projectModel}
        </if>

        <if test="projectManageListRequest.priceProxy!=null">
            and p.price_proxy = #{projectManageListRequest.priceProxy}
        </if>

        <if test="projectManageListRequest.status!=null and projectManageListRequest.status.size()>0">
            and (p.status in<foreach collection="projectManageListRequest.status" index="index" item="item" open="("
                                     separator="," close=")">#{item}</foreach>)
        </if>

        <if test="projectManageListRequest.alertStatus!=null and projectManageListRequest.alertStatus==1">
            and p.fault = 0 and p.alarm = 0
        </if>

        <if test="projectManageListRequest.alertStatus!=null and projectManageListRequest.alertStatus==2">
            and p.fault > 0
        </if>

        <if test="projectManageListRequest.alertStatus!=null and projectManageListRequest.alertStatus==3">
            and p.alarm > 0
        </if>
        <if test="projectManageListRequest.alertStatus!=null and projectManageListRequest.alertStatus==4">
            and (p.fault > 0 or p.alarm > 0)
        </if>

        <if test="projectManageListRequest.projectIds!=null and projectManageListRequest.projectIds.size()>0">
            and (p.id in<foreach collection="projectManageListRequest.projectIds" index="index" item="item" open="("
                                 separator="," close=")">#{item}</foreach>)
        </if>

        <if test="projectManageListRequest.startTime!=null and projectManageListRequest.startTime>0">
            and p.create_time <![CDATA[>=]]> #{projectManageListRequest.startTime}
        </if>

        <if test="projectManageListRequest.endTime != null and projectManageListRequest.endTime>0">
            and p.create_time <![CDATA[<=]]> #{projectManageListRequest.endTime}
        </if>

        <if test="projectManageListRequest.country!=null">
            and p.country = #{projectManageListRequest.country}
        </if>

        <if test="projectManageListRequest.controlType != null">
            <choose>
                <when test="projectManageListRequest.controlType == 1">
                    and exists (
                        select 1
                        from t_group g
                        where g.project_id = p.id
                        and g.capacity_controller = 1
                    )
                </when>
                <when test="projectManageListRequest.controlType == 2">
                    and exists (
                        select 1
                        from t_group g
                        where g.project_id = p.id
                        and g.demand_control != 'disable'
                    )
                </when>
                <when test="projectManageListRequest.controlType == 0">
                    and not exists(
                        select 1
                        from t_group g
                        where g.project_id = p.id
                        and (g.demand_control != 'disable'
                        or g.capacity_controller = 1)
                    )
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
        order by
        <choose>
            <when test="projectManageListRequest.orderRule == 0">
                <choose>
                    <when test="projectManageListRequest.orderShield">
                        p.order ASC, p.create_time DESC
                    </when>
                    <otherwise>
                        p.order_shield ASC, p.create_time DESC
                    </otherwise>
                </choose>
            </when>
            <when test="projectManageListRequest.orderRule == 1">
                <choose>
                    <when test="projectManageListRequest.orderShield">
                        p.order_one ASC, p.create_time DESC
                    </when>
                    <otherwise>
                        p.order_one_shield ASC, p.create_time DESC
                    </otherwise>
                </choose>
            </when>
            <when test="projectManageListRequest.orderRule == 2 ">
                p.create_time DESC
            </when>
            <when test="projectManageListRequest.orderRule == 3 ">
                p.create_time ASC
            </when>
            <otherwise>
                p.create_time ASC
            </otherwise>
        </choose>
    </select>

    <select id="queryProjectManageInfo"
            parameterType="java.lang.String"
            resultMap="ProjectManageVoMap">
        select p.*,
               e.`area_id`,
               e.`area`,
               e.`size`,
               e.`address`,
               e.`longitude`,
               e.`latitude`,
               e.`remark`,
               e.`ems`,
               e.`pcs`,
               e.`bms`,
               l.uuid as power_level_id,
               l.power_level,
               t.uuid as power_type_id,
               t.power_type,
               t.power_type_en,
               e.`enable_radiation`,
               e.`enable_weather`,
               e.`enable_etl`
        from t_project p,
             t_project_ext e,
             t_power_level l,
             t_power_type t
        where p.id = e.id
          and p.whether_delete = false
          and e.power_type = t.uuid
          and e.power_level = l.uuid
          and p.id = #{id}
    </select>

</mapper>
