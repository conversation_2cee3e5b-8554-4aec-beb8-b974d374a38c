package com.wifochina.modules.timezone.controller;

import javax.annotation.Resource;

import com.wifochina.modules.timezone.entity.TimeZoneEntity;
import com.wifochina.modules.timezone.request.TimeZoneCodeRequest;
import com.wifochina.modules.timezone.service.TimeZoneService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.page.Result;
import com.wifochina.modules.country.entity.CountryEntity;
import com.wifochina.modules.country.service.CountryService;
import com.wifochina.modules.timezone.request.TimeZoneRequest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @since 2023-12-22
 */
@RestController
@RequestMapping("/timezone")
@Api(tags = "97-时区")
public class TimeZoneController {
    @Resource
    private TimeZoneService timeZoneService;

    /**
     * 获取时区列表
     */
    @PostMapping("/get")
    @ApiOperation("获取时区详情")
    public Result<TimeZoneEntity> get(@RequestBody TimeZoneCodeRequest timeZoneCodeRequest) {
        TimeZoneEntity timeZone =
            timeZoneService.lambdaQuery().eq(TimeZoneEntity::getTimezone, timeZoneCodeRequest.getTimeZone()).one();
        return Result.success(timeZone);
    }

    /**
     * 获取时区列表
     */
    @PostMapping("/list")
    @ApiOperation("获取时区分页列表")
    public Result<Page<TimeZoneEntity>> list(@RequestBody PageBean pageBean) {
        Page<TimeZoneEntity> list = timeZoneService.page(Page.of(pageBean.getPageNum(), pageBean.getPageSize()));
        return Result.success(list);
    }
}
