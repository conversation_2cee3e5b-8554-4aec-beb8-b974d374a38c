package com.wifochina.modules.operation.VO;

import com.wifochina.modules.renewable.vo.RenewableProfitVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.Map;

/**
 * ProfitVO
 *
 * @date 4/14/2022 10:25 AM
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value = "运营收益统计结果")
@Data
public class ProfitVO {

    public ProfitVO() {
        this.setPv_discharge_benefit("0");
        this.setPv_discharge_quantity("0");
        this.setWind_discharge_benefit("0");
        this.setWind_discharge_quantity("0");
        this.setWaster_discharge_benefit("0");
        this.setWaster_discharge_quantity("0");
        this.setDemand_control_benefit("0");
        this.setDemand_control_power("0");

        this.setPvProfitVo(new RenewableProfitVo());
        this.setWindProfitVo(new RenewableProfitVo());
        this.setWasterProfitVo(new RenewableProfitVo());
    }

    @ApiModelProperty(value = "储能充放电总利润")
    private String electric_benefit;

    @ApiModelProperty(value = "今日总利润")
    private String today_benefit;

    @ApiModelProperty(value = "昨日总利润")
    private String yesterday_benefit;

    @ApiModelProperty(value = "降低需量功率")
    private String demand_control_power;

    @ApiModelProperty(value = "降低需量单价")
    private String demand_control_price;

    @ApiModelProperty(value = "需量控制收益")
    private String demand_control_benefit;

    @ApiModelProperty(value = "分组需量")
    private Map<String, GroupDemandProfit> demandProfitMap;

    @ApiModelProperty(value = "分组储能收益, key分组Id")
    private Map<String, GroupElectricProfit> groupElectricProfitMap;

    @ApiModelProperty(value = "PV总发电电量")
    private String pv_discharge_quantity;

    @ApiModelProperty(value = "PV总发电收益")
    private String pv_discharge_benefit;

    @ApiModelProperty(value = "风电总发电电量")
    private String wind_discharge_quantity;

    @ApiModelProperty(value = "风电总发电收益")
    private String wind_discharge_benefit;

    @ApiModelProperty(value = "余热发电总发电电量")
    private String waster_discharge_quantity;

    @ApiModelProperty(value = "余热发电总发电收益")
    private String waster_discharge_benefit;

    /** 峰：peak 平：flat 谷：valley 尖： tip 充电：charge 放电：discharge 量：quantity 成本：cost */
    @ApiModelProperty(value = "总充电电量KWH")
    private String total_charge_quantity;

    @ApiModelProperty(value = "尖充电量KWH")
    private String tip_charge_quantity;

    @ApiModelProperty(value = "峰充电量KWH")
    private String peak_charge_quantity;

    @ApiModelProperty(value = "平充电量")
    private String flat_charge_quantity;

    @ApiModelProperty(value = "谷充电量")
    private String vally_charge_quantity;

    @ApiModelProperty(value = "总充电成本")
    private String total_charge_cost;

    @ApiModelProperty(value = "尖充电量成本")
    private String tip_charge_cost;

    @ApiModelProperty(value = "峰充电量成本")
    private String peak_charge_cost;

    @ApiModelProperty(value = "平充电量成本")
    private String flat_charge_cost;

    @ApiModelProperty(value = "谷充电量成本")
    private String vally_charge_cost;

    @ApiModelProperty(value = "总放电电量")
    private String total_discharge_quantity;

    @ApiModelProperty(value = "尖放电量")
    private String tip_discharge_quantity;

    @ApiModelProperty(value = "峰放电量")
    private String peak_discharge_quantity;

    @ApiModelProperty(value = "平放电量")
    private String flat_discharge_quantity;

    @ApiModelProperty(value = "谷放电量")
    private String vally_discharge_quantity;

    @ApiModelProperty(value = "总放电收益")
    private String total_discharge_benefit;

    @ApiModelProperty(value = "尖放电量收益")
    private String tip_discharge_benefit;

    @ApiModelProperty(value = "峰放电量收益")
    private String peak_discharge_benefit;

    @ApiModelProperty(value = "平放电量收益")
    private String flat_discharge_benefit;

    @ApiModelProperty(value = "谷放电量收益")
    private String vally_discharge_benefit;

    @ApiModelProperty(value = "深谷充电量")
    private String deep_vally_charge_quantity;

    @ApiModelProperty(value = "深谷充电量成本")
    private String deep_vally_charge_cost;

    @ApiModelProperty(value = "深谷放电量")
    private String deep_vally_discharge_quantity;

    @ApiModelProperty(value = "深谷放电量收益")
    private String deep_vally_discharge_benefit;

    /** 2023-11-17 11:06:52 add 新增一个总收益字段 扩展字段 用于前端展示 重复于TotalProfitVO */
    @ApiModelProperty(value = "总收益(储能+需量+风电+ PV(根据PV收益开关判断))")
    private String total_benefit_extend;

    @ApiModelProperty(value = "pv的收益明细")
    private RenewableProfitVo pvProfitVo;

    @ApiModelProperty(value = "wind的收益明细")
    private RenewableProfitVo windProfitVo;

    @ApiModelProperty(value = "waster的收益明细")
    private RenewableProfitVo wasterProfitVo;
}
