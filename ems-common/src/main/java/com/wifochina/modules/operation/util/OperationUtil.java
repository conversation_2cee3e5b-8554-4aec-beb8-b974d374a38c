package com.wifochina.modules.operation.util;

import cn.hutool.extra.spring.SpringUtil;

import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.remedies.entity.EmsDataRemediesEntity;

import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-10-18 11:03 AM
 */
public class OperationUtil {

    public static ElectricProfitVO getDynamicPeriodElectricProfitVo(
            ElectricDynamicPeriodEntity electricDynamicPeriodEntity) {
        ElectricProfitVO electricProfitVO = new ElectricProfitVO();
        electricProfitVO.init();
        electricProfitVO.setTotalBenefit(electricDynamicPeriodEntity.getTotalBenefit());
        electricProfitVO.setTotalChargeQuantity(electricDynamicPeriodEntity.getChargeQuantity());
        electricProfitVO.setTotalDischargeQuantity(
                electricDynamicPeriodEntity.getDischargeQuantity());
        electricProfitVO.setTotalChargeCost(electricDynamicPeriodEntity.getChargeCost());
        electricProfitVO.setTotalDischargeBenefit(
                electricDynamicPeriodEntity.getDischargeBenefit());
        return electricProfitVO;
    }

    public static ElectricProfitVO getPeakValleysPeriodElectricProfitVo(
            ElectricEntity electricEntity) {
        ElectricProfitVO electricProfitVO = new ElectricProfitVO();
        electricProfitVO.setTipChargeQuantity(electricEntity.getTipChargeQuantity());
        electricProfitVO.setPeakChargeQuantity(electricEntity.getPeakChargeQuantity());
        electricProfitVO.setFlatChargeQuantity(electricEntity.getFlatChargeQuantity());
        electricProfitVO.setVallyChargeQuantity(electricEntity.getVallyChargeQuantity());
        electricProfitVO.setDeepVallyChargeQuantity(electricEntity.getDeepVallyChargeQuantity());
        electricProfitVO.setTipChargeCost(electricEntity.getTipChargeCost());
        electricProfitVO.setPeakChargeCost(electricEntity.getPeakChargeCost());
        electricProfitVO.setFlatChargeCost(electricEntity.getFlatChargeCost());
        electricProfitVO.setVallyChargeCost(electricEntity.getVallyChargeCost());
        electricProfitVO.setDeepVallyChargeCost(electricEntity.getDeepVallyChargeCost());
        electricProfitVO.setPeakDischargeQuantity(electricEntity.getPeakDischargeQuantity());
        electricProfitVO.setVallyDischargeQuantity(electricEntity.getVallyDischargeQuantity());
        electricProfitVO.setFlatDischargeQuantity(electricEntity.getFlatDischargeQuantity());
        electricProfitVO.setTipDischargeQuantity(electricEntity.getTipDischargeQuantity());
        electricProfitVO.setDeepVallyDischargeQuantity(
                electricEntity.getDeepVallyDischargeQuantity());
        electricProfitVO.setPeakDischargeBenefit(electricEntity.getPeakDischargeBenefit());
        electricProfitVO.setTipDischargeBenefit(electricEntity.getTipDischargeBenefit());
        electricProfitVO.setFlatDischargeBenefit(electricEntity.getFlatDischargeBenefit());
        electricProfitVO.setVallyDischargeBenefit(electricEntity.getVallyDischargeBenefit());
        electricProfitVO.setDeepVallyDischargeBenefit(
                electricEntity.getDeepVallyDischargeBenefit());
        sumElectricProfitVoTotal(electricProfitVO);
        return electricProfitVO;
    }

    public static void addRemediesCommon(
            ElectricProfitVO electricProfitVO, ElectricProfitVO remedies) {
        double peakChargeQuantity =
                electricProfitVO.getPeakChargeQuantity() + remedies.getPeakChargeQuantity();
        double peakChargeCost = electricProfitVO.getPeakChargeCost() + remedies.getPeakChargeCost();
        electricProfitVO.setPeakChargeQuantity(peakChargeQuantity > 0 ? peakChargeQuantity : 0);
        electricProfitVO.setPeakChargeCost(peakChargeCost > 0 ? peakChargeCost : 0);
        double peakDischargeQuantity =
                electricProfitVO.getPeakDischargeQuantity() + remedies.getPeakDischargeQuantity();
        double peakDischargeBenefit =
                electricProfitVO.getPeakDischargeBenefit() + remedies.getPeakDischargeBenefit();
        electricProfitVO.setPeakDischargeQuantity(
                peakDischargeQuantity > 0 ? peakDischargeQuantity : 0);
        electricProfitVO.setPeakDischargeBenefit(
                peakDischargeBenefit > 0 ? peakDischargeBenefit : 0);
        double vallyChargeQuantity =
                electricProfitVO.getVallyChargeQuantity() + remedies.getVallyChargeQuantity();
        double vallyChargeCost =
                electricProfitVO.getVallyChargeCost() + remedies.getVallyChargeCost();
        electricProfitVO.setVallyChargeQuantity(vallyChargeQuantity > 0 ? vallyChargeQuantity : 0);
        electricProfitVO.setVallyChargeCost(vallyChargeCost > 0 ? vallyChargeCost : 0);
        double vallyDischargeQuantity =
                electricProfitVO.getVallyDischargeQuantity() + remedies.getVallyDischargeQuantity();
        double vallyDischargeBenefit =
                electricProfitVO.getVallyDischargeBenefit() + remedies.getVallyDischargeBenefit();
        electricProfitVO.setVallyDischargeQuantity(
                vallyDischargeQuantity > 0 ? vallyDischargeQuantity : 0);
        electricProfitVO.setVallyDischargeBenefit(
                vallyDischargeBenefit > 0 ? vallyDischargeBenefit : 0);
        double flatChargeQuantity =
                electricProfitVO.getFlatChargeQuantity() + remedies.getFlatChargeQuantity();
        double flatChargeCost = electricProfitVO.getFlatChargeCost() + remedies.getFlatChargeCost();
        electricProfitVO.setFlatChargeQuantity(flatChargeQuantity > 0 ? flatChargeQuantity : 0);
        electricProfitVO.setFlatChargeCost(flatChargeCost > 0 ? flatChargeCost : 0);
        double flatDischargeQuantity =
                electricProfitVO.getFlatDischargeQuantity() + remedies.getFlatDischargeQuantity();
        double flatDischargeBenefit =
                electricProfitVO.getFlatDischargeBenefit() + remedies.getFlatDischargeBenefit();
        electricProfitVO.setFlatDischargeQuantity(
                flatDischargeQuantity > 0 ? flatDischargeQuantity : 0);
        electricProfitVO.setFlatDischargeBenefit(
                flatDischargeBenefit > 0 ? flatDischargeBenefit : 0);
        double tipChargeQuantity =
                electricProfitVO.getTipChargeQuantity() + remedies.getTipChargeQuantity();
        double tipChargeCost = electricProfitVO.getTipChargeCost() + remedies.getTipChargeCost();
        electricProfitVO.setTipChargeQuantity(tipChargeQuantity > 0 ? tipChargeQuantity : 0);
        electricProfitVO.setTipChargeCost(tipChargeCost > 0 ? tipChargeCost : 0);
        double tipDischargeQuantity =
                electricProfitVO.getTipDischargeQuantity() + remedies.getTipDischargeQuantity();
        double tipDischargeBenefit =
                electricProfitVO.getTipDischargeBenefit() + remedies.getTipDischargeBenefit();
        electricProfitVO.setTipDischargeQuantity(
                tipDischargeQuantity > 0 ? tipDischargeQuantity : 0);
        electricProfitVO.setTipDischargeBenefit(tipDischargeBenefit > 0 ? tipDischargeBenefit : 0);
        double deepVallyChargeQuantity =
                electricProfitVO.getDeepVallyChargeQuantity()
                        + remedies.getDeepVallyChargeQuantity();
        double deepVallyChargeCost =
                electricProfitVO.getDeepVallyChargeCost() + remedies.getDeepVallyChargeCost();
        electricProfitVO.setDeepVallyChargeQuantity(
                deepVallyChargeQuantity > 0 ? deepVallyChargeQuantity : 0);
        electricProfitVO.setDeepVallyChargeCost(deepVallyChargeCost > 0 ? deepVallyChargeCost : 0);
        double deepVallyDischargeQuantity =
                electricProfitVO.getDeepVallyDischargeQuantity()
                        + remedies.getDeepVallyDischargeQuantity();
        double deepVallyDischargeBenefit =
                electricProfitVO.getDeepVallyDischargeBenefit()
                        + remedies.getDeepVallyDischargeBenefit();
        electricProfitVO.setDeepVallyDischargeQuantity(
                deepVallyDischargeQuantity > 0 ? deepVallyDischargeQuantity : 0);
        electricProfitVO.setDeepVallyDischargeBenefit(
                deepVallyDischargeBenefit > 0 ? deepVallyDischargeBenefit : 0);
    }

    /** 只是把 为了 dynamic 方式 的补值操作 remedies 目前只是today的 数据 要加上去的 */
    public static void addRemediesForDynamic(
            ElectricProfitVO electricProfitVO, ElectricProfitVO remedies) {
        if (electricProfitVO == null) {
            electricProfitVO = new ElectricProfitVO();
            electricProfitVO.init();
        }
        electricProfitVO.setTotalChargeQuantity(
                electricProfitVO.getTotalChargeQuantity() + remedies.getTotalChargeQuantity());
        electricProfitVO.setTotalDischargeQuantity(
                electricProfitVO.getTotalDischargeQuantity()
                        + remedies.getTotalDischargeQuantity());
        electricProfitVO.setTotalChargeCost(
                electricProfitVO.getTotalChargeCost() + remedies.getTotalChargeCost());
        electricProfitVO.setTotalDischargeBenefit(
                electricProfitVO.getTotalDischargeBenefit() + remedies.getTotalDischargeBenefit());
        electricProfitVO.setTotalBenefit(
                electricProfitVO.getTotalBenefit() + remedies.getTotalBenefit());
    }

    public static void addRemedies(ElectricProfitVO electricProfitVO, ElectricProfitVO remedies) {
        if (electricProfitVO == null) {
            electricProfitVO = new ElectricProfitVO();
            electricProfitVO.init();
        }
        addRemediesCommon(electricProfitVO, remedies);
        sumElectricProfitVoTotal(electricProfitVO);
    }

    public static void sumElectricProfitVoTotal(ElectricProfitVO electricProfitVO) {
        electricProfitVO.setTotalChargeQuantity(
                electricProfitVO.getPeakChargeQuantity()
                        + electricProfitVO.getVallyChargeQuantity()
                        + electricProfitVO.getFlatChargeQuantity()
                        + electricProfitVO.getTipChargeQuantity()
                        + electricProfitVO.getDeepVallyChargeQuantity());
        electricProfitVO.setTotalChargeCost(
                electricProfitVO.getPeakChargeCost()
                        + electricProfitVO.getVallyChargeCost()
                        + electricProfitVO.getFlatChargeCost()
                        + electricProfitVO.getTipChargeCost()
                        + electricProfitVO.getDeepVallyChargeCost());
        electricProfitVO.setTotalDischargeQuantity(
                electricProfitVO.getPeakDischargeQuantity()
                        + electricProfitVO.getVallyDischargeQuantity()
                        + electricProfitVO.getFlatDischargeQuantity()
                        + electricProfitVO.getTipDischargeQuantity()
                        + electricProfitVO.getDeepVallyDischargeQuantity());
        electricProfitVO.setTotalDischargeBenefit(
                electricProfitVO.getPeakDischargeBenefit()
                        + electricProfitVO.getVallyDischargeBenefit()
                        + electricProfitVO.getFlatDischargeBenefit()
                        + electricProfitVO.getTipDischargeBenefit()
                        + electricProfitVO.getDeepVallyDischargeBenefit());
        electricProfitVO.setTotalBenefit(
                electricProfitVO.getTotalDischargeBenefit()
                        - electricProfitVO.getTotalChargeCost());
    }

    public static List<String> getIncomeDeviceIds(
            String projectId, DeviceService deviceService, AmmeterService ammeterService) {
        List<DeviceEntity> deviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getIncome, true)
                        .eq(DeviceEntity::getProjectId, projectId)
                        .list();
        List<AmmeterEntity> ammeterEntities =
                ammeterService
                        .lambdaQuery()
                        .eq(AmmeterEntity::getIncome, true)
                        .eq(AmmeterEntity::getProjectId, projectId)
                        .list();
        List<String> deviceIds = new ArrayList<>(deviceEntities.size() + ammeterEntities.size());
        deviceEntities.forEach(e -> deviceIds.add(e.getId()));
        ammeterEntities.forEach(e -> deviceIds.add(e.getId()));
        return deviceIds;
    }

    public static List<String> getIncomeDeviceIdsForGroup(
            String projectId, String groupId, GroupService groupService) {
        return groupService.getIncomeDeviceAmmeterIdsForGroup(projectId, groupId);
    }

    public static void addRemedies(ElectricEntity electricEntity, EmsDataRemediesEntity remedies) {
        double peakChargeQuantity =
                electricEntity.getPeakChargeQuantity() + remedies.getPeakHistoryInputEnergy();
        electricEntity.setPeakChargeQuantity(peakChargeQuantity > 0 ? peakChargeQuantity : 0);
        double peakDischargeQuantity =
                electricEntity.getPeakDischargeQuantity() + remedies.getPeakHistoryOutputEnergy();
        electricEntity.setPeakDischargeQuantity(
                peakDischargeQuantity > 0 ? peakDischargeQuantity : 0);
        double vallyChargeQuantity =
                electricEntity.getVallyChargeQuantity() + remedies.getVallyHistoryInputEnergy();
        electricEntity.setVallyChargeQuantity(vallyChargeQuantity > 0 ? vallyChargeQuantity : 0);
        double vallyDischargeQuantity =
                electricEntity.getVallyDischargeQuantity() + remedies.getVallyHistoryOutputEnergy();
        electricEntity.setVallyDischargeQuantity(
                vallyDischargeQuantity > 0 ? vallyDischargeQuantity : 0);
        double flatChargeQuantity =
                electricEntity.getFlatChargeQuantity() + remedies.getFlatHistoryInputEnergy();
        electricEntity.setFlatChargeQuantity(flatChargeQuantity > 0 ? flatChargeQuantity : 0);
        double flatDischargeQuantity =
                electricEntity.getFlatDischargeQuantity() + remedies.getFlatHistoryOutputEnergy();
        electricEntity.setFlatDischargeQuantity(
                flatDischargeQuantity > 0 ? flatDischargeQuantity : 0);
        double tipChargeQuantity =
                electricEntity.getTipChargeQuantity() + remedies.getTipHistoryInputEnergy();
        electricEntity.setTipChargeQuantity(tipChargeQuantity > 0 ? tipChargeQuantity : 0);
        double tipDischargeQuantity =
                electricEntity.getTipDischargeQuantity() + remedies.getTipHistoryOutputEnergy();
        electricEntity.setTipDischargeQuantity(tipDischargeQuantity > 0 ? tipDischargeQuantity : 0);
        double deepVallyChargeQuantity =
                electricEntity.getDeepVallyChargeQuantity()
                        + remedies.getDeepVallyHistoryInputEnergy();
        electricEntity.setDeepVallyChargeQuantity(
                deepVallyChargeQuantity > 0 ? deepVallyChargeQuantity : 0);
        double deepVallyDischargeQuantity =
                electricEntity.getDeepVallyDischargeQuantity()
                        + remedies.getDeepVallyHistoryOutputEnergy();
        electricEntity.setDeepVallyDischargeQuantity(
                deepVallyDischargeQuantity > 0 ? deepVallyDischargeQuantity : 0);
        electricEntity.setTotalChargeQuantity(
                peakChargeQuantity
                        + vallyChargeQuantity
                        + flatChargeQuantity
                        + tipChargeQuantity
                        + deepVallyChargeQuantity);
        electricEntity.setTotalDischargeQuantity(
                peakDischargeQuantity
                        + vallyDischargeQuantity
                        + flatDischargeQuantity
                        + tipDischargeQuantity
                        + deepVallyDischargeQuantity);
    }

    /** 既可以用于补差值，也可以用于求和 */
    public static void addRemediesList(
            List<ElectricEntity> electricEntities, List<EmsDataRemediesEntity> remediesEntities) {
        Map<Long, EmsDataRemediesEntity> emsDataRemediesEntityMap =
                remediesEntities.stream()
                        .collect(Collectors.toMap(EmsDataRemediesEntity::getTime, e -> e));
        for (ElectricEntity electricEntity : electricEntities) {
            EmsDataRemediesEntity remedies = emsDataRemediesEntityMap.get(electricEntity.getTime());
            Optional.ofNullable(remedies)
                    .ifPresent(
                            e -> {
                                double peakChargeQuantity =
                                        electricEntity.getPeakChargeQuantity()
                                                + remedies.getPeakHistoryInputEnergy();
                                electricEntity.setPeakChargeQuantity(
                                        peakChargeQuantity > 0 ? peakChargeQuantity : 0);
                                electricEntity.setPeakChargeCost(
                                        electricEntity.getPeakChargeQuantity()
                                                * electricEntity.getPeakBuyPrice());
                                double peakDischargeQuantity =
                                        electricEntity.getPeakDischargeQuantity()
                                                + remedies.getPeakHistoryOutputEnergy();
                                electricEntity.setPeakDischargeQuantity(
                                        peakDischargeQuantity > 0 ? peakDischargeQuantity : 0);
                                electricEntity.setPeakDischargeBenefit(
                                        electricEntity.getPeakDischargeQuantity()
                                                * electricEntity.getPeakSellPrice());
                                double vallyChargeQuantity =
                                        electricEntity.getVallyChargeQuantity()
                                                + remedies.getVallyHistoryInputEnergy();
                                electricEntity.setVallyChargeQuantity(
                                        vallyChargeQuantity > 0 ? vallyChargeQuantity : 0);
                                electricEntity.setVallyChargeCost(
                                        electricEntity.getVallyChargeQuantity()
                                                * electricEntity.getVallyBuyPrice());
                                double vallyDischargeQuantity =
                                        electricEntity.getVallyDischargeQuantity()
                                                + remedies.getVallyHistoryOutputEnergy();
                                electricEntity.setVallyDischargeQuantity(
                                        vallyDischargeQuantity > 0 ? vallyDischargeQuantity : 0);
                                electricEntity.setVallyDischargeBenefit(
                                        electricEntity.getVallyDischargeQuantity()
                                                * electricEntity.getVallySellPrice());
                                double flatChargeQuantity =
                                        electricEntity.getFlatChargeQuantity()
                                                + remedies.getFlatHistoryInputEnergy();
                                electricEntity.setFlatChargeQuantity(
                                        flatChargeQuantity > 0 ? flatChargeQuantity : 0);
                                electricEntity.setFlatChargeCost(
                                        electricEntity.getFlatChargeQuantity()
                                                * electricEntity.getFlatBuyPrice());
                                double flatDischargeQuantity =
                                        electricEntity.getFlatDischargeQuantity()
                                                + remedies.getFlatHistoryOutputEnergy();
                                electricEntity.setFlatDischargeQuantity(
                                        flatDischargeQuantity > 0 ? flatDischargeQuantity : 0);
                                electricEntity.setFlatDischargeBenefit(
                                        electricEntity.getFlatDischargeQuantity()
                                                * electricEntity.getFlatSellPrice());
                                double tipChargeQuantity =
                                        electricEntity.getTipChargeQuantity()
                                                + remedies.getTipHistoryInputEnergy();
                                electricEntity.setTipChargeQuantity(
                                        tipChargeQuantity > 0 ? tipChargeQuantity : 0);
                                electricEntity.setTipChargeCost(
                                        electricEntity.getTipChargeQuantity()
                                                * electricEntity.getTipBuyPrice());
                                double tipDischargeQuantity =
                                        electricEntity.getTipDischargeQuantity()
                                                + remedies.getTipHistoryOutputEnergy();
                                electricEntity.setTipDischargeQuantity(
                                        tipDischargeQuantity > 0 ? tipDischargeQuantity : 0);
                                electricEntity.setTipDischargeBenefit(
                                        electricEntity.getTipDischargeQuantity()
                                                * electricEntity.getTipSellPrice());
                                double deepVallyChargeQuantity =
                                        electricEntity.getDeepVallyChargeQuantity()
                                                + remedies.getDeepVallyHistoryInputEnergy();
                                electricEntity.setDeepVallyChargeQuantity(
                                        deepVallyChargeQuantity > 0 ? deepVallyChargeQuantity : 0);
                                electricEntity.setDeepVallyChargeCost(
                                        electricEntity.getDeepVallyChargeQuantity()
                                                * electricEntity.getDeepVallyBuyPrice());
                                double deepVallyDischargeQuantity =
                                        electricEntity.getDeepVallyDischargeQuantity()
                                                + remedies.getDeepVallyHistoryOutputEnergy();
                                electricEntity.setDeepVallyDischargeQuantity(
                                        deepVallyDischargeQuantity > 0
                                                ? deepVallyDischargeQuantity
                                                : 0);
                                electricEntity.setDeepVallyDischargeBenefit(
                                        electricEntity.getDeepVallyDischargeQuantity()
                                                * electricEntity.getDeepVallySellPrice());
                                electricEntity.setTotalChargeQuantity(
                                        peakChargeQuantity
                                                + vallyChargeQuantity
                                                + flatChargeQuantity
                                                + tipChargeQuantity
                                                + deepVallyChargeQuantity);
                                electricEntity.setTotalDischargeQuantity(
                                        peakDischargeQuantity
                                                + vallyDischargeQuantity
                                                + flatDischargeQuantity
                                                + tipDischargeQuantity
                                                + deepVallyDischargeQuantity);
                                electricEntity.setTotalChargeCost(
                                        electricEntity.getPeakChargeCost()
                                                + electricEntity.getVallyChargeCost()
                                                + electricEntity.getFlatChargeCost()
                                                + electricEntity.getTipChargeCost()
                                                + electricEntity.getDeepVallyChargeCost());
                                electricEntity.setTotalDischargeBenefit(
                                        electricEntity.getPeakDischargeBenefit()
                                                + electricEntity.getVallyDischargeBenefit()
                                                + electricEntity.getFlatDischargeBenefit()
                                                + electricEntity.getTipDischargeBenefit()
                                                + electricEntity.getDeepVallyDischargeBenefit());
                                electricEntity.setTotalBenefit(
                                        electricEntity.getTotalDischargeBenefit()
                                                - electricEntity.getTotalChargeCost());
                            });
        }
    }

    /** ElectricEntity 求和 */
    public static ElectricEntity sumElectricEntities(List<ElectricEntity> electricEntities) {
        ElectricEntity electricEntity = new ElectricEntity();
        if (!electricEntities.isEmpty()) {
            electricEntity.setPeakChargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getPeakChargeQuantity)
                            .sum());
            electricEntity.setPeakChargeCost(
                    electricEntities.stream().mapToDouble(ElectricEntity::getPeakChargeCost).sum());
            electricEntity.setPeakDischargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getPeakDischargeQuantity)
                            .sum());
            electricEntity.setPeakDischargeBenefit(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getPeakDischargeBenefit)
                            .sum());
            electricEntity.setVallyChargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getVallyChargeQuantity)
                            .sum());
            electricEntity.setVallyChargeCost(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getVallyChargeCost)
                            .sum());
            electricEntity.setVallyDischargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getVallyDischargeQuantity)
                            .sum());
            electricEntity.setVallyDischargeBenefit(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getVallyDischargeBenefit)
                            .sum());
            electricEntity.setFlatChargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getFlatChargeQuantity)
                            .sum());
            electricEntity.setFlatChargeCost(
                    electricEntities.stream().mapToDouble(ElectricEntity::getFlatChargeCost).sum());
            electricEntity.setFlatDischargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getFlatDischargeQuantity)
                            .sum());
            electricEntity.setFlatDischargeBenefit(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getFlatDischargeBenefit)
                            .sum());
            electricEntity.setTipChargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getTipChargeQuantity)
                            .sum());
            electricEntity.setTipChargeCost(
                    electricEntities.stream().mapToDouble(ElectricEntity::getTipChargeCost).sum());
            electricEntity.setTipDischargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getTipDischargeQuantity)
                            .sum());
            electricEntity.setTipDischargeBenefit(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getTipDischargeBenefit)
                            .sum());
            electricEntity.setDeepVallyChargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getDeepVallyChargeQuantity)
                            .sum());
            electricEntity.setDeepVallyChargeCost(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getDeepVallyChargeCost)
                            .sum());
            electricEntity.setDeepVallyDischargeQuantity(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getDeepVallyDischargeQuantity)
                            .sum());
            electricEntity.setDeepVallyDischargeBenefit(
                    electricEntities.stream()
                            .mapToDouble(ElectricEntity::getDeepVallyDischargeBenefit)
                            .sum());
            electricEntity.setTotalChargeQuantity(
                    electricEntity.getPeakChargeQuantity()
                            + electricEntity.getVallyChargeQuantity()
                            + electricEntity.getFlatChargeQuantity()
                            + electricEntity.getTipChargeQuantity()
                            + electricEntity.getDeepVallyChargeQuantity());
            electricEntity.setTotalChargeCost(
                    electricEntity.getPeakChargeCost()
                            + electricEntity.getVallyChargeCost()
                            + electricEntity.getFlatChargeCost()
                            + electricEntity.getTipChargeCost()
                            + electricEntity.getDeepVallyChargeCost());
            electricEntity.setTotalDischargeQuantity(
                    electricEntity.getPeakDischargeQuantity()
                            + electricEntity.getVallyDischargeQuantity()
                            + electricEntity.getFlatDischargeQuantity()
                            + electricEntity.getTipDischargeQuantity()
                            + electricEntity.getDeepVallyDischargeQuantity());
            electricEntity.setTotalDischargeBenefit(
                    electricEntity.getPeakDischargeBenefit()
                            + electricEntity.getVallyDischargeBenefit()
                            + electricEntity.getFlatDischargeBenefit()
                            + electricEntity.getTipDischargeBenefit()
                            + electricEntity.getDeepVallyDischargeBenefit());
            for (ElectricEntity entity : electricEntities) {
                updatePriceIfZero(electricEntity, entity, "peakBuyPrice");
                updatePriceIfZero(electricEntity, entity, "peakSellPrice");
                updatePriceIfZero(electricEntity, entity, "vallyBuyPrice");
                updatePriceIfZero(electricEntity, entity, "vallySellPrice");
                updatePriceIfZero(electricEntity, entity, "flatBuyPrice");
                updatePriceIfZero(electricEntity, entity, "flatSellPrice");
                updatePriceIfZero(electricEntity, entity, "tipBuyPrice");
                updatePriceIfZero(electricEntity, entity, "tipSellPrice");
                updatePriceIfZero(electricEntity, entity, "deepVallyBuyPrice");
                updatePriceIfZero(electricEntity, entity, "deepVallySellPrice");
            }
            electricEntity.setYear(electricEntities.get(0).getYear());
            electricEntity.setMonth(electricEntities.get(0).getMonth());
            return electricEntity;
        } else {
            return null;
        }
    }

    private static void updatePriceIfZero(
            ElectricEntity electricEntity, ElectricEntity sourceEntity, String fieldName) {
        try {
            Field field = ElectricEntity.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            double currentValue = (double) field.get(electricEntity);
            if (currentValue == 0) {
                double sourceFieldValue = (double) field.get(sourceEntity);
                field.set(electricEntity, sourceFieldValue);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("Failed to access or modify field", e);
        }
    }

    public static void main(String[] args) {
        //        List<ElectricPriceEntity> timeSharingCacheHalfHourPeriods =
        //                getTimeSharingCacheHalfHourPeriods(null);
    }

    public static List<ElectricPriceEntity> getTimeSharingCacheHalfHourPeriods(
            ElectricPriceEntity matchPriceEntity) {
        List<ElectricPriceEntity> list = new ArrayList<>();

        LocalTime startLocalTime = LocalTime.of(0, 0, 0);
        LocalTime endLocalTime = null;
        for (int i = 0; i < 48; i++) {
            if (endLocalTime != null) {
                startLocalTime = endLocalTime;
            }
            endLocalTime = startLocalTime.plusMinutes(30);
            ElectricPriceEntity periodEntity =
                    new ElectricPriceEntity().setStartTime(startLocalTime).setEndTime(endLocalTime);
            list.add(periodEntity);
        }

        for (ElectricPriceEntity matchPeriod : matchPriceEntity.getPeriodPriceList()) {
            LocalTime matchStartTime = matchPeriod.getStartTime();
            LocalTime matchEndTime = matchPeriod.getEndTime();
            double buyPrice = matchPeriod.getBuyPrice();
            double sellPrice = matchPeriod.getSellPrice();
            for (ElectricPriceEntity periodEntity : list) {
                LocalTime startTime = periodEntity.getStartTime();
                if ((startTime.equals(matchStartTime) || startTime.isAfter(matchStartTime))
                        && (startTime.isBefore(matchEndTime)
                                || (matchEndTime.equals(LocalTime.MIDNIGHT)
                                        && !startTime.equals(LocalTime.MIDNIGHT)))) {
                    periodEntity.setBuyPrice(buyPrice);
                    periodEntity.setSellPrice(sellPrice);
                }
            }
        }
        return list;
    }

    public static List<ElectricPriceEntity> getTimeSharingCachePeriods(
            ElectricPriceEntity matchPriceEntity) {
        // 第一步 构建24个 E List
        List<ElectricPriceEntity> list = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            LocalTime startLocalTime = LocalTime.of(i, 0, 0);
            LocalTime endLocalTime = LocalTime.of((i + 1) % 24, 0, 0);
            ElectricPriceEntity periodEntity =
                    new ElectricPriceEntity().setStartTime(startLocalTime).setEndTime(endLocalTime);
            list.add(periodEntity);
        }
        // 将 matchPeriod 里面的价格分配到24条记录中
        for (ElectricPriceEntity matchPeriod : matchPriceEntity.getPeriodPriceList()) {
            LocalTime matchStartTime = matchPeriod.getStartTime();
            LocalTime matchEndTime = matchPeriod.getEndTime();
            double buyPrice = matchPeriod.getBuyPrice();
            double sellPrice = matchPeriod.getSellPrice();
            for (ElectricPriceEntity periodEntity : list) {
                LocalTime startTime = periodEntity.getStartTime();
                if ((startTime.equals(matchStartTime) || startTime.isAfter(matchStartTime))
                        && (startTime.isBefore(matchEndTime)
                                || (matchEndTime.equals(LocalTime.MIDNIGHT)
                                        && !startTime.equals(LocalTime.MIDNIGHT)))) {
                    periodEntity.setBuyPrice(buyPrice);
                    periodEntity.setSellPrice(sellPrice);
                }
            }
        }
        return list;
    }

    public static ProfitRequest preProcessingRequest(
            ProfitRequest profitRequest, String projectId) {
        GroupService groupService = SpringUtil.getBean(GroupService.class);
        if (!StringUtils.hasLength(profitRequest.getDeviceId())) {
            if (!StringUtils.hasLength(profitRequest.getGroupId())
                    || EmsConstants.ALL.equals(profitRequest.getGroupId())) {
                GroupEntity systemGroup = groupService.systemGroupEntity(projectId);
                profitRequest.setGroupId(systemGroup.getId());
                profitRequest.setDeviceId(EmsConstants.ALL);
            }
        }
        return profitRequest;
    }
}
