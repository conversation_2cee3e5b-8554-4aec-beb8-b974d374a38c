package com.wifochina.modules.operation.service.impl;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-03-13 3:37 PM
 */
@Component
public class OperationYearQuartzServiceImpl extends AbstractOperationQuartzService {
    @Override
    public String getOperationTriggerName(String timezoneCode) {
        return String.format("operation-year-trigger:%s", timezoneCode);
    }

    @Override
    public String getOperationJobName(String timezoneCode) {
        return String.format("operation-year-job:%s", timezoneCode);
    }

    @Override
    public String getJobCrontab() {
        return "0 0 3 1 1 ?";
    }
}
