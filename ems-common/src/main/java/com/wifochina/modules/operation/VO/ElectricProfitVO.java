package com.wifochina.modules.operation.VO;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * ProfitVO
 *
 * @date 4/14/2022 10:25 AM
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value = "电池收益统计结果")
@Data
public class ElectricProfitVO {

    @ApiModelProperty(value = "储能充放电总收益 = 放电收益-充电成本")
    @JsonProperty("total_benefit")
    private Double totalBenefit;

    /** 峰：peak 平：flat 谷：valley 尖： tip 充电：charge 放电：discharge 量：quantity 成本：cost */
    @ApiModelProperty(value = "总充电电量KWH")
    @JsonProperty("total_charge_quantity")
    private Double totalChargeQuantity;

    @ApiModelProperty(value = "尖充电量KWH")
    @JsonProperty("tip_charge_quantity")
    private Double tipChargeQuantity;

    @ApiModelProperty(value = "峰充电量KWH")
    @JsonProperty("peak_charge_quantity")
    private Double peakChargeQuantity;

    @ApiModelProperty(value = "平充电量")
    @JsonProperty("flat_charge_quantity")
    private Double flatChargeQuantity;

    @ApiModelProperty(value = "谷充电量")
    @JsonProperty("vally_charge_quantity")
    private Double vallyChargeQuantity;

    @ApiModelProperty(value = "深谷充电量")
    @JsonProperty("deep_vally_charge_quantity")
    private Double deepVallyChargeQuantity;

    @ApiModelProperty(value = "总充电成本")
    @JsonProperty("total_charge_cost")
    private Double totalChargeCost;

    @ApiModelProperty(value = "尖充电量成本")
    @JsonProperty("tip_charge_cost")
    private Double tipChargeCost;

    @ApiModelProperty(value = "峰充电量成本")
    @JsonProperty("peak_charge_cost")
    private Double peakChargeCost;

    @ApiModelProperty(value = "平充电量成本")
    @JsonProperty("flat_charge_cost")
    private Double flatChargeCost;

    @ApiModelProperty(value = "谷充电量成本")
    @JsonProperty("vally_charge_cost")
    private Double vallyChargeCost;

    @ApiModelProperty(value = "深谷充电量成本")
    @JsonProperty("deep_vally_charge_cost")
    private Double deepVallyChargeCost;

    @ApiModelProperty(value = "总放电电量")
    @JsonProperty("total_discharge_quantity")
    private Double totalDischargeQuantity;

    @ApiModelProperty(value = "尖放电量")
    @JsonProperty("tip_discharge_quantity")
    private Double tipDischargeQuantity;

    @ApiModelProperty(value = "峰放电量")
    @JsonProperty("peak_discharge_quantity")
    private Double peakDischargeQuantity;

    @ApiModelProperty(value = "平放电量")
    @JsonProperty("flat_discharge_quantity")
    private Double flatDischargeQuantity;

    @ApiModelProperty(value = "谷放电量")
    @JsonProperty("vally_discharge_quantity")
    private Double vallyDischargeQuantity;

    @ApiModelProperty(value = "深谷放电量")
    @JsonProperty("deep_vally_discharge_quantity")
    private Double deepVallyDischargeQuantity;

    @ApiModelProperty(value = "总放电收益")
    @JsonProperty("total_discharge_benefit")
    private Double totalDischargeBenefit;

    @ApiModelProperty(value = "尖放电量收益")
    @JsonProperty("tip_discharge_benefit")
    private Double tipDischargeBenefit;

    @ApiModelProperty(value = "峰放电量收益")
    @JsonProperty("peak_discharge_benefit")
    private Double peakDischargeBenefit;

    @ApiModelProperty(value = "平放电量收益")
    @JsonProperty("flat_discharge_benefit")
    private Double flatDischargeBenefit;

    @ApiModelProperty(value = "谷放电量收益")
    @JsonProperty("vally_discharge_benefit")
    private Double vallyDischargeBenefit;

    @ApiModelProperty(value = "深谷放电量收益")
    @JsonProperty("deep_vally_discharge_benefit")
    private Double deepVallyDischargeBenefit;

    // 给对象值赋予0d，方便相加
    public ElectricProfitVO init() {
        this.totalBenefit = 0d;
        this.totalChargeQuantity = 0d;
        this.tipChargeQuantity = 0d;
        this.peakChargeQuantity = 0d;
        this.flatChargeQuantity = 0d;
        this.vallyChargeQuantity = 0d;
        this.deepVallyChargeQuantity = 0d;
        this.totalChargeCost = 0d;
        this.tipChargeCost = 0d;
        this.peakChargeCost = 0d;
        this.flatChargeCost = 0d;
        this.vallyChargeCost = 0d;
        this.deepVallyChargeCost = 0d;
        this.totalDischargeQuantity = 0d;
        this.tipDischargeQuantity = 0d;
        this.peakDischargeQuantity = 0d;
        this.flatDischargeQuantity = 0d;
        this.vallyDischargeQuantity = 0d;
        this.deepVallyDischargeQuantity = 0d;
        this.totalDischargeBenefit = 0d;
        this.tipDischargeBenefit = 0d;
        this.peakDischargeBenefit = 0d;
        this.flatDischargeBenefit = 0d;
        this.vallyDischargeBenefit = 0d;
        this.deepVallyDischargeBenefit = 0d;
        return this;
    }

    @ApiModelProperty(value = "最后更新时间")
    private Long lastUpdateTime;

    public ElectricProfitVO() {
        this.init();
    }
}
