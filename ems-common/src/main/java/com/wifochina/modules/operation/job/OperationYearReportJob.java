package com.wifochina.modules.operation.job;

import java.time.LocalDateTime;
import java.time.ZoneId;

import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.notice.service.NoticeReportService;

import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 * @since 2024-03-11 4:50 PM
 */
public class OperationYearReportJob extends AbstractOperationReportJob {

    @Override
    public Long getEnd(String timezoneCode) {
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of(timezoneCode));
        LocalDateTime endDataTime = LocalDateTime.of(localDateTime.getYear(), 1, 1, 0, 0, 0);
        return endDataTime.toInstant(MyTimeUtil.getZoneOffsetFromZoneCode(timezoneCode)).getEpochSecond();
    }

    @Override
    public void sendReport(String projectId, String timezoneCode) {
        Long start = getStart(timezoneCode);
        NoticeReportService noticeReportService = SpringUtil.getBean(NoticeReportService.class);
        noticeReportService.noticeSendYear(projectId, start);
    }

    @Override
    public Long getStart(String timezoneCode) {
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of(timezoneCode));
        LocalDateTime endDataTime = LocalDateTime.of(localDateTime.getYear(), 1, 1, 0, 0, 0);
        LocalDateTime startDataTime = endDataTime.minusYears(1);
        return startDataTime.toInstant(MyTimeUtil.getZoneOffsetFromZoneCode(timezoneCode)).getEpochSecond();
    }
}
