package com.wifochina.modules.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import org.hibernate.validator.constraints.Length;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_electric_price")
@ApiModel(value = "ElectricPriceEntity对象")
@Accessors(chain = true)
public class ElectricPriceEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电价id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "电价开始日期")
    private Long startDate;

    @ApiModelProperty(value = "电价结束日期")
    private Long endDate;

    @ApiModelProperty(value = "电价时段（谷1 平2 峰3 尖4 深谷5）")
    private Integer period;

    @ApiModelProperty(value = "电价配置类型(海外or国内)")
    private String type;

    @ApiModelProperty(value = "自定义电价时段名称")
    /** 需要要最长50个字符 */
    @Length(max = 50)
    private String customPeriodName;

    //    @ApiModelProperty(value = "分段电价")
    //    private Double price;
    //
    @ApiModelProperty(value = "卖出电价")
    private Double sellPrice;

    @ApiModelProperty(value = "买入电价")
    private Double buyPrice;

    @ApiModelProperty(value = "时段内部序号（预留）")
    private Integer periodOrder;

    @ApiModelProperty(value = "时段开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime startTime;

    @ApiModelProperty(value = "时段结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime endTime;

    @ApiModelProperty(value = "需量价格")
    private Double demandPrice;

    @ApiModelProperty(value = "光伏自用价格")
    private Double pvSelfPrice;

    @ApiModelProperty(value = "脱硫标杆价格")
    private Double pvDfPrice;

    @ApiModelProperty(value = "光伏国家补贴价格")
    private Double pvSubsidyPrice;

    @ApiModelProperty(value = "(国内)光伏全额上网价格(国外是用作上网电价)")
    private Double pvPrice;

    @ApiModelProperty(value = "是否至今，至今为1，不是为0")
    private Integer whetherCurrent;

    @ApiModelProperty(value = "父id")
    private Integer pid;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "分段时间、类型与价格")
    @TableField(exist = false)
    private List<ElectricPriceEntity> periodPriceList;

    @ApiModelProperty(value = "风电自用价格")
    private Double windSelfPrice;

    @ApiModelProperty(value = "风电脱硫标杆价格")
    private Double windDfPrice;

    @ApiModelProperty(value = "风电国家补贴价格")
    private Double windSubsidyPrice;

    @ApiModelProperty(value = "(国内)风电全额上网价格(国外是用作上网电价)")
    private Double windPrice;

    /** 2025-02-07 14:29:32 1.4.2 add */
    // ---------add start--------//
    @ApiModelProperty(value = "余热发电自用价格")
    private Double wasterSelfPrice;

    @ApiModelProperty(value = "余热发电脱硫标杆价格")
    private Double wasterDfPrice;

    @ApiModelProperty(value = "余热发电国家补贴价格")
    private Double wasterSubsidyPrice;

    @ApiModelProperty(value = "(国内)余热发电全额上网价格(国外是用作上网电价)")
    private Double wasterPrice;

    // ---------add end--------//

    /** 2024-04-18 14:49:12 add 点击模版id 用于表示这个电价配置 属于某个模版的 */
    @ApiModelProperty(value = "电价模版id")
    private String priceTemplateId;
}
