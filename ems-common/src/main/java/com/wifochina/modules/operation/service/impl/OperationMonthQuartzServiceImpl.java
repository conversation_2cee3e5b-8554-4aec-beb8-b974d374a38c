package com.wifochina.modules.operation.service.impl;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-03-13 2:42 PM
 */
@Component
public class OperationMonthQuartzServiceImpl extends AbstractOperationQuartzService {
    @Override
    public String getOperationTriggerName(String timezoneCode) {
        return String.format("operation-month-trigger:%s", timezoneCode);
    }

    @Override
    public String getOperationJobName(String timezoneCode) {
        return String.format("operation-month-job:%s", timezoneCode);
    }

    @Override
    public String getJobCrontab() {
        return "0 0 2 1 * ?";
    }
}
