<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.operation.mapper.ElectricPriceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wifochina.modules.operation.entity.ElectricPriceEntity">
        <id column="id" property="id"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="period" property="period"/>
        <result column="price" property="price"/>
        <result column="period_order" property="periodOrder"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="demand_price" property="demandPrice"/>
        <result column="pv_self_price" property="pvSelfPrice"/>
        <result column="pv_df_price" property="pvDfPrice"/>
        <result column="pv_subsidy_price" property="pvSubsidyPrice"/>
        <result column="pv_price" property="pvPrice"/>
        <result column="whether_current" property="whetherCurrent"/>
        <result column="pid" property="pid"/>
        <result column="project_id" property="projectId"/>
    </resultMap>

</mapper>
