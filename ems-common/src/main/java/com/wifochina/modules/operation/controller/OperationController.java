package com.wifochina.modules.operation.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.TimePointEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.DemandCalcModeEnum;
import com.wifochina.common.util.DemandControlEnum;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.electric.vo.ElectricPriceSystemData;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.ElectricProfitService;
import com.wifochina.modules.income.NewOperationProfitService;
import com.wifochina.modules.income.cache.memorycache.IIncomeMemoryCacheService;
import com.wifochina.modules.income.request.ProfitRequest;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.operation.request.RealTimePriceRequest;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@RequestMapping("/operation")
@RestController
@Api(tags = "10-运营收益")
@Slf4j
public class OperationController {

    @Resource private ElectricProfitService electricProfitService;

    @Resource private ElectricPriceService electricPriceService;

    @Resource private IIncomeMemoryCacheService incomeMemoryCacheService;

    @Resource private ProjectService projectService;

    @Resource private ProjectExtService projectExtService;

    @Resource private PriceAreaService priceAreaService;

    @Resource private NewOperationProfitService newOperationProfitService;

    @Resource private GroupService groupService;

    @Resource private LogService logService;

    @GetMapping("resetDemandControl")
    @ApiOperation("临时兼容1.4.0修复需量的DemandControl变化")
    @Deprecated
    public Result<Void> resetDemandControl() {
        // 经过 周涛说的 只要以前老的 分组下开了 需量控制开关 就算做 开启了需量收益, 也就是把新的字段DemandControl
        // 设置为enable_show_demand_income
        List<GroupEntity> list = groupService.list();
        for (GroupEntity group : list) {
            // TODO 这里执行完后上线后 可以删除这个代码 并且把 老的demandController 和 demandIncome 字段也删除
            Boolean demandController = group.getDemandController();
            if (demandController != null && demandController) {
                group.setDemandControl(DemandControlEnum.enable_show_demand_income.getName());
            } else {
                group.setDemandControl(DemandControlEnum.disable.getName());
            }
            groupService.updateById(group);
        }
        return Result.success();
    }

    @GetMapping("resetDemandCalModel")
    @ApiOperation("临时兼容1.4.0修复需量的CalModel变化")
    @Deprecated
    public Result<Void> resetDemandCalModel() {
        /**
         * // @ApiModelProperty(value = "需量计算模式:1固定时段15分钟;2固定时段30分钟;3一分钟滑窗") // 1.4.0改成了这样 只有2个 ,
         * 默认是 滑差 @ApiModelProperty(value = "需量计算模式:1滑差模式;2固定模式") private Integer demandCalcModel;
         */
        // 原先 demandCalcModel 1固定时段15分钟;2固定时段30分钟;3一分钟滑窗
        List<GroupEntity> list = groupService.list();
        for (GroupEntity group : list) {
            Integer demandCalcModel = group.getDemandCalcModel();
            Integer newDemandCalcModel = null;
            if (demandCalcModel != null) {
                if (demandCalcModel == 1) {
                    newDemandCalcModel = DemandCalcModeEnum.FIXED.getModel();
                    group.setDemandPeriod(15);
                }
                if (demandCalcModel == 2) {
                    newDemandCalcModel = DemandCalcModeEnum.FIXED.getModel();
                    group.setDemandPeriod(30);
                }
                if (demandCalcModel == 3) {
                    newDemandCalcModel = DemandCalcModeEnum.SLIDING.getModel();
                    // 以前滑窗只有 15分钟这个周期
                    group.setDemandPeriod(15);
                    // 以前也只有1分钟一个
                    group.setSlipTime(1);
                }
                group.setDemandCalcModel(newDemandCalcModel);
                groupService.updateById(group);
            }
        }
        return Result.success();
    }

    @GetMapping("reSetAreaTemp")
    @ApiOperation("临时电价区域老数据修复接口后期删除")
    @Deprecated
    public Result<Void> reSetAreaTemp() {
        List<ProjectExtEntity> projectExtEntities =
                projectExtService.getBaseMapper().selectList(new LambdaQueryWrapper<>());
        for (ProjectExtEntity projectExtEntity : projectExtEntities) {
            String area = projectExtEntity.getArea();
            PriceAreaEntity priceAreaEntity =
                    priceAreaService
                            .getBaseMapper()
                            .selectOne(
                                    new LambdaQueryWrapper<PriceAreaEntity>()
                                            .eq(PriceAreaEntity::getArea, area));
            if (priceAreaEntity != null) {
                String id = priceAreaEntity.getId();
                projectExtEntity.setArea(id);
                // 更新老数据
                projectExtService.getBaseMapper().updateById(projectExtEntity);
                log.info(
                        "change projectExt id:{} area from {} to {}",
                        projectExtEntity.getId(),
                        area,
                        id);
            } else {
                // 可能有部分是已经切过去的
                log.warn(
                        "cant find priceArea by projectExt id:{} area:{}",
                        projectExtEntity.getId(),
                        area);
            }
        }
        return Result.success();
    }

    /** 添加电价/时段配置 */
    @PostMapping("/savePrice")
    @ApiOperation("保存价格配置")
    @Log(module = "OPERATION", methods = "OPERATION_PRICE_SAVE", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/operation/savePrice')")
    public Result<Object> SaveOrUpdatePrice(
            @RequestBody List<ElectricPriceEntity> electricPriceList) {
        electricPriceService.savePrice(electricPriceList);
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        incomeMemoryCacheService.updateElectricProfitVO(
                WebUtils.projectId.get(),
                projectEntity.getProjectName(),
                projectEntity.getCreateTime(),
                projectEntity.getTimezone());
        incomeMemoryCacheService.updatePvVo(
                WebUtils.projectId.get(),
                projectEntity.getProjectName(),
                projectEntity.getCreateTime(),
                projectEntity.getTimezone());
        incomeMemoryCacheService.updateDemandVoAsync(projectEntity);
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("OPERATION")
        //                        .method("OPERATION_PRICE_SAVE")
        //                        .object(electricPriceList)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.OPERATION_USER_PRICES_TRACE_FORMAT,
        //                                        WebUtils.projectId.get()))
        //                        .build());
        return Result.success();
    }

    /** 查询电价配置 */
    @PostMapping("/getPrice")
    @ApiOperation("查询电价配置")
    @PreAuthorize("hasAuthority('/operation/getPrice')")
    public Result<List<ElectricPriceEntity>> getPrice() {
        List<ElectricPriceEntity> electricPriceList =
                electricPriceService.getPriceList(WebUtils.projectId.get());
        return Result.success(electricPriceList);
    }

    @PostMapping("/getRealTimePriceDiagram")
    @ApiOperation("查询电价配置")
    @PreAuthorize("hasAuthority('/operation/getRealTimePriceDiagram')")
    public Result<List<ElectricPriceSystemData.Data>> getRealTimePriceDiagram(
            @RequestParam("startTime") Long startTime,
            @RequestParam(value = "endTime", required = false) Long endTime) {
        RealTimePriceRequest request =
                new RealTimePriceRequest().setStartTime(startTime).setEndTime(endTime);
        // check 一下 只有当前项目是 实时电价 才能去查询
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        if (!projectEntity
                .getElectricPriceType()
                .equals(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name())) {
            throw new ServiceException(ErrorResultCode.JUST_ONLY_DYNAMIC_PRICE.value());
        }
        if (projectEntity.getPriceProxy()) {
            PriceAreaEntity priceAreaEntity = priceAreaService.queryProxyForDynamic(projectEntity);
            if (priceAreaEntity != null) {
                projectEntity.setElectricPriceArea(priceAreaEntity.getElectricPriceArea());
                projectEntity.setElectricPriceSpan(priceAreaEntity.getElectricPriceSpan());
                projectEntity.setCountry(priceAreaEntity.getCountryId());
            } else {
                return Result.success();
            }
        }
        List<ElectricPriceSystemData.Data> datas =
                electricPriceService.getRealTimePriceSystemData(
                        request.getStartTime(),
                        request.getEndTime(),
                        new ElectricPriceService.RealTimePriceSystemContext() {
                            @Override
                            public Integer countryId() {
                                return projectEntity.getCountry();
                            }

                            @Override
                            public String priceArea() {
                                return projectEntity.getElectricPriceArea();
                            }

                            @Override
                            public String span() {
                                return projectEntity.getElectricPriceSpan();
                            }
                        });
        return Result.success(datas);
    }

    /** 运营收益查询 */
    @PostMapping("/getProfit")
    @ApiOperation("运营收益查询")
    @PreAuthorize("hasAuthority('/operation/getProfit')")
    public Result<ProfitVO> getProfit(@RequestBody BenefitRequest benefitRequest)
            throws InterruptedException {
        // TODO refactor 这个是 计算 运营收益 但是是 所有的不仅仅只有 电 还有 pv wind等等
        //        ProfitVO profitVO =
        //                operationProfitService.getProfitVO(benefitRequest,
        // WebUtils.projectId.get(), true);
        // TODO refactor 这个是 计算 运营收益 但是是 所有的不仅仅只有 电 还有 pv wind等等
        ProfitVO profitVO =
                newOperationProfitService.getProfitVO(
                        benefitRequest, WebUtils.projectId.get(), true);
        return Result.success(profitVO);
    }

    /** 充放电收益查询 */
    @PostMapping("/getBatteryProfit")
    @ApiOperation("充放电收益查询")
    @PreAuthorize("hasAuthority('/operation/getBatteryProfit')")
    public Result<Map<String, Object>> getBatteryProfit(@RequestBody ProfitRequest profitRequest) {
        profitRequest.setProjectId(WebUtils.projectId.get());
        Map<String, Object> benefitMap =
                electricProfitService.getElectricProfitCollect(profitRequest);
        return Result.success(benefitMap);
    }

    /** 三页收益查询 */
    @PostMapping("/getTotalProfit")
    @ApiOperation("三页收益查询")
    @PreAuthorize("hasAuthority('/operation/getBatteryProfit')")
    public Result<Map<String, Object>> getAllTotalProfitVO() {
        Map<String, Object> benefitMap =
                newOperationProfitService.getTotalOperationProfit(
                        false,
                        WebUtils.projectId.get(),
                        TimePointEnum.TODAY,
                        TimePointEnum.YESTERDAY,
                        TimePointEnum.TOTAL);
        return Result.success(benefitMap);
    }

    @PostMapping("/recalculateProfit")
    @ApiOperation("重新累计收益")
    @PreAuthorize("hasAuthority('/operation/getBatteryProfit')")
    public Result<Object> recalculateProfit() {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        incomeMemoryCacheService.updateElectricProfitVO(
                projectEntity.getId(),
                projectEntity.getProjectName(),
                projectEntity.getCreateTime(),
                projectEntity.getTimezone());
        return Result.success();
    }

    @PostMapping("/recalculateDemand")
    @ApiOperation("重新累计需量")
    @PreAuthorize("hasAuthority('/operation/getBatteryProfit')")
    public Result<Object> recalculateDemand() {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        incomeMemoryCacheService.updateDemandVoAsync(projectEntity);
        return Result.success();
    }

    @PostMapping("/recalculatePv")
    @ApiOperation("重新累计光伏")
    @PreAuthorize("hasAuthority('/operation/getBatteryProfit')")
    public Result<Object> recalculatePv() {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        incomeMemoryCacheService.updatePvVo(
                projectEntity.getId(),
                projectEntity.getProjectName(),
                projectEntity.getCreateTime(),
                projectEntity.getTimezone());
        return Result.success();
    }

    @PostMapping("/recalculateWind")
    @ApiOperation("重新累计风电")
    @PreAuthorize("hasAuthority('/operation/getBatteryProfit')")
    public Result<Object> recalculateWind() {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        incomeMemoryCacheService.updateWindVo(
                projectEntity.getId(),
                projectEntity.getProjectName(),
                projectEntity.getCreateTime(),
                projectEntity.getTimezone());
        return Result.success();
    }
}
