package com.wifochina.modules.operation.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * Created on 2024/6/24 19:25.
 *
 * <AUTHOR>
 */
@Data
public class CacheDataReRunRequest {

    @ApiModelProperty(value = "range里的from和end 是字符串 管理端无法确定时区 后台做")
    private GenerateTimeRange range;

    @ApiModelProperty(value = "type 区分 PV/WIND/BATTERY/DEMAND 提供这几个类型")
    private String type;

    @ApiModelProperty(value = "支持管理端的多项目 lists")
    private List<String> projectIds;
}
