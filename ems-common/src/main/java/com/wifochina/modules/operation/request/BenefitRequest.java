package com.wifochina.modules.operation.request;

import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.project.entity.ProjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * profitRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @date 4/14/2022 11:17 AM
 */
@Data
@ApiModel(value = "按日期查询收益")
@Accessors(chain = true)
public class BenefitRequest {

    @ApiModelProperty(value = "开始时间 yyyy-MM-dd ", required = true)
    private Long startDate;

    @ApiModelProperty(value = "结束时间 yyyy-MM-dd ", required = true)
    private Long endDate;

    @ApiModelProperty(value = "UTC与客户端的时区差,东8区，传入-8", required = true)
    private Integer duration;


    public static BenefitRequest getTimeProjectInitToYestEnd(ProjectEntity project) {
        BenefitRequest benefitRequest = new BenefitRequest();
        // 项目初始开启时间的 当天的零点
        long initProjectZeroStartDate =
                MyTimeUtil.getOneDayZeroTime(
                        project.getCreateTime(), project.getTimezone());
        benefitRequest.setStartDate(initProjectZeroStartDate);
        long todayZero = MyTimeUtil.getTodayZeroTime(project.getTimezone());
        benefitRequest.setEndDate(todayZero - 1);
        return benefitRequest;
    }

    public static BenefitRequest getTimeYest(ProjectEntity project) {
        BenefitRequest benefitRequest = new BenefitRequest();
        long todayZero = MyTimeUtil.getTodayZeroTime(project.getTimezone());
        // 昨天的零点
        benefitRequest.setStartDate(todayZero - EmsConstants.ONE_DAY_SECOND);
        // 昨天的59时间
        benefitRequest.setEndDate(todayZero - 1);
        return benefitRequest;
    }

    public static BenefitRequest getTimeMonth(ProjectEntity project) {
        BenefitRequest benefitRequest = new BenefitRequest();
        long todayZero = MyTimeUtil.getTodayZeroTime(project.getTimezone());
        benefitRequest.setEndDate(todayZero);
        long initProjectZeroStartDate =
                MyTimeUtil.getOneDayZeroTime(
                        project.getCreateTime(), project.getTimezone());
        if (initProjectZeroStartDate < todayZero) {
            //表示如果 项目初始时间 > 大于 当月零点时间, 则 把开始时间设置为 项目初始时间, 因为当月零点还没有创建项目
            benefitRequest.setStartDate(Math.max(initProjectZeroStartDate, MyTimeUtil.getCurrentMonthZeroTime(project.getTimezone())));
        }
        return benefitRequest;
    }
}
