package com.wifochina.modules.operation.job;

import java.time.LocalDateTime;
import java.time.ZoneId;

import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.notice.service.NoticeReportService;

import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 * @since 2024-03-11 4:50 PM
 */
public class OperationMonthReportJob extends AbstractOperationReportJob {

    @Override
    public Long getEnd(String timezoneCode) {
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of(timezoneCode));
        LocalDateTime endDataTime =
            LocalDateTime.of(localDateTime.getYear(), localDateTime.getMonthValue(), 1, 0, 0, 0);
        return endDataTime.toInstant(MyTimeUtil.getZoneOffsetFromZoneCode(timezoneCode)).getEpochSecond();
    }

    @Override
    public void sendReport(String projectId, String timezoneCode) {
        NoticeReportService noticeReportService = SpringUtil.getBean(NoticeReportService.class);
        Long start = getStart(timezoneCode);
        noticeReportService.noticeSendMonth(projectId, start);
    }

    @Override
    public Long getStart(String timezoneCode) {
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of(timezoneCode));
        LocalDateTime endDataTime =
            LocalDateTime.of(localDateTime.getYear(), localDateTime.getMonthValue(), 1, 0, 0, 0);
        LocalDateTime startDataTime = endDataTime.minusMonths(1);
        return startDataTime.toInstant(MyTimeUtil.getZoneOffsetFromZoneCode(timezoneCode)).getEpochSecond();
    }

}
