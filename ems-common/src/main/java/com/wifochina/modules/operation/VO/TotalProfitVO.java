package com.wifochina.modules.operation.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * @date 4/14/2022 10:25 AM
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value = "运营收益统计结果")
@Data
public class TotalProfitVO {

    public TotalProfitVO() {
        init();
    }

    private void init() {
        electric_benefit = "0.0";
        total_benefit = "0.0";
        demand_control_power = "0.0";
        demand_control_benefit = "0.0";
        pv_discharge_quantity = "0.0";
        pv_discharge_benefit = "0.0";
        wind_discharge_quantity = "0.0";
        wind_discharge_benefit = "0.0";
        total_charge_quantity = "0.0";
        total_charge_cost = "0.0";
        total_discharge_quantity = "0.0";
        total_discharge_benefit = "0.0";
    }

    @ApiModelProperty(value = "储能充放电总利润")
    private String electric_benefit;

    @ApiModelProperty(value = "总利润")
    private String total_benefit;

    @ApiModelProperty(value = "降低需量功率")
    private String demand_control_power;

    @ApiModelProperty(value = "需量控制收益")
    private String demand_control_benefit;

    @ApiModelProperty(value = "PV总发电电量")
    private String pv_discharge_quantity;

    @ApiModelProperty(value = "PV总发电收益")
    private String pv_discharge_benefit;

    @ApiModelProperty(value = "风电总发电电量")
    private String wind_discharge_quantity;

    @ApiModelProperty(value = "风电总发电收益")
    private String wind_discharge_benefit;

    @ApiModelProperty(value = "总充电电量KWH")
    private String total_charge_quantity;

    @ApiModelProperty(value = "总充电成本")
    private String total_charge_cost;

    @ApiModelProperty(value = "总放电电量")
    private String total_discharge_quantity;

    @ApiModelProperty(value = "总放电收益")
    private String total_discharge_benefit;
}
