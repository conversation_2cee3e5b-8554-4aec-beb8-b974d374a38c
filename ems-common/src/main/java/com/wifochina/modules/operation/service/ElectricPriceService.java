package com.wifochina.modules.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.common.util.RestElectricPriceSystemUtils;
import com.wifochina.modules.electric.vo.ElectricPriceSystemData;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface ElectricPriceService extends IService<ElectricPriceEntity> {

    void checkElectricPriceType(List<ElectricPriceEntity> electricPriceList);

    /**
     * 保存电价配置
     *
     * @param electricPriceList : 电价配置列表
     */
    void savePrice(List<ElectricPriceEntity> electricPriceList);

    /**
     * 根据时间点 查询到 匹配这个时间点的 电价配置实体 这个内部是有适配 国定电价 和 实时电价 以及国内的 尖峰平谷的
     *
     * @param projectId : 项目id
     * @param timePoint : 时间点
     * @return : 电价配置
     */
    ElectricPriceEntity getElectricPriceMatch(String projectId, long timePoint);

    /**
     * 根据项目查询 电价配置列表
     *
     * @param projectId : 项目id
     * @return : 项目对应的电价配置 List
     */
    List<ElectricPriceEntity> getPriceList(String projectId);

    List<ElectricPriceSystemData.Data> getElectricPriceSystemDataUsePriceProxy(
            long start, ProjectEntity projectEntity);

    /**
     * 包装了一层 查询实时电价系统的数据 转换成ElectricPriceEntity
     *
     * @param timePoint : 时间点 开始时间
     * @param projectEntity : 项目对象
     * @return : List<ElectricPriceEntity>
     */
    List<ElectricPriceEntity> getPriceFromElectricSystem(
            long timePoint, ProjectEntity projectEntity);

    RestElectricPriceSystemUtils.RegionsAndIntervalData getRegionsAndIntervalData(
            Integer countryId);

    /**
     * 查询 实时电价系统的 接口
     *
     * @param timePoint : 时间点 开始时间
     * @param context : context
     * @return : 实时电价系统返回的 数据类型
     */
    List<ElectricPriceSystemData.Data> getRealTimePriceSystemData(
            Long startTime, Long endTime, RealTimePriceSystemContext context);

    /** 供 实时电价接口 查询的context接口 */
    interface RealTimePriceSystemContext {
        /**
         * 国家id
         *
         * @return : 国家id
         */
        Integer countryId();

        /**
         * 电价的区域 ( 对应国家的区域)
         *
         * @return : 区域标识
         */
        String priceArea();

        /**
         * 时间段
         *
         * @see com.wifochina.common.constants.ElectricPriceSpanEnum
         * @return : MINUTE_60 or MINUTE_30 or MINUTE_15
         */
        String span();
    }
}
