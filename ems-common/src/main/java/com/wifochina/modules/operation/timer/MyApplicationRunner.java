package com.wifochina.modules.operation.timer;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.config.ElectricPriceSystemConfig;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.influx.FluxAdapter;
import com.wifochina.common.preparerunner.AbstractPrepareRunner;
import com.wifochina.common.util.*;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.common.search.DefaultDifferenceQueryEngine;
import com.wifochina.modules.common.search.DefaultRateQueryEngine;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.common.search.PeriodUtils;
import com.wifochina.modules.country.entity.CountryEntity;
import com.wifochina.modules.country.service.CountryService;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.timer.TaskOperationTimer;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-10-09 9:51 PM
 */
@Slf4j
@Component
@Order(1)
public class MyApplicationRunner implements ApplicationRunner {

    @Resource private TaskOperationTimer taskOperationTimer;
    @Resource private ThreadPoolTaskExecutor asyncServiceExecutor;
    @Resource private ElectricPriceSystemConfig electricPriceSystemConfig;
    @Resource private RestTemplate restTemplate;
    @Resource private CacheUtils cacheUtils;
    @Resource private CountryService countryService;
    @Resource private Environment environment;
    @Resource private GroupService groupService;
    @Resource private StrategyService strategyService;
    @Resource private DemandService demandService;
    @Resource private InfluxClientService influxClientService;
    @Resource private ProjectService projectService;

    @Resource private DefaultRateQueryEngine rateQueryEngine;
    @Resource private DefaultDifferenceQueryEngine differenceQueryEngine;

    @Resource private ProjectExtService projectExtService;
    @Resource private PriceAreaService priceAreaService;

    @Resource List<AbstractPrepareRunner> prepareRunners;

    @Override
    public void run(ApplicationArguments args) {
        // 设置一些 config
        initSetConfig();
        // 全量补全 老的需量缓存数据
        // taskOperationTimer.cacheDemandMonth();
        // 初始化一些缓存数据 he 跑一些初始任务
        //        if (!isDevProfileActive()) {
        //            initCacheExecute();
        //        }

        initCacheExecute();
    }

    private void initCacheExecute() {
        // TODO 这里要加一个 控制变量 控制只执行一次
        //    tempExecuteCode();
        cacheCountry();
        asyncServiceExecutor.submit(() -> taskOperationTimer.cacheTodayProfit());
        asyncServiceExecutor.submit(() -> taskOperationTimer.cacheOneWeekProfit());
        asyncServiceExecutor.submit(
                () -> prepareRunners.forEach(AbstractPrepareRunner::prepareRun));
        taskOperationTimer.updateHistory();
        taskOperationTimer.updateDemandVo();
        //        taskOperationTimer.cachePvWindProfit();
        // 这个是把老的需量缓存没有的 给添加上
        //        asyncServiceExecutor.submit(() -> taskOperationTimer.cacheDemandMonth());
        insertCapacity();
    }

    @Deprecated
    private void tempExecuteCode() {
        log.info("temp 兼容code 把area 区域 的 字段处理一下 start...");
        List<ProjectExtEntity> projectExtEntities =
                projectExtService.getBaseMapper().selectList(new LambdaQueryWrapper<>());
        for (ProjectExtEntity projectExtEntity : projectExtEntities) {
            String area = projectExtEntity.getArea();
            PriceAreaEntity priceAreaEntity =
                    priceAreaService
                            .getBaseMapper()
                            .selectOne(
                                    new LambdaQueryWrapper<PriceAreaEntity>()
                                            .eq(PriceAreaEntity::getArea, area));
            if (priceAreaEntity != null) {
                String id = priceAreaEntity.getId();
                projectExtEntity.setArea(id);
                // 更新老数据
                projectExtService.getBaseMapper().updateById(projectExtEntity);
                log.info(
                        "change projectExt id:{} area from {} to {}",
                        projectExtEntity.getId(),
                        area,
                        id);
            } else {
                // 可能有部分是已经切过去的
                log.warn(
                        "cant find priceArea by projectExt id:{} area:{}",
                        projectExtEntity.getId(),
                        area);
            }
        }
        log.info("temp 兼容code 把area 区域 的 字段处理一下 end...");

        log.info("temp 兼容code 把需量的 demandCalcModel 字段处理一下 start...");
        /**
         * // @ApiModelProperty(value = "需量计算模式:1固定时段15分钟;2固定时段30分钟;3一分钟滑窗") // 1.4.0改成了这样 只有2个 ,
         * 默认是 滑差 @ApiModelProperty(value = "需量计算模式:1滑差模式;2固定模式") private Integer demandCalcModel;
         */
        // 原先 demandCalcModel 1固定时段15分钟;2固定时段30分钟;3一分钟滑窗
        List<GroupEntity> list = groupService.list();
        for (GroupEntity group : list) {
            Integer demandCalcModel = group.getDemandCalcModel();
            Integer newDemandCalcModel = null;
            if (demandCalcModel != null) {
                if (demandCalcModel == 1) {
                    newDemandCalcModel = DemandCalcModeEnum.FIXED.getModel();
                    group.setDemandPeriod(15);
                }
                if (demandCalcModel == 2) {
                    newDemandCalcModel = DemandCalcModeEnum.FIXED.getModel();
                    group.setDemandPeriod(30);
                }
                if (demandCalcModel == 3) {
                    newDemandCalcModel = DemandCalcModeEnum.SLIDING.getModel();
                    // 以前滑窗只有 15分钟这个周期
                    group.setDemandPeriod(15);
                    // 以前也只有1分钟一个
                    group.setSlipTime(1);
                }
                if (demandCalcModel == 4) {
                    newDemandCalcModel = DemandCalcModeEnum.SLIDING.getModel();
                    group.setDemandPeriod(30);
                    // 以前也只有1分钟一个
                    group.setSlipTime(1);
                }
                group.setDemandCalcModel(newDemandCalcModel);
                groupService.updateById(group);
            }
        }
        log.info("temp 兼容code 把需量的 demandCalcModel 字段处理一下 end...");
    }

    private boolean isDevProfileActive() {
        boolean isLocal = isLocalHost();
        if (isLocal) {
            return true;
        }
        return false;
        //        return String.join(",", (environment.getActiveProfiles())).contains("dev")
        //                || String.join(",", (environment.getActiveProfiles())).contains("test");
    }

    private boolean isLocalHost() {
        try {
            // 获取当前主机地址
            String hostAddress = InetAddress.getLocalHost().getHostAddress();

            // 检查是否是 127.0.0.1 或 0.0.0.0
            return "127.0.0.1".equals(hostAddress) || "0.0.0.0".equals(hostAddress);
        } catch (UnknownHostException e) {
            // 如果获取主机地址失败，默认返回 false
            return false;
        }
    }

    private void initSetConfig() {
        String url = environment.getProperty("ems.influx.url");
        if (StringUtil.isEmpty(url)) {
            // 已经不在适用了 不会再有lindorm了
            // ApplicationHolder.setTimeSeries(EmsConstants.LINDORM);
            throw new ServiceException("没有配置influx url");
        } else {
            ApplicationHolder.setTimeSeries(EmsConstants.INFLUX);
        }
        // 标记一下 是否是 云版本的
        boolean cloudFlag =
                Boolean.TRUE.equals(environment.getProperty("ems.cloud", Boolean.class));
        ApplicationHolder.setDeployType(cloudFlag);
        RestElectricPriceSystemUtils.setRestTemplate(restTemplate);
        RestElectricPriceSystemUtils.setConfig(electricPriceSystemConfig);
        FluxAdapter.setInfluxClientService(influxClientService, projectService);
        EquipmentTimeSeriesUtils.setRateQueryEngine(rateQueryEngine);
        EquipmentTimeSeriesUtils.setDifferenceQueryEngine(differenceQueryEngine);
        PeriodUtils.influxClientService = influxClientService;
    }

    private void cacheCountry() {
        // 目前系统只需要 缓存一下 Chain的 后续可以扩展
        // 看到有些 数据库有重复的 这里用list
        List<CountryEntity> chinaList =
                countryService.list(
                        new LambdaQueryWrapper<CountryEntity>()
                                .eq(CountryEntity::getEnUs, "China"));
        if (CollectionUtil.isNotEmpty(chinaList)) {
            CountryEntity country = chinaList.get(0);
            cacheUtils.setCountryEntityMap(country.getId(), country);
        } else {
            throw new ServiceException(ErrorResultCode.COUNTRY_CHINA_NOT_FIND.value());
        }
    }

    private void insertCapacity() {
        List<GroupEntity> groupEntities =
                groupService.lambdaQuery().eq(GroupEntity::getCapacityController, true).list();
        groupEntities.forEach(
                g -> {
                    try {
                        StrategyEntity strategy =
                                strategyService.getOuterStrategyByGroupId(g.getId());
                        if (strategy.getControlPower() != null) {
                            demandService.saveCapacityDemandToDb(
                                    strategy.getControlPower(), g.getProjectId(), g.getId());
                            log.info(
                                    "cache capacity for {} is {}",
                                    g.getId(),
                                    strategy.getControlPower());
                        }
                    } catch (Exception e) {
                        log.error("cache capacity error for {}", g.getId());
                    }
                });
    }
}
