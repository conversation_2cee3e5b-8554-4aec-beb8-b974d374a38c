package com.wifochina.modules.operation.timer;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupDeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupDeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.VO.ElectricVo;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectDailyService;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Created on 2024/1/23 17:24.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskElectricTimer {

    private final ProjectService projectService;

    private final AmmeterService ammeterService;

    private final GroupService groupService;

    private final DeviceService deviceService;
    private final ProjectDailyService projectDailyService;
    private final GroupDeviceService groupDeviceService;
    public static Map<String, ElectricVo> monthElectricMap = new ConcurrentHashMap<>();
    public static Map<String, Long> monthElectricJobExecuteMap = new ConcurrentHashMap<>();

    private final ThreadPoolTaskExecutor asyncServiceExecutor;

    @EventListener(ApplicationReadyEvent.class)
    void init() {
        // 项目后的时候就立即执行一次
        asyncServiceExecutor.submit(this::cacheElectric);
    }

    /** TODO 暂时先不用 单独缓存电量的 */
    @Scheduled(cron = "0 0 */2 * * ?")
    @Async("asyncServiceExecutor")
    public void cacheElectric() {
        log.debug(
                "{} -->正在执行",
                Thread.currentThread().getName() + " TaskOperationTimer # cacheTodayProfit");
        List<ProjectEntity> list =
                projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false));
        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                continue;
            }
            WebUtils.projectId.set(projectEntity.getId());
            long monthStart = MyTimeUtil.getCurrentMonthZeroTime(projectEntity.getTimezone());
            long monthEnd = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            if (monthElectricJobExecuteMap.get(projectEntity.getId()) != null) {
                if (monthElectricJobExecuteMap.get(projectEntity.getId()) == monthEnd) {
                    // 表示已经跑过了任务
                    return;
                }
            }
            GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectEntity.getId());
            if (systemGroupEntity == null || StringUtil.isEmpty(systemGroupEntity.getId())) {
                // 看到日志里有systemGroupEntity null
                log.error(
                        "systemGroupEntity is null are you kidding me? , projectId :{}",
                        WebUtils.projectId.get());
                continue;
            }
            List<DeviceEntity> unRealDeviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getUnreal, true)
                            .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                            .list();
            List<String> unRealDeviceIds =
                    unRealDeviceEntities.stream()
                            .map(DeviceEntity::getId)
                            .collect(Collectors.toList());
            List<GroupDeviceEntity> groupDeviceEntities =
                    groupDeviceService.list(
                            Wrappers.lambdaQuery(GroupDeviceEntity.class)
                                    .eq(GroupDeviceEntity::getGroupId, systemGroupEntity.getId()));
            List<String> systemDeviceIds =
                    groupDeviceEntities.stream()
                            .map(GroupDeviceEntity::getDeviceId)
                            .collect(Collectors.toList());
            List<String> sysDeviceIds =
                    systemDeviceIds.stream()
                            .filter(item -> !unRealDeviceIds.contains(item))
                            .collect(Collectors.toList());

            double emsMonthOutSum = 0;
            double emsMonthInSum = 0;
            if (monthStart == monthEnd) {
                ElectricVo electricVo = new ElectricVo();
                electricVo.setEmsOutSum(emsMonthOutSum);
                electricVo.setEmsInSum(emsMonthInSum);
                monthElectricMap.put(projectEntity.getId(), electricVo);
                monthElectricJobExecuteMap.put(projectEntity.getId(), monthEnd);
            }
            List<String> incomeDeviceIds;
            List<String> incomeMeterIds;
            if (systemGroupEntity.getShowType()) {
                List<DeviceEntity> incomeDeviceEntities =
                        deviceService
                                .lambdaQuery()
                                .eq(DeviceEntity::getIncome, true)
                                .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                                .list();
                if (!incomeDeviceEntities.isEmpty()) {
                    incomeDeviceIds =
                            incomeDeviceEntities.stream()
                                    .map(DeviceEntity::getId)
                                    .collect(Collectors.toList());

                    List<String> finalIncomeDeviceIds = incomeDeviceIds;
                    emsMonthOutSum =
                            projectDailyService.getDailyPower(
                                    monthStart,
                                    monthEnd,
                                    "ems_history_output_energy",
                                    null,
                                    finalIncomeDeviceIds);
                    emsMonthInSum =
                            projectDailyService.getDailyPower(
                                    monthStart,
                                    monthEnd,
                                    "ems_history_input_energy",
                                    null,
                                    finalIncomeDeviceIds);
                }
                List<AmmeterEntity> incomeAmmeterEntities =
                        ammeterService
                                .lambdaQuery()
                                .eq(AmmeterEntity::getIncome, true)
                                .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                                .list();
                if (!incomeAmmeterEntities.isEmpty()) {
                    incomeMeterIds =
                            incomeAmmeterEntities.stream()
                                    .map(AmmeterEntity::getId)
                                    .collect(Collectors.toList());
                    List<String> finalIncomeMeterIds = incomeMeterIds;
                    emsMonthOutSum +=
                            projectDailyService.getDailyPower(
                                    monthStart,
                                    monthEnd,
                                    "ac_history_positive_power_in_kwh",
                                    "meter",
                                    finalIncomeMeterIds);
                    emsMonthInSum +=
                            projectDailyService.getDailyPower(
                                    monthStart,
                                    monthEnd,
                                    "ac_history_positive_power_in_kwh",
                                    "meter",
                                    finalIncomeMeterIds);
                }
            } else {
                emsMonthOutSum =
                        projectDailyService.getDailyPower(
                                monthStart,
                                monthEnd,
                                "ems_history_output_energy",
                                null,
                                sysDeviceIds);
                emsMonthInSum =
                        projectDailyService.getDailyPower(
                                monthStart,
                                monthEnd,
                                "ems_history_input_energy",
                                null,
                                sysDeviceIds);
            }
            ElectricVo electricVo = new ElectricVo();
            electricVo.setEmsOutSum(emsMonthOutSum);
            electricVo.setEmsInSum(emsMonthInSum);
            monthElectricMap.put(projectEntity.getId(), electricVo);
            monthElectricJobExecuteMap.put(projectEntity.getId(), monthEnd);
        }
        log.info(
                "{} -->执行完成",
                Thread.currentThread().getName() + " TaskElectricTimer # cacheElectric");
    }
}
