package com.wifochina.modules.operation.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-01-30 11:36 AM
 */
@Data
public class GroupDemandProfit {
    @ApiModelProperty(value = "降低需量功率")
    private double demandControlPower;

    @ApiModelProperty(value = "需量控制收益")
    private double demandControlBenefit;

    @ApiModelProperty(value = "降低需量单价")
    private double demandControlPrice;

    @ApiModelProperty(value = "分组名车给你")
    private String groupName;

    public final String getData(int index) {

        if (index == 2) {
            return this.getGroupName()+"\n"+"降低需量功率(KW)";
        }
        if (index == 3) {
            return String.valueOf(this.getDemandControlPower());
        }
        if (index == 4) {
            return "需量电价(元/kw)";
        }
        if (index == 5) {
            return String.valueOf(this.getDemandControlPrice());
        }
        if (index == 6) {
            return "结算金额(元)";
        }
        if (index == 7) {
            return String.valueOf(this.getDemandControlBenefit());
        }
        return null;
    }
}
