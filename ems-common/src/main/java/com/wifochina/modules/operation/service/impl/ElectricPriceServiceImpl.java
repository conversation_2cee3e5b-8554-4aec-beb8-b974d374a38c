package com.wifochina.modules.operation.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ElectricPriceSpanEnum;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.RestElectricPriceSystemUtils;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.country.entity.CountryEntity;
import com.wifochina.modules.country.service.CountryService;
import com.wifochina.modules.electric.vo.ElectricPriceSystemData;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.mapper.ElectricPriceMapper;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.project.service.ProjectService;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@Service
@Slf4j
public class ElectricPriceServiceImpl extends ServiceImpl<ElectricPriceMapper, ElectricPriceEntity>
        implements ElectricPriceService {

    @Autowired private ProjectService projectService;

    @Autowired private ProjectExtService projectExtService;

    @Autowired private PriceAreaService priceAreaService;

    @Autowired private RestTemplate restTemplate;

    @Autowired private CountryService countryService;

    /**
     * 校验一下 电价配置 根据 电价的类型 主要是区分不同类型的电价 需要的 参数的校验
     *
     * @param electricPriceList : 电价配置 list
     */
    @Override
    public void checkElectricPriceType(List<ElectricPriceEntity> electricPriceList) {
        if (electricPriceList.isEmpty()) {
            return;
        }
        // 尖峰平谷 需要 参数 每个sub list 的 period 字段 , 因为尖峰平谷是根据 period 的值来代表每个段的
        if (ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD
                .name()
                .equals(electricPriceList.get(0).getType())) {
            // 如果是以前的 尖峰平谷需要提供 period
            electricPriceList.forEach(
                    e -> {
                        if (e.getPeriodPriceList().stream()
                                .anyMatch(ep -> ep.getPeriod() == null)) {
                            // 如果是 period 模式尖峰平谷方式 则 每一个子list里面必须要有 period 参数
                            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
                        }
                    });
        } else if (ElectricPriceTypeEnum.FIXED_PRICE_PERIOD
                .name()
                .equals(electricPriceList.get(0).getType())) {
            // 固定电价 需要校验 提供的段的名称
            electricPriceList.forEach(
                    e -> {
                        if (e.getPeriodPriceList().stream()
                                .anyMatch(sue -> StringUtil.isEmpty(sue.getCustomPeriodName()))) {
                            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
                        }
                    });
        } else if (ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD
                .name()
                .equals(electricPriceList.get(0).getType())) {
            // 暂时想不到 实时电价 需要校验什么
            log.info("");
        } else {
            // 未提供类型 是错误的
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePrice(List<ElectricPriceEntity> electricPriceList) {
        // 1.3.7 新增的 校验一下 这个电价是提供给国内还是海外的 对应需要校验参数不同
        checkElectricPriceType(electricPriceList);
        // 获取当前登录用户的projectId
        String projectId = WebUtils.projectId.get();
        // 删除旧的价格配置
        getBaseMapper()
                .delete(
                        Wrappers.lambdaQuery(ElectricPriceEntity.class)
                                .isNull(ElectricPriceEntity::getPriceTemplateId)
                                .eq(ElectricPriceEntity::getProjectId, projectId));
        if (!electricPriceList.isEmpty()) {
            // 如果日期不符合规则，直接抛出异常
            checkDate(electricPriceList);

            // 先检查时间是否排满，再进行间隔配置的插入
            for (ElectricPriceEntity electricityPrice : electricPriceList) {
                electricityPrice.setProjectId(WebUtils.projectId.get());
                List<ElectricPriceEntity> list = electricityPrice.getPeriodPriceList();
                // 将时间段按照开始时间进行排序
                list =
                        list.stream()
                                .sorted(
                                        Comparator.comparing(ElectricPriceEntity::getStartTime)
                                                .thenComparing(ElectricPriceEntity::getEndTime))
                                .collect(Collectors.toList());
                if (!list.isEmpty()) {
                    // 取第一个节点作为前一个节点
                    ElectricPriceEntity electricityPrice1 = list.get(0);
                    for (int i = 1; i < list.size(); i++) {
                        // 当前节点
                        ElectricPriceEntity electricityPrice2 = list.get(i);
                        ServiceAssert.isTrue(
                                electricityPrice1
                                                .getEndTime()
                                                .compareTo(electricityPrice2.getStartTime())
                                        <= 0,
                                ErrorResultCode.TIME_OVERLAPPED.value());
                        ServiceAssert.isTrue(
                                electricityPrice1
                                                .getEndTime()
                                                .compareTo(electricityPrice2.getStartTime())
                                        >= 0,
                                ErrorResultCode.TIME_IS_NOT_FULL.value());
                        if (list.size() - 1 == i) {
                            //// 最后一个如果前后相同，证明传入的都是00:00:00，那肯定没有排满24小时
                            ServiceAssert.isTrue(
                                    electricityPrice2.getEndTime() == list.get(0).getStartTime(),
                                    ErrorResultCode.TIME_IS_NOT_FULL.value());
                            break;
                        }
                        electricityPrice1 = electricityPrice2;
                    }
                }

                boolean success = this.save(electricityPrice);
                if (!success) {
                    throw new ServiceException(ErrorResultCode.DISTRIBUTION_FAILED.value());
                }
                for (ElectricPriceEntity timePrice : electricityPrice.getPeriodPriceList()) {
                    if (timePrice.getStartTime() == timePrice.getEndTime()) {
                        continue;
                    }
                    timePrice.setId(null);
                    timePrice.setPid(electricityPrice.getId());
                    timePrice.setProjectId(projectId);
                    timePrice.setStartDate(electricityPrice.getStartDate());
                    timePrice.setEndDate(electricityPrice.getEndDate());
                    if (!this.save(timePrice)) {
                        throw new ServiceException(ErrorResultCode.DISTRIBUTION_FAILED.value());
                    }
                }
            }
        }
    }

    /**
     * 检查日期是否排满以及是否有重叠
     *
     * @param electricPriceList 电价配置列表
     */
    public void checkDate(List<ElectricPriceEntity> electricPriceList) {
        // 得到第一个电价配置
        ElectricPriceEntity electricPriceEntity = electricPriceList.get(0);
        if (electricPriceEntity.getEndDate() == null
                && electricPriceEntity.getStartDate() == null) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value());
        }
        // 如果不是至今的话。判断下结束日期是否大于开始日期
        if (electricPriceEntity.getWhetherCurrent() == 0) {
            ServiceAssert.isTrue(
                    electricPriceEntity.getEndDate().compareTo(electricPriceEntity.getStartDate())
                            >= 0,
                    ErrorResultCode.START_GE_END_TIME.value());
        }
        // 比较剩下的时间配置，防止有未覆盖的时间
        for (int i = 1; i < electricPriceList.size(); i++) {
            // 当前的储能价格
            ElectricPriceEntity electricityPrice = electricPriceList.get(i);
            if (electricityPrice.getEndDate() == null && electricityPrice.getStartDate() == null) {
                throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value());
            }

            // 如果是至今，跳过
            if (electricityPrice.getWhetherCurrent() == 1) {
                ServiceAssert.isTrue(
                        electricPriceEntity.getEndDate().compareTo(electricityPrice.getStartDate())
                                <= 0,
                        ErrorResultCode.DATE_OVERLAPPED.value());
                continue;
            }
            // 结束时间>开始时间 报错
            ServiceAssert.isTrue(
                    electricityPrice.getEndDate().compareTo(electricityPrice.getStartDate()) >= 0,
                    ErrorResultCode.START_GE_END_TIME.value());

            // 上一个节点的结束日期+1天，应该等于当前节点的开始日期，否则就是漏了或者重叠了 to
            // to1 如果上一个结束时间+1后 比当前的大，证明有重叠
            ServiceAssert.isTrue(
                    (electricityPrice.getStartDate() - electricPriceEntity.getEndDate())
                            <= MyTimeUtil.ONE_DAY_SECONDS,
                    ErrorResultCode.DATE_IS_NOT_FULL.value());
            // to2 如果上一个结束时间+1后 比当前的小，证明有漏
            ServiceAssert.isTrue(
                    (electricityPrice.getStartDate() - electricPriceEntity.getEndDate())
                            >= MyTimeUtil.ONE_DAY_SECONDS,
                    ErrorResultCode.DATE_OVERLAPPED.value());
            // to3
            // 不重复且不漏，继续下一个
            electricPriceEntity = electricityPrice;
        }
    }

    @Override
    public ElectricPriceEntity getElectricPriceMatch(String projectId, long timePoint) {
        ElectricPriceEntity matchPrice = null;
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 查询该项目的 全部的 电价列表
        List<ElectricPriceEntity> list = getPriceList(projectId);
        for (ElectricPriceEntity tempElectricPriceEntity : list) {
            if (tempElectricPriceEntity.getEndDate() == null
                    || timePoint <= tempElectricPriceEntity.getEndDate()) {
                matchPrice = tempElectricPriceEntity;
                break;
            }
        }
        // 如果尖峰平谷or 固定电价 都走以前的这种查询
        if (ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD
                        .name()
                        .equals(projectEntity.getElectricPriceType())
                || ElectricPriceTypeEnum.FIXED_PRICE_PERIOD
                        .name()
                        .equals(projectEntity.getElectricPriceType())) {
            return matchPrice;
        } else if (ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD
                .name()
                .equals(projectEntity.getElectricPriceType())) {
            // 正常需要有这个配置 只是这个配置里 没有时段的配置 但是有 需量 光伏和风电的 电价配置
            if (matchPrice == null) {
                // throw new ServiceException(ErrorResultCode.ELECTRIC_NOT_CONFIG.value());
                return matchPrice;
            }
            // 从电价系统 实时获取 这个list不知道会不会可能是没查询到 我这边给了一个空列表
            List<ElectricPriceEntity> periodList =
                    this.getPriceFromElectricSystem(timePoint, projectEntity);
            matchPrice
                    // new ElectricPriceEntity()
                    // .setStartDate(timePoint)
                    // TODO 这个待定是 -1 还是不-1
                    // .setEndDate(timePoint + EmsConstants.ONE_DAY_SECOND - 1)
                    .setType(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.getValue())
                    .setProjectId(projectId)
                    .setPeriodPriceList(periodList);
        }
        return matchPrice;
    }

    @Nullable
    @Override
    public List<ElectricPriceEntity> getPriceFromElectricSystem(
            long timePoint, ProjectEntity projectEntity) {
        List<ElectricPriceEntity> periodList = new ArrayList<>();
        // 下面就是去查询实时电价 后 把这些实时电价的时段 设置到 matchPrice里面
        // 1.3.7 add 实时电价 直接去电价系统查询点击信息
        try {
            // endTimePoint 为null 表示查询一天的 以timePoint + 一天时间戳
            List<ElectricPriceSystemData.Data> datas =
                    this.getRealTimePriceSystemData(
                            timePoint,
                            null,
                            new RealTimePriceSystemContext() {
                                @Override
                                public Integer countryId() {
                                    return projectEntity.getCountry();
                                }

                                @Override
                                public String priceArea() {
                                    return projectEntity.getElectricPriceArea();
                                }

                                @Override
                                public String span() {
                                    return projectEntity.getElectricPriceSpan();
                                }
                            });
            // 1.3.7 现在想办法吧这个 data 转换成 electricEntity
            // 处理时段的
            if (datas != null && !datas.isEmpty()) {
                // 把这个ElectricPriceSystemData.Data转换成 ElectricPriceEntity
                periodList =
                        datas.stream()
                                .sorted(
                                        Comparator.comparing(
                                                ElectricPriceSystemData.Data::getStartTimeUnix))
                                .map(
                                        d -> {
                                            // 时间段的开始时间
                                            int startTimeUnix = d.getStartTimeUnix();
                                            // 开始时间+ span 对应的 秒数 即可
                                            // TODO 这个待定是 -1 还是不-1
                                            int endTimeUnix =
                                                    startTimeUnix
                                                            + ElectricPriceSpanEnum.getValue(
                                                                    projectEntity
                                                                            .getElectricPriceSpan());
                                            return new ElectricPriceEntity()
                                                    .setStartTime(
                                                            MyTimeUtil.getLocalTime(
                                                                    projectEntity.getTimezone(),
                                                                    startTimeUnix))
                                                    .setEndTime(
                                                            MyTimeUtil.getLocalTime(
                                                                    projectEntity.getTimezone(),
                                                                    endTimeUnix))
                                                    // 这里把电价设置为 卖出和买入一样
                                                    // /1000 是 mwh 的 , 我们系统都是按照 kwh ,
                                                    // 已经在getRealTimePriceSystemData 这个方法里面/1000了
                                                    .setSellPrice(d.getAverage())
                                                    .setBuyPrice(d.getAverage())
                                                    .setProjectId(projectEntity.getId());
                                        })
                                .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("getElectricPriceMatch search dynamic price error :{}", e.getMessage());
        }
        return periodList;
    }

    @Override
    public RestElectricPriceSystemUtils.RegionsAndIntervalData getRegionsAndIntervalData(
            Integer countryId) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期
        String preDate = MyTimeUtil.getPreZeroTime().format(formatter);
        return RestElectricPriceSystemUtils.getRegionAndInterval(
                preDate,
                () -> {
                    // 有时间再优化 减少查询db
                    CountryEntity country = countryService.getById(countryId);
                    if (country == null) {
                        throw new ServiceException("country is null");
                    }
                    return country.getEnUs();
                });
    }

    @Override
    public List<ElectricPriceSystemData.Data> getRealTimePriceSystemData(
            Long startTime, Long endTime, RealTimePriceSystemContext context) {
        if (endTime == null && startTime != null) {
            endTime = startTime + EmsConstants.ONE_DAY_SECOND - 1;
        }
        Long finalEndTime = endTime;
        CountryEntity country = countryService.getById(context.countryId());
        ElectricPriceSystemData data =
                RestElectricPriceSystemUtils.getData(
                        new RestElectricPriceSystemUtils.DataContext() {
                            @Override
                            public long startTime() {
                                return startTime;
                            }

                            @Override
                            public long endTime() {
                                return finalEndTime;
                            }

                            @Override
                            public String region() {
                                if (StringUtil.isEmpty(context.priceArea())) {
                                    throw new ServiceException("electricPriceArea is null");
                                }
                                return context.priceArea();
                            }

                            @Override
                            public String country() {
                                // 有时间再优化 减少查询db
                                if (country == null) {
                                    throw new ServiceException("country is null");
                                }
                                return country.getEnUs();
                            }
                        });
        List<ElectricPriceSystemData.Data> datas = new ArrayList<>();
        if (data != null && data.getData() != null && !data.getData().isEmpty()) {
            // 根据 span 间隔 去过滤 电价系统的 数据
            datas =
                    data.getData().stream()
                            .peek(
                                    d -> {
                                        // 转换为khw , 电价系统那边是 mhw
                                        d.setAverage(d.getAverage() / 1000);
                                    })
                            // 根据 span 15 30 还是60分钟进行一个 筛选
                            .filter(
                                    d ->
                                            Objects.equals(
                                                    d.getIntervalSeconds(),
                                                    ElectricPriceSpanEnum.getValue(context.span())))
                            .collect(Collectors.toList());
        } else {
            log.warn(
                    "未查询到实时电价信息projectId:{} , priceArea:{}, country:{}",
                    WebUtils.projectId.get(),
                    context.priceArea(),
                    country.getEnUs());
        }
        return datas;
    }

    @Override
    public List<ElectricPriceEntity> getPriceList(String projectId) {
        List<ElectricPriceEntity> list = List.of();
        String id = null;
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 如果是 尖峰平谷 (国内)
        if (projectEntity.getPriceProxy()) {
            ProjectExtEntity projectExtEntity = projectExtService.getById(projectId);
            PriceAreaEntity priceAreaEntity =
                    priceAreaService.getOne(
                            Wrappers.lambdaQuery(PriceAreaEntity.class)
                                    // 注意这里的 ext 的 area 已经是 price_area 的 id
                                    .eq(PriceAreaEntity::getId, projectExtEntity.getArea()));
            if (priceAreaEntity != null) {
                // 如果是电价托管的方式 那么这个 ElectricPrice 里面的projectId 是 area 区域的id
                id = priceAreaEntity.getId();
            }
        } else {
            id = projectId;
        }
        if (id != null) {
            list =
                    this.getBaseMapper()
                            .selectList(
                                    Wrappers.<ElectricPriceEntity>lambdaQuery()
                                            // 如果是电价托管的方式 那么这个 ElectricPrice 里面的projectId 是 area
                                            // 区域的id , 如果不是电价托管的就是 项目本身的id
                                            .eq(ElectricPriceEntity::getProjectId, id)
                                            .isNull(ElectricPriceEntity::getPriceTemplateId)
                                            .isNull(ElectricPriceEntity::getPid)
                                            .orderByAsc(ElectricPriceEntity::getStartDate));

            for (ElectricPriceEntity electricPriceEntity : list) {
                List<ElectricPriceEntity> priceList =
                        this.getBaseMapper()
                                .selectList(
                                        Wrappers.<ElectricPriceEntity>lambdaQuery()
                                                .eq(
                                                        ElectricPriceEntity::getPid,
                                                        electricPriceEntity.getId())
                                                .orderByAsc(ElectricPriceEntity::getStartTime));
                electricPriceEntity.setPeriodPriceList(priceList);
            }
        }
        return list;
    }

    @Override
    public List<ElectricPriceSystemData.Data> getElectricPriceSystemDataUsePriceProxy(
            long start, ProjectEntity projectEntity) {
        if (projectEntity.getPriceProxy()) {
            PriceAreaEntity priceAreaEntity = priceAreaService.queryProxyForDynamic(projectEntity);
            if (priceAreaEntity != null) {
                projectEntity.setElectricPriceArea(priceAreaEntity.getElectricPriceArea());
                projectEntity.setElectricPriceSpan(priceAreaEntity.getElectricPriceSpan());
                projectEntity.setCountry(priceAreaEntity.getCountryId());
            } else {
                return List.of();
            }
        }
        return this.getRealTimePriceSystemData(
                start,
                null,
                new ElectricPriceService.RealTimePriceSystemContext() {
                    @Override
                    public Integer countryId() {
                        return projectEntity.getCountry();
                    }

                    @Override
                    public String priceArea() {
                        return projectEntity.getElectricPriceArea();
                    }

                    @Override
                    public String span() {
                        return projectEntity.getElectricPriceSpan();
                    }
                });
    }
}
