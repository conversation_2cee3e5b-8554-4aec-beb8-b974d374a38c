package com.wifochina.modules.operation.service.impl;

import com.wifochina.modules.operation.service.OperationQuartzService;
import com.wifochina.modules.project.service.ProjectService;

import lombok.extern.slf4j.Slf4j;

import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-15 7:27 PM
 */
@Slf4j
public abstract class AbstractOperationQuartzService implements OperationQuartzService {

    public static final String JOB_GROUP = "operation-report-group";

    @Resource protected ProjectService projectService;

    @Resource protected Scheduler scheduler;

    @Override
    public void addOperationJob(String timezoneCode, Class<? extends QuartzJobBean> jobClass) {
        // 构建JobDetail (表示一个具体的可执行的调度程序，Job是这个可执行调度程序所要执行的内容)
        JobDetail jobDetail = getOperationJobDetail(timezoneCode, jobClass);
        // 构建出发去Trigger （调度参数的配置，代表何时出发该任务)
        Trigger trigger = getOperationTrigger(timezoneCode, getJobCrontab());
        if (trigger != null) {
            try {
                scheduler.scheduleJob(jobDetail, trigger);
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
            } catch (ObjectAlreadyExistsException existsException) {
                log.warn("job exists:{}", existsException.getMessage());
            } catch (SchedulerException e) {
                log.error(e.getMessage());
            }
        }
    }

    @Override
    public void delOperationJob(String timezoneCode) {
        try {
            // TriggerKey 定义了trigger的名称和组别 ，通过任务名和任务组名获取TriggerKey
            String triggerName = getOperationTriggerName(timezoneCode);
            TriggerKey triggerKey = TriggerKey.triggerKey(triggerName, JOB_GROUP);
            // 停止触发器
            scheduler.resumeTrigger(triggerKey);
            // 移除触发器
            scheduler.unscheduleJob(triggerKey);
            // 移除任务
            String jobName = getOperationJobName(timezoneCode);
            scheduler.deleteJob(JobKey.jobKey(jobName, JOB_GROUP));
            log.info("删除定时任务成功");
        } catch (SchedulerException e) {
            log.error("");
        }
    }

    @Override
    public void rescheduleOperationJob(String timezoneCode) {
        try {
            // 通过任务名和任务组名获取jobKey
            String jobName = getOperationJobName(timezoneCode);
            JobKey jobKey = JobKey.jobKey(jobName, JOB_GROUP);
            // 继续任务
            scheduler.resumeJob(jobKey);
            log.info("继续定时任务成功");
        } catch (SchedulerException e) {
            log.error("");
        }
    }

    public JobDetail getOperationJobDetail(
            String timezoneCode, Class<? extends QuartzJobBean> jobClass) {
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = getOperationJobName(timezoneCode);
        jobData.put("timezoneCode", timezoneCode);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        return JobBuilder.newJob(jobClass)
                .withIdentity(jobName, JOB_GROUP)
                .usingJobData(jobDataMap)
                .storeDurably()
                .build();
    }

    public Trigger getOperationTrigger(String timezoneCode, String jobCron) {
        String triggerName = getOperationTriggerName(timezoneCode);
        return TriggerBuilder.newTrigger()
                .withIdentity(triggerName, JOB_GROUP)
                // .startAt(DateBuilder.futureDate(1, DateBuilder.IntervalUnit.SECOND))
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(jobCron)
                                .inTimeZone(TimeZone.getTimeZone(timezoneCode)))
                // .startNow()
                .build();
    }

    public abstract String getOperationTriggerName(String timezoneCode);

    public abstract String getOperationJobName(String timezoneCode);

    public abstract String getJobCrontab();
}
