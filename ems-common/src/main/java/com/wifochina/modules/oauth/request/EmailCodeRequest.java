package com.wifochina.modules.oauth.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;

/**
 * 请求你认证
 * 
 * <AUTHOR>
 */
@Data
public class EmailCodeRequest {

    @Email(message = "ILLEGAL_EMAIL")
    @ApiModelProperty(value = "邮箱", required = true)
    private String email;

    @ApiModelProperty(value = "验证码", required = true)
    private String code;
}
