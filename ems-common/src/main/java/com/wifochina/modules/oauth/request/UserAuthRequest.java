package com.wifochina.modules.oauth.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请求你认证
 * 
 * <AUTHOR>
 */
@Data
public class UserAuthRequest {

    @ApiModelProperty(value = "客户端ID", required = true)
    private String client_id;

    @ApiModelProperty(value = "客户端秘钥", required = true)
    private String client_secret;

    @ApiModelProperty(value = "授权方式:password", required = true)
    private String grant_type;

    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "SCOPE", required = true)
    private String scope;
}
