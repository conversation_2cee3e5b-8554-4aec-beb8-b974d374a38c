package com.wifochina.modules.oauth.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.modules.oauth.dto.UserDto;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.service.UserService;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @date 6/24/2022 1:41 PM
 * <AUTHOR>
 * @version 1.0
 */
public interface LoginService {
    String login(UserDto userDto);

    default UserEntity getLoginUser(
            @RequestBody @Validated UserDto userDto, UserService userService) {
        return userService.getOne(
                Wrappers.lambdaQuery(UserEntity.class)
                        .eq(
                                userDto.getUsername() != null,
                                UserEntity::getUserName,
                                userDto.getUsername())
                        .or()
                        .eq(
                                userDto.getUsername() != null,
                                UserEntity::getPhone,
                                userDto.getUsername())
                        .or()
                        .eq(
                                userDto.getUsername() != null,
                                UserEntity::getEmail,
                                userDto.getUsername())
                        .or()
                        .eq(userDto.getEmail() != null, UserEntity::getEmail, userDto.getEmail())
                        .or()
                        .eq(userDto.getPhone() != null, UserEntity::getPhone, userDto.getPhone()));
    }
}
