package com.wifochina.modules.oauth.vo;

import com.wifochina.modules.oauth.AuthUser;
import com.wifochina.modules.user.entity.UserEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;

import java.util.LinkedHashSet;

/**
 * <AUTHOR>
 * @since 2023-11-08 10:26 AM
 */
@Getter
public class InfoUser extends AuthUser {

    public InfoUser(UserEntity userEntity, LinkedHashSet<String> authorityList) {
        super(userEntity, authorityList);
    }

    @ApiModelProperty(value = "是否有可控电表")
    private Boolean hasControllable;

    @ApiModelProperty(value = "是否有摄像头")
    private Boolean hasCamera;

    @ApiModelProperty(value = "是否有电表")
    private Boolean hasMeter;

    public void setHasControllable(Boolean hasControllable) {
        this.hasControllable = hasControllable;
    }

    public void setHasCamera(Boolean hasCamera) {
        this.hasCamera = hasCamera;
    }

    public void setHasMeter(Boolean hasMeter) {
        this.hasMeter = hasMeter;
    }
}
