package com.wifochina.modules.oauth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.oauth.AuthUser;
import com.wifochina.modules.user.entity.AuthorityEntity;
import com.wifochina.modules.user.entity.RoleAuthorityEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.entity.UserRoleEntity;
import com.wifochina.modules.user.service.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 集成认证用户服务
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    RoleService roleServiceImpl;

    UserRoleService userRoleServiceImpl;

    RoleAuthorityService roleAuthorityService;

    AuthorityService authorityService;

    private UserService userService;

    /** 该方法由security自动调用 */
    @Override
    public AuthUser loadUserByUsername(String username)
            throws UsernameNotFoundException, ServiceException {

        UserEntity userEntity =
                userService.getOne(
                        Wrappers.lambdaQuery(UserEntity.class)
                                .eq(UserEntity::getUserName, username));
        if (userEntity == null) {
            throw new UsernameNotFoundException(ErrorResultCode.USER_PASSWORD_INVALID.value());
        }

        LinkedHashSet<String> authorityList = new LinkedHashSet<>();
        if (!EmsConstants.WH_ADMIN_USERNAME.equalsIgnoreCase(userEntity.getUserName())) {
            UserRoleEntity userRoleEntity =
                    userRoleServiceImpl.getOne(
                            new LambdaQueryWrapper<UserRoleEntity>()
                                    .eq(UserRoleEntity::getUserId, userEntity.getId()));
            // 获取角色控制点
            Optional.ofNullable(userRoleEntity)
                    .ifPresent(
                            (e) -> {
                                List<RoleAuthorityEntity> roleAuthorityList =
                                        roleAuthorityService.list(
                                                new LambdaQueryWrapper<RoleAuthorityEntity>()
                                                        .in(
                                                                RoleAuthorityEntity::getRoleId,
                                                                Collections.singletonList(
                                                                        userRoleEntity
                                                                                .getRoleId())));
                                if (roleAuthorityList != null && !roleAuthorityList.isEmpty()) {
                                    List<Long> apIds =
                                            roleAuthorityList.stream()
                                                    .map(RoleAuthorityEntity::getAuthorityId)
                                                    .collect(Collectors.toList());
                                    if (!apIds.isEmpty()) {
                                        List<AuthorityEntity> authorities =
                                                authorityService.listByIds(apIds);
                                        if (authorities != null && !authorities.isEmpty()) {
                                            authorityList.addAll(
                                                    authorities.stream()
                                                            .map(AuthorityEntity::getApKey)
                                                            .collect(Collectors.toList()));
                                        }
                                    }
                                }
                            });
        } else {
            // 默认拥有全部权限
            List<AuthorityEntity> authorities = authorityService.list();
            if (authorities != null && !authorities.isEmpty()) {
                authorityList.addAll(
                        authorities.stream()
                                .map(AuthorityEntity::getApKey)
                                .collect(Collectors.toList()));
            }
        }
        return new AuthUser(userEntity, authorityList);
    }

    public UserEntity getUserEntityByUserId(String userId) {
        return userService.getById(userId);
    }

    @Autowired
    public void setRoleServiceImpl(RoleService roleServiceImpl) {
        this.roleServiceImpl = roleServiceImpl;
    }

    @Autowired
    public void setUserRoleServiceImpl(UserRoleService userRoleServiceImpl) {
        this.userRoleServiceImpl = userRoleServiceImpl;
    }

    @Autowired
    public void setRoleAuthorityService(RoleAuthorityService roleAuthorityService) {
        this.roleAuthorityService = roleAuthorityService;
    }

    @Autowired
    public void setAuthorityService(AuthorityService authorityService) {
        this.authorityService = authorityService;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }
}
