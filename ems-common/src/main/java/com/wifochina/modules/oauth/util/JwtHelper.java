package com.wifochina.modules.oauth.util;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.wifochina.common.util.StringUtil;
import com.wifochina.common.util.ZipUtil;
import com.wifochina.modules.oauth.AuthUser;

import io.jsonwebtoken.*;
import io.jsonwebtoken.SignatureException;

import lombok.extern.slf4j.Slf4j;

import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.Instant;
import java.util.*;

/**
 * @since 6/24/2022 4:27 PM
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j
public class JwtHelper {

    public static Set<String> illegalTokenSet = new HashSet<>();

    /** 私钥 */
    @Value("${authentication.jwt.priKey}")
    private String priKey;

    /** 公钥 */
    @Value("${authentication.jwt.pubKey}")
    private String pubKey;

    /** 工期时间 s */
    @Value("${authentication.jwt.expire}")
    private Integer expire;

    public String generateUserTokenNew(JwtUserInfo jwtInfo, boolean isForever, Long expiredAt) {
        JwtBuilder jwtBuilder =
                Jwts.builder()
                        // 设置主题
                        .setSubject(jwtInfo.getUserId())
                        .claim("projectId", jwtInfo.getProjectId());

        if (!StringUtil.isEmpty(jwtInfo.getUserName())) {
            jwtBuilder.claim("userName", jwtInfo.getUserName());
        }
        if (!StringUtil.isEmpty(jwtInfo.getRoleId())) {
            jwtBuilder.claim("roleId", jwtInfo.getRoleId());
        }
        if (!StringUtil.isEmpty(jwtInfo.getAccountSystemSessionId())) {
            jwtBuilder.claim("accountSystemSessionId", jwtInfo.getAccountSystemSessionId());
        }
        return generateTokenNew(jwtBuilder, isForever, expiredAt);
    }

    /**
     * 生成用户token
     *
     * @param jwtInfo 待加密的用户信息
     * @return token
     */
    public String generateRemoteUserToken(JwtUserInfo jwtInfo, boolean isForever) {
        JwtBuilder jwtBuilder =
                Jwts.builder()
                        // 设置主题
                        .setSubject(jwtInfo.getUserId())
                        .claim("projectId", jwtInfo.getProjectId())
                        .claim("authUser", jwtInfo.getAuthUser());
        return generateTokenNew(jwtBuilder, isForever, null);
    }

    /**
     * 生成token
     *
     * @param builder JwtBuilder
     * @return token
     */
    protected String generateTokenNew(JwtBuilder builder, boolean isForever, Long expiredAt) {
        try {
            Date expirationDate;
            if (expiredAt == null) {
                expirationDate = Date.from(Instant.now().plusSeconds(expire));
            } else {
                expirationDate = Date.from(Instant.ofEpochSecond(expiredAt));
            }
            // 返回的字符串便是我们的jwt串了
            String compactJws =
                    builder.setExpiration(isForever ? null : expirationDate)
                            // 设置算法（必须）
                            .signWith(SignatureAlgorithm.RS256, getRsaPrivateKey())
                            // 这个是全部设置完成后拼成jwt串的方法
                            .compact();
            return ZipUtil.compress(compactJws);
        } catch (Exception e) {
            log.error(
                    "generate token fail, privateKey:[{}], err:[{}]",
                    priKey,
                    Throwables.getStackTraceAsString(e));
            // 抛出自定义异常
        }
        return null;
    }

    /**
     * 获取token中的用户信息
     *
     * @param token token
     * @return /
     */
    public JwtUserInfo getJwtFromToken(String token) {
        String unCompressToken = ZipUtil.decompress(token);
        if (unCompressToken == null) {
            return null;
        }
        Jws<Claims> claimsJws = this.parserToken(unCompressToken);
        if (claimsJws != null) {
            Claims body = claimsJws.getBody();
            String userId = body.getSubject();
            String projectId = String.valueOf(body.get("projectId"));
            String s = JSON.toJSONString(body.get("authUser"));
            String userName = String.valueOf(body.get("userName"));
            String roleId = String.valueOf(body.get("roleId"));
            String accountSystemSessionId = String.valueOf(body.get("accountSystemSessionId"));
            AuthUser authUser = JSON.parseObject(s, AuthUser.class);
            return JwtUserInfo.builder()
                    .userId(userId)
                    .projectId(projectId)
                    .authUser(authUser)
                    // for 账号系统需求 新增了 userName 和 roleId
                    .userName(userName)
                    .roleId(roleId)
                    .accountSystemSessionId(accountSystemSessionId)
                    .build();
        } else {
            illegalTokenSet.add(token);
        }
        return null;
    }

    /**
     * 公钥解析token
     *
     * @param token token
     * @return /
     */
    private Jws<Claims> parserToken(String token) {
        try {
            return Jwts.parser().setSigningKey(this.getRsaPublicKey()).parseClaimsJws(token);
        } catch (ExpiredJwtException ex) {
            // 过期
            // 抛出自定义异常
            log.error("parse token fail, token:[{}], err:[{}]", token, "expired");
        } catch (SignatureException ex) {
            // 签名错误
            // 抛出自定义异常
        } catch (IllegalArgumentException ex) {
            // token 为空
            // 抛出自定义异常
        } catch (Exception e) {
            log.error(
                    "parse token fail, token:[{}], publicKey:[{}], err:[{}]",
                    token,
                    pubKey,
                    Throwables.getStackTraceAsString(e));
            // 抛出自定义异常
        }
        return null;
    }

    /** 获取 RSAPublicKey */
    private RSAPublicKey getRsaPublicKey() throws NoSuchAlgorithmException {
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.decodeBase64(pubKey));
        RSAPublicKey publicKey = null;
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        try {
            publicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error(
                    "get RSAPublicKey fail ,pubKey:[{}] ,err:[{}]",
                    pubKey,
                    Throwables.getStackTraceAsString(e));
        }
        return publicKey;
    }

    /** 获取 RSAPrivateKey */
    private RSAPrivateKey getRsaPrivateKey() throws NoSuchAlgorithmException {
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(priKey));
        RSAPrivateKey privateKey = null;
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        try {
            privateKey = (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error(
                    "get RSAPrivateKey fail ,priKey:[{}], err:[{}]",
                    priKey,
                    Throwables.getStackTraceAsString(e));
        }
        return privateKey;
    }

    /** 生成base64加密后的公钥和私钥 */
    public static Map<String, String> genRsaKey() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024, new SecureRandom());
        KeyPair keyPair = keyPairGenerator.genKeyPair();
        byte[] publicKeyBytes = keyPair.getPublic().getEncoded();
        byte[] privateKeyBytes = keyPair.getPrivate().getEncoded();
        HashMap<String, String> keyMap = new HashMap<>(2);
        keyMap.put("publicKey", Base64.encodeBase64String(publicKeyBytes));
        log.info(keyMap.get("publicKey"));
        keyMap.put("privateKey", Base64.encodeBase64String(privateKeyBytes));
        log.info(keyMap.get("privateKey"));
        return keyMap;
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        System.out.println(JwtHelper.genRsaKey());
    }
}
