package com.wifochina.modules.oauth.util;

import java.io.IOException;

import javax.servlet.http.HttpServletResponse;

/**
 * rendString
 * 
 * @date 6/28/2022 5:22 PM
 * <AUTHOR>
 * @version 1.0
 */
public class WebUtils {

    // 可以考虑换成TransmittableThreadLocal
    public static ThreadLocal<String> projectId = new ThreadLocal<>();
    //public static TransmittableThreadLocal<String> projectId = new TransmittableThreadLocal<>();

    public static void rendString(HttpServletResponse response, String string) throws IOException {
        response.setStatus(200);
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        response.getWriter().print(string);
    }
}
