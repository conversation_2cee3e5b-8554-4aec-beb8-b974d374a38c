package com.wifochina.modules.oauth.util;

import com.wifochina.modules.oauth.AuthUser;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * JwtUserInfo
 *
 * @date 6/24/2022 4:50 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JwtUserInfo implements Serializable {
    /** 账号ID */
    private String userId;

    /** 当前项目ID */
    private String projectId;

    private String userName;

    private String roleId;
    private String accountSystemSessionId;

    /** 用户信息 */
    private AuthUser authUser;
}
