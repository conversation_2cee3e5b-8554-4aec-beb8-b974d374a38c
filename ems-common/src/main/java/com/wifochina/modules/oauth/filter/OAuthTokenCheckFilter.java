package com.wifochina.modules.oauth.filter;

import com.wifochina.common.util.ApplicationHolder;
import com.wifochina.common.util.StringUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created on 2024/11/4 11:27.
 *
 * <AUTHOR>
 */
@Component
public class OAuthTokenCheckFilter extends OncePerRequestFilter {
    @Override
    protected void doFilterInternal(
            HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // 新增标记 checkOldToken 如果有这个则直接按照以前的 认证接口走 不进行转发 为了存量用户 账号系统会过来认证 通过用户名密码
        if ("/api/oauth/token".equals(request.getRequestURI()) && StringUtil.isEmpty(request.getHeader("checkOldToken"))) {
            boolean shouldRedirect = true;
            // 根据是云版本还是场站版本 去走不同的认证接口
            if (ApplicationHolder.isCloudDeployType()) {
                request.getRequestDispatcher("/oauth/account/token").forward(request, response);
                return;
            }
        }
        filterChain.doFilter(request, response);
    }
}
