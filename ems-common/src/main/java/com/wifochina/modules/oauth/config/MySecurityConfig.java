package com.wifochina.modules.oauth.config;

import com.wifochina.modules.oauth.IAccountSystemAuthenticationFilter;
import com.wifochina.modules.oauth.IJwtAuthenticationTokenFilter;
import com.wifochina.modules.oauth.filter.ClientIPFilter;
import com.wifochina.modules.oauth.filter.OAuthTokenCheckFilter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.Filter;

/**
 * MySecurityConfig
 *
 * <AUTHOR>
 * @version 1.0
 * @since 6/24/2022 10:29 AM
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class MySecurityConfig extends WebSecurityConfigurerAdapter {

    @Bean
    public Filter clientIPFilter() {
        return new ClientIPFilter();
    }

    @Autowired private IJwtAuthenticationTokenFilter jwtAuthenticationTokenFilter;

    @Autowired(required = false)
    private IAccountSystemAuthenticationFilter accountSystemAuthenticationFilter;

    @Resource private AccessDeniedHandler accessDeniedHandler;

    @Resource private AuthenticationEntryPoint authenticationEntryPoint;

    @Resource private OAuthTokenCheckFilter oAuthTokenCheckFilter;
    public static final List<String> SESSION_ID_URLS =
            List.of(
                    "/oauth/account/bindEmail",
                    "/oauth/account/bindPhone",
                    "/oauth/account/changePassword",
                    "/oauth/account/updateUser",
                    "/oauth/account/cancelUser",
                    "/oauth/account/user/info",
                    "/oauth/account/roleList",
                    "/oauth/account/logout",
                    "/oauth/account/emailCode");
    public static final List<String> NO_AUTH_URLS =
            List.of(
                    //                    "/oauth/token",
                    "/oauth/account/forgetPassword",
                    "/oauth/account/emailCodeCheck",
                    "/oauth/account/nameFindEmail",
                    "/oauth/account/bindEmail",
                    "/oauth/account/bindPhone",
                    "/oauth/account/changePassword",
                    "/oauth/account/updateUser",
                    "/oauth/account/cancelUser",
                    "/oauth/account/user/info",
                    "/oauth/account/roleList",
                    "/oauth/account/logout",
                    "/oauth/account/emailUsernameCheck",
                    "/oauth/account/user/register",
                    "/oauth/account/captchaCodeCheck",
                    "/oauth/account/emailCode",
                    "/oauth/account/phoneCode",
                    "/oauth/account/captchaCode",
                    "/oauth/account/authentication",
                    "/oauth/account/token",
                    "/config/**",
                    "/device/list",
                    "/user/findEmailByUserName",
                    "/user/findPhoneByUserName",
                    "/ammeter/list",
                    "/landing/code",
                    "/landing/host",
                    "/code/send",
                    "/country/get",
                    "/country/list",
                    "/code/verify",
                    "/code/email/send",
                    "/code/mail/verify",
                    "/version/latest",
                    "/user/updatePwdByEmail",
                    "/graphicCode/send",
                    "/graphicCode/verify",
                    "/user/updatePwdByPhone",
                    "/vcc/*",
                    "/user/register",
                    "/user/registerNew",
                    "/user/verifyUserVerificationCode",
                    "/user/forgetPassword",
                    "/user/exist",
                    "/user/checkUserWhetherDelete",
                    "/static/resource/**",
                    "/v2/api-docs",
                    "/ws",
                    "/doc.html",
                    "/*.html",
                    "/**/*.html",
                    "/**/*.css",
                    "/**/*.js",
                    "/favicon.ico",
                    "/**/*.txt",
                    "/**/springfox-swagger-ui/**",
                    "/**/swagger-resources/**",
                    "/**/api-docs/**",
                    "/**/api-docs-ext/**",
                    "/webjars/**",
                    "/*.jpg",
                    "/actuator/**",
                    "/druid/**",
                    "/*.png");

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf()
                .disable()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests()
                .antMatchers("/oauth/token")
                .anonymous()
                .antMatchers(NO_AUTH_URLS.toArray(new String[0]))
                .permitAll()
                .anyRequest()
                .authenticated();
        http.addFilterBefore(oAuthTokenCheckFilter, UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(clientIPFilter(), UsernamePasswordAuthenticationFilter.class);
        if (accountSystemAuthenticationFilter != null) {
            http.addFilterAt((Filter) accountSystemAuthenticationFilter, ClientIPFilter.class);
        }
        http.addFilterBefore(
                jwtAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
        http.exceptionHandling()
                .accessDeniedHandler(accessDeniedHandler)
                .authenticationEntryPoint(authenticationEntryPoint);
    }

    @Override
    @Bean
    public AuthenticationManager authenticationManager() throws Exception {
        return super.authenticationManager();
    }
}
