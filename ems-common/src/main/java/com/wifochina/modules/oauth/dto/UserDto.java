package com.wifochina.modules.oauth.dto;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.constraints.Email;

/**
 * @date 5/6/2022 12:14 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class UserDto {

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @Email(message = "ILLEGAL_EMAIL")
    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "验证码")
    private String code;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "0：密码登录 1：手机验证码登录 2:邮箱验证码登录 ")
    private Integer type;

    @ApiModelProperty(value = "手机号区号")
    private String phoneLocCode;
}
