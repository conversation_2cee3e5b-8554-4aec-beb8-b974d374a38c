package com.wifochina.modules.oauth.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.ShearCaptcha;
import cn.hutool.core.math.Calculator;

import com.wifochina.common.config.RemoteRestTemplateRequestInterceptor;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.*;
import com.wifochina.modules.common.request.RequestWithId;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.CameraEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.CameraService;
import com.wifochina.modules.oauth.AuthUser;
import com.wifochina.modules.oauth.dto.UserDto;
import com.wifochina.modules.oauth.graphic.MyMath;
import com.wifochina.modules.oauth.request.*;
import com.wifochina.modules.oauth.service.LoginService;
import com.wifochina.modules.oauth.util.JwtHelper;
import com.wifochina.modules.oauth.util.JwtUserInfo;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.oauth.vo.GraphicVo;
import com.wifochina.modules.oauth.vo.InfoUser;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.job.CancelUserJob;
import com.wifochina.modules.user.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.TriggerKey;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 6/24/2022 1:36 PM
 */
@RestController
@Api(tags = "01-认证接口")
@Slf4j
@RequiredArgsConstructor
public class AuthController {

    private final LoginService loginService;

    private final RedisTemplate<String, String> redisTemplate;

    private final UserService userService;

    private final ProjectService projectService;

    private final SmsService smsService;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final JwtHelper jwtHelper;

    private final RestTemplateBuilder restTemplateBuilder;

    private final AmmeterService ammeterService;

    private final CameraService cameraService;

    private final EmailService emailService;

    private final Scheduler scheduler;

    @Value("${sms.loginId}")
    private String loginId;

    @Value("${sms.verifyId}")
    private String verifyId;

    @Value("${sms.registerId}")
    private String registerId;

    @Value("${sms.loginExpire}")
    private String loginSmsExpire;

    @Value("${email.loginExpire}")
    private String loginEmailExpire;

    @Value("${graphic.verifyExpire}")
    private String graphicExpire;

    @Value("${sms.codeLength}")
    private Integer codeLength;

    @Value("${user.regret}")
    private String regretTime;

    @Value("${local.forward}")
    private String forwardUrl;

    @ApiOperation("用户授权登录")
    @PostMapping("/oauth/token")
    public Result<Map<String, Object>> getAccessToken(@Validated @RequestBody UserDto userDto) {

        String token = loginService.login(userDto);
        Map<String, Object> map = new HashMap<>(2);
        map.put("access_token", token);
        //        UserEntity userEntity = LoginServiceImpl.getLoginUser(userDto, userService);
        UserEntity userEntity = loginService.getLoginUser(userDto, userService);
        if (userEntity != null) {
            List<ProjectEntity> list = userService.getProjectsByUserId(userEntity.getId());
            map.put("project", list);
            map.put("info", userEntity);
            List<String> authorityList = userService.getUserAuthorityByUserId(userEntity.getId());
            map.put("authority", authorityList);
        }
        // 如何是未反悔的访客用户，过期了直接直接删除，没过期就取消删除
        Optional.ofNullable(userEntity)
                .ifPresent(
                        e -> {
                            if (EmsConstants.USER_ROLE_REGISTER.equals(userEntity.getRole())
                                    && Boolean.TRUE.equals(userEntity.getIsDelete())) {
                                int oneDaySecond = 86400;
                                if ((userEntity.getUpdateTime()
                                                + Math.ceil(Float.parseFloat(regretTime))
                                                        * oneDaySecond)
                                        < Instant.now().getEpochSecond()) {
                                    // 这一部分 正常不会执行了 会走定时任务执行, 暂时不动
                                    userService.removeUser(userEntity.getId());
                                    throw new ServiceException(
                                            ErrorResultCode.USER_PASSWORD_INVALID.value());
                                } else {
                                    cancelLogoutJob(userEntity);
                                }
                            }
                        });
        return Result.success(map);
    }

    /**
     * 取消掉 注销的定时任务
     *
     * @param userEntity : 用户
     */
    private void cancelLogoutJob(UserEntity userEntity) {
        // 取消的逻辑, 找到job 和 trigger 然后给删除
        String jobName = CancelUserJob.getJobName(userEntity.getId());
        String triggerName = CancelUserJob.getTriggerName(userEntity.getId());
        try {
            TriggerKey triggerKey =
                    TriggerKey.triggerKey(triggerName, CancelUserJob.CANCEL_JOB_GROUP);
            // 停止触发器
            scheduler.resumeTrigger(triggerKey);
            // 移除触发器
            scheduler.unscheduleJob(triggerKey);
            // 移除任务
            scheduler.deleteJob(JobKey.jobKey(jobName, CancelUserJob.CANCEL_JOB_GROUP));
            log.info("删除 注销用户jobName: {} 定时任务成功", jobName);
        } catch (SchedulerException ex) {
            log.error("cancel  logout error :{}", ex.getMessage());
            throw new RuntimeException(ex);
        }
        // 以前的把 用户的删除状态标志位 回复
        userService
                .lambdaUpdate()
                .set(UserEntity::getIsDelete, false)
                .eq(UserEntity::getId, userEntity.getId())
                .update();
    }

    @ApiOperation("远程登录token获取")
    @PostMapping("/oauth/remoteToken")
    public Result<String> getRemoteAccessToken(@RequestBody RequestWithId requestWithId) {
        ProjectEntity projectEntity = projectService.getById(requestWithId.getId());
        AuthUser loginUser = SecurityUtil.getPrincipal();
        if (EmsConstants.WH_ADMIN_USERNAME.equals(SecurityUtil.getUsername())) {
            assert loginUser != null;
            loginUser.getUserEntity().setId("1");
        }
        // TODO  需要注意 userName 和  roleId
        String token =
                jwtHelper.generateRemoteUserToken(
                        JwtUserInfo.builder()
                                .userId(
                                        EmsConstants.WH_ADMIN_USERNAME.equals(
                                                        SecurityUtil.getUsername())
                                                ? "1"
                                                : SecurityUtil.getUserId())
                                .authUser(loginUser)
                                .build(),
                        false);
        String url = forwardUrl + projectEntity.getProjectDeviceAlias() + "/api/user/addLocalUser";
        RestTemplate remoteRestTemplate =
                restTemplateBuilder
                        .setConnectTimeout(Duration.ofMillis(5000))
                        .setReadTimeout(Duration.ofMillis(20000))
                        .build();
        remoteRestTemplate.setInterceptors(
                List.of(
                        new RemoteRestTemplateRequestInterceptor(
                                projectEntity.getLocalProjectId(), token)));
        try {
            remoteRestTemplate.getForObject(url, Object.class);
        } catch (Exception e) {
            log.error("can not add local user for " + projectEntity.getProjectName());
        }
        // 生成jwt
        return Result.success(token);
    }

    @ApiOperation("退出登录")
    @GetMapping("/oauth/logout")
    public Result<Object> deleteAccessToken() {
        AuthUser userEntity =
                (AuthUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        try {
            redisTemplate.delete(userEntity.getUserEntity().getId());
        } catch (Exception ignored) {

        }
        return Result.success();
    }

    /** 获取登录用户信息 */
    @GetMapping("/user/info")
    @ApiOperation("获取当前用户信息")
    public Result<InfoUser> getUserInfo() {
        if (!StringUtils.hasLength(SecurityUtil.getUserId())) {
            return Result.failure(ErrorResultCode.ILLEGAL_ACCESS.value());
        }
        AuthUser authUser = SecurityUtil.getPrincipal();
        assert authUser != null;
        InfoUser infoUser = new InfoUser(authUser.getUserEntity(), authUser.getAuthorityList());
        BeanUtils.copyProperties(authUser, infoUser);
        List<ProjectEntity> list = List.of();
        try {
            list = userService.getProjectsByUserId(SecurityUtil.getUserId());
        } catch (NullPointerException ignored) {
            // 如果是场站版本远程登录， 可能无项目。
        }
        infoUser.setProjects(list);
        Optional.ofNullable(WebUtils.projectId.get())
                .ifPresent(
                        e -> {
                            boolean hasControllable =
                                    ammeterService
                                                    .lambdaQuery()
                                                    .eq(AmmeterEntity::getControllable, true)
                                                    .eq(AmmeterEntity::getProjectId, e)
                                                    .count()
                                            > 0;
                            boolean hasCamera =
                                    cameraService
                                                    .lambdaQuery()
                                                    .eq(CameraEntity::getProjectId, e)
                                                    .count()
                                            > 0;
                            boolean hasMeter =
                                    ammeterService
                                                    .lambdaQuery()
                                                    .eq(AmmeterEntity::getProjectId, e)
                                                    .count()
                                            > 0;
                            infoUser.setHasControllable(hasControllable);
                            infoUser.setHasCamera(hasCamera);
                            infoUser.setHasMeter(hasMeter);
                        });
        return Result.success(infoUser);
    }

    @ApiOperation("手机验证码发送")
    @PostMapping("/code/send")
    public Result<Map<String, Object>> sendCode(@RequestBody SmsRequest smsRequest) {
        if (!RegexUtil.isPhone(smsRequest.getPhone())) {
            throw new ServiceException(ErrorResultCode.PHONE_ILLEGAL.value());
        }
        Long second = redisTemplate.getExpire(smsRequest.getPhone(), TimeUnit.SECONDS);
        // 验证码用途:1登录 2修改手机号 3注册
        String type = loginId;
        if (smsRequest.getType().equals(2)) {
            type = verifyId;
        }
        if (smsRequest.getType().equals(3)) {
            type = registerId;
        }
        long expire = Long.parseLong(loginSmsExpire);
        int oneMinute = 60;
        int noKeyInRedis = -2;
        if (second == null || second == noKeyInRedis || second < (expire - 1) * oneMinute) {
            String tempCode = String.valueOf(System.currentTimeMillis());
            String code = tempCode.substring(tempCode.length() - codeLength);
            final String finalType = type;
            threadPoolTaskExecutor.execute(
                    () -> smsService.send(smsRequest.getPhone(), code, finalType));
            redisTemplate.opsForValue().set(smsRequest.getPhone(), code);
            redisTemplate.expire(smsRequest.getPhone(), expire, TimeUnit.MINUTES);
            return Result.success();
        } else {
            throw new ServiceException(ErrorResultCode.SMS_SEND_ONE_MINUTE.value());
        }
    }

    @ApiOperation("手机验证码校验")
    @PostMapping("/code/verify")
    public Result<Void> smcCodeVerify(@RequestBody PhoneCodeRequest phoneCodeRequest) {
        String code = redisTemplate.opsForValue().get(phoneCodeRequest.getPhone());
        if (!StringUtils.hasLength(code)) {
            throw new ServiceException(ErrorResultCode.CODE_IS_EXPIRE.value());
        } else {
            if (!code.equals(String.valueOf(phoneCodeRequest.getCode()))) {
                throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
            }
        }
        return Result.success();
    }

    @ApiOperation("邮箱验证码发送")
    @PostMapping("/code/email/send")
    public Result<Map<String, Object>> sendEmailCode(
            @Validated @RequestBody EmailRequest emailRequest) {
        Long second = redisTemplate.getExpire(emailRequest.getEmail(), TimeUnit.SECONDS);
        long expire = Long.parseLong(loginEmailExpire);
        int oneMinute = 60;
        int noKeyInRedis = -2;
        if (second == null || second == noKeyInRedis || second < (expire - 1) * oneMinute) {
            String tempCode = String.valueOf(System.currentTimeMillis());
            String code = tempCode.substring(tempCode.length() - codeLength);
            String subject = "Email verification code";
            String message = "code will expire in 5 minutes: " + code;
            threadPoolTaskExecutor.execute(
                    () ->
                            emailService.sendMessage(
                                    List.of(emailRequest.getEmail()), subject, message));
            redisTemplate.opsForValue().set(emailRequest.getEmail(), code);
            redisTemplate.expire(emailRequest.getEmail(), expire, TimeUnit.MINUTES);
            return Result.success();
        } else {
            throw new ServiceException(ErrorResultCode.SMS_SEND_ONE_MINUTE.value());
        }
    }

    @ApiOperation("邮箱验证码校验")
    @PostMapping("/code/mail/verify")
    public Result<Void> emailCodeVerify(@RequestBody EmailCodeRequest emailCodeRequest) {
        String code = redisTemplate.opsForValue().get(emailCodeRequest.getEmail());
        if (!StringUtils.hasLength(code)) {
            throw new ServiceException(ErrorResultCode.CODE_IS_EXPIRE.value());
        } else {
            if (!code.equals(String.valueOf(emailCodeRequest.getCode()))) {
                throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
            }
        }
        return Result.success();
    }

    @ApiOperation("图形验证码发送")
    @GetMapping("/graphicCode/send")
    public Result<GraphicVo> graphicCode() {
        long expire = Long.parseLong(graphicExpire == null ? "5" : graphicExpire);
        String tempCode = String.valueOf(System.currentTimeMillis());
        String codeId = tempCode.substring(tempCode.length() - 10);
        ShearCaptcha captcha = CaptchaUtil.createShearCaptcha(200, 45, 2, 2);
        // 自定义验证码内容为四则运算方式
        captcha.setGenerator(new MyMath());
        // 重新生成code
        captcha.createCode();
        int calculateResult = (int) Calculator.conversion(captcha.getCode());
        log.info("code is ---------------------------> " + captcha.getCode());
        redisTemplate.opsForValue().set(codeId, String.valueOf(calculateResult));
        redisTemplate.expire(codeId, expire, TimeUnit.MINUTES);
        GraphicVo graphicVo = new GraphicVo();
        graphicVo.setId(codeId);
        graphicVo.setImage64(captcha.getImageBase64());
        return Result.success(graphicVo);
    }

    @ApiOperation("图形验证码验证")
    @PostMapping("/graphicCode/verify")
    public Result<Map<String, Object>> graphicCode(@RequestBody GraphicRequest graphicRequest) {
        String code = redisTemplate.opsForValue().get(graphicRequest.getId());
        if (!StringUtils.hasLength(code)) {
            throw new ServiceException(ErrorResultCode.CODE_IS_EXPIRE.value());
        } else {
            if (!code.equals(String.valueOf(graphicRequest.getCode()))) {
                throw new ServiceException(ErrorResultCode.CODE_IS_INCORRECT.value());
            }
        }
        redisTemplate.delete(graphicRequest.getId());
        return Result.success();
    }
}
