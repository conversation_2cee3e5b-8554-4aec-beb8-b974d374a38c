package com.wifochina.modules.oauth.util;

import org.springframework.security.core.context.SecurityContextHolder;

import com.wifochina.modules.oauth.AuthUser;

/**
 * <AUTHOR>
 */
public class SecurityUtil {

    /** 获取用户名 */
    public static String getUsername() {
        AuthUser user = getPrincipal();
        if (user != null) {
            return user.getUsername();
        }
        return null;
    }

    /** 获取认证用户信息 */
    public static AuthUser getPrincipal() {
        if (SecurityContextHolder.getContext() == null
                || SecurityContextHolder.getContext().getAuthentication() == null) {
            return null;
        }
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal != null) {
            if (principal instanceof AuthUser) {
                AuthUser authUser = ((AuthUser) principal);
                authUser.setPassword(null);
                return authUser;
            }
        }
        return null;
    }

    public static String getUserId() {
        AuthUser user = getPrincipal();
        if (user != null) {
            return user.getUserEntity().getId();
        }
        return null;
    }

    public static String getLoginRoleId() {
        AuthUser user = getPrincipal();
        if (user != null) {
            return user.getUserEntity().getRoleId();
        }
        return null;
    }

    public static Integer getAuthStat() {
        AuthUser user = getPrincipal();
        if (user != null) {
            return user.getUserEntity().getAuthStat();
        }
        return null;
    }

    public static boolean hasAuthority(String authority) {
        AuthUser user = getPrincipal();
        if (user != null) {
            return user.getAuthorities().toString().contains(authority);
        }
        return false;
    }
}
