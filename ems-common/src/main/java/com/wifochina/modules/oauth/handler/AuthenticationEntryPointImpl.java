package com.wifochina.modules.oauth.handler;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.page.Result;
import com.wifochina.modules.oauth.util.WebUtils;

/**
 * AuthenticationEntryPointImpl
 * 
 * @date 6/28/2022 5:20 PM
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
        AuthenticationException authException) throws IOException {
        Result<Object> resultVo = Result.failure(ErrorResultCode.ILLEGAL_ACCESS.value());
        WebUtils.rendString(response, JSON.toJSONString(resultVo));
    }
}
