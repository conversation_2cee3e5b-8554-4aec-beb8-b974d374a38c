package com.wifochina.modules.oauth.graphic;

import cn.hutool.captcha.generator.CodeGenerator;
import cn.hutool.core.math.Calculator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
public class MyMath implements CodeGenerator {
    private static final long serialVersionUID = -5514819971774091076L;
    private static final String operators = "+-*";
    private final int numberLength;

    public MyMath() {
        this(2);
    }

    public MyMath(int numberLength) {
        this.numberLength = numberLength;
    }

    @Override
    public String generate() {
        int limit = this.getLimit();
        String number1 = Integer.toString(RandomUtil.randomInt(limit));
        number1 = StrUtil.padAfter(number1, this.numberLength, ' ');
        String number2 = StrUtil.padAfter("1", this.numberLength, ' ');
        return StrUtil.builder().append(number1).append(RandomUtil.randomChar("+-")).append(number2).append('=')
            .toString();
    }

    @Override
    public boolean verify(String code, String userInputCode) {
        int result;
        try {
            result = Integer.parseInt(userInputCode);
        } catch (NumberFormatException var5) {
            return false;
        }

        int calculateResult = (int)Calculator.conversion(code);
        return result == calculateResult;
    }

    public int getLength() {
        return this.numberLength * 2 + 2;
    }

    private int getLimit() {
        return Integer.parseInt("1" + StrUtil.repeat('0', this.numberLength));
    }
}
