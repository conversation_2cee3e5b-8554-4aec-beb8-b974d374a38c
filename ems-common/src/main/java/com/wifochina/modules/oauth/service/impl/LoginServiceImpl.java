package com.wifochina.modules.oauth.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.LoginFailException;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.StringUtil;
import com.wifochina.common.web.HttpsClientRequestFactory;
import com.wifochina.modules.oauth.AuthUser;
import com.wifochina.modules.oauth.dto.UserDto;
import com.wifochina.modules.oauth.service.LoginService;
import com.wifochina.modules.oauth.util.JwtHelper;
import com.wifochina.modules.oauth.util.JwtUserInfo;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.request.UserRegisterRequest;
import com.wifochina.modules.user.service.UserService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 6/24/2022 1:42 PM
 */
@Service
@Slf4j
public class LoginServiceImpl implements LoginService {

    private AuthenticationManager authenticationManager;
    private JwtHelper jwtHelper;

    private RedisTemplate<String, String> redisTemplate;

    private UserDetailsService userDetailsService;

    private UserService userService;

    private RestTemplate restTemplate;

    @Value("${ems.adUrl}")
    private String adUrl;

    private HttpsClientRequestFactory httpsClientRequestFactory;

    private static final int LOGIN_ERROR_COUNT = 5;

    @Override
    public String login(UserDto userDto) {
        // 进行用户认证
        AuthUser loginUser = null;
        UserEntity tempUserEntity = getLoginUser(userDto, userService);

        // 这边要进行 登录错误次数校验
        // 1.根据查询到的 UserEntity 获取到id  然后去redis中查询 目前已经错误的次数 如果超过了5次 则直接返回错误 并且根据value 值进行时间判断
        if (tempUserEntity != null) {
            checkLoginErrorCount(tempUserEntity);
        }

        if (userDto.getType() != null && userDto.getType() == 1) {
            // if code is correct
            if (tempUserEntity == null) {
                throw new ServiceException(ErrorResultCode.PHONE_IS_NOT_REGISTER.value());
            } else {
                String code =
                        JSON.parseObject(
                                redisTemplate.opsForValue().get(userDto.getPhone()), String.class);
                if (userDto.getCode().equals(code)) {
                    loginUser =
                            (AuthUser)
                                    userDetailsService.loadUserByUsername(
                                            tempUserEntity.getUserName());
                    redisTemplate.delete(userDto.getPhone());
                } else {
                    loginErrorProcess(
                            tempUserEntity, ErrorResultCode.CODE_IS_INCORRECT.value(), "验证码错误");
                }
            }
        } else if (userDto.getType() != null && userDto.getType() == 2) {
            // if code is correct
            if (tempUserEntity == null) {
                throw new ServiceException(ErrorResultCode.EMAIL_IS_NOT_REGISTER.value());
            } else {
                String code =
                        JSON.parseObject(
                                redisTemplate.opsForValue().get(userDto.getEmail()), String.class);
                if (userDto.getCode().equals(code)) {
                    loginUser =
                            (AuthUser)
                                    userDetailsService.loadUserByUsername(
                                            tempUserEntity.getUserName());
                    redisTemplate.delete(userDto.getEmail());
                } else {
                    loginErrorProcess(
                            tempUserEntity, ErrorResultCode.CODE_IS_INCORRECT.value(), "验证码错误");
                }
            }
        } else {
            if (tempUserEntity == null) {
                // 这里是 当前的用户找不到
                if (userDto.getType() == null || userDto.getType() == 0) {
                    // login ad as true
                    Map<String, String> paraMap = new HashMap<>(2);
                    paraMap.put("username", userDto.getUsername());
                    paraMap.put("password", userDto.getPassword());
                    try {
                        Optional.ofNullable(adUrl)
                                .ifPresent(
                                        (e) ->
                                                restTemplate.setRequestFactory(
                                                        httpsClientRequestFactory));
                        assert adUrl != null;
                        JSONObject jsonObject =
                                restTemplate.postForObject(adUrl, paraMap, JSONObject.class);
                        String key = "message";
                        assert jsonObject != null;
                        if (jsonObject.get(key).equals(EmsConstants.SUCCESS)) {
                            UserRegisterRequest registerRequest = new UserRegisterRequest();
                            registerRequest.setUserName(userDto.getUsername());
                            registerRequest.setType(EmsConstants.USER_ROLE_AD);
                            String name = (String) (jsonObject.getJSONObject("data").get("name"));
                            registerRequest.setName(name);
                            registerRequest.setPassword(userDto.getPassword());
                            registerRequest.setConfirmPassword(userDto.getPassword());
                            userService.register(registerRequest);
                        } else {
                            throw new ServiceException(ErrorResultCode.USER_NOT_EXISTS.value());
                        }
                    } catch (Exception e) {
                        // throw new
                        // ServiceException(ErrorResultCode.USER_PASSWORD_INVALID.value());
                        throw new ServiceException(ErrorResultCode.USER_NOT_EXISTS.value());
                    }
                }
            }
            if (tempUserEntity != null
                    && EmsConstants.USER_ROLE_AD.equals(tempUserEntity.getRole())) {
                // login ad as true
                Map<String, String> paraMap = new HashMap<>(2);
                paraMap.put("username", tempUserEntity.getUserName());
                paraMap.put("password", userDto.getPassword());
                try {
                    Optional.ofNullable(adUrl)
                            .ifPresent(
                                    (e) ->
                                            restTemplate.setRequestFactory(
                                                    httpsClientRequestFactory));
                    assert adUrl != null;
                    JSONObject jsonObject =
                            restTemplate.postForObject(adUrl, paraMap, JSONObject.class);
                    String key = "message";
                    assert jsonObject != null;
                    if (jsonObject.get(key).equals(EmsConstants.SUCCESS)) {
                        loginUser =
                                (AuthUser)
                                        userDetailsService.loadUserByUsername(
                                                tempUserEntity.getUserName());
                    }
                } catch (Exception e) {
                    //                    loginErrorProcess(
                    //                            tempUserEntity,
                    // ErrorResultCode.USER_PASSWORD_INVALID.value());
                    loginErrorProcess(
                            tempUserEntity,
                            ErrorResultCode.USER_PASSWORD_INVALID.value(),
                            "用户名或密码错误");
                }

            } else {
                UsernamePasswordAuthenticationToken authenticationToken =
                        new UsernamePasswordAuthenticationToken(
                                tempUserEntity == null
                                        ? userDto.getUsername()
                                        : tempUserEntity.getUserName(),
                                userDto.getPassword());
                try {
                    Authentication authentication =
                            authenticationManager.authenticate(authenticationToken);
                    loginUser = (AuthUser) authentication.getPrincipal();
                } catch (BadCredentialsException e) {
                    if (tempUserEntity != null) {
                        loginErrorProcess(
                                tempUserEntity,
                                ErrorResultCode.USER_PASSWORD_INVALID.value(),
                                "用户名或密码错误");
                    }
                }
            }
            if (loginUser == null) {
                if (tempUserEntity != null) {
                    loginErrorProcess(
                            tempUserEntity,
                            ErrorResultCode.USER_PASSWORD_INVALID.value(),
                            "用户名或密码错误");
                }
            }
        }
        if (loginUser == null) {
            if (tempUserEntity != null) {
                loginErrorProcess(
                        tempUserEntity, ErrorResultCode.USER_PASSWORD_INVALID.value(), "用户名或密码错误");
                // loginErrorProcess(tempUserEntity, ErrorResultCode.USER_PASSWORD_INVALID.value());
            }
            return null;
        }
        UserEntity userEntity = loginUser.getUserEntity();
        // redis user store information
        redisTemplate
                .opsForValue()
                .set(String.valueOf(userEntity.getId()), JSON.toJSONString(loginUser));
        // 生成jwt
        String id = String.valueOf(userEntity.getId());
        boolean isForever = userEntity.getUserName().contains(EmsConstants.ADMIN_USERNAME);
        // 2024-03-18 14:21:09 add 清除缓存的错误登录的次数
        redisTemplate.delete("login_error_count_" + id);
        // 清除 锁定的次数
        userEntity.setLockCount(0);
        userService.updateById(userEntity);
        return jwtHelper.generateUserTokenNew(
                JwtUserInfo.builder().userId(id).build(), isForever, null);
    }

    private void checkLoginErrorCount(UserEntity tempUserEntity) {
        String errorCount =
                redisTemplate.opsForValue().get("login_error_count_" + tempUserEntity.getId());
        if (!StringUtil.isEmpty(errorCount)) {
            if (Integer.parseInt(errorCount) >= LOGIN_ERROR_COUNT) {
                // 直接返回错误 并且要返回 时间  多久后尝试
                Long expire =
                        redisTemplate.getExpire("login_error_count_" + tempUserEntity.getId());
                // 还有多久过期时间 ,看看能不能通过 exception 返回给移动端
                if (expire != null) {
                    // if (expire < TimeUnit.HOURS.toSeconds(1)) {
                    // 计算剩余分钟数
                    long minutes = TimeUnit.SECONDS.toMinutes(expire);
                    log.info(
                            "User:{} ,Remaining time: " + minutes + " minutes can login",
                            tempUserEntity.getId());
                    throw new LoginFailException(
                            ErrorResultCode.ACCOUNT_IS_LOCK.value(),
                            "账号已经锁定",
                            new LoginFailException.FailData(0, (int) minutes));
                    //                        throw new ServiceException(
                    //                                ErrorResultCode.ACCOUNT_IS_LOCK.value(),
                    //                                "账号已经锁定，请" + minutes + "分钟后尝试");
                    //                    } else {
                    //                        // 计算剩余小时数
                    //                        long hours = TimeUnit.SECONDS.toHours(expire);
                    //                        long remainingMinutes =
                    //                                TimeUnit.SECONDS.toMinutes(
                    //                                        expire -
                    // TimeUnit.HOURS.toSeconds(hours));
                    //                        log.info(
                    //                                "User:{} ,Remaining time: " + hours + " hours
                    // minutes:{} can login",
                    //                                tempUserEntity.getId(),
                    //                                remainingMinutes);
                    //                        throw new
                    // LoginFailException(ErrorResultCode.ACCOUNT_IS_LOCK.value(),
                    //                                "账号已经锁定", new
                    // LoginFailException.FailData(0,(int)minutes));
                    //                        throw new ServiceException(
                    //                                ErrorResultCode.ACCOUNT_IS_LOCK.value(),
                    //                                "账号已经锁定，请" + hours + "小时" + remainingMinutes +
                    // "分钟后尝试");
                    //                    }
                } else {
                    log.error(
                            "User:{} ,Remaining time: 0 seconds can login system error",
                            tempUserEntity.getId());
                }
            }
        }
    }

    private void loginErrorProcess(
            UserEntity tempUserEntity, String errorResultCode, String message) {
        String errorCount;
        int remainCount;
        errorCount = redisTemplate.opsForValue().get("login_error_count_" + tempUserEntity.getId());
        if (!StringUtil.isEmpty(errorCount)) {
            remainCount = LOGIN_ERROR_COUNT - (Integer.parseInt(errorCount) + 1);
            // 记录一下 错误次数
            redisTemplate
                    .opsForValue()
                    .set(
                            "login_error_count_" + tempUserEntity.getId(),
                            String.valueOf(Integer.parseInt(errorCount) + 1));
            // 大于>5了 就表示要进行锁定了
            if (Integer.parseInt(errorCount) + 1 >= LOGIN_ERROR_COUNT) {
                // 这里要判断 lockCount 是否>0 如果>0 则表示已经锁定了 需要锁定的时间 变成24小时
                Integer lockCount = tempUserEntity.getLockCount();
                if (lockCount != null && lockCount <= 0) {
                    // 设置过期时间
                    redisTemplate.expire(
                            "login_error_count_" + tempUserEntity.getId(), 30, TimeUnit.MINUTES);
                    tempUserEntity.setLockCount(tempUserEntity.getLockCount() + 1);
                    userService.updateById(tempUserEntity);
                    throw new LoginFailException(
                            errorResultCode, message, new LoginFailException.FailData(0, 30));
                } else {
                    // 设置过期时间
                    redisTemplate.expire(
                            "login_error_count_" + tempUserEntity.getId(), 24, TimeUnit.HOURS);
                    tempUserEntity.setLockCount(tempUserEntity.getLockCount() + 1);
                    userService.updateById(tempUserEntity);
                    // throw new ServiceException(errorResultCode, "账号已锁定，请24小时后重试");
                    throw new LoginFailException(
                            errorResultCode, message, new LoginFailException.FailData(0, 24 * 60));
                }
            }
        } else {
            // 表示 第一次的错误 记录一下 错误数是 1
            remainCount = LOGIN_ERROR_COUNT - 1;
            redisTemplate.opsForValue().set("login_error_count_" + tempUserEntity.getId(), "1");
        }
        throw new LoginFailException(
                errorResultCode, message, new LoginFailException.FailData(remainCount, 0));
        // throw new ServiceException(errorResultCode, "剩余次数" + remainCount + "/" +
        // LOGIN_ERROR_COUNT);
    }

    @Autowired
    public void setAuthenticationManager(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
    }

    @Autowired
    public void setJwtHelper(JwtHelper jwtHelper) {
        this.jwtHelper = jwtHelper;
    }

    @Autowired
    @Qualifier("userDetailsServiceImpl")
    public void setUserDetailsService(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    public void setHttpsClientRequestFactory(HttpsClientRequestFactory httpsClientRequestFactory) {
        this.httpsClientRequestFactory = httpsClientRequestFactory;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    //    public static UserEntity getLoginUser(
    //            @RequestBody @Validated UserDto userDto, UserService userService) {
    //        return userService.getOne(
    //                Wrappers.lambdaQuery(UserEntity.class)
    //                        .eq(
    //                                userDto.getUsername() != null,
    //                                UserEntity::getUserName,
    //                                userDto.getUsername())
    //                        .or()
    //                        .eq(
    //                                userDto.getUsername() != null,
    //                                UserEntity::getPhone,
    //                                userDto.getUsername())
    //                        .or()
    //                        .eq(
    //                                userDto.getUsername() != null,
    //                                UserEntity::getEmail,
    //                                userDto.getUsername())
    //                        .or()
    //                        .eq(userDto.getEmail() != null, UserEntity::getEmail,
    // userDto.getEmail())
    //                        .or()
    //                        .eq(userDto.getPhone() != null, UserEntity::getPhone,
    // userDto.getPhone()));
    //    }
}
