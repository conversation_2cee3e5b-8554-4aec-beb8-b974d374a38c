package com.wifochina.modules.oauth;

import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.user.entity.UserEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * LoginUser
 *
 * @since 6/24/2022 9:56 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AuthUser implements UserDetails {

    private final UserEntity userEntity;

    private String accountSystemSessionId;
    private List<ProjectEntity> projects;

    @ApiModelProperty(value = "权限表")
    private final LinkedHashSet<String> authorityList;

    //    private List<PolicyAllowObj> policyAlloweList;

    public AuthUser(
            UserEntity userEntity,
            String accountSystemSessionId,
            LinkedHashSet<String> authorityList) {
        this.userEntity = userEntity;
        this.accountSystemSessionId = accountSystemSessionId;
        this.authorityList = authorityList;
        //        this.policyAlloweList = policyAllowObjList;
    }

    public AuthUser(UserEntity userEntity, LinkedHashSet<String> authorityList) {
        this.userEntity = userEntity;
        this.authorityList = authorityList;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return this.authorityList.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    @Override
    public String getPassword() {
        return userEntity.getPassword();
    }

    @Override
    public String getUsername() {
        return userEntity.getUserName();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    public void setPassword(String password) {}
}
