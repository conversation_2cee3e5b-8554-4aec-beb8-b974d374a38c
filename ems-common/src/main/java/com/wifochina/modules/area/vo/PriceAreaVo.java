package com.wifochina.modules.area.vo;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
public class PriceAreaVo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电价配置id")
    private String id;

    @ApiModelProperty(value = "项目地区")
    private String area;

    @ApiModelProperty(value = "用电类型id")
    private String powerTypeId;

    @ApiModelProperty(value = "用电类型")
    private String powerType;

    @ApiModelProperty(value = "用电类型英文")
    private String powerTypeEn;

    @ApiModelProperty(value = "电压等级id")
    private String powerLevelId;

    @ApiModelProperty(value = "电压等级")
    private String powerLevel;

    @ApiModelProperty(value = "电价类型")
    private String priceType;

    @ApiModelProperty(value = "国家Id")
    private Integer countryId;

    /** 电价区域字段 */
    @ApiModelProperty("电价区域")
    private String electricPriceArea;

    /** 电价跨度字段 */
    @ApiModelProperty("电价跨度")
    private String electricPriceSpan;
}
