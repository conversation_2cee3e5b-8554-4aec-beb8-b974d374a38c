package com.wifochina.modules.area.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * PriceAreaPageRequest
 * 
 * @date 9/5/2022 5:15 PM
 * <AUTHOR>
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "管理端-项目区域查询")
public class PriceAreaPageRequest extends PageBean {
    @ApiModelProperty(value = "项目地区")
    private String area;

    @ApiModelProperty(value = "用电类型")
    private String powerType;

    @ApiModelProperty(value = "电压等级")
    private String powerLevel;
}
