<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.area.mapper.PriceAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="PriceAreaEntity" type="com.wifochina.modules.area.vo.PriceAreaVo">
        <result column="id" property="id"/>
        <result column="area" property="area"/>
        <result column="power_type_id" property="powerTypeId"/>
        <result column="power_type" property="powerType"/>
        <result column="power_type_en" property="powerTypeEn"/>
        <result column="power_level_id" property="powerLevelId"/>
        <result column="power_level" property="powerLevel"/>
        <result column="price_type" property="priceType"/>
        <result column="country_id" property="countryId"/>
        <result column="electric_price_area" property="electricPriceArea"/>
        <result column="electric_price_span" property="electricPriceSpan"/>
    </resultMap>

    <select id="queryPriceArea"
            parameterType="java.lang.String"
            resultMap="PriceAreaEntity">
        select a.id,
               a.area,
               a.price_type,
               a.country_id,
               a.electric_price_area,
               a.electric_price_span,
               l.uuid as power_Level_Id,
               l.power_level,
               t.uuid as power_type_id,
               t.power_type,
               t.power_type_en
        from t_price_area a,
             t_power_level l,
             t_power_type t
        where a.power_type = t.uuid
          and a.power_level = l.uuid
        order by a.area
    </select>
</mapper>
