package com.wifochina.modules.area.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.vo.PriceAreaVo;
import com.wifochina.modules.project.entity.ProjectEntity;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface PriceAreaService extends IService<PriceAreaEntity> {
    IPage<PriceAreaVo> queryPriceArea(IPage<PriceAreaVo> priceAreaPage);

    PriceAreaEntity queryProxyForDynamic(ProjectEntity projectEntity);
}
