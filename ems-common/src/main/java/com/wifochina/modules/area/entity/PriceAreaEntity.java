package com.wifochina.modules.area.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_price_area")
@ApiModel(value = "PriceAreaEntity对象")
public class PriceAreaEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电价配置id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "项目地区")
    private String area;

    @ApiModelProperty(value = "用电类型")
    private String powerType;

    @ApiModelProperty(value = "电压等级")
    private String powerLevel;

    @ApiModelProperty(value = "电价类型")
    /**
     * @see com.wifochina.common.constants.ElectricPriceTypeEnum
     */
    private String priceType;

    /** 1.3.7 add 下面3个 为了让这个 托管在 这个区域下的 选择实时电价的 能有一个特定的国家的 实时电价 */
    @ApiModelProperty(value = "国家Id")
    private Integer countryId;

    /** 电价区域字段 */
    @ApiModelProperty("电价区域")
    private String electricPriceArea;

    /** 电价跨度字段 */
    @ApiModelProperty("电价跨度")
    private String electricPriceSpan;
}
