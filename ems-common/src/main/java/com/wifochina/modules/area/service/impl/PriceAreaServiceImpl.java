package com.wifochina.modules.area.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.mapper.PriceAreaMapper;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.area.vo.PriceAreaVo;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Service
public class PriceAreaServiceImpl extends ServiceImpl<PriceAreaMapper, PriceAreaEntity>
        implements PriceAreaService {

    @Resource private ProjectExtService projectExtService;

    @Override
    public IPage<PriceAreaVo> queryPriceArea(IPage<PriceAreaVo> priceAreaPage) {
        return this.baseMapper.queryPriceArea(priceAreaPage);
    }

    @Override
    public PriceAreaEntity queryProxyForDynamic(ProjectEntity projectEntity) {
        if (projectEntity.getPriceProxy()) {
            ProjectExtEntity projectExtEntity = projectExtService.getById(projectEntity.getId());
            if (projectExtEntity != null) {
                String area = projectExtEntity.getArea();
                return this.baseMapper.selectOne(
                        new LambdaQueryWrapper<PriceAreaEntity>().eq(PriceAreaEntity::getId, area));
            }
        }
        return null;
    }
}
