package com.wifochina.modules.configuration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wifochina.modules.configuration.entity.ConfigurationEntity;

import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface ConfigurationMapper extends BaseMapper<ConfigurationEntity> {

    List<ConfigurationEntity> listConfiguration(@Param("projectId") String projectId,
                                                @Param("isReleased") Boolean isReleased);

}
