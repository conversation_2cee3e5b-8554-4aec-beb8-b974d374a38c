package com.wifochina.modules.configuration.controller;

import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.configuration.entity.ConfigurationEntity;
import com.wifochina.modules.configuration.request.ConfigurationRequest;
import com.wifochina.modules.configuration.service.ConfigurationService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.ConfigurationLogDetailService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/16 17:11
 * @version 1.0
 */
@Slf4j
@RestController
@Api(tags = "32-项目配置")
@RequestMapping("/configuration")
public class ConfigurationController {

    @Resource private ConfigurationService configurationService;

    @GetMapping("/list")
    @ApiOperation("查询列表")
    @PreAuthorize("hasAuthority('/configuration/list')")
    public Result<List<ConfigurationEntity>> list(
            @RequestParam(required = false) Boolean isReleased) {
        List<ConfigurationEntity> list = configurationService.listConfiguration(isReleased);
        return Result.success(list);
    }

    @PostMapping("/add")
    @ApiOperation("增加项目配置")
    @PreAuthorize("hasAuthority('/configuration/add')")
    @Log(module = "CONFIGURATION", type = OperationType.ADD)
    public Result<Void> add(@RequestBody ConfigurationRequest request) {
        configurationService.addConfiguration(request);
        return Result.success();
    }

    @PutMapping("/update")
    @ApiOperation("更新项目配置")
    @PreAuthorize("hasAuthority('/configuration/update')")
    @Log(module = "CONFIGURATION", type = OperationType.UPDATE)
    public Result<Void> update(@RequestBody ConfigurationRequest request) {
        configurationService.updateConfiguration(request);
        return Result.success();
    }

    @PutMapping("/order")
    @ApiOperation("排序")
    @PreAuthorize("hasAuthority('/configuration/order')")
    public Result<Void> update(@RequestBody List<Integer> ids) {
        configurationService.orderConfiguration(ids);
        return Result.success();
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除项目配置")
    @PreAuthorize("hasAuthority('/configuration/delete')")
    @Log(
            module = "CONFIGURATION",
            type = OperationType.DEL,
            logDetailServiceClass = ConfigurationLogDetailService.class)
    public Result<Void> delete(@PathVariable("id") Integer id) {
        configurationService.removeById(id);
        return Result.success();
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("查询详情")
    public Result<ConfigurationEntity> detail(
            @PathVariable("id") Integer id) {
        ConfigurationEntity res = configurationService.detail(id);
        return Result.success(res);
    }
}
