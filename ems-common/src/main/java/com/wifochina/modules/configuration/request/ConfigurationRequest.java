package com.wifochina.modules.configuration.request;

import com.wifochina.common.log.LogFieldIgnore;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
public class ConfigurationRequest {

    private Integer id;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "密码")
    @LogFieldIgnore
    private String password;

    @ApiModelProperty(value = "配置路径")
    private String configUrl;

    @ApiModelProperty(value = "生成类型")
    private Integer generationType;

    @ApiModelProperty(value = "绘图脚本")
    @LogFieldIgnore
    private String graphScript;

    @ApiModelProperty(value = "是否发布")
    private Boolean isReleased;

    @ApiModelProperty(value = "索引")
    private Integer indexOrder;
}
