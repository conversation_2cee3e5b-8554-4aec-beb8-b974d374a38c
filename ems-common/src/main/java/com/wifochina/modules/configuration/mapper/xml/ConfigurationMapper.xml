<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.configuration.mapper.ConfigurationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wifochina.modules.configuration.entity.ConfigurationEntity">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="password" property="password"/>
        <result column="config_url" property="configUrl"/>
        <result column="generation_type" property="generationType"/>
        <result column="is_released" property="isReleased"/>
        <result column="index_order" property="indexOrder"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="listConfiguration" resultMap="BaseResultMap">
        select id, project_id, name, type, password, config_url, generation_type, is_released, index_order, create_time
        from t_configuration
        where project_id = #{projectId}
        <if test="isReleased != null and '' != isReleased">
            and is_released = #{isReleased}
        </if>
    </select>

</mapper>
