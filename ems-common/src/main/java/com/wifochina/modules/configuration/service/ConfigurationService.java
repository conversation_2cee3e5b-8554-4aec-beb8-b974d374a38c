package com.wifochina.modules.configuration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.configuration.entity.ConfigurationEntity;
import com.wifochina.modules.configuration.request.ConfigurationRequest;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface ConfigurationService extends IService<ConfigurationEntity> {

    List<ConfigurationEntity> listConfiguration(Boolean isReleased);

    void addConfiguration(ConfigurationRequest request);

    void updateConfiguration(ConfigurationRequest request);

    void orderConfiguration(List<Integer> ids);

    ConfigurationEntity detail(Integer id);

    void initProjectConfiguration(Boolean isCn,String projectId);
}
