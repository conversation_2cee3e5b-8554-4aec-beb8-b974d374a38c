package com.wifochina.modules.configuration.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/17 11:08
 * @version 1.0
 */
@Getter
public enum ConfigurationTypeEnum {
    AGC_AVC(2),
    WIRING_DIAGRAM(3),
    LOAD_REGULATION(4),
    EMS_CONTROL(5),
    WHOLE_STATION(0),
    STAND_ALONE(1);

    final int code;

    ConfigurationTypeEnum(int code) {
        this.code = code;
    }

    public static ConfigurationTypeEnum getEnumByCode(int code) {
        for (ConfigurationTypeEnum value : ConfigurationTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
