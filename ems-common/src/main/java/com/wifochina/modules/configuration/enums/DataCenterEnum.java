package com.wifochina.modules.configuration.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/17 11:08
 * @version 1.0
 */
@Getter
public enum DataCenterEnum {
    EU("1"),
    AU("2"),
    CN("3");

    final String code;

    DataCenterEnum(String code) {
        this.code = code;
    }

    public static DataCenterEnum getEnumByCode(String code) {
        for (DataCenterEnum value : DataCenterEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isCN(String code) {
        return DataCenterEnum.CN.getCode().equals(code);
    }
}
