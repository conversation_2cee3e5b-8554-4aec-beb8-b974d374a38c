package com.wifochina.modules.configuration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 项目配置
 *
 * <AUTHOR>
 * @date 2025/4/16 15:34
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("t_configuration")
@ApiModel(value = "ConfigurationEntity对象", description = "ConfigurationEntity对象")
public class ConfigurationEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "配置路径")
    private String configUrl;

    @ApiModelProperty(value = "生成类型")
    private Integer generationType;

    @ApiModelProperty(value = "绘图脚本")
    private String graphScript;

    @ApiModelProperty(value = "是否发布")
    private Boolean isReleased;

    @ApiModelProperty(value = "索引")
    private Integer indexOrder;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
