package com.wifochina.modules.notice.utils.data.common;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2023/9/27 18:47.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CommonShow {
    /** 是否 显示 储能充放电开关 */
    private boolean showEnergyStorage = false;

    /** 是否 显示 需量控制 开关 */
    private boolean showDemandControl = false;

    /** 是否 显示 PV发电 开关 */
    private boolean showPvPower = false;

    /** 是否 显示 风力发电 开关 */
    private boolean showWindPower = false;

    private boolean showWasterPower = false;

    private String year;
    private String month;
}
