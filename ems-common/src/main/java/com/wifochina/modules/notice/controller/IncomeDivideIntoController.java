package com.wifochina.modules.notice.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.notice.entity.IncomeDivideIntoEntity;
import com.wifochina.modules.notice.request.IncomeDivideIntoRequest;
import com.wifochina.modules.notice.service.IncomeDivideIntoService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Created on 2024/6/18 15:52.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "收益分成管理")
@RequestMapping("/incomeDivideInto")
@AllArgsConstructor
public class IncomeDivideIntoController {

    private final IncomeDivideIntoService incomeDivideIntoService;

    @PostMapping("/update")
    @ApiOperation("更新收益分成")
    @PreAuthorize("hasAuthority('/notice/incomeDivideInto/add')")
    @Log(module = "INCOME_DIVIDE", methods = "INCOME_DIVIDE_UPDATE", type = OperationType.UPDATE_SIMPLE)
    public Result<Void> update(@RequestBody IncomeDivideIntoRequest incomeDivideIntoRequest) {
        String projectId = WebUtils.projectId.get();
        IncomeDivideIntoEntity old =
                incomeDivideIntoService.getOne(
                        new LambdaQueryWrapper<IncomeDivideIntoEntity>()
                                .eq(IncomeDivideIntoEntity::getProjectId, projectId));
        if (old != null) {
            // 更新操作
            BeanUtils.copyProperties(incomeDivideIntoRequest, old);
            incomeDivideIntoService.saveOrUpdate(old);
        }
        return Result.success();
    }

    @PostMapping("/add")
    @ApiOperation("增加和更新收益分成")
    @PreAuthorize("hasAuthority('/notice/incomeDivideInto/add')")
    @Log(module = "INCOME_DIVIDE", type = OperationType.ADD)
    public Result<Void> add(@RequestBody IncomeDivideIntoRequest incomeDivideIntoRequest) {
        String projectId = WebUtils.projectId.get();
        // 新增操作
        IncomeDivideIntoEntity incomeDivideIntoEntity = new IncomeDivideIntoEntity();
        BeanUtils.copyProperties(incomeDivideIntoRequest, incomeDivideIntoEntity);
        incomeDivideIntoEntity.setProjectId(projectId);
        incomeDivideIntoService.save(incomeDivideIntoEntity);
        return Result.success();
    }

    @GetMapping("/detail")
    @ApiOperation("查询当前项目的收益分成")
    public Result<IncomeDivideIntoEntity> detail() {
        String projectId = WebUtils.projectId.get();
        IncomeDivideIntoEntity old =
                incomeDivideIntoService.getOne(
                        new LambdaQueryWrapper<IncomeDivideIntoEntity>()
                                .eq(IncomeDivideIntoEntity::getProjectId, projectId));
        return Result.success(old);
    }
}
