package com.wifochina.modules.notice.controller;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.notice.request.RecoverRequest;
import com.wifochina.modules.notice.service.NoticeReportService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023-09-27 7:04 PM
 */
@Slf4j
@RestController
@Api(tags = "26-每日账单通知")
@RequestMapping("/notice")
public class NoticeReportController {

    @Resource private NoticeReportService noticeReportService;

    @Resource private ProjectService projectService;

    @PostMapping("/testMonth")
    @ApiOperation("月度测试通知")
    public Result<Object> testMonth(@RequestBody RecoverRequest recoverRequest) {
        if (recoverRequest.getProjectId().equals(EmsConstants.ALL)) {
            List<ProjectEntity> projectEntities =
                    projectService
                            .lambdaQuery()
                            .eq(ProjectEntity::getWhetherDelete, false)
                            .ne(ProjectEntity::getProjectModel, 1)
                            .list();
//            projectEntities =
//                    projectEntities.stream()
//                            .filter(
//                                    project ->
//                                            project.getId()
//                                                    .equals("4f537620d37d40e19dd25be5ca6ad941"))
//                            .collect(Collectors.toList());
            for (ProjectEntity projectEntity : projectEntities) {
                noticeReportService.noticeSendMonth(
                        projectEntity.getId(), recoverRequest.getStart());
            }
        } else {
            noticeReportService.noticeSendMonth(
                    recoverRequest.getProjectId(), recoverRequest.getStart());
        }
        return Result.success();
    }

    @PostMapping("/testYear")
    @ApiOperation("年度测试通知")
    public Result<Object> testYear(@RequestBody RecoverRequest recoverRequest) {
        if (recoverRequest.getProjectId().equals(EmsConstants.ALL)) {
            List<ProjectEntity> projectEntities =
                    projectService
                            .lambdaQuery()
                            .eq(ProjectEntity::getWhetherDelete, false)
                            .ne(ProjectEntity::getProjectModel, 1)
                            .list();
            for (ProjectEntity projectEntity : projectEntities) {
                noticeReportService.noticeSendYear(
                        projectEntity.getId(), recoverRequest.getStart());
            }
        } else {
            noticeReportService.noticeSendYear(
                    recoverRequest.getProjectId(), recoverRequest.getStart());
        }
        return Result.success();
    }
}
