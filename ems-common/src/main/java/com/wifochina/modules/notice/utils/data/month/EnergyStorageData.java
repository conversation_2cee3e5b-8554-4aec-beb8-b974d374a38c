package com.wifochina.modules.notice.utils.data.month;

import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2023/9/19 18:32.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class EnergyStorageData {
    /** 计表类型 */
    @NotNull
    private String meterType;
    /** 时段 */
    @NotNull
    private String meterTime;
    /** 上月示数 */
    private double preMonthDisplay;
    /** 本月示数 */
    private double nowMonthDisplay;
    /** 上月电量 */
    private double preMonthMeter;
    /** 电价 */
    private double meterPrice;
    /** 结算金额 */
    private double settleAmount;

    public final String getData(int index) {
        if (index == 1) {
            return this.getMeterType();
        }
        if (index == 2) {
            return this.getMeterTime();
        }
        if (index == 3) {
            return String.valueOf(this.getPreMonthDisplay());
        }
        if (index == 4) {
            return String.valueOf(this.getNowMonthDisplay());
        }
        if (index == 5) {
            return String.valueOf(this.getPreMonthMeter());
        }
        if (index == 6) {
            return String.valueOf(this.getMeterPrice());
        }
        if (index == 7) {
            return String.valueOf(this.getSettleAmount());
        }
        return null;
    }
}
