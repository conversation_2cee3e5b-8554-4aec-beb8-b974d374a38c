package com.wifochina.modules.notice.utils.data.year;

import com.wifochina.modules.notice.entity.IncomeDivideIntoEntity;
import com.wifochina.modules.notice.utils.data.common.CommonShow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Created on 2023/9/20 17:46. 年度账单的数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class YearBillReportData extends CommonShow {
    private boolean isCollect;

    private String titleName;

    private String year;

    private String projectId;

    /** 支持 分组的 也兼容不分组的 */
    // private LinkedList<YearBillGroupReportData> yearBillGroupReportDatas;
    /** 年度账单中 列表数据 */
    private String sheetName;

    private List<YearBillReport> yearBillReports;

    private IncomeDivideIntoEntity incomeDivideIntoEntity;
}
