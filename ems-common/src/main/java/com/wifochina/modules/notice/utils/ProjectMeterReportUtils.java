package com.wifochina.modules.notice.utils;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.RenewableTypeReportEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.notice.entity.IncomeDivideIntoEntity;
import com.wifochina.modules.notice.utils.data.common.PvPowerData;
import com.wifochina.modules.notice.utils.data.common.ReportListLineData;
import com.wifochina.modules.notice.utils.data.common.WindPowerData;
import com.wifochina.modules.notice.utils.data.month.*;
import com.wifochina.modules.notice.utils.data.year.YearBillReport;
import com.wifochina.modules.notice.utils.data.year.YearBillReportData;
import com.wifochina.modules.operation.VO.GroupDemandProfit;

import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

/**
 * Created on 2023/9/20 02:51.
 *
 * <AUTHOR>
 */
@Slf4j
public class ProjectMeterReportUtils {

    /** 储能充放电 子表头 */
    private static final String[] ENERGY_STORAGE_SUB_HEAD_ARR = {
        "", "计表类型", "时段", "上月示数(kWh)", "本月示数(kWh)", "本月电量(kWh)", "电价(元/kWh)", "结算金额(元)"
    };

    private static final String[] DEMAND_CONTROL_SUB_HEAD_ARR = {
        "需量控制", "需量收益", "降低需量功率(kW)", "", "需量电价(元/kW)", "", "结算金额(元)", ""
    };

    private static final String[] DETAIL_HEADER_ARR = {
        "时间", "尖峰放电(度)", "峰段放电(度)", "平段放电(度)", "谷段放电(度)", "深谷放电(度)", "尖峰充电(度)", "峰段充电(度)",
        "平段充电(度)", "谷段充电(度)", "深谷充电(度)", "总充电(度)", "总放电(度)", "系统效率", "PV发电(度)", "风力发电(度)", "余热发电(度)"
    };

    private static final String[] INCOME_ARR = {
        "月度储能充放电总收益(元)",
        "客户方收益分享部分占比" + EmsConstants.SAMPLE_PLACEHOLDER + "%(元)",
        "资方储能充放电收益(元)",
        "月度需量控制总收益(元)",
        "月度光伏发电总收益(元)",
        "月度风力发电总收益(元)",
        "月度余热发电总收益(元)",
    };

    // TODO Try refactoring fail .. who like who do it, why it is always me
    //    private static final String[] YEAR_HEADER_01 = {"时间", "收益"};
    //    private static final String[] YEAR_INCOME_DIVIDE_HEADER_02 = {
    //        "客户方收益分享部分占比 " + EmsConstants.SAMPLE_PLACEHOLDER + "%(元)", "资方储能充放电总收益(元)",
    //    };
    //    private static final String[] YEAR_ENERGY_STORAGE_HEADER_03 = {
    //        "尖峰放电(度)",
    //        "尖峰电价(元/kWh)",
    //        "结算金额(元)",
    //        "峰段放电(度)",
    //        "峰段电价(元/kWh)",
    //        "结算金额(度)",
    //        "平段放电(度)",
    //        "平段电价(元/kWh)",
    //        "结算金额(元)",
    //        "谷段放电(度)",
    //        "谷段电价(元/kWh)",
    //        "结算金额(元)",
    //        "深谷放电(度)",
    //        "深谷电价(元/kWh)",
    //        "结算金额(元)",
    //        "尖峰充电(度)",
    //        "尖峰电价(元/kWh)",
    //        "结算金额(元)",
    //        "峰段充电(度)",
    //        "峰段电价(元/kWh)",
    //        "结算金额(元)",
    //        "平段充电(度)",
    //        "平段电价(元/kWh)",
    //        "结算金额(元)",
    //        "谷段充电(度)",
    //        "谷段电价(元/kWh)",
    //        "结算金额(元)",
    //        "深谷充电(度)",
    //        "深谷电价(元/kWh)",
    //        "结算金额(元)",
    //        "总充电(度)",
    //        "总放电(度)",
    //        "系统效率",
    //    };
    //
    //    private static final String[] YEAR_HEADER_02 = {
    //        "时间", "收益", "客户方收益分享部分占比 " + EmsConstants.SAMPLE_PLACEHOLDER + "%(元)",
    // "资方储能充放电总收益(元)",
    //    };

    /** 有 资方占比的2个列的情况 下电价相关的 列index */
    private static final Set<Integer> IGNORE_COLLECT_PRICE_ARR =
            Set.of(5, 8, 11, 14, 17, 20, 23, 26, 29, 32);

    /** 无 资方占比的2个列的情况 下电价相关的 列index */
    private static final Set<Integer> IGNORE_COLLECT_PRICE_ARR2 =
            Set.of(3, 6, 9, 12, 15, 18, 21, 24, 27, 30);

    private static final String[] YEAR_HEADER_ARR = {
        "时间",
        "收益",
        "客户方收益分享部分占比 " + EmsConstants.SAMPLE_PLACEHOLDER + "%(元)",
        "资方储能充放电总收益(元)",
        "尖峰放电(度)",
        "尖峰电价(元/kWh)",
        "结算金额(元)",
        "峰段放电(度)",
        "峰段电价(元/kWh)",
        "结算金额(度)",
        "平段放电(度)",
        "平段电价(元/kWh)",
        "结算金额(元)",
        "谷段放电(度)",
        "谷段电价(元/kWh)",
        "结算金额(元)",
        "深谷放电(度)",
        "深谷电价(元/kWh)",
        "结算金额(元)",
        "尖峰充电(度)",
        "尖峰电价(元/kWh)",
        "结算金额(元)",
        "峰段充电(度)",
        "峰段电价(元/kWh)",
        "结算金额(元)",
        "平段充电(度)",
        "平段电价(元/kWh)",
        "结算金额(元)",
        "谷段充电(度)",
        "谷段电价(元/kWh)",
        "结算金额(元)",
        "深谷充电(度)",
        "深谷电价(元/kWh)",
        "结算金额(元)",
        "总充电(度)",
        "总放电(度)",
        "系统效率",
        "PV发电(度)",
        "结算金额(元)",
        "风力发电(度)",
        "结算金额(元)",
        "余热发电(度)",
        "结算金额(元)",
    };
    // 对应 "系统效率", 的 index
    private static final int YEAR_FORMULA_COL_INDEX = 34;
    private static final int MONTH_FORMULA_COL_INDEX = 13;

    private static final String DEMAND_CONTROL_TEXT = "需量控制";
    private static final String RENEWABLE_ENERGY_TEXT = "可再生能源";
    private static final String WIN_TEXT = "收益计算";
    private static final String REMARK_TEXT = "备注";
    private static final int YEAR_LAST_COL_INDEX = 38;
    private static final int MONTH_LAST_COL_INDEX = 16;

    private static final int monthLastCol = 7;
    private static final int monthFirstCol = 1;

    public static void main(String[] args) {
        // 月份报表
        // writeMeterMonthReport(
        // initMonBillData(),
        // (DetailReportData)
        // new DetailReportData()
        // .setTitleName("无锡为恒500kW/1MWh储能系统(测试60)项目账单")
        // .setDetailReports(getDetailReports())
        // .setShowEnergyStorage(true)
        // .setShowPvPower(true)
        // .setShowWindPower(true));
        // 年份报表
        // writeMeterYearReport(initYearBillData());
    }

    public static List<RenewableTypeReportEnum> getRenewableTypeReportEnumByEnable(
            GroupEntity systemGroupEntity) {
        boolean pvFlag =
                Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())
                        && Boolean.TRUE.equals(systemGroupEntity.getPvProfitController());
        boolean windFlag =
                Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())
                        && Boolean.TRUE.equals(systemGroupEntity.getWindEarningsController());
        boolean wasterFlag =
                Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration())
                        && Boolean.TRUE.equals(systemGroupEntity.getWasterEarningsController());
        // 根据不同的开关去
        List<RenewableTypeReportEnum> renewableTypeReportEnums = new ArrayList<>();
        if (pvFlag) {
            renewableTypeReportEnums.add(RenewableTypeReportEnum.PV);
        }
        if (windFlag) {
            renewableTypeReportEnums.add(RenewableTypeReportEnum.WIND);
        }
        if (wasterFlag) {
            renewableTypeReportEnums.add(RenewableTypeReportEnum.WASTER);
        }
        return renewableTypeReportEnums;
    }

    public static List<DetailReport> getDetailReports() {
        List<DetailReport> detailReports = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            DetailReport detailReport = new DetailReport();
            detailReport.setFlatCharge("18");
            detailReport.setValleyCharge("10");
            detailReport.setTime("7月10");
            detailReport.setPvDischarge("111");
            detailReport.setWindDischarge("222");
            detailReport.setDeepValleyDischarge("20");
            detailReport.setDeepValleyCharge("50");
            detailReports.add(detailReport);
        }
        return detailReports;
    }

    public static YearBillReportData initYearBillData() {

        List<YearBillReport> yearBillReports = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            YearBillReport yearBillReport = new YearBillReport();
            yearBillReport.setFlatCharge("100");
            yearBillReport.setTotalCharge("33315");
            yearBillReport.setTotalDischarge("28881");
            yearBillReport.setPvPower("150");
            yearBillReport.setWindPower("130");
            yearBillReport.setWin("1000");
            // yearBillReport.setDownDemandPower("100");
            yearBillReport.setTime(
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

            // 深谷 放电
            yearBillReport.setDeepValleyDischarge("20");
            yearBillReport.setDeepValleyDischargePrice("1.3");
            yearBillReport.setDeepValleyDischargeAmount("40");
            // 深谷 充电
            yearBillReport.setDeepValleyCharge("50");
            yearBillReport.setDeepValleyChargePrice("1.2");
            yearBillReport.setDeepValleyChargeAmount("50");
            yearBillReports.add(yearBillReport);
        }
        return (YearBillReportData)
                new YearBillReportData()
                        .setYear("2023")
                        .setTitleName("无锡为恒500kW/1MWh储能系统(测试60)项目")
                        .setYearBillReports(yearBillReports)
                        .setShowEnergyStorage(true)
                        .setShowPvPower(true)
                        .setShowWindPower(true)
                        .setShowDemandControl(true);
    }

    /**
     * 模拟初始化数据
     *
     * @return : billReportData
     */
    public static MonthBillReportData initMonBillData() {
        MonthBillReportData monthBillReportData = new MonthBillReportData();
        monthBillReportData.setProjectName("无锡为恒500kW/1MWh储能系统(测试60)项目");
        monthBillReportData.setBillingMonth("7月1日-7月31日");
        monthBillReportData.setPreReadingMeterDate("新装或者2023/7/1");
        monthBillReportData.setNowReadingMeterDate("8月1日2023年");
        monthBillReportData.setYear("2023").setMonth("10");

        List<EnergyStorageData> energyStorageDataList = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            EnergyStorageData energyStorageData =
                    new EnergyStorageData()
                            .setPreMonthDisplay(0d)
                            .setNowMonthDisplay(16812d)
                            .setPreMonthMeter(16812d)
                            .setMeterPrice(1.2478d)
                            .setSettleAmount(20977.81d);
            if (i <= 5) {
                energyStorageData.setMeterType("出电");
            } else {
                energyStorageData.setMeterType("进电");
            }
            if (i == 1 || i == 6) {
                energyStorageData.setMeterTime("尖峰");
            } else if (i == 2 || i == 7) {
                energyStorageData.setMeterTime("峰段");
            } else if (i == 3 || i == 8) {
                energyStorageData.setMeterTime("平段");
            } else if (i == 4 || i == 9) {
                energyStorageData.setMeterTime("谷段");
            } else if (i == 5 || i == 10) {
                energyStorageData.setMeterTime("深谷");
            }
            energyStorageDataList.add(energyStorageData);
        }
        // 注释 则 模拟不存在 储能放电
        monthBillReportData.setShowEnergyStorage(true);
        monthBillReportData.setEnergyStorageDataList(energyStorageDataList);

        // 注释 则 模拟不存在 需量控制
        monthBillReportData.setShowDemandControl(true);
        monthBillReportData.setDemandControlData(
                new DemandControlData()
                        .setDownDemandPower("1")
                        .setDemandPrice("52")
                        .setDemandSettleAmount("100"));
        // 光伏收益, 注释则模拟不存在 光伏收益
        monthBillReportData.setShowPvPower(true);
        monthBillReportData.setPvPowerData(
                new PvPowerData().setPvPower("0").setPvPowerSettleAmount("0"));
        // 风电收益
        monthBillReportData.setShowWindPower(true);
        monthBillReportData.setWindPowerData(
                new WindPowerData().setWindPower("0").setWindPowerSettleAmount("0"));

        monthBillReportData.setMonTotalWin("29595.95");
        monthBillReportData.setDemandProfitMap(new HashMap<>());
        return monthBillReportData;
    }

    /**
     * 写 月度账单
     *
     * @return : File
     */
    public static File writeMeterMonthReport(
            MonthBillReportData monthBillReportData, DetailReportDatas detailReportDatas) {
        checkMonthData(monthBillReportData, detailReportDatas);
        File file = null;
        try (Workbook workbook = new XSSFWorkbook()) {
            // 第一个sheet 账单sheet
            if (monthBillReportData != null) {
                billSheet(monthBillReportData, workbook);
            }

            // 支持多个sheet detail for 分组
            if (detailReportDatas != null && detailReportDatas.getDetailReportDataMap() != null) {
                detailReportDatas
                        .getDetailReportDataMap()
                        .forEach(
                                (key, detailReportData) -> {
                                    // 如果都设置了不显示 就不生成detail Sheet
                                    // 1.4.2 support waster
                                    boolean showDetailSheet =
                                            detailReportData.isShowEnergyStorage()
                                                    || detailReportData.isShowPvPower()
                                                    || detailReportData.isShowWindPower()
                                                    || detailReportData.isShowWasterPower();
                                    // 第二个sheet 明细sheet
                                    if (showDetailSheet) {
                                        detailSheet(detailReportData, workbook);
                                    }
                                });
            }

            // 保存Excel文件
            file = new File("output.xlsx");
            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                workbook.write(outputStream);
                log.info("月度账单已经生成...");
            }
        } catch (IOException e) {
            log.error("writeMeterMonthReport error {}", e.getMessage());
        }
        return file;
    }

    private static void checkMonthData(
            MonthBillReportData monthBillReportData, DetailReportDatas detailReportDatas) {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        try (factory) {
            Validator validator = factory.getValidator();
            if (monthBillReportData != null) {
                Set<ConstraintViolation<MonthBillReportData>> violations =
                        validator.validate(monthBillReportData);
                // 打印校验结果
                if (!violations.isEmpty()) {
                    throw new javax.validation.ValidationException(violations.toString());
                }
                // 校验 储能充放电
                if (monthBillReportData.isShowEnergyStorage()) {
                    if (monthBillReportData.getGroupEnergyStorageDateMap() != null
                            && !monthBillReportData.getGroupEnergyStorageDateMap().isEmpty()) {
                        monthBillReportData
                                .getGroupEnergyStorageDateMap()
                                .forEach(
                                        (key, energyStorageDataList) -> {
                                            for (EnergyStorageData energyStorageData :
                                                    energyStorageDataList) {
                                                Set<ConstraintViolation<EnergyStorageData>>
                                                        reportViolations =
                                                                validator.validate(
                                                                        energyStorageData);
                                                if (!reportViolations.isEmpty()) {
                                                    for (ConstraintViolation<EnergyStorageData>
                                                            reportViolation : reportViolations) {
                                                        factory.close();
                                                        throw new javax.validation
                                                                .ValidationException(
                                                                reportViolation.toString());
                                                    }
                                                }
                                            }
                                        });
                    } else {

                        throw new ServiceException(
                                ErrorResultCode.ENERGY_STORAGE_DATA_NOT_EXIST.value(),
                                "设置了showEnergyStorage 则储能充放电数据groupEnergyStorageDataMap不能为空");
                    }
                    //                    if (monthBillReportData.getEnergyStorageDataList() !=
                    // null) {
                    //                        for (EnergyStorageData energyStorageData :
                    //
                    // monthBillReportData.getEnergyStorageDataList()) {
                    //                            Set<ConstraintViolation<EnergyStorageData>>
                    // reportViolations =
                    //                                    validator.validate(energyStorageData);
                    //                            if (!reportViolations.isEmpty()) {
                    //                                for (ConstraintViolation<EnergyStorageData>
                    // reportViolation :
                    //                                        reportViolations) {
                    //                                    factory.close();
                    //                                    throw new
                    // javax.validation.ValidationException(
                    //                                            reportViolation.toString());
                    //                                }
                    //                            }
                    //                        }
                    //                    } else {
                    //                        throw new ServiceException(
                    //
                    // ErrorResultCode.ENERGY_STORAGE_DATA_NOT_EXIST.value(),
                    //                                "设置了showEnergyStorage
                    // 则储能充放电数据energyStorageDataList不能为空");
                    //                    }
                }
            }

            if (monthBillReportData.isShowDemandControl()) {
                if (monthBillReportData.getDemandControlData() == null) {
                    throw new ServiceException(
                            ErrorResultCode.DEMAND_CONTROL_DATA_NOT_EXIST.value(),
                            "设置了showDemandControl 则需量控制数据demandControlData不能为空");
                }
            }
            if (monthBillReportData.isShowWindPower()) {
                if (monthBillReportData.getWindPowerData() == null) {
                    throw new ServiceException(
                            ErrorResultCode.WIND_POWER_DATA_NOT_EXIST.value(),
                            "设置了showWindPower 则风力发电数据windPowerData不能为空");
                }
            }
            if (monthBillReportData.isShowPvPower()) {
                if (monthBillReportData.getPvPowerData() == null) {
                    throw new ServiceException(
                            ErrorResultCode.PV_POWER_DATA_NOT_EXIST.value(),
                            "设置了showWindPower 则PV发电数据pvPowerData不能为空");
                }
            }
            if (detailReportDatas != null && detailReportDatas.getDetailReportDataMap() != null) {
                Map<String, DetailReportData> detailReportDataMap =
                        detailReportDatas.getDetailReportDataMap();
                detailReportDataMap.forEach(
                        (key, detailReportData) -> {
                            Set<ConstraintViolation<DetailReportData>> violations =
                                    validator.validate(detailReportData);
                            // 打印校验结果
                            if (!violations.isEmpty()) {
                                throw new javax.validation.ValidationException(
                                        violations.toString());
                            }
                        });
            }
        }

        //
    }

    /**
     * 写 年度账单
     *
     * @return : File
     */
    public static File writeMeterYearReport(List<YearBillReportData> yearBillReportDatas) {
        YearBillReportData collectYearBillReportData =
                yearBillReportDatas.stream()
                        .filter(YearBillReportData::isCollect)
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(""));
        List<YearBillReportData> groupYearBillReportDatas =
                yearBillReportDatas.stream()
                        .filter(yearBillReportData -> !yearBillReportData.isCollect())
                        .collect(Collectors.toList());
        // YearBillReportData yearBillReportData = (YearBillReportData) commonShow;
        File file = null;
        try (Workbook workbook = new XSSFWorkbook()) {
            // 如果都不show 则不生成 年度账单sheet
            boolean showYearBillSheet =
                    collectYearBillReportData.isShowEnergyStorage()
                            || collectYearBillReportData.isShowPvPower()
                            || collectYearBillReportData.isShowWindPower()
                            || collectYearBillReportData.isShowWasterPower()
                            || collectYearBillReportData.isShowDemandControl();
            if (showYearBillSheet) {
                // 第一个sheet 年度账单sheet
                yearBillSheet(collectYearBillReportData, workbook);
                if (!groupYearBillReportDatas.isEmpty()) {
                    groupYearBillReportDatas.forEach(
                            groupYearBillReportData -> {
                                yearBillSheet(groupYearBillReportData, workbook);
                            });
                }
            }
            file = new File("output.xlsx");
            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                workbook.write(outputStream);
                log.info("年度账单已经生成...");
            }
        } catch (IOException e) {
            log.error("writeMeterYearReport error {}", e.getMessage());
        }
        return file;
    }

    private static void yearBillSheet(YearBillReportData yearBillReportData, Workbook workbook) {
        // 控制是否显示
        Set<Integer> ignoreIndexForYear = getIgnoreIndexForYear(yearBillReportData);
        Sheet sheet = workbook.createSheet(yearBillReportData.getSheetName() + "年年度账单");
        defaultAnnualWidthHeight(sheet);
        CellStyle centerCellStyle = getCenterCellStyle(workbook);
        setBord(centerCellStyle);
        IncomeDivideIntoEntity incomeDivideIntoEntity =
                yearBillReportData.getIncomeDivideIntoEntity();
        yearTitleHead(
                yearBillReportData, workbook, sheet, incomeDivideIntoEntity, ignoreIndexForYear);
        DataFormat dataFormat = workbook.createDataFormat();
        short percentageFormat = dataFormat.getFormat("0.00%");
        //
        List<YearBillReport> yearBillReports = yearBillReportData.getYearBillReports();
        if (CollectionUtils.isEmpty(yearBillReports)) {
            return;
        }
        // index exclude first cloumn -- time
        int i = YEAR_HEADER_ARR.length - 1 - ignoreIndexForYear.size();
        // 直接拿这个控制一下, 如果当前这个用户没有开 控制需量展示这个开关 则直接控制住这个最大的列
        if (yearBillReportData.isShowDemandControl()) {
            int groupSize = getGroupSize(yearBillReportData);
            i = i + 2 * groupSize;
        }
        int formualIndex = YEAR_FORMULA_COL_INDEX;
        if (incomeDivideIntoEntity != null) {
            // 如果有这个收益分成 则需要 把这个 效率列 推后2个
            formualIndex = formualIndex + 2;
        }
        handlerDataLine(
                workbook,
                sheet,
                percentageFormat,
                i,
                // YEAR_FORMULA_COL_INDEX,
                formualIndex,
                yearBillReports,
                YearBillReport.class,
                ignoreIndexForYear,
                incomeDivideIntoEntity,
                yearBillReportData);
    }

    private static String[] getNewYearHeaderARR() {
        // 要插入的新元素
        String[] newElements = {"客户分成比例", "资方占比"};

        // 找到 "收益" 的索引
        int insertIndex = Arrays.asList(YEAR_HEADER_ARR).indexOf("收益") + 1;

        // 创建新的数组
        String[] newArray = new String[YEAR_HEADER_ARR.length + newElements.length];

        // 复制原数组前部分到新数组
        System.arraycopy(YEAR_HEADER_ARR, 0, newArray, 0, insertIndex);

        // 插入新元素
        System.arraycopy(newElements, 0, newArray, insertIndex, newElements.length);

        // 复制原数组后部分到新数组
        System.arraycopy(
                YEAR_HEADER_ARR,
                insertIndex,
                newArray,
                insertIndex + newElements.length,
                YEAR_HEADER_ARR.length - insertIndex);

        // 输出新数组
        System.out.println(Arrays.toString(newArray));
        return newArray;
    }

    public static int getGroupSize(YearBillReportData yearBillReportData) {
        int size = 0;
        for (int j = 0; j < yearBillReportData.getYearBillReports().size(); j++) {
            List<GroupDemandProfit> groupDemandProfits =
                    yearBillReportData.getYearBillReports().get(j).getDemandProfitList();
            if (!CollectionUtils.isEmpty(groupDemandProfits)) {
                size = Math.max(size, groupDemandProfits.size());
            }
        }
        return size;
    }

    public static int getIndexOfMaxGroupNum(YearBillReportData yearBillReportData) {
        int size = 0;
        int index = 0;
        for (int j = 0; j < yearBillReportData.getYearBillReports().size(); j++) {
            List<GroupDemandProfit> groupDemandProfits =
                    yearBillReportData.getYearBillReports().get(j).getDemandProfitList();
            if (!CollectionUtils.isEmpty(groupDemandProfits)) {
                if (groupDemandProfits.size() > size) {
                    size = groupDemandProfits.size();
                    index = j;
                }
            }
        }
        return index;
    }

    private static void handlerMonthDataLine(
            Workbook workbook,
            Sheet sheet,
            short percentageFormat,
            int lastColIndex,
            int formulaIndex,
            List<? extends ReportListLineData> dataList,
            Class<? extends ReportListLineData> clazz,
            Set<Integer> ignoreIndex) {
        Integer ignoreStartIndex = 0;
        if (!CollectionUtils.isEmpty(ignoreIndex)) {
            Integer[] array = ignoreIndex.toArray(new Integer[0]);
            // 获取到第一个 忽略的列 index
            ignoreStartIndex = array[0];
        }
        // 生成数据样式
        CellStyle rightCellStyle = workbook.createCellStyle();
        rightCellStyle.setAlignment(HorizontalAlignment.RIGHT);
        setBord(rightCellStyle);
        for (int i = 0; i < dataList.size(); i++) {
            ReportListLineData data = dataList.get(i);
            Row row = sheet.createRow(i + 2);
            row.setHeightInPoints((short) 30);
            List<GroupDemandProfit> groupDemandProfits;
            for (int j = 0; j <= lastColIndex; j++) {
                // // 跳过后面的
                Cell cell;
                // 处理一下 系统效率列
                if (j == formulaIndex) {
                    cell = row.createCell(j, CellType.FORMULA);
                    int nowLine = i + 3;
                    // 设置 计算公式
                    String formula =
                            getAlphabetByIndex(formulaIndex - 1)
                                    + nowLine
                                    + "/"
                                    + getAlphabetByIndex(formulaIndex - 2)
                                    + nowLine;
                    // 设置计算公式
                    cell.setCellFormula(formula);
                    // 这里为了不影响其他的cell, 因为这个要设置 percentageFormat 百分比
                    CellStyle centerCellStyle = workbook.createCellStyle();
                    setBord(centerCellStyle);
                    cell.setCellStyle(centerCellStyle);
                    cell.getCellStyle().setDataFormat(percentageFormat);
                } else {
                    cell = row.createCell(j);
                    cell.setCellStyle(rightCellStyle);
                    String result = null;
                    // 当列>第一个忽略的列时候 获取数据的时候需要加上 ignoreIndex.size()保证获取到的数据是对的
                    if (j >= ignoreStartIndex) {
                        result = data.getData(j + ignoreIndex.size(), clazz);
                    } else {
                        result = data.getData(j, clazz);
                    }
                    // 匹配整数或浮点数 为了让Excel计算公式起作用 不然String类型的无法生效
                    if (result != null && result.matches("-?\\d+(\\.\\d+)?")) {
                        cell.setCellValue(Double.parseDouble(result));
                    } else {
                        cell.setCellValue(result);
                    }
                    if (clazz == YearBillReport.class) {
                        if (j == YEAR_HEADER_ARR.length - 1 - ignoreIndex.size()) {
                            groupDemandProfits = data.getGroupDemandProfitList(41, clazz);
                            if (groupDemandProfits != null) {
                                int k = 0;
                                for (GroupDemandProfit groupDemandProfit : groupDemandProfits) {
                                    Cell cell0 = row.createCell(j + k + 1);
                                    cell0.setCellStyle(rightCellStyle);
                                    cell0.setCellValue(groupDemandProfit.getDemandControlPower());
                                    Cell cell1 = row.createCell(j + k + 2);
                                    cell1.setCellStyle(rightCellStyle);
                                    cell1.setCellValue(groupDemandProfit.getDemandControlBenefit());
                                    k = k + 2;
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }

        // 汇总行
        collectLastLine(
                workbook,
                sheet,
                dataList.size() + 3,
                lastColIndex,
                formulaIndex,
                percentageFormat,
                ignoreIndex,
                false);
    }

    private static void handlerDataLine(
            Workbook workbook,
            Sheet sheet,
            short percentageFormat,
            int lastColIndex,
            int formulaIndex,
            List<? extends ReportListLineData> dataList,
            Class<? extends ReportListLineData> clazz,
            Set<Integer> ignoreIndex,
            IncomeDivideIntoEntity incomeDivideIntoEntity,
            YearBillReportData yearBillReportData) {
        Integer ignoreStartIndex = 0;
        if (!CollectionUtils.isEmpty(ignoreIndex)) {
            Integer[] array = ignoreIndex.toArray(new Integer[0]);
            // 获取到第一个 忽略的列 index
            ignoreStartIndex = array[0];
        }
        // 生成数据样式
        CellStyle rightCellStyle = workbook.createCellStyle();
        rightCellStyle.setAlignment(HorizontalAlignment.RIGHT);
        setBord(rightCellStyle);
        for (int i = 0; i < dataList.size(); i++) {
            ReportListLineData data = dataList.get(i);
            Row row = sheet.createRow(i + 2);
            row.setHeightInPoints((short) 30);
            List<GroupDemandProfit> groupDemandProfits;
            for (int j = 0; j <= lastColIndex; j++) {
                // // 跳过后面的
                Cell cell;
                // 处理一下 系统效率列
                if (j == formulaIndex) {
                    cell = row.createCell(j, CellType.FORMULA);
                    int nowLine = i + 3;
                    // 设置 计算公式
                    String formula =
                            getAlphabetByIndex(formulaIndex - 1)
                                    + nowLine
                                    + "/"
                                    + getAlphabetByIndex(formulaIndex - 2)
                                    + nowLine;
                    // 设置计算公式
                    cell.setCellFormula(formula);
                    // 这里为了不影响其他的cell, 因为这个要设置 percentageFormat 百分比
                    CellStyle centerCellStyle = workbook.createCellStyle();
                    setBord(centerCellStyle);
                    cell.setCellStyle(centerCellStyle);
                    cell.getCellStyle().setDataFormat(percentageFormat);
                } else {
                    int index = j;
                    // 理解一下 就是 j < 2的时候 正常取, 如果是 忽略的index第一个是2的话 表示没有分成比例
                    String result;
                    // 当列>第一个忽略的列时候 获取数据的时候需要加上 ignoreIndex.size()保证获取到的数据是对的
                    if (j >= ignoreStartIndex) {
                        // 包括2表示 忽略分成比例 必须要分成比例 所以后续的 都加上2 即可获取到对应的数据
                        // 但是还有 pv 和 wind 的需要控制
                        // index = j;
                        if (index > formulaIndex) {
                            // 限制一下 从系统效率后 , 如果pv 没有开 则pv的2个都过滤掉
                            if ((index == formulaIndex - 1 || index == formulaIndex - 2)
                                    && !yearBillReportData.isShowPvPower()) {
                                index = index + 2;
                            }

                            // 限制一下 从系统效率后 , 如果wind 没有开 则wind的2个都过滤掉, 3 4 代表 系统效率后面的3 4个 , pv对应是前面2个
                            if ((index == formulaIndex - 3 || index == formulaIndex - 4)
                                    && !yearBillReportData.isShowWindPower()) {
                                index = index + 2;
                            }
                            // 限制一下 从系统效率后 , 如果wind 没有开 则wind的2个都过滤掉, 3 4 代表 系统效率后面的3 4个 , pv对应是前面2个
                            if ((index == formulaIndex - 5 || index == formulaIndex - 6)
                                    && !yearBillReportData.isShowWasterPower()) {
                                index = index + 2;
                            }
                        }
                        if (index >= 2 && incomeDivideIntoEntity == null) {
                            index = index + 2;
                        }
                        //                        result = data.getData(index, clazz);
                    }
                    int demandIndex = 43;
                    if (!yearBillReportData.isShowPvPower()) {
                        demandIndex = demandIndex - 2;
                    }
                    if (!yearBillReportData.isShowWindPower()) {
                        demandIndex = demandIndex - 2;
                    }
                    if (!yearBillReportData.isShowWasterPower()) {
                        demandIndex = demandIndex - 2;
                    }
                    // 特殊处理一下 需量的
                    if (demandIndex == index) {
                        if (clazz == YearBillReport.class) {
                            groupDemandProfits = data.getGroupDemandProfitList(43, clazz);
                            if (groupDemandProfits != null
                                    && yearBillReportData.isShowDemandControl()) {
                                int k = 0;
                                for (GroupDemandProfit groupDemandProfit : groupDemandProfits) {
                                    Cell cell0 = row.createCell(j + k + 0);
                                    cell0.setCellStyle(rightCellStyle);
                                    cell0.setCellValue(groupDemandProfit.getDemandControlPower());
                                    Cell cell1 = row.createCell(j + k + 1);
                                    cell1.setCellStyle(rightCellStyle);
                                    cell1.setCellValue(groupDemandProfit.getDemandControlBenefit());
                                    k = k + 2;
                                }
                                break;
                            }
                        }
                    } else {
                        cell = row.createCell(j);
                        cell.setCellStyle(rightCellStyle);
                        result = data.getData(index, clazz);
                        if (result != null && result.matches("-?\\d+(\\.\\d+)?")) {
                            cell.setCellValue(Double.parseDouble(result));
                        } else {
                            cell.setCellValue(result);
                        }
                    }
                }
            }
        }

        // 汇总行
        collectLastLine(
                workbook,
                sheet,
                dataList.size() + 3,
                lastColIndex,
                formulaIndex,
                percentageFormat,
                ignoreIndex,
                true);
    }

    private static void detailSheet(DetailReportData detailReportData, Workbook workbook) {
        Sheet sheet = workbook.createSheet(detailReportData.getDetailSheetName());
        defaultDetailWidthHeight(sheet);
        CellStyle centerCellStyle = getCenterCellStyle(workbook);
        setBord(centerCellStyle);

        Set<Integer> ignoreIndex = getIgnoreIndexForMonth(detailReportData);
        String titleName = detailReportData.getTitleName();
        createTitleAndHead(
                workbook, sheet, titleName, MONTH_LAST_COL_INDEX, DETAIL_HEADER_ARR, ignoreIndex);
        CellStyle rightCellStyle = workbook.createCellStyle();
        rightCellStyle.setAlignment(HorizontalAlignment.RIGHT);

        DataFormat dataFormat = workbook.createDataFormat();
        short percentageFormat = dataFormat.getFormat("0.00%");
        List<DetailReport> detailReports = detailReportData.getDetailReports();
        if (CollectionUtils.isEmpty(detailReports)) {
            // 无数据行
            return;
        }
        handlerMonthDataLine(
                workbook,
                sheet,
                percentageFormat,
                MONTH_LAST_COL_INDEX - ignoreIndex.size(),
                MONTH_FORMULA_COL_INDEX,
                detailReports,
                DetailReport.class,
                ignoreIndex);
    }

    private static Set<Integer> getIgnoreIndexForMonth(DetailReportData detailReportData) {
        Set<Integer> ignoreHead = new LinkedHashSet<>();
        if (!detailReportData.isShowEnergyStorage()) {
            for (int i = 1; i <= 13; i++) {
                ignoreHead.add(i);
            }
        }
        if (!detailReportData.isShowPvPower()) {
            ignoreHead.add(14);
        }
        if (!detailReportData.isShowWindPower()) {
            ignoreHead.add(15);
        }
        if (!detailReportData.isShowWasterPower()) {
            ignoreHead.add(16);
        }
        return ignoreHead;
    }

    private static Set<Integer> getIgnoreIndexForYear(YearBillReportData yearBillReportData) {
        Set<Integer> ignoreHead = new LinkedHashSet<>();
        IncomeDivideIntoEntity incomeDivideIntoEntity =
                yearBillReportData.getIncomeDivideIntoEntity();

        if (!yearBillReportData.isShowEnergyStorage()) {
            for (int i = 1; i <= 36; i++) {
                // 1列到28列 都要忽略
                ignoreHead.add(i);
            }
        } else {
            if (incomeDivideIntoEntity == null) {
                ignoreHead.add(2);
                ignoreHead.add(3);
            }
        }

        if (!yearBillReportData.isShowPvPower()) {
            ignoreHead.add(37);
            ignoreHead.add(38);
            //            if (incomeDivideIntoEntity == null) {
            //                // PV发电(度) + 结算金额(元)
            //                ignoreHead.add(35);
            //                ignoreHead.add(36);
            //            } else {
            //                // PV发电(度) + 结算金额(元)
            //                ignoreHead.add(35);
            //                ignoreHead.add(36);
            //            }
        }
        if (!yearBillReportData.isShowWindPower()) {
            //            ignoreHead.add(37);
            //            ignoreHead.add(38);
            ignoreHead.add(39);
            ignoreHead.add(40);
        }
        if (!yearBillReportData.isShowWasterPower()) {
            ignoreHead.add(41);
            ignoreHead.add(42);
        }
        return ignoreHead;
    }

    /**
     * 根据index 索引获取 对应的 26个英文字母 如 0 > A, 1 > B
     *
     * @param index : index
     * @return : 英文字母
     */
    private static String getAlphabetByIndex(int index) {
        if (index < 0) {
            throw new IllegalArgumentException("索引必须大于或等于0");
        }

        StringBuilder result = new StringBuilder();

        while (index >= 0) {
            int remainder = index % 26;
            result.insert(0, (char) ('A' + remainder));
            index = (index / 26) - 1;
        }

        return result.toString();
    }

    /**
     * 汇总最后一行数据
     *
     * @param workbook
     * @param sheet
     * @param lastLine
     * @param lastCol
     * @param formulaIndex
     * @param percentageFormat
     * @param ignoreIndex
     */
    private static void collectLastLine(
            Workbook workbook,
            Sheet sheet,
            int lastLine,
            int lastCol,
            int formulaIndex,
            short percentageFormat,
            Set<Integer> ignoreIndex,
            boolean yearReportFlag) {
        CellStyle centerCellStyle = getCellStyleForLastLine(workbook);
        // 最后一行 从0开始
        Row sumRow = sheet.createRow(lastLine - 1);
        for (int j = 0; j <= lastCol; j++) {
            Cell cell;
            if (j == 0) {
                cell = sumRow.createCell(j);
                cell.setCellValue("汇总");
                cell.setCellStyle(centerCellStyle);
                cell.getCellStyle().setAlignment(HorizontalAlignment.RIGHT);

            } else if (j == formulaIndex) {
                // 处理 系统效率
                cell = sumRow.createCell(j, CellType.FORMULA);
                String formula =
                        getAlphabetByIndex(formulaIndex - 1)
                                + lastLine
                                + "/"
                                + getAlphabetByIndex(formulaIndex - 2)
                                + lastLine;
                // 设置计算公式
                // String formula = "K" + lastLine + "/J" + lastLine;
                cell.setCellFormula(formula);
                // 这里为了不影响其他的cell, 因为这个要设置 percentageFormat 百分比
                CellStyle centerCellStyleNew = getCellStyleForLastLine(workbook);
                cell.setCellStyle(centerCellStyleNew);
                cell.getCellStyle().setDataFormat(percentageFormat);
            } else {
                // 1.4.2 过滤掉关于 price 价格的 列 但是只有在 year 年报的情况下 , 月报不需要 会导致异常
                if (yearReportFlag) {
                    if (ignoreIndex.iterator().hasNext()) {
                        int firstIgnoreIndex = ignoreIndex.iterator().next();
                        if (firstIgnoreIndex == 2) {
                            // 如果 客户收益占比和资方这个 忽略了 那么特出处理一下
                            if (IGNORE_COLLECT_PRICE_ARR2.contains(j)) {
                                continue;
                            }
                        } else {
                            if (IGNORE_COLLECT_PRICE_ARR.contains(j)) {
                                continue;
                            }
                        }
                    } else {
                        if (IGNORE_COLLECT_PRICE_ARR.contains(j)) {
                            continue;
                        }
                    }
                }

                cell = sumRow.createCell(j, CellType.FORMULA);
                int dataSize = lastLine - 1;
                String formula =
                        "SUM("
                                + getAlphabetByIndex(j)
                                + "3:"
                                + getAlphabetByIndex(j)
                                + dataSize
                                + ")";
                // 设置SUM函数来计算A列的合计
                cell.setCellFormula(formula);
                cell.setCellStyle(centerCellStyle);
            }
        }
    }

    @NotNull
    private static CellStyle getCellStyleForLastLine(Workbook workbook) {
        CellStyle centerCellStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 13);
        centerCellStyle.setFont(headerFont);
        return centerCellStyle;
    }

    private static void yearTitleHead(
            YearBillReportData yearBillReportData,
            Workbook workbook,
            Sheet sheet,
            IncomeDivideIntoEntity incomeDivideIntoEntity,
            Set<Integer> ignoreHead) {
        List<String> mytempHeartList = Arrays.asList(YEAR_HEADER_ARR);
        List<String> myHeartList = new ArrayList<>(mytempHeartList);
        int i = YEAR_HEADER_ARR.length - 1;
        if (incomeDivideIntoEntity != null) {
            myHeartList.set(
                    2,
                    myHeartList
                            .get(2)
                            .replace(
                                    EmsConstants.SAMPLE_PLACEHOLDER,
                                    String.valueOf(
                                            incomeDivideIntoEntity.getCustomerOccupancy() * 100)));
        } else {
            ignoreHead.add(2);
        }
        if (yearBillReportData.isShowDemandControl()) {
            int groupIndex = getIndexOfMaxGroupNum(yearBillReportData);
            if (!CollectionUtils.isEmpty(
                    yearBillReportData
                            .getYearBillReports()
                            .get(groupIndex)
                            .getDemandProfitList())) {
                for (int j = 0;
                        j
                                < yearBillReportData
                                        .getYearBillReports()
                                        .get(groupIndex)
                                        .getDemandProfitList()
                                        .size();
                        j++) {
                    GroupDemandProfit groupDemandProfit =
                            yearBillReportData
                                    .getYearBillReports()
                                    .get(groupIndex)
                                    .getDemandProfitList()
                                    .get(j);
                    String name = groupDemandProfit.getGroupName() + " 降低需量功率(kW)";
                    myHeartList.add(name);
                    myHeartList.add("结算金额(元)");
                    i = i + 2;
                }
            }
        }
        createTitleAndHead(
                workbook,
                sheet,
                yearBillReportData.getTitleName() + yearBillReportData.getYear() + "年度账单",
                i,
                myHeartList.toArray(new String[0]),
                ignoreHead);
    }

    public static String[] removeElements(String[] array, Set<Integer> indicesToRemove) {
        if (array == null || indicesToRemove == null || indicesToRemove.size() == 0) {
            return array; // 如果输入为空或没有要删除的索引，则返回原数组
        }
        Integer[] removeIndexArray = indicesToRemove.toArray(new Integer[0]);
        int[] intArray = new int[removeIndexArray.length];
        for (int i = 0; i < removeIndexArray.length; i++) {
            intArray[i] = removeIndexArray[i]; // 自动拆箱
        }
        // 创建一个新的数组，长度减去要删除的索引数量
        String[] newArray = new String[array.length - indicesToRemove.size()];

        int newIndex = 0; // 新数组的索引
        for (int i = 0; i < array.length; i++) {
            // 检查当前索引是否需要删除
            if (Arrays.binarySearch(intArray, i) < 0) {
                // 如果不需要删除，将元素复制到新数组中
                newArray[newIndex] = array[i];
                newIndex++;
            }
        }
        return newArray;
    }

    private static void createTitleAndHead(
            Workbook workbook,
            Sheet sheet,
            String titleName,
            int lastCol,
            String[] headArr,
            Set<Integer> ignoreHeadSet) {
        Row titleRow = sheet.createRow(0);
        CellRangeAddress rangeHeader;
        if (lastCol == ignoreHeadSet.size()) {
            return;
        }
        if (!CollectionUtils.isEmpty(ignoreHeadSet)) {
            // 把忽略的行给删除
            lastCol = lastCol - ignoreHeadSet.size();
            rangeHeader = new CellRangeAddress(0, 0, 0, lastCol);
            headArr = removeElements(headArr, ignoreHeadSet);
        } else {
            rangeHeader = new CellRangeAddress(0, 0, 0, lastCol);
        }
        sheet.addMergedRegion(rangeHeader);
        CellStyle titleCellStyle = workbook.createCellStyle();
        setBord(titleCellStyle);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 23);
        titleCellStyle.setFont(headerFont);
        // 设置水平居中对齐
        titleCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中对齐
        titleCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleRow.setHeightInPoints((short) 70);
        for (int i = 0; i <= lastCol; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellValue(titleName);
            // 设置表头样式（可选）
            cell.setCellStyle(titleCellStyle);
        }
        // 头生成
        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中对齐
        headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        setBord(headerCellStyle);
        Row headerRow = sheet.createRow(1);
        headerRow.setHeightInPoints((short) 40);
        for (int i = 0; i <= lastCol; i++) {
            // if (ignoreHeadSet != null && ignoreHeadSet.contains(i)) {
            // continue;
            // }
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headArr[i]);
            cell.setCellStyle(headerCellStyle);
        }
    }

    private static List<DetailReport> data() {
        List<DetailReport> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            DetailReport data = new DetailReport();
            data.setFlatDischarge("0");
            list.add(data);
        }
        return list;
    }

    private static void billSheet(MonthBillReportData monthBillReportData, Workbook workbook) {
        Sheet sheet = workbook.createSheet("账单");
        defaultBillWidthHeight(sheet);
        CellStyle centerCellStyle = getCenterCellStyle(workbook);
        setBord(centerCellStyle);
        // 设置表头
        setHeader(workbook, sheet, monthBillReportData);

        // 1.3.7 new add 设置 收益分成 相关单位信息
        setIncomeDivideInto(centerCellStyle, sheet, monthBillReportData);
        // 计费月份
        setBillingMonth(centerCellStyle, sheet, monthBillReportData);
        // 抄表日期
        setReadingMeter(centerCellStyle, sheet, monthBillReportData);
        // boolean existEnergyStorageFlag = energyStorage(monthBillReportData, sheet,
        // centerCellStyle);
        // 储能充放电
        energyStorage(monthBillReportData, sheet, centerCellStyle);
        // 需量控制
        demandControl(monthBillReportData, sheet, centerCellStyle);
        // 可再生能源
        renewableEnergy(monthBillReportData, sheet, centerCellStyle);
        int lastRowNum = sheet.getLastRowNum();
        log.info("lastRowNum :{}", lastRowNum);
        // 收益计算
        setWin(workbook, centerCellStyle, sheet, monthBillReportData);
        //        setWaster(workbook, centerCellStyle, sheet, monthBillReportData);

        // 备注
        setRemark(centerCellStyle, sheet);
    }

    private static void setWaster(
            Workbook workbook,
            CellStyle centerCellStyle,
            Sheet sheet,
            MonthBillReportData monthBillReportData) {

        boolean incomeShow = false;
        if (monthBillReportData.getIncomeDivideIntoEntity() != null) {
            incomeShow = true;
        }
        // 按照以前的 只显示 月度总收益的情况
        if (!incomeShow) {

            int startRowIndex = getLastNewRowNum(sheet);
            Row row = sheet.createRow(startRowIndex);
            Cell cell0 = row.createCell(0);
            cell0.setCellStyle(centerCellStyle);
            cell0.setCellValue("收益计算");

            for (int i = 1; i <= 4; i++) {
                Cell cell1 = row.createCell(i);
                cell1.setCellStyle(centerCellStyle);
                cell1.setCellValue("月度总收益(元)");
            }
            CellRangeAddress range = new CellRangeAddress(startRowIndex, startRowIndex, 1, 4);
            sheet.addMergedRegion(range);

            CellStyle newCellStyle = getCenterCellStyle(workbook);
            setBord(newCellStyle);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            newCellStyle.setFont(headerFont);
            for (int i = 5; i <= 7; i++) {
                Cell cell1 = row.createCell(i);
                cell1.setCellStyle(newCellStyle);
                cell1.setCellValue(monthBillReportData.getMonTotalWin());
            }
            CellRangeAddress range2 = new CellRangeAddress(startRowIndex, startRowIndex, 5, 7);
            sheet.addMergedRegion(range2);
        } else {
            List<Double> monIncomeList = monthBillReportData.getMonIncomeList();

            // 需要显示 客户方收益 资方收益 等
            for (int loop = 0; loop < 6; loop++) {
                int startRowIndex = getLastNewRowNum(sheet);
                Row row = sheet.createRow(startRowIndex);
                Cell cell0 = row.createCell(0);
                cell0.setCellStyle(centerCellStyle);
                cell0.setCellValue("收益计算");
                String title = INCOME_ARR[loop];
                if (loop == 1) {
                    // TODO 这里如果后期国外有 账单了 需要把 (元) 变成对应的 欧元
                    // 把 百分比给 填充到这个 title 上
                    title =
                            title.replace(
                                    EmsConstants.SAMPLE_PLACEHOLDER,
                                    String.valueOf(
                                            monthBillReportData
                                                            .getIncomeDivideIntoEntity()
                                                            .getCustomerOccupancy()
                                                    * 100));
                }
                for (int i = 1; i <= 4; i++) {
                    Cell cell1 = row.createCell(i);
                    cell1.setCellStyle(centerCellStyle);
                    cell1.setCellValue(title);
                }
                CellRangeAddress range = new CellRangeAddress(startRowIndex, startRowIndex, 1, 4);
                sheet.addMergedRegion(range);

                CellStyle newCellStyle = getCenterCellStyle(workbook);
                setBord(newCellStyle);
                Font headerFont = workbook.createFont();
                headerFont.setBold(true);
                newCellStyle.setFont(headerFont);
                for (int i = 5; i <= 7; i++) {
                    Cell cell1 = row.createCell(i);
                    cell1.setCellStyle(newCellStyle);
                    Double incomeValue = monIncomeList.get(loop);
                    // cell1.setCellValue(monthBillReportData.getMonTotalWin());
                    // 从 收益list 里面根据 index loop 获取到对应的 收益
                    cell1.setCellValue(incomeValue);
                }
                CellRangeAddress range2 = new CellRangeAddress(startRowIndex, startRowIndex, 5, 7);
                sheet.addMergedRegion(range2);
            }
        }
    }

    private static void renewableEnergy(
            MonthBillReportData monthBillReportData, Sheet sheet, CellStyle centerCellStyle) {
        boolean showPvPower = monthBillReportData.isShowPvPower();
        boolean showWindPower = monthBillReportData.isShowWindPower();
        boolean showWasterPower = monthBillReportData.isShowWasterPower();
        if (showPvPower || showWindPower || showWasterPower) {
            // 可再生能源
            setRenewableEnergy(centerCellStyle, sheet, monthBillReportData);
        }
        // else pv 和 wind都不展示 则直接不展示 可再生能源这一行
    }

    private static void demandControl(
            MonthBillReportData monthBillReportData, Sheet sheet, CellStyle centerCellStyle) {
        boolean showDemandControl = monthBillReportData.isShowDemandControl();
        // 是否存在 需量控制
        if (showDemandControl) {
            // 需量控制
            setDemandControl(centerCellStyle, sheet, monthBillReportData);
        }
    }

    private static void energyStorage(
            MonthBillReportData monthBillReportData, Sheet sheet, CellStyle centerCellStyle) {
        // 是否忽略 储能充放电
        boolean showEnergyStorage = monthBillReportData.isShowEnergyStorage();
        if (showEnergyStorage) {
            // 储能充放电
            setEnergyStorageChange(centerCellStyle, sheet, monthBillReportData);
        }
    }

    /**
     * 设置 默认的sheet 宽高
     *
     * @param sheet: sheet
     */
    private static void defaultBillWidthHeight(Sheet sheet) {
        int defaultColumnWidth = 18;
        sheet.setDefaultColumnWidth(defaultColumnWidth);
        short defaultRowHeight = 22;
        sheet.setDefaultRowHeightInPoints(defaultRowHeight);
    }

    private static void defaultDetailWidthHeight(Sheet sheet) {
        int defaultColumnWidth = 15;
        sheet.setDefaultColumnWidth(defaultColumnWidth);
    }

    private static void defaultAnnualWidthHeight(Sheet sheet) {
        int defaultColumnWidth = 17;
        sheet.setDefaultColumnWidth(defaultColumnWidth);
    }

    private static int getLastNewRowNum(Sheet sheet) {
        return sheet.getLastRowNum() + 1;
    }

    private static void setIncomeDivideInto(
            CellStyle cellStyle, Sheet sheet, MonthBillReportData monthBillReportData) {

        //        int lastCol = 7;
        //        int firstCol = 1;
        IncomeDivideIntoEntity incomeDivideIntoEntity =
                monthBillReportData.getIncomeDivideIntoEntity();
        if (incomeDivideIntoEntity == null) {
            return;
        }
        // int lastRowNum = sheet.getLastRowNum();
        int lastNewRowNum = getLastNewRowNum(sheet);
        CellRangeAddress range =
                new CellRangeAddress(lastNewRowNum, lastNewRowNum, monthFirstCol, monthLastCol);
        // 创建单元格并设置内容
        Row row = sheet.createRow(lastNewRowNum);
        Cell customerCell0 = row.createCell(0);
        customerCell0.setCellValue("客户单位");
        customerCell0.setCellStyle(cellStyle);
        for (int i = monthFirstCol; i <= monthLastCol; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(incomeDivideIntoEntity.getNameOfCustomer());
            cell.setCellStyle(cellStyle);
        }
        // 合并单元格
        sheet.addMergedRegion(range);

        CellRangeAddress range2 =
                new CellRangeAddress(
                        lastNewRowNum + 1, lastNewRowNum + 1, monthFirstCol, monthLastCol);
        // 创建单元格并设置内容
        Row row2 = sheet.createRow(lastNewRowNum + 1);
        Cell employerCell0 = row2.createCell(0);
        employerCell0.setCellValue("资方单位");
        employerCell0.setCellStyle(cellStyle);
        for (int i = monthFirstCol; i <= monthLastCol; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellValue(incomeDivideIntoEntity.getNameOfEmployer());
            cell.setCellStyle(cellStyle);
        }
        // 合并单元格
        sheet.addMergedRegion(range2);
    }

    private static void setBillingMonth(
            CellStyle cellStyle, Sheet sheet, MonthBillReportData monthBillReportData) {
        // int lastRowNum = sheet.getLastRowNum();
        int lastNewRowNum = getLastNewRowNum(sheet);
        // B2到H2
        CellRangeAddress range =
                new CellRangeAddress(lastNewRowNum, lastNewRowNum, monthFirstCol, monthLastCol);
        // 创建单元格并设置内容
        Row row = sheet.createRow(lastNewRowNum);
        Cell cell0 = row.createCell(0);
        cell0.setCellValue("计费月份");
        cell0.setCellStyle(cellStyle);
        for (int i = monthFirstCol; i <= monthLastCol; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(monthBillReportData.getBillingMonth());
            cell.setCellStyle(cellStyle);
        }
        // 合并单元格
        sheet.addMergedRegion(range);
    }

    private static void setReadingMeter(
            CellStyle cellStyle, Sheet sheet, MonthBillReportData monthBillReportData) {
        int lastNewRowNum = getLastNewRowNum(sheet);
        CellRangeAddress range1 = new CellRangeAddress(lastNewRowNum, lastNewRowNum, 1, 3);
        // 创建单元格并设置内容
        Row row = sheet.createRow(lastNewRowNum);
        Cell cell0 = row.createCell(0);
        cell0.setCellValue("上次抄表时间");
        cell0.setCellStyle(cellStyle);
        for (int i = 1; i <= 3; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(monthBillReportData.getPreReadingMeterDate());
            cell.setCellStyle(cellStyle);
        }
        // 合并单元格
        sheet.addMergedRegion(range1);

        // 本次抄表时间
        Cell cell4 = row.createCell(4);
        cell4.setCellValue("本次抄表时间");
        cell4.setCellStyle(cellStyle);
        CellRangeAddress range2 = new CellRangeAddress(lastNewRowNum, lastNewRowNum, 5, 7);
        for (int i = 5; i <= 7; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(monthBillReportData.getNowReadingMeterDate());
            cell.setCellStyle(cellStyle);
        }
        // 合并单元格
        sheet.addMergedRegion(range2);
    }

    private static void setEnergyStorageChange(
            CellStyle cellStyle, Sheet sheet, MonthBillReportData monthBillReportData) {
        // 第2行
        // int lastRowNum = sheet.getLastRowNum();
        int lastNewRowNum = getLastNewRowNum(sheet);
        Row row = sheet.createRow(lastNewRowNum);
        Cell cell0 = row.createCell(0);
        cell0.setCellValue("储能充放电");
        cell0.setCellStyle(cellStyle);
        Map<String, List<EnergyStorageData>> groupEnergyStorageDateMap =
                monthBillReportData.getGroupEnergyStorageDateMap();
        // 3
        int startRow = lastNewRowNum;
        // 默认的13 如果不分组的话
        int notGroupEndRow = startRow + 10;
        int endRow = notGroupEndRow;
        if (groupEnergyStorageDateMap != null && !groupEnergyStorageDateMap.isEmpty()) {
            // 首先要判断是否 需要按照分组展示 如果不需要就按照以前的 展示不动
            // 分组展示
            // 首先这个 13 要计算了 根据有几个非系统分组
            int groupSize = groupEnergyStorageDateMap.size();
            // 这里 第一个分组 有一个 头部 计表类型那一行 , 下面的分组没有那一行 都只占10行
            endRow += 10 * (groupSize - 1);
            CellRangeAddress range = new CellRangeAddress(startRow, endRow, 0, 0);
            sheet.addMergedRegion(range);
            // 设置头 计表类型 时段 上月示数(kWh) 本月示数(kWh) 本月电量(kWh) 电价(元/kWh) 结算金额(元)
            for (int i = monthFirstCol; i <= monthLastCol; i++) {
                Cell cell = row.createCell(i);
                cell.setCellValue(ENERGY_STORAGE_SUB_HEAD_ARR[i]);
                cell.setCellStyle(cellStyle);
            }

            int index = 0;
            int notHeaderStartRow = (startRow + 1);
            for (Map.Entry<String, List<EnergyStorageData>> entry :
                    groupEnergyStorageDateMap.entrySet()) {
                String groupId = entry.getKey();
                List<EnergyStorageData> energyStorageDataList = entry.getValue();
                // 处理 计表类型的 下一行开始
                int groupEndRow = notGroupEndRow + (index * 10);
                // startRow + 1 = 以前的4
                int startGroupRow = notHeaderStartRow + (index * 10);
                // 就是从4开始 除去第一行的头 后面都是每个分组10行
                for (int i = startGroupRow; i <= groupEndRow; i++) {
                    EnergyStorageData energyStorageData =
                            energyStorageDataList.get(i - notHeaderStartRow - (index * 10));
                    Row dynamicRow = sheet.createRow(i);
                    // 处理 每一行中的 每一列 注意是从 0 开始
                    for (int j = 0; j <= 7; j++) {
                        Cell cell = dynamicRow.createCell(j);
                        cell.setCellValue(energyStorageData.getData(j));
                        cell.setCellStyle(cellStyle);
                    }
                }
                index++;
            }
            // }
        }
    }

    private static void setDemandControl(
            CellStyle centerCellStyle, Sheet sheet, MonthBillReportData monthBillReportData) {
        int startRowIndex = getLastNewRowNum(sheet);
        // int startRowIndex = sheet.getLastRowNum() + 1;
        Row row = sheet.createRow(startRowIndex);
        int totalLine = startRowIndex - 1 + monthBillReportData.getDemandProfitMap().size();
        Cell cell0 = row.createCell(0);
        cell0.setCellValue(DEMAND_CONTROL_SUB_HEAD_ARR[0]);
        cell0.setCellStyle(centerCellStyle);
        Cell cell1 = row.createCell(1);
        cell1.setCellValue(DEMAND_CONTROL_SUB_HEAD_ARR[1]);
        cell1.setCellStyle(centerCellStyle);
        if (monthBillReportData.getDemandProfitMap().size() > 1) {
            // 需要合并
            CellRangeAddress range = new CellRangeAddress(startRowIndex, totalLine, 0, 0);
            sheet.addMergedRegion(range);
            CellRangeAddress range1 = new CellRangeAddress(startRowIndex, totalLine, 1, 1);
            sheet.addMergedRegion(range1);
        }
        int i = startRowIndex;
        int firstIndex = 0;
        for (String key : monthBillReportData.getDemandProfitMap().keySet()) {
            GroupDemandProfit groupDemandProfit = monthBillReportData.getDemandProfitMap().get(key);
            Row dynamicRow;
            if (firstIndex == 0) {
                // 第一行已经在上面 1093行创建过了 跳过这里
                dynamicRow = row;
            } else {
                i++;
                dynamicRow = sheet.createRow(i);
            }
            for (int j = 0; j <= 7; j++) {
                Cell cell = dynamicRow.createCell(j);
                if (j == 0) {
                    cell.setCellValue(DEMAND_CONTROL_SUB_HEAD_ARR[0]);
                } else if (j == 1) {
                    cell.setCellValue(DEMAND_CONTROL_SUB_HEAD_ARR[1]);
                } else {
                    cell.setCellValue(groupDemandProfit.getData(j));
                }
                cell.setCellStyle(centerCellStyle);
            }
            firstIndex++;
        }
    }

    private static void setRenewableEnergy(
            CellStyle centerCellStyle, Sheet sheet, MonthBillReportData monthBillReportData) {
        boolean showPvPower = monthBillReportData.isShowPvPower();
        boolean showWindPower = monthBillReportData.isShowWindPower();
        boolean showWasterPower = monthBillReportData.isShowWasterPower();
        List<RenewableTypeReportEnum> renewableTypeReportEnums = new ArrayList<>();
        if (showPvPower) {
            renewableTypeReportEnums.add(RenewableTypeReportEnum.PV);
        }
        if (showWindPower) {
            renewableTypeReportEnums.add(RenewableTypeReportEnum.WIND);
        }
        if (showWasterPower) {
            renewableTypeReportEnums.add(RenewableTypeReportEnum.WASTER);
        }
        renewable(
                centerCellStyle,
                sheet,
                monthBillReportData,
                getLastNewRowNum(sheet),
                renewableTypeReportEnums);
        // 1.4.2 refactor code use up renewable method replace
        //        if (showPvPower && showWindPower) {
        //            // 存在 光伏收益
        //            // 光伏和风电都展示
        //            pvWind(centerCellStyle, sheet, monthBillReportData, getLastNewRowNum(sheet));
        //        } else if (showPvPower) {
        //            // 只展示 光伏收益
        //            pv(centerCellStyle, sheet, monthBillReportData, getLastNewRowNum(sheet));
        //        } else if (showWindPower) {
        //            // 只展示 风电收益
        //            wind(centerCellStyle, sheet, monthBillReportData, getLastNewRowNum(sheet));
        //        }
    }

    private static void pvOrWind(
            CellStyle centerCellStyle,
            Sheet sheet,
            MonthBillReportData monthBillReportData,
            int startRowIndex,
            String pvOrWind) {

        for (int i = startRowIndex; i <= startRowIndex + 1; i++) {
            Row row = sheet.createRow(i);
            Cell cell0 = row.createCell(0);
            cell0.setCellStyle(centerCellStyle);
            cell0.setCellValue("可再生能源");

            Cell cell1 = row.createCell(1);
            cell1.setCellStyle(centerCellStyle);
            cell1.setCellValue(pvOrWind);

            Cell cell2 = row.createCell(2);
            cell2.setCellStyle(centerCellStyle);
            cell2.setCellValue("发电量(kWh)");
            Cell cell2Merge = row.createCell(3);
            cell2Merge.setCellStyle(centerCellStyle);

            Cell cell3 = row.createCell(4);
            cell3.setCellStyle(centerCellStyle);

            Cell cell4 = row.createCell(5);
            cell4.setCellStyle(centerCellStyle);
            cell4.setCellValue("结算金额(元)");
            Cell cell4Merge = row.createCell(6);
            cell4Merge.setCellStyle(centerCellStyle);

            Cell cell5 = row.createCell(7);
            cell5.setCellStyle(centerCellStyle);

            if ("光伏收益".equals(pvOrWind)) {
                cell3.setCellValue(monthBillReportData.getPvPowerData().getPvPower());
                cell5.setCellValue(monthBillReportData.getPvPowerData().getPvPowerSettleAmount());
            } else {
                cell3.setCellValue(monthBillReportData.getWindPowerData().getWindPower());
                cell5.setCellValue(
                        monthBillReportData.getWindPowerData().getWindPowerSettleAmount());
            }
        }
        // 合并 可再生能源
        CellRangeAddress range = new CellRangeAddress(startRowIndex, startRowIndex + 1, 0, 0);

        // 合并 风电收益
        CellRangeAddress range2 = new CellRangeAddress(startRowIndex, startRowIndex + 1, 1, 1);
        // 合并 发电量
        CellRangeAddress range3 = new CellRangeAddress(startRowIndex, startRowIndex + 1, 2, 3);
        // 合并 结算金额
        CellRangeAddress range4 = new CellRangeAddress(startRowIndex, startRowIndex + 1, 5, 6);

        CellRangeAddress range5 = new CellRangeAddress(startRowIndex, startRowIndex + 1, 4, 4);
        CellRangeAddress range6 = new CellRangeAddress(startRowIndex, startRowIndex + 1, 7, 7);

        sheet.addMergedRegion(range);
        sheet.addMergedRegion(range2);
        sheet.addMergedRegion(range3);
        sheet.addMergedRegion(range4);
        sheet.addMergedRegion(range5);
        sheet.addMergedRegion(range6);
    }

    private static void pv(
            CellStyle centerCellStyle,
            Sheet sheet,
            MonthBillReportData monthBillReportData,
            int startRowIndex) {

        // 显示 pv
        pvOrWind(centerCellStyle, sheet, monthBillReportData, startRowIndex, "光伏收益");
    }

    private static void wind(
            CellStyle centerCellStyle,
            Sheet sheet,
            MonthBillReportData monthBillReportData,
            int startRowIndex) {
        pvOrWind(centerCellStyle, sheet, monthBillReportData, startRowIndex, "风电收益");
    }

    private static String getRenewablePowerString(
            MonthBillReportData monthBillReportData,
            RenewableTypeReportEnum renewableTypeReportEnum) {
        String powerDataString = "0";
        switch (renewableTypeReportEnum) {
            case PV:
                powerDataString = monthBillReportData.getPvPowerData().getPvPower();
                break;
            case WIND:
                powerDataString = monthBillReportData.getWindPowerData().getWindPower();
                break;
            case WASTER:
                powerDataString = monthBillReportData.getWasterPowerData().getWasterPower();
                break;
        }
        return powerDataString;
    }

    private static String getRenewablePowerSettleAmount(
            MonthBillReportData monthBillReportData,
            RenewableTypeReportEnum renewableTypeReportEnum) {
        String powerSettleAmountString = "0.0";
        switch (renewableTypeReportEnum) {
            case PV:
                powerSettleAmountString =
                        monthBillReportData.getPvPowerData().getPvPowerSettleAmount();
                break;
            case WIND:
                powerSettleAmountString =
                        monthBillReportData.getWindPowerData().getWindPowerSettleAmount();
                break;
            case WASTER:
                powerSettleAmountString =
                        monthBillReportData.getWasterPowerData().getWasterPowerSettleAmount();
                break;
        }
        return powerSettleAmountString;
    }

    /**
     * 思路 1.根据开关先判断出 有几个 比如3个 或者2个或者1个
     *
     * <p>根据Count数目 可以得到 0 列 要合并几个 CellRangeAddress range = new CellRangeAddress(startRowIndex,
     * startRowIndex + 1, 0, 0); 2 3 , 5 6 是必须要合并的列
     */
    private static void renewable(
            CellStyle centerCellStyle,
            Sheet sheet,
            MonthBillReportData monthBillReportData,
            int startRowIndex,
            List<RenewableTypeReportEnum> renewableTypes) {

        // type类型有1个就是 +0, 2个就是+1
        int endRowIndex = startRowIndex + (renewableTypes.size() - 1);
        IntStream.range(0, renewableTypes.size())
                .forEach(
                        i -> {
                            int renewableStartRowIndex = startRowIndex + i;
                            RenewableTypeReportEnum renewableTypeReportEnum = renewableTypes.get(i);
                            Row row = sheet.createRow(renewableStartRowIndex);
                            Cell cell0 = row.createCell(0);
                            cell0.setCellStyle(centerCellStyle);
                            cell0.setCellValue("可再生能源");
                            // xxx收益Jk
                            Cell cell1 = row.createCell(1);
                            cell1.setCellStyle(centerCellStyle);
                            cell1.setCellValue(renewableTypeReportEnum.getIncomeName());
                            Cell cell2 = row.createCell(2);
                            cell2.setCellStyle(centerCellStyle);
                            cell2.setCellValue("发电量(kWh)");
                            Cell cell2Merge = row.createCell(3);
                            cell2Merge.setCellStyle(centerCellStyle);

                            Cell cell4 = row.createCell(4);
                            cell4.setCellStyle(centerCellStyle);
                            // 根据不同的type 去取不同的 power data 值
                            cell4.setCellValue(
                                    getRenewablePowerString(
                                            monthBillReportData, renewableTypeReportEnum));
                            Cell cell5 = row.createCell(5);
                            cell5.setCellStyle(centerCellStyle);
                            cell5.setCellValue("结算金额(元)");
                            Cell cell6Merge = row.createCell(6);
                            cell6Merge.setCellStyle(centerCellStyle);

                            Cell cell7 = row.createCell(7);
                            cell7.setCellStyle(centerCellStyle);
                            // 根据不同的 type 去取不同的 结算金额
                            cell7.setCellValue(
                                    getRenewablePowerSettleAmount(
                                            monthBillReportData, renewableTypeReportEnum));

                            CellRangeAddress range2 =
                                    new CellRangeAddress(
                                            renewableStartRowIndex, renewableStartRowIndex, 2, 3);
                            CellRangeAddress range3 =
                                    new CellRangeAddress(
                                            renewableStartRowIndex, renewableStartRowIndex, 5, 6);
                            sheet.addMergedRegion(range2);
                            sheet.addMergedRegion(range3);
                        });
        //        CellRangeAddress range = new CellRangeAddress(startRowIndex, endRowIndex, 0, 0);
        //        sheet.addMergedRegion(range);
    }

    private static void pvWind(
            CellStyle centerCellStyle,
            Sheet sheet,
            MonthBillReportData monthBillReportData,
            int startRowIndex) {
        for (int i = startRowIndex; i <= startRowIndex + 1; i++) {
            Row row = sheet.createRow(i);
            Cell cell0 = row.createCell(0);
            cell0.setCellStyle(centerCellStyle);
            cell0.setCellValue("可再生能源");

            Cell cell1 = row.createCell(1);
            cell1.setCellStyle(centerCellStyle);
            if (i == startRowIndex) {
                cell1.setCellValue("光伏收益");
            } else {
                cell1.setCellValue("风电收益");
            }
            Cell cell2 = row.createCell(2);
            cell2.setCellStyle(centerCellStyle);
            cell2.setCellValue("发电量(kWh)");
            Cell cell2Merge = row.createCell(3);
            cell2Merge.setCellStyle(centerCellStyle);

            Cell cell3 = row.createCell(4);
            cell3.setCellStyle(centerCellStyle);
            if (i == startRowIndex) {
                cell3.setCellValue(monthBillReportData.getPvPowerData().getPvPower());
            } else {
                cell3.setCellValue(monthBillReportData.getWindPowerData().getWindPower());
            }
            Cell cell4 = row.createCell(5);
            cell4.setCellStyle(centerCellStyle);
            cell4.setCellValue("结算金额(元)");

            Cell cell4Merge = row.createCell(6);
            cell4Merge.setCellStyle(centerCellStyle);

            Cell cell5 = row.createCell(7);
            cell5.setCellStyle(centerCellStyle);
            if (i == startRowIndex) {
                cell5.setCellValue(monthBillReportData.getPvPowerData().getPvPowerSettleAmount());
            } else {
                cell5.setCellValue(
                        monthBillReportData.getWindPowerData().getWindPowerSettleAmount());
            }

            CellRangeAddress range2 = new CellRangeAddress(i, i, 2, 3);
            CellRangeAddress range3 = new CellRangeAddress(i, i, 5, 6);
            sheet.addMergedRegion(range2);
            sheet.addMergedRegion(range3);
        }
        CellRangeAddress range = new CellRangeAddress(startRowIndex, startRowIndex + 1, 0, 0);

        sheet.addMergedRegion(range);
    }

    private static void setWin(
            Workbook workbook,
            CellStyle centerCellStyle,
            Sheet sheet,
            MonthBillReportData monthBillReportData) {
        boolean incomeShow = false;
        if (monthBillReportData.getIncomeDivideIntoEntity() != null) {
            incomeShow = true;
        }
        // 按照以前的 只显示 月度总收益的情况
        if (!incomeShow) {

            int startRowIndex = getLastNewRowNum(sheet);
            Row row = sheet.createRow(startRowIndex);
            Cell cell0 = row.createCell(0);
            cell0.setCellStyle(centerCellStyle);
            cell0.setCellValue("收益计算");

            for (int i = 1; i <= 4; i++) {
                Cell cell1 = row.createCell(i);
                cell1.setCellStyle(centerCellStyle);
                cell1.setCellValue("月度总收益(元)");
            }
            CellRangeAddress range = new CellRangeAddress(startRowIndex, startRowIndex, 1, 4);
            sheet.addMergedRegion(range);

            CellStyle newCellStyle = getCenterCellStyle(workbook);
            setBord(newCellStyle);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            newCellStyle.setFont(headerFont);
            for (int i = 5; i <= 7; i++) {
                Cell cell1 = row.createCell(i);
                cell1.setCellStyle(newCellStyle);
                cell1.setCellValue(monthBillReportData.getMonTotalWin());
            }
            CellRangeAddress range2 = new CellRangeAddress(startRowIndex, startRowIndex, 5, 7);
            sheet.addMergedRegion(range2);
        } else {
            List<Double> monIncomeList = monthBillReportData.getMonIncomeList();

            // 需要显示 客户方收益 资方收益 等
            for (int loop = 0; loop < 7; loop++) {
                int startRowIndex = getLastNewRowNum(sheet);
                Row row = sheet.createRow(startRowIndex);
                Cell cell0 = row.createCell(0);
                cell0.setCellStyle(centerCellStyle);
                cell0.setCellValue("收益计算");
                String title = INCOME_ARR[loop];
                if (loop == 1) {
                    // TODO 这里如果后期国外有 账单了 需要把 (元) 变成对应的 欧元
                    // 把 百分比给 填充到这个 title 上
                    title =
                            title.replace(
                                    EmsConstants.SAMPLE_PLACEHOLDER,
                                    String.valueOf(
                                            monthBillReportData
                                                            .getIncomeDivideIntoEntity()
                                                            .getCustomerOccupancy()
                                                    * 100));
                }
                for (int i = 1; i <= 4; i++) {
                    Cell cell1 = row.createCell(i);
                    cell1.setCellStyle(centerCellStyle);
                    cell1.setCellValue(title);
                }
                CellRangeAddress range = new CellRangeAddress(startRowIndex, startRowIndex, 1, 4);
                sheet.addMergedRegion(range);

                CellStyle newCellStyle = getCenterCellStyle(workbook);
                setBord(newCellStyle);
                Font headerFont = workbook.createFont();
                headerFont.setBold(true);
                newCellStyle.setFont(headerFont);
                for (int i = 5; i <= 7; i++) {
                    Cell cell1 = row.createCell(i);
                    cell1.setCellStyle(newCellStyle);
                    Double incomeValue = monIncomeList.get(loop);
                    // cell1.setCellValue(monthBillReportData.getMonTotalWin());
                    // 从 收益list 里面根据 index loop 获取到对应的 收益
                    cell1.setCellValue(incomeValue);
                }
                CellRangeAddress range2 = new CellRangeAddress(startRowIndex, startRowIndex, 5, 7);
                sheet.addMergedRegion(range2);
            }
        }
    }

    private static int getStartRowIndex(
            Boolean existEnergyStorageFlag,
            Boolean existDemandControl,
            Boolean existRenewableEnergy,
            String type,
            MonthBillReportData monthBillReportData) {
        int rowIndex = 0;
        // 需量控制
        if (type.equals(DEMAND_CONTROL_TEXT)) {
            // 获取需量控制StartRowIndex
            if (existEnergyStorageFlag) {
                rowIndex = 14;
            } else {
                rowIndex = 3;
            }
        }
        int demandSize = 0;
        if (monthBillReportData.getDemandProfitMap() != null
                && !monthBillReportData.getDemandProfitMap().isEmpty()) {
            demandSize = monthBillReportData.getDemandProfitMap().size() - 1;
        }
        // 可再生能源
        if (type.equals(RENEWABLE_ENERGY_TEXT)) {
            if (existEnergyStorageFlag && existDemandControl) {
                rowIndex = 15 + demandSize;
            } else if (!existEnergyStorageFlag && !existDemandControl) {
                rowIndex = 3;
            } else {
                if (existEnergyStorageFlag) {
                    rowIndex = 14;
                } else {
                    rowIndex = 4 + demandSize;
                }
            }
        }
        if (type.equals(WIN_TEXT) || type.equals(REMARK_TEXT)) {
            // 收益计算
            if (type.equals(WIN_TEXT)) {
                rowIndex = 3;
            }
            // 备注
            if (type.equals(REMARK_TEXT)) {
                rowIndex = 4;
            }
            if (existEnergyStorageFlag != null && existEnergyStorageFlag) {
                rowIndex = rowIndex + 11;
            }
            if (existDemandControl != null && existDemandControl) {
                rowIndex = rowIndex + 1 + demandSize;
            }
            if (existRenewableEnergy != null && existRenewableEnergy) {
                rowIndex = rowIndex + 2;
            }
        }
        return rowIndex;
    }

    /**
     * 计算 开始行 根据不同类型
     *
     * @param existEnergyStorageFlag : 是否存在 储能放电
     * @param existDemandControl : 是否存在 需量控制
     * @param type : 类型
     * @return : index
     */
    private static int getStartRowIndex(
            Boolean existEnergyStorageFlag,
            Boolean existDemandControl,
            String type,
            MonthBillReportData monthBillReportData) {
        return getStartRowIndex(
                existEnergyStorageFlag, existDemandControl, null, type, monthBillReportData);
    }

    private static int getStartRowIndex(
            Boolean existEnergyStorageFlag, String type, MonthBillReportData monthBillReportData) {
        return getStartRowIndex(existEnergyStorageFlag, null, type, monthBillReportData);
    }

    private static void setRemark(CellStyle centerCellStyle, Sheet sheet) {
        int startRowIndex = getLastNewRowNum(sheet);
        Row row = sheet.createRow(startRowIndex);
        Cell cell0 = row.createCell(0);
        cell0.setCellStyle(centerCellStyle);
        cell0.setCellValue("备注:");

        for (int i = 1; i <= 7; i++) {
            Cell cell1 = row.createCell(i);
            cell1.setCellStyle(centerCellStyle);
            cell1.setCellValue("储能月度总收益=出电有功结算金额(尖峰+峰段+平段+谷段+深谷)-进电有功结算金额(尖峰+峰段+平段+谷段+深谷)");
        }
        CellRangeAddress range = new CellRangeAddress(startRowIndex, startRowIndex, 1, 7);
        sheet.addMergedRegion(range);
    }

    /**
     * 设置头Header
     *
     * @param workbook : workbook
     * @param sheet : sheet
     * @param monthBillReportData : billReportData
     */
    private static void setHeader(
            Workbook workbook, Sheet sheet, MonthBillReportData monthBillReportData) {
        Row headerRow = sheet.createRow(0);
        CellRangeAddress rangeHeader = new CellRangeAddress(0, 0, 0, 7);
        sheet.addMergedRegion(rangeHeader);
        CellStyle headerCellStyle = workbook.createCellStyle();
        setBord(headerCellStyle);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 23);
        headerCellStyle.setFont(headerFont);
        // 设置水平居中对齐
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中对齐
        headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        for (int i = 0; i <= 7; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(monthBillReportData.getProjectName());
            // 设置表头样式（可选）
            cell.setCellStyle(headerCellStyle);
        }
    }

    /**
     * 设置 文本居中样式
     *
     * @param workbook: workbook
     */
    private static CellStyle getCenterCellStyle(Workbook workbook) {
        CellStyle centerCellStyle = workbook.createCellStyle();
        centerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中对齐
        centerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return centerCellStyle;
    }

    /**
     * 设置 边框加粗样式
     *
     * @param cellStyle: cellStyle
     */
    private static void setBord(CellStyle cellStyle) {
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色为黑色
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
    }
}
