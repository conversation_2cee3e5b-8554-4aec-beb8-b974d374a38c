package com.wifochina.modules.notice.controller;

import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.notice.request.RecoverRequest;
import com.wifochina.modules.notice.service.NoticeReportService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created on 2025/7/4 14:42.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "34-账单重跑")
@RequestMapping("/reportReRunSend")
public class ReRunReportController {
    @Resource private NoticeReportService noticeReportService;

    @Resource private ProjectService projectService;

    @PostMapping("/month")
    @PreAuthorize("hasAuthority('/notice/reportReRunSend')")
    @ApiOperation("月度账单重发通知(只支持云端项目)")
    public Result<Object> reRunMonth(@RequestBody RecoverRequest recoverRequest) {
        if (recoverRequest.getProjectId().equals(EmsConstants.ALL)) {
            List<ProjectEntity> projectEntities =
                    projectService
                            .lambdaQuery()
                            .eq(ProjectEntity::getWhetherDelete, false)
                            .ne(ProjectEntity::getProjectModel, 1)
                            .list();
            for (ProjectEntity projectEntity : projectEntities) {
                noticeReportService.noticeSendMonth(
                        projectEntity.getId(), recoverRequest.getStart());
            }
        } else {
            noticeReportService.noticeSendMonth(
                    recoverRequest.getProjectId(), recoverRequest.getStart());
        }
        return Result.success();
    }

    @PostMapping("/year")
    @PreAuthorize("hasAuthority('/notice/reportReRunSend')")
    @ApiOperation("年度账单重发通知(只支持云端项目)")
    public Result<Object> reRunYear(@RequestBody RecoverRequest recoverRequest) {
        if (recoverRequest.getProjectId().equals(EmsConstants.ALL)) {
            List<ProjectEntity> projectEntities =
                    projectService
                            .lambdaQuery()
                            .eq(ProjectEntity::getWhetherDelete, false)
                            .ne(ProjectEntity::getProjectModel, 1)
                            .list();
            for (ProjectEntity projectEntity : projectEntities) {
                noticeReportService.noticeSendYear(
                        projectEntity.getId(), recoverRequest.getStart());
            }
        } else {
            noticeReportService.noticeSendYear(
                    recoverRequest.getProjectId(), recoverRequest.getStart());
        }
        return Result.success();
    }
}
