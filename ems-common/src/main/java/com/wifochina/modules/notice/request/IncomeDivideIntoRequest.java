package com.wifochina.modules.notice.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023-09-15 4:48 PM
 */
@Data
public class IncomeDivideIntoRequest {

    @ApiModelProperty("资方单位名称")
    @NotNull
    private String nameOfEmployer;

    @ApiModelProperty("客户单位名称")
    @NotNull
    private String nameOfCustomer;

    @ApiModelProperty("客户方收益占比%")
    @NotNull
    private double customerOccupancy;
}
