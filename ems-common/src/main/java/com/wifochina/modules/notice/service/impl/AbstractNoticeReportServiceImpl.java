package com.wifochina.modules.notice.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.constants.RenewableModelEnum;
import com.wifochina.common.constants.RenewableTypeReportEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.price.RenewableModelPriceFormulaComponent;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.PricePeriodEnum;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.electric.ElectricService;
import com.wifochina.modules.electric.entity.ElectricEntity;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.OperationProfitService;
import com.wifochina.modules.income.RenewableProfitService;
import com.wifochina.modules.income.query.pvwind.RenewableIncomeQueryService;
import com.wifochina.modules.notice.entity.IncomeDivideIntoEntity;
import com.wifochina.modules.notice.entity.NoticeEntity;
import com.wifochina.modules.notice.service.IncomeDivideIntoService;
import com.wifochina.modules.notice.service.NoticeEmailService;
import com.wifochina.modules.notice.service.NoticeReportService;
import com.wifochina.modules.notice.service.NoticeService;
import com.wifochina.modules.notice.utils.ProjectMeterReportUtils;
import com.wifochina.modules.notice.utils.data.common.PvPowerData;
import com.wifochina.modules.notice.utils.data.common.RenewableReportData;
import com.wifochina.modules.notice.utils.data.common.WasterPowerData;
import com.wifochina.modules.notice.utils.data.common.WindPowerData;
import com.wifochina.modules.notice.utils.data.month.*;
import com.wifochina.modules.notice.utils.data.year.YearBillReport;
import com.wifochina.modules.notice.utils.data.year.YearBillReportData;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.VO.GroupDemandProfit;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.request.BenefitRequest;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.operation.util.OperationUtil;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.service.EmsDataRemediesService;
import com.wifochina.modules.renewable.entity.RenewableProfitEntity;
import com.wifochina.modules.renewable.request.RenewableProfitRequest;
import com.wifochina.modules.renewable.vo.RenewableProfitVo;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-09-22 9:57 AM
 */
@Slf4j
public abstract class AbstractNoticeReportServiceImpl implements NoticeReportService {

    @Resource protected ProjectService projectService;

    @Resource private AmmeterService ammeterService;

    @Resource private DeviceService deviceService;

    @Resource private NoticeService noticeService;

    @Resource private GroupService groupService;

    @Resource private OperationProfitService operationProfitService;
    @Resource private RenewableProfitService renewableProfitService;

    @Resource private NoticeEmailService noticeEmailService;

    @Resource private ProjectExtService projectExtService;

    @Resource private PriceAreaService priceAreaService;

    @Resource private ElectricPriceService electricPriceService;

    @Resource private ElectricService electricService;

    @Resource private EmsDataRemediesService emsDataRemediesService;
    @Resource private RenewableIncomeQueryService renewableIncomeQueryService;

    @Resource private IncomeDivideIntoService incomeDivideIntoService;

    @Resource private RenewableModelPriceFormulaComponent renewableModelPriceFormulaComponent;

    private WasterPowerData getWasterPowerDate(
            boolean hasWaster, BenefitRequest benefitRequest, String projectId) {

        // 余热发电收益
        ProfitVO profitWasterVO =
                renewableProfitService.getRenewableProfitVo(
                        benefitRequest, projectId, CalculateTypeEnum.WASTER);
        WasterPowerData wasterPowerData = new WasterPowerData();
        if (hasWaster) {
            wasterPowerData.setWasterPower(
                    String.format(
                            "%.5f",
                            Double.parseDouble(profitWasterVO.getWaster_discharge_quantity())));
            wasterPowerData.setWasterPowerSettleAmount(
                    String.format(
                            "%.5f",
                            Double.parseDouble(profitWasterVO.getWaster_discharge_benefit())));
        } else {
            wasterPowerData.setWasterPower("0");
            wasterPowerData.setWasterPowerSettleAmount("0");
        }
        return wasterPowerData;
    }

    private WindPowerData getWindPowerDate(
            boolean hasWind,
            boolean hasWindCal,
            BenefitRequest benefitRequest,
            String projectId,
            GroupEntity groupEntity) {

        // 风电收益
        ProfitVO profitWindVO =
                renewableProfitService.getRenewableProfitVo(
                        benefitRequest, projectId, CalculateTypeEnum.WIND);
        // ProfitVO profitWindVO = operationProfitService.getWindProfitVO(benefitRequest,
        // projectId);
        WindPowerData windPowerData = new WindPowerData();
        // 2024-04-08 10:33:41 add 1.3.6 新增的 风电收益 计算开关
        if (hasWind && hasWindCal) {
            windPowerData.setWindPower(
                    String.format(
                            "%.5f", Double.parseDouble(profitWindVO.getWind_discharge_quantity())));
            windPowerData.setWindPowerSettleAmount(
                    String.format(
                            "%.5f", Double.parseDouble(profitWindVO.getWind_discharge_benefit())));
        } else {
            windPowerData.setWindPower("0");
            windPowerData.setWindPowerSettleAmount("0");
        }
        return windPowerData;
    }

    private PvPowerData getPvPowerData(
            boolean hasPv,
            BenefitRequest benefitRequest,
            String projectId,
            GroupEntity groupEntity) {
        // 光伏收益
        ProfitVO profitPvVO =
                renewableProfitService.getRenewableProfitVo(
                        benefitRequest, projectId, CalculateTypeEnum.PV);
        // ProfitVO profitPvVO = operationProfitService.getPvProfitVO(benefitRequest, projectId);
        PvPowerData pvPowerData = new PvPowerData();
        // 2023-11-17 11:12:47 add pv收益控制
        boolean pvProfitController = groupEntity.getPvProfitController();
        if (hasPv && pvProfitController) {
            pvPowerData.setPvPower(
                    String.format(
                            "%.5f", Double.parseDouble(profitPvVO.getPv_discharge_quantity())));
            pvPowerData.setPvPowerSettleAmount(
                    String.format(
                            "%.5f", Double.parseDouble(profitPvVO.getPv_discharge_benefit())));
        } else {
            pvPowerData.setPvPower("0");
            pvPowerData.setPvPowerSettleAmount("0");
        }
        return pvPowerData;
    }

    @Override
    public void noticeSendMonth(String projectId, long start) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        LocalDateTime startDataTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(start),
                        MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
        LocalDateTime endDataTime = startDataTime.plusMonths(1);
        long end =
                endDataTime
                        .toInstant(
                                MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()))
                        .getEpochSecond();
        end = end - 1;
        List<NoticeEntity> noticeEntities =
                noticeService.lambdaQuery().eq(NoticeEntity::getProjectId, projectId).list();
        GroupEntity systemGroupEntity =
                groupService
                        .lambdaQuery()
                        .eq(GroupEntity::getProjectId, projectId)
                        .eq(GroupEntity::getWhetherSystem, true)
                        .one();
        NoticeEntity tomNoticeEntity = new NoticeEntity();
        tomNoticeEntity.setType("ems,control,pv,wind,waster");
        tomNoticeEntity.setEmail("<EMAIL>");
        //        tomNoticeEntity.setEmail("<EMAIL>");
        noticeEntities.add(tomNoticeEntity);
        //        noticeEntities = new ArrayList<>();
        //        tomNoticeEntity.setEmail("<EMAIL>");
        //        tomNoticeEntity.setType("ems,control,pv,wind,waster");
        //        noticeEntities.add(tomNoticeEntity);
        if (Boolean.TRUE.equals(systemGroupEntity.getCalcEarningsController())) {
            MonthBillReportData monthBillReportData = new MonthBillReportData();
            monthBillReportData.setProjectName(projectEntity.getProjectName());
            // 设置计费月份
            monthBillReportData.setBillingMonth(
                    startDataTime.getMonthValue()
                            + "月1日 - "
                            + startDataTime.getMonthValue()
                            + "月"
                            + endDataTime.minusDays(1).getDayOfMonth()
                            + "日");
            monthBillReportData.setYear(String.valueOf(startDataTime.getYear()));
            monthBillReportData.setMonth(String.valueOf(startDataTime.getMonthValue()));
            // 上次抄表时间
            if (projectEntity.getCreateTime() > start) {
                start = projectEntity.getCreateTime();
                monthBillReportData.setPreReadingMeterDate("新装");
            } else {
                monthBillReportData.setPreReadingMeterDate(
                        startDataTime.getYear()
                                + "年"
                                + startDataTime.getMonthValue()
                                + "月1日 00:00:00");
            }
            // 本次抄表时间
            monthBillReportData.setNowReadingMeterDate(
                    endDataTime.getYear() + "年" + endDataTime.getMonthValue() + "月1日 00:00:00");
            // 储能收益
            List<AmmeterEntity> ammeterEntities =
                    ammeterService
                            .lambdaQuery()
                            .eq(AmmeterEntity::getProjectId, projectId)
                            .eq(AmmeterEntity::getIncome, true)
                            .list();
            List<DeviceEntity> deviceEntities =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getProjectId, projectId)
                            .eq(DeviceEntity::getIncome, true)
                            .list();
            List<String> deviceIds =
                    new ArrayList<>(deviceEntities.size() + ammeterEntities.size());
            deviceEntities.forEach(e -> deviceIds.add(e.getId()));
            ammeterEntities.forEach(e -> deviceIds.add(e.getId()));
            // String deviceCondition = getString(deviceEntities, ammeterEntities);
            BenefitRequest benefitRequest = new BenefitRequest();
            benefitRequest.setStartDate(start);
            benefitRequest.setEndDate(end);
            // 需量控制收益
            ProfitVO profitDemandVO =
                    operationProfitService.getDemandProfitVO(benefitRequest, projectId);
            DemandControlData demandControlData = new DemandControlData();
            List<GroupEntity> demandGroupEntity = groupService.queryEnableDemandIncome(projectId);
            boolean hasDemand = !demandGroupEntity.isEmpty();
            if (hasDemand) {
                demandControlData.setDemandPrice(profitDemandVO.getDemand_control_price());
                demandControlData.setDownDemandPower(
                        String.format(
                                "%.5f",
                                Double.parseDouble(profitDemandVO.getDemand_control_power())));
                demandControlData.setDemandSettleAmount(
                        String.format(
                                "%.5f",
                                Double.parseDouble(profitDemandVO.getDemand_control_benefit())));
                monthBillReportData.setDemandProfitMap(profitDemandVO.getDemandProfitMap());
            } else {
                demandControlData.setDemandPrice("0");
                demandControlData.setDownDemandPower("0");
                demandControlData.setDemandSettleAmount("0");
            }
            monthBillReportData.setDemandControlData(demandControlData);
            // 1.4.4 改成 开了pv收益的才行
            boolean hasPv =
                    systemGroupEntity.getPhotovoltaicController() != null
                            && systemGroupEntity.getPhotovoltaicController()
                            && systemGroupEntity.getPvProfitController() != null
                            && systemGroupEntity.getPvProfitController();

            // 光伏收益
            PvPowerData pvPowerData =
                    getPvPowerData(hasPv, benefitRequest, projectId, systemGroupEntity);
            monthBillReportData.setPvPowerData(pvPowerData);
            // 风电收益
            boolean hasWind =
                    systemGroupEntity.getEnableWindPowerGeneration() != null
                            && systemGroupEntity.getEnableWindPowerGeneration()
                            && systemGroupEntity.getWindEarningsController() != null
                            && systemGroupEntity.getWindEarningsController();
            boolean hasWindCal =
                    systemGroupEntity.getEnableWindPowerGeneration() != null
                            && systemGroupEntity.getEnableWindPowerGeneration()
                            && systemGroupEntity.getWindEarningsController() != null
                            && systemGroupEntity.getWindEarningsController();
            WindPowerData windPowerData =
                    getWindPowerDate(
                            hasWind, hasWindCal, benefitRequest, projectId, systemGroupEntity);
            monthBillReportData.setWindPowerData(windPowerData);

            // 1.4.2 add 余热发电收益
            boolean hasWaster =
                    Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration())
                            && Boolean.TRUE.equals(systemGroupEntity.getWasterEarningsController());
            WasterPowerData wasterPowerDate =
                    getWasterPowerDate(hasWaster, benefitRequest, projectId);
            monthBillReportData.setWasterPowerData(wasterPowerDate);

            // 储能收益
            // 2024-04-16 11:19:17 add  是否需要分组展示
            boolean groupEarningController =
                    groupService.groupEarningCondition(projectId, systemGroupEntity);
            // boolean groupEarningController = CollectionUtil.isNotEmpty(groupEntities);
            monthBillReportData.setGroupEarningsController(groupEarningController);
            // 根据分组存储 分组的 储能充放电的 各个段的数据
            Map<String, List<EnergyStorageData>> groupEnergyStorageDateMap = new HashMap<>();
            Map<String, DetailReportData> groupDetailReportDataMap = new HashMap<>();
            if (!groupEarningController) {
                // 添加补值的group id
                List<String> groupIds = groupService.queryGroupIdsByProjectId(projectId);
                deviceIds.addAll(groupIds);
                // 不分组储能
                ElectricRequest electricRequest = new ElectricRequest();
                electricRequest.setProjectId(projectId);
                electricRequest.setStart(start);
                electricRequest.setEnd(end);
                electricRequest.setDeviceIdList(deviceIds);
                List<ElectricEntity> electricEntities =
                        electricService.queryElectricEveryDayProfit(electricRequest);
                // 账单明细
                DetailReportData detailReportData =
                        getDetailReportData(
                                systemGroupEntity,
                                projectEntity,
                                startDataTime,
                                start,
                                end,
                                projectId,
                                systemGroupEntity,
                                electricEntities,
                                false);
                double emsProfit =
                        getEmsProfit(
                                projectId,
                                start,
                                systemGroupEntity,
                                projectEntity,
                                electricEntities,
                                groupEnergyStorageDateMap,
                                false);
                groupDetailReportDataMap.put(systemGroupEntity.getId(), detailReportData);
                monthBillReportData.setEmsProfit(emsProfit);
                monthBillReportData.setGroupEnergyStorageDateMap(groupEnergyStorageDateMap);
            } else {
                List<GroupEntity> groupEntities =
                        groupService.queryGroupsNotSystemAndBindIncomeDeviceOrMeter(projectId);
                // 1.4.4 added filter open vpp group
                groupEntities =
                        groupEntities.stream()
                                .filter(
                                        groupEntity ->
                                                !Boolean.TRUE.equals(groupEntity.getOpenVpp()))
                                .collect(Collectors.toList());
                // 分组储能收益
                double emsProfit = 0.0;
                for (GroupEntity group : groupEntities) {
                    // 查询开启了收益标签的 设备或者电表的 ids
                    List<String> groupIncomeDeviceIds =
                            groupService.getIncomeDeviceAmmeterIdsForGroup(
                                    projectId, group.getId());
                    // 增加补值 id
                    groupIncomeDeviceIds.add(group.getId());
                    String groupDeviceSql = buildDeviceSql(groupIncomeDeviceIds);
                    // 先获取到 设备的ids  然后去计算
                    ElectricProfitVO electricProfitVO = new ElectricProfitVO();
                    // 可能有测试项目 下面electricProfitVO的属性还是null 导致 addRemedies 方法里面报错, 所以需要初始化
                    electricProfitVO.init();
                    if (!groupIncomeDeviceIds.isEmpty()) {
                        ElectricRequest electricRequest = new ElectricRequest();
                        electricRequest.setProjectId(projectId);
                        electricRequest.setStart(start);
                        electricRequest.setEnd(end);
                        electricRequest.setDeviceIdList(groupIncomeDeviceIds);
                        List<ElectricEntity> groupElectricEntities =
                                electricService.queryElectricEveryDayProfit(electricRequest);
                        // 账单明细
                        DetailReportData detailReportData =
                                getDetailReportData(
                                        systemGroupEntity,
                                        projectEntity,
                                        startDataTime,
                                        start,
                                        end,
                                        projectId,
                                        group,
                                        groupElectricEntities,
                                        true);
                        groupDetailReportDataMap.put(group.getId(), detailReportData);
                        emsProfit +=
                                getEmsProfit(
                                        projectId,
                                        start,
                                        group,
                                        projectEntity,
                                        groupElectricEntities,
                                        groupEnergyStorageDateMap,
                                        true);
                        monthBillReportData.setEmsProfit(emsProfit);
                        monthBillReportData.setGroupEnergyStorageDateMap(groupEnergyStorageDateMap);
                    }
                }
            }

            // 1.3.7 新增查询 收益分成数据
            IncomeDivideIntoEntity incomeDivideIntoEntity =
                    incomeDivideIntoService.findInfoByProjectId(projectId);
            if (incomeDivideIntoEntity != null) {
                log.info(
                        "setIncomeDivideIntoEntity for monthBillReportData :{}",
                        incomeDivideIntoEntity);
                monthBillReportData.setIncomeDivideIntoEntity(incomeDivideIntoEntity);
            }

            for (NoticeEntity noticeEntity : noticeEntities) {
                String email = noticeEntity.getEmail();
                List<String> emailList = new ArrayList<>(1);
                emailList.add(email);
                MonthBillReportData monthBillReportDataTemp = new MonthBillReportData();
                BeanUtils.copyProperties(monthBillReportData, monthBillReportDataTemp);
                Map<String, DetailReportData> detailReportDataMap = new HashMap<>();

                double total = 0;
                // 汇总 月度的 总收益
                total =
                        collectMonthTotal(
                                noticeEntity,
                                monthBillReportDataTemp,
                                total,
                                hasDemand,
                                hasPv,
                                hasWind,
                                hasWindCal,
                                hasWaster);

                monthBillReportDataTemp.setMonTotalWin(String.valueOf(total));
                monthBillReportDataTemp.setProjectId(projectId);
                // 处理一下 明细sheet的 一些开关情况
                for (Map.Entry<String, DetailReportData> entry :
                        groupDetailReportDataMap.entrySet()) {
                    String groupId = entry.getKey();
                    DetailReportData detailReportData = entry.getValue();
                    DetailReportData detailReportDataTemp = new DetailReportData();
                    BeanUtils.copyProperties(detailReportData, detailReportDataTemp);
                    if (noticeEntity.getType().contains("ems")) {
                        detailReportDataTemp.setShowEnergyStorage(true);
                    }
                    if (noticeEntity.getType().contains("control") && hasDemand) {
                        detailReportDataTemp.setShowDemandControl(true);
                    }
                    if (noticeEntity.getType().contains("pv") && hasPv) {
                        detailReportDataTemp.setShowPvPower(true);
                    }
                    // 2024-04-08 10:33:41 add 1.3.6 新增的 风电收益 计算开关
                    if (noticeEntity.getType().contains("wind") && hasWind && hasWindCal) {
                        detailReportDataTemp.setShowWindPower(true);
                    }
                    // 2025-07-04 15:13:12 dcdc 1.4.4 根据开关来展示
                    if (noticeEntity.getType().contains("waster") && hasWaster) {
                        detailReportDataTemp.setShowWasterPower(true);
                    }
                    detailReportDataMap.put(groupId, detailReportDataTemp);
                }
                // 走原来的
                noticeEmailService.sendMonthReportEmail(
                        new NoticeEmailService.MonthDataHolder() {
                            @Override
                            public MonthBillReportData billData() {
                                // 初始化模拟数据 sheet1
                                return monthBillReportDataTemp;
                            }

                            @Override
                            public DetailReportDatas detailDatas() {
                                // sheet2 明细数据
                                return new DetailReportDatas()
                                        .setDetailReportDataMap(detailReportDataMap);
                            }
                        },
                        emailList);
            }
        }
    }

    private static double collectMonthTotal(
            NoticeEntity noticeEntity,
            MonthBillReportData monthBillReportDataTemp,
            double total,
            boolean hasDemand,
            boolean hasPv,
            boolean hasWind,
            boolean hasWindCal,
            boolean hasWaster) {
        // 注意这个 emsProfit 如果是有分组储能收益 前面也已经每个分组的收益累加后的结果
        if (noticeEntity.getType().contains("ems")) {
            monthBillReportDataTemp.setShowEnergyStorage(true);
            total += monthBillReportDataTemp.getEmsProfit();
            log.info("ems total: {}", monthBillReportDataTemp.getEmsProfit());
        }
        if (noticeEntity.getType().contains("control") && hasDemand) {
            monthBillReportDataTemp.setShowDemandControl(true);
            total +=
                    Double.parseDouble(
                            monthBillReportDataTemp.getDemandControlData().getDemandSettleAmount());
            log.info(
                    "control total: {}",
                    monthBillReportDataTemp.getDemandControlData().getDemandSettleAmount());
        }
        if (noticeEntity.getType().contains("pv") && hasPv) {
            monthBillReportDataTemp.setShowPvPower(true);
            total +=
                    Double.parseDouble(
                            monthBillReportDataTemp.getPvPowerData().getPvPowerSettleAmount());
            log.info(
                    "pv total: {}",
                    monthBillReportDataTemp.getPvPowerData().getPvPowerSettleAmount());
        }

        // 2024-04-08 10:33:41 add 1.3.6 新增的 风电收益 计算开关
        if (noticeEntity.getType().contains("wind") && hasWind && hasWindCal) {
            monthBillReportDataTemp.setShowWindPower(true);
            total +=
                    Double.parseDouble(
                            monthBillReportDataTemp.getWindPowerData().getWindPowerSettleAmount());
            log.info(
                    "wind total: {}",
                    monthBillReportDataTemp.getWindPowerData().getWindPowerSettleAmount());
        }
        if (noticeEntity.getType().contains("waster") && hasWaster) {
            monthBillReportDataTemp.setShowWasterPower(true);
            total +=
                    Double.parseDouble(
                            monthBillReportDataTemp
                                    .getWasterPowerData()
                                    .getWasterPowerSettleAmount());
            log.info(
                    "waster total: {}",
                    monthBillReportDataTemp.getWasterPowerData().getWasterPowerSettleAmount());
        }
        //        if (noticeEntity.getType().contains("wind") && hasWind && hasWindCal) {
        //            monthBillReportDataTemp.setShowWindPower(true);
        //            total +=
        //                    Double.parseDouble(
        //
        // monthBillReportDataTemp.getWindPowerData().getWindPowerSettleAmount());
        //        }

        // 1.3.7 new add 当有收益分成的时候 需要按照这个list 展示 收益计算的 Excel
        if (monthBillReportDataTemp.getIncomeDivideIntoEntity() != null) {

            List<Double> incomeList = new ArrayList<>();
            incomeList.add(monthBillReportDataTemp.getEmsProfit());
            // 客户方储能收益 占比
            incomeList.add(
                    monthBillReportDataTemp.getEmsProfit()
                            * monthBillReportDataTemp
                                    .getIncomeDivideIntoEntity()
                                    .getCustomerOccupancy());
            // 资方储能收益
            incomeList.add(
                    monthBillReportDataTemp.getEmsProfit()
                            - (monthBillReportDataTemp.getEmsProfit()
                                    * monthBillReportDataTemp
                                            .getIncomeDivideIntoEntity()
                                            .getCustomerOccupancy()));

            // 需量
            incomeList.add(
                    Double.parseDouble(
                            monthBillReportDataTemp
                                    .getDemandControlData()
                                    .getDemandSettleAmount()));
            // 光伏发电
            incomeList.add(
                    Double.parseDouble(
                            monthBillReportDataTemp.getPvPowerData().getPvPowerSettleAmount()));
            // 风电
            incomeList.add(
                    Double.parseDouble(
                            monthBillReportDataTemp.getWindPowerData().getWindPowerSettleAmount()));

            // 1.4.2 added waster 余热发电
            incomeList.add(
                    Double.parseDouble(
                            monthBillReportDataTemp
                                    .getWasterPowerData()
                                    .getWasterPowerSettleAmount()));
            monthBillReportDataTemp.setMonIncomeList(incomeList);
        }

        return total;
    }

    private double getEmsProfit(
            String projectId,
            long start,
            GroupEntity groupEntity,
            ProjectEntity projectEntity,
            List<ElectricEntity> electricEntities,
            Map<String, List<EnergyStorageData>> groupEnergyStorageDateMap,
            boolean groupEarningFlag) {
        double emsProfit = 0;
        if (CollectionUtils.isNotEmpty(electricEntities)) {
            // 账单求和
            List<EnergyStorageData> list =
                    getElectricData(
                            projectEntity,
                            start,
                            projectId,
                            electricEntities,
                            groupEntity,
                            groupEarningFlag);
            groupEnergyStorageDateMap.put(groupEntity.getId(), list);
            // monthBillReportData.setEnergyStorageDataList(list);
            // 前5个是放，后5个是充
            int index = 5;
            for (int i = 0; i < index; i++) {
                EnergyStorageData energyStorageData = list.get(i);
                emsProfit = emsProfit + energyStorageData.getSettleAmount();
            }
            for (int i = index; i < list.size(); i++) {
                EnergyStorageData energyStorageData = list.get(i);
                emsProfit = emsProfit - energyStorageData.getSettleAmount();
            }
        }
        return emsProfit;
    }

    @NotNull
    private static String buildDeviceSql(List<String> deviceIds) {
        StringBuilder deviceIdSql = new StringBuilder("(");
        for (int i = 0; i < deviceIds.size(); i++) {
            if (i == 0) {
                deviceIdSql.append("deviceId = '").append(deviceIds.get(i)).append("'");
            } else {
                deviceIdSql.append(" or deviceId = '").append(deviceIds.get(i)).append("'");
            }
        }
        deviceIdSql.append(")");
        return deviceIdSql.toString();
    }

    @Override
    public void noticeSendYear(String projectId, long start) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<NoticeEntity> noticeEntities =
                noticeService.lambdaQuery().eq(NoticeEntity::getProjectId, projectId).list();
        GroupEntity systemGroupEntity =
                groupService
                        .lambdaQuery()
                        .eq(GroupEntity::getProjectId, projectId)
                        .eq(GroupEntity::getWhetherSystem, true)
                        .one();
        if (noticeEntities.isEmpty()
                || Boolean.TRUE.equals(!systemGroupEntity.getCalcEarningsController())) {
            return;
        }
        NoticeEntity tomNoticeEntity = new NoticeEntity();
        tomNoticeEntity.setType("ems,control,pv,wind,waster");
        tomNoticeEntity.setEmail("<EMAIL>");
        //        tomNoticeEntity.setEmail("<EMAIL>");
        //        noticeEntities = new ArrayList<>();
        noticeEntities.add(tomNoticeEntity);
        Pair<Long, Long> startEndTimePair = getActualStartEndTime(projectEntity, start);
        ZoneOffset zoneOffset = MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone());
        LocalDateTime startDateTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(startEndTimePair.getFirst()), zoneOffset);

        List<YearBillReportData> yearBillReportDatas = new LinkedList<>();
        // 得到 是否需要分组储能收益的
        List<GroupEntity> groups = new ArrayList<>();
        // 储能收益
        // 2024-04-16 11:19:17 add  是否需要分组展示
        boolean groupEarningController =
                groupService.groupEarningCondition(projectId, systemGroupEntity);
        // boolean groupEarningController = CollectionUtil.isNotEmpty(groupEntities);
        if (!groupEarningController) {
            log.info("year report groupEarningController is false");
            groups.add(systemGroupEntity);
        } else {
            List<GroupEntity> groupEntities =
                    groupService.queryGroupsNotSystemAndBindIncomeDeviceOrMeter(projectId);
            log.info("year report groupEarningController is true");
            groups.addAll(groupEntities);
        }
        List<String> deviceIds = new ArrayList<>();
        // 遍历 分组去操作
        // 1.先判断是否有 分组储能收益 然后 得到 比如2个分组 得到deviceId 去调用这个循环 得到一个数据 是汇总的 这里面要跑 pv 和 需量
        // 2.再循环单独跑 每个分组 但是不要包括 pv 和 需量
        if (!groupEarningController) {
            // 以前的汇总数据
            deviceIds = OperationUtil.getIncomeDeviceIds(projectId, deviceService, ammeterService);
            // 增加补值group id
            deviceIds.addAll(groupService.queryGroupIdsByProjectId(projectId));
            YearBillReportData yearBillReportData =
                    yearBillGroupReportDataBuild(
                                    projectId,
                                    start,
                                    startEndTimePair,
                                    deviceIds,
                                    zoneOffset,
                                    projectEntity,
                                    startDateTime,
                                    systemGroupEntity,
                                    true)
                            .setSheetName("汇总")
                            .setCollect(true)
                            .setYear(String.valueOf(startDateTime.getYear()))
                            .setTitleName(projectEntity.getProjectName());
            yearBillReportDatas.add(yearBillReportData);
        } else {
            // 分组储能收益开启
            // 1.先判断是否有 分组储能收益 然后 得到 比如2个分组 得到deviceId 去调用这个循环 得到一个数据 是汇总的 这里面要跑 pv 和 需量
            // 2.再循环单独跑 每个分组 但是不要包括 pv 和 需量
            // 1.4.4 added group filter vpp
            groups =
                    groups.stream()
                            .filter(groupEntity -> !Boolean.TRUE.equals(groupEntity.getOpenVpp()))
                            .collect(Collectors.toList());
            for (GroupEntity group : groups) {
                deviceIds.addAll(
                        OperationUtil.getIncomeDeviceIdsForGroup(
                                projectId, group.getId(), groupService));
                deviceIds.add(group.getId());
            }
            // 得到总共分组的 设备ids 的汇总数据 , 这里 group 直接null 和 groupEarningController = false
            // 注意这里 只是所有分组储能的 分组下的 符合的设备or电表ids的集合
            YearBillReportData yearBillReportData =
                    yearBillGroupReportDataBuild(
                                    projectId,
                                    start,
                                    startEndTimePair,
                                    deviceIds,
                                    zoneOffset,
                                    projectEntity,
                                    startDateTime,
                                    systemGroupEntity,
                                    true)
                            .setSheetName("汇总")
                            .setCollect(true)
                            .setYear(String.valueOf(startDateTime.getYear()))
                            .setTitleName(projectEntity.getProjectName());

            yearBillReportDatas.add(yearBillReportData);
            for (GroupEntity group : groups) {
                List<String> groupDeviceIds = new ArrayList<>();
                // 注意这里 只是这个分组下的 符合的设备or电表ids
                groupDeviceIds.addAll(
                        OperationUtil.getIncomeDeviceIdsForGroup(
                                projectId, group.getId(), groupService));
                groupDeviceIds.add(group.getId());
                // 具体每个 分组储能的明细
                YearBillReportData groupReportData =
                        yearBillGroupReportDataBuild(
                                        projectId,
                                        start,
                                        startEndTimePair,
                                        groupDeviceIds,
                                        zoneOffset,
                                        projectEntity,
                                        startDateTime,
                                        systemGroupEntity,
                                        false)
                                .setSheetName(group.getName())
                                .setCollect(false)
                                .setYear(String.valueOf(startDateTime.getYear()))
                                .setTitleName(projectEntity.getProjectName());
                yearBillReportDatas.add(groupReportData);
            }
        }
        for (NoticeEntity noticeEntity : noticeEntities) {
            boolean showEnergyStorage = false;
            boolean showDemandControl = false;
            boolean showPvPower = false;
            boolean showWindPower = false;
            boolean showWasterPower = false;
            String email = noticeEntity.getEmail();
            List<String> emailList = new ArrayList<>(1);
            emailList.add(email);
            // 获取到汇总的 , 只有在汇总的sheet里面的 才会去 有 pv wind 和 需量
            YearBillReportData collectYearBillReportData =
                    yearBillReportDatas.stream()
                            .filter(YearBillReportData::isCollect)
                            .findFirst()
                            .orElseThrow(() -> new ServiceException(""));
            List<YearBillReportData> groupYearBillReportDatas =
                    yearBillReportDatas.stream()
                            .filter(yearBillReportData -> !yearBillReportData.isCollect())
                            .collect(Collectors.toList());
            if (noticeEntity.getType().contains("ems")) {
                showEnergyStorage = true;
            }
            if (noticeEntity.getType().contains("control")
                    && !collectYearBillReportData.getYearBillReports().isEmpty()
                    && collectYearBillReportData.getYearBillReports().get(0).getDemandProfitList()
                            != null
                    && !collectYearBillReportData
                            .getYearBillReports()
                            .get(0)
                            .getDemandProfitList()
                            .isEmpty()) {
                showDemandControl = true;
            }
            // 2023-11-17 11:12:47 add pv收益控制
            if (noticeEntity.getType().contains("pv")
                    && Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())
                    && systemGroupEntity.getPvProfitController()) {
                showPvPower = true;
            }
            if (noticeEntity.getType().contains("wind")
                    && Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())
                    && Boolean.TRUE.equals(systemGroupEntity.getWindEarningsController())) {
                showWindPower = true;
            }
            if (noticeEntity.getType().contains("waster")
                    && Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration())
                    && Boolean.TRUE.equals(systemGroupEntity.getWasterEarningsController())) {
                showWasterPower = true;
            }
            // 处理一下 非汇总的 分组储能的
            groupYearBillReportDatas.forEach(
                    groupYearBillReportData -> {
                        groupYearBillReportData.setShowEnergyStorage(true);
                        groupYearBillReportData
                                .getYearBillReports()
                                .forEach(
                                        yearBillReport -> yearBillReport.setDemandProfitList(null));
                    });
            collectYearBillReportData.setProjectId(projectId);

            // 1.4.1 修复年账单 多个用户不同type, 如果第一个用户只有ems 那么会导致把需量list 设置为null 从而导致后面有show demand的用户也没有数据了
            // 所以这里改成 根据不同用户的不同的展示去控制, 不动原始数据
            collectYearBillReportData.setShowEnergyStorage(showEnergyStorage);
            collectYearBillReportData.setShowDemandControl(showDemandControl);
            collectYearBillReportData.setShowPvPower(showPvPower);
            collectYearBillReportData.setShowWindPower(showWindPower);
            collectYearBillReportData.setShowWasterPower(showWasterPower);
            noticeEmailService.sendYearReportEmail(() -> yearBillReportDatas, emailList);
        }
    }

    private YearBillReportData yearBillGroupReportDataBuild(
            String projectId,
            long start,
            Pair<Long, Long> startEndTimePair,
            List<String> deviceIds,
            ZoneOffset zoneOffset,
            ProjectEntity projectEntity,
            LocalDateTime startDateTime,
            GroupEntity systemGroupEntity,
            boolean isCollect) {
        List<ElectricEntity> electricEntities =
                getElectricEntitys(projectId, startEndTimePair, deviceIds);
        // 获取 一年12个月的map key = 1,2,3..
        Map<Integer, List<ElectricEntity>> map = getYearOfMonthMap();
        for (ElectricEntity electricEntity : electricEntities) {
            map.get(electricEntity.getMonth()).add(electricEntity);
        }
        List<ElectricEntity> monthEntities = new LinkedList<>();
        for (int i = 1; i <= 12; i++) {
            ElectricEntity monthEntity = OperationUtil.sumElectricEntities(map.get(i));
            Optional.ofNullable(monthEntity).ifPresent(e -> monthEntities.add(monthEntity));
        } //
        YearBillReportData yearBillReportData = new YearBillReportData();
        // 1.3.7 新增查询 收益分成数据
        IncomeDivideIntoEntity incomeDivideIntoEntity =
                incomeDivideIntoService.findInfoByProjectId(projectId);
        if (incomeDivideIntoEntity != null) {
            log.info(
                    "setIncomeDivideIntoEntity for yearBillReportData :{}", incomeDivideIntoEntity);
            yearBillReportData.setIncomeDivideIntoEntity(incomeDivideIntoEntity);
        }
        Map<Long, YearBillReport> yearBillReportMap =
                getYearBillReportMap(incomeDivideIntoEntity, monthEntities, zoneOffset);
        List<ElectricPriceEntity> list = electricPriceService.getPriceList(projectId);
        LocalDateTime startDataTime =
                LocalDateTime.ofInstant(Instant.ofEpochSecond(start), zoneOffset);
        // 只有汇总的才需要
        if (isCollect) {
            demandProfit(
                    projectEntity,
                    start,
                    zoneOffset,
                    startDateTime,
                    startDataTime,
                    yearBillReportMap,
                    list);
            renewableProfitYear(projectEntity, systemGroupEntity, yearBillReportMap);
            //            pvOrWindProfit(
            //                    projectId, zoneOffset, systemGroupEntity, yearBillReportMap,
            // MeterTypeEnum.PV);
            //            pvOrWindProfit(
            //                    projectId,
            //                    zoneOffset,
            //                    systemGroupEntity,
            //                    yearBillReportMap,
            //                    MeterTypeEnum.WIND);
        }
        yearBillReportData.setYearBillReports(
                yearBillReportMap.values().stream()
                        .sorted(Comparator.comparingLong(YearBillReport::getOrder))
                        .collect(Collectors.toList()));

        return yearBillReportData;
    }

    private void windProfit(
            String projectId,
            long start,
            ZoneOffset zoneOffset,
            GroupEntity systemGroupEntity,
            LocalDateTime startDataTime,
            Map<Long, YearBillReport> yearBillReportMap,
            List<ElectricPriceEntity> list) {
        // 风电收益
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())) {
            Map<Long, Double> windPowerMap =
                    getPvOrWindPower(
                            start,
                            startDataTime.plusYears(1).toEpochSecond(zoneOffset),
                            "4",
                            projectId);
            Map<Long, Double> onLinePowerMap =
                    getPvOrWindPower(
                            start,
                            startDataTime.plusYears(1).toEpochSecond(zoneOffset),
                            "2",
                            projectId);
            for (Long key : windPowerMap.keySet()) {
                double power = windPowerMap.get(key);
                YearBillReport yearBillReport = yearBillReportMap.get(key);
                if (yearBillReport == null) {
                    continue;
                }
                yearBillReport.setWindPower(String.format("%.5f", power));
                for (ElectricPriceEntity electricPriceEntity : list) {
                    boolean isToNow =
                            (key >= electricPriceEntity.getStartDate())
                                    && (electricPriceEntity.getEndDate() == null);
                    boolean isBetween =
                            isToNow
                                    || (key >= electricPriceEntity.getStartDate())
                                            && (key <= electricPriceEntity.getEndDate());
                    if (!isBetween) {
                        continue;
                    }
                    if (systemGroupEntity.getWindPowerModel() == 2) {
                        double self = power - onLinePowerMap.get(key);
                        if (self > 0) {
                            yearBillReport.setWindPowerSettleAmount(
                                    String.format(
                                            "%.5f",
                                            (electricPriceEntity.getWindSelfPrice()
                                                                    + electricPriceEntity
                                                                                    .getWindSubsidyPrice()
                                                                            * 0.42)
                                                            * self
                                                    + (electricPriceEntity.getWindSubsidyPrice()
                                                                            * 0.42
                                                                    + electricPriceEntity
                                                                            .getWindDfPrice())
                                                            * onLinePowerMap.get(key)));
                        } else {
                            yearBillReport.setWindPowerSettleAmount(
                                    String.format(
                                            "%.5f",
                                            (electricPriceEntity.getWindSubsidyPrice() * 0.42
                                                            + electricPriceEntity.getWindDfPrice())
                                                    * onLinePowerMap.get(key)));
                        }
                    } else {
                        yearBillReport.setWindPowerSettleAmount(
                                String.format("%.5f", electricPriceEntity.getWindPrice() * power));
                    }
                    break;
                }
            }
        }
    }

    private void renewableProfitYear(
            ProjectEntity project,
            GroupEntity systemGroupEntity,
            Map<Long, YearBillReport> yearBillReportMap) {
        ZoneOffset zoneOffset = MyTimeUtil.getZoneOffsetFromZoneCode(project.getTimezone());
        List<RenewableTypeReportEnum> renewableTypeReportEnums =
                ProjectMeterReportUtils.getRenewableTypeReportEnumByEnable(systemGroupEntity);
        for (RenewableTypeReportEnum renewableTypeReportEnum : renewableTypeReportEnums) {
            yearBillReportMap.forEach(
                    (monthKey, yearBillReport) -> {
                        LocalDateTime localDateTime =
                                Instant.ofEpochSecond(monthKey)
                                        .atOffset(zoneOffset)
                                        .toLocalDateTime();
                        RenewableProfitRequest request =
                                new RenewableProfitRequest(
                                        monthKey,
                                        localDateTime.plusMonths(1).toEpochSecond(zoneOffset),
                                        project.getId(),
                                        renewableTypeReportEnum.name());
                        RenewableProfitEntity renewableProfitEntity =
                                renewableIncomeQueryService.queryRenewableTotalProfit(request);
                        RenewableProfitVo renewableProfitVo = new RenewableProfitVo();
                        BeanUtils.copyProperties(renewableProfitEntity, renewableProfitVo);
                        // 根据不同的 类型取不同的字段
                        CalculateTypeEnum calculateTypeEnum =
                                RenewableTypeReportEnum.transform(renewableTypeReportEnum);
                        RenewableModelEnum model =
                                RenewableModelEnum.getRenewableModel(
                                        systemGroupEntity, calculateTypeEnum);

                        RenewableReportData renewableReportData =
                                new RenewableReportData()
                                        .setPower(
                                                renewableProfitEntity.getTotalDischargeQuantity());
                        // 根据 不同的模式 和 CalculateType 去取到 对应的收益 交给processor
                        renewableModelPriceFormulaComponent.modelBenefitCollect(
                                calculateTypeEnum,
                                model,
                                renewableProfitVo,
                                new RenewableModelPriceFormulaComponent
                                        .ModelBenefitCollectPostProcessor() {
                                    @Override
                                    public void renewableBenefitPostProcessor(double benefit) {
                                        renewableReportData.setPowerSettleAmount(benefit);
                                    }
                                });
                        fillRenewableReportDataToYear(
                                renewableTypeReportEnum, yearBillReport, renewableReportData);
                    });
        }
    }

    private void fillRenewableReportDataToYear(
            RenewableTypeReportEnum renewableTypeReportEnum,
            YearBillReport yearBillReport,
            RenewableReportData renewableReportData) {
        switch (renewableTypeReportEnum) {
            case PV:
                yearBillReport.setPvPower(String.format("%.5f", renewableReportData.getPower()));
                yearBillReport.setPvPowerSettleAmount(
                        String.format("%.5f", renewableReportData.getPowerSettleAmount()));
                break;
            case WIND:
                yearBillReport.setWindPower(String.format("%.5f", renewableReportData.getPower()));
                yearBillReport.setWindPowerSettleAmount(
                        String.format("%.5f", renewableReportData.getPowerSettleAmount()));

                break;
            case WASTER:
                yearBillReport.setWasterPower(
                        String.format("%.5f", renewableReportData.getPower()));
                yearBillReport.setWasterPowerSettleAmount(
                        String.format("%.5f", renewableReportData.getPowerSettleAmount()));
                break;
        }
    }

    private void pvOrWindProfit(
            String projectId,
            ZoneOffset zoneOffset,
            GroupEntity systemGroupEntity,
            Map<Long, YearBillReport> yearBillReportMap,
            MeterTypeEnum meterTypeEnum) {

        // 2023-11-17 11:12:47 add pv收益控制
        boolean pvProfitController = systemGroupEntity.getPvProfitController();
        boolean windFlag =
                Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())
                        && Boolean.TRUE.equals(systemGroupEntity.getWindEarningsController());
        boolean wasterFlag =
                Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration())
                        && Boolean.TRUE.equals(systemGroupEntity.getWasterEarningsController());
        // TODO 这里要改一下, pv wind 和 waster 需要根据是否开启了开关才去查询

        // 光伏收益
        if (Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())
                && pvProfitController) {
            // 直接查询缓存中的数据
            yearBillReportMap.forEach(
                    (monthKey, yearBillReport) -> {
                        LocalDateTime localDateTime =
                                Instant.ofEpochSecond(monthKey)
                                        .atOffset(zoneOffset)
                                        .toLocalDateTime();
                        RenewableProfitRequest request =
                                new RenewableProfitRequest(
                                        monthKey,
                                        localDateTime.plusMonths(1).toEpochSecond(zoneOffset),
                                        projectId,
                                        meterTypeEnum.name());
                        RenewableProfitEntity renewableProfitEntity =
                                renewableIncomeQueryService.queryRenewableTotalProfit(request);

                        //                        double totalPower =
                        // pvWindProfitEntity.getTotalDischargeSelfQuantity();
                        double totalPower = renewableProfitEntity.getTotalDischargeQuantity();

                        if (meterTypeEnum.name().equals(MeterTypeEnum.PV.name())) {
                            RenewableProfitVo pvProfitVo = new RenewableProfitVo();
                            BeanUtils.copyProperties(renewableProfitEntity, pvProfitVo);
                            RenewableModelEnum model =
                                    RenewableModelEnum.getModel(
                                            systemGroupEntity.getPhotovoltaicModel());
                            yearBillReport.setPvPower(String.format("%.5f", totalPower));
                            // 根据 不同的模式 和 CalculateType 去取到 对应的收益 交给processor
                            renewableModelPriceFormulaComponent.modelBenefitCollect(
                                    CalculateTypeEnum.PV,
                                    model,
                                    pvProfitVo,
                                    new RenewableModelPriceFormulaComponent
                                            .ModelBenefitCollectPostProcessor() {
                                        @Override
                                        public void renewableBenefitPostProcessor(double benefit) {
                                            yearBillReport.setPvPowerSettleAmount(
                                                    String.format("%.5f", benefit));
                                        }
                                    });
                        } else if (meterTypeEnum.name().equals(MeterTypeEnum.WIND.name())) {
                            RenewableProfitVo windProfitVo = new RenewableProfitVo();
                            BeanUtils.copyProperties(renewableProfitEntity, windProfitVo);
                            RenewableModelEnum model =
                                    RenewableModelEnum.getModel(
                                            systemGroupEntity.getWindPowerModel());
                            yearBillReport.setWindPower(String.format("%.5f", totalPower));
                            renewableModelPriceFormulaComponent.modelBenefitCollect(
                                    CalculateTypeEnum.WIND,
                                    model,
                                    windProfitVo,
                                    new RenewableModelPriceFormulaComponent
                                            .ModelBenefitCollectPostProcessor() {
                                        @Override
                                        public void renewableBenefitPostProcessor(double benefit) {
                                            yearBillReport.setWindPowerSettleAmount(
                                                    String.format("%.5f", benefit));
                                        }
                                    });
                        }
                    });
        }
    }

    private void demandProfit(
            ProjectEntity project,
            //            String projectId,
            long start,
            ZoneOffset zoneOffset,
            LocalDateTime startDateTime,
            LocalDateTime startDataTime,
            Map<Long, YearBillReport> yearBillReportMap,
            List<ElectricPriceEntity> list) {
        String projectId = project.getId();
        // 需量控制收益
        List<GroupEntity> demandGroupEntities = groupService.queryEnableDemandIncome(projectId);
        List<String> demandGroupIds =
                demandGroupEntities.stream().map(GroupEntity::getId).collect(Collectors.toList());
        if (!demandGroupIds.isEmpty()) {
            Map<String, Map<Long, Double>> controlPowerMap =
                    operationProfitService.getDemandIncomePowerFromDb(
                            new RangeRequest()
                                    .setStartDate(start)
                                    .setEndDate(
                                            startDateTime.plusYears(1).toEpochSecond(zoneOffset)),
                            projectId);
            //                    operationProfitService.getDemandIncomePower(
            //                            start,
            // startDataTime.plusYears(1).toEpochSecond(zoneOffset), projectId);
            Map<String, String> groupNameMap =
                    demandGroupEntities.stream()
                            .collect(Collectors.toMap(GroupEntity::getId, GroupEntity::getName));
            int year = startDateTime.getYear();
            List<Long> timeList = new ArrayList<>(12);
            for (int i = 1; i <= 12; i++) {
                LocalDateTime localDateTime = LocalDateTime.of(year, i, 1, 0, 0, 0);
                timeList.add(localDateTime.toEpochSecond(zoneOffset));
            }
            for (Long time : timeList) {
                // 2025-01-02 18:27:49 pangu内卷群里问 周涛 一个月内的需量价格是一样的吗 回复: 是的, 所以这里直接获取当月匹配的这个电价就行了
                // 这里是支持 电价托管的,  而原本 传递进来的list的电价列表是不支持 电价托管查询 导致需量的电价没有 从而没有需量的相关收益
                //                ElectricPriceEntity electricPriceMatch =
                //                        electricPriceService.getElectricPriceMatch(projectId,
                // time);
                //                if (electricPriceMatch == null ||
                // electricPriceMatch.getDemandPrice() == null) {
                //                    log.error("年账单生成过程中 未找到匹配的{} 电价设置 无法计算该月的需量收益 ", time);
                //                    continue;
                //                }
                YearBillReport yearBillReport = yearBillReportMap.get(time);
                //                YearBillReport yearBillReport = yearBillReportMap.get(time);
                if (yearBillReport == null) {
                    continue;
                }
                double totalPower = 0d;
                double totalProfit = 0d;
                for (String groupId : demandGroupIds) {
                    GroupDemandProfit groupDemandProfit = new GroupDemandProfit();
                    // 这里做了个简单的处理 因为yearBillReportMap改成从db里获取了 ,但是给的key是 月末的时间, 而time的时间却是月初 转换一下
                    Double power = controlPowerMap.get(groupId).get(time);
                    //                    Double power =
                    //                            controlPowerMap
                    //                                    .get()
                    //                                    .get(
                    //                                            MyTimeUtil.getMonthEndTimestamp(
                    //                                                    time,
                    // ZoneId.of(project.getTimezone())));
                    double profit = 0d;
                    if (power == null) {
                        groupDemandProfit.setGroupName(groupNameMap.get(groupId));
                        groupDemandProfit.setDemandControlPower(0d);
                        groupDemandProfit.setDemandControlBenefit(0d);
                        yearBillReport.getDemandProfitList().add(groupDemandProfit);
                        continue;
                    }
                    //
                    // groupDemandProfit.setDemandControlPrice(electricPriceMatch.getDemandPrice());
                    //                    profit = power * electricPriceMatch.getDemandPrice();
                    //                    totalPower += power;
                    //                    totalProfit += profit;
                    for (ElectricPriceEntity electricPriceEntity : list) {
                        boolean isToNow =
                                (time >= electricPriceEntity.getStartDate())
                                        && (electricPriceEntity.getEndDate() == null);
                        boolean isBetween =
                                isToNow
                                        || (time >= electricPriceEntity.getStartDate())
                                                && (time <= electricPriceEntity.getEndDate());
                        if (isBetween) {
                            groupDemandProfit.setDemandControlPrice(
                                    electricPriceEntity.getDemandPrice());
                            profit = power * electricPriceEntity.getDemandPrice();
                            totalPower += power;
                            totalProfit += profit;
                        }
                    }
                    groupDemandProfit.setGroupName(groupNameMap.get(groupId));
                    groupDemandProfit.setDemandControlPower(power);
                    groupDemandProfit.setDemandControlBenefit(profit);
                    yearBillReport.getDemandProfitList().add(groupDemandProfit);
                }
                yearBillReport.setDownDemandPower(String.format("%.5f", totalPower));
                yearBillReport.setDemandSettleAmount(String.format("%.5f", totalProfit));
            }
        }
    }

    private static Map<Long, YearBillReport> getYearBillReportMap(
            IncomeDivideIntoEntity incomeDivideIntoEntity,
            List<ElectricEntity> monthEntities,
            ZoneOffset zoneOffset) {
        Map<Long, YearBillReport> yearBillReportMap = new LinkedHashMap<>(12);
        for (ElectricEntity electricEntity : monthEntities) {
            YearBillReport yearBillReport = new YearBillReport();
            yearBillReport.setSpikeCharge(
                    String.format("%.5f", electricEntity.getTipChargeQuantity()));
            yearBillReport.setSpikeChargePrice(
                    String.format("%.5f", electricEntity.getTipBuyPrice()));
            yearBillReport.setSpikeChargeAmount(
                    String.format("%.5f", electricEntity.getTipChargeCost()));
            yearBillReport.setPeakCharge(
                    String.format("%.5f", electricEntity.getPeakChargeQuantity()));
            yearBillReport.setPeakChargePrice(
                    String.format("%.5f", electricEntity.getPeakBuyPrice()));
            yearBillReport.setPeakChargeAmount(
                    String.format("%.5f", electricEntity.getPeakChargeCost()));
            yearBillReport.setFlatCharge(
                    String.format("%.5f", electricEntity.getFlatChargeQuantity()));
            yearBillReport.setFlatChargePrice(
                    String.format("%.5f", electricEntity.getFlatBuyPrice()));
            yearBillReport.setFlatChargeAmount(
                    String.format("%.5f", electricEntity.getFlatChargeCost()));
            yearBillReport.setValleyCharge(
                    String.format("%.5f", electricEntity.getVallyChargeQuantity()));
            yearBillReport.setValleyChargePrice(
                    String.format("%.5f", electricEntity.getVallyBuyPrice()));
            yearBillReport.setValleyChargeAmount(
                    String.format("%.5f", electricEntity.getVallyChargeCost()));
            yearBillReport.setDeepValleyCharge(
                    String.format("%.5f", electricEntity.getDeepVallyChargeQuantity()));
            yearBillReport.setDeepValleyChargePrice(
                    String.format("%.5f", electricEntity.getDeepVallyBuyPrice()));
            yearBillReport.setDeepValleyChargeAmount(
                    String.format("%.5f", electricEntity.getDeepVallyChargeCost()));
            yearBillReport.setSpikeDischarge(
                    String.format("%.5f", electricEntity.getTipDischargeQuantity()));
            yearBillReport.setSpikeDischargeAmount(
                    String.format("%.5f", electricEntity.getTipDischargeBenefit()));
            yearBillReport.setPeakDischarge(
                    String.format("%.5f", electricEntity.getPeakDischargeQuantity()));
            yearBillReport.setPeakDischargeAmount(
                    String.format("%.5f", electricEntity.getPeakDischargeBenefit()));
            yearBillReport.setFlatDischarge(
                    String.format("%.5f", electricEntity.getFlatDischargeQuantity()));
            yearBillReport.setFlatDischargeAmount(
                    String.format("%.5f", electricEntity.getFlatDischargeBenefit()));
            yearBillReport.setValleyDischarge(
                    String.format("%.5f", electricEntity.getVallyDischargeQuantity()));
            yearBillReport.setValleyDischargeAmount(
                    String.format("%.5f", electricEntity.getVallyDischargeBenefit()));
            yearBillReport.setDeepValleyDischarge(
                    String.format("%.5f", electricEntity.getDeepVallyDischargeQuantity()));
            yearBillReport.setDeepValleyDischargeAmount(
                    String.format("%.5f", electricEntity.getDeepVallyDischargeBenefit()));
            yearBillReport.setTotalCharge(
                    String.format("%.5f", electricEntity.getTotalChargeQuantity()));
            yearBillReport.setTotalDischarge(
                    String.format("%.5f", electricEntity.getTotalDischargeQuantity()));
            double benefit =
                    Double.parseDouble(yearBillReport.getPeakDischargeAmount())
                            + Double.parseDouble(yearBillReport.getValleyDischargeAmount())
                            + Double.parseDouble(yearBillReport.getFlatDischargeAmount())
                            + Double.parseDouble(yearBillReport.getSpikeDischargeAmount())
                            + Double.parseDouble(yearBillReport.getDeepValleyDischargeAmount())
                            - Double.parseDouble(yearBillReport.getPeakChargeAmount())
                            - Double.parseDouble(yearBillReport.getValleyChargeAmount())
                            - Double.parseDouble(yearBillReport.getFlatChargeAmount())
                            - Double.parseDouble(yearBillReport.getSpikeChargeAmount())
                            - Double.parseDouble(yearBillReport.getDeepValleyChargeAmount());
            yearBillReport.setWin(String.format("%.5f", benefit));
            if (incomeDivideIntoEntity != null) {
                yearBillReport.setCustomerWin(
                        String.format(
                                "%.5f", incomeDivideIntoEntity.getCustomerOccupancy() * benefit));
                yearBillReport.setEmployWin(
                        String.format(
                                "%.5f",
                                benefit - incomeDivideIntoEntity.getCustomerOccupancy() * benefit));
            } else {
                yearBillReport.setCustomerWin("0.0");
                yearBillReport.setEmployWin("0.0");
            }
            yearBillReport.setSpikeDischargePrice(
                    String.format("%.5f", electricEntity.getTipSellPrice()));
            yearBillReport.setValleyDischargePrice(
                    String.format("%.5f", electricEntity.getVallySellPrice()));
            yearBillReport.setFlatDischargePrice(
                    String.format("%.5f", electricEntity.getFlatSellPrice()));
            yearBillReport.setPeakDischargePrice(
                    String.format("%.5f", electricEntity.getPeakSellPrice()));
            yearBillReport.setDeepValleyDischargePrice(
                    String.format("%.5f", electricEntity.getDeepVallySellPrice()));
            LocalDateTime localDateTimeTemp =
                    LocalDateTime.of(
                            electricEntity.getYear(), electricEntity.getMonth(), 1, 0, 0, 0);
            long time = localDateTimeTemp.toEpochSecond(zoneOffset);
            yearBillReport.setTime(localDateTimeTemp.getMonthValue() + "月");
            yearBillReport.setOrder(time);
            yearBillReportMap.put(time, yearBillReport);
        }
        return yearBillReportMap;
    }

    @NotNull
    private static Map<Integer, List<ElectricEntity>> getYearOfMonthMap() {
        Map<Integer, List<ElectricEntity>> map = new HashMap<>(12);
        for (int i = 1; i <= 12; i++) {
            List<ElectricEntity> list = new ArrayList<>(12);
            map.put(i, list);
        }
        return map;
    }

    private List<ElectricEntity> getElectricEntitys(
            String projectId, Pair<Long, Long> startEndTimePair, List<String> deviceIds) {
        ElectricRequest electricRequest =
                new ElectricRequest(
                        startEndTimePair.getFirst(),
                        startEndTimePair.getSecond(),
                        projectId,
                        deviceIds);
        return electricService.queryElectricEveryDayProfit(electricRequest);
    }

    public static Pair<Long, Long> getActualStartEndTime(ProjectEntity projectEntity, Long start) {
        if (projectEntity.getCreateTime() > start) {
            start = projectEntity.getCreateTime();
        }
        ZoneOffset zoneOffset = MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone());
        LocalDateTime startDateTime =
                LocalDateTime.ofInstant(Instant.ofEpochSecond(start), zoneOffset);
        long end = startDateTime.plusYears(1).toEpochSecond(zoneOffset);
        if (end > LocalDateTime.now().toEpochSecond(zoneOffset)) {
            end = LocalDateTime.now().toEpochSecond(zoneOffset);
        }
        return Pair.of(start, end);
    }

    @NotNull
    private DetailReportData getDetailReportData(
            GroupEntity systemGroupEntity,
            ProjectEntity projectEntity,
            LocalDateTime startDataTime,
            long start,
            long end,
            String projectId,
            GroupEntity groupEntity,
            List<ElectricEntity> electricEntities,
            boolean groupEarningFlag) {
        DetailReportData detailReportData = new DetailReportData();
        if (!groupEarningFlag) {
            detailReportData.setDetailSheetName("明细");
            detailReportData.setTitleName(
                    projectEntity.getProjectName() + startDataTime.getMonthValue() + "月用电明细");
        } else {
            // 支持 分组的
            detailReportData.setDetailSheetName("明细(" + groupEntity.getName() + ")");
            detailReportData.setTitleName(
                    projectEntity.getProjectName()
                            + startDataTime.getMonthValue()
                            + "月用电明细("
                            + groupEntity.getName()
                            + ")");
        }
        Map<Long, DetailReport> detailReportMap = new HashMap<>(31);
        long detailStartTime = start;
        if (projectEntity.getCreateTime() >= start) {
            detailStartTime = projectEntity.getCreateTime();
        }
        while (detailStartTime < end) {
            DetailReport detailReport = new DetailReport();
            LocalDateTime localDateTimeTemp =
                    LocalDateTime.ofInstant(
                            Instant.ofEpochSecond(detailStartTime),
                            MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
            detailReport.setTime(
                    localDateTimeTemp.getMonthValue()
                            + "月"
                            + localDateTimeTemp.getDayOfMonth()
                            + "日");
            detailReport.setOrder(detailStartTime);
            detailReportMap.put(detailStartTime, detailReport);
            detailStartTime = detailStartTime + EmsConstants.ONE_DAY_SECOND;
        }
        for (ElectricEntity electricEntity : electricEntities) {
            DetailReport detailReport = detailReportMap.get(electricEntity.getTime());
            Optional.ofNullable(detailReport)
                    .ifPresent(
                            e -> {
                                e.setSpikeCharge(
                                        String.format(
                                                "%.5f", electricEntity.getTipChargeQuantity()));
                                e.setPeakCharge(
                                        String.format(
                                                "%.5f", electricEntity.getPeakChargeQuantity()));
                                e.setFlatCharge(
                                        String.format(
                                                "%.5f", electricEntity.getFlatChargeQuantity()));
                                e.setValleyCharge(
                                        String.format(
                                                "%.5f", electricEntity.getVallyChargeQuantity()));
                                e.setDeepValleyCharge(
                                        String.format(
                                                "%.5f",
                                                electricEntity.getDeepVallyChargeQuantity()));
                                e.setSpikeDischarge(
                                        String.format(
                                                "%.5f", electricEntity.getTipDischargeQuantity()));
                                e.setPeakDischarge(
                                        String.format(
                                                "%.5f", electricEntity.getPeakDischargeQuantity()));
                                e.setFlatDischarge(
                                        String.format(
                                                "%.5f", electricEntity.getFlatDischargeQuantity()));
                                e.setValleyDischarge(
                                        String.format(
                                                "%.5f",
                                                electricEntity.getVallyDischargeQuantity()));
                                e.setDeepValleyDischarge(
                                        String.format(
                                                "%.5f",
                                                electricEntity.getDeepVallyDischargeQuantity()));
                                e.setTotalCharge(
                                        String.format(
                                                "%.5f", electricEntity.getTotalChargeQuantity()));
                                e.setTotalDischarge(
                                        String.format(
                                                "%.5f",
                                                electricEntity.getTotalDischargeQuantity()));
                            });
        }
        if (Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())) {
            setPvDischarge(start, end, projectId, detailReportMap);
        }
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())) {
            setWindDischarge(start, end, projectId, detailReportMap);
        }
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration())) {
            setWasterDischarge(start, end, projectId, detailReportMap);
        }

        List<DetailReport> detailReports = new ArrayList<>(detailReportMap.values());
        detailReports =
                detailReports.stream()
                        .sorted(Comparator.comparing(DetailReport::getOrder))
                        .collect(Collectors.toList());
        detailReportData.setDetailReports(detailReports);
        return detailReportData;
    }

    @NotNull
    private List<EnergyStorageData> getElectricData(
            ProjectEntity projectEntity,
            long start,
            String projectId,
            List<ElectricEntity> electricEntities,
            GroupEntity groupEntity,
            boolean groupEarningFlag) {
        EnergyStorageData peakChargeData = new EnergyStorageData();
        peakChargeData.setMeterTime(PricePeriodEnum.PEEK.getName());
        peakChargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.CHARGE.getName() + ")"
                        : PricePeriodEnum.CHARGE.getName());
        EnergyStorageData vallyChargeData = new EnergyStorageData();
        vallyChargeData.setMeterTime(PricePeriodEnum.VALLEY.getName());
        vallyChargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.CHARGE.getName() + ")"
                        : PricePeriodEnum.CHARGE.getName());
        EnergyStorageData flatChargeData = new EnergyStorageData();
        flatChargeData.setMeterTime(PricePeriodEnum.FLAT.getName());

        flatChargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.CHARGE.getName() + ")"
                        : PricePeriodEnum.CHARGE.getName());
        EnergyStorageData tipChargeData = new EnergyStorageData();
        tipChargeData.setMeterTime(PricePeriodEnum.TIP.getName());
        tipChargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.CHARGE.getName() + ")"
                        : PricePeriodEnum.CHARGE.getName());
        EnergyStorageData deepChargeData = new EnergyStorageData();
        deepChargeData.setMeterTime(PricePeriodEnum.DEEP_VALLEY.getName());
        deepChargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.CHARGE.getName() + ")"
                        : PricePeriodEnum.CHARGE.getName());
        EnergyStorageData peakDischargeData = new EnergyStorageData();
        peakDischargeData.setMeterTime(PricePeriodEnum.PEEK.getName());
        peakDischargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.DISCHARGE.getName() + ")"
                        : PricePeriodEnum.DISCHARGE.getName());
        EnergyStorageData vallyDischargeData = new EnergyStorageData();
        vallyDischargeData.setMeterTime(PricePeriodEnum.VALLEY.getName());
        vallyDischargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.DISCHARGE.getName() + ")"
                        : PricePeriodEnum.DISCHARGE.getName());
        EnergyStorageData flatDischargeData = new EnergyStorageData();
        flatDischargeData.setMeterTime(PricePeriodEnum.FLAT.getName());
        flatDischargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.DISCHARGE.getName() + ")"
                        : PricePeriodEnum.DISCHARGE.getName());
        EnergyStorageData tipDischargeData = new EnergyStorageData();
        tipDischargeData.setMeterTime(PricePeriodEnum.TIP.getName());
        tipDischargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.DISCHARGE.getName() + ")"
                        : PricePeriodEnum.DISCHARGE.getName());
        EnergyStorageData deepDischargeData = new EnergyStorageData();
        deepDischargeData.setMeterTime(PricePeriodEnum.DEEP_VALLEY.getName());
        deepDischargeData.setMeterType(
                groupEarningFlag
                        ? groupEntity.getName() + "(" + PricePeriodEnum.DISCHARGE.getName() + ")"
                        : PricePeriodEnum.DISCHARGE.getName());
        ElectricRequest electricRequest = new ElectricRequest();
        electricRequest.setProjectId(projectId);
        if (!groupEarningFlag) {
            List<String> ids =
                    OperationUtil.getIncomeDeviceIds(projectId, deviceService, ammeterService);
            // 增加补值的分组 id
            ids.addAll(
                    groupService.queryGroup(projectId).stream()
                            .map(GroupEntity::getId)
                            .collect(Collectors.toList()));
            electricRequest.setDeviceIdList(ids);
        } else {
            // 如果是 分组储能收益 改成 查询某个分组下面的
            electricRequest.setDeviceIdList(
                    OperationUtil.getIncomeDeviceIdsForGroup(
                            projectId, groupEntity.getId(), groupService));
        }
        if (projectEntity.getCreateTime() < start) {
            electricRequest.setStart(projectEntity.getCreateTime());
            electricRequest.setEnd(start);
            ElectricEntity electricEntityTotal =
                    electricService.queryElectricTotalProfit(electricRequest);
            Optional.ofNullable(electricEntityTotal)
                    .ifPresent(
                            e -> {
                                tipChargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal.getTipChargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                peakChargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal.getPeakChargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                flatChargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal.getFlatChargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                vallyChargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal
                                                                .getVallyChargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                deepChargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal
                                                                .getDeepVallyChargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                tipDischargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal
                                                                .getTipDischargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                peakDischargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal
                                                                .getPeakDischargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                flatDischargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal
                                                                .getFlatDischargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                vallyDischargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal
                                                                .getVallyDischargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                                deepDischargeData.setPreMonthDisplay(
                                        BigDecimal.valueOf(
                                                        electricEntityTotal
                                                                .getDeepVallyDischargeQuantity())
                                                .setScale(5, RoundingMode.UP)
                                                .doubleValue());
                            });
        }
        ElectricEntity electricEntityMonth = OperationUtil.sumElectricEntities(electricEntities);
        if (electricEntityMonth == null) {
            electricEntityMonth = new ElectricEntity();
        }
        double tipBuyPrice = electricEntityMonth.getTipBuyPrice();
        double tipSellPrice = electricEntityMonth.getTipSellPrice();
        tipChargeData.setMeterPrice(
                BigDecimal.valueOf(tipBuyPrice).setScale(5, RoundingMode.UP).doubleValue());
        tipDischargeData.setMeterPrice(
                BigDecimal.valueOf(tipSellPrice).setScale(5, RoundingMode.UP).doubleValue());
        tipChargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getTipChargeQuantity()
                                        + tipChargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        tipChargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getTipChargeCost())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        tipChargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getTipChargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        double peakBuyPrice = electricEntityMonth.getPeakBuyPrice();
        double peakSellPrice = electricEntityMonth.getPeakSellPrice();
        peakChargeData.setMeterPrice(
                BigDecimal.valueOf(peakBuyPrice).setScale(5, RoundingMode.UP).doubleValue());
        peakDischargeData.setMeterPrice(
                BigDecimal.valueOf(peakSellPrice).setScale(5, RoundingMode.UP).doubleValue());
        peakChargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getPeakChargeQuantity()
                                        + peakChargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        peakChargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getPeakChargeCost())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        peakChargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getPeakChargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        double flatBuyPrice = electricEntityMonth.getFlatBuyPrice();
        double flatSellPrice = electricEntityMonth.getFlatSellPrice();
        flatChargeData.setMeterPrice(
                BigDecimal.valueOf(flatBuyPrice).setScale(5, RoundingMode.UP).doubleValue());
        flatDischargeData.setMeterPrice(
                BigDecimal.valueOf(flatSellPrice).setScale(5, RoundingMode.UP).doubleValue());
        flatChargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getFlatChargeQuantity()
                                        + flatChargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        flatChargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getFlatChargeCost())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        flatChargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getFlatChargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        double vallyBuyPrice = electricEntityMonth.getVallyBuyPrice();
        double vallySellPrice = electricEntityMonth.getVallySellPrice();
        vallyChargeData.setMeterPrice(
                BigDecimal.valueOf(vallyBuyPrice).setScale(5, RoundingMode.UP).doubleValue());
        vallyDischargeData.setMeterPrice(
                BigDecimal.valueOf(vallySellPrice).setScale(5, RoundingMode.UP).doubleValue());
        vallyChargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getVallyChargeQuantity()
                                        + vallyChargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        vallyChargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getVallyChargeCost())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        vallyChargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getVallyChargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        double deepVallyBuyPrice = electricEntityMonth.getDeepVallyBuyPrice();
        double deepVallySellPrice = electricEntityMonth.getDeepVallySellPrice();
        deepChargeData.setMeterPrice(
                BigDecimal.valueOf(deepVallyBuyPrice).setScale(5, RoundingMode.UP).doubleValue());
        deepDischargeData.setMeterPrice(
                BigDecimal.valueOf(deepVallySellPrice).setScale(5, RoundingMode.UP).doubleValue());
        deepChargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getDeepVallyChargeQuantity()
                                        + deepChargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        deepChargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getDeepVallyChargeCost())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        deepChargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getDeepVallyChargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        tipDischargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getTipDischargeQuantity()
                                        + tipDischargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        tipDischargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getTipDischargeBenefit())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        tipDischargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getTipDischargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        peakDischargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getPeakDischargeQuantity()
                                        + peakDischargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        peakDischargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getPeakDischargeBenefit())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        peakDischargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getPeakDischargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        flatDischargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getFlatDischargeQuantity()
                                        + flatDischargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        flatDischargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getFlatDischargeBenefit())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        flatDischargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getFlatDischargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        vallyDischargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getVallyDischargeQuantity()
                                        + vallyDischargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        vallyDischargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getVallyDischargeBenefit())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        vallyDischargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getVallyDischargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        deepDischargeData.setNowMonthDisplay(
                BigDecimal.valueOf(
                                electricEntityMonth.getDeepVallyDischargeQuantity()
                                        + deepDischargeData.getPreMonthDisplay())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        deepDischargeData.setSettleAmount(
                BigDecimal.valueOf(electricEntityMonth.getDeepVallyDischargeBenefit())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        deepDischargeData.setPreMonthMeter(
                BigDecimal.valueOf(electricEntityMonth.getDeepVallyDischargeQuantity())
                        .setScale(5, RoundingMode.UP)
                        .doubleValue());
        List<EnergyStorageData> list = new ArrayList<>(10);
        list.add(tipDischargeData);
        list.add(peakDischargeData);
        list.add(flatDischargeData);
        list.add(vallyDischargeData);
        list.add(deepDischargeData);
        list.add(tipChargeData);
        list.add(peakChargeData);
        list.add(flatChargeData);
        list.add(vallyChargeData);
        list.add(deepChargeData);
        return list;
    }

    /**
     * 风电出
     *
     * @param start 起始
     * @param end 终结
     * @param projectId 项目id
     * @param detailReportMap 细节
     */
    public abstract void setWindDischarge(
            long start, long end, String projectId, Map<Long, DetailReport> detailReportMap);

    /**
     * 风电出
     *
     * @param start 起始
     * @param end 终结
     * @param projectId 项目id
     * @param detailReportMap 细节
     */
    public abstract void setWasterDischarge(
            long start, long end, String projectId, Map<Long, DetailReport> detailReportMap);

    /**
     * 光伏出
     *
     * @param start 起始
     * @param end 终结
     * @param projectId 项目id
     * @param detailReportMap 细节
     */
    public abstract void setPvDischarge(
            long start, long end, String projectId, Map<Long, DetailReport> detailReportMap);

    /**
     * 风电或者光伏出
     *
     * @param startDate 起始
     * @param endDate 终结
     * @param type 类型
     * @param projectId 项目id
     * @return Map<Long, Double>
     */
    public abstract Map<Long, Double> getPvOrWindPower(
            Long startDate, Long endDate, String type, String projectId);
}
