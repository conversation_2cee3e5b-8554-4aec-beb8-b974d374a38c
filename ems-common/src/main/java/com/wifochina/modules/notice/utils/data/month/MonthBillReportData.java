package com.wifochina.modules.notice.utils.data.month;

import com.wifochina.modules.notice.entity.IncomeDivideIntoEntity;
import com.wifochina.modules.notice.utils.data.common.CommonShow;
import com.wifochina.modules.notice.utils.data.common.PvPowerData;
import com.wifochina.modules.notice.utils.data.common.WasterPowerData;
import com.wifochina.modules.notice.utils.data.common.WindPowerData;
import com.wifochina.modules.operation.VO.GroupDemandProfit;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

/**
 * Created on 2023/9/19 18:40.
 *
 * <AUTHOR>
 */
@Data
@Accessors
public class MonthBillReportData extends CommonShow {

    private String projectId;
    @NotNull
    private String projectName;

    /**
     * 计费月份
     */
    @NotNull
    private String billingMonth;

    /**
     * 上次抄表日期
     */
    @NotNull
    private String preReadingMeterDate;

    /**
     * 本次抄表日期
     */
    @NotNull
    private String nowReadingMeterDate;

    /** 是否 忽略 储能充放电开关 */
    // private boolean showEnergyStorage = false;

    /**
     * 储能充放电
     */
    private List<EnergyStorageData> energyStorageDataList = null;

    /**
     * 新增的 分组的储能充放电map
     */
    private Map<String, List<EnergyStorageData>> groupEnergyStorageDateMap = null;

    /**
     * 是否忽略 需量控制 开关
     */
    // private boolean showDemandControl = false;

    private DemandControlData demandControlData;

    @ApiModelProperty(value = "分组需量")
    private Map<String, GroupDemandProfit> demandProfitMap;

    /**
     * 是否 忽略 PV发电 开关
     */
    // private boolean showPvPower = false;

    @NotNull
    private PvPowerData pvPowerData;

    /**
     * 是否 忽略 风力发电 开关
     */
    // private boolean showWindPower = false;

    @NotNull
    private WindPowerData windPowerData;
    /**
     * 余热发电
     */
    @NotNull
    private WasterPowerData wasterPowerData;

    /**
     * 月度总收益
     */
    @NotNull
    private String monTotalWin;

    /**
     * 1.3.7 new add
     */
    @ApiModelProperty(value = "收益分成数据实体")
    private IncomeDivideIntoEntity incomeDivideIntoEntity;

    @ApiModelProperty(value = "月度收益列表(只有在有收益分成的时候才用到这个字段)")
    private List<Double> monIncomeList;

    /**
     * 获取到 储能充放电 8条数据 前面两列都已经填充好的 List<EnergyStorageData> 可选
     *
     * @return : List<EnergyStorageData>
     */
    public final List<EnergyStorageData> getInitEnergyStorageDataList() {
        energyStorageDataList = new LinkedList<>();
        for (int i = 1; i <= 8; i++) {
            EnergyStorageData energyStorageData = new EnergyStorageData();
            if (i <= 5) {
                energyStorageData.setMeterType("出电");
            } else {
                energyStorageData.setMeterType("放电");
            }
            if (i == 1 || i == 6) {
                energyStorageData.setMeterTime("尖峰");
            } else if (i == 2 || i == 7) {
                energyStorageData.setMeterTime("峰段");
            } else if (i == 3 || i == 8) {
                energyStorageData.setMeterTime("平段");
            } else if (i == 4 || i == 9) {
                energyStorageData.setMeterTime("谷段");
            } else if (i == 5 || i == 10) {
                energyStorageData.setMeterTime("深谷");
            }
            energyStorageDataList.add(energyStorageData);
        }
        return energyStorageDataList;
    }

    /**
     * 标记是否需要按照分组展示
     */
    private boolean groupEarningsController;

    private double emsProfit;
}
