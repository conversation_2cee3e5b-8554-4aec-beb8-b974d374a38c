package com.wifochina.modules.notice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * Created on 2024/6/18 15:37.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("t_income_divide_into")
@ApiModel(value = "收益分成对象")
public class IncomeDivideIntoEntity {

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @ApiModelProperty("projectId")
    private String projectId;

    @ApiModelProperty("资方单位名称")
    private String nameOfEmployer;

    @ApiModelProperty("客户单位名称")
    private String nameOfCustomer;

    @ApiModelProperty("客户方收益占比%")
    private double customerOccupancy;
}
