package com.wifochina.modules.notice.utils.data.month;

import com.wifochina.modules.notice.utils.data.common.CommonShow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * Created on 2023/9/20 14:58.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DetailReportData extends CommonShow {
    @NotNull private String titleName;
    private List<DetailReport> detailReports;
    private String detailSheetName;
}
