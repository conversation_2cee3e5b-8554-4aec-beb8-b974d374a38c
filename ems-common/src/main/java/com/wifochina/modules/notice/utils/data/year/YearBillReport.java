package com.wifochina.modules.notice.utils.data.year;

import com.wifochina.modules.notice.utils.data.common.ReportListLineData;
import com.wifochina.modules.operation.VO.GroupDemandProfit;

import io.swagger.annotations.ApiModelProperty;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * Created on 2023/9/20 19:31.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode
public class YearBillReport implements ReportListLineData {

    private String time;

    /** 收益 */
    private String win;

    /** 客户方收益占比 */
    private String customerWin;

    /** 资方收益占比 */
    private String employWin;

    /** 尖峰放电量(度) */
    private String spikeDischarge;

    /** 尖峰电价(元/kWh) */
    private String spikeDischargePrice;

    /** 尖峰电价 结算金额 */
    private String spikeDischargeAmount;

    /** 峰段放电量(度) */
    private String peakDischarge;

    private String peakDischargePrice;
    private String peakDischargeAmount;

    /** 平段放电量(度) */
    private String flatDischarge;

    private String flatDischargePrice;
    private String flatDischargeAmount;

    /** 谷段放电量(度) */
    private String valleyDischarge;

    private String valleyDischargePrice;
    private String valleyDischargeAmount;

    /** 深谷放电量 */
    private String deepValleyDischarge;

    private String deepValleyDischargePrice;
    private String deepValleyDischargeAmount;

    /** 尖峰充电量(度) */
    private String spikeCharge;

    private String spikeChargePrice;
    private String spikeChargeAmount;

    /** 峰段充电量(度) */
    private String peakCharge;

    private String peakChargePrice;
    private String peakChargeAmount;

    /** 平段充电量(度) */
    private String flatCharge;

    private String flatChargePrice;
    private String flatChargeAmount;

    /** 谷段充电量(度) */
    private String valleyCharge;

    private String valleyChargePrice;
    private String valleyChargeAmount;

    /** 深谷充 电量 */
    private String deepValleyCharge;

    private String deepValleyChargePrice;
    private String deepValleyChargeAmount;

    /** 总充电量(度) */
    private String totalCharge;

    /** 总放电量(度) */
    private String totalDischarge;

    /** 系统效率 */
    private String systemRatio;

    /** pv 发电data */
    private String pvPower;

    private String pvPowerSettleAmount;

    private String windPower;
    private String windPowerSettleAmount;

    private String wasterPower;
    private String wasterPowerSettleAmount;


    @ApiModelProperty(value = "分组需量")
    List<GroupDemandProfit> demandProfitList = new ArrayList<>(16);

    /** 降低 需量功率 */
    private String downDemandPower;

    private String demandSettleAmount;

    private Long order;
}
