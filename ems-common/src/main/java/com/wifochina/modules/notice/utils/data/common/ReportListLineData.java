package com.wifochina.modules.notice.utils.data.common;

import java.lang.reflect.Field;
import java.util.List;

import com.wifochina.modules.operation.VO.GroupDemandProfit;

/**
 * Created on 2023/9/21 10:01.
 *
 * <AUTHOR>
 */
public interface ReportListLineData {
    /**
     * 根据 index 获取 属性的方法
     *
     * @param index : index
     * @param clazz : clazz
     * @return : 属性数据
     */
    default String getData(int index, Class<? extends ReportListLineData> clazz) {
        Field[] declaredFields = clazz.getDeclaredFields();
        Field field = declaredFields[index];
        String result = "";
        try {
            field.setAccessible(true);
            Object o = field.get(this);
            result = String.valueOf(o);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return result;
    }

    default List<GroupDemandProfit> getGroupDemandProfitList(int index, Class<? extends ReportListLineData> clazz) {
        Field[] declaredFields = clazz.getDeclaredFields();
        Field field = declaredFields[index];
        List<GroupDemandProfit> result = null;
        try {
            field.setAccessible(true);
            Object o = field.get(this);
            result = (List<GroupDemandProfit>)o;
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return result;
    }
}
