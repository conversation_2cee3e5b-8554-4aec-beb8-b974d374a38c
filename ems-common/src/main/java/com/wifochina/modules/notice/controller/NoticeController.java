package com.wifochina.modules.notice.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.NoticeLogDetailService;
import com.wifochina.modules.notice.entity.NoticeEntity;
import com.wifochina.modules.notice.request.NoticePageRequest;
import com.wifochina.modules.notice.request.NoticeRequest;
import com.wifochina.modules.notice.service.NoticeService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.service.LogService;
import com.wifochina.modules.user.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Slf4j
@RestController
@Api(tags = "26-每日账单通知")
@RequestMapping("/notice")
public class NoticeController {

    @Resource private NoticeService noticeService;

    @Resource private UserService userService;

    @Resource private LogService logService;

    @PostMapping("/add")
    @ApiOperation("增加通知")
    @PreAuthorize("hasAuthority('/notice/add')")
    @Log(module = "NOTICE", type = OperationType.ADD)
    public Result<Object> add(@RequestBody NoticeRequest noticeRequest) {
        NoticeEntity noticeEntity = new NoticeEntity();
        BeanUtils.copyProperties(noticeRequest, noticeEntity);
        noticeEntity.setId(StringUtil.uuid());
        noticeEntity.setProjectId(WebUtils.projectId.get());
        noticeEntity.setType(String.join(",", noticeRequest.getType()));
        noticeService.save(noticeEntity);
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("NOTICE")
        //                        .method("NOTICE_ADD")
        //                        .object(noticeRequest)
        //                        .traceId(
        //                                String.format(LogService.NOTICE_TRACE_FORMAT,
        // noticeEntity.getId()))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/update")
    @ApiOperation("修改通知")
    @PreAuthorize("hasAuthority('/notice/edit')")
    @Log(module = "NOTICE", type = OperationType.UPDATE)
    public Result<String> update(@RequestBody NoticeRequest noticeRequest) {
        NoticeEntity noticeEntity = new NoticeEntity();
        BeanUtils.copyProperties(noticeRequest, noticeEntity);
        noticeEntity.setType(String.join(",", noticeRequest.getType()));
        noticeService.updateById(noticeEntity);

        //        logService.logUpdateDetail(
        //                LogInfo.builder()
        //                        .module("NOTICE")
        //                        .method("NOTICE_UPDATE")
        //                        .object(noticeRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.NOTICE_TRACE_FORMAT,
        // noticeRequest.getId()))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除通知")
    @PreAuthorize("hasAuthority('/notice/delete')")
    @Log(
            module = "NOTICE",
            type = OperationType.DEL,
            logDetailServiceClass = NoticeLogDetailService.class)
    public Result<String> delete(@PathVariable("id") String id) {
        NoticeEntity noticeEntity = noticeService.getById(id);
        noticeService.removeById(id);
        //        logService.logDeleteDetail(
        //                LogInfo.builder()
        //                        .module("NOTICE")
        //                        .method("NOTICE_DEL")
        //                        .object(id)
        //                        .delDetail(Map.of(noticeEntity.getId(), noticeEntity.getEmail()))
        //                        .traceId(String.format(LogService.NOTICE_TRACE_FORMAT, id))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/query")
    @ApiOperation("查询通知")
    @PreAuthorize("hasAuthority('/notice/query')")
    public Result<IPage<NoticeEntity>> query(@RequestBody NoticePageRequest noticePageRequest) {
        Page<NoticeEntity> page =
                Page.of(noticePageRequest.getPageNum(), noticePageRequest.getPageSize());
        boolean containAdEmail = EmsUtil.isContainAd(SecurityUtil.getUserId(), userService);
        // 用户角色类型 client项目普通ad域控admin项目管理super超级
        IPage<NoticeEntity> list =
                noticeService.page(
                        page,
                        Wrappers.lambdaQuery(NoticeEntity.class)
                                .eq(NoticeEntity::getProjectId, WebUtils.projectId.get())
                                .like(
                                        StringUtil.notEmpty(noticePageRequest.getEmail()),
                                        NoticeEntity::getEmail,
                                        noticePageRequest.getEmail())
                                .notLike(
                                        !containAdEmail,
                                        NoticeEntity::getEmail,
                                        EmsConstants.AD_EMAIL_SUFFIXES)
                                .like(
                                        StringUtil.notEmpty(noticePageRequest.getType()),
                                        NoticeEntity::getType,
                                        noticePageRequest.getType())
                                .orderByAsc(NoticeEntity::getCreateTime));
        list.getRecords()
                .forEach(
                        e -> {
                            String[] arr = e.getType().split(",");
                            e.setTypeList(Arrays.asList(arr));
                        });

        return Result.success(list);
    }
}
