package com.wifochina.modules.notice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.notice.entity.IncomeDivideIntoEntity;
import com.wifochina.modules.notice.mapper.IncomeDivideIntoMapper;
import com.wifochina.modules.notice.service.IncomeDivideIntoService;

import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Service
public class IncomeDivideIntoServiceImpl
        extends ServiceImpl<IncomeDivideIntoMapper, IncomeDivideIntoEntity>
        implements IncomeDivideIntoService {
    @Override
    public IncomeDivideIntoEntity findInfoByProjectId(String projectId) {
        return this.baseMapper.selectOne(
                new LambdaQueryWrapper<IncomeDivideIntoEntity>()
                        .eq(IncomeDivideIntoEntity::getProjectId, projectId));
    }
}
