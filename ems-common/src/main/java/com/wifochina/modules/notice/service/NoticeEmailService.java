package com.wifochina.modules.notice.service;

import com.wifochina.modules.notice.utils.ProjectMeterReportUtils;
import com.wifochina.modules.notice.utils.data.month.DetailReportDatas;
import com.wifochina.modules.notice.utils.data.month.MonthBillReportData;
import com.wifochina.modules.notice.utils.data.year.YearBillReportData;

import java.io.File;
import java.util.List;

/**
 * Created on 2023/9/20 16:44.
 *
 * <AUTHOR>
 */
public interface NoticeEmailService {

    /**
     * 发送月度账单Email
     *
     * @param monthDataHolder : holder
     * @param receivers : receivers
     */
    void sendMonthReportEmail(MonthDataHolder monthDataHolder, List<String> receivers);

    /**
     * 发送年度账单Email
     *
     * @param yearDataHolder : holder
     * @param receivers : receivers
     */
    void sendYearReportEmail(YearDataHolder yearDataHolder, List<String> receivers);

    interface MonthDataHolder {
        /**
         * 生成账单数据Excel file
         *
         * @return :file
         */
        default File file() {
            return ProjectMeterReportUtils.writeMeterMonthReport(billData(), detailDatas());
        }

        /**
         * 第一个sheet 相关的账单数据
         *
         * @return :BillReportData
         */
        MonthBillReportData billData();

        /**
         * 第二个sheet 相关的明细数据
         *
         * @return : DetailReportData
         */
        DetailReportDatas detailDatas();
    }

    interface YearDataHolder {
        /**
         * 生成账单数据Excel file
         *
         * @return :file
         */
        default File file() {
            return ProjectMeterReportUtils.writeMeterYearReport(yearBillDatas());
        }

        /**
         * 第一个sheet 相关的账单数据
         *
         * @return :BillReportData
         */
        List<YearBillReportData> yearBillDatas();
    }
}
