package com.wifochina.modules.notice.utils.data.month;

import com.wifochina.modules.notice.utils.data.common.ReportListLineData;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * Created on 2023/9/20 14:06. 明细数据
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode
public class DetailReport implements ReportListLineData {

    /** 时间 */
    private String time;

    /** 尖峰放电量(度) */
    private String spikeDischarge = "0";

    /** 峰段放电量(度) */
    private String peakDischarge = "0";

    /** 平段放电量(度) */
    private String flatDischarge = "0";

    /** 谷段放电量(度) */
    private String valleyDischarge = "0";

    /** 深谷放电量(度) */
    private String deepValleyDischarge = "0";

    /** 尖峰充电量(度) */
    private String spikeCharge = "0";

    /** 峰段充电量(度) */
    private String peakCharge = "0";

    /** 平段充电量(度) */
    private String flatCharge = "0";

    /** 谷段充电量(度) */
    private String valleyCharge = "0";

    /** 深谷充电量(度) */
    private String deepValleyCharge = "0";

    /** 总充电量(度) */
    private String totalCharge = "0";

    /** 总放电量(度) */
    private String totalDischarge = "0";

    /** 系统效率 */
    private String systemRatio = "0";

    /** PV发电(度) */
    private String pvDischarge = "0";

    /** 风力发电(度) */
    private String windDischarge = "0";

    /** 余热发电(度) */
    private String wasterDischarge = "0";

    /** 时间 */
    private Long order;
}
