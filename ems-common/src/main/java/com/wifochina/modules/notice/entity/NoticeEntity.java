package com.wifochina.modules.notice.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@Getter
@Setter
@TableName("t_notice")
@ApiModel(value = "NoticeEntity对象")
public class NoticeEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通知识符uuid")
    private String id;

    @ApiModelProperty("通知类型（储能ems、光伏pv、风电wind、需量control、余热发电waster 以,号分割）")
    private String type;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("项目id")
    private String projectId;

    @TableField(exist = false)
    @ApiModelProperty("通知类型")
    List<String> typeList;
}
