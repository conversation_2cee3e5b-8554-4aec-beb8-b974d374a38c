package com.wifochina.modules.notice.service.impl;

import com.wifochina.common.constants.EmailLogTypeEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.util.EmailService;
import com.wifochina.modules.notice.service.NoticeEmailService;
import com.wifochina.modules.notice.utils.data.month.MonthBillReportData;
import com.wifochina.modules.notice.utils.data.year.YearBillReportData;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

import javax.mail.MessagingException;

/**
 * Created on 2023/9/20 16:46. <br>
 * 发送 账单 Email
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NoticeEmailServiceImpl implements NoticeEmailService {

    @Autowired private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String emailFrom;

    @Autowired private EmailService emailService;

    @Override
    public void sendMonthReportEmail(MonthDataHolder monthDataHolder, List<String> receivers) {
        MonthBillReportData monthBillReportData = monthDataHolder.billData();
        // 生成excel账单报表 file文件
        File file = monthDataHolder.file();
        try {
            emailService.sendMessage(
                    receivers,
                    monthBillReportData.getProjectName()
                            + monthBillReportData.getYear()
                            + "年"
                            + monthBillReportData.getMonth()
                            + "月账单",
                    "尊敬的客户: \n附件是"
                            + monthBillReportData.getProjectName()
                            + monthBillReportData.getYear()
                            + "年"
                            + monthBillReportData.getMonth()
                            + "月账单, 请注意查收!",
                    helper -> {
                        FileSystemResource fileSystemResource = new FileSystemResource(file);
                        try {
                            helper.addAttachment(
                                    monthBillReportData.getYear()
                                            + "年"
                                            + monthBillReportData.getMonth()
                                            + "月度账单.xlsx",
                                    fileSystemResource);
                        } catch (MessagingException e) {
                            throw new RuntimeException(e);
                        }
                    },
                    () ->
                            new EmailService.ServiceInfo()
                                    .setProjectId(monthBillReportData.getProjectId())
                                    .setEmailLogTypeEnum(EmailLogTypeEnum.BILL_MONTH));

        } catch (Exception e) {
            log.error("发送月度账单邮件失败 msg: {}", e.getMessage());
        }
    }

    @Override
    public void sendYearReportEmail(YearDataHolder yearDataHolder, List<String> receivers) {
        YearBillReportData yearBillReportData =
                yearDataHolder.yearBillDatas().stream()
                        .filter(YearBillReportData::isCollect)
                        .findFirst()
                        .orElseThrow(() -> new ServiceException(""));
        // 生成excel账单报表 file文件
        File file = yearDataHolder.file();
        try {
            emailService.sendMessage(
                    receivers,
                    yearBillReportData.getTitleName() + yearBillReportData.getYear() + "年账单",
                    "尊敬的客户: \n附件是"
                            + yearBillReportData.getTitleName()
                            + yearBillReportData.getYear()
                            + "年账单, 请注意查收!",
                    helper -> {
                        FileSystemResource fileSystemResource = new FileSystemResource(file);
                        try {
                            helper.addAttachment(
                                    yearBillReportData.getYear() + "年年度账单.xlsx",
                                    fileSystemResource);
                        } catch (MessagingException e) {
                            throw new RuntimeException(e);
                        }
                    },
                    () ->
                            new EmailService.ServiceInfo()
                                    .setProjectId(yearBillReportData.getProjectId())
                                    .setEmailLogTypeEnum(EmailLogTypeEnum.BILL_YEAR));
        } catch (Exception e) {
            log.error("发送年度账单邮件失败 msg: {}", e.getMessage());
        }
    }
}
