package com.wifochina.modules;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BaseEntity
 * 
 * @date 2022/3/14 18:56
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class BaseEntity implements Serializable {

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private Long createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改时间")
    private Long updateTime;

    // /**
    // * 删除
    // * */
    // @ExcelIgnore
    // @TableField(value = "deleted", fill = FieldFill.INSERT)
    // @ApiModelProperty("逻辑删除")
    // private boolean deleted;
    //
    // /**
    // * 备注
    // * */
    // @ExcelIgnore
    // @TableField("remark")
    // @ApiModelProperty("备注")
    // private String remark;

}
