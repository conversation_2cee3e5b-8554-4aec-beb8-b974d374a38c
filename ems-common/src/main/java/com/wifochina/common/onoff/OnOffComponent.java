package com.wifochina.common.onoff;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Created on 2024/4/9 16:22.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class OnOffComponent {
    private final GroupService groupService;
    private final DeviceService deviceService;

    /**
     * 校验 电表的 是否接入 根据 系统分组的 对应开关
     *
     * @param systemGroupEntity : 系统分组
     * @param meterType : 电表类型 @see MeterTypeEnum
     * @return : 对应是否开关
     */
    public boolean checkMeterEnable(GroupEntity systemGroupEntity, String meterType) {
        if (MeterTypeEnum.PV.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController());
        }
        if (MeterTypeEnum.GRID.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableElectricGrid());
        }

        if (MeterTypeEnum.WIND.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration());
        }
        if (MeterTypeEnum.DIESEL.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableWoodPowerGeneration());
        }
        if (MeterTypeEnum.PILE.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableChargingPilePower());
        }

        if (MeterTypeEnum.GAS.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableGasPowerGeneration());
        }
        if (EmsConstants.EMS.equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableEms());
        }
        if (MeterTypeEnum.WASTER.meterType().toString().equals(meterType)) {
            return Boolean.TRUE.equals(systemGroupEntity.getEnableWastePowerGeneration());
        }
        return false;
    }

    public boolean renewableProfitOnOff(String projectId, CalculateTypeEnum calculateTypeEnum) {
        GroupEntity groupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getWhetherSystem, 1)
                                .eq(GroupEntity::getProjectId, projectId));
        boolean onOffFlag = false;
        if (CalculateTypeEnum.PV.equals(calculateTypeEnum)) {
            onOffFlag =
                    Boolean.TRUE.equals(groupEntity.getPhotovoltaicController())
                            && Boolean.TRUE.equals(groupEntity.getPvProfitController());
        }
        if (CalculateTypeEnum.WIND.equals(calculateTypeEnum)) {
            onOffFlag =
                    Boolean.TRUE.equals(groupEntity.getEnableWindPowerGeneration())
                            && Boolean.TRUE.equals(groupEntity.getWindEarningsController());
        }
        if (CalculateTypeEnum.WASTER.equals(calculateTypeEnum)) {
            onOffFlag =
                    Boolean.TRUE.equals(groupEntity.getEnableWastePowerGeneration())
                            && Boolean.TRUE.equals(groupEntity.getWasterEarningsController());
        }
        return onOffFlag;
    }

    public boolean dcdcAndProfitOnOff(String projectId, Consumer<List<String>> deviceIdsConsumer) {
        GroupEntity groupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getWhetherSystem, 1)
                                .eq(GroupEntity::getProjectId, projectId));
        // 2024-01-17 09:25:04 add 得到 是否开启dcdc收益开关
        // 得到是否开启dcdc
        boolean dcdcProfit = groupEntity.getDcdcProfitController();
        List<DeviceEntity> deviceEntityListlist =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getUnreal, false)
                        .eq(DeviceEntity::getProjectId, projectId)
                        .list();
        boolean hasDcdc = deviceEntityListlist.stream().anyMatch(DeviceEntity::getHasDcdc);
        List<String> sysDeviceIds =
                deviceEntityListlist.stream().map(DeviceEntity::getId).collect(Collectors.toList());
        deviceIdsConsumer.accept(sysDeviceIds);
        // 开启了dcdc 并且开启了 dcdc收益开关
        return hasDcdc && dcdcProfit;
    }
}
