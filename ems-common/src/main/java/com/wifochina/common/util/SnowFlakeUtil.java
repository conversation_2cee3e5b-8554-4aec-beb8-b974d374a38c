package com.wifochina.common.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:10
 * @version 1.0
 */
@Component
public class SnowFlakeUtil implements EnvironmentAware {

    private Environment environment;

    private volatile Snowflake snowflake;

    public long generateId() {
        singleInstance();
        return snowflake.nextId();
    }

    public String generateStringId() {
        singleInstance();
        return snowflake.nextIdStr();
    }

    private void singleInstance() {
        if (snowflake == null) {
            synchronized (SnowFlakeUtil.class) {
                if (snowflake == null) {
                    snowflake =
                            IdUtil.getSnowflake(
                                    Long.parseLong(
                                            Optional.ofNullable(
                                                            environment.getProperty(
                                                                    "snowflake.work"))
                                                    .orElse("1")),
                                    Long.parseLong(
                                            Optional.ofNullable(
                                                            environment.getProperty(
                                                                    "snowflake.datacenter"))
                                                    .orElse("1")));
                }
            }
        }
    }

    @Override
    public void setEnvironment(@NonNull Environment environment) {
        this.environment = environment;
    }
}
