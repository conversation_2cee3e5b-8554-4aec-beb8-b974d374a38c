package com.wifochina.common.util;

import com.wifochina.common.constants.TimePointEnum;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 7/1/2022 10:57 AM
 */
public final class EmsConstants {
    public static final String GROUP_ID = "groupId";
    public static final String EXCEEDED_DEMAND = "exceededDemand";
    public static final String ACTUAL_DEMAND = "actualDemand";
    public static final String OVER_UP_LIMIT_FLAG = "overUpLimitFlag";

    private EmsConstants() {}

    public static final String SUCCESS = "success";
    public static final String DEMAND_NOTICE = "demandNotice";
    public static final String CAPACITY_NOTICE = "capacityNotice";
    public static final String CONTROLLABLE = "controllable";
    public static final int DAY_NUM_OF_WEEK = 7;
    public static final Double SOC_MAX = 100.0;
    public static final Double SOC_DEFAULT = 4.0;
    public static final int CHAR_SIZE = 16;
    public static final String FAULT = "fault";
    public static final String ALARM = "alarm";
    public static final String ADMIN_USERNAME = "admin";
    public static final String WH_ADMIN_USERNAME = "whadmin";
    public static final String BUCKET_MEAN = "mean";
    public static final String ALL = "all";
    public static final String INFLUX_FIELD = "_field";
    public static final String INFLUX_VALUE = "_value";
    public static final String INFLUX_TIME = "_time";
    public static final String EMS_HISTORY_OUTPUT_ENERGY = "ems_history_output_energy";
    public static final String EMS_HISTORY_INPUT_ENERGY = "ems_history_input_energy";
    public static final String WEEK = "week";
    public static final String MONTH = "month";
    public static final String YEAR = "year";
    public static final String DAILY = "daily";
    public static final String PERIOD_1_MO = "1mo";
    public static final String SYSTEM_GROUP_NAME = "系统分组";
    public static final String SYSTEM_GROUP_NAME_EN = "System";
    public static final String USER_ROLE_CLIENT = "client";
    public static final String USER_ROLE_ADMIN = "admin";
    public static final String USER_ROLE_SUPER = "super";
    public static final String USER_ROLE_AD = "ad";
    public static final String USER_ROLE_REGISTER = "register";
    public static final String COMMON = "common";
    public static final String AIR_COLUMN = "100+AIRi*10";
    public static final String WATER_COLUMN = "250+WATERi*10";
    public static final String FIRE_COLUMN = "150+FIREi*10";
    public static final String CLUSTER_COLUMN = "1100+CLUSTERi*50";
    public static final String PCS_COLUMN = "500+PCSi*100";
    public static final String DCDC_COLUMN = "20100+DCDCi*100";
    public static final String STATES_COLUMN = "21100+STATESi*50";
    public static final String DEVICE_MAINTAIN_AUTHORITY = "/system/maintain";
    public static final String MESSAGE_SHOW_HIDE = "/event/showHide";
    public static final String MANAGE_SHOW_INVESTOR = "/manage/project/showInvestor";
    public static final String LOG_USER_SYSTEM_ID = "system";
    public static final int CACHE_EMS_RUNNING_STATUS_TIME = 3;

    public static final int ONE_DAY_SECOND = 86400;

    public static final String PANGU = "pangu";

    public static final String AD_EMAIL_SUFFIXES = "@weiheng-tech.com";

    public static final Map<String, TimePointEnum> TIME_POINT_ENUM_MAP =
            Arrays.stream(TimePointEnum.values())
                    .collect(Collectors.toMap(TimePointEnum::getValue, value -> value));

    public static final String PROJECT_ID_HEADER = "ProjectId";

    public static final String AUTHORIZATION_HEADER = "Authorization";

    public static final String BEARER = "bearer";

    public static final String GO_AUTH_HEADER = "W-AUTH";

    public static final String SPILT_COMMA = ",";

    public static final String CONTROLLABLE_STATE_BIT_PATTERN = "controllable_{i}_state_bit";
    public static final String CONTROLLABLE_ALARM_BIT_PATTERN = "controllable_{i}_alarm_bit";
    public static final String CONTROLLABLE_FAULT_BIT_PATTERN = "controllable_{i}_fault_bit";

    public static final String CHINA_EN_US = "China";

    public static final String AMMETER = "ammeter";
    public static final String DEVICE = "device";

    public static final String INFLUX = "influx";

    public static final String SAMPLE_PLACEHOLDER = "{sample_placeholder}";

    public static final String INFLUX_MAX_FUNC = "max";

    public static final String INFLUX_LAST_FUNC = "last";
    public static final String INFLUX_MEAN_FUNC = "mean";

    public static final String HUAWEI_CHARGER = "HuaweiCharger";

    /** 云版本 下面的 ems 的 measurement */
    public static final String EMS = "ems";

    /** 场站下面的 电表的 measurement */
    public static final String STATIONS_METER = "T3_5s";

    public static final String CONTROLLABLE_SITE = "T4_5s";

    public static final String DEVICE_ID = "deviceId";
    public static final String METER_ID = "meterId";
    public static final String AMMETER_ID = "ammeterId";
    public static final String PROJECT_ID = "projectId";
    public static final String PROJECT_ID_O = "ProjectId";
    public static final int CONSTANT_1000 = 1000;
    public static final String TIME_ZONE_KEY = "timezone";
    public static final String REAL_DEMAND = "realDemand";

    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    public static final String PAGE_NUM = "pageNum";
    public static final String PAGE_SIZE = "pageSize";
    public static final String LIMIT_1 = "limit 1";

    public static final String OUT = "out";
    public static final String IN = "in";
    public static final String UNDER_LINE = "_";
}
