package com.wifochina.common.util;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023-07-21 5:51 PM
 */
@Getter
public enum InfluxTimeCalcEnum {

    /**
     * type time
     */
    MONTH("month", "1d"), YEAR("year", "1mo"), DAILY("daily", "1h");

    private final String type;
    private final String time;

    InfluxTimeCalcEnum(String type, String time) {
        this.type = type;
        this.time = time;
    }

    public static String getTimeFromType(String type) {
        for (InfluxTimeCalcEnum influxTimeCalcEnum : InfluxTimeCalcEnum.values()) {
            if (influxTimeCalcEnum.getType().equals(type)) {
                return influxTimeCalcEnum.getTime();
            }
        }
        return null;
    }

}
