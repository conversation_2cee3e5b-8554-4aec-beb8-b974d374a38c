package com.wifochina.common.util;

/**
 * Created on 2024-09-23 13:59:39 当前控制需量调整模式
 *
 * <AUTHOR>
 */
public enum DemandControlAdjustModelEnum {
    manual("manual", "手动"),

    auto_calc("auto_calc", "自动(计算)"),

    auto_meter("auto_meter", "自动(电表)");

    /** 设备状态 */
    private final String name;

    private final String desc;

    DemandControlAdjustModelEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public static boolean auto(String adjustModel) {
        return DemandControlAdjustModelEnum.auto_calc.getName().equals(adjustModel)
                || DemandControlAdjustModelEnum.auto_meter.getName().equals(adjustModel);
    }

    public String getName() {
        return name;
    }
}
