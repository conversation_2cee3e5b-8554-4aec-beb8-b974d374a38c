package com.wifochina.common.util;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.modules.group.entity.ControllerEntity;
import com.wifochina.modules.group.service.ControllerService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * Created on 2023/11/7 16:27.
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RestGoUtils {

    private final ProjectService projectService;
    private final ControllerService controllerService;
    private final RestTemplate restTemplate;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    public <T> T sendPost(GoRestApi goRestApi, Class<T> tClass) {
        T t;
        String url;
        ProjectEntity projectEntity = projectService.getById(goRestApi.projectId());
        if (projectEntity.getProjectModel() == 1) {
            List<ControllerEntity> controllerEntities =
                    controllerService.list(
                            Wrappers.lambdaQuery(ControllerEntity.class)
                                    .eq(ControllerEntity::getProjectId, goRestApi.projectId()));
            if (controllerEntities != null && !controllerEntities.isEmpty()) {
                ControllerEntity controllerEntity = controllerEntities.get(0);
                String outerControllerUrl =
                        "http://"
                                + controllerEntity.getIp().trim()
                                + ":"
                                + controllerEntity.getPort();
                url = outerControllerUrl + goRestApi.api();
            } else {
                return null;
            }
        } else {
            url = gatewayUrl + goRestApi.api();
        }
        log.debug("send post to go api, url: {}, request: {}", url, goRestApi.request());
        t = restTemplate.postForObject(url, goRestApi.request(), tClass);
        log.debug("go api, url:{}, result: {}", url, t);
        return t;
    }

    @FunctionalInterface
    public interface GoRestApi {
        /**
         * api url
         *
         * @return : java.lang.String
         */
        String api();

        /**
         * 项目id
         *
         * @return : java.lang.String 默认从WebUtils.projectId.get()获取
         */
        default String projectId() {
            return WebUtils.projectId.get();
        }

        /**
         * 请求参数
         *
         * @return : java.lang.Object 默认null
         */
        default Object request() {
            return null;
        }
    }
}
