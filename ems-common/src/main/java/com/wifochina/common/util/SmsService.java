package com.wifochina.common.util;

import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.google.gson.Gson;

import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;

/**
 * SmsUtil
 * 
 * @date 10/28/2022 1:24 PM
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j
public class SmsService {

    @Value("${sms.accessKeyId}")
    private String accessKeyId;

    @Value("${sms.accessKeySecret}")
    private String accessKeySecret;

    @Value("${sms.type}")
    private String regionId;

    @Value("${sms.signName}")
    private String signName;

    public void send(String phone, String message, String templateCode) {
        String endPoint = "dysmsapi.aliyuncs.com";
        String param = "{\"code\":\"" + message + "\"}";
        StaticCredentialProvider provider = StaticCredentialProvider
            .create(Credential.builder().accessKeyId(accessKeyId).accessKeySecret(accessKeySecret).build());

        AsyncClient client = AsyncClient.builder().region(regionId).credentialsProvider(provider)
            .overrideConfiguration(ClientOverrideConfiguration.create().setEndpointOverride(endPoint)).build();

        SendSmsRequest sendSmsRequest = SendSmsRequest.builder().templateCode(templateCode).templateParam(param)
            .phoneNumbers(phone).signName(signName).build();

        client.sendSms(sendSmsRequest);

        CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
        SendSmsResponse resp;
        try {
            resp = response.get();
            log.info("sms is ->" + (new Gson().toJson(resp)));
        } catch (Exception e) {
            log.error("sms is not send to ->" + phone);
        } finally {
            client.close();
        }
    }
}
