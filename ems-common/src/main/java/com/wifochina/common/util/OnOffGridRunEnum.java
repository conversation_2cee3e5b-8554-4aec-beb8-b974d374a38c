package com.wifochina.common.util;

import lombok.Getter;

/**
 * Created on 2024 10/12 16:43 并离网切换的几种模式
 *
 * <AUTHOR>
 */
public enum OnOffGridRunEnum {
    on_grid_run("on_grid_run", "并网运行(默认)"),
    off_grid_run("off_grid_run", "离网运行"),
    on_off_grid_run_manual("on_off_grid_run_manual", "并/离网运行(手动)"),
    on_off_grid_run_auto("on_off_grid_run_auto", "并/离网运行(自动)");

    /** 设备状态 */
    @Getter private final String name;

    private final String desc;

    OnOffGridRunEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }
}
