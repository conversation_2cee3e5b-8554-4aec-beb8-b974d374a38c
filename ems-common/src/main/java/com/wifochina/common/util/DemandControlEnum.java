package com.wifochina.common.util;

import lombok.Getter;

/**
 * Created on 2023/10/10 13:54. 点位的Enum
 *
 * <AUTHOR>
 */
public enum DemandControlEnum {
    /** 离线 0 待机1 放电2 充电3 停机4 */
    disable("disable", "不启用"),

    enable_show_demand_income("enable_show_demand_income", "启用需量控制,显示需量收益"),

    enable_hide_demand_income("enable_hide_demand_income", "启用需量控制,隐藏需量收益");

    /** 设备状态 */
    @Getter private final String name;

    private final String desc;

    DemandControlEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    /**
     * 是否开启了demandControl + demandIncome
     *
     * @param name : name
     * @return : boolean
     */
    public static boolean enableDemandIncome(String name) {
        return !StringUtil.isEmpty(name) && name.equals(enable_show_demand_income.name);
    }

    /**
     * 是否开启了demandControl
     *
     * @param name : name
     * @return : boolean
     */
    public static boolean enableDemandControl(String name) {
        return !StringUtil.isEmpty(name)
                && (name.equals(enable_show_demand_income.name)
                        || name.equals(enable_hide_demand_income.name));
    }
}
