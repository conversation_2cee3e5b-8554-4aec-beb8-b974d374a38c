package com.wifochina.common.util;

import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.group.entity.IdSearchSupport;

import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created on 2024/6/14 10:53.
 *
 * <AUTHOR>
 */
public class IdSearchUtils {

    public static final String PLACEHOLDER = "{ID_SEARCH_PLACEHOLDER}";

    public static String createIdSearchSqlForTimeSeries(
            List<? extends IdSearchSupport> idSearchSupports) {
        String timeSeries = EmsConstants.INFLUX;
        if (idSearchSupports.isEmpty()) {
            throw new ServiceException("空列表");
        }
        // 获取第一个对象的类型
        String expectedType = idSearchSupports.get(0).type();
        // 使用 Stream API 检查所有对象的类型是否一致
        boolean allMatch =
                idSearchSupports.stream().allMatch(support -> support.type().equals(expectedType));
        if (!allMatch) {
            throw new ServiceException("只能相同类型的构建sql");
        }
        if (expectedType.equals(EmsConstants.DEVICE)) {
            List<String> deviceIds =
                    idSearchSupports.stream().map(IdSearchSupport::id).collect(Collectors.toList());
            return createDeviceSqlForInfluxDb(deviceIds);
        } else if (expectedType.equals(EmsConstants.AMMETER)) {
            List<String> ammeterIds =
                    idSearchSupports.stream().map(IdSearchSupport::id).collect(Collectors.toList());
            if (ApplicationHolder.isCloudDeployType()) {
                // 现在都是influxdb只是区分 cloud的和场站的
                return createMeterSqlForInfluxCloud(ammeterIds);
            } else {
                return createMeterSqlForInflux(ammeterIds);
            }
        }
        throw new ServiceException("不支持的类型");
    }

    public static String createMeterSqlForInfluxCloud(List<String> ammeterIds) {
        if (CollectionUtils.isEmpty(ammeterIds)) {
            return "";
        }
        StringBuilder ammeterIdSql = new StringBuilder("(");
        for (int i = 0; i < ammeterIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("r.meterId ==\"").append(ammeterIds.get(i)).append("\"");
            } else {
                ammeterIdSql.append(" or r.meterId == \"").append(ammeterIds.get(i)).append("\"");
            }
        }
        ammeterIdSql.append(")");
        return ammeterIdSql.toString();
    }

    public static String createMeterSqlForInflux(List<String> ammeterIds) {
        StringBuilder ammeterIdSql = new StringBuilder("(");
        for (int i = 0; i < ammeterIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("r.ammeterId ==\"").append(ammeterIds.get(i)).append("\"");
            } else {
                ammeterIdSql.append(" or r.ammeterId == \"").append(ammeterIds.get(i)).append("\"");
            }
        }
        ammeterIdSql.append(")");
        return ammeterIdSql.toString();
    }

    public static String createDeviceSqlForInfluxDb(List<String> deviceIds) {
        StringBuilder deviceIdSql = new StringBuilder("(");
        for (int i = 0; i < deviceIds.size(); i++) {
            if (i == 0) {
                deviceIdSql.append("r.deviceId ==\"").append(deviceIds.get(i)).append("\"");
            } else {
                deviceIdSql.append(" or r.deviceId == \"").append(deviceIds.get(i)).append("\"");
            }
        }
        deviceIdSql.append(")");
        return deviceIdSql.toString();
    }

    public static String createDeviceSqlForLindormDb(List<String> deviceIds) {
        StringBuilder deviceIdSql = new StringBuilder("and (");
        for (int i = 0; i < deviceIds.size(); i++) {
            if (i == 0) {
                deviceIdSql.append("deviceId ='").append(deviceIds.get(i)).append("'");
            } else {
                deviceIdSql.append(" or deviceId = '").append(deviceIds.get(i)).append("'");
            }
        }
        deviceIdSql.append(")");
        return deviceIdSql.toString();
    }
}
