package com.wifochina.common.util;

import lombok.Getter;

/**
 * 设备状态
 *
 * <AUTHOR>
 * @since 2023-07-19 9:55 AM
 */
@Getter
public enum EmsStatusEnum {
    /** 离线 0 待机1 充电2 放电3 停机4 故障5 待调8 断网9 */
    OFFLINE(0),
    STANDBY(1),
    CHARGING(2),
    DISCHARGING(3),
    STOP(4),
    Fault(5),
    UNINSTALL(8),
    UNKNOWN(9),
    // 2025-06-05 16:40:12 新增运行状态 目前只有在 这地方使用 如果有功功率小于额定功率的千分之5，目无功功率的值大于额定有功功率值的千分之5，显示 运行状态
    RUNNING(10);

    /** 设备状态 */
    private final Integer status;

    EmsStatusEnum(Integer status) {
        this.status = status;
    }
}
