package com.wifochina.common.util;

import org.springframework.data.util.Pair;

/**
 * <AUTHOR>
 * @since 2023-12-11 7:22 PM
 */
public class ParseCapacity {

    private final static String SPLIT_SYMBOL = "/";

    private final static String MWH = "mwh";

    private final static String KWH = "kwh";

    private final static String KW = "kw";

    private final static String MW = "mw";

    public static Pair<Long, Long> getCapacity(String size) {
        long capacity = 0;
        long power = 0;
        size = size.toLowerCase();
        if (size.contains(SPLIT_SYMBOL)) {
            // 500kw/1MWh
            String[] split = size.split(SPLIT_SYMBOL);
            // 500kw
            String powerString = split[0].toLowerCase();
            // 1mwh
            String capacityString = split[1].toLowerCase();
            if (capacityString.contains(MWH)) {
                float f = Float.parseFloat(capacityString.replace("mwh", ""));
                capacity = (long)(f * 1000);
            } else if (capacityString.contains(KWH)) {
                capacity = Long.parseLong(capacityString.replace("kwh", ""));
            }
            if (powerString.contains(KW)) {
                power = Long.parseLong(powerString.replace("kw", ""));
            } else if (powerString.contains(MW)) {
                float f = Float.parseFloat(powerString.replace("mw", ""));
                power = (long)(f * 1000);
            }
        }
        return Pair.of(capacity, power);
    }
}
