package com.wifochina.common.util;

/**
 * 计算上一周的日期
 *
 * <AUTHOR>
 * @date 2025/8/7 15:26
 * @version 1.0
 */
public class LastWeekCalculator {

    public static int[] lastWeekSameDay(int year, int month, int day) {
        // 减去7天
        day -= 7;

        // 检查日期是否在当月
        if (day > 0) {
            return new int[]{year, month, day};
        }

        // 处理跨月情况
        month--;
        if (month == 0) {
            month = 12;
            year--;
        }

        // 获取上个月的天数并计算新日期
        int daysInPrevMonth = getDaysInMonth(year, month);
        day += daysInPrevMonth;

        return new int[]{year, month, day};
    }

    // 获取指定月份的天数
    private static int getDaysInMonth(int year, int month) {
        switch (month) {
            case 4: case 6: case 9: case 11: // 4月、6月、9月、11月
                return 30;
            case 2: // 2月（考虑闰年）
                if (isLeapYear(year)) {
                    return 29;
                } else {
                    return 28;
                }
            default: // 1月、3月、5月、7月、8月、10月、12月
                return 31;
        }
    }

    // 检查是否为闰年
    private static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }
}
