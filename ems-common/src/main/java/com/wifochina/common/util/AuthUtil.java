package com.wifochina.common.util;

import java.nio.charset.StandardCharsets;
import java.time.Instant;

import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 * @since 2023-09-15 10:08 AM
 */
public class AuthUtil {
    public static String getAuth() {
        long time = Instant.now().getEpochSecond();
        String sign = EmsConstants.PANGU + time;
        String md5String = DigestUtils.md5DigestAsHex(sign.getBytes(StandardCharsets.UTF_8));
        String md5Summary = md5String.substring(10, 20);
        String signString = EmsConstants.PANGU + ":" + time + ":" + md5Summary;
        return Base64.encodeBase64String(signString.getBytes(StandardCharsets.UTF_8));
    }

    public static boolean validateAuth(String wAuth) {
        try {
            String[] args = new String(Base64.decodeBase64(wAuth),StandardCharsets.UTF_8).split(":");
            String sign = EmsConstants.PANGU + args[1];
            String md5String = DigestUtils.md5DigestAsHex(sign.getBytes(StandardCharsets.UTF_8));
            String md5Summary = md5String.substring(10, 20);
            if(!md5Summary.equals(args[2])){
                return false;
            }
            long time = Instant.now().getEpochSecond();
            return (time - Long.parseLong(args[1])) <= 60;
        }catch (Exception e){
            return false;
        }
    }

}
