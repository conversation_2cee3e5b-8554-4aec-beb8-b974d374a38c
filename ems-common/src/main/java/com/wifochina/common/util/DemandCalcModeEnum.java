package com.wifochina.common.util;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-01-29 9:44 AM
 */
@Getter
public enum DemandCalcModeEnum {

    /** 需量计算模式 interval unit : minute */
    FIXED_15M(1, 15L),
    FIXED_30M(2, 30L),
    SLIDING_15M(3, 15L),
    SLIDING_30M(4, 30L),

    SLIDING(2, 0L),
    FIXED(1, 0L),
    ;

    private final Integer model;
    private final Long interval;

    public static Boolean isSliding(Integer model) {
        return Objects.equals(SLIDING.model, model);
    }

    //    public static Boolean isSliding(Integer model) {
    //        return Objects.equals(SLIDING_15M.model, model) || Objects.equals(SLIDING_30M.model,
    // model);
    //    }

    DemandCalcModeEnum(Integer model, Long interval) {
        this.model = model;
        this.interval = interval;
    }

    public static Long getIntervalByCalcModel(Integer model) {
        for (DemandCalcModeEnum demandCalcModeEnum : DemandCalcModeEnum.values()) {
            if (demandCalcModeEnum.model.equals(model)) {
                return demandCalcModeEnum.interval;
            }
        }
        return null;
    }
}
