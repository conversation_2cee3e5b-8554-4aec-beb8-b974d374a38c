package com.wifochina.common.util;

import lombok.Getter;

@Getter
public enum RemediesDataTypeEnum {
    PV("pv"),
    GRIDOUT("gridOut"),
    GRIDIN("gridIn"),
    EMSOUT("emsOut"),
    EMSIN("emsIn"),
    LOAD("load"),
    WIND("wind"),
    DIESEL("diesel"),
    GAS("gas"),
    WASTER("waster"),
    PILE("pile");

    private final String type;

    RemediesDataTypeEnum(String type) {
        this.type = type;
    }
}
