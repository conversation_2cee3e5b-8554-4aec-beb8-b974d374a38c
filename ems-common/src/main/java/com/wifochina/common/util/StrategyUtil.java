package com.wifochina.common.util;

import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.group.request.go.Strategy;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class StrategyUtil {
    public static List<Strategy> fillStrategies(
            List<Strategy> originalStrategies, String timezoneCode) {

        int weekDay = MyTimeUtil.getCurrentWeekday(timezoneCode);
        // 遍历一周中的每一天
        originalStrategies =
                originalStrategies.stream()
                        .filter(strategy -> strategy.getWeek() == weekDay)
                        .collect(Collectors.toList());
        List<Strategy> filledStrategies = new ArrayList<>(1440); // 每天1440分钟

        // 初始化列表，所有元素的power值为null
        for (int i = 0; i < 1440; i++) {
            Strategy strategy = new Strategy();
            strategy.setWeek(weekDay);
            strategy.setStart_minute(i);
            filledStrategies.add(strategy);
        }
        // 遍历原始的Strategy列表，填充新列表
        for (Strategy strategy : originalStrategies) {

            int start = strategy.getStart_minute();
            int end = strategy.getEnd_minute() == 0 ? 1440 : strategy.getEnd_minute();

            // 确保start和end在有效范围内
            if (start >= 0 && end <= 1440) {
                for (int minute = start; minute < end; minute++) {
                    Strategy minuteStrategy = filledStrategies.get(minute);
                    minuteStrategy.setPower(
                            strategy.getFunction() == 2
                                    ? null
                                    : (strategy.getFunction() == 0
                                            ? -strategy.getPower()
                                            : strategy.getPower()));
                    minuteStrategy.setFunction(strategy.getFunction());
                    minuteStrategy.setWeek(strategy.getWeek());
                    minuteStrategy.setSoc(strategy.getSoc());
                    filledStrategies.set(minute, minuteStrategy);
                }
            }
        }
        return filledStrategies;
    }
}
