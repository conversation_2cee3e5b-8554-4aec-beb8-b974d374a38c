package com.wifochina.common.util;

/**
 * Created on 2023/10/10 13:54. 点位的Enum
 * 
 * <AUTHOR>
 */
public enum PointEnum {
    /**
     * 离线 0 待机1 放电2 充电3 停机4
     */
    ems_history_output_energy("EMS历史输出能量(kWh)", "Uint32"),

    ems_history_input_energy("EMS历史输入能量(kWh)", "Uint32"),

    ems_ac_active_power("电网侧功率(kW) 正为放电", "Int16"),

    bms_soc("SOC(0~100)", "Uint16"),

    bms_design_energy("设计存储能力(kWh)", "Uint16");

    /**
     * 设备状态
     */
    private final String name;
    private final String type;

    PointEnum(String name, String type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }
}
