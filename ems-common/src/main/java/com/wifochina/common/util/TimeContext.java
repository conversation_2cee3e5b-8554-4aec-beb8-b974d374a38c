package com.wifochina.common.util;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.*;

/**
 * Created on 2024/5/27 10:23.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TimeContext {
    ZonedDateTime timePointDate;

    public static TimeContext getContext(String timeZone, long timePoint) {
        ZonedDateTime zonedDateTime =
                LocalDate.ofInstant(Instant.ofEpochSecond(timePoint), ZoneId.of(timeZone))
                        .atStartOfDay()
                        .atZone(ZoneId.of(timeZone));
        return new TimeContext().setTimePointDate(zonedDateTime);
    }
}
