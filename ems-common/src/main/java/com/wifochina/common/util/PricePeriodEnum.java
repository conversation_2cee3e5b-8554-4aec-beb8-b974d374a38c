package com.wifochina.common.util;

/**
 * <AUTHOR>
 * @since 2023-09-28 1:33 PM
 */
public enum PricePeriodEnum {
    /**
     * for report
     */
    CHARGE("进电"), DISCHARGE("出电"), TIP("尖峰"), PEEK("峰段"), VALLEY("谷段"), FLAT("平段"), DEEP_VALLEY("深谷");

    private final String name;

    PricePeriodEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
