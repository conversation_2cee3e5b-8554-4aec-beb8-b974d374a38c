package com.wifochina.common.util;

import cn.hutool.core.collection.CollectionUtil;

import com.wifochina.common.config.ElectricPriceSystemConfig;
import com.wifochina.common.constants.ElectricPriceSpanEnum;
import com.wifochina.modules.electric.vo.ElectricPriceSystemData;
import com.wifochina.modules.electric.vo.ElectricPriceSystemRegionsIntervalData;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created on 2024/5/29 14:02.
 *
 * <AUTHOR>
 */
public class RestElectricPriceSystemUtils {
    private static ElectricPriceSystemConfig electricPriceSystemConfig;

    private static RestTemplate restTemplate;

    public static void setConfig(ElectricPriceSystemConfig config) {
        electricPriceSystemConfig = config;
    }

    public static void setRestTemplate(RestTemplate template) {
        restTemplate = template;
    }

    /**
     * ## 文档地址 http://192.168.130.69:8000/swagger/index.html
     *
     * <p>## 签名认证 接口接入时会进行签名认证，签名通过header方式传入 sign: <sign> key是sign，value是生成的签名串，生成签名的方式： 1.
     * 生成待加密字符串：authString = name + timestamp + salt 2. 使用sha256对待加密字符串加密串：auth = Sha256(authString)
     * 3. 截取加密串10~20位，并在其前面拼接上name和timestamp，分隔符：“:”，再对结果进行base64编码：sign =
     * base64(name:timestamp:auth)
     *
     * <p>```go timestamp := time.Now().Unix() // 秒级时间戳 name := "" salt := "M3T5vWSoK4B4oZu4" s :=
     * utils.Sha256(name + timestamp + salt) sign := utils.Base64StdEncode(name + ":" + timestamp +
     * ":" + s[10:20]) http.Request.Header("sign",sign) ``` * name 表示请求来源应用名称 * timestamp
     * 请求时间戳(秒级)，请求超过300s则认为请求已失效 * salt 加密使用的key，调用接口时会提供给调用方
     *
     * @return
     */
    private static String getSign(long timestamp) {
        // 获取当前时间戳（秒级）
        timestamp = timestamp / 1000;
        // long timestamp = l / 1000;
        // 生成待加密字符串
        String authString =
                electricPriceSystemConfig.getName()
                        + timestamp
                        + electricPriceSystemConfig.getSalt();

        // 使用SHA-256对待加密字符串进行加密
        String sha256Hash = MessageDigestUtils.sha256(authString);

        // 截取加密串的第10到20位
        String authSubString = sha256Hash.substring(10, 20);
        // 生成签名
        return MessageDigestUtils.base64Encode(
                electricPriceSystemConfig.getName() + ":" + timestamp + ":" + authSubString);
    }

    @Data
    @Accessors(chain = true)
    public static class RegionsAndIntervalData {
        Set<String> intervals = new HashSet<>();
        Set<String> regions = new HashSet<>();
    }

    /**
     * 获取 国家的 区域 和 interval
     *
     * @param date : 哪一天的
     * @param context : context
     * @return : RegionsAndIntervalData
     */
    public static RegionsAndIntervalData getRegionAndInterval(String date, DataContext context) {
        // 获取前一天的 日期 字符串类型
        RegionsAndIntervalData result = new RegionsAndIntervalData();
        long timestamp = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Sign", getSign(timestamp));
        // 创建HttpEntity，包含请求头
        HttpEntity<String> entity = new HttpEntity<>(headers);
        // 使用签名进行API调用
        String apiUrl =
                UriComponentsBuilder.fromUriString(
                                electricPriceSystemConfig.getEndpoint()
                                        + electricPriceSystemConfig
                                                .getElectricPriceRegionsIntervalUrl())
                        .queryParam("country", context.country())
                        .queryParam("startTime", context.startTime())
                        .queryParam("endTime", context.endTime())
                        .queryParam("dataSource", electricPriceSystemConfig.getDataSource())
                        .queryParam("_t", timestamp)
                        // 接口需要 指定日期
                        .queryParam("date", date)
                        .toUriString();

        ResponseEntity<ElectricPriceSystemRegionsIntervalData> response =
                restTemplate.exchange(
                        apiUrl,
                        HttpMethod.GET,
                        entity,
                        ElectricPriceSystemRegionsIntervalData.class);
        ElectricPriceSystemRegionsIntervalData body = response.getBody();
        if (body != null && CollectionUtil.isNotEmpty(body.getData())) {
            /**
             * 这种结构 没有把区域和interval 分开, 只能自己分组了 { "code": 0, "data": [ { "data_source": "Entsoe",
             * "country": "Germany", "region": "DE", "interval_seconds": 900 }, { "data_source":
             * "Entsoe", "country": "Germany", "region": "DE", "interval_seconds": 3600 } ], "msg":
             * "ok" }
             */
            Set<String> regions =
                    body.getData().stream()
                            .collect(
                                    Collectors.groupingBy(
                                            ElectricPriceSystemRegionsIntervalData.Data::getRegion))
                            .keySet();

            Set<Integer> intervals =
                    body.getData().stream()
                            .collect(
                                    Collectors.groupingBy(
                                            ElectricPriceSystemRegionsIntervalData.Data
                                                    ::getIntervalSeconds))
                            .keySet();
            Set<String> intervalSpans =
                    intervals.stream()
                            .map(
                                    i -> {
                                        ElectricPriceSpanEnum spanEnum =
                                                ElectricPriceSpanEnum.getByValue(i);
                                        return spanEnum.getDesc();
                                    })
                            .collect(Collectors.toSet());
            // 获取到 regions 和 intervals
            result = new RegionsAndIntervalData().setIntervals(intervalSpans).setRegions(regions);
        }
        return result;
    }

    public static ElectricPriceSystemData getData(DataContext context) {
        long timestamp = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Sign", getSign(timestamp));
        // 创建HttpEntity，包含请求头
        HttpEntity<String> entity = new HttpEntity<>(headers);
        // 使用签名进行API调用
        String apiUrl =
                UriComponentsBuilder.fromUriString(
                                electricPriceSystemConfig.getEndpoint()
                                        + electricPriceSystemConfig.getElectricPriceUrl())
                        .queryParam("country", context.country())
                        .queryParam("region", context.region())
                        .queryParam("startTime", context.startTime())
                        .queryParam("endTime", context.endTime())
                        .queryParam("dataSource", electricPriceSystemConfig.getDataSource())
                        .queryParam("_t", timestamp)
                        .toUriString();
        ResponseEntity<ElectricPriceSystemData> response =
                restTemplate.exchange(
                        apiUrl, HttpMethod.GET, entity, ElectricPriceSystemData.class);
        return response.getBody();
    }

    public interface DataContext {
        /**
         * 开始时间
         *
         * @return : time
         */
        default long startTime() {
            return 0;
        }

        /**
         * 结束时间
         *
         * @return : time
         */
        default long endTime() {
            return 0;
        }

        /**
         * 区域 (一个国家的区域)
         *
         * @return : 区域 如 SE1
         */
        default String region() {
            return null;
        }

        /**
         * 国家 en_us 标识
         *
         * @return : en_us 国家码
         */
        String country();
    }
}
