package com.wifochina.common.util;

import lombok.Getter;

import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @since 2023-07-21 5:51 PM
 */
@Getter
public enum NewInfluxTimeCalcEnum {

    /** type time */
    MONTH("month", 1L, ChronoUnit.DAYS),
    YEAR("year", 1L, ChronoUnit.MONTHS),
    DAILY("daily", 1L, ChronoUnit.HOURS);

    private final String type;
    private final Long time;
    private final ChronoUnit unit;

    NewInfluxTimeCalcEnum(String type, Long time, ChronoUnit unit) {
        this.type = type;
        this.time = time;
        this.unit = unit;
    }

    public static NewInfluxTimeCalcEnum getTimeFromType(String type) {
        for (NewInfluxTimeCalcEnum influxTimeCalcEnum : NewInfluxTimeCalcEnum.values()) {
            if (influxTimeCalcEnum.getType().equals(type)) {
                return influxTimeCalcEnum;
            }
        }
        return null;
    }
}
