package com.wifochina.common.util;

import java.util.Collection;
import java.util.Map;

import org.springframework.util.StringUtils;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;

/**
 * <AUTHOR>
 */
public class ServiceAssert {

    /**
     * Assert a boolean expression, throwing an {@code IllegalArgumentException} if the expression evaluates to
     * {@code false}.
     * 
     * <pre class="code">
     * Assert.isTrue(i &gt; 0, "The value must be greater than zero");
     * </pre>
     * 
     * @throws ServiceException
     *             if {@code expression} is {@code false}
     */
    public static void systemError() {
        throw new ServiceException(ErrorResultCode.INTERNAL_SERVER_ERROR.value());
    }

    /**
     * Assert a boolean expression, throwing an {@code IllegalArgumentException} if the expression evaluates to
     * {@code false}.
     * 
     * <pre class="code">
     * Assert.isTrue(i &gt; 0, "The value must be greater than zero");
     * </pre>
     * 
     * @throws ServiceException
     *             if {@code expression} is {@code false}
     */
    public static void serviceError() {
        throw new ServiceException(ErrorResultCode.SERVICE_EXCEPTION.value());
    }

    /**
     * Assert a boolean expression, throwing an {@code IllegalArgumentException} if the expression evaluates to
     * {@code false}.
     * 
     * <pre class="code">
     * Assert.isTrue(i &gt; 0, "The value must be greater than zero");
     * </pre>
     * 
     * @throws ServiceException
     *             if {@code expression} is {@code false}
     */
    public static void unauthorizedError() {
        throw new ServiceException(ErrorResultCode.UNAUTHORIZED.value());
    }

    /**
     * Assert a boolean expression, throwing an {@code IllegalArgumentException} if the expression evaluates to
     * {@code false}.
     * 
     * <pre class="code">
     * Assert.isTrue(i &gt; 0, "The value must be greater than zero");
     * </pre>
     * 
     * @throws ServiceException
     *             if {@code expression} is {@code false}
     */
    public static void illegalAccessError() {
        throw new ServiceException(ErrorResultCode.ILLEGAL_ACCESS.value());
    }

    /**
     * Assert a boolean expression, throwing an {@code IllegalArgumentException} if the expression evaluates to
     * {@code false}.
     * 
     * <pre class="code">
     * Assert.isTrue(i &gt; 0, "The value must be greater than zero");
     * </pre>
     * 
     * @throws ServiceException
     *             if {@code expression} is {@code false}
     */
    public static void argumentError() {
        throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
    }

    /**
     * Assert a boolean expression, throwing an {@code IllegalArgumentException} if the expression evaluates to
     * {@code false}.
     * 
     * <pre class="code">
     * Assert.isTrue(i &gt; 0, "The value must be greater than zero");
     * </pre>
     * 
     * @param expression
     *            a boolean expression
     * @param code
     *            error code
     * @throws ServiceException
     *             if {@code expression} is {@code false}
     */
    public static void isTrue(boolean expression, String code) {
        if (!expression) {
            throw new ServiceException(code);
        }
    }

    /**
     * Assert a boolean expression, throwing an {@code IllegalArgumentException} if the expression evaluates to
     * {@code false}.
     * 
     * <pre class="code">
     * Assert.isTrue(i &gt; 0, "The value must be greater than zero");
     * </pre>
     * 
     * @param expression
     *            a boolean expression
     * @param code
     *            error code
     * @param message
     *            the exception message to use if the assertion fails
     * @throws ServiceException
     *             if {@code expression} is {@code false}
     */
    public static void isTrue(boolean expression, String code, String message) {
        if (!expression) {
            throw new ServiceException(code, message);
        }
    }

    /**
     * Assert that an object is {@code null}.
     * 
     * <pre class="code">
     * Assert.isNull(value, "The value must be null");
     * </pre>
     * 
     * @param object
     *            the object to check
     * @param code
     *            erro code
     * @throws ServiceException
     *             if the object is not {@code null}
     */
    public static void isNull(Object object, String code) {
        if (object == null) {
            throw new ServiceException(code);
        }
    }

    /**
     * Assert that an object is {@code null}.
     * 
     * <pre class="code">
     * Assert.isNull(value, "The value must be null");
     * </pre>
     * 
     * @param object
     *            the object to check
     * 
     * @throws ServiceException
     *             if the object is not {@code null}
     */
    public static void isNull(Object object) {
        if (object == null) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }

    /**
     * Assert that an object is {@code null}.
     * 
     * <pre class="code">
     * Assert.isNull(value, "The value must be null");
     * </pre>
     * 
     * @throws ServiceException
     *             if the object is not {@code null}
     */
    public static <T> void isEmpty(Collection<T> objects) {
        if (null == objects || objects.isEmpty()) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }

    }

    /**
     * Assert that an object is not {@code null}.
     * 
     * <pre class="code">
     * Assert.notNull(clazz, "The class must not be null");
     * </pre>
     * 
     * @param object
     *            the object to check
     * @param code
     *            erro code
     * @throws ServiceException
     *             if the object is {@code null}
     */
    public static void notNull(Object object, String code) {
        if (object == null) {
            throw new ServiceException(code);
        }
    }

    /**
     * Assert that an object is not {@code null}.
     * 
     * <pre class="code">
     * Assert.notNull(clazz, "The class must not be null");
     * </pre>
     * 
     * @param object
     *            the object to check
     * @throws ServiceException
     *             if the object is {@code null}
     */
    public static void notNull(Object object) {
        if (object == null) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }

    /**
     * Assert that the given String is not empty; that is, it must not be {@code null} and not the empty String.
     * 
     * <pre class="code">
     * Assert.isEmpty(name, "Name must not be empty");
     * </pre>
     * 
     * @param text
     *            the String to check
     * @param code
     *            error code
     * @see StringUtils#hasLength
     * @throws ServiceException
     *             if the text is empty
     */
    public static void isEmpty(String text, String code) {
        if (!StringUtil.isEmpty(text)) {
            throw new ServiceException(code);
        }
    }

    /**
     * Assert that the given String is not empty; that is, it must not be {@code null} and not the empty String.
     * 
     * <pre class="code">
     * Assert.isEmpty(name, "Name must not be empty");
     * </pre>
     * 
     * @param text
     *            the String to check
     * @see StringUtils#hasLength
     * @throws ServiceException
     *             if the text is empty
     */
    public static void isEmpty(String text) {
        if (StringUtil.isEmpty(text)) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
    }

    /**
     * Assert that the given text does not contain the given substring.
     * 
     * <pre class="code">
     * Assert.doesNotContain(name, "rod", "Name must not contain 'rod'");
     * </pre>
     * 
     * @param textToSearch
     *            the text to search
     * @param substring
     *            the substring to find within the text
     * @param code
     *            error code
     * @throws ServiceException
     *             if the text contains the substring
     */
    public static void doesNotContain(String textToSearch, String substring, String code) {
        if (!StringUtil.isEmpty(textToSearch) && !StringUtil.isEmpty(substring) && textToSearch.contains(substring)) {
            throw new ServiceException(code);
        }
    }

    /**
     * Assert that an array contains elements; that is, it must not be {@code null} and must contain at least one
     * element.
     * 
     * <pre class="code">
     * Assert.notEmpty(array, "The array must contain elements");
     * </pre>
     * 
     * @param array
     *            the array to check
     * @param code
     *            error code
     * @throws ServiceException
     *             if the object array is {@code null} or contains no elements
     */
    public static void notEmpty(Object[] array, String code) {
        if (null == array || array.length <= 0) {
            throw new ServiceException(code);
        }
    }

    /**
     * Assert that an array contains no {@code null} elements.
     * <p>
     * Note: Does not complain if the array is empty!
     * 
     * <pre class="code">
     * Assert.noNullElements(array, "The array must contain non-null elements");
     * </pre>
     * 
     * @param array
     *            the array to check
     * @param code
     *            error code
     * @throws ServiceException
     *             if the object array contains a {@code null} element
     */
    public static void noNullElements(Object[] array, String code) {
        if (array != null) {
            for (Object element : array) {
                if (element == null) {
                    throw new ServiceException(code);
                }
            }
        }
    }

    /**
     * Assert that a collection contains elements; that is, it must not be {@code null} and must contain at least one
     * element.
     * 
     * <pre class="code">
     * Assert.notEmpty(collection, "Collection must contain elements");
     * </pre>
     * 
     * @param collection
     *            the collection to check
     * @param code
     *            error code
     * @throws ServiceException
     *             if the collection is {@code null} or contains no elements
     */
    public static void nmpty(Collection<?> collection, String code) {
        if (null == collection || collection.isEmpty()) {
            throw new ServiceException(code);
        }
    }

    /**
     * Assert that a Map contains entries; that is, it must not be {@code null} and must contain at least one entry.
     * 
     * <pre class="code">
     * Assert.notEmpty(map, "Map must contain entries");
     * </pre>
     * 
     * @param map
     *            the map to check
     * @param code
     *            error code
     * @throws ServiceException
     *             if the map is {@code null} or contains no entries
     */
    public static void notEmpty(Map<?, ?> map, String code) {
        if (null == map || map.isEmpty()) {
            throw new ServiceException(code);
        }
    }

    /**
     * Assert that the provided object is an instance of the provided class.
     * 
     * <pre class="code">
     * Assert.instanceOf(Foo.class, foo, "Foo expected");
     * </pre>
     * 
     * @param type
     *            the type to check against
     * @param obj
     *            the object to check
     * @param code
     *            error code
     * @param message
     *            a message which will be prepended to provide further context. If it is empty or ends in ":" or ";" or
     *            "," or ".", a full exception message will be appended. If it ends in a space, the name of the
     *            offending object's type will be appended. In any other case, a ":" with a space and the name of the
     *            offending object's type will be appended.
     * @throws ServiceException
     *             if the object is not an instance of type
     */
    public static void isInstanceOf(Class<?> type, Object obj, String code, String message) {
        notNull(type, code);
        if (!type.isInstance(obj)) {
            instanceCheckFailed(type, obj, message);
        }
    }

    private static void instanceCheckFailed(Class<?> type, Object obj, String msg) {
        String className = (obj != null ? obj.getClass().getName() : "null");
        String result = "";
        boolean defaultMessage = true;
        if (!StringUtil.isEmpty(msg)) {
            if (endsWithSeparator(msg)) {
                result = msg + " ";
            } else {
                result = messageWithTypeName(msg, className);
                defaultMessage = false;
            }
        }
        if (defaultMessage) {
            result = result + ("Object of class [" + className + "] must be an instance of " + type);
        }
        throw new IllegalArgumentException(result);
    }

    private static boolean endsWithSeparator(String msg) {
        return (msg.endsWith(":") || msg.endsWith(";") || msg.endsWith(",") || msg.endsWith("."));
    }

    private static String messageWithTypeName(String msg, Object typeName) {
        return msg + (msg.endsWith(" ") ? "" : ": ") + typeName;
    }
}
