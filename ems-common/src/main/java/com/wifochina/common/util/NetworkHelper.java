package com.wifochina.common.util;

import java.io.IOException;
import java.net.*;
import java.util.Enumeration;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.page.Result;
import com.wifochina.modules.group.request.InternetIpRequest;

/**
 * <AUTHOR>
 * @since : 2022-03-03 00:06:18 测试网络连通性 致敬大师，彼岸无岸，当下即是
 */
public class NetworkHelper {

    private static final NetworkHelper INSTANCE = new NetworkHelper();

    public static NetworkHelper getInstance() {
        return INSTANCE;
    }

    public static Result<String> isConnect(InternetIpRequest internetIpRequest) {
        ServiceAssert.isEmpty(internetIpRequest.getIp());
        if (RegexUtil.isNotIp(internetIpRequest.getIp())) {
            throw new ServiceException(ErrorResultCode.IP_ILLEGAL.value());
        }
        ServiceAssert.isNull(internetIpRequest.getPort());
        boolean connectStatus =
            NetworkHelper.getInstance().isReachIpAndPort(internetIpRequest.getIp(), internetIpRequest.getPort());
        if (connectStatus) {
            return Result.success(ErrorResultCode.CONNECT_SUCCESS.value());
        } else {
            return Result.success(ErrorResultCode.CONNECT_FAILED.value());
        }
    }

    /**
     * 获取能与远程主机指定端口建立连接的本机ip地址
     */
    public Boolean isReachIpAndPort(String remoteIp, int port) {
        boolean isReach = false;

        InetAddress remoteAddr;
        Enumeration<NetworkInterface> netInterfaces;
        try {
            remoteAddr = InetAddress.getByName(remoteIp);
            netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> localAddrs = ni.getInetAddresses();
                while (localAddrs.hasMoreElements()) {
                    InetAddress localAddr = localAddrs.nextElement();
                    if (isReachable(localAddr, remoteAddr, port, 10000)) {
                        isReach = true;
                        break;
                    }
                }
            }
        } catch (Exception ignored) {
        }
        return isReach;
    }

    /**
     * 测试local InetAddr能否与远程的主机指定端口建立连接相连
     **/
    public boolean isReachable(InetAddress localInetAddr, InetAddress remoteInetAddr, int port, int timeout) {
        boolean isReachable = false;
        try (Socket socket = new Socket()) {
            // 端口号设置为 0 表示在本地挑选一个可用端口进行连接
            SocketAddress localSocketAddr = new InetSocketAddress(localInetAddr, 0);
            socket.bind(localSocketAddr);
            InetSocketAddress endpointSocketAddr = new InetSocketAddress(remoteInetAddr, port);
            socket.connect(endpointSocketAddr, timeout);
            isReachable = true;
        } catch (IOException ignored) {
        }
        return isReachable;
    }

}
