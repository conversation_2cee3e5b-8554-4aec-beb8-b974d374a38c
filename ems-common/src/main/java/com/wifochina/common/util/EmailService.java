package com.wifochina.common.util;

import com.wifochina.common.constants.EmailLogTypeEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.modules.notice.entity.EmailLogEntity;
import com.wifochina.modules.notice.service.EmailLogService;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * <AUTHOR>
 * @since 2023-12-08 4:46 PM
 */
@Component
@Slf4j
public class EmailService {

    @Resource private JavaMailSender mailSender;
    private final EmailLogService emailLogService;

    @Value("${spring.mail.username}")
    private String emailFrom;

    public EmailService(EmailLogService emailLogService) {
        this.emailLogService = emailLogService;
    }

    /**
     * 简单发送邮件 不需要附件 不需要 记录发送邮件的日志 目前供给 验证码调用的时候 使用这个方法
     *
     * @param receivers : receivers
     * @param subject : subject
     * @param message : message
     */
    public void sendMessage(List<String> receivers, String subject, String message) {
        MimeMessage mimeMessage = this.mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setBcc(receivers.toArray(String[]::new));
            helper.setFrom(emailFrom);
            helper.setSubject(subject);
            helper.setText(message);
            mailSender.send(mimeMessage);
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送邮件 支持 记录日志 提供一下业务信息ServiceInfo
     *
     * @see ServiceInfoHolder
     * @see ServiceInfo
     * @param receivers: receivers
     * @param subject : subject
     * @param message : message
     * @param holder : 业务相关信息holder
     */
    public void sendMessage(
            List<String> receivers, String subject, String message, ServiceInfoHolder holder) {
        MimeMessage mimeMessage = this.mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            send(receivers, subject, message, mimeMessage, helper, holder);
        } catch (MessagingException e) {
            log.error("发送邮件失败 msg: {}", e.getMessage());
        }
    }

    /**
     * 发送邮件 支持 记录日志 提供一下业务信息ServiceInfo 和 附件扩展支持
     *
     * @see ServiceInfoHolder
     * @see ServiceInfo
     * @param receivers: receivers
     * @param subject : subject
     * @param message : message
     * @param callback : 附件处理回调
     * @param holder : 业务相关信息holder
     */
    public void sendMessage(
            List<String> receivers,
            String subject,
            String message,
            MimeMessageHelperCallback callback,
            ServiceInfoHolder holder) {
        MimeMessage mimeMessage = this.mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            // 调用 callback 处理 helper， 如添加附件等
            callback.processHelper(helper);
            send(receivers, subject, message, mimeMessage, helper, holder);
        } catch (MessagingException e) {
            log.error("发送邮件失败 msg: {}", e.getMessage());
        }
    }

    /**
     * 发送邮件的具体实现
     *
     * @see ServiceInfoHolder
     * @see ServiceInfo
     * @param receivers: receivers
     * @param subject : subject
     * @param message : message
     * @param helper : MimeMessageHelper
     * @param holder : 业务相关信息holder
     */
    private void send(
            List<String> receivers,
            String subject,
            String message,
            MimeMessage mimeMessage,
            MimeMessageHelper helper,
            ServiceInfoHolder holder) {
        // 防止 传入null
        if (holder.getServiceInfo() == null) {
            throw new ServiceException("ServiceInfoHolder.getServiceInfo() 不能为空");
        }
        EmailLogEntity emailLogEntity =
                new EmailLogEntity()
                        .setEmail(receivers.stream().reduce((a, b) -> a + "," + b).orElse(""))
                        .setSubject(subject)
                        .setContent(message)
                        .setType(holder.getServiceInfo().emailLogTypeEnum.getType())
                        .setGroupId(holder.getServiceInfo().getGroupId())
                        .setProjectId(holder.getServiceInfo().getProjectId())
                        .setRetry(0)
                        .setTime((System.currentTimeMillis()));
        try {
            helper.setBcc(receivers.toArray(String[]::new));
            helper.setFrom(emailFrom);
            helper.setSubject(subject);
            helper.setText(message);
            mailSender.send(mimeMessage);
            emailLogEntity.setSuccess(true);
        } catch (Exception e) {
            log.error(
                    "Send邮件失败 receivers:{}, subject:{}, message:{},  errorMsg: {}",
                    receivers,
                    subject,
                    message,
                    e.getMessage());
            emailLogEntity.setSuccess(false);
        } finally {
            emailLogService.save(emailLogEntity);
        }
    }

    public interface MimeMessageHelperCallback {
        /**
         * 后置处理 MimeMessageHelper
         *
         * @param helper : MimeMessageHelper
         */
        void processHelper(MimeMessageHelper helper);
    }

    public interface ServiceInfoHolder {
        /**
         * 获取业务相关信息
         *
         * @return : ServiceInfo
         */
        ServiceInfo getServiceInfo();
    }

    @Data
    @Accessors(chain = true)
    public static class ServiceInfo {
        private String projectId;
        private String groupId;
        private EmailLogTypeEnum emailLogTypeEnum;
    }
}
