package com.wifochina.common.util;

import java.util.regex.Pattern;

/**
 * @date 6/1/2022 9:02 AM
 * <AUTHOR>
 * @version 1.0
 */
public class RegexUtil {

    /**
     * Ip正则表达式校验不合法返回true
     */
    public static boolean isNotIp(String content) {
        String pattern = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";
        return !Pattern.matches(pattern, content);
    }

    /**
     * Ip正则表达式校验
     */
    public static boolean isPhone(String content) {
        if (content.length() > 16 || content.length() < 9) {
            return false;
        }
        String pattern = "^[+]*[(]?[0-9]{1,4}[)]?[-\\s./0-9]*$";
        return Pattern.matches(pattern, content);
    }

    /**
     * password 正则表达式校验
     */
    public static boolean checkPassword(String password) {
        String pattern = "^[A-Za-z0-9]{6,16}$";
        boolean isMatch = Pattern.matches(pattern, password);
        return isMatch;
    }

    /**
     * username 正则表达式校验
     */
    public static boolean checkUserName(String username) {
        String pattern = "^[A-Za-z0-9]{5,24}$";
        boolean isMatch = Pattern.matches(pattern, username);
        return isMatch;
    }
}
