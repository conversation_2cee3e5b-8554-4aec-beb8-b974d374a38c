package com.wifochina.common.util;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023-07-21 5:51 PM
 */
@Getter
public enum LindormTimeCalcEnum {

    /**
     * type time
     */
    MONTH("month", "1dc"), YEAR("year", "1nc"), DAILY("daily", "1h");

    private final String type;
    private final String time;

    LindormTimeCalcEnum(String type, String time) {
        this.type = type;
        this.time = time;
    }

    public static String getTimeFromType(String type) {
        for (LindormTimeCalcEnum lindormTimeCalcEnum : LindormTimeCalcEnum.values()) {
            if (lindormTimeCalcEnum.getType().equals(type)) {
                return lindormTimeCalcEnum.getTime();
            }
        }
        return null;
    }

}
