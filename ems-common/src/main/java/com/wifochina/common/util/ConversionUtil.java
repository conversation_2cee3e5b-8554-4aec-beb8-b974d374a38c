package com.wifochina.common.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-02-24 1:26 PM
 */
public class ConversionUtil {

    public static <T> List<T> objToList(Object obj, Class<T> clazz) {
        List<T> list = new ArrayList<>();
        if (obj instanceof ArrayList<?>) {
            for (Object o : (List<?>)obj) {
                list.add(clazz.cast(o));
            }
        }
        return list;
    }
}
