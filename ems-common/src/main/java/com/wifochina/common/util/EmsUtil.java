package com.wifochina.common.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Objects;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.GroupAmmeterService;
import com.wifochina.modules.group.service.GroupDeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.project.request.ZeroPowerContext;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.service.UserService;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-03-06 2:40 PM
 */
public class EmsUtil {

    @Data
    @Accessors(chain = true)
    public static class LoadParams {
        private double emsOutPower = 0.0;
        private double gridOutPower = 0.0;
        private double emsInPower = 0.0;
        private double pvExternalPower = 0.0;
        private double windPower = 0.0;
        private double dieselPower = 0.0;
        private double gasPower = 0.0;
        private double pilePower = 0.0;
        private double wasterPower = 0.0;
    }

    public static double loadPower(LoadParams loadParams) {
        return loadParams.getEmsOutPower()
                + loadParams.getPvExternalPower()
                + loadParams.getWindPower()
                + loadParams.getDieselPower()
                + loadParams.getGasPower()
                + loadParams.getWasterPower()
                - loadParams.getGridOutPower()
                - loadParams.getEmsInPower()
                - loadParams.getPilePower();
    }

    public static int getPeriod(Long start, Long end, int interval) {
        MyTimeUtil.checkTime(start, end);
        // 相差的天数
        int periodTime = (int) ((end - start) / 86400);
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        return (periodTime / 2 + 1) * interval;
    }

    public static List<String> getMeterIdsFromGroupAmmeterEntity(
            List<GroupAmmeterEntity> list, String type, AmmeterService ammeterService) {
        List<String> ammeterIdsTemp =
                list.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .map(String::valueOf)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ammeterIdsTemp)) {
            return Collections.emptyList();
        }
        return ammeterService
                .list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .in(AmmeterEntity::getId, ammeterIdsTemp)
                                .eq(AmmeterEntity::getType, type))
                .stream()
                .map(AmmeterEntity::getId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    public static List<String> getGridMeterIdsByGroupId(
            String groupId,
            AmmeterService ammeterService,
            GroupAmmeterService groupAmmeterService) {
        List<String> groupMeterIds =
                groupAmmeterService
                        .lambdaQuery()
                        .eq(GroupAmmeterEntity::getGroupId, groupId)
                        .list()
                        .stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupMeterIds)) {
            return Collections.emptyList();
        }
        return ammeterService
                .lambdaQuery()
                .eq(AmmeterEntity::getType, 2)
                .in(AmmeterEntity::getId, groupMeterIds)
                .list()
                .stream()
                .map(AmmeterEntity::getId)
                .collect(Collectors.toList());
    }

    public static List<String> getDeviceIdsByGroupId(
            String groupId, GroupDeviceService groupDeviceService) {
        return groupDeviceService
                .lambdaQuery()
                .eq(GroupDeviceEntity::getGroupId, groupId)
                .list()
                .stream()
                .map(GroupDeviceEntity::getDeviceId)
                .collect(Collectors.toList());
    }

    public static List<String> getMeterIds(
            String projectId,
            String groupId,
            String itemId,
            Integer type,
            GroupAmmeterService groupAmmeterService,
            GroupService groupService,
            AmmeterService ammeterService) {
        List<String> ammeterIds = new ArrayList<>();
        // 根据请求中的itemId和groupId获取有效的GroupAmmeterEntity列表
        List<GroupAmmeterEntity> groupAmmeterEntities;
        if (Objects.equal(itemId, EmsConstants.ALL) || !StringUtils.hasLength(itemId)) {
            // 获取项目下的所有GroupEntity
            List<GroupEntity> groupEntities =
                    groupService.lambdaQuery().eq(GroupEntity::getProjectId, projectId).list();
            if (Objects.equal(groupId, EmsConstants.ALL) || !StringUtils.hasLength(groupId)) {
                // 如果groupId也为ALL或为空，则根据所有group的id查询GroupAmmeterEntity
                groupAmmeterEntities =
                        groupAmmeterService
                                .lambdaQuery()
                                .in(
                                        GroupAmmeterEntity::getGroupId,
                                        groupEntities.stream()
                                                .map(GroupEntity::getId)
                                                .collect(Collectors.toList()))
                                .list();
            } else {
                // 否则根据指定groupId查询GroupAmmeterEntity
                groupAmmeterEntities =
                        groupAmmeterService
                                .lambdaQuery()
                                .eq(GroupAmmeterEntity::getGroupId, groupId)
                                .list();
            }
        } else {
            // 如果itemId不为ALL且非空，则直接添加到ammeterIds中
            ammeterIds.add(itemId);
            // 此处假设当itemId不为ALL时，无需查询数据库获取GroupAmmeterEntity
            groupAmmeterEntities = Collections.emptyList();
        }

        // 对查询结果进行过滤，只保留符合type要求的AmmeterEntity
        if (!groupAmmeterEntities.isEmpty()) {
            List<String> ammeterIdsTemp =
                    groupAmmeterEntities.stream()
                            .map(GroupAmmeterEntity::getAmmeterId)
                            .filter(id -> ammeterService.getById(id).getType().equals(type))
                            .distinct()
                            .collect(Collectors.toList());
            ammeterIds.addAll(ammeterIdsTemp);
        }
        return ammeterIds;
    }

    public static String createDeviceSqlForInfluxDb(List<String> deviceIds) {
        StringBuilder deviceIdSql = new StringBuilder("(");
        for (int i = 0; i < deviceIds.size(); i++) {
            if (i == 0) {
                deviceIdSql.append("r.deviceId ==\"").append(deviceIds.get(i)).append("\"");
            } else {
                deviceIdSql.append(" or r.deviceId == \"").append(deviceIds.get(i)).append("\"");
            }
        }
        deviceIdSql.append(")");
        return deviceIdSql.toString();
    }

    public static String createDeviceSqlForLindormDb(List<String> deviceIds) {
        StringBuilder deviceIdSql = new StringBuilder("and (");
        for (int i = 0; i < deviceIds.size(); i++) {
            if (i == 0) {
                deviceIdSql.append("deviceId ='").append(deviceIds.get(i)).append("'");
            } else {
                deviceIdSql.append(" or deviceId = '").append(deviceIds.get(i)).append("'");
            }
        }
        deviceIdSql.append(")");
        return deviceIdSql.toString();
    }

    public static String createMeterSqlForSite(List<String> ammeterIds) {
        StringBuilder ammeterIdSql = new StringBuilder("(");
        for (int i = 0; i < ammeterIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("r.ammeterId ==\"").append(ammeterIds.get(i)).append("\"");
            } else {
                ammeterIdSql.append(" or r.ammeterId == \"").append(ammeterIds.get(i)).append("\"");
            }
        }
        ammeterIdSql.append(")");
        return ammeterIdSql.toString();
    }

    public static String createMeterSqlForCloud(List<String> ammeterIds) {
        StringBuilder ammeterIdSql = new StringBuilder("(");
        for (int i = 0; i < ammeterIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("r.meterId ==\"").append(ammeterIds.get(i)).append("\"");
            } else {
                ammeterIdSql.append(" or r.meterId == \"").append(ammeterIds.get(i)).append("\"");
            }
        }
        ammeterIdSql.append(")");
        return ammeterIdSql.toString();
    }

    public static int getEmsStatusSupportReactivePower(
            double emsDesignPower,
            double emsInPower,
            double emsOutPower,
            double emsReactiveInPower,
            double emsReactiveOutPower,
            boolean isOff,
            boolean hasEmsRun,
            ZeroPowerContext zeroPowerContext) {

        double diffPower = emsOutPower - emsInPower;
        double diffReactivePower = emsReactiveOutPower - emsReactiveInPower;
        // 默认离线
        int status = EmsStatusEnum.OFFLINE.getStatus();
        if (!isOff) {
            if (hasEmsRun) {
                // 待机
                if (Math.abs(diffPower) <= (emsDesignPower * 0.005)) {
                    if (zeroPowerContext != null && zeroPowerContext.isOpenZeroController()) {
                        zeroPowerContext.setSetAcActiveZero(true);
                    }
                    // 且 无功额定功率
                    if (Math.abs(diffReactivePower) <= (emsDesignPower * 0.005)) {
                        if (zeroPowerContext != null && zeroPowerContext.isOpenZeroController()) {
                            zeroPowerContext.setSetAcReactiveZero(true);
                        }
                        status = EmsStatusEnum.STANDBY.getStatus();
                    } else {
                        // 如果有功功率小于额定功率的千分之5，目无功功率的值大于额定有功功率值的千分之5，显示 运行状态
                        status = EmsStatusEnum.RUNNING.getStatus();
                    }
                } else if (diffPower > 0) {
                    status = EmsStatusEnum.DISCHARGING.getStatus();
                } else {
                    status = EmsStatusEnum.CHARGING.getStatus();
                }
            } else {
                status = EmsStatusEnum.STOP.getStatus();
            }
        }
        // 设置上状态
        return status;
    }

    public static int getEmsStatus(
            double emsDesignPower,
            double emsInPower,
            double emsOutPower,
            boolean isOff,
            boolean hasEmsRun,
            ZeroPowerContext zeroPowerContext) {
        double diffPower = emsOutPower - emsInPower;
        // 默认离线
        int status = EmsStatusEnum.OFFLINE.getStatus();
        if (!isOff) {
            if (hasEmsRun) {
                // 待机
                if (Math.abs(diffPower) <= (emsDesignPower * 0.005)) {
                    if (zeroPowerContext != null && zeroPowerContext.isOpenZeroController()) {
                        zeroPowerContext.setSetAcActiveZero(true);
                    }
                    status = EmsStatusEnum.STANDBY.getStatus();
                } else if (diffPower > 0) {
                    status = EmsStatusEnum.DISCHARGING.getStatus();
                } else {
                    status = EmsStatusEnum.CHARGING.getStatus();
                }
            } else {
                status = EmsStatusEnum.STOP.getStatus();
            }
        }
        // 设置上状态
        return status;
    }

    public static Double getPowerFactor(Double activePowerValue, Double reactivePowerValue) {
        Double apparentPower =
                Math.sqrt((Math.pow(activePowerValue, 2) + Math.pow(reactivePowerValue, 2)));
        return (Math.abs(activePowerValue / apparentPower));
    }

    public static String replaceTime(Long start, Long end, String queryString) {
        return queryString
                .replace("{start}", String.valueOf(start * 1000))
                .replace("{end}", String.valueOf(end * 1000));
    }

    public static String queryReplaceTime(Long start, Long end, String queryString) {
        LocalDateTime startDate = LocalDateTime.ofEpochSecond(start, 0, ZoneOffset.UTC);
        LocalDateTime endDate = LocalDateTime.ofEpochSecond(end - 240, 0, ZoneOffset.UTC);
        // 开始时间不晚于结束时间，否则返回时间错误异常
        ServiceAssert.isTrue(!endDate.isBefore(startDate), ErrorResultCode.TIME_OVERLAPPED.value());
        DateTimeFormatter stdUtcFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        queryString =
                queryString
                        .replace("{start}", stdUtcFormat.format(startDate))
                        .replace("{end}", stdUtcFormat.format(endDate));
        return queryString;
    }

    public static boolean isContainAd(String userId, UserService userService) {
        boolean containAdEmail;
        if (ApplicationHolder.isCloudDeployType()) {
            containAdEmail =
                    java.util.Objects.equals(
                            java.util.Objects.requireNonNull(SecurityUtil.getPrincipal())
                                    .getUserEntity()
                                    .getRole(),
                            "2");
        } else {
            UserEntity userEntity = userService.getById(userId);
            containAdEmail =
                    EmsConstants.USER_ROLE_AD.equals(userEntity.getRole())
                            || EmsConstants.USER_ROLE_SUPER.equals(userEntity.getRole());
        }
        return containAdEmail;
    }

    public static List<String> findDuplicateNumbers(List<String> numbers) {
        // 使用 Stream API 统计频率
        Map<String, Long> frequencyMap =
                numbers.stream()
                        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        // 筛选出重复的序列号
        return frequencyMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
}
