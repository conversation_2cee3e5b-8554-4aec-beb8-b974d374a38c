package com.wifochina.common.util;

import com.wifochina.modules.country.entity.CountryEntity;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created on 2024/6/5 13:54.
 *
 * <AUTHOR>
 */
@Service
public class CacheUtils {

    private static final Map<Integer, CountryEntity> COUNTRY_ENTITY_MAP = new HashMap<>();

    public synchronized void setCountryEntityMap(Integer key, CountryEntity countryEntity) {
        COUNTRY_ENTITY_MAP.put(key, countryEntity);
    }

    /**
     * return probably null
     *
     * @param key : country id
     * @return : countryEntity or null
     */
    public CountryEntity getCountry(Integer key) {
        return COUNTRY_ENTITY_MAP.get(key);
    }

    /**
     * 判断是否是中国
     *
     * @param key : 国家id
     * @return : boolean
     */
    public boolean isChina(Integer key) {
        CountryEntity country = getCountry(key);
        return country != null
                && !StringUtil.isEmpty(country.getEnUs())
                && EmsConstants.CHINA_EN_US.equals(country.getEnUs());
    }
}
