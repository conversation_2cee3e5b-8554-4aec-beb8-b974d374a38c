package com.wifochina.common.util;

/**
 * Created on 2024/6/17 14:40.
 *
 * <AUTHOR>
 */
public class ApplicationHolder {
    private static String TIME_SERIES = "";
    private static boolean CLOUD_DEPLOY_TYPE;

    public static synchronized void setTimeSeries(String key) {
        ApplicationHolder.TIME_SERIES = key;
    }

    public static synchronized void setDeployType(boolean cloudDeployFlag) {
        ApplicationHolder.CLOUD_DEPLOY_TYPE = cloudDeployFlag;
    }

    public static String getTimeSeries() {
        return ApplicationHolder.TIME_SERIES;
    }

    public static boolean isCloudDeployType() {
        return CLOUD_DEPLOY_TYPE;
    }

    public static String getEquipmentTypeKey(String measurement) {
        // TODO?  maybe other
        return "type";
    }

    /**
     * 无力回天 !!!!!!!!!!!!!! 历史遗留问题太严重 * @param measurement
     *
     * @return :
     */
    public static String getEquipmentKey(String measurement) {
        if (isCloudDeployType()) {
            // 云版本
            if (measurement.contains(EmsConstants.EMS)) {
                return EmsConstants.DEVICE_ID;
            } else if (measurement.contains(EmsConstants.CONTROLLABLE)) {
                // TODO 可能改成 ctr1Id
                return "ctrlId";
            } else {
                return EmsConstants.METER_ID;
            }
        } else {
            // 场站版本
            if (measurement.contains(EmsConstants.STATIONS_METER)) {
                return EmsConstants.AMMETER_ID;
            } else if (measurement.contains(EmsConstants.CONTROLLABLE_SITE)) {
                // TODO 可能改成 ctr1Id
                return "ctrlId";
            } else {
                return EmsConstants.DEVICE_ID;
            }
        }
    }

    public static String getGroupKey() {
        return EmsConstants.GROUP_ID;
    }
}
