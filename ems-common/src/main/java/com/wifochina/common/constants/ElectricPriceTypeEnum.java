package com.wifochina.common.constants;

/**
 * <AUTHOR>
 * @date 2023-06-28 12:09 PM
 */
public enum ElectricPriceTypeEnum {
    /** 原来尖峰平谷的方式 */
    PEAK_VALLEYS_PRICE_PERIOD("PEAK_VALLEYS_PRICE_PERIOD"),
    /** 自定义时间段的 方式 */
    FIXED_PRICE_PERIOD("FIXED_PRICE_PERIOD"),
    /** 实时电价时段的 方式 */
    DYNAMIC_PRICE_PERIOD("DYNAMIC_PRICE_PERIOD");

    private final String value;

    ElectricPriceTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
