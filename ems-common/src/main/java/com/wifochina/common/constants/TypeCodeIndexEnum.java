package com.wifochina.common.constants;

import lombok.Getter;

/**
 * @since 5/24/2022 6:07 PM
 * <AUTHOR>
 * @version 1.0
 */
@Getter
public enum TypeCodeIndexEnum {
    /**
     * 空调
     */
    AIR("air", 6),

    /**
     * 消防
     */
    FIRE("fire", 8),

    /**
     * 水冷
     */
    WATER_COOLER("water", 15),

    /**
     * 功率转换器
     */
    PCS("pcs", 2),

    /**
     * 能量管理系统
     */
    BMS("bms", 4),

    /**
     * 系统（ems)
     */
    SYSTEM("system", 0),

    DCDC("dcdc", 20001),

    //不同index的可能不一样，-1 代表不可以用这个解析
    STATES("states", -1);

    private final String code;

    private final int index;

    TypeCodeIndexEnum(String code, int index) {
        this.code = code;
        this.index = index;
    }

}
