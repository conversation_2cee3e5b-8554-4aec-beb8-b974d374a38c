package com.wifochina.common.constants;

import com.wifochina.common.exception.ServiceException;

/**
 * Created on 2025/2/17 10:09.
 *
 * <AUTHOR>
 */
public enum RenewableTypeReportEnum {
    PV("PV", "光伏收益"),
    WIND("WIND", "风电收益"),
    WASTER("WASTER", "余热收益");
    private final String name;

    private final String incomeName;

    RenewableTypeReportEnum(String name, String incomeName) {
        this.name = name;
        this.incomeName = incomeName;
    }

    public static CalculateTypeEnum transform(RenewableTypeReportEnum renewableTypeReportEnum) {
        CalculateTypeEnum transformType = null;
        for (CalculateTypeEnum calculateTypeEnum : CalculateTypeEnum.values()) {
            if (calculateTypeEnum.name().equals(renewableTypeReportEnum.name)) {
                transformType = calculateTypeEnum;
                break;
            }
        }
        if (transformType == null) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        return transformType;
    }

    public String getIncomeName() {
        return incomeName;
    }
}
