package com.wifochina.common.constants;

import com.wifochina.common.exception.ServiceException;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023-06-28 12:09 PM
 */
public enum ElectricPriceSpanEnum {
    MINUTE_60(3600, "MINUTE_60"),
    MINUTE_30(1800, "MINUTE_30"),
    MINUTE_15(900, "MINUTE_15");

    private final Integer value;
    private final String desc;

    ElectricPriceSpanEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getValue(String type) {
        return ElectricPriceSpanEnum.valueOf(type).getValue();
    }

    public static ElectricPriceSpanEnum getByValue(Integer value) {
        return Arrays.stream(ElectricPriceSpanEnum.values())
                .filter(electricPriceSpanEnum -> electricPriceSpanEnum.getValue().equals(value))
                .findFirst()
                .orElseThrow(() -> new ServiceException("cant find ElectricPriceSpanEnum"));
    }
}
