package com.wifochina.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 告警通知方式类型
 *
 * <AUTHOR>
 * @date 2025/8/5 19:23
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum AlarmNotificationEnum {

    EMAIL("0"),
    SMS("1"),
    PHONE_CALL("2");

    private final String code;

    public static boolean containsEmail(String notificationType) {
        return notificationType != null && notificationType.contains(EMAIL.getCode());
    }

    public static boolean containsSms(String notificationType) {
        return notificationType != null && notificationType.contains(SMS.getCode());
    }

    public static boolean containsPhoneCall(String notificationType) {
        return notificationType != null && notificationType.contains(PHONE_CALL.getCode());
    }
}
