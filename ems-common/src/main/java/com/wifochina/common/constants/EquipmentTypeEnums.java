package com.wifochina.common.constants;

/**
 * Created on 2025/2/20 09:53.<br>
 * 扩展 equipment 是 meter + device(ems) 集合吧 以后如果需要用到 可以用这个枚举类来处理
 *
 * <AUTHOR>
 */
public enum EquipmentTypeEnums {
    PV_METER("PV电表", 1),
    GRID_METER("并网点电表", 2),
    LOAD_METER("负载电表", 3),
    WIND_METER("风电电表", 4),
    DIESEL_METER("柴油电表", 5),
    PILE_METER("充电桩电表", 6),
    COMMON_METER("通用电表", 7),
    GAS_METER("燃气电表", 8),
    // 2023-11-08 10:40:29 add 新增储能电表 用于计算收益 替代以前的通用电表+标签方式
    EMS_METER("储能电表", 9),

    // 2024-03-11 13:42:43 add 余热发电 类型的电表
    WASTER_METER("余热发电", 10),

    EMS_DEVICE("EMS设备", 0);

    private final String name;
    private final Integer type;

    EquipmentTypeEnums(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public Integer meterType() {
        return type;
    }

    public static EquipmentTypeEnums getEquipmentByType(String equipmentType) {
        for (EquipmentTypeEnums transactType : values()) {
            if (transactType.name().equals(equipmentType)) {
                // 获取指定的枚举
                return transactType;
            }
        }
        return null;
    }

    public static EquipmentTypeEnums getMeterType(Integer type) {
        for (EquipmentTypeEnums transactType : values()) {
            if (transactType.meterType().equals(type)) {
                // 获取指定的枚举
                return transactType;
            }
        }
        return null;
    }
}
