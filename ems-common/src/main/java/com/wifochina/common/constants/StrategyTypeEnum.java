package com.wifochina.common.constants;

import lombok.Getter;

@Getter
public enum StrategyTypeEnum {
    // 国内的就是这个
    PEAK_AND_VALLEY(1, "峰谷策略"),
    // 国外的 选择了固定电价的
    FIXED_TIME(3, "固定时段策略"),
    /** 5,6,7 以后只有 7 没有 5 , 6 了 前端统一认为7是 自定义策略 */
    @Deprecated
    MAX_DIFFERENCE(5, "动态-最大差价"),
    @Deprecated
    PRICE_DEFINED(6, "动态-电价定值"),
    // 国外的 选择了 实时电价的
    TIMING_CHARGE_DISCHARGE(7, "动态-定时充放"),

    AUTOMATIC_MODE(8, "动态-全自动模式");

    private final int type;

    StrategyTypeEnum(int type, String desc) {
        this.type = type;
    }

    public static StrategyTypeEnum getStrategyType(int type) {
        for (StrategyTypeEnum strategyTypeEnum : StrategyTypeEnum.values()) {
            if (strategyTypeEnum.getType() == type) {
                return strategyTypeEnum;
            }
        }
        return null;
    }

    public static int getStrategyType(String electricPriceType) {
        if (ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD.getValue().equals(electricPriceType)) {
            return PEAK_AND_VALLEY.getType();
        }

        if (ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.getValue().equals(electricPriceType)) {
            return FIXED_TIME.getType();
        }

        if (ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.getValue().equals(electricPriceType)) {
            return TIMING_CHARGE_DISCHARGE.getType();
        }
        return 1;
    }
}
