package com.wifochina.common.constants;

/**
 * Created on 2023/11/8 09:51.
 *
 * <AUTHOR>
 */
public enum MeterTypeEnum {
    PV("PV电表", 1),
    GRID("并网点电表", 2),
    LOAD("负载电表", 3),
    WIND("风电电表", 4),
    DIESEL("柴油电表", 5),
    PILE("充电桩电表", 6),
    COMMON("通用电表", 7),
    GAS("燃气电表", 8),
    // 2023-11-08 10:40:29 add 新增储能电表 用于计算收益 替代以前的通用电表+标签方式
    EMS("储能电表", 9),

    // 2024-03-11 13:42:43 add 余热发电 类型的电表
    WASTER("余热发电", 10);

    private final String name;
    private final Integer type;

    MeterTypeEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public Integer meterType() {
        return type;
    }

    public static MeterTypeEnum getMeterType(Integer type) {
        for (MeterTypeEnum transactType : values()) {
            if (transactType.meterType().equals(type)) {
                // 获取指定的枚举
                return transactType;
            }
        }
        return null;
    }
}
