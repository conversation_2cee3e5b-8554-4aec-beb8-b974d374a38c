package com.wifochina.common.constants;

import cn.hutool.core.collection.CollectionUtil;

import com.wifochina.common.exception.ServiceException;

import com.wifochina.modules.group.entity.GroupEntity;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created on 2023/11/8 09:51. 光伏发电模式 枚举
 *
 * <AUTHOR>
 */
@Getter
public enum RenewableModelEnum {
    FULL_INTERNET_ACCESS_MODEL("全额上网模式", 1),
    SELF_USE_PERIOD_MODEL("自发自用(时段电价)", 2),
    SELF_USE_AGREEMENT_MODEL("自发自用(协议电价)", 3);

    /** 描述 */
    private final String name;

    /** 值 */
    private final int value;

    public static RenewableModelEnum getRenewableModel(
            GroupEntity systemGroupEntity, CalculateTypeEnum calculateTypeEnum) {
        int value = 0;
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.PV.name())) {
            value = systemGroupEntity.getPhotovoltaicModel();
        }
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WIND.name())) {
            value = systemGroupEntity.getWindPowerModel();
        }
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WASTER.name())) {
            value = systemGroupEntity.getWasterPowerModel();
        }
        return getModel(value);
    }

    public static RenewableModelEnum getModel(int value) {
        List<RenewableModelEnum> collect =
                Arrays.stream(RenewableModelEnum.values())
                        .filter(pvWindModelEnum -> pvWindModelEnum.value == value)
                        .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect) && collect.size() == 1) {
            return collect.get(0);
        }
        throw new ServiceException("getModel can`t not find by value: " + value);
    }

    RenewableModelEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }
}
