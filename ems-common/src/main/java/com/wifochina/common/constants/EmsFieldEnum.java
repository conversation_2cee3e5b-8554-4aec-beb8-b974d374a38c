package com.wifochina.common.constants;

/**
 * Created on 2023/11/6 18:41.
 *
 * <AUTHOR>
 */
public enum EmsFieldEnum {
    BMS_CELL_HIGH_VOLTAGE("bms高电压", "bms_cell_high_voltage"),
    BMS_CELL_LOW_VOLTAGE("bms低电压", "bms_cell_low_voltage"),

    BMS_CELL_HIGH_TEMPERATURE("bms高温度", "bms_cell_high_temperature"),
    BMS_CELL_LOW_TEMPERATURE("bms低温度", "bms_cell_low_temperature"),
    DCDC_METER_POWER("dcdc_meter_power", "dcdc_meter_power"),

    BMS_VOLTAGE("bms_voltage", "bms_voltage"),

    BMS_SOC("bms_soc", "bms_soc"),
    EMS_SOC("ems_soc", "ems_soc"),

    EMS_AC_ACTIVE_POWER("ems_ac_active_power", "ems_ac_active_power"),

    EMS_AC_REACTIVE_POWER("ems_ac_reactive_power", "ems_ac_reactive_power"),
    EMS_HISTORY_INPUT_ENERGY("ems_history_input_energy", "ems_history_input_energy"),

    EMS_HISTORY_OUTPUT_ENERGY("ems_history_output_energy", "ems_history_output_energy"),

    DCDC_METER_HISTORY_ENERGY_POS("dcdc_meter_history_energy_pos", "dcdc_meter_history_energy_pos"),

    POWER_RESULT_UDP_EMS_REACTIVE("power_result_udp_ems_reactive", "power_result_udp_ems_reactive"),
    POWER_RESULT_UDP_REACTIVE_POWER_COMMAND(
            "power_result_udp_reactive_power_command", "power_result_udp_reactive_power_command"),
    POWER_RESULT_UDP_EMS_ACTIVE("power_result_udp_ems_active", "power_result_udp_ems_active"),
    POWER_RESULT_UDP_ACTIVE_POWER_COMMAND(
            "power_result_udp_active_power_command", "power_result_udp_active_power_command"),
    EMS_AC_ACTIVE_POWER_POS("ems_ac_active_power_pos", "ems_ac_active_power_pos"),

    EMS_AC_ACTIVE_POWER_NEG("ems_ac_active_power_neg", "ems_ac_active_power_neg");

    private final String name;
    private final String field;

    EmsFieldEnum(String name, String field) {
        this.name = name;
        this.field = field;
    }

    public String field() {
        return field;
    }
}
