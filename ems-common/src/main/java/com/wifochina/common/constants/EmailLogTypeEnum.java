package com.wifochina.common.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-03-12 15:18:49
 */
@Getter
public enum EmailLogTypeEnum {
    /** 需量超了 */
    COUNTRY_OVER("需量超了", 1),

    /** 月初需量初始化错误 */
    COUNTRY_INIT_ERROR("月初需量初始化错误", 2),

    /** 收益月报表 */
    BILL_MONTH("收益月报表", 3),

    /** 收益年报表 */
    BILL_YEAR("收益年报表", 4),

    /** 动态电价每日定时策略下发 */
    DYNAMIC_PRICE_STRATEGY("动态电价每日定时策略下发", 5),

    STRATEGY_DELIVER_ERROR("自动抬升策略下发失败", 6);

    private final String msg;

    private final int type;

    EmailLogTypeEnum(String msg, int type) {
        this.msg = msg;
        this.type = type;
    }
}
