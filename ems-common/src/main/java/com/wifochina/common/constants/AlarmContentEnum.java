package com.wifochina.common.constants;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/8/5 19:23
 * @version 1.0
 */
@Getter
public enum AlarmContentEnum {

    /**
     * 系统内部错误
     */
    PROJECT_FAULT(0, "项目故障"),
    PROJECT_ALARM(1, "项目告警"),
    PROJECT_EARNING(2, "项目收益"),
    DEVICE_STOP(3, "设备停机"),
    DEVICE_OFFLINE(4, "设备离线"),
    SYSTEM_EFFICIENCY(5, "系统效率");


    private final int code;
    private final String name;

    AlarmContentEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AlarmContentEnum getAlarmContentEnum(int code) {
        AlarmContentEnum[] values = AlarmContentEnum.values();
        return Arrays.stream(values).filter(i -> code == i.code)
                .findFirst()
                .orElse(null);
    }

    public static String getName(int code) {
        AlarmContentEnum[] values = AlarmContentEnum.values();
        return Arrays.stream(values).filter(i -> code == i.code)
                .map(AlarmContentEnum::getName)
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为项目故障或项目告警
     */
    public static boolean isProjectFaultOrAlarm(AlarmContentEnum alarmContentEnum) {
        return PROJECT_ALARM.equals(alarmContentEnum) ||
                PROJECT_FAULT.equals(alarmContentEnum);
    }
}
