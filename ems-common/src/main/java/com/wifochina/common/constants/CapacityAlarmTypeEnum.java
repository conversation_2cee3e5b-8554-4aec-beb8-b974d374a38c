package com.wifochina.common.constants;

/**
 * Created on 2023/11/8 09:51.
 *
 * <AUTHOR>
 */
public enum CapacityAlarmTypeEnum {
    HAPPEN("发生", 1),
    DISAPPEAR("消失", 0),
    LOG_RECORD("日志记录", -1);

    private final String name;
    private final Integer type;

    CapacityAlarmTypeEnum(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public static CapacityAlarmTypeEnum fromType(Integer type) {
        CapacityAlarmTypeEnum[] values = values();
        for (CapacityAlarmTypeEnum value : values) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }

    public Integer type() {
        return type;
    }
}
