package com.wifochina.common.constants;

/**
 * Created on 2023/11/6 18:41.
 *
 * <AUTHOR>
 */
public enum MeterFieldEnum {
    DC_POWER("直流功率", "dc_power"),
    DC_CURRENT("直流current", "dc_current"),
    AC_ACTIVE_POWERS_0("交流功率00", "ac_active_powers_0"),
    AC_ACTIVE_POWERS_1("交流功率01", "ac_active_powers_1"),
    AC_ACTIVE_POWERS_2("交流功率02", "ac_active_powers_2"),
    AC_ACTIVE_POWER("总交流功率", "ac_active_power"),
    MAX_AC_ACTIVE_POWER("最大交流功率", "max_ac_active_power"),
    MAX_AC_REACTIVE_POWER("最大无功功率", "max_ac_reactive_power"),

    // 无功功率
    AC_REACTIVE_POWER("总无功功率", "ac_reactive_power"),
    AC_REACTIVE_POWERS_0("交流无功功率00", "ac_reactive_powers_0"),
    AC_REACTIVE_POWERS_1("交流无功功率01", "ac_reactive_powers_1"),
    AC_REACTIVE_POWERS_2("交流无功功率02", "ac_reactive_powers_2"),

    // 电压
    DC_VOLTAGE("直流电压", "dc_voltage"),
    AC_VOLTAGE("总交流电压", "ac_voltage"),
    AC_VOLTAGES_0("交流电压00", "ac_voltages_0"),
    AC_VOLTAGES_1("交流电压01", "ac_voltages_1"),
    AC_VOLTAGES_2("交流电压02", "ac_voltages_2"),

    // 频率
    FREQUENCY("频率", "frequency"),

    // 历史输入输出
    AC_HISTORY_POSITIVE_POWER_IN_KWH("交流历史输出电量", "ac_history_positive_power_in_kwh"),
    AC_HISTORY_NEGATIVE_POWER_IN_KWH("交流历史输入电量", "ac_history_negative_power_in_kwh"),
    DC_HISTORY_POSITIVE_POWER_IN_KWH("直流历史输出电量", "dc_history_positive_power_in_kwh"),
    DC_HISTORY_NEGATIVE_POWER_IN_KWH("直流历史输入电量", "dc_history_negative_power_in_kwh"),

    AC_HISTORY_POSITIVE_POWER_IN_KWH_1("历史正向有功电量尖", "ac_history_positive_power_in_kwh_1"),
    AC_HISTORY_POSITIVE_POWER_IN_KWH_2("历史正向有功电量峰", "ac_history_positive_power_in_kwh_2"),
    AC_HISTORY_POSITIVE_POWER_IN_KWH_3("历史正向有功电量平", "ac_history_positive_power_in_kwh_3"),
    AC_HISTORY_POSITIVE_POWER_IN_KWH_4("历史正向有功电量谷", "ac_history_positive_power_in_kwh_4"),
    AC_HISTORY_POSITIVE_POWER_IN_KWH_5("历史正向有功电量深谷", "ac_history_positive_power_in_kwh_5"),
    AC_HISTORY_NEGATIVE_POWER_IN_KWH_1("历史反向有功电量尖", "ac_history_negative_power_in_kwh_1"),
    AC_HISTORY_NEGATIVE_POWER_IN_KWH_2("历史反向有功电量峰", "ac_history_negative_power_in_kwh_2"),
    AC_HISTORY_NEGATIVE_POWER_IN_KWH_3("历史反向有功电量平", "ac_history_negative_power_in_kwh_3"),
    AC_HISTORY_NEGATIVE_POWER_IN_KWH_4("历史反向有功电量谷", "ac_history_negative_power_in_kwh_4"),
    AC_HISTORY_NEGATIVE_POWER_IN_KWH_5("历史反向有功电量深谷", "ac_history_negative_power_in_kwh_5"),
    AC_MAX_POSITIVE_ACTIVE_POWER("最大需量正向有功", "ac_max_positive_active_power"),
    AC_MAX_POSITIVE_REACTIVE_POWER("最大需量正向无工", "ac_max_positive_reactive_power"),
    AC_MAX_NEGATIVE_ACTIVE_POWER("最大需量反向有功", "ac_max_negative_active_power"),
    AC_MAX_NEGATIVE_REACTIVE_POWER("最大需量方向无工", "ac_max_negative_reactive_power"),

    MAX_NEGATIVE_ACTIVE_POWER_DEMAND("电表最大需量数据", "max_negative_active_power_demand"),

    WASTER_IN("余热发电交流功率", "ac_active_power"),
    WASTER_OUT("余热发电无功功率", "ac_reactive_power"),

    CONTROL_POWER("控制需量", "control_power"),
    METER_POWER("Meter功率", "meter_power");

    private final String name;
    private final String field;

    MeterFieldEnum(String name, String field) {
        this.name = name;
        this.field = field;
    }

    public String field() {
        return field;
    }
}
