package com.wifochina.common.constants;

import lombok.Getter;

/**
 * @since 5/24/2022 6:07 PM
 * <AUTHOR>
 * @version 1.0
 */
@Getter
public enum EquipNumberEnum {
    /**
     * 空调
     */
    AIR("air", 5),

    /**
     * 消防
     */
    FIRE("fire", 7),

    /**
     * 水冷
     */
    WATER_COOLER("water", 14),

    /**
     * 电池簇数量
     */
    CLUSTER("cluster", 1005),
    /**
     * 每簇电池组数量
     */
    STACK("stack", 1006),
    /**
     * 每组电芯数量
     */
    CELL("cell", 1007),
    /**
     * 每组电芯温度传感器数量
     */
    CELLT("cellT", 1008),
    /**
     * 功率转换器
     */
    PCS("pcs", 1),

    /**
     * 能量管理系统
     */
    BMS("bms", 14),

    DCDC("dcdc", 20000),


    STATES("states", 20002);

    private final String code;

    private final int index;

    EquipNumberEnum(String code, int index) {
        this.code = code;
        this.index = index;
    }

}
