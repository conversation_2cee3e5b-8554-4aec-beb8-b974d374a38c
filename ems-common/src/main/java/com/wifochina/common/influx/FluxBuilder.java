package com.wifochina.common.influx;

import cn.hutool.core.collection.CollectionUtil;

import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.FilterFlux;
import com.influxdb.query.dsl.functions.FromFlux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.ApplicationHolder;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;

import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;

/**
 * Created on 2024/7/25 14:37.
 *
 * <AUTHOR>
 */
public class FluxBuilder {
    private static ProjectService projectService;

    public static void setProjectService(ProjectService projectService) {
        FluxBuilder.projectService = projectService;
    }

    public InfluxQueryBucketStage project(ProjectEntity project) {
        return new InfluxQueryBucketStage(project);
    }

    public InfluxQueryBucketStage projectId(String projectId) {
        return new InfluxQueryBucketStage(projectService.getById(projectId));
    }

    public static void valueKey(String key) {}

    @AllArgsConstructor
    @Accessors(chain = true)
    public static class InfluxQueryBucketStage {
        private ProjectEntity project;
        private String bucket;
        private boolean timeZoneSet = false;
        private boolean dateSet = false;

        public InfluxQueryBucketStage(ProjectEntity project) {
            if (project == null) {
                throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
            }
            this.project = project;
        }

        public InfluxQueryRangeStage from(String bucket) {
            this.bucket = bucket;
            return new InfluxQueryRangeStage(this);
        }

        public InfluxQueryBucketStage timeZoneSet(boolean setFlag) {
            this.timeZoneSet = setFlag;
            return this;
        }

        //        public InfluxQueryBucketStage dateSet(boolean setFlag) {
        //            this.dateSet = setFlag;
        //            return this;
        //        }
    }

    @AllArgsConstructor
    @Accessors(chain = true)
    public static class InfluxQueryRangeStage {
        private InfluxQueryBucketStage influxQueryBucketStage;
        private Long start;
        private Long end;

        public InfluxQueryRangeStage(InfluxQueryBucketStage influxQueryBucketStage) {
            this.influxQueryBucketStage = influxQueryBucketStage;
        }

        public InfluxQueryMeasurementStage range(Long start, Long end) {
            this.start = start;
            this.end = end;
            return new InfluxQueryMeasurementStage(this);
        }
    }

    @AllArgsConstructor
    @Accessors(chain = true)
    public static class InfluxQueryMeasurementStage {
        private InfluxQueryRangeStage influxQueryRangeStage;
        private String measurement;

        public InfluxQueryMeasurementStage(InfluxQueryRangeStage influxQueryRangeStage) {
            this.influxQueryRangeStage = influxQueryRangeStage;
        }

        public InfluxQueryFilterStage measurement(String measurement) {
            this.measurement = measurement;
            return new InfluxQueryFilterStage(this);
        }
    }

    @AllArgsConstructor
    @Accessors(chain = true)
    public static class InfluxQueryFilterStage {
        private InfluxQueryMeasurementStage influxQueryMeasurementStage;
        private List<String> equipmentIds;
        private List<String> equipmentTypes;
        private String groupId;
        private List<String> fields;
        private String deviceIndex;

        public InfluxQueryFilterStage(InfluxQueryMeasurementStage influxQueryMeasurementStage) {
            this.influxQueryMeasurementStage = influxQueryMeasurementStage;
        }

        public InfluxQueryFilterStage equipmentTypes(List<String> types) {
            this.equipmentTypes = types;
            return this;
        }

        protected List<String> getEquipmentTypes() {
            return equipmentTypes;
        }

        public InfluxQueryFilterStage equipmentIds(List<String> ids) {
            this.equipmentIds = ids;
            return this;
        }

        public InfluxQueryFilterStage groupId(String groupId) {
            this.groupId = groupId;
            return this;
        }

        public InfluxQueryFilterStage fields(List<String> fields) {
            this.fields = fields;
            return this;
        }

        public InfluxQueryFilterStage deviceIndex(String deviceIndex) {
            this.deviceIndex = deviceIndex;
            return this;
        }

        protected List<String> getEquipmentIds() {
            return equipmentIds;
        }

        protected List<String> getFields() {
            return fields;
        }

        public FilterFlux toFlux() {
            return baseFlux(
                    influxQueryMeasurementStage.influxQueryRangeStage.influxQueryBucketStage.bucket,
                    influxQueryMeasurementStage.measurement,
                    influxQueryMeasurementStage
                            .influxQueryRangeStage
                            .influxQueryBucketStage
                            .project,
                    this);
        }
    }

    private static FilterFlux baseFlux(
            String bucket,
            String measurement,
            ProjectEntity project,
            InfluxQueryFilterStage filterStage) {
        FilterFlux baseFilterFlux = null;
        if (StringUtils.hasLength(measurement) && StringUtils.hasLength(bucket)) {
            // 这里是 根据measurement 获取到 对应的key
            String equipmentKey = ApplicationHolder.getEquipmentKey(measurement);
            String equipmentTypeKey = ApplicationHolder.getEquipmentTypeKey(measurement);
            FromFlux from = Flux.from(bucket);
            Flux flux;
            if (filterStage
                    .influxQueryMeasurementStage
                    .influxQueryRangeStage
                    .influxQueryBucketStage
                    .timeZoneSet) {
                flux =
                        from.withLocationFixed(
                                MyTimeUtil.getOffsetSecondsFromZoneCode(project.getTimezone())
                                        + "s");
            } else {
                flux = from;
            }
            baseFilterFlux =
                    flux.range(
                                    filterStage
                                            .influxQueryMeasurementStage
                                            .influxQueryRangeStage
                                            .start,
                                    filterStage
                                            .influxQueryMeasurementStage
                                            .influxQueryRangeStage
                                            .end)
                            .filter(Restrictions.measurement().equal(measurement))
                            .filter(
                                    Restrictions.tag(EmsConstants.PROJECT_ID)
                                            .equal(project.getId()));
            // 如果有设备查询的话 添加设备查询条件
            if (CollectionUtil.isNotEmpty(filterStage.getEquipmentIds())) {
                // 添加 equipment 包括设备or电表 的查询
                baseFilterFlux =
                        baseFilterFlux.filter(
                                Restrictions.or(
                                        filterStage.getEquipmentIds().stream()
                                                .map(
                                                        equipmentId ->
                                                                Restrictions.tag(equipmentKey)
                                                                        .equal(equipmentId))
                                                .toArray(Restrictions[]::new)));
            }
            if (CollectionUtil.isNotEmpty(filterStage.getEquipmentTypes())) {
                baseFilterFlux =
                        baseFilterFlux.filter(
                                Restrictions.or(
                                        filterStage.getEquipmentTypes().stream()
                                                .map(
                                                        equipmentType ->
                                                                Restrictions.tag(equipmentTypeKey)
                                                                        .equal(equipmentType))
                                                .toArray(Restrictions[]::new)));
            }
            if (StringUtils.hasLength(filterStage.groupId)) {
                // 如果有分组
                baseFilterFlux =
                        baseFilterFlux.filter(
                                Restrictions.tag(ApplicationHolder.getGroupKey())
                                        .equal(filterStage.groupId));
            }
            if (CollectionUtil.isNotEmpty(filterStage.getFields())) {
                baseFilterFlux =
                        baseFilterFlux.filter(
                                Restrictions.or(
                                        filterStage.getFields().stream()
                                                .map(field -> Restrictions.field().equal(field))
                                                .toArray(Restrictions[]::new)));
            }
            if (StringUtils.hasLength(filterStage.deviceIndex)) {
                baseFilterFlux =
                        baseFilterFlux.filter(
                                // todo refactor  extract deviceIndex
                                Restrictions.tag("deviceIndex").equal(filterStage.deviceIndex));
            }
        }
        return baseFilterFlux;
    }
}
