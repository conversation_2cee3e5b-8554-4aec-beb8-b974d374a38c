package com.wifochina.common.influx;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.Assert;

import java.text.DecimalFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Created on 2024/7/25 19:00.
 *
 * <AUTHOR>
 */
@Slf4j
public class FluxAdapter {

    private static InfluxClientService influxClientService;
    private static final FluxBuilder BUILDER = new FluxBuilder();
    private static final FluxImportBuilder IMPORT_BUILDER = new FluxImportBuilder();

    public static void setInfluxClientService(
            InfluxClientService influxClientService, ProjectService projectService) {
        FluxAdapter.influxClientService = influxClientService;
        FluxBuilder.setProjectService(projectService);
    }

    public static FluxBuilder builder() {
        return BUILDER;
    }

    public static FluxImportBuilder importBuilder() {
        return IMPORT_BUILDER;
    }

    /**
     * for (FluxTable table : tables) { int ii = 1; for (FluxRecord record : table.getRecords()) {
     * if (ii == 1) { ii++; continue; } ValueVO valueVO = new ValueVO(); Double value = (Double)
     * record.getValueByKey("_value"); if (value != null) {
     * valueVO.setValue(Double.valueOf(String.format("%.2f", value))); Instant time = (Instant)
     * record.getValueByKey("_time"); assert time != null; valueVO.setTime(time.getEpochSecond() -
     * 60); String key = (String) record.getValueByKey(EmsConstants.INFLUX_FIELD);
     * valueVoMap.get(key).add(valueVO); } } }
     */
    public static InfluxPrepareStage query(String influxSql) {
        Assert.notNull(influxClientService, "influxClientService is null");
        log.debug("query influxSql : \n {}", influxSql);
        List<FluxTable> fluxTables = influxClientService.getQueryApi().query(influxSql);
        return new InfluxPrepareStage(fluxTables);
    }

    public static InfluxPrepareStage query(Function<FluxBuilder, String> function) {

        String influxSql = function.apply(BUILDER);
        return query(influxSql);
    }

    @AllArgsConstructor
    public static class InfluxResultStage {

        private FluxResultHandlerContext context;

        public FluxResultHandlerContext getResultContext() {
            return context;
        }

        /**
         * 这个是不过滤 value = null的数据<br>
         * added toListNotFilterNullValue
         *
         * @return : List<ValueVO>
         */
        public List<ValueVO> toListNotFilterNullValue() {
            List<ValueVO> results = new ArrayList<>(1);
            for (FluxTable fluxTable : context.getFluxTables()) {
                // 有时候要丢掉一条数据
                int i = 1;
                for (FluxRecord record : fluxTable.getRecords()) {
                    if (context.isDropFirst()) {
                        if (i == 1) {
                            i++;
                            continue;
                        }
                    }
                    ValueVO valueInfo = new ValueVO();
                    Instant time = (Instant) record.getValueByKey(context.getTimeKey());
                    Double value = (Double) record.getValueByKey(context.getValueKey());
                    if (time != null) {
                        if (context.getDf() != null && value != null) {
                            valueInfo.setValue(Double.parseDouble(context.getDf().format(value)));
                        } else {
                            valueInfo.setValue(value);
                        }
                        valueInfo.setTime(time.getEpochSecond() + context.getTimeOffset());
                        results.add(valueInfo);
                    }
                }
            }
            return results;
        }

        public List<ValueVO> toListFilterNullValue() {
            List<ValueVO> results = new ArrayList<>(1);
            for (FluxTable fluxTable : context.getFluxTables()) {
                // 有时候要丢掉一条数据
                int i = 1;
                for (FluxRecord record : fluxTable.getRecords()) {
                    if (context.isDropFirst()) {
                        if (i == 1) {
                            i++;
                            continue;
                        }
                    }
                    ValueVO valueInfo = new ValueVO();
                    Instant time = (Instant) record.getValueByKey(context.getTimeKey());
                    Double value = (Double) record.getValueByKey(context.getValueKey());
                    if (value != null && time != null) {
                        if (context.getDf() != null) {
                            valueInfo.setValue(Double.parseDouble(context.getDf().format(value)));
                        } else {
                            valueInfo.setValue(value);
                        }
                        valueInfo.setTime(time.getEpochSecond() + context.getTimeOffset());
                        results.add(valueInfo);
                    }
                }
            }
            return results;
        }

        public Map<Long, Double> toDouble() {
            Map<Long, Double> resultMap = new HashMap<>(16);
            for (FluxTable fluxTable : context.getFluxTables()) {
                int i = 1;
                for (FluxRecord record : fluxTable.getRecords()) {
                    if (context.isDropFirst()) {
                        if (i == 1) {
                            i++;
                            continue;
                        }
                    }
                    Instant time = (Instant) record.getValueByKey(context.getTimeKey());
                    Double value = (Double) record.getValueByKey(context.getValueKey());
                    if (value != null && time != null) {
                        if (context.getDf() != null) {
                            value = Double.parseDouble(context.getDf().format(value));
                        }
                        resultMap.put(time.getEpochSecond() + context.getTimeOffset(), value);
                    }
                }
            }
            return resultMap;
        }

        public Map<String, ValueVO> onlyOneMap(List<String> equipmentIds) {
            Map<String, ValueVO> resultMap = new HashMap<>(2);
            int i = 1;
            for (FluxTable fluxTable : context.getFluxTables()) {
                for (FluxRecord record : fluxTable.getRecords()) {
                    ValueVO valueInfo = new ValueVO();
                    Instant time = (Instant) record.getValueByKey(context.getTimeKey());
                    Double value = (Double) record.getValueByKey(context.getValueKey());
                    String deviceId =
                            (String) record.getValueByKey(influxClientService.getDeviceKey());
                    String meterId =
                            (String) record.getValueByKey(influxClientService.getMeterKey());
                    String controlId =
                            (String) record.getValueByKey(influxClientService.getControllableKey());
                    equipmentIds.forEach(
                            equipmentId -> {
                                if (value != null
                                        && time != null
                                        && (equipmentId.equals(deviceId)
                                                || equipmentId.equals(meterId)
                                                || equipmentId.equals(controlId))) {

                                    if (context.getDf() != null) {
                                        valueInfo.setValue(
                                                Double.parseDouble(context.getDf().format(value)));
                                    } else {
                                        valueInfo.setValue(value);
                                    }
                                    valueInfo.setTime(
                                            time.getEpochSecond() + context.getTimeOffset());
                                    resultMap.put(equipmentId, valueInfo);
                                }
                            });
                }
            }
            return resultMap;
        }

        public Map<String, List<ValueVO>> toMap() {
            return toMap(null);
        }

        public Map<String, List<ValueVO>> toMap(String equipmentId) {
            Map<String, List<ValueVO>> resultMap = new HashMap<>(2);
            int i = 1;
            for (FluxTable fluxTable : context.getFluxTables()) {
                for (FluxRecord record : fluxTable.getRecords()) {
                    if (context.isDropFirst() && i == 1) {
                        i++;
                        continue;
                    }
                    processRecord(record, resultMap, equipmentId);
                }
            }
            return resultMap;
        }

        /**
         * 这里有个问题就是 如果 这个属性 因为 某个tag 比如 system : 1 or system: 0 这个不太注意的tag 导致的情况就是 meterId或者deviceId
         * 这种 如果协调控制器 写入了 2个 相同id 的 不同 system tag的 会有2个数据返回, 这里方法 resultMap.putIfAbsent 是不会存入第二条数据的
         * 也就是只存储了第一条数据.
         *
         * @return : Map<String, ValueVO>
         */
        public Map<String, ValueVO> toMapEachEquipmentOnlyValue() {
            Map<String, ValueVO> resultMap = new HashMap<>(2);
            for (FluxTable fluxTable : context.getFluxTables()) {
                for (FluxRecord record : fluxTable.getRecords()) {
                    ValueVO valueInfo = new ValueVO();
                    Double value = (Double) record.getValueByKey(context.getValueKey());
                    String deviceId =
                            (String) record.getValueByKey(influxClientService.getDeviceKey());
                    String meterId =
                            (String) record.getValueByKey(influxClientService.getMeterKey());
                    String controlId =
                            (String) record.getValueByKey(influxClientService.getControllableKey());

                    String realEquipment = null;
                    if (!StringUtil.isEmpty(deviceId)) {
                        realEquipment = deviceId;
                    } else if (!StringUtil.isEmpty(meterId)) {
                        realEquipment = meterId;
                    } else if (!StringUtil.isEmpty(controlId)) {
                        realEquipment = controlId;
                    }
                    if (value != null && !StringUtil.isEmpty(realEquipment)) {
                        if (context.getDf() != null) {
                            valueInfo.setValue(Double.parseDouble(context.getDf().format(value)));
                        } else {
                            valueInfo.setValue(value);
                        }
                        resultMap.putIfAbsent(realEquipment, valueInfo);
                    }
                }
            }
            return resultMap;
        }

        public Map<String, ValueVO> toMapEachFieldOnlyEquipmentOnlyValue() {
            Map<String, ValueVO> resultMap = new HashMap<>(2);
            for (FluxTable fluxTable : context.getFluxTables()) {
                for (FluxRecord record : fluxTable.getRecords()) {
                    ValueVO valueInfo = new ValueVO();
                    Instant time = (Instant) record.getValueByKey(context.getTimeKey());
                    Double value = (Double) record.getValueByKey(context.getValueKey());
                    String deviceId =
                            (String) record.getValueByKey(influxClientService.getDeviceKey());
                    String meterId =
                            (String) record.getValueByKey(influxClientService.getMeterKey());
                    String controlId =
                            (String) record.getValueByKey(influxClientService.getControllableKey());
                    String fieldKey = (String) record.getValueByKey(context.getFieldKey());
                    String realEquipment = null;
                    if (!StringUtil.isEmpty(deviceId)) {
                        realEquipment = deviceId;
                    } else if (!StringUtil.isEmpty(meterId)) {
                        realEquipment = meterId;
                    } else if (!StringUtil.isEmpty(controlId)) {
                        realEquipment = controlId;
                    }
                    if (value != null && !StringUtil.isEmpty(realEquipment)) {
                        if (context.getDf() != null) {
                            valueInfo.setValue(Double.parseDouble(context.getDf().format(value)));
                        } else {
                            valueInfo.setValue(value);
                        }
                        valueInfo.setTime(time.getEpochSecond());
                        resultMap.putIfAbsent(fieldKey, valueInfo);
                    }
                }
            }
            return resultMap;
        }

        /**
         * 多个设备 但个field的情况 按照 每个设备的多个 values list 返回
         *
         * @return
         */
        public Map<String, List<ValueVO>> toMapEachEquipment() {
            Map<String, List<ValueVO>> resultMap = new HashMap<>(2);
            int i = 1;
            for (FluxTable fluxTable : context.getFluxTables()) {
                for (FluxRecord record : fluxTable.getRecords()) {
                    if (context.isDropFirst() && i == 1) {
                        i++;
                        continue;
                    }
                    ValueVO valueInfo = new ValueVO();
                    Instant time = (Instant) record.getValueByKey(context.getTimeKey());
                    Double value = (Double) record.getValueByKey(context.getValueKey());
                    String deviceId =
                            (String) record.getValueByKey(influxClientService.getDeviceKey());
                    String meterId =
                            (String) record.getValueByKey(influxClientService.getMeterKey());
                    String controlId =
                            (String) record.getValueByKey(influxClientService.getControllableKey());

                    String realEquipment = null;
                    if (!StringUtil.isEmpty(deviceId)) {
                        realEquipment = deviceId;
                    } else if (!StringUtil.isEmpty(meterId)) {
                        realEquipment = meterId;
                    } else if (!StringUtil.isEmpty(controlId)) {
                        realEquipment = controlId;
                    }
                    if (value != null && time != null && !StringUtil.isEmpty(realEquipment)) {
                        if (context.getDf() != null) {
                            valueInfo.setValue(Double.parseDouble(context.getDf().format(value)));
                        } else {
                            valueInfo.setValue(value);
                        }
                        valueInfo.setTime(time.getEpochSecond() + context.getTimeOffset());
                        resultMap
                                .computeIfAbsent(realEquipment, k -> new ArrayList<>())
                                .add(valueInfo);
                    }
                }
            }
            return resultMap;
        }

        public ValueVO onlyOneValue() {
            ValueVO valueInfo = new ValueVO();
            for (FluxTable fluxTable : context.getFluxTables()) {
                for (FluxRecord record : fluxTable.getRecords()) {
                    Double value = (Double) record.getValueByKey(context.getValueKey());
                    Instant time = (Instant) record.getValueByKey(context.getTimeKey());
                    valueInfo.setValue(value);
                    if (time != null) {
                        valueInfo.setTime(time.getEpochSecond());
                    }
                    break;
                }
                break;
            }
            return valueInfo;
        }

        private void processRecord(
                FluxRecord record, Map<String, List<ValueVO>> resultMap, String equipmentId) {
            ValueVO valueInfo = new ValueVO();
            Instant time = (Instant) record.getValueByKey(context.getTimeKey());
            Double value = (Double) record.getValueByKey(context.getValueKey());
            String fieldKey = (String) record.getValueByKey(context.getFieldKey());
            String deviceId = (String) record.getValueByKey(influxClientService.getDeviceKey());
            String meterId = (String) record.getValueByKey(influxClientService.getMeterKey());
            String controlId =
                    (String) record.getValueByKey(influxClientService.getControllableKey());

            if (value != null
                            && time != null
                            && (equipmentId == null
                                    || equipmentId.equals(deviceId)
                                    || equipmentId.equals(meterId))
                    || equipmentId.equals(controlId)) {
                if (context.getDf() != null) {
                    valueInfo.setValue(Double.parseDouble(context.getDf().format(value)));
                } else {
                    valueInfo.setValue(value);
                }

                if (context.getUnitConverter() != null) {
                    context.getUnitConverter().converter(valueInfo);
                }
                valueInfo.setTime(time.getEpochSecond() + context.getTimeOffset());
                resultMap.computeIfAbsent(fieldKey, k -> new ArrayList<>()).add(valueInfo);
            }
        }
    }

    @AllArgsConstructor
    public static class InfluxPrepareStage {
        private List<FluxTable> fluxTables;
        private FluxResultHandlerContext context = new FluxResultHandlerContext();

        public List<FluxTable> getFluxTables() {
            return fluxTables;
        }

        public InfluxPrepareStage(List<FluxTable> fluxTables) {
            context.setFluxTables(fluxTables);
        }

        public InfluxPrepareStage timeKey(String timeKey) {
            context.setTimeKey(timeKey);
            return this;
        }

        public InfluxPrepareStage valueKey(String valueKey) {
            context.setValueKey(valueKey);
            return this;
        }

        public InfluxPrepareStage decimal(int decimalPlaces) {
            context.setDf(new DecimalFormat("#." + "0".repeat(Math.max(0, decimalPlaces))));
            return this;
        }

        public InfluxPrepareStage timeOffset(long offset) {
            context.setTimeOffset(offset);
            return this;
        }

        public InfluxPrepareStage unitConverter(UnitConverter unitConverter) {
            context.setUnitConverter(unitConverter);
            return this;
        }

        public InfluxPrepareStage dropFirst() {
            context.setDropFirst(true);
            return this;
        }

        public InfluxPrepareStage fieldKey(String fieldKey) {
            context.setFieldKey(fieldKey);
            return this;
        }

        public InfluxResultStage handleResult() {
            return new InfluxResultStage(this.context);
        }
    }
}
