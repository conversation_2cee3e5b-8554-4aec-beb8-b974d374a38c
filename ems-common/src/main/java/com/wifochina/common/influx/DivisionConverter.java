package com.wifochina.common.influx;

import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.diagram.VO.ValueVO;
import org.springframework.stereotype.Service;

/**
 * Created on 2025/1/22 09:59.
 *
 * <AUTHOR>
 */
@Service
public class DivisionConverter implements UnitConverter {

    @Override
    public void converter(ValueVO valueVO) {
        if (valueVO != null && valueVO.getValue() != null) {
            valueVO.setValue(valueVO.getValue() / EmsConstants.CONSTANT_1000);
        }
    }

    // division 提出去
    public void converter(ValueVO valueVO, int division) {
        if (valueVO != null && valueVO.getValue() != null) {
            valueVO.setValue(valueVO.getValue() / EmsConstants.CONSTANT_1000);
        }
    }
}
