package com.wifochina.common.influx;

import com.influxdb.query.FluxTable;
import com.wifochina.common.util.EmsConstants;

import lombok.Data;

import java.text.DecimalFormat;
import java.util.List;

/**
 * Created on 2024/7/25 19:32.
 *
 * <AUTHOR>
 */
@Data
public class FluxResultHandlerContext {
    private List<FluxTable> fluxTables;
    private String timeKey = EmsConstants.INFLUX_TIME;
    private String valueKey = EmsConstants.INFLUX_VALUE;
    private String fieldKey = EmsConstants.INFLUX_FIELD;
    private DecimalFormat df = null;
    private UnitConverter unitConverter = null;
    private long timeOffset = 0;
    private boolean dropFirst = false;
}
