package com.wifochina.common.mdc.filter;

import com.wifochina.common.util.StringUtil;

import org.slf4j.MDC;

import java.io.IOException;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;

public class MdcFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        try {
            if (request instanceof HttpServletRequest) {
                // 生成或获取 traceId
                String traceId = StringUtil.uuid();
                MDC.put("traceId", traceId);
            }
            chain.doFilter(request, response);
        } finally {
            // 清理 MDC，避免线程污染
            MDC.clear();
        }
    }
}
