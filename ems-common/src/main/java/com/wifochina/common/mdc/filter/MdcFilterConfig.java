package com.wifochina.common.mdc.filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 2025/1/8 19:22.
 *
 * <AUTHOR>
 */
@Configuration
public class MdcFilterConfig {
    @Bean
    public FilterRegistrationBean<MdcFilter> mdcFilter() {
        FilterRegistrationBean<MdcFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new MdcFilter());
        // 拦截所有请求
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }
}
