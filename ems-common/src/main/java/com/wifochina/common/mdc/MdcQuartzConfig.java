package com.wifochina.common.mdc;

/**
 * Created on 2025/1/8 16:57.
 *
 * <AUTHOR>
 */
import com.wifochina.common.job.TraceIdJobListener;

import org.quartz.Scheduler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MdcQuartzConfig {

    private final TraceIdJobListener traceIdJobListener;

    public MdcQuartzConfig(TraceIdJobListener traceIdJobListener) {
        this.traceIdJobListener = traceIdJobListener;
    }

    @Bean
    public Scheduler scheduler(Scheduler scheduler) throws Exception {
        // 注册自定义的 JobListener
        scheduler.getListenerManager().addJobListener(traceIdJobListener);
        return scheduler;
    }
}
