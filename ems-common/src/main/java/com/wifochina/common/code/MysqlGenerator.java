package com.wifochina.common.code;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.querys.MySqlQuery;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.wifochina.modules.BaseEntity;

/**
 * <AUTHOR> Date: 2022-03-03 22:31:33
 *     <p>致敬大师，彼岸无岸，当下即是
 */
public class MysqlGenerator {
    /** 数据源配置 */
    private static final DataSourceConfig.Builder DATA_SOURCE_CONFIG =
            new DataSourceConfig.Builder(
                            "**************************************************************************************************************************",
                            "root",
                            "123@abcd")
                    .dbQuery(new MySqlQuery());

    /** 输出路径 */
    private static final String OUTPUT_DIR =
            "/Users/<USER>/Documents/codeGenerator" + "/src/main/java";

    public static void main(String[] args) {

        FastAutoGenerator.create(DATA_SOURCE_CONFIG)
                .globalConfig(
                        builder -> {
                            // 设置作者
                            builder.author("jacob.sun")
                                    // 开启 swagger 模式
                                    .enableSwagger()
                                    // 指定输出目录
                                    .outputDir(OUTPUT_DIR)
                                    // 禁止打开输出目录
                                    .disableOpenDir();
                        })
                .packageConfig(
                        builder -> {
                            // 设置父包名
                            builder.parent("com.wifochina.modules");
                            builder.moduleName("event");
                        })
                // 设置需要生成的表名
                .strategyConfig(
                        builder ->
                                builder.addInclude("t_meter_event_code")
                                        .addTablePrefix("t_")
                                        .controllerBuilder()
                                        .enableFileOverride()
                                        .enableRestStyle()
                                        .serviceBuilder()
                                        .enableFileOverride()
                                        .entityBuilder()
                                        .enableFileOverride()
                                        .formatFileName("%sEntity")
                                        .superClass(BaseEntity.class)
                                        .enableLombok()
                                        .serviceBuilder()
                                        .formatServiceFileName("%sService")
                                        .controllerBuilder()
                                        .formatFileName("%sController")

                                        // 设置需要生成的表名
                                        .mapperBuilder()
                                        .enableFileOverride())
                // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}
