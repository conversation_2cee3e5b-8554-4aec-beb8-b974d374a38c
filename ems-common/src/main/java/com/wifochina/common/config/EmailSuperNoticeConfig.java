package com.wifochina.common.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "ems.email.notice")
@Data
public class EmailSuperNoticeConfig {


    // 需量超了
    private List<String> demandExceed;
    // 月初需量
    private List<String> demandMonthInit;
    // 动态电价策略下发
    private List<String> strategyDynamicPriceUpload;
}
