package com.wifochina.common.config;

import java.io.IOException;
import java.util.Optional;

import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import com.wifochina.common.util.AuthUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.oauth.util.WebUtils;

/**
 * HeaderRequestInterceptor
 * 
 * @since 7/25/2022 12:08 PM
 * <AUTHOR>
 * @version 1.0
 */
public class RestTemplateRequestInterceptor implements ClientHttpRequestInterceptor {
    @NotNull
    @Override
    public ClientHttpResponse intercept(HttpRequest request, @NotNull byte[] body, ClientHttpRequestExecution execution)
        throws IOException {
        Optional.ofNullable(WebUtils.projectId.get())
            .ifPresent((e) -> request.getHeaders().set(EmsConstants.PROJECT_ID_HEADER, WebUtils.projectId.get()));
        request.getHeaders().set(EmsConstants.GO_AUTH_HEADER, AuthUtil.getAuth());
        return execution.execute(request, body);
    }
}