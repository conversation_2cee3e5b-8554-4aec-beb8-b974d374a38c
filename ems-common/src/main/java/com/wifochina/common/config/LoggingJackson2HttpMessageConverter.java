package com.wifochina.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wifochina.modules.oauth.AccountUser;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

@Slf4j
public class LoggingJackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {

    public LoggingJackson2HttpMessageConverter(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    public Object read(@NotNull Type type, Class<?> contextClass, HttpInputMessage inputMessage)
            throws IOException {

        // 记录响应内容（处理前）
        String body = StreamUtils.copyToString(inputMessage.getBody(), StandardCharsets.UTF_8);

        // 将响应体转换为对象
        Object result = super.read(type, contextClass, inputMessage);

        // 判断是否需要记录日志
        if (shouldLog(type)) {
            log.info("----------------------cover start----------------------------");
            log.info("input json: {}", body);
            log.info("output json: {}", result);
            log.info("----------------------cover end----------------------------");
        }

        return result; // 返回反序列化后的结果
    }

    private boolean shouldLog(Type type) {
        // 判断是否是 AccountResult<AccountUser>
        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;

            // 检查原始类型是否是 AccountResult
            if ("class com.wifochina.modules.oauth.dto.AccountResult"
                    .equals(parameterizedType.getRawType().toString())) {
                // 获取泛型参数类型
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                return actualTypeArguments.length == 1
                        && actualTypeArguments[0] == AccountUser.class; // 泛型参数是 AccountUser
            }
        }
        return false; // 不满足条件
    }
}
