package com.wifochina.common.config;

import com.wifochina.common.util.AuthUtil;
import com.wifochina.common.util.EmsConstants;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

/**
 * HeaderRequestInterceptor
 *
 * @since 7/25/2022 12:08 PM
 * <AUTHOR>
 * @version 1.0
 */
public class LocalGoRequestInterceptor implements ClientHttpRequestInterceptor {
    private final String projectId;
    public LocalGoRequestInterceptor(String projectId){
        this.projectId = projectId;
    }
    @NotNull
    @Override
    public ClientHttpResponse intercept(HttpRequest request, @NotNull byte[] body, ClientHttpRequestExecution execution)
            throws IOException {
        request.getHeaders().set(EmsConstants.PROJECT_ID_HEADER, this.projectId);
        request.getHeaders().set(EmsConstants.GO_AUTH_HEADER, AuthUtil.getAuth());
        return execution.execute(request, body);
    }
}