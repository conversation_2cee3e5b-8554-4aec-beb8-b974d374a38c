package com.wifochina.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-09-26 13:52:36
 */
@Configuration
public class RetryTemplateConfig {

    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        // TODO 这个 3 从环境变量里读取
        // 并且看看要不要设置 后延的那个
        retryTemplate.setRetryPolicy(new SimpleRetryPolicy(3, Map.of(Exception.class, true)));
        return retryTemplate;
    }
}
