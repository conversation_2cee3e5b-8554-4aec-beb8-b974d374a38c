package com.wifochina.common.config;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.kotlin.KotlinModule;

import okhttp3.OkHttpClient;

import org.jetbrains.annotations.NotNull;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.*;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * RestTemplateConfig
 *
 * @since 3/29/2022 2:15 PM
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
public class RestTemplateConfig {

    public static void main(String[] args) {
        long curTime = DateUtil.currentSeconds();
        String authStr = "2" + curTime + "bkrq8uuG3B4rb8GgbLK!";
        String shaStr = DigestUtil.sha256Hex(authStr);
        String token =
                Base64Encoder.encode(
                        String.format("%s:%d:%s", 2, curTime, shaStr.substring(10, 20)));
        System.out.println(token);
        //        RestTemplate restTemplate = new RestTemplate();
        //        OkHttpClient okHttpClient =
        //                new OkHttpClient.Builder()
        //                        .connectTimeout(3, TimeUnit.SECONDS)
        //                        .readTimeout(4, TimeUnit.SECONDS)
        //                        .retryOnConnectionFailure(false)
        //                        .connectionPool(new okhttp3.ConnectionPool(10, 5,
        // TimeUnit.MINUTES))
        //                        .build();
        //
        //        OkHttp3ClientHttpRequestFactory factory = new
        // OkHttp3ClientHttpRequestFactory(okHttpClient);
        //        restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(factory));
        //        restTemplate.setInterceptors(List.of(new LoggingInterceptor()));
        //
        //        restTemplate.getForEntity("http://account-api.dev.weiheng-tech.com/api/v1/login",
        //                new ParameterizedTypeReference<R<UserResDTO>>() {}
        //                )
        //        Map<String, Object> requestBody = new HashMap<>();
        //        requestBody.put("account", "<EMAIL>");
        //        requestBody.put("password", "admin123");
        //        requestBody.put("type", 1);
        //
        //        HttpHeaders httpHeaders = new HttpHeaders();
        //        httpHeaders.add("W-sign", token);
        //        httpHeaders.add("Language", "en_US");
        //        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        //        HttpEntity<Object> objectHttpEntity = new HttpEntity(requestBody, httpHeaders);
        //        ResponseEntity<R<UserResDTO>> exchange =
        //                restTemplate.exchange(
        //                        "http://account-api.dev.weiheng-tech.com/api/v1/login",
        //                        HttpMethod.POST,
        //                        objectHttpEntity,
        //                        new ParameterizedTypeReference<R<UserResDTO>>() {});
        //        R<UserResDTO> body = exchange.getBody();
        //        System.out.println(body);
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 注册 Kotlin 模块
        objectMapper.registerModule(new KotlinModule());
        // 支持 java 8 时间转换
        objectMapper.registerModule(new JavaTimeModule());
        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    //    @Bean
    //    public RestTemplate restTemplate(RestTemplateBuilder builder, ObjectMapper objectMapper) {
    //        OkHttpClient okHttpClient =
    //                new OkHttpClient.Builder()
    //                        .connectTimeout(3, TimeUnit.SECONDS)
    //                        .readTimeout(5, TimeUnit.SECONDS)
    //                        .retryOnConnectionFailure(false)
    //                        .connectionPool(new okhttp3.ConnectionPool(10, 5, TimeUnit.MINUTES))
    //                        .build();
    //        return getRestTemplate(objectMapper, okHttpClient);
    //    }
    //
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder, ObjectMapper objectMapper) {
        OkHttpClient okHttpClient =
                new OkHttpClient.Builder()
                        // 增大超时时间
                        .connectTimeout(5, TimeUnit.SECONDS)
                        // 增大超时时间
                        .readTimeout(10, TimeUnit.SECONDS)
                        // 启用连接失败重试
                        .retryOnConnectionFailure(true)
                        // 增大连接池
                        .connectionPool(new okhttp3.ConnectionPool(50, 5, TimeUnit.MINUTES))
                        .build();
        return createRestTemplate(objectMapper, okHttpClient);
    }

    private static @NotNull RestTemplate createRestTemplate(
            ObjectMapper objectMapper, OkHttpClient okHttpClient) {
        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
        RestTemplate restTemplate = new RestTemplate();
        //        restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(factory));
        restTemplate.setRequestFactory(factory);
        restTemplate.setInterceptors(List.of(new RestTemplateRequestInterceptor()));
        //        restTemplate.setMessageConverters(
        //                List.of(new MappingJackson2HttpMessageConverter(objectMapper))); //
        // 避免自定义日志转换器影响
        return restTemplate;
    }

    //    private static @NotNull RestTemplate getRestTemplate(
    //            ObjectMapper objectMapper, OkHttpClient okHttpClient) {
    //        OkHttp3ClientHttpRequestFactory factory = new
    // OkHttp3ClientHttpRequestFactory(okHttpClient);
    //        RestTemplate restTemplate = new RestTemplate();
    //        // 包装了一下 不然无法重复读取body
    //        restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(factory));
    //        restTemplate.setInterceptors(
    //                List.of(new RestTemplateRequestInterceptor(), new LoggingInterceptor()));
    //        restTemplate.setMessageConverters(
    //                List.of(new LoggingJackson2HttpMessageConverter(objectMapper)));
    //        return restTemplate;
    //    }
}
