package com.wifochina.common.config;

import com.aliyun.oss.OSS;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayInputStream;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/19 14:07
 * @version 1.0
 */
@Slf4j
@Repository
public class OssMapper {

	@Value("${ali.oss.bucket}")
	private String bucket;

	@Resource
	private OSS ossClient;

	public void putObject(String fileName, byte[] bytes) {
		try {
			ossClient.putObject(bucket, fileName, new ByteArrayInputStream(bytes));
		} catch (Exception e) {
			log.warn("oss putObject error: {}", e.getMessage());
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value());
		}
	}

	public void putFile(String bucket, String fileName, byte[] bytes) {
		try {
			ossClient.putObject(bucket, fileName, new ByteArrayInputStream(bytes));
		} catch (Exception e) {
			log.warn("oss putFile error: {}", e.getMessage());
			throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value());
		}
	}
}
