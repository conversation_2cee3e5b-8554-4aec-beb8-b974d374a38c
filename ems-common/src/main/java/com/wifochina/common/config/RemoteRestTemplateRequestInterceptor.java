package com.wifochina.common.config;

import java.io.IOException;

import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import com.wifochina.common.util.EmsConstants;

/**
 * <AUTHOR>
 * @since 2023-11-01 6:26 PM
 */
public class RemoteRestTemplateRequestInterceptor implements ClientHttpRequestInterceptor {
    private final String token;

    private final String projectId;

    public RemoteRestTemplateRequestInterceptor(String projectId, String token){
        this.projectId = projectId;
        this.token = token;
    }
    @NotNull
    @Override
    public ClientHttpResponse intercept(HttpRequest request, @NotNull byte[] body, ClientHttpRequestExecution execution) throws IOException {
        request.getHeaders().set(EmsConstants.PROJECT_ID_HEADER, projectId);
        request.getHeaders().set(EmsConstants.AUTHORIZATION_HEADER, token);
        return execution.execute(request, body);
    }
}
