package com.wifochina.common.config;

import cn.hutool.core.collection.ConcurrentHashSet;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
public class LoggingInterceptor implements ClientHttpRequestInterceptor {

    public static final Set<String> LOG_URL = new ConcurrentHashSet<>();

    @NotNull
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request,
            @NotNull byte[] body,
            @NotNull org.springframework.http.client.ClientHttpRequestExecution execution)
            throws IOException {
        // 执行请求
        ClientHttpResponse response;
        String requestUri = request.getURI().toString();
        if (requestUri.contains("?")) {
            requestUri =
                    request.getURI()
                            .toString()
                            .substring(0, request.getURI().toString().indexOf("?"));
        }
        // 控制一下 只打印自己需要打印的 请求信息 , 因为这个LoggingInterceptor加到了通用的 restTemplate上面了 包含go的一些很多接口 不需要打印日志
        // 执行请求
        try {
            if (LOG_URL.contains(requestUri)) {
                // 打印请求的 URL 和请求体
                // 请求日志输出
                log.info("======================== Request Start ========================");
                log.info("Request URL: {}", request.getURI());
                log.info("Request Method: {}", request.getMethod());
                log.info("Request Headers: {}", request.getHeaders());
                log.info("Request Body: {}", new String(body));
                response = execution.execute(request, body);
                // 响应日志输出
                log.info("Response Status Code: {}", response.getStatusCode());
                byte[] responseBody = StreamUtils.copyToByteArray(response.getBody());
                //                byte[] responseBody = response.getBody().readAllBytes();
                log.info("Response Body: {}", new String(responseBody));
                log.info("======================== Request End ========================");
            } else {
                // 执行请求
                response = execution.execute(request, body);
            }
        } finally {
        }
        return response;
    }
}
