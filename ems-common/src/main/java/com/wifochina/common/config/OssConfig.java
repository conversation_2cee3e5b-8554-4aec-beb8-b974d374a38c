package com.wifochina.common.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/8/19 14:07
 * @version 1.0
 */
@Configuration
public class OssConfig {

	@Value("${ali.oss.key}")
	private String key;

	@Value("${ali.oss.secret}")
	private String secret;

	@Value("${ali.oss.endpoint}")
	private String endpoint;

	@Bean
	public OSS ossClient() {
		return new OSSClientBuilder().build(endpoint, key, secret);
	}
}
