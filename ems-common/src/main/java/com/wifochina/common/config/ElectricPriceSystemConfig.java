package com.wifochina.common.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 2024/5/29 14:03.
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "electricpricesystem")
@Data
public class ElectricPriceSystemConfig {
    private String salt;
    private String name;
    private String endpoint;
    private String electricPriceUrl;
    private String electricPriceRegionsIntervalUrl;
    private String dataSource;
}
