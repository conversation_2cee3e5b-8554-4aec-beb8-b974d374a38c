package com.wifochina.common.job;

/**
 * Created on 2025/1/8 16:55.
 *
 * <AUTHOR>
 */
import com.wifochina.common.util.StringUtil;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.listeners.JobListenerSupport;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

@Component
public class TraceIdJobListener extends JobListenerSupport {

    @Override
    public String getName() {
        return "TraceIdJobListener";
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext context) {
        // 生成或提取 traceId
        String traceId = StringUtil.uuid();
        // 设置到 MDC 中
        MDC.put("traceId", traceId);
    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
        // 清理 MDC，防止线程污染
        MDC.clear();
    }
}
