package com.wifochina.common.job;

import lombok.extern.slf4j.Slf4j;

import org.quartz.*;

/**
 * Created on 2024/3/19 19:23.
 *
 * <AUTHOR>
 */
@Slf4j
public class JobFlow {

    /**
     * 注册Job方法
     *
     * @param scheduler : scheduler
     * @param jobInfo : jobInfo 自行实现
     * @throws RuntimeException : RuntimeException
     */
    public static void registerJob(Scheduler scheduler, JobInfo jobInfo) throws RuntimeException {
        try {
            JobDataMap jobDataMap = new JobDataMap();
            jobInfo.fillData(jobDataMap);
            log.info("registerJob jobDataMap:{}", jobDataMap);
            if (!scheduler.isStarted()) {
                scheduler.start();
            }
            scheduler.scheduleJob(jobInfo.jobDetail(jobDataMap), jobInfo.trigger());
        } catch (SchedulerException e) {
            log.error("scheduler.scheduleJob error :{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public interface JobInfo {

        /**
         * 填充JobDataMap 数据
         *
         * @param jobDataMap : jobDataMap
         */
        void fillData(JobDataMap jobDataMap);

        /**
         * 提供一个 JobDetail
         *
         * @param jobDataMap : jobDataMap
         * @return : JobDetail
         */
        JobDetail jobDetail(JobDataMap jobDataMap);

        /**
         * 提供一个trigger
         *
         * @return : Trigger
         */
        Trigger trigger();
    }
}
