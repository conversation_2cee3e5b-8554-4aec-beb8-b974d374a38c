package com.wifochina.common.price;

import com.wifochina.common.constants.CalculateTypeEnum;
import com.wifochina.common.constants.RenewableModelEnum;
import com.wifochina.common.util.CacheUtils;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.renewable.RenewablePowerDatas;
import com.wifochina.modules.renewable.vo.RenewableProfitVo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.stereotype.Component;

/**
 * Created on 2024/6/22 10:37. 这个类主要是 用来提供 pv收益模式 的不同计算方式
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class RenewableModelPriceFormulaComponent {
    private final CacheUtils cacheUtils;

    public interface ModelBenefitCollectPostProcessor {

        default void renewableBenefitPostProcessor(double benefit) {}
        //        default void pvPostProcessor(double benefit) {}
        //
        //        default void windPostProcessor(double benefit) {}
    }

    /** 根据 pv 收益 模式 收益汇聚 */
    public void modelBenefitCollect(
            CalculateTypeEnum calculateTypeEnum,
            RenewableModelEnum renewableModelEnum,
            RenewableProfitVo renewableProfitVo,
            ModelBenefitCollectPostProcessor processor) {
        // 全额上网 直接取全额上网的收益 -> processor
        if (renewableModelEnum.getValue() == RenewableModelEnum.FULL_INTERNET_ACCESS_MODEL.getValue()) {
            processor.renewableBenefitPostProcessor(
                    renewableProfitVo.getTotalFullInternetAccessBenefit());
            //            if (CalculateTypeEnum.PV.name().equals(calculateTypeEnum.name())) {
            //                processor.renewableBenefitPostProcessor(
            //                        renewableProfitVo.getTotalFullInternetAccessBenefit());
            //            } else if (CalculateTypeEnum.WIND.name().equals(calculateTypeEnum.name()))
            // {
            //                processor.renewableBenefitPostProcessor(
            //                        renewableProfitVo.getTotalFullInternetAccessBenefit());
            //            }
        } else if (renewableModelEnum.getValue() == RenewableModelEnum.SELF_USE_PERIOD_MODEL.getValue()) {
            processor.renewableBenefitPostProcessor(
                    renewableProfitVo.getTotalDischargeSelfBenefit()
                            + renewableProfitVo.getTotalOnlineBenefit());
            // 自发自用 时段 取自用电收益 + 馈网收益-> processor
            //            if (CalculateTypeEnum.PV.name().equals(calculateTypeEnum.name())) {
            //                // 自发自用的模式 总的合计收益是 时段收益+馈网的总收益
            //                processor.renewableBenefitPostProcessor(
            //                        renewableProfitVo.getTotalDischargeSelfBenefit()
            //                                + renewableProfitVo.getTotalOnlineBenefit());
            //
            //            } else if (CalculateTypeEnum.WIND.name().equals(calculateTypeEnum.name()))
            // {
            //                processor.renewableBenefitPostProcessor(
            //                        renewableProfitVo.getTotalDischargeSelfBenefit()
            //                                + renewableProfitVo.getTotalOnlineBenefit());
            //            }

        } else if (renewableModelEnum.getValue()
                // 自发自用 协议电价 取协议电收益 + 馈网收益-> processor
                == RenewableModelEnum.SELF_USE_AGREEMENT_MODEL.getValue()) {
            processor.renewableBenefitPostProcessor(
                    renewableProfitVo.getTotalAgreementBenefit()
                            + renewableProfitVo.getTotalOnlineBenefit());
            //            if (CalculateTypeEnum.PV.name().equals(calculateTypeEnum.name())) {
            //                // 自发自用的 协议电价 模式 总的合计收益是 协议电价收益+馈网的总收益
            //                processor.renewableBenefitPostProcessor(
            //                        renewableProfitVo.getTotalAgreementBenefit()
            //                                + renewableProfitVo.getTotalOnlineBenefit());
            //            }
            //            if (CalculateTypeEnum.WIND.name().equals(calculateTypeEnum.name())) {
            //                // 自发自用的 协议电价 模式 总的合计收益是 协议电价收益+馈网的总收益
            //                processor.renewableBenefitPostProcessor(
            //                        renewableProfitVo.getTotalAgreementBenefit()
            //                                + renewableProfitVo.getTotalOnlineBenefit());
            //            }
        }
    }

    @Data
    @Accessors(chain = true)
    public static class RenewableFormulaBenefit {
        private double fullInternetModelBenefit;
        private double periodModelBenefit;
        private double onlineBenefit;
        private double agreementModelBenefit;
    }

    /**
     * 收益聚合计算
     *
     * @param calculateTypeEnum : enum
     * @param superPrice : 电价配置的 外层 super对象
     * @param periodPrice : 电价配置的内部的时段对象
     * @param projectEntity : project
     * @param renewablePowerDatas : 电量对象
     * @return : RenewableFormulaBenefit
     */
    public RenewableFormulaBenefit collectFormula(
            CalculateTypeEnum calculateTypeEnum,
            ElectricPriceEntity superPrice,
            ElectricPriceEntity periodPrice,
            ProjectEntity projectEntity,
            RenewablePowerDatas renewablePowerDatas) {
        return new RenewableFormulaBenefit()
                // 全额上网模式收益
                .setFullInternetModelBenefit(
                        fullInternetFormula(calculateTypeEnum, superPrice, renewablePowerDatas))
                // 时段模式 收益
                .setPeriodModelBenefit(
                        periodModelFormula(
                                calculateTypeEnum, superPrice, periodPrice, renewablePowerDatas))
                // 协议电价 模式收益
                .setAgreementModelBenefit(
                        agreementModelFormula(
                                calculateTypeEnum, superPrice, projectEntity, renewablePowerDatas))
                // online的额外收益
                .setOnlineBenefit(
                        onlineFormula(
                                calculateTypeEnum, superPrice, projectEntity, renewablePowerDatas));
    }

    /**
     * 全额 上网的收益计算
     *
     * @param calculateTypeEnum : 计算类型 pv or wind
     * @param superPrice : 电价实体的 外层 实体
     * @param renewablePowerDatas : power的holder
     * @return : 收益
     */
    public double fullInternetFormula(
            CalculateTypeEnum calculateTypeEnum,
            ElectricPriceEntity superPrice,
            RenewablePowerDatas renewablePowerDatas) {
        double fullInternetBenefit = 0.0;
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.PV.name())) {
            fullInternetBenefit = superPrice.getPvPrice() * renewablePowerDatas.power();
        }
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WIND.name())) {
            fullInternetBenefit = superPrice.getWindPrice() * renewablePowerDatas.power();
        }
        // 1.4.2 add 余热发电 收益相关
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WASTER.name())) {
            fullInternetBenefit = superPrice.getWasterPrice() * renewablePowerDatas.power();
        }
        return fullInternetBenefit;
    }

    /**
     * 这个和online收益 组成的是 自发自用收益
     *
     * @param calculateTypeEnum : pv or wind
     * @param superPrice : 外层电价实体
     * @param periodPrice : 时段的 实体
     * @param renewablePowerDatas : 电量的holder
     * @return : Pair<时段收益,馈网收益>
     */
    public Double periodModelFormula(
            CalculateTypeEnum calculateTypeEnum,
            ElectricPriceEntity superPrice,
            ElectricPriceEntity periodPrice,
            RenewablePowerDatas renewablePowerDatas) {

        double benefit = 0.0;
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.PV.name())) {
            benefit =
                    (renewablePowerDatas.selfPower()
                            * (periodPrice.getSellPrice() + superPrice.getPvSubsidyPrice()));
        }
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WIND.name())) {
            benefit =
                    (renewablePowerDatas.selfPower()
                            * (periodPrice.getSellPrice() + superPrice.getWindSubsidyPrice()));
        }
        // 1.4.2 add 新增余热发电
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WASTER.name())) {
            benefit =
                    (renewablePowerDatas.selfPower()
                            * (periodPrice.getSellPrice() + superPrice.getWasterSubsidyPrice()));
        }
        return benefit;
    }

    /**
     * online部分的收益
     *
     * @param calculateTypeEnum : pv or wind
     * @param superPrice : 外层电价实体
     * @param projectEntity : 项目
     * @param renewablePowerDatas : 电量的holder
     * @return : Pair<时段收益,馈网收益>
     */
    public double onlineFormula(
            CalculateTypeEnum calculateTypeEnum,
            ElectricPriceEntity superPrice,
            ProjectEntity projectEntity,
            RenewablePowerDatas renewablePowerDatas) {
        double onlineBenefit = 0.0;
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.PV.name())) {
            if (cacheUtils.isChina(projectEntity.getCountry())) {
                // 国内就是使用 (脱硫标杆价格 + 国家补贴) * 自用电
                onlineBenefit =
                        ((superPrice.getPvSubsidyPrice() + superPrice.getPvDfPrice())
                                * renewablePowerDatas.onlinePower());
            } else {
                // if else 的区别就是 中国的是 windDfPrice(脱硫标杆价格) 国外的是 windPrice(上网电价 , 国外这个字段当做 上网电价 ,
                // 国内这个字段当做 全额上网的电价)
                onlineBenefit =
                        ((superPrice.getPvSubsidyPrice() + superPrice.getPvPrice())
                                * renewablePowerDatas.onlinePower());
            }
        }
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WIND.name())) {
            if (cacheUtils.isChina(projectEntity.getCountry())) {
                onlineBenefit =
                        ((superPrice.getWindSubsidyPrice() + superPrice.getWindDfPrice())
                                * renewablePowerDatas.onlinePower());
            } else {
                // if else 的区别就是 中国的是 windDfPrice(脱硫标杆价格) 国外的是 windPrice(上网电价 , 国外这个字段当做 上网电价 ,
                // 国内这个字段当做 全额上网的电价)
                onlineBenefit =
                        ((superPrice.getWindSubsidyPrice() + superPrice.getWindPrice())
                                * renewablePowerDatas.onlinePower());
            }
        }
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WASTER.name())) {
            if (cacheUtils.isChina(projectEntity.getCountry())) {
                onlineBenefit =
                        ((superPrice.getWasterSubsidyPrice() + superPrice.getWasterDfPrice())
                                * renewablePowerDatas.onlinePower());
            } else {
                // if else 的区别就是 中国的是 wasterDfPrice(脱硫标杆价格) 国外的是 wasterPrice(上网电价 , 国外这个字段当做 上网电价 ,
                // 国内这个字段当做 全额上网的电价)
                onlineBenefit =
                        ((superPrice.getWasterSubsidyPrice() + superPrice.getWasterPrice())
                                * renewablePowerDatas.onlinePower());
            }
        }
        return onlineBenefit;
    }

    /**
     * 协议电价 模式的 的收益
     *
     * @param calculateTypeEnum : pv or wind
     * @param superPrice : 外层电价实体
     * @param projectEntity : 项目
     * @param renewablePowerDatas : 电量的holder
     * @return : double
     */
    public double agreementModelFormula(
            CalculateTypeEnum calculateTypeEnum,
            ElectricPriceEntity superPrice,
            ProjectEntity projectEntity,
            RenewablePowerDatas renewablePowerDatas) {
        double agreementBenefit = 0.0;
        // TODO 暂时 被发现说 国内国外 这部分一样 都是 (自用价格 + 国家补贴 ) * 自用电量 暂时先不改这个 if isChina判断
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.PV.name())) {
            if (cacheUtils.isChina(projectEntity.getCountry())) {
                // 国内
                agreementBenefit =
                        (renewablePowerDatas.selfPower()
                                * (superPrice.getPvSelfPrice() + superPrice.getPvSubsidyPrice()));
            } else {
                // 国外的
                agreementBenefit =
                        (renewablePowerDatas.selfPower()
                                * (superPrice.getPvSelfPrice() + superPrice.getPvSubsidyPrice()));
            }
        }
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WIND.name())) {
            if (cacheUtils.isChina(projectEntity.getCountry())) {
                // 国内
                agreementBenefit =
                        (renewablePowerDatas.selfPower()
                                * (superPrice.getWindSelfPrice()
                                        + superPrice.getWindSubsidyPrice()));
            } else {
                // 国外的
                agreementBenefit =
                        (renewablePowerDatas.selfPower()
                                * (superPrice.getWindSelfPrice()
                                        + superPrice.getWindSubsidyPrice()));
            }
        }
        // 1.4.2 2025-02-07 15:04:04 add 余热发电相关
        if (calculateTypeEnum.name().equals(CalculateTypeEnum.WASTER.name())) {
            if (cacheUtils.isChina(projectEntity.getCountry())) {
                // 国内
                agreementBenefit =
                        (renewablePowerDatas.selfPower()
                                * (superPrice.getWasterSelfPrice()
                                        + superPrice.getWasterSubsidyPrice()));
            } else {
                // 国外的
                agreementBenefit =
                        (renewablePowerDatas.selfPower()
                                * (superPrice.getWasterSelfPrice()
                                        + superPrice.getWasterSubsidyPrice()));
            }
        }
        return agreementBenefit;
    }
}
