package com.wifochina.common.retry;

import cn.hutool.core.lang.UUID;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.wifochina.common.retry.data.RetryTaskContext;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.retrytask.entity.RetryFailTaskEntity;
import com.wifochina.modules.retrytask.mapper.RetryFailTaskMapper;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.springframework.retry.RecoveryCallback;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * retry Taks interface 执行器
 *
 * <AUTHOR>
 *     <p>// * @param <T> task T
 */
@Service
@AllArgsConstructor
@Slf4j
public abstract class IRetryTaskExecutor
        implements RetryCallback<Object, Exception>, RecoveryCallback<Object> {

    private static final ThreadLocal<RetryFailTaskEntity> TASK_THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<ContextHolder> CONTEXT_HOLDER_THREAD_LOCAL =
            new ThreadLocal<>();
    private final RetryFailTaskMapper retryFailTaskMapper;
    private static final String TASK_ID = "taskId";
    private static final String CONTEXT_JSON = "contextJson";

    public abstract String taskLabel();

    // 这个bind返回一个对象 这个对象里包括 一个execute 内部使用retryTemplate.execute 这样就链式调用
    public void bindTask(RetryFailTaskEntity failTask) {
        TASK_THREAD_LOCAL.set(failTask);
    }

    public void prepareContext(ContextHolder contextHolder) {
        CONTEXT_HOLDER_THREAD_LOCAL.set(contextHolder);
    }

    @Override
    public Object doWithRetry(RetryContext context) throws Exception {
        RetryFailTaskEntity bindTask = TASK_THREAD_LOCAL.get();
        RetryTaskContext taskContext = new RetryTaskContext();
        ContextHolder contextHolder;
        if (bindTask != null) {
            contextHolder = new ContextHolder();
            // 根据绑定过来的task 获取到 context 比如 执行发送月报某天 这个某天参数
            context.setAttribute(TASK_ID, bindTask.getTaskId());
            taskContext.setTaskId(bindTask.getTaskId());
            taskContext.setDbFailTask(bindTask);
            // 恢复contextJson 丢给doRetry 方便后续操作
            Map<String, Object> object = JSON.parseObject(bindTask.getContextJson(), Map.class);
            object.forEach(contextHolder::put);
        } else {
            // 如果不是从数据库恢复, 就应该是业务执行的
            contextHolder = CONTEXT_HOLDER_THREAD_LOCAL.get();
        }
        try {
            doRetry(taskContext, contextHolder);
            // 把contextJson 设置上去 给recover方法里使用
        } finally {
            context.setAttribute(CONTEXT_JSON, JSON.toJSONString(contextHolder.getContextHolder()));
        }
        return null;
    }

    protected abstract void doRetry(RetryTaskContext retryFailContext, ContextHolder contextHolder);

    @Getter
    @Accessors(chain = true)
    public static class ContextHolder {
        private String projectId;

        private final Map<String, Object> contextHolder = Maps.newConcurrentMap();

        public ContextHolder setProjectId(String projectId) {
            this.projectId = projectId;
            return this;
        }

        public IRetryTaskExecutor.ContextHolder put(String key, Object value) {
            contextHolder.put(key, value);
            return this;
        }

        public Object get(String key) {
            return contextHolder.get(key);
        }
    }

    @Override
    public Object recover(RetryContext context) throws Exception {
        // 这里是重写的recover方法
        String taskId = (String) context.getAttribute(TASK_ID);
        String contextJson = (String) context.getAttribute(CONTEXT_JSON);
        if (StringUtil.isEmpty(taskId)) {
            taskId = UUID.randomUUID().toString();
        }
        RetryFailTaskEntity retryEntity =
                retryFailTaskMapper.selectOne(
                        new LambdaQueryWrapper<RetryFailTaskEntity>()
                                .eq(RetryFailTaskEntity::getTaskId, taskId));
        boolean oldRetryFlag = false;
        if (retryEntity != null) {
            oldRetryFlag = true;
            // 把这几次的 retry count 加上去
            retryEntity.setRetryCount(
                    retryEntity.getRetryCount() + Long.valueOf(context.getRetryCount()));
            retryEntity.setState(false);
            retryEntity.setFailMsg(context.getLastThrowable().getMessage());
        } else {
            retryEntity = new RetryFailTaskEntity();
            retryEntity.setState(false);
            retryEntity.setTaskId(taskId);
            retryEntity.setRetryCount(Long.valueOf(context.getRetryCount()));
            retryEntity.setFailMsg(context.getLastThrowable().getMessage());
        }
        // 执行db
        // 先给它一个entity
        retryEntity.setTaskLabel(taskLabel());
        retryEntity.setContextJson(contextJson);
        // 执行db
        if (oldRetryFlag) {
            retryFailTaskMapper.updateById(retryEntity);
        } else {
            retryFailTaskMapper.insert(retryEntity);
        }
        // 这里是执行任务最后一次执行后失败的情况 然后要清空上下文
        TASK_THREAD_LOCAL.remove();
        return null;
    }
}
