package com.wifochina.common.retry.service;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TestDemandService {

    public void serviceMethod(DemandContext demandContext) {
        log.info(
                "Thread : {} do ... service in time :{} Method projectId:{}",
                Thread.currentThread().getName(),
                demandContext.time(),
                demandContext.projectId());
        try {
            TimeUnit.SECONDS.sleep(20);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        throw new RuntimeException("test exception");
    }

    public interface DemandContext {
        String projectId();

        String time();
    }
}
