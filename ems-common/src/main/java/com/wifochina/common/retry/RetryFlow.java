package com.wifochina.common.retry;

import lombok.Data;

import org.springframework.retry.support.RetryTemplate;

import java.util.function.Supplier;

/**
 * Created on 2024/9/26 10:39.
 *
 * <AUTHOR>
 */
public class RetryFlow {

    public static class RetryExecute {
        private final PrePareContextHolder prePareContextHolder;

        public RetryExecute(PrePareContextHolder prePareContextHolder) {
            this.prePareContextHolder = prePareContextHolder;
        }

        public void execute() {
            try {
                prePareContextHolder.retryTemplate.execute(
                        prePareContextHolder.executor, prePareContextHolder.executor);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Data
    public static class PrePareContextHolder {
        IRetryTaskExecutor executor;
        RetryTemplate retryTemplate;

        public RetryExecute prepareContext(Supplier<IRetryTaskExecutor.ContextHolder> supplier) {
            this.getExecutor().prepareContext(supplier.get());
            return new RetryExecute(this);
        }
    }

    public static class PrePareRetryExecutor {

        private final RetryTemplate retryTemplate;

        public PrePareRetryExecutor(RetryTemplate retryTemplate) {
            this.retryTemplate = retryTemplate;
        }

        public PrePareContextHolder retryExecutor(IRetryTaskExecutor executor) {
            PrePareContextHolder prePareContextHolder = new PrePareContextHolder();
            prePareContextHolder.setExecutor(executor);
            prePareContextHolder.setRetryTemplate(retryTemplate);
            return prePareContextHolder;
        }
    }

    public PrePareRetryExecutor retryTemplate(RetryTemplate retryTemplate) {
        return new PrePareRetryExecutor(retryTemplate);
    }
}
