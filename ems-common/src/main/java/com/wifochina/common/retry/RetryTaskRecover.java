package com.wifochina.common.retry;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wifochina.modules.retrytask.entity.RetryFailTaskEntity;
import com.wifochina.modules.retrytask.mapper.RetryFailTaskMapper;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@Order(1)
@AllArgsConstructor
/** retry recovery 重试任务的恢复逻辑 从db里面恢复任务 */
public class RetryTaskRecover implements ApplicationRunner {

    private final List<IRetryTaskExecutor> iRetryTasks;
    private final RetryFailTaskMapper retryFailTaskMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /** init recover retry task */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("init start recover retry task");
        // RetryTemplate retryTemplate = getRetryTemplateTemp();
        RetryTemplate retryTemplate = new RetryTemplate();
        // 模拟重启的时候从db里读取失败的记录 然后执行对应的方法和任务
        retryTemplate.setRetryPolicy(new SimpleRetryPolicy(3, Map.of(Exception.class, true)));

        List<RetryFailTaskEntity> failLists =
                retryFailTaskMapper.selectList(
                        new LambdaQueryWrapper<RetryFailTaskEntity>()
                                .eq(RetryFailTaskEntity::getState, false));
        // TODO 添加一下分布式锁 防止重复执行
        failLists.forEach(
                failTaskEntity -> {
                    threadPoolTaskExecutor.execute(
                            () -> {
                                try {
                                    IRetryTaskExecutor iRetryExecutor =
                                            getTaskExecutorAndBind(failTaskEntity);
                                    if (iRetryExecutor != null) {
                                        retryTemplate.execute(iRetryExecutor, iRetryExecutor);
                                    }
                                    // 通过failTaskEntity 获取对应的执行器
                                } catch (Exception e) {
                                    log.error("e:msg:{}", e.getMessage());
                                }
                            });
                });
        log.info("init end recover retry task");
    }

    /** 获取TaskExecutor 并且直接把db里失败的Task任务bind上去 */
    private IRetryTaskExecutor getTaskExecutorAndBind(RetryFailTaskEntity dbEntity) {
        IRetryTaskExecutor iRetryExecutor =
                iRetryTasks.stream()
                        .filter(v -> v.taskLabel().equals(dbEntity.getTaskLabel()))
                        .findFirst()
                        .orElse(null);
        if (iRetryExecutor != null) {
            iRetryExecutor.bindTask(dbEntity);
        } else {
            log.warn(
                    "没有找到重试的执行器 taskId :{} , taskLabel: {}",
                    dbEntity.getTaskId(),
                    dbEntity.getTaskLabel());
            // throw new RuntimeException("没有找到对应的执行器");
        }
        return iRetryExecutor;
    }

    /** 获取RetryTemplate 可能需要根据 不同任务出创建一个retryTemplate 可能会把 3 这个 最大重试次数查询出来设置上去 */
    private RetryTemplate getRetryTemplateTemp() {
        RetryTemplate retryTemplate = new RetryTemplate();
        // 模拟重启的时候从db里读取失败的记录 然后执行对应的方法和任务
        retryTemplate.setRetryPolicy(new SimpleRetryPolicy(3, Map.of(Exception.class, true)));
        return retryTemplate;
    }
}
