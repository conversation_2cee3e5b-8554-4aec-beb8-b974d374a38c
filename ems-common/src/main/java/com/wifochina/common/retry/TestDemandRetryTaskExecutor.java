package com.wifochina.common.retry;

import com.wifochina.common.retry.data.RetryTaskContext;
import com.wifochina.common.retry.service.TestDemandService;
import com.wifochina.modules.retrytask.mapper.RetryFailTaskMapper;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/** Created by johnny on 2023/4/12 */
@Slf4j
// @Component
public class TestDemandRetryTaskExecutor extends IRetryTaskExecutor {

    @Autowired private TestDemandService testDemandService;

    public TestDemandRetryTaskExecutor(RetryFailTaskMapper retryFailTaskMapper) {
        super(retryFailTaskMapper);
    }

    @Override
    public String taskLabel() {
        // 任务的标记
        return "DemandRetryTask";
    }

    @Override
    protected void doRetry(RetryTaskContext context, ContextHolder contextHolder) {
        String projectId = null;
        String time = null;
        // 当这个不为空 说明 启动了并且从数据库恢复出来了失败任务再执行
        if (context.getDbFailTask() != null) {
            // 可以从 dbFailTask 里面获取到 一些数据
            log.info("dbFailTask : {}", context.getDbFailTask());
            time = (String) contextHolder.get("time");
            projectId = context.getDbFailTask().getProjectId();
        }
        final String finalProjectId = projectId;
        final String finalTime = time;
        // 准备好对应的上下文context
        // 调用某个service 方法执行业务
        testDemandService.serviceMethod(
                new TestDemandService.DemandContext() {

                    @Override
                    public String projectId() {
                        return finalProjectId;
                    }

                    @Override
                    public String time() {
                        return finalTime;
                    }
                });
        // 最后需要把这个向contextHolder里面放入 需要恢复的时候用的上下文
        contextHolder.put("projectId", projectId);
        contextHolder.put("time", new Date());
        // ...
    }
}
