package com.wifochina.common.page;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.GlobalExceptionHandler;
import com.wifochina.common.handler.MessageSourceHandler;
import com.wifochina.common.util.StringUtil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * service层返回对象包装类
 *
 * <AUTHOR>
 * @param <T>
 */
@Getter
@ApiModel(value = "Result", description = "接口包装信息")
public class Result<T> implements Serializable {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ApiModelProperty(value = "状态标识")
    private boolean success = false;

    @ApiModelProperty(value = "状态码")
    private String code;

    @ApiModelProperty(value = "异常消息")
    private String message;

    @ApiModelProperty(value = "数据结果")
    private T data;

    private Result() {}

    public static <T> Result<T> success() {
        return success(null);
    }

    public static <T> Result<T> success(T result) {
        Result<T> item = new Result<>();
        item.success = true;
        item.data = result;
        item.code = "200";
        item.message = "success";
        return item;
    }

    public static <T> Result<T> failure(String code) {
        return failure(code, null);
    }

    public static <T> Result<T> failure(String code, T data) {
        return failure(code, null, data);
    }

    public static <T> Result<T> failureNew(String code, String message, T data) {

        if (!StringUtils.hasLength(message)) {
            //            try {
            //                message = MessageSourceHandler.getMessage(code);
            //            } catch (Exception ex) {
            //                log.error(ex.getMessage(), ex);
            //            }
            //            if (StringUtil.isEmpty(message)) {
            //                if (StringUtil.isEmpty(code)) {
            //                    message =
            //                            MessageSourceHandler.getMessage(
            //                                    ErrorResultCode.SERVICE_EXCEPTION.value());
            //                } else {
            //                    message = code;
            //                }
            //            }
        }
        Result<T> item = new Result<>();
        item.success = false;
        item.code = code;
        item.message = message;
        item.data = data;
        return item;
    }

    public static <T> Result<T> failure(String code, String message, T data) {

        if (!StringUtils.hasLength(message)) {
            try {
                message = MessageSourceHandler.getMessage(code);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            if (StringUtil.isEmpty(message)) {
                if (StringUtil.isEmpty(code)) {
                    message =
                            MessageSourceHandler.getMessage(
                                    ErrorResultCode.SERVICE_EXCEPTION.value());
                } else {
                    message = code;
                }
            }
        }
        Result<T> item = new Result<>();
        item.success = false;
        item.code = code;
        item.message = message;
        item.data = data;
        return item;
    }
}
