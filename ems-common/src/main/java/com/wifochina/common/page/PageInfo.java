package com.wifochina.common.page;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/***
 * 自动发送的数据格式：
 * 
 * <AUTHOR> * 1. start: 开始记录的起始数，如第 20 条,从0开始 * 2. limit : 单页多少条记录 * 3. pageIndex : 第几页，同start参数重复，可以选择其中一个使用
 ***/
@ApiModel(value = "PageInfo", description = "分页对象")
public class PageInfo<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    public PageInfo() {}

    /**
     * @param pageIndex
     */
    public PageInfo(int pageIndex) {
        setPageIndex(pageIndex);
    }

    /**
     * @param pageIndex
     * @param limit
     */
    public PageInfo(int pageIndex, int limit) {
        this.pageIndex = pageIndex;
        this.limit = limit;
    }

    @ApiModelProperty(value = "每页显示数量", notes = "每页显示数量")
    private int limit = 15;

    @ApiModelProperty(value = "当前页的下标", notes = "当前显示第几页的下标")
    private int pageIndex = 0;

    @ApiModelProperty(hidden = true)
    public int getStart() {
        return this.pageIndex * this.limit;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    @ApiModelProperty(hidden = true)
    public Map<String, Object> getPagingMap() {
        Map<String, Object> map = new HashMap<String, Object>(2);

        Integer start = this.pageIndex * this.limit;
        map.put("startRow", start);
        map.put("endRow", this.getLimit());
        return map;
    }

}