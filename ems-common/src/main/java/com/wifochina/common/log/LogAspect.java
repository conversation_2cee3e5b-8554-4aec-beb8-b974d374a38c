package com.wifochina.common.log;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.wifochina.common.constants.OperationConstants;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.log.LogDetailContext;
import com.wifochina.modules.log.LogDetailService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.DefaultLogDetailService;
import com.wifochina.modules.log.detailresolveservice.SimpleAddLogDetailService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.entity.LogEntity;
import com.wifochina.modules.user.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 控制层日志aop
 *
 * <AUTHOR>
 */
@Configuration
@Aspect
@Slf4j
public class LogAspect {

    // 正则表达式缓存
    private static final ConcurrentHashMap<String, Pattern> CLIENT_PATTERNS =
            new ConcurrentHashMap<>();

    private static final String PC = "PC";
    private static final String MOBILE = "MOBILE";
    private static final String OTHER = "OTHER";

    @Value("${log.client.mobile}")
    private String mobileExpression;

    @Value("${log.client.pc}")
    private String pcExpression;

    @Autowired private LogService logService;
    @Autowired private List<LogDetailService<?>> logDetailServices;

    @Pointcut("@annotation(com.wifochina.common.log.Log)")
    private void controllerAspect() {}

    /**
     * 方法执行
     *
     * @param pjp 切入点
     * @return object
     * @throws Throwable SecurityException
     */
    @Around("controllerAspect()")
    @SuppressWarnings("unchecked")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        // 拦截的实体类，就是当前正在执行的controller
        Object target = pjp.getTarget();
        // 拦截的方法名称，当前正在执行的方法
        String methodName = pjp.getSignature().getName();
        // 拦截的放参数类型
        MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
        Class<?>[] parameterTypes = methodSignature.getMethod().getParameterTypes();
        Method method = target.getClass().getMethod(methodName, parameterTypes);
        Log annotation = method.getAnnotation(Log.class);
        LogEntity logEntity = buildBasicLogEntity(annotation);

        Object res;
        OperationType type = annotation.type();
        try {
            Object[] firstArgs = pjp.getArgs();
            LogDetailContext<Object> logDetailContext =
                    getObjectLogDetailContext(logEntity, type, firstArgs);
            LogDetailService<Object> service;
            if (OperationType.DEL.equals(type)) {
                Class<? extends LogDetailService<?>> forDeleteLogDetailServiceClass =
                        annotation.logDetailServiceClass();
                LogDetailService<?> deleteService = matchDetailService(forDeleteLogDetailServiceClass);
                service = (LogDetailService<Object>) deleteService;
            } else if (OperationType.ADD_SIMPLE.equals(type)
                    || OperationType.UPDATE_SIMPLE.equals(type)
                    || OperationType.DEL_SIMPLE.equals(type)) {
                LogDetailService<?> simpleService =
                        SpringUtil.getBean(SimpleAddLogDetailService.class);
                service = (LogDetailService<Object>) simpleService;
            } else {
                LogDetailService<?> logDetailService = findLogDetailService(firstArgs);
                service = (LogDetailService<Object>) logDetailService;
            }
            // 传递参数
            service.detailLog(logDetailContext);
            // 调用具体的 业务方法
            res = pjp.proceed();
            logEntity.setResult(OperationConstants.SUCCESS.name().toLowerCase());
        } catch (Throwable e) {
            logEntity.setResult(OperationConstants.FAILURE.name().toLowerCase());
            throw e;
        } finally {
            String params = StrUtil.EMPTY;
            if (pjp.getArgs() != null && pjp.getArgs().length > 0) {
                String paramStr = JSON.toJSONString(pjp.getArgs()[0]);
                params = paramStr.length() >= 10000
                        ? paramStr.substring(0, 10000)
                        : paramStr;
            }
            logEntity.setParams(params);
            long costTime = Instant.now().toEpochMilli() - logEntity.getRequestTime();
            logEntity.setCostTime(costTime);
            if (type != OperationType.GET) {
                logService.save(logEntity);
            }
            log.debug(
                    "当前用户：{}，执行方法：{}，请求时间：{}，耗时：{}",
                    logEntity.getUserId(),
                    methodName,
                    DateUtil.format(
                            new Date(logEntity.getRequestTime()),
                            DatePattern.NORM_DATETIME_PATTERN),
                    costTime);
        }
        return res;
    }

    /** 根据指定的服务类型匹配对应的可用的log服务 */
    private LogDetailService<?> matchDetailService(
            Class<? extends LogDetailService<?>> deleteService) {
        if (deleteService == DefaultLogDetailService.class) {
            return SpringUtil.getBean(DefaultLogDetailService.class);
        }
        return logDetailServices.stream()
                .filter(deleteService::isInstance)
                .findFirst()
                .orElse(SpringUtil.getBean(DefaultLogDetailService.class));
    }

    /** 根据request入参匹配对应log服务 */
    private LogDetailService<?> findLogDetailService(Object[] firstArgs) {
        if (ArrayUtil.isEmpty(firstArgs) || firstArgs[0] == null) {
            return SpringUtil.getBean(DefaultLogDetailService.class);
        }
        Class<?> firstArgClass = firstArgs[0].getClass();
        for (LogDetailService<?> service : logDetailServices) {
            Class<?> type = getGenericType(service.getClass());
            if (type.isAssignableFrom(firstArgClass)) {
                return service;
            }
        }
        return SpringUtil.getBean(DefaultLogDetailService.class);
    }

    /** 根据服务类型获取对应父级类型 */
    private Class<?> getGenericType(Class<?> clazz) {
        Type genericSuperclass = clazz.getGenericSuperclass();
        // Type genericSuperclass = clazz.getGenericInterfaces()[0];
        if (genericSuperclass instanceof ParameterizedType) {
            Type[] actualTypeArguments =
                    ((ParameterizedType) genericSuperclass).getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                return (Class<?>) actualTypeArguments[0];
            }
        }
        return Object.class;
    }

    /** 构建基础日志对象 */
    private LogEntity buildBasicLogEntity(Log annotation) {
        LogEntity logEntity = new LogEntity();
        logEntity.setModule(annotation.module());
        // 1.4.2 看了一下 这2个拼起来和 正常以前 method 一样
        logEntity.setMethod(
                StrUtil.isEmpty(annotation.methods())
                        ? annotation.module() + "_" + annotation.type().name()
                        : annotation.methods());
        logEntity.setIp(MDC.get("clientIP"));
        logEntity.setRequestTime(Instant.now().getEpochSecond());
        logEntity.setProjectId(
                Optional.ofNullable(WebUtils.projectId.get()).orElse(EmsConstants.COMMON));
        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                        .getRequest();
        String userAgent = request.getHeader("User-Agent");
        // 预编译正则表达式
        Pattern mobilePattern =
                CLIENT_PATTERNS.computeIfAbsent(
                        String.format(".*(%s).*", mobileExpression), Pattern::compile);
        Pattern pcPattern =
                CLIENT_PATTERNS.computeIfAbsent(
                        String.format(".*(%s).*", pcExpression), Pattern::compile);

        if (mobilePattern.matcher(userAgent).find()) {
            // 移动端请求
            logEntity.setClient(MOBILE);
        } else if (pcPattern.matcher(userAgent).find()) {
            // PC端浏览器请求
            logEntity.setClient(PC);
        } else {
            // 其他类型的请求
            logEntity.setClient(OTHER);
        }
        logEntity.setUserId(SecurityUtil.getUserId() == null ? "system" : SecurityUtil.getUserId());
        return logEntity;
    }

    /** log detail上下文实现 */
    private static @NotNull LogDetailContext<Object> getObjectLogDetailContext(
            LogEntity logEntity, OperationType type, Object[] firstArgs) {
        return new LogDetailContext<>() {
            @NotNull
            @Override
            public LogEntity logEntity() {
                return logEntity;
            }

            @NotNull
            @Override
            public OperationType type() {
                return type;
            }

            @Override
            public Object requestT() {
                return ArrayUtil.isEmpty(firstArgs) ? StrUtil.EMPTY : firstArgs[0];
            }
        };
    }
}
