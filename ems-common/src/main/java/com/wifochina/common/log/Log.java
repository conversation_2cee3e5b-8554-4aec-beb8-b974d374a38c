package com.wifochina.common.log;

import com.wifochina.modules.log.LogDetailService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.DefaultLogDetailService;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    String module() default "";

    String methods() default "";

    OperationType type() default OperationType.NO_DETAIL;

    Class<? extends LogDetailService<?>> logDetailServiceClass() default
            DefaultLogDetailService.class;
}
