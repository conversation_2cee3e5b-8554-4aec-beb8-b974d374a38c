package com.wifochina.common.time;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.util.ServiceAssert;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.util.Pair;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * @since 5/20/2022 6:16 PM
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class MyTimeUtil {
    public static final long ONE_DAY_SECONDS = 86400;
    public static final int WEEK_DAY = 7;

    public static final long GMT8_SECONDS = 28800;

    public static void main(String[] args) {
        long timestamp = 1739772213923L;
        String formattedTime = formatMilliTimestampWithTimezone(timestamp, "Asia/Shanghai");
        System.out.println(formattedTime); // 输出格式化后的
    }

    public static List<Long> generateMinuteList(long start, long end, int period, ZoneId zoneId) {
        DateTimeFormatter formatter =
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").withZone(zoneId);
        List<Long> minuteList = new ArrayList<>();

        long current = start;
        while (current <= end) {
            if ((current / 60) % period == 0) { // 根据周期筛选
                minuteList.add(Instant.ofEpochSecond(current).getEpochSecond());
            }
            current += 60; // 每次增加一分钟
        }
        if (!minuteList.isEmpty()) {
            minuteList.remove(0);
            return minuteList;
        }

        return minuteList;
    }

    public static long getMonthEndTimestamp(long startOfMonthTimestamp, ZoneId zoneId) {
        // 将时间戳转换为 ZonedDateTime
        ZonedDateTime startOfMonth = Instant.ofEpochSecond(startOfMonthTimestamp).atZone(zoneId);

        // 获取当前月的最后一天的 23:59:59
        LocalDate lastDayOfMonth =
                startOfMonth
                        .toLocalDate()
                        .withDayOfMonth(startOfMonth.toLocalDate().lengthOfMonth());
        ZonedDateTime endOfMonth = lastDayOfMonth.atTime(23, 59, 59).atZone(zoneId);

        // 转换为时间戳
        return endOfMonth.toEpochSecond();
    }

    public static long getMonthStartTimestamp(long endOfMonthTimestamp) {
        // 将时间戳转换为 ZonedDateTime
        ZonedDateTime endOfMonth =
                Instant.ofEpochSecond(endOfMonthTimestamp).atZone(ZoneId.systemDefault());

        // 获取当前月的第一天的 00:00:00
        LocalDate firstDayOfMonth = endOfMonth.toLocalDate().withDayOfMonth(1);
        ZonedDateTime startOfMonth = firstDayOfMonth.atStartOfDay(ZoneId.systemDefault());

        // 转换为时间戳
        return startOfMonth.toEpochSecond();
    }

    /**
     * @param startTime
     * @param timezone
     * @return
     */
    public static List<RangeRequest> getStartMonthZeroAndPreLast(Long startTime, String timezone) {
        ZoneId zoneId = ZoneId.of(timezone);
        Instant instant = Instant.ofEpochSecond(startTime);
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);

        // 获取项目的开始年月
        YearMonth startYearMonth = YearMonth.from(zonedDateTime);

        // 获取当前的年月
        YearMonth currentYearMonth = YearMonth.now(zoneId);

        // 检查 createTime 是否是当月的零点时间
        boolean isCreateTimeStartOfMonth =
                zonedDateTime.toLocalTime().equals(LocalTime.MIDNIGHT)
                        && zonedDateTime.getDayOfMonth() == 1;

        // 如果 createTime 不是月的开始时间，从下个月开始计算
        if (!isCreateTimeStartOfMonth) {
            startYearMonth = startYearMonth.plusMonths(1); // 从下一个月开始
        }

        // 计算项目开始到当前时间的月数
        long monthsBetween = ChronoUnit.MONTHS.between(startYearMonth, currentYearMonth);

        // 生成每个月的开始时间和结束时间，并转换为时间戳
        //        List<Long> timeRangeList = new ArrayList<>();
        List<RangeRequest> timeRangeList = new ArrayList<>();

        monthsBetween = monthsBetween - 1; // 去掉最后一个
        for (int i = 0; i <= monthsBetween; i++) {
            // 获取当前循环中的 YearMonth
            YearMonth currentMonth = startYearMonth.plusMonths(i);

            // 获取当月的第一天零点
            LocalDate firstDayOfMonth = currentMonth.atDay(1);
            ZonedDateTime startOfMonth = firstDayOfMonth.atStartOfDay(zoneId);
            long startOfMonthEpoch = startOfMonth.toEpochSecond();

            // 获取当月的最后一天的最后一秒
            LocalDate lastDayOfMonth = currentMonth.atEndOfMonth();
            ZonedDateTime endOfMonth = lastDayOfMonth.atTime(23, 59, 59).atZone(zoneId);
            long endOfMonthEpoch = endOfMonth.toEpochSecond();

            // 打印或保存时间戳范围
            log.debug(
                    "Month {}: Start: {}, End: {}",
                    currentMonth,
                    startOfMonthEpoch,
                    endOfMonthEpoch);
            // 保存每个月的时间戳范围
            timeRangeList.add(
                    new RangeRequest().setStartDate(startOfMonthEpoch).setEndDate(endOfMonthEpoch));
        }
        return timeRangeList;
    }

    /** 获取到上个月最后一天的 23:59:59 时间戳 注意是上一个月的最后一天 */
    public static long getPreMonthLastTime(String timezoneCode) {

        // 获取当前时间
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timezoneCode));
        // 计算上个月的最后一天
        ZonedDateTime lastDayOfPreviousMonth =
                now.withDayOfMonth(1) // 当前月的第一天
                        .minusMonths(1) // 减去一个月
                        .withDayOfMonth(
                                now.minusMonths(1).toLocalDate().lengthOfMonth()) // 获取上个月的最后一天
                        .withHour(23) // 设置为23点
                        .withMinute(59) // 设置为59分
                        .withSecond(59) // 设置为59秒
                        .withNano(999999999); // 设置为纳秒的最大值，以确保精度

        return lastDayOfPreviousMonth.toEpochSecond();
    }

    public static long getOffSetHourByZoneCode(String zoneCode) {
        ZoneId zoneId = ZoneId.of(zoneCode); // 替换为你的时区 code
        ZonedDateTime zonedDateTime = ZonedDateTime.now(zoneId);
        int offsetInSeconds = zonedDateTime.getOffset().getTotalSeconds();
        return offsetInSeconds / 3600;
    }

    /**
     * 获取今日零点时间 带时区
     *
     * @param timeZoneCode : 时区代码
     * @return : long
     */
    public static long getTodayZeroTime(String timeZoneCode) {
        ZoneId zoneId = ZoneId.of(timeZoneCode);
        LocalDate today = LocalDate.now(zoneId);
        ZonedDateTime todayAtMidnightInZone = today.atStartOfDay(zoneId);
        return todayAtMidnightInZone.toInstant().getEpochSecond();
    }

    public static int getWeekday(long time, String timeZoneCode) {
        ZoneId zoneId = ZoneId.of(timeZoneCode);
        LocalDateTime today = LocalDateTime.ofInstant(Instant.ofEpochSecond(time), zoneId);
        return today.getDayOfWeek().getValue();
    }

    public static int getCurrentWeekday(String timeZoneCode) {
        ZoneId zoneId = ZoneId.of(timeZoneCode);
        LocalDateTime today = LocalDateTime.ofInstant(Instant.now(), zoneId);
        return today.getDayOfWeek().getValue();
    }

    public static long getOneDayZeroTime(Long time, String timeZoneCode) {
        ZoneId timeZone = ZoneId.of(timeZoneCode);
        // 将时间戳转换为UTC时区的ZonedDateTime
        ZonedDateTime utcDateTime =
                ZonedDateTime.ofInstant(Instant.ofEpochSecond(time), ZoneOffset.UTC);
        // 转换到目标时区
        ZonedDateTime targetDateTime = utcDateTime.withZoneSameInstant(timeZone);
        // 获取日期，并找到该日期在目标时区的午夜时刻
        LocalDate date = targetDateTime.toLocalDate();
        ZonedDateTime midnight = date.atStartOfDay(timeZone);
        // 返回目标时区午夜时间的时间戳（以秒为单位）
        return midnight.toEpochSecond();
    }

    public static int getMinutesInDayNew(long timestamp, String timeZoneCode, boolean isZero) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of(timeZoneCode));
        LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();

        int hour = localDateTime.getHour();
        int minute = localDateTime.getMinute();
        // 如果是 00:00，并且原始时间是 Unix 秒级整点（不含秒），我们认为是“第二天的起点”，代表前一天结束
        if (hour == 0 && minute == 0 && !isZero) {
            return 1440; // 表示前一天的最后一分钟结束
        }
        return hour * 60 + minute;
    }

    public static int getMinutesInDay(long timestamp, String timeZoneCode) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of(timeZoneCode));
        LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();

        int hour = localDateTime.getHour();
        int minute = localDateTime.getMinute();
        // 如果是 00:00，并且原始时间是 Unix 秒级整点（不含秒），我们认为是“第二天的起点”，代表前一天结束
        if (hour == 0 && minute == 0 && localDateTime.getSecond() == 0) {
            return 1440; // 表示前一天的最后一分钟结束
        }

        return hour * 60 + minute;
    }

    public static long getZonedStartOfDayEpoch(String strategyDateStr, String timeZoneId) {
        // 获取当前年份
        int currentYear = ZonedDateTime.now(ZoneId.of(timeZoneId)).getYear();

        // 构造完整日期字符串，如 "2025-06-04"
        String fullDateStr = currentYear + "-" + strategyDateStr;

        // 解析为 LocalDate
        LocalDate date = LocalDate.parse(fullDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 设置为该日期的 00:00，带上时区
        ZonedDateTime zonedStartOfDay = date.atStartOfDay(ZoneId.of(timeZoneId));

        // 转换为时间戳（秒）
        return zonedStartOfDay.toEpochSecond();
    }

    /**
     * 获取上个月第一天的零点时间，带时区
     *
     * @param timeZoneCode: 时区
     * @return : long
     */
    public static long getPreMonthZeroTime(String timeZoneCode) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now(getZoneOffsetFromZoneCode(timeZoneCode));
        // 获取上个月的第一天
        LocalDate firstDayOfPreviousMonth = currentDate.minusMonths(1).withDayOfMonth(1);
        // 设置时间为零点
        LocalDateTime startOfPreviousMonth = firstDayOfPreviousMonth.atTime(LocalTime.MIDNIGHT);
        return startOfPreviousMonth
                .atOffset(getZoneOffsetFromZoneCode(timeZoneCode))
                .toEpochSecond();
    }

    /**
     * 获取当前月份第一天的零点时间 带时区
     *
     * @param timeZoneCode: 时区
     * @return : long
     */
    public static long getCurrentMonthZeroTime(String timeZoneCode) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now(getZoneOffsetFromZoneCode(timeZoneCode));
        // 获取当前月份的第一天
        LocalDate firstDayOfMonth = LocalDate.of(currentDate.getYear(), currentDate.getMonth(), 1);
        // 设置时间为零点
        LocalDateTime startOfMonth = firstDayOfMonth.atTime(LocalTime.MIDNIGHT);
        return startOfMonth.atOffset(getZoneOffsetFromZoneCode(timeZoneCode)).toEpochSecond();
    }

    /**
     * 返回timeToCheck 是否在startTime和endTime范围内
     *
     * @param timeToCheck : timeToCheck
     * @param startTime : startTime
     * @param endTime : endTime
     * @return : boolean
     */
    public static boolean isWithinTimeRange(
            LocalTime timeToCheck, LocalTime startTime, LocalTime endTime) {
        return !timeToCheck.isBefore(startTime) && !timeToCheck.isAfter(endTime);
    }

    /**
     * 获取最近7天的零点时间 带时区 昨天开始往前推7天
     *
     * @param timeZoneCode: 时区
     * @return : long
     */
    public static List<DayHolder> getOneWeekTime(String timeZoneCode) {
        List<DayHolder> dayHolders = new ArrayList<>();
        ZoneOffset zoneOffset = getZoneOffsetFromZoneCode(timeZoneCode);
        // 1.4.2 改成了 昨天开始往前推7天
        ZonedDateTime yesterdayDateTime = ZonedDateTime.now(zoneOffset).minusDays(1);
        for (int i = 0; i < WEEK_DAY; i++) {
            ZonedDateTime startOfDay = yesterdayDateTime.toLocalDate().atStartOfDay(zoneOffset);
            ZonedDateTime endOfDay =
                    yesterdayDateTime.toLocalDate().atTime(23, 59, 59).atZone(zoneOffset);
            // 将日期时间范围格式化为字符串并添加到列表中
            dayHolders.add(new DayHolder(startOfDay, endOfDay));
            // 将日期向前推一天
            yesterdayDateTime = yesterdayDateTime.minusDays(1);
        }
        return dayHolders;
    }

    /**
     * 获取今天的 23:59:59 时间戳 带duration
     *
     * @param timeZoneCode: 时区
     * @return long
     */
    public static long getTodayEndTime(String timeZoneCode) {
        ZoneOffset zoneOffset = getZoneOffsetFromZoneCode(timeZoneCode);
        ZonedDateTime currentDateTime = ZonedDateTime.now(zoneOffset);
        ZonedDateTime endOfDay =
                currentDateTime.toLocalDate().atTime(23, 59, 59).atZone(zoneOffset);
        return endOfDay.toEpochSecond();
    }

    public static long getYesterdayEndTime(String timeZoneCode) {
        // 获取当前时区
        ZoneId zoneId = ZoneId.of(timeZoneCode);

        // 获取当前日期时间
        ZonedDateTime now = ZonedDateTime.now(zoneId);

        // 减去一天得到前一天的日期时间
        ZonedDateTime yesterday = now.minusDays(1);

        // 构建 LocalTime 对象表示 23:59:59
        LocalTime endTime = LocalTime.of(23, 59, 59);

        // 合并日期和时间，得到前一天的 23:59:59
        ZonedDateTime endOfYesterday = yesterday.with(endTime);

        // 转换为时间戳

        return endOfYesterday.toEpochSecond();
    }

    /**
     * 判断给定的时间戳 是否当前年份的
     *
     * @param time : 给定的时间戳
     * @param timeZoneCode : 时区
     * @return : boolean 是否当前年份
     */
    public static boolean isInCurrentYear(Long time, String timeZoneCode, TimeUnit timeUnit) {
        Instant instant;
        if (timeUnit == TimeUnit.MILLISECONDS) {
            instant = Instant.ofEpochMilli(time);
        } else {
            instant = Instant.ofEpochSecond(time);
        }
        LocalDate localDate = instant.atZone(getZoneOffsetFromZoneCode(timeZoneCode)).toLocalDate();
        int currentYear = LocalDate.now(getZoneOffsetFromZoneCode(timeZoneCode)).getYear();
        int year = localDate.getYear();
        return currentYear == year;
    }

    /**
     * 给一个时间范围 得到这个时间范围内的 零点时间戳 比如 long startDate = 1699582508L; 2023-11-10 10:15:08 long endDate =
     * 1699927207L; 2023-11-14 10:00:07 得到list // 1699545600 : 2023-11-10 00:00:00 1699632000 :
     * 2023-11-11 00:00:00 1699718400 : 2023-11-12 00:00:00 1699804800 : 2023-11-13 00:00:00
     * 1699891200 : 2023-11-14 00:00:00 //
     *
     * @param startDate :开始时间
     * @param endDate : 结束时间
     * @return : java.util.List<java.lang.Long> 零点时间戳
     */
    public static List<Long> splitRangeToMidnights(
            long startDate, long endDate, String timeZoneCode) {
        List<Long> midnightTimestamps = new ArrayList<>();
        // 将时间戳转换为 LocalDateTime，以便获取日期部分
        LocalDateTime startDateTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(startDate), getZoneOffsetFromZoneCode(timeZoneCode));
        LocalDateTime endDateTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(endDate), getZoneOffsetFromZoneCode(timeZoneCode));
        // 拆分日期范围
        LocalDateTime currentDate = startDateTime;
        while (!currentDate.isAfter(endDateTime)) {
            // 获取当前日期的零点时间
            LocalDateTime midnight = currentDate.withHour(0).withMinute(0).withSecond(0);
            // 将零点时间转换为时间戳并添加到列表
            midnightTimestamps.add(
                    midnight.atZone(getZoneOffsetFromZoneCode(timeZoneCode)).toEpochSecond());
            // 下一天
            currentDate = midnight.plusDays(1);
        }

        return midnightTimestamps;
    }

    public static LocalDate getPreZeroTime() {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 获取前一天的日期
        LocalDate yesterday = today.minusDays(1);

        return yesterday;
    }

    @Data
    public static class DayHolder {
        private ZonedDateTime startZonedDateTime;
        private ZonedDateTime endZonedDateTime;

        public DayHolder(ZonedDateTime startZonedDateTime, ZonedDateTime endZonedDateTime) {
            this.startZonedDateTime = startZonedDateTime;
            this.endZonedDateTime = endZonedDateTime;
        }
    }

    public static ZoneOffset getZoneOffsetFromZoneCode(String timeZoneCode) {
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneCode);
        Calendar calendar = Calendar.getInstance(timeZone);
        // int offsetSeconds = timeZone.getRawOffset();
        int offsetSeconds = timeZone.getOffset(calendar.getTimeInMillis());
        return ZoneOffset.ofTotalSeconds(offsetSeconds / 1000);
    }

    // TimeZone timeZone = TimeZone.getTimeZone(timeZoneCode);
    // int offsetSeconds = timeZone.getRawOffset();
    // return offsetSeconds / 1000;
    public static long getOffsetSecondsFromZoneCode(String timeZoneCode) {
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneCode);
        Calendar calendar = Calendar.getInstance(timeZone);
        int offsetSeconds = timeZone.getOffset(calendar.getTimeInMillis());
        // 非 夏令
        // int offsetSeconds = timeZone.getRawOffset();
        return offsetSeconds / 1000;
    }

    public static long getOffsetSecondsFromZoneCodeSpecialTime(
            String timeZoneCode, long specialTime) {
        // 带 夏令
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneCode);
        Calendar calendar = Calendar.getInstance(timeZone);
        calendar.setTimeInMillis(specialTime * 1000);
        int offsetMillis = timeZone.getOffset(calendar.getTimeInMillis());
        return offsetMillis / 1000;
        // 非夏令
        // TimeZone timeZone = TimeZone.getTimeZone(timeZoneCode);
        // int offsetSeconds = timeZone.getRawOffset();
        // return offsetSeconds / 1000;
    }

    public static void checkTime(Long start, Long end) {
        // 开始时间不晚于结束时间，否则返回时间错误异常
        ServiceAssert.isTrue(end.compareTo(start) >= 0, ErrorResultCode.TIME_OVERLAPPED.value());
    }

    public static LocalTime getLocalTime(String timeZoneCode, long timestamp) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        return instant.atZone(ZoneId.of(timeZoneCode)).toLocalTime();
    }

    public static Pair<Long, Long> getPeriodStartEndPair(
            LocalTime startTime, LocalTime endTime, LocalDate time, String timeZone) {
        long periodStartTime = startTime.atDate(time).atZone(ZoneId.of(timeZone)).toEpochSecond();
        // 判断一下 最后一段 因为电价那边配置的最后一段时间 是 00:00 这里要变成 23:59:59 不然又变成开始时间了
        // 这里的处理是因为 price 这个时段最后一个 比如 22:00 -> 00:00 因为endTime 是 00:00 算出来的
        // periodEndTime 又变成了
        // 当天凌晨起始时间了,但是需要的是
        // 这里应该是 电价配置那边 把最后一个时段 比如 22:00 -> 23:59:59
        if (startTime.getHour() != 0 || startTime.getMinute() != 0 || endTime.getHour() != 0) {
            endTime = endTime.minusSeconds(1);
        }
        long periodEndTime = endTime.atDate(time).atZone(ZoneId.of(timeZone)).toEpochSecond();
        return Pair.of(periodStartTime, periodEndTime);
    }

    /** */
    public static Pair<Long, Long> getPeriodStartEndPair(
            LocalTime startTime, LocalTime endTime, long oneDayZero, String timeZone) {
        LocalDate time =
                LocalDate.ofInstant(Instant.ofEpochSecond(oneDayZero), ZoneId.of(timeZone));
        long periodStartTime = startTime.atDate(time).atZone(ZoneId.of(timeZone)).toEpochSecond();
        // 判断一下 最后一段 因为电价那边配置的最后一段时间 是 00:00 这里要变成 23:59:59 不然又变成开始时间了
        // 这里的处理是因为 price 这个时段最后一个 比如 22:00 -> 00:00 因为endTime 是 00:00 算出来的
        // periodEndTime 又变成了
        // 当天凌晨起始时间了,但是需要的是
        // 这里应该是 电价配置那边 把最后一个时段 比如 22:00 -> 23:59:59
        if (startTime.getHour() != 0 || startTime.getMinute() != 0 || endTime.getHour() != 0) {
            endTime = endTime.minusSeconds(1);
        }
        long periodEndTime = endTime.atDate(time).atZone(ZoneId.of(timeZone)).toEpochSecond();
        return Pair.of(periodStartTime, periodEndTime);

        // LocalTime startTime =
        // price.getStartTime();
        // LocalTime endTime = price.getEndTime();
        // // 把每个时段当做一条 记录
        // long periodStartTime =
        // startTime
        // .atDate(time)
        //
        // .atZone(ZoneId.of(context.project().getTimezone()))
        // .toEpochSecond();
        // if (endTime.getMinute() == 0) {
        // if (startTime.getHour() != 0
        // || startTime.getMinute() != 0
        // || endTime.getHour() != 0) {
        // endTime = endTime.minusSeconds(1);
        // }
        // }
        // long periodEndTime =
        // endTime.atDate(time)
        //
        // .atZone(ZoneId.of(context.project().getTimezone()))
        // .toEpochSecond();

    }

    public static boolean isDifferenceMoreThan31Days(Long start, Long end) {
        // 秒转换为天
        long secondsIn31Days = 31L * 24 * 60 * 60;
        // 计算两个时间戳之间的差值
        long differenceInSeconds = end - start;
        // 判断差值是否大于或等于31天的秒数
        return differenceInSeconds >= secondsIn31Days;
    }

    public static List<Long> getDailyMidnightTimestamps(
            ZoneId zoneId, long startTime, long endTime) {
        List<Long> timestamps = new ArrayList<>();
        // 转换为 LocalDate
        //        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate startDate = Instant.ofEpochSecond(startTime).atZone(zoneId).toLocalDate();
        LocalDate endDate = Instant.ofEpochSecond(endTime).atZone(zoneId).toLocalDate();
        LocalDate yesterDay = LocalDate.now(zoneId).minusDays(1);

        // 如果 endTime 是当月，则取今天或今天之前的日期
        if (endDate.getMonth() == yesterDay.getMonth()
                && endDate.getYear() == yesterDay.getYear()) {
            endDate = yesterDay;
        }

        // 遍历日期，添加每天零点时间戳
        while (!startDate.isAfter(endDate)) {
            long midnightTimestamp = startDate.atStartOfDay(zoneId).toEpochSecond();
            timestamps.add(midnightTimestamp);
            startDate = startDate.plusDays(1); // 加一天
        }
        return timestamps;
    }

    /**
     * 获取从 startTime 到 endTime 每个月第一天零点的时间戳列表
     *
     * @param zoneId 时区
     * @param startTime 开始时间（秒）
     * @param endTime 结束时间（秒）
     * @return 每个月第一天零点时间戳列表（秒）
     */
    public static List<Long> getMonthlyMidnightTimestamps(
            ZoneId zoneId, long startTime, long endTime) {
        List<Long> timestamps = new ArrayList<>();

        // 转换为 LocalDate
        LocalDate startDate =
                Instant.ofEpochSecond(startTime).atZone(zoneId).toLocalDate().withDayOfMonth(1);
        LocalDate endDate =
                Instant.ofEpochSecond(endTime).atZone(zoneId).toLocalDate().withDayOfMonth(1);
        LocalDate currentMonth = LocalDate.now(zoneId).withDayOfMonth(1); // 当前月的第一天

        // 如果 endTime 是当月，截止到上个月
        // 如果 endTime 是当月或未来，截止到本月
        if (!endDate.isBefore(currentMonth)) {
            endDate = currentMonth;
        }

        // 遍历日期，添加每月第一天零点时间戳
        while (!startDate.isAfter(endDate)) {
            long midnightTimestamp = startDate.atStartOfDay(zoneId).toEpochSecond();
            timestamps.add(midnightTimestamp);
            startDate = startDate.plusMonths(1); // 加一个月
        }

        return timestamps;
    }

    public static List<LocalDate> getRanges(String startDate, String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate from = LocalDate.parse(startDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);
        // 处理 时间段范围内的
        long between = ChronoUnit.DAYS.between(from, end);
        List<LocalDate> dateList = new ArrayList<>();
        if (between == 0) {
            if (from.isEqual(end)) {
                dateList.add(from);
                return dateList;
            }
        }
        between++;
        for (int i = 0; i < between; i++) {
            LocalDate date = from.plusDays(i);
            dateList.add(date);
        }
        return dateList;
    }

    public static long getEveryDayKey(String timezone, int year, int month, int day) {
        LocalDate localDate = LocalDate.of(year, month, day);

        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MIDNIGHT);
        ZoneId zoneId = ZoneId.of(timezone); // Use your desired
        // time zone
        return ZonedDateTime.of(localDateTime, zoneId).toEpochSecond();
    }

    public static long getEveryMonthKey(String timezone, Integer year, Integer month) {
        LocalDate localDate = LocalDate.of(year, month, 1);
        // time zone
        return localDate.atStartOfDay(ZoneId.of(timezone)).toEpochSecond();
    }

    public static String formatterDateTime(
            ZonedDateTime zonedDateTime, DateTimeFormatter formatter) {
        return zonedDateTime.format(formatter);
    }

    public static final DateTimeFormatter DATE_FORMATTER_MONTH =
            DateTimeFormatter.ofPattern("yyyy-MM");

    public static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final DateTimeFormatter DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_HOUR_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    public static String formatMilliTimestampWithTimezone(long timestamp, String zoneCode) {
        // 将时间戳转换为 Instant
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZoneId zoneId = ZoneId.of(zoneCode);
        // 转换为带时区的 ZonedDateTime
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);

        // 格式化为指定的字符串格式
        return zonedDateTime.format(DATE_TIME_FORMATTER);
    }

    public static String formatTimestampWithTimezone(
            long secondTimeStamp, String zoneCode, DateTimeFormatter formatter) {
        // 将时间戳转换为 Instant
        Instant instant = Instant.ofEpochSecond(secondTimeStamp);
        ZoneId zoneId = ZoneId.of(zoneCode);
        // 转换为带时区的 ZonedDateTime
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);
        // 格式化为指定的字符串格式
        return zonedDateTime.format(formatter);
    }
}
