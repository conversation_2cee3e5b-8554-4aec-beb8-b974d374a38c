package com.wifochina.common.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * 平台服务层异常，主要是在业务数据或者状态异常时使用
 *
 * <AUTHOR>
 */
@Getter
public class ServiceException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    @Setter private String code;

    private int httpCode;

    private String data;

    public ServiceException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public ServiceException(String code, int realCode, String message) {
        super(message);
        this.httpCode = realCode;
        this.code = code;
    }

    public ServiceException(String code, String message) {
        super(message);
        this.code = code;
    }

    public ServiceException(String code, String data, String message) {
        super(message);
        this.code = code;
        this.data = data;
    }

    public ServiceException(String code) {
        this.code = code;
    }
}
