package com.wifochina.common.exception;

import static com.wifochina.common.page.Result.failureNew;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.StringUtil;

import com.wifochina.modules.oauth.util.WebUtils;
import lombok.extern.slf4j.Slf4j;

import org.apache.catalina.connector.ClientAbortException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MultipartException;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Objects;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * <AUTHOR> 全局 验证 异常 处理
 */
@RestControllerAdvice(annotations = {RestController.class, Controller.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class GlobalExceptionHandler {

    @Value("${spring.profiles.active:dev}")
    public String activeProfile;

    /**
     * 构建返回错误
     *
     * @param errorResultCode 错误code
     */
    private Result<?> failure(ErrorResultCode errorResultCode) {
        return failure(errorResultCode.value());
    }

    private Result<?> failure(ErrorResultCode errorResultCode, String message) {
        return Result.failure(errorResultCode.value(), message, null);
    }

    private Result<?> failure(String code) {
        return Result.failure(code, null, null);
    }

    private Result<?> failure(String code, String message, Object data) {
        return Result.failure(code, null, data);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<?> handleValidationException(ConstraintViolationException e) {
        for (ConstraintViolation<?> cv : e.getConstraintViolations()) {
            String message = cv.getMessage();
            return failure(message);
        }
        return failure(ErrorResultCode.ILLEGAL_ARGUMENT.value());
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleBindException(BindException e) {
        log.error(e.getMessage(), e);
        return failure(e.getFieldErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        return failure(e.getFieldErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(MultipartException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleMultipartException() {
        return failure(ErrorResultCode.MULTIPART_TOO_LARGE);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleIllegalArgumentException() {
        return failure(ErrorResultCode.ILLEGAL_ARGUMENT);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleMissingServletRequestParameterException(
            MissingServletRequestParameterException e) {
        log.error(e.getMessage(), e);
        return failure(ErrorResultCode.MISSING_ARGUMENT);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleMethodArgumentTypeMismatchExceptionException(
            MethodArgumentTypeMismatchException e) {
        log.error(e.getMessage(), e);
        return failure(ErrorResultCode.ILLEGAL_ARGUMENT_TYPE);
    }

    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleAccessDeniedException() {
        return failure(ErrorResultCode.UNAUTHORIZED);
    }

    @ExceptionHandler(ClientAbortException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleClientAbortException(
            ClientAbortException clientAbortException, HttpServletResponse response) {
        log.error("ClientAbortException occurred: {}", clientAbortException.getMessage());
        if (response.isCommitted()) {
            // 如果响应已经提交，无法返回数据，直接返回
            log.warn("Response already committed, cannot write to output stream.");
            return null;
        }
        return failure(ErrorResultCode.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleHttpRequestMethodNotSupportedException() {
        return failure(ErrorResultCode.METHOD_NOT_ALLOWED);
    }

    @ExceptionHandler(LoginFailException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleLoginFailException(LoginFailException e) {
        log.error(e.getMessage(), e);
        return failure(e.getCode(), e.getMessage(), e.getData());
    }

    @ExceptionHandler(ServiceException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleServiceException(ServiceException e) {
        if (e.getCode() == null && e.getMessage() == null) {
            return failure(ErrorResultCode.SERVICE_EXCEPTION);
        } else if (e.getCode() != null
                && e.getCode().equals(ErrorResultCode.CUSTOM_MSG_ERROR.value())
                && e.getMessage() != null) {
            if (!StringUtil.isEmpty(e.getHttpCode())) {
                return failureNew(String.valueOf(e.getHttpCode()), e.getMessage(), e.getData());
            }
            return failureNew(
                    ErrorResultCode.SERVICE_EXCEPTION.value(), e.getMessage(), e.getData());
        } else {
            if (!StringUtil.isEmpty(e.getMessage())) {
                return failure(
                        Objects.requireNonNull(ErrorResultCode.fromValue(e.getCode())),
                        e.getMessage());
            }
            return failure(Objects.requireNonNull(ErrorResultCode.fromValue(e.getCode())));
        }
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error(e.getMessage(), e);
        return Result.failure(ErrorResultCode.ILLEGAL_ARGUMENT.value());
    }

    @ExceptionHandler(HttpMessageConversionException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleHttpMessageConversionException(HttpMessageConversionException e) {
        log.error(e.getMessage(), e);
        return failure(ErrorResultCode.ILLEGAL_ARGUMENT);
    }

    @ExceptionHandler(IllegalStateException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleIllegalStateException(IllegalStateException e) {
        log.error(e.getMessage(), e);
        return failure(ErrorResultCode.ILLEGAL_STATE);
    }

    @ExceptionHandler(HttpClientErrorException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleBadRequestException() {
        return failure(ErrorResultCode.ILLEGAL_ARGUMENT);
    }

    @ExceptionHandler(BadCredentialsException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleBadCredentialsException() {
        return failure(ErrorResultCode.USER_PASSWORD_INVALID);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleException(Exception e) {
        //        log.error(
        //                e.getMessage() != null ? e.getMessage() + "\n" : "Exception message is
        // null\n", e);
        // 将堆栈信息转换为字符串
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        String stackTrace = sw.toString();
        String projectId = WebUtils.projectId.get();
        log.error(
                "Global Exception projectId:{} , handler: {}, stackTrace:{}",
                projectId,
                e,
                stackTrace);
        return failure(ErrorResultCode.INTERNAL_SERVER_ERROR);
    }
}
