package com.wifochina.common.realtime;

import com.wifochina.common.page.Result;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.oauth.util.WebUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

/**
 * Created on 2025/2/26 09:41.
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/realtimeData")
@RestController
@Api(tags = "100-实时数据接口")
@RequiredArgsConstructor
public class RealTimeDataController {

    private final DataService dataService;

    private final PointListHolder pointListHolder;
    private final RestTemplate restTemplate;
}
