package com.wifochina.common.realtime

import com.alibaba.fastjson.JSON
import com.wifochina.modules.data.util.DataService
import com.wifochina.realtimemodel.common.DeviceRealTimeData
import com.wifochina.realtimemodel.common.IProtocolData
import com.wifochina.realtimemodel.enums.ProtocolEnum
import com.wifochina.realtimemodel.common.ProtocolDataHandler
import com.wifochina.realtimemodel.common.WeihengEms100
import com.wifochina.realtimemodel.common.WeihengEms200
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Component

/**
 * Created on 2025/7/2 14:10.
 * <AUTHOR>
 */
@Component
class RealTimeUtils(
    val dataService: DataService, val redisTemplate: RedisTemplate<String, String>
) {

    private fun handler(data: IntArray, handler: ProtocolDataHandler) {
        val result: IProtocolData?
        when (ProtocolEnum.protocol(data[42])) {
            ProtocolEnum.WeihengEms100 -> {
                result = WeihengEms100(data)
                handler.common(result)
                handler.handler100(result)
            }

            ProtocolEnum.WeihengEms200 -> {
                result = WeihengEms200(data)
                handler.common(result)
                handler.handler200(result)
            }
        }
    }

    fun getData(projectId: String, deviceId: String, handler: ProtocolDataHandler) {
        JSON.parseObject(
            redisTemplate.opsForValue().get("realtime:device:${projectId}:${deviceId}"), DeviceRealTimeData::class.java
        )?.let { deviceRealTimeData ->
            handler(deviceRealTimeData.data!!, handler)
        } ?: run {
            // 直接按照接口调用去获取吧.. 可能场站不会部署这个  realtime
            dataService.get(deviceId)?.let { data ->
                handler(data, handler)
            }
        }
    }
}