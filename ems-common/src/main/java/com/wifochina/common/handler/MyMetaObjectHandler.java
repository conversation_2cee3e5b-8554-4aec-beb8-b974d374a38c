package com.wifochina.common.handler;

import java.time.Instant;

import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.wifochina.modules.oauth.util.SecurityUtil;

/**
 * MyMetaObjectHandler
 * 
 * @date 2022/3/1 1:25
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_TIME = "updateTime";
    private static final String CREATE_BY = "createBy";
    private static final String UPDATE_BY = "updateBy";
    @Override
    public void insertFill(MetaObject metaObject) {
        Long now = Instant.now().getEpochSecond();
        if (this.getFieldValByName(CREATE_TIME, metaObject) == null) {
            this.setFieldValByName(CREATE_TIME, now, metaObject);
        }
        this.setFieldValByName(UPDATE_TIME, now, metaObject);
        this.setFieldValByName(CREATE_BY, SecurityUtil.getUsername(), metaObject);
        this.setFieldValByName(UPDATE_BY, SecurityUtil.getUsername(), metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Long now = Instant.now().getEpochSecond();
        this.setFieldValByName(UPDATE_TIME, now, metaObject);
        this.setFieldValByName(UPDATE_BY, SecurityUtil.getUsername(), metaObject);
    }

}
