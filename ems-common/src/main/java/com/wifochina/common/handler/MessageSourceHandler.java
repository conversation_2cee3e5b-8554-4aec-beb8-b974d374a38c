package com.wifochina.common.handler;

import lombok.extern.slf4j.Slf4j;

import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.support.RequestContextUtils;

import java.util.Locale;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

/**
 * 致敬大师，彼岸无岸，当下即是
 *
 * <AUTHOR> Date: 2022-03-03 15:40:59
 */
@Component
@Slf4j
public class MessageSourceHandler {

    private static MessageSource messageSource;

    private static final int LOCALE_PARSE = 2;

    public MessageSourceHandler(MessageSource messageSource) {
        MessageSourceHandler.messageSource = messageSource;
    }

    public static String getMessage(String messageCode) {
        return getMessage(messageCode, null);
    }

    public static String getMessageExternalLanguage(String messageCode, String language) {
        Objects.requireNonNull(language);
        // String language = request.getHeader("Accept-Language");
        Locale locale = Locale.SIMPLIFIED_CHINESE;
        try {
            if (StringUtils.hasLength(language)) {
                String[] area = language.split("_");
                if (area.length == LOCALE_PARSE) {
                    locale = new Locale(area[0], area[1]);
                } else {
                    area = language.split(",");
                    area = area[0].split("-");
                    if (area.length >= LOCALE_PARSE) {
                        locale = new Locale(area[0], area[1]);
                    }
                }
            }
        } catch (Exception e) {
            // todo
            log.error("[language]---->" + e.getMessage());
        }

        return messageSource.getMessage(messageCode, null, locale);
    }

    public static String getMessage(String messageCode, Object[] arr) {
        HttpServletRequest request =
                ((ServletRequestAttributes)
                                Objects.requireNonNull(RequestContextHolder.getRequestAttributes()))
                        .getRequest();
        String language = request.getHeader("Accept-Language");
        Locale locale = Locale.SIMPLIFIED_CHINESE;
        try {
            if (StringUtils.hasLength(language)) {
                String[] area = language.split("_");
                if (area.length == LOCALE_PARSE) {
                    locale = new Locale(area[0], area[1]);
                } else {
                    area = language.split(",");
                    area = area[0].split("-");
                    if (area.length >= LOCALE_PARSE) {
                        locale = new Locale(area[0], area[1]);
                    } else {
                        locale = RequestContextUtils.getLocale(request);
                    }
                }
            }
        } catch (Exception e) {
            // todo
            log.error("[language]---->" + e.getMessage());
        }

        return messageSource.getMessage(messageCode, arr, locale);
    }
}
