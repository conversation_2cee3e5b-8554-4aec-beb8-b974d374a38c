package com.wifochina.common.web;

import org.jetbrains.annotations.NotNull;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.Socket;
import java.security.cert.X509Certificate;

import javax.net.ssl.*;

/**
 * TLS的三个作用： （1）身份认证 通过证书认证来确认对方的身份，防止中间人攻击 （2）数据私密性 使用对称性密钥加密传输的数据，由于密钥只有客户端/服务端有，其他人无法窥探。 （3）数据完整性
 * 使用摘要算法对报文进行计算，收到消息后校验该值防止数据被篡改或丢失。
 *
 * <p>使用RestTemplate进行HTTPS请求访问： private static RestTemplate restTemplate = new RestTemplate(new
 * HttpsClientRequestFactory());
 *
 * @date 8/18/2022 10:19 AM
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class HttpsClientRequestFactory extends SimpleClientHttpRequestFactory {
    @Override
    protected void prepareConnection(
            @NotNull HttpURLConnection connection, @NotNull String httpMethod) {
        try {
            if (!(connection instanceof HttpsURLConnection)) {
                // http协议
                super.prepareConnection(connection, httpMethod);
            }
            if ((connection instanceof HttpsURLConnection)) {
                // https协议
                HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                TrustManager[] trustAllCerts =
                        new TrustManager[] {
                            new X509TrustManager() {
                                @Override
                                public X509Certificate[] getAcceptedIssuers() {
                                    return null;
                                }

                                @Override
                                public void checkClientTrusted(
                                        X509Certificate[] certs, String authType) {}

                                @Override
                                public void checkServerTrusted(
                                        X509Certificate[] certs, String authType) {}
                            }
                        };
                SSLContext sslContext = SSLContext.getInstance("TLS");
                sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
                httpsConnection.setSSLSocketFactory(
                        new MyCustomSSLSocketFactory(sslContext.getSocketFactory()));
                httpsConnection.setHostnameVerifier((s, sslSession) -> true);
                super.prepareConnection(httpsConnection, httpMethod);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static class MyCustomSSLSocketFactory extends SSLSocketFactory {

        private final SSLSocketFactory delegate;

        public MyCustomSSLSocketFactory(SSLSocketFactory delegate) {
            this.delegate = delegate;
        }

        // 返回默认启用的密码套件。除非一个列表启用，对SSL连接的握手会使用这些密码套件。
        // 这些默认的服务的最低质量要求保密保护和服务器身份验证
        @Override
        public String[] getDefaultCipherSuites() {
            return delegate.getDefaultCipherSuites();
        }

        // 返回的密码套件可用于SSL连接启用的名字
        @Override
        public String[] getSupportedCipherSuites() {
            return delegate.getSupportedCipherSuites();
        }

        @Override
        public Socket createSocket(
                final Socket socket, final String host, final int port, final boolean autoClose)
                throws IOException {
            final Socket underlyingSocket = delegate.createSocket(socket, host, port, autoClose);
            return overrideProtocol(underlyingSocket);
        }

        @Override
        public Socket createSocket(final String host, final int port) throws IOException {
            final Socket underlyingSocket = delegate.createSocket(host, port);
            return overrideProtocol(underlyingSocket);
        }

        @Override
        public Socket createSocket(
                final String host,
                final int port,
                final InetAddress localAddress,
                final int localPort)
                throws IOException {
            final Socket underlyingSocket =
                    delegate.createSocket(host, port, localAddress, localPort);
            return overrideProtocol(underlyingSocket);
        }

        @Override
        public Socket createSocket(final InetAddress host, final int port) throws IOException {
            final Socket underlyingSocket = delegate.createSocket(host, port);
            return overrideProtocol(underlyingSocket);
        }

        @Override
        public Socket createSocket(
                final InetAddress host,
                final int port,
                final InetAddress localAddress,
                final int localPort)
                throws IOException {
            final Socket underlyingSocket =
                    delegate.createSocket(host, port, localAddress, localPort);
            return overrideProtocol(underlyingSocket);
        }

        private Socket overrideProtocol(final Socket socket) {
            if (!(socket instanceof SSLSocket)) {
                throw new RuntimeException("An instance of SSLSocket is expected");
            }
            // ((SSLSocket) socket).setEnabledProtocols(new String[]{"TLSv1.2"});
            ((SSLSocket) socket).setEnabledProtocols(new String[] {"TLSv1", "TLSv1.1", "TLSv1.2"});
            return socket;
        }
    }
}
