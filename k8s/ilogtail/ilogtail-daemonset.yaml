apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: ilogtail-ds
  namespace: ilogtail
  labels:
    k8s-app: logtail-ds
spec:
  selector:
    matchLabels:
      k8s-app: logtail-ds
  template:
    metadata:
      labels:
        k8s-app: logtail-ds
    spec:
      tolerations:
        - operator: Exists # deploy on all nodes
      containers:
        - name: logtail
          env:
            - name: ALIYUN_LOG_ENV_TAGS # add log tags from env
              value: _node_name_|_node_ip_
            - name: _node_name_
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: _node_ip_
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: cpu_usage_limit # iLog<PERSON>'s self monitor cpu limit
              value: "1"
            - name: mem_usage_limit # iLogtail's self monitor mem limit
              value: "512"
            - name: default_access_key_id # accesskey id if you want to flush to SLS
              valueFrom:
                secretKeyRef:
                  name: ilogtail-secret
                  key: access_key_id
                  optional: true
            - name: default_access_key # accesskey secret if you want to flush to SLS
              valueFrom:
                secretKeyRef:
                  name: ilogtail-secret
                  key: access_key
                  optional: true
          image: >-
            sls-opensource-registry.cn-shanghai.cr.aliyuncs.com/ilogtail-community-edition/ilogtail:1.8.4
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 400m
              memory: 384Mi
          volumeMounts:
            - mountPath: /var/run # for container runtime socket
              name: run
            - mountPath: /logtail_host # for log access on the node
              mountPropagation: HostToContainer
              name: root
              readOnly: true
            - mountPath: /usr/local/ilogtail/checkpoint # for checkpoint between container restart
              name: checkpoint
            #- mountPath: /usr/local/ilogtail/config/local # mount config dir
            # 1.8.4好像还是这个路径
            - mountPath: /usr/local/ilogtail/user_yaml_config.d/ # mount config dir
              name: user-config
              readOnly: true
          lifecycle:
            preStop:
              exec:
                command:
                  - /usr/local/ilogtail/ilogtail_control.sh
                  - stop
                  - "3"
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 7953
              scheme: HTTP
            initialDelaySeconds: 3
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
      dnsPolicy: ClusterFirstWithHostNet
      hostNetwork: true
      volumes:
        - hostPath:
            path: /var/run
            type: Directory
          name: run
        - hostPath:
            path: /
            type: Directory
          name: root
        - hostPath:
            path: /etc/ilogtail-ilogtail-ds/checkpoint
            type: DirectoryOrCreate
          name: checkpoint
        - configMap:
            defaultMode: 420
            name: ilogtail-user-cm
          name: user-config
