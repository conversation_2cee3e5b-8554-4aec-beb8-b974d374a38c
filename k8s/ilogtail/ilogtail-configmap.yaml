apiVersion: v1
kind: ConfigMap
metadata:
  name: ilogtail-user-cm
  namespace: ilogtail
data:
  log_ems_api.yaml: |
    enable: true
    inputs:
      - Type: file_log
        LogPath: /opt/log
        FilePattern: "*.log"
        ContainerFile: true
        ContainerInfo:
          K8sNamespaceRegex: ems-cloud-local
          K8sPodRegex: ^(ems-api.*)$
    processors:
      - Type: processor_string_replace
        SourceKey: content
        Method: regex
        Match: \x1b\[[0-9;]*m
        ReplaceString: ''
      - Type: processor_regex_accelerate
        Keys:
          - timestr
          - client_ip
          - thread
          - level
          - logger
          - message
        Regex: (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) \[([^\]]*)\] \[([^\]]+)\] (\w+)\s+([\w.]+) - (.*)
        DiscardUnmatch: false
        LogBeginRegex: .*Exception.*
        LogEndRegex: (\s+at java.base/java.lang.Thread.run.*)$
    flushers:
      - Type: flusher_kafka_v2
        Brokers:
          - ***************:30178
        Topic: log-ems-api-dev-test
        Convert:
          TagFieldsRename:
            host.name: ""
            host.ip: ip
            k8s.node.name: ""
            k8s.pod.name: pod
            k8s.node.ip: ""
            k8s.pod.uid: ""
            container.image.name: ""
            container.ip: ""
            container.name: container
            k8s.namespace.name: namespace
            log.file.path: ""
          ProtocolFieldsRename:
            time: 'timestamp'
          Protocol: custom_single_flatten
          Encoding: json
