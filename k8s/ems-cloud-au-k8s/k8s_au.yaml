kind: Deployment
apiVersion: apps/v1
metadata:
  name: ems-api
  namespace: ems-cloud-au
  labels:
    app: ems-api
  annotations:
    deployment.kubernetes.io/revision: '123'
    kubesphere.io/creator: jacob
    kubesphere.io/description: pangu后端服务，提供各种api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ems-api
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ems-api
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
      containers:
        - name: container-29xk0s
          image: 'harbor.weiheng-tech.com/dev/ems/ems-api-cloud:latest'
          ports:
            - name: tcp-8808
              containerPort: 8808
              protocol: TCP
          env:
            - name: SWAGGER_ENABLE
              value: 'true'
            - name: DATABASE_HOST
              value: 'mysql://ems-mysql:3306'
            - name: DATABASE_USERNAME
              value: root
            - name: DATABASE_PASSWORD
              value: 123@abcd
            - name: REDIS_HOST
              value: ems-redis
            - name: REDIS_PORT
              value: '6379'
            - name: REDIS_DATABASE
              value: '8'
            - name: AUTH_PUB_KEY
              value: >-
                MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCXPkkw5KYbcln2hh0tWXTH9TxdqHIKNYXANGTePk6DE+Cs/e1HzXrUxm8E+YfKQKreFJyIujJEAsfaIcJZy9N/ZwFn4Mk3K7C2orv0YXrn3hXJ/HFqxCCnQqnp+urGHCd+qTOciiNV2pIQG0R+iRb3gBolwDmuFcUABYpAcpN5zQIDAQAB
            - name: AUTH_PRI_KEY
              value: >-
                MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJc+STDkphtyWfaGHS1ZdMf1PF2ocgo1hcA0ZN4+ToMT4Kz97UfNetTGbwT5h8pAqt4UnIi6MkQCx9ohwlnL039nAWfgyTcrsLaiu/RheufeFcn8cWrEIKdCqen66sYcJ36pM5yKI1XakhAbRH6JFveAGiXAOa4VxQAFikByk3nNAgMBAAECgYB+eylMSTscovHXN2s5HKGMA2t7S74rCX2UMnzUWzjfZ4UyRpzjulRpkpUPjPphStlaJdSOh2A3/jdSFX9qBwoUKBFNdp67EOhW7NjRc7whBVnO0/zrBaeW3Efn5YEb1o+X3QNzp3NfWXdyJv65RewuUZPZZSfkqh4H3hjBqmBs4QJBAOted9KU8/uReGYwhsMG6CEuM3qKy27NfWbOi6tHnhfk+ypY8b/zvCXMsjMfCkvZ2jxCY7FIuvhnuwZ2mg6b4+UCQQCkgBcNsbFMMngu6oIlJ9Ewlfn7CvPwoXD6phh1wb6jHvrYLf+KKnyWmQEJhoA3luXebAcXEToHX/9Wj3t1lK/JAkBTzEVy5u9awLcSAvLn2ryom49ecK3vHCAqixz09UGXFkJKGHKxubBh8Nf9FW8QBFcLn0NpKhDPQfc3XOCKlPv1AkAES2/OpLf7REoM94RkUfDNMu0u169ctepMMO/six1eBt4HrNPCGK/eAqqbRA6u5Nqlfu6EdKeuL5xr9x0DCdm5AkAHwnvjggnioz3LQidF7FfbGLsfDz8Ab/jGHoFc8ycbtO+ePHRibw4mpofj6yzESI6dkKI+S2967abNL3qhGHSB
            - name: EMS_ANALYSIS
              value: 'false'
            - name: GATEWAY_URL
              value: 'http://nginx.dev.weiheng-tech.com:32308'
            - name: EMS_TASK
              value: 'false'
            - name: AD_URL
              value: 'http://ldap-login.dev.weiheng-tech.com'
            - name: PROJECT_TASK
              value: 'true'
            - name: DATA_CENTER
              value: '2'
            - name: EMS_VERSION
              value: 1.3.6
            - name: USER_REGRET
              value: '0.008'
            - name: LOGIN_ID
              value: SMS_268615022
            - name: VERIFY_ID
              value: SMS_268615022
            - name: REGISTER_ID
              value: SMS_268615022
            - name: LOGIN_EXPIRE
              value: '15'
            - name: VERIFY_EXPIRE
              value: '15'
            - name: registerExpire
              value: '15'
            - name: FORWARD_URL
              value: 'http://pangu-forward.dev.weiheng-tech.com/'
            - name: REFRESH_URL
              value: 'http://ems-site-forwarder:5000/refresh'
            - name: XXL_URL
              value: 'ems-xxl-job:8080'
            - name: PROFILES_ACTIVE
              value: dev-au
            - name: EMS_EMAIL_NOTICE_DEMAND_EXCEED
              value: '<EMAIL>'
            - name: EMS_EMAIL_NOTICE_MONTH_INIT
              value: '<EMAIL>'
            - name: EMS_EMAIL_NOTICE_STRATEGY_DYNAMIC_PRICE_UPLOAD
              value: '<EMAIL>'
            - name: TZ
              value: Asia/Shanghai
          resources:
            limits:
              memory: 3000Mi
            requests:
              cpu: '2'
              memory: 200Mi
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
