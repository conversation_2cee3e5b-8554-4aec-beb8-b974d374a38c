import site
import os
import subprocess
import json


def check_and_install(package, check_cmd, install_cmd):
    """检查命令是否存在, 如果不存在直接安装"""
    try:
        subprocess.run(check_cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print(f"{package} 已安装")
    except subprocess.CalledProcessError:
        print(f"{package}未安装, 准备安装...")
        subprocess.run(install_cmd, shell=True, check=True)
        print(f"{package}未安装, 安装完成")


def execute_mysqldump(network_mode):
    dump_cmd = f"docker run --rm --network {network_mode} mysql:5.7 mysqldump -hmysql -P3306 -uroot -p123@abcd --no-data cloud > aaa.sql"
    print(dump_cmd)
    result = subprocess.run(dump_cmd, shell=True, check=True)
    print(f"mysqldump scheme 执行完毕 {result}")



def get_network_mode(container_name):
    try:
        result = subprocess.run(["docker", "inspect", container_name], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                universal_newlines=True,
                                check=True
                                )
        container_info = json.loads(result.stdout)
        network_mode = container_info[0]["HostConfig"]["NetworkMode"]
        return network_mode
    except subprocess.CalledProcessError as e:
        print(f"执行docker inspect {container_name} 失败: {e}")
        return None
    except (KeyError, IndexError, json.JSONDecodeError) as e:
        print(f"解析Json 失败: {e}")
        return None


if __name__ == '__main__':
    print("mysql dump script")
    check_and_install("lrzsz", "command -v rz", "yum -y install lrzsz")
    network_mode = get_network_mode("mysql")
    if network_mode:
        print(f"mysql NetworkMode: {network_mode}")
        execute_mysqldump(network_mode)

