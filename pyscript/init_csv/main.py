# This is a sample Python script.
import gitlab
import base64
import urllib3
import warnings
import pandas as pd
from io import StringIO
import mysql.connector
from enum import Enum, auto

# Press ⌃R to execute it or replace it with your code.
# Press Double ⇧ to search everywhere for classes, files, tool windows, actions, and settings.

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class CsvDataType(Enum):
    EVENT_CODE = auto()
    METER_EVENT_CODE = auto()
    POINT_DATA = auto()


class CsvData:
    csv_content: str
    data_type: CsvDataType


def get_csv_content(csv_data_type: CsvDataType) -> CsvData:
    # 连接 GitLab
    gl = gitlab.Gitlab('https://gitlab.weiheng-tech.com', private_token='********************', ssl_verify=False)

    # 获取项目（用 project ID 或者 namespace/name）
    project = gl.projects.get('SystemIntegrated/go-protocols-csv')

    # 下载 CSV 文件
    if csv_data_type == CsvDataType.EVENT_CODE:
        file_path = "event_code_new.csv"
    elif csv_data_type == CsvDataType.METER_EVENT_CODE:
        file_path = "meter_event_code.csv"
    elif csv_data_type == CsvDataType.POINT_DATA:
        file_path = "t_point_data.csv"
    file = project.files.get(file_path=file_path, ref='master')
    # 读取文件内容
    csv_content = base64.b64decode(file.content).decode('utf-8')  # 转换为 UTF-8 字符串
    # 打印文件内容
    print(csv_content)
    return csv_content


# Press the green button in the gutter to run the script.



def to_save_db(data_frames: dict):
    # 连接 MySQL
    db = mysql.connector.connect(
        host="nginx.dev.weiheng-tech.com",
        port=30175,
        user="root",
        password="123@abcd",
        database="cloud-johnny",
    )

    # 创建游标
    cursor = db.cursor()

    for df_type, df in data_frames.items():
        print(f"Processing {df_type} DataFrame")

        # 遍历 DataFrame 存入数据库
        for _, row in df.iterrows():
            if df_type == CsvDataType.EVENT_CODE:
                columns = "point_column, device_type_code, device_type, event_level, bit_offset, bit_value, event_description, event_description_en, bit_stand, remarks, permission, event_code"
                placeholders = ", ".join(["%s"] * 12)  # 12 列
                sql = f"""
                             INSERT INTO t_event_code ({columns})
                             VALUES ({placeholders})
                            """
                cursor.execute(sql, tuple(row))  # 执行插入语句
            elif df_type == CsvDataType.METER_EVENT_CODE:
                columns = "project_id, project_name, meter_type, bit_offset, bit_value, description, description_en, digital_0_analog_1_control_2, event_level, event_code"
                placeholders = ", ".join(["%s"] * 10)  # 10 列
                sql = f"""
                             INSERT INTO t_meter_event_code ({columns})
                             VALUES ({placeholders})
                            """
                cursor.execute(sql, tuple(row))  # 执行插入语句
            elif df_type == CsvDataType.POINT_DATA:
                columns = "point_offset,point_address,point_name,point_column,point_mul,point_type,point_level,point_len"
                placeholders = ", ".join(["%s"] * 8)  # 10 列
                sql = f"""
                             INSERT INTO t_point_data ({columns})
                             VALUES ({placeholders})
                            """
                cursor.execute(sql, tuple(row))  # 执行插入语句
    # 提交事务，保存所有的更改
    db.commit()

    # 关闭游标和数据库连接
    cursor.close()
    db.close()


if __name__ == '__main__':
    # 第一步获取到 最新的 csv 文件内容
    event_csv_content = get_csv_content(CsvDataType.EVENT_CODE)
    meter_csv_content = get_csv_content(CsvDataType.METER_EVENT_CODE)
    point_data_csv_content = get_csv_content(CsvDataType.POINT_DATA)


    # 第二步解析这个文件 把 CSV 内容转换为 DataFrame
    # event_csv_data = StringIO(event_csv_content)
    # event_df = pd.read_csv(event_csv_data)
    # meter_event_csv_data = StringIO(meter_csv_content)
    # meter_event_df = pd.read_csv(meter_event_csv_data)
    # point_data_csv_data = StringIO(point_data_csv_content)
    # point_data_df = pd.read_csv(point_data_csv_data)


    to_save_db({
        #CsvDataType.POINT_DATA: point_data_df
        # CsvDataType.EVENT_CODE: event_df,
        # CsvDataType.METER_EVENT_CODE: meter_event_df
    })

# See PyCharm help at https://www.jetbrains.com/help/pycharm/

