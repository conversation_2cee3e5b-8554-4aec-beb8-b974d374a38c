-- Adminer 4.8.1 MySQL 5.7.36 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

DROP TABLE IF EXISTS `t_ammeter`;
CREATE TABLE `t_ammeter` (
                             `id` varchar(128) NOT NULL COMMENT '电表标识符uuid',
                             `name` varchar(128) DEFAULT NULL COMMENT '电表名称',
                             `ip` varchar(32) DEFAULT NULL COMMENT '电表ip',
                             `port` int(10) DEFAULT NULL COMMENT '电表端口',
                             `type` int(11) DEFAULT NULL COMMENT '1 PV电表(PV)、 2并网点电表(Grid)、3负载电表(Load)',
                             `vendor` varchar(256) DEFAULT NULL COMMENT '电表型号，厂商名称 只有 DTSD3125 和 EMSMeter',
                             `status` int(1) DEFAULT NULL COMMENT '1已连接、2未连接、0未测试',
                             `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                             `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
                             `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                             `update_by` varchar(128) DEFAULT NULL COMMENT '修改者',
                             `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                             `reverse` tinyint(1) DEFAULT '0' COMMENT '是否反接',
                             `meter_num` int(10) DEFAULT NULL COMMENT 'CEM9000测控号',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


SET NAMES utf8mb4;

DROP TABLE IF EXISTS `t_authority`;
CREATE TABLE `t_authority` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT,
                               `pid` bigint(20) DEFAULT NULL COMMENT '父ID',
                               `ap_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接入点标识（格式   {模块}:{菜单}:{功能}）',
                               `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接入点名称',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `t_authority` (`id`, `pid`, `ap_key`, `name`) VALUES
(1,	NULL,	'/homePage',	'首页'),
(2,	NULL,	'/project',	'电站全控屏'),
(3,	NULL,	'/group',	'设备分组管理'),
(4,	NULL,	'/strategy',	'控制策略'),
(5,	NULL,	'/operation',	'运营收益'),
(6,	NULL,	'/heatMap',	'热力图'),
(7,	NULL,	'/monitor',	'运行监控'),
(8,	NULL,	'/event',	'事件管理'),
(9,	NULL,	'/report',	'报表'),
(10,	NULL,	'/diagram',	'历史曲线'),
(11,	NULL,	'/user',	'账户权限管理'),
(12,	NULL,	'/system',	'系统配置'),
(13,	NULL,	'/carbon',	'碳中和'),
(14,	NULL,	'/manage/area',	'管理端-区域管理'),
(15,	NULL,	'/manage/project',	'管理端-项目管理'),
(16,	NULL,	'/manage/user',	'管理端-账户权限管理'),
(31,	3,	'/group/group',	'分组管理'),
(32,	3,	'/group/ammeter',	'电表管理'),
(33,	3,	'/group/device',	'设备管理'),
(34,	3,	'/group/controller',	'控制器管理'),
(51,	5,	'/operation/getBatteryProfit',	'充放电收益'),
(52,	5,	'/operation/getProfit',	'运营收益'),
(61,	6,	'/heatMap/charge',	'充电功率'),
(62,	6,	'/heatMap/discharge',	'放电功率'),
(63,	6,	'/heatMap/load',	'负载功率'),
(64,	6,	'/heatMap/pvDischarge',	'PV发电'),
(65,	6,	'/heatMap/chargeCost',	'充电成本'),
(66,	6,	'/heatMap/temperature',	'集装箱温度'),
(67,	6,	'/heatMap/voltage',	'集装箱电压'),
(68,	6,	'/heatMap/powerRate',	'充放电功率'),
(71,	7,	'/monitor/pcsStatus',	'pcs监控'),
(72,	7,	'/monitor/bmsStatus',	'bms监控'),
(73,	7,	'/monitor/batteryStatus',	'bms电池簇监控'),
(74,	7,	'/monitor/airFireStatus',	'空调消防监控'),
(91,	9,	'/report/daily',	'日报'),
(92,	9,	'/report/month',	'月报'),
(93,	9,	'/report/year',	'年报'),
(94,	9,	'/report/meter/daily',	'电表日报'),
(95,	9,	'/report/meter/month',	'电表月报'),
(96,	9,	'/report/meter/year',	'电表年报'),
(101,	10,	'/diagram/getChargeRate',	'充放电功率'),
(103,	10,	'/diagram/getSocRate',	'BMS-SOC曲线'),
(104,	10,	'/diagram/getVoltageRate',	'BMS-电压曲线'),
(105,	10,	'/diagram/getBatteryRate',	'单体电池电压温度曲线'),
(106,	10,	'/diagram/getRealDemandRate',	'实际需量曲线'),
(107,	10,	'/diagram/getOriginalDemandRate',	'原需量曲线'),
(108,	10,	'/diagram/getLoadRate',	'负载曲线'),
(109,	10,	'/diagram/getOriginalDemandRate_wh',	'原需量曲线-分组'),
(110,	10,	'/diagram/getRealDemandRate_wh',	'实际需量曲线-分组'),
(111,	11,	'/user/account',	'账户管理'),
(112,	11,	'/user/role',	'权限管理'),
(121,	12,	'/system/all',	'全部设备'),
(122,	12,	'/system/singleton',	'单个设备'),
(131,	13,	'/carbon/reduction',	'碳减排'),
(132,	13,	'/carbon/track',	'碳足迹'),
(141,	14,	'/manage/area/create',	'区域创建'),
(142,	14,	'/manage/area/update',	'区域修改'),
(143,	14,	'/manage/area/delete',	'区域删除'),
(144,	14,	'/manage/price',	'电价配置'),
(151,	15,	'/manage/project/create',	'项目创建'),
(152,	15,	'/manage/project/update',	'项目修改'),
(153,	15,	'/manage/project/delete',	'项目删除'),
(161,	16,	'/manage/user/role',	'角色管理'),
(162,	16,	'/manage/user/account',	'账户管理'),
(201,	10,	'/diagram/getDemandRate',	'需量曲线查询'),
(202,	10,	'/diagram/getGridRate',	'并网曲线查询'),
(203,	10,	'/diagram/getGridRate_wh',	'并网曲线分组查询'),
(204,	10,	'/diagram/getPvRate',	'pv曲线查询'),
(205,	10,	'/diagram/getPvRate_wh',	'pv曲线分组查询');
(209,	10,	'/diagram/getMaxDemandRate',	'最大需量曲线查询'),

DROP TABLE IF EXISTS `t_controller`;
CREATE TABLE `t_controller` (
                                `id` varchar(128) NOT NULL COMMENT 'uuid',
                                `ip` tinytext COMMENT 'ip',
                                `port` int(11) DEFAULT NULL COMMENT '端口',
                                `sn` char(128) DEFAULT NULL COMMENT '序列号',
                                `version` varchar(30) DEFAULT NULL COMMENT '版本号',
                                `status` int(11) DEFAULT NULL COMMENT '1已连接、2未连接、0未测试',
                                `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                                `create_by` tinytext COMMENT '创建者',
                                `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                `update_by` tinytext COMMENT '更新者',
                                `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='协调控制器';


DROP TABLE IF EXISTS `t_device`;
CREATE TABLE `t_device` (
                            `id` varchar(128) NOT NULL COMMENT '设备标识uuid',
                            `name` varchar(64) DEFAULT NULL COMMENT '设备名称',
                            `ip` varchar(32) DEFAULT NULL COMMENT '设备ip',
                            `port` int(10) DEFAULT NULL COMMENT '设备端口',
                            `status` int(1) DEFAULT '0' COMMENT '0未连接1已连接2未连接',
                            `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                            `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                            `create_by` varchar(32) DEFAULT NULL COMMENT '创建者',
                            `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                            `update_by` varchar(32) DEFAULT NULL COMMENT '更新者',
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `t_electric_price`;
CREATE TABLE `t_electric_price` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '电价id',
                                    `start_date` bigint(20) DEFAULT NULL COMMENT '电价开始日期',
                                    `end_date` bigint(20) DEFAULT NULL COMMENT '电价结束日期',
                                    `period` int(11) DEFAULT NULL COMMENT '电价时段（谷1 平2 峰3 尖4）',
                                    `price` double DEFAULT NULL COMMENT '分段电价',
                                    `period_order` int(11) DEFAULT NULL COMMENT '时段内部序号（预留）',
                                    `start_time` time DEFAULT NULL COMMENT '时段开始时间',
                                    `end_time` time DEFAULT NULL COMMENT '时段结束时间',
                                    `demand_price` double DEFAULT '0' COMMENT '需量价格',
                                    `pv_self_price` double DEFAULT '0' COMMENT '光伏自用价格',
                                    `pv_df_price` double DEFAULT '0' COMMENT '脱硫标杆价格',
                                    `pv_subsidy_price` double DEFAULT '0' COMMENT '光伏国家补贴价格',
                                    `pv_price` double DEFAULT '0' COMMENT '光伏发电价格',
                                    `whether_current` int(11) DEFAULT '0' COMMENT '是否至今，至今为1，不是为0',
                                    `pid` int(11) DEFAULT NULL COMMENT '父id',
                                    `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                                    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
                                    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(128) DEFAULT NULL COMMENT '修改者',
                                    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_electric_price` (`id`, `start_date`, `end_date`, `period`, `price`, `period_order`, `start_time`, `end_time`, `demand_price`, `pv_self_price`, `pv_df_price`, `pv_subsidy_price`, `pv_price`, `whether_current`, `pid`, `project_id`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
(199,	1653321600,	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	40,	0.4,	0.4,	0.4,	1,	1,	NULL,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074),
(200,	1653321600,	NULL,	1,	0.2707,	NULL,	'00:00:00',	'08:00:00',	0,	0,	0,	0,	0,	0,	199,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074),
(201,	1653321600,	NULL,	2,	0.6466,	NULL,	'11:00:00',	'14:00:00',	0,	0,	0,	0,	0,	0,	199,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074),
(202,	1653321600,	NULL,	2,	0.6466,	NULL,	'15:00:00',	'18:00:00',	0,	0,	0,	0,	0,	0,	199,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074),
(203,	1653321600,	NULL,	2,	0.6466,	NULL,	'22:00:00',	'00:00:00',	0,	0,	0,	0,	0,	0,	199,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074),
(204,	1653321600,	NULL,	3,	1.1119,	NULL,	'08:00:00',	'10:00:00',	0,	0,	0,	0,	0,	0,	199,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074),
(205,	1653321600,	NULL,	3,	1.1119,	NULL,	'18:00:00',	'22:00:00',	0,	0,	0,	0,	0,	0,	199,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074),
(206,	1653321600,	NULL,	4,	1.33428,	NULL,	'10:00:00',	'11:00:00',	0,	0,	0,	0,	0,	0,	199,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074),
(207,	1653321600,	NULL,	4,	1.33428,	NULL,	'14:00:00',	'15:00:00',	0,	0,	0,	0,	0,	0,	199,	'4f537620d37d40e19dd25be5ca6ad941',	'whadmin',	1664178074,	'whadmin',	1664178074);

DROP TABLE IF EXISTS `t_event_code`;
CREATE TABLE `t_event_code` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `point_column` varchar(50) DEFAULT NULL COMMENT '对应的点位字段',
                                `device_type_code` int(11) DEFAULT NULL COMMENT '设备类型码',
                                `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型名称',
                                `event_level` varchar(20) DEFAULT NULL COMMENT '事件等级Fault、State、Alarm',
                                `bit_offset` int(11) DEFAULT NULL COMMENT 'BIT码偏移量',
                                `bit_value` int(11) DEFAULT NULL COMMENT 'BIT码',
                                `event_description` varchar(80) DEFAULT NULL COMMENT '事件描述',
                                `bit_stand` int(11) DEFAULT NULL COMMENT 'BIT位是0或者1有效',
                                `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
                                `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                `create_by` varchar(32) DEFAULT NULL COMMENT '创建者',
                                `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                `update_by` varchar(32) DEFAULT NULL COMMENT '更新者',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='告警信息表';

INSERT INTO `t_event_code` (`id`, `point_column`, `device_type_code`, `device_type`, `event_level`, `bit_offset`, `bit_value`, `event_description`, `bit_stand`, `remarks`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
(1,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	0,	'储能电池极性反接故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(2,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	1,	'储能电池电压异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(3,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	2,	'直流侧半母线硬件过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(4,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	3,	'交流硬件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(5,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	4,	'IGBT A相故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(6,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	5,	'IGBT B相故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(7,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	6,	'IGBT C相故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(8,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	7,	'交流侧A相霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(9,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	8,	'交流侧A相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(10,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	9,	'交流侧A相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(11,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	10,	'交流侧A相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(12,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	11,	'交流侧B相电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(13,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	12,	'交流侧B相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(14,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	13,	'交流侧B相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(15,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	14,	'交流侧B相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(16,	'pcs_{i}_fault_bit_0',	0,	'为恒630kWPCS',	'Fault',	0,	15,	'交流侧C相电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(17,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	0,	'交流侧C相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(18,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	1,	'交流侧C相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(19,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	2,	'交流侧C相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(20,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	3,	'直流侧电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(21,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	4,	'直流侧霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(22,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	5,	'直流侧霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(23,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	6,	'直流侧霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(24,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	7,	'直流侧脱扣器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(25,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	8,	'直流侧断路器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(26,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	9,	'IGBT风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(27,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	10,	'电抗器风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(28,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	11,	'直流侧全母线软件过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(29,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	12,	'直流侧全母线软件欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(30,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	13,	'半母线电压偏差过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(31,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	14,	'直流侧软件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(32,	'pcs_{i}_fault_bit_1',	0,	'为恒630kWPCS',	'Fault',	16,	15,	'电网交流过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(33,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	0,	'电网交流欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(34,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	1,	'离网交流过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(35,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	2,	'离网交流欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(36,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	3,	'电网交流过频',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(37,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	4,	'电网交流欠频',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(38,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	5,	'交流软件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(39,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	6,	'输出缺相故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(40,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	7,	'防雷器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(41,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	8,	'孤岛故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(42,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	9,	'电网电压不平衡',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(43,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	10,	'交流侧电流直流分量超限',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(44,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	11,	'环境过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(45,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	12,	'IGBT模块A相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(46,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	13,	'IGBT模块B相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(47,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	14,	'IGBT模块C相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(48,	'pcs_{i}_fault_bit_2',	0,	'为恒630kWPCS',	'Fault',	32,	15,	'电抗器过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(49,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	0,	'AD零漂过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(50,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	1,	'HMI通讯失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(51,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	2,	'BMS通讯失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(52,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	3,	'EMS通讯失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(53,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	4,	'并机通讯失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(54,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	5,	'I2C总线故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(55,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	6,	'SPI故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(56,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	7,	'物联网通讯失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(57,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	8,	'客户后台通讯失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(58,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	9,	'直流侧预充接触器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(59,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	10,	'交流侧预充接触器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(60,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	11,	'CBC过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(61,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	12,	'变流器过载',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(62,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	13,	'电网电压反序',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(63,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	14,	'母线软启动失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(64,	'pcs_{i}_fault_bit_3',	0,	'为恒630kWPCS',	'Fault',	48,	15,	'交流侧脱扣器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(65,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	0,	'交流侧断路器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(66,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	1,	'直流侧熔断器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(67,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	2,	'电流控制偏差过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(68,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	3,	'环境低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(69,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	4,	'IGBT模块A相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(70,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	5,	'IGBT模块B相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(71,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	6,	'IGBT模块C相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(72,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	7,	'交流开关合闸电压不匹配',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(73,	'pcs_{i}_fault_bit_4',	0,	'为恒630kWPCS',	'Fault',	64,	8,	'绝缘电阻偏低',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(74,	'pcs_{i}_fault_bit_7',	0,	'为恒630kWPCS',	'Fault',	112,	15,	'其他故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(75,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	0,	'PCS运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(76,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	1,	'AC断路器闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(77,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	2,	'AC接触器闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(78,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	3,	'DC断路器闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(79,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	4,	'DC接触器闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(80,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	5,	'DC辅助接触器闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(81,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	6,	'IGBT的A相风扇开启',	0,	NULL,	NULL,	NULL,	NULL,	NULL),
(82,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	7,	'IGBT的B相风扇开启',	0,	NULL,	NULL,	NULL,	NULL,	NULL),
(83,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	8,	'IGBT的C相风扇开启',	0,	NULL,	NULL,	NULL,	NULL,	NULL),
(84,	'pcs_{i}_state_bit_0',	0,	'为恒630kWPCS',	'State',	0,	9,	'电感风扇开启',	0,	NULL,	NULL,	NULL,	NULL,	NULL),
(85,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	10,	'电池搁置状态',	1,	'偏移量0的数据',	NULL,	NULL,	NULL,	NULL),
(86,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	11,	'电池充电状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(87,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	12,	'电池放电状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(88,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	13,	'电池休眠状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(89,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	14,	'风扇异常告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(90,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	3,	'系统故障',	1,	'偏移量0',	NULL,	NULL,	NULL,	NULL),
(91,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	4,	'电流保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(92,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	5,	'电压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(93,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	6,	'温度保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(94,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	7,	'电压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(95,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	8,	'电流告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(96,	'bms_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	9,	'温度告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(97,	'bms_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	0,	'放电回路开关',	1,	'偏移量15，开关量',	NULL,	NULL,	NULL,	NULL),
(98,	'bms_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	1,	'充电回路开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(99,	'bms_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	2,	'预充电回路开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(100,	'bms_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	3,	'蜂鸣器开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(101,	'bms_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	4,	'加热膜开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(102,	'bms_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	5,	'限流模块开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(103,	'bms_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	6,	'风扇开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(104,	'bms_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	0,	'强充标志位',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(105,	'bms_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	1,	'均衡充电标志位',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(106,	'bms_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	2,	'禁止充电标志位',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(107,	'bms_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	3,	'禁止放电标志位',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(108,	'bms_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	4,	'绝缘电阻故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(109,	'bms_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	5,	'绝缘电阻故障等级2',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(110,	'bms_state_bit_3',	1,	'派能高压电池箱',	'State',	48,	0,	'初始化完成',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(111,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	0,	'单体低压保护',	1,	'偏移量1',	NULL,	NULL,	NULL,	NULL),
(112,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	1,	'单体高压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(113,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	2,	'总电压低压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(114,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	3,	'总电压高压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(115,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	4,	'充电低温保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(116,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	5,	'充电高温保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(117,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	6,	'放电低温保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(118,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	7,	'放电高温保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(119,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	8,	'充电过流保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(120,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	9,	'放电过流保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(121,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	10,	'短路保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(122,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	12,	'模块温度高保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(123,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	13,	'模块低压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(124,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	14,	'模块高压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(125,	'bms_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	15,	'单体二级欠压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(126,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	0,	'电压传感器故障',	1,	'Error Code 1',	NULL,	NULL,	NULL,	NULL),
(127,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	1,	'温度传感器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(128,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	2,	'内部通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(129,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	3,	'输入过压故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(130,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	4,	'输入反接故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(131,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	5,	'继电器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(132,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	6,	'电池损坏故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(133,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	7,	'关机电路故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(134,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	8,	'BMIC故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(135,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	9,	'内部总线故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(136,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	10,	'开机自检异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(137,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	11,	'安全功能异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(138,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	12,	'绝缘故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(139,	'bms_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	13,	'紧急停机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(140,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	0,	'单体低压告警',	1,	'偏移量2',	NULL,	NULL,	NULL,	NULL),
(141,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	1,	'单体高压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(142,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	2,	'总低压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(143,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	3,	'总高压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(144,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	4,	'充电低温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(145,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	5,	'充电高温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(146,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	6,	'放电低温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(147,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	7,	'放电高温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(148,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	8,	'充电过流告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(149,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	9,	'放电过流告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(150,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	11,	'主控温度高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(151,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	12,	'模块温度高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(152,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	13,	'模块低压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(153,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	14,	'模块高压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(154,	'bms_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	15,	'端子温度异常告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(155,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	10,	'电池搁置状态',	1,	'偏移量0的数据',	NULL,	NULL,	NULL,	NULL),
(156,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	11,	'电池充电状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(157,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	12,	'电池放电状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(158,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	13,	'电池休眠状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(159,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	14,	'风扇异常告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(160,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	3,	'系统故障',	1,	'偏移量0',	NULL,	NULL,	NULL,	NULL),
(161,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	4,	'电流保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(162,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	5,	'电压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(163,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	6,	'温度保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(164,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	7,	'电压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(165,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	8,	'电流告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(166,	'bms_cluster_{i}_state_bit_0',	1,	'派能高压电池箱',	'State',	0,	9,	'温度告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(167,	'bms_cluster_{i}_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	0,	'放电回路开关',	1,	'偏移量15，开关量',	NULL,	NULL,	NULL,	NULL),
(168,	'bms_cluster_{i}_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	1,	'充电回路开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(169,	'bms_cluster_{i}_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	2,	'预充电回路开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(170,	'bms_cluster_{i}_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	3,	'蜂鸣器开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(171,	'bms_cluster_{i}_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	4,	'加热膜开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(172,	'bms_cluster_{i}_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	5,	'限流模块开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(173,	'bms_cluster_{i}_state_bit_1',	1,	'派能高压电池箱',	'State',	16,	6,	'风扇开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(174,	'bms_cluster_{i}_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	0,	'强充标志位',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(175,	'bms_cluster_{i}_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	1,	'均衡充电标志位',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(176,	'bms_cluster_{i}_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	2,	'禁止充电标志位',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(177,	'bms_cluster_{i}_state_bit_2',	1,	'派能高压电池箱',	'State',	32,	3,	'禁止放电标志位',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(178,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	0,	'单体低压保护',	1,	'偏移量1',	NULL,	NULL,	NULL,	NULL),
(179,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	1,	'单体高压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(180,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	2,	'总电压低压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(181,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	3,	'总电压高压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(182,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	4,	'充电低温保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(183,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	5,	'充电高温保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(184,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	6,	'放电低温保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(185,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	7,	'放电高温保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(186,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	8,	'充电过流保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(187,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	9,	'放电过流保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(188,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	10,	'短路保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(189,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	12,	'模块温度高保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(190,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	13,	'模块低压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(191,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	14,	'模块高压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(192,	'bms_cluster_{i}_fault_bit_0',	1,	'派能高压电池箱',	'Fault',	0,	15,	'单体二级欠压保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(193,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	0,	'电压传感器故障',	1,	'Error Code 1',	NULL,	NULL,	NULL,	NULL),
(194,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	1,	'温度传感器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(195,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	2,	'内部通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(196,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	3,	'输入过压故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(197,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	4,	'输入反接故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(198,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	5,	'继电器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(199,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	6,	'电池损坏故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(200,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	7,	'关机电路故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(201,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	8,	'BMIC故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(202,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	9,	'内部总线故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(203,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	10,	'开机自检异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(204,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	11,	'安全功能异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(205,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	12,	'绝缘故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(206,	'bms_cluster_{i}_fault_bit_1',	1,	'派能高压电池箱',	'Fault',	16,	13,	'紧急停机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(207,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	0,	'单体低压告警',	1,	'偏移量2',	NULL,	NULL,	NULL,	NULL),
(208,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	1,	'单体高压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(209,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	2,	'总低压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(210,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	3,	'总高压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(211,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	4,	'充电低温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(212,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	5,	'充电高温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(213,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	6,	'放电低温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(214,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	7,	'放电高温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(215,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	8,	'充电过流告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(216,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	9,	'放电过流告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(217,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	11,	'主控温度高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(218,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	12,	'模块温度高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(219,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	13,	'模块低压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(220,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	14,	'模块高压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(221,	'bms_cluster_{i}_alarm_bit_0',	1,	'派能高压电池箱',	'Alarm',	0,	15,	'端子温度异常告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(222,	'air_conditioner_{i}_state_bit_0',	2,	'Envicool MC125',	'State',	0,	0,	'开机指令',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(223,	'air_conditioner_{i}_state_bit_0',	2,	'Envicool MC125',	'State',	0,	1,	'系统待机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(224,	'air_conditioner_{i}_state_bit_0',	2,	'Envicool MC125',	'State',	0,	2,	'系统运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(225,	'air_conditioner_{i}_state_bit_0',	2,	'Envicool MC125',	'State',	0,	3,	'电加热运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(226,	'air_conditioner_{i}_state_bit_0',	2,	'Envicool MC125',	'State',	0,	4,	'柜内风扇运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(227,	'air_conditioner_{i}_state_bit_0',	2,	'Envicool MC125',	'State',	0,	5,	'柜外风扇运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(228,	'air_conditioner_{i}_state_bit_0',	2,	'Envicool MC125',	'State',	0,	6,	'压缩机运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(229,	'air_conditioner_{i}_state_bit_0',	2,	'Envicool MC125',	'State',	0,	7,	'水泵运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(230,	'air_conditioner_{i}_fault_bit_0',	2,	'Envicool MC125',	'Fault',	0,	0,	'柜内风扇故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(231,	'air_conditioner_{i}_fault_bit_0',	2,	'Envicool MC125',	'Fault',	0,	1,	'柜外风扇故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(232,	'air_conditioner_{i}_fault_bit_0',	2,	'Envicool MC125',	'Fault',	0,	2,	'压缩机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(233,	'air_conditioner_{i}_fault_bit_0',	2,	'Envicool MC125',	'Fault',	0,	3,	'水泵故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(234,	'air_conditioner_{i}_fault_bit_0',	2,	'Envicool MC125',	'Fault',	0,	4,	'系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(235,	'air_conditioner_{i}_fault_bit_0',	2,	'Envicool MC125',	'Fault',	0,	5,	'电加热故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(236,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	0,	'高温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(237,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	1,	'内风机故障告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(238,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	2,	'外风机故障告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(239,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	3,	'压缩机故障告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(240,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	4,	'柜内回风温度温度探头故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(241,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	5,	'系统高压力告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(242,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	6,	'低温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(243,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	7,	'直流过压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(244,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	8,	'直流欠压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(245,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	9,	'交流过压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(246,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	10,	'交流欠压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(247,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	11,	'交流掉电告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(248,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	12,	'蒸发器温度传感器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(249,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	13,	'冷凝器温度传感器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(250,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	14,	'环境温度传感器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(251,	'air_conditioner_{i}_fault_bit_1',	2,	'Envicool MC125',	'Fault',	16,	15,	'蒸发器冻结报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(252,	'air_conditioner_{i}_fault_bit_2',	2,	'Envicool MC125',	'Fault',	32,	0,	'频繁高压力告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(253,	'air_conditioner_{i}_fault_bit_2',	2,	'Envicool MC125',	'Fault',	32,	1,	'严重告警总状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(254,	'firefighting_{i}_state_bit_0',	3,	'QKP01',	'State',	0,	0,	'气体喷洒状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(255,	'firefighting_{i}_state_bit_0',	3,	'QKP01',	'State',	0,	1,	'启动喷洒状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(256,	'firefighting_{i}_state_bit_0',	3,	'QKP01',	'State',	0,	2,	'备用电源状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(257,	'firefighting_{i}_state_bit_0',	3,	'QKP01',	'State',	0,	3,	'主电源状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(258,	'firefighting_{i}_state_bit_0',	3,	'QKP01',	'State',	0,	4,	'延时状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(259,	'firefighting_{i}_state_bit_0',	3,	'QKP01',	'State',	0,	5,	'手自动开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(260,	'firefighting_{i}_state_bit_0',	3,	'QKP01',	'State',	0,	6,	'电磁阀状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(261,	'firefighting_{i}_state_bit_0',	3,	'QKP01',	'State',	0,	7,	'压力开关状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(262,	'firefighting_{i}_fault_bit_0',	3,	'QKP01',	'Fault',	0,	0,	'火警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(263,	'firefighting_{i}_fault_bit_0',	3,	'QKP01',	'Fault',	0,	1,	'故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(264,	'firefighting_{i}_fault_bit_0',	3,	'QKP01',	'Fault',	0,	2,	'动作',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(265,	'firefighting_{i}_fault_bit_0',	3,	'QKP01',	'Fault',	0,	3,	'启动',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(266,	'firefighting_{i}_fault_bit_0',	3,	'QKP01',	'Fault',	0,	4,	'自动状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(267,	'firefighting_{i}_fault_bit_0',	3,	'QKP01',	'Fault',	0,	5,	'手动状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(268,	'firefighting_{i}_fault_bit_0',	3,	'QKP01',	'Fault',	0,	6,	'延时状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(269,	'system_state_bit_0',	65535,	'EMS Kernel',	'State',	0,	0,	'系统待机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(270,	'system_state_bit_0',	65535,	'EMS Kernel',	'State',	0,	1,	'系统运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(271,	'system_state_bit_0',	65535,	'EMS Kernel',	'State',	0,	2,	'系统充电中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(272,	'system_state_bit_0',	65535,	'EMS Kernel',	'State',	0,	3,	'系统放电中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(273,	'system_state_bit_0',	65535,	'EMS Kernel',	'State',	0,	15,	'初始化完成',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(274,	'system_alarm_bit_0',	65535,	'EMS Kernel',	'Alarm',	0,	0,	'CPU负载高',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(275,	'system_alarm_bit_0',	65535,	'EMS Kernel',	'Alarm',	0,	1,	'内存不足',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(276,	'system_alarm_bit_0',	65535,	'EMS Kernel',	'Alarm',	0,	2,	'硬盘不足',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(277,	'system_alarm_bit_0',	65535,	'EMS Kernel',	'Alarm',	0,	3,	'热备不可切换',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(278,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	0,	'BMS通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(279,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	1,	'PCS通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(280,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	2,	'空调通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(281,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	3,	'电表通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(282,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	4,	'消防通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(283,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	5,	'IO通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(284,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	6,	'UPS通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(285,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	7,	'水冷机通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(286,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	10,	'气体检测故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(287,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	11,	'外部急停',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(288,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	12,	'消防故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(289,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	13,	'水浸故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(290,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	14,	'开门故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(291,	'system_fault_bit_0',	65535,	'EMS Kernel',	'Fault',	0,	15,	'其它外部故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(292,	'system_fault_bit_1',	65535,	'EMS Kernel',	'Fault',	16,	0,	'BMS子系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(293,	'system_fault_bit_1',	65535,	'EMS Kernel',	'Fault',	16,	1,	'PCS子系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(294,	'system_fault_bit_1',	65535,	'EMS Kernel',	'Fault',	16,	2,	'空调子系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(295,	'system_fault_bit_1',	65535,	'EMS Kernel',	'Fault',	16,	3,	'电表子系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(296,	'system_fault_bit_1',	65535,	'EMS Kernel',	'Fault',	16,	4,	'消防子系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(297,	'system_fault_bit_1',	65535,	'EMS Kernel',	'Fault',	16,	5,	'IO子系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(298,	'system_fault_bit_1',	65535,	'EMS Kernel',	'Fault',	16,	6,	'UPS子系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(299,	'system_fault_bit_1',	65535,	'EMS Kernel',	'Fault',	16,	7,	'水冷机子系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(300,	'bms_state_bit_0',	6,	'CATL',	'State',	0,	0,	'初始化',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(301,	'bms_state_bit_0',	6,	'CATL',	'State',	0,	1,	'正常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(302,	'bms_state_bit_0',	6,	'CATL',	'State',	0,	2,	'禁充',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(303,	'bms_state_bit_0',	6,	'CATL',	'State',	0,	3,	'禁放',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(304,	'bms_state_bit_0',	6,	'CATL',	'State',	0,	4,	'告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(305,	'bms_state_bit_0',	6,	'CATL',	'State',	0,	5,	'故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(306,	'bms_state_bit_1',	6,	'CATL',	'State',	16,	0,	'下电正常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(307,	'bms_state_bit_1',	6,	'CATL',	'State',	16,	1,	'上电正常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(308,	'bms_state_bit_1',	6,	'CATL',	'State',	16,	2,	'上电故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(309,	'bms_state_bit_1',	6,	'CATL',	'State',	16,	3,	'下电故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(310,	'bms_state_bit_3',	6,	'CATL',	'State',	48,	0,	'初始化完成',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(311,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	0,	'内部通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(312,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	1,	'急停按下',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(313,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	2,	'急停故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(314,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	3,	'入门故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(315,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	4,	'逃生门故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(316,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	5,	'主负荷开关故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(317,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	6,	'防雷器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(318,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	7,	'排风扇故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(319,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	8,	'火灾故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(320,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	9,	'UPS故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(321,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	10,	'EMS心跳故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(322,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	11,	'启动高压RACK数故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(323,	'bms_fault_bit_0',	6,	'CATL',	'Fault',	0,	12,	'主电源丢失故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(324,	'bms_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	0,	'单体过压一级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(325,	'bms_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	1,	'单体欠压一级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(326,	'bms_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	2,	'单体过温一级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(327,	'bms_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	3,	'单体低温一级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(328,	'bms_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	5,	'绝缘检测一级',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(329,	'bms_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	0,	'单体过压二级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(330,	'bms_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	1,	'单体欠压二级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(331,	'bms_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	2,	'单体过温二级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(332,	'bms_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	3,	'单体低温二级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(333,	'bms_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	5,	'绝缘检测二级',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(334,	'bms_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	0,	'单体过压三级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(335,	'bms_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	1,	'单体欠压三级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(336,	'bms_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	2,	'单体过温三级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(337,	'bms_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	3,	'单体低温三级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(338,	'bms_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	5,	'绝缘检测三级',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(339,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	0,	'过流告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(340,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	1,	'单体电压差异较大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(341,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	2,	'单体温度差异较大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(342,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	3,	'系统过压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(343,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	4,	'系统低压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(344,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	6,	'单体极限温度告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(345,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	7,	'单体极限电压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(346,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	8,	'消防设备故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(347,	'bms_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	12,	'主电源丢失告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(348,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	0,	'预充电继电器闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(349,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	1,	'主正继电器闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(350,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	2,	'主负继电器闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(351,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	3,	'风扇运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(352,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	4,	'平衡运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(353,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	5,	'低功率状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(354,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	6,	'TMS关机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(355,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	7,	'TMS冷却模式',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(356,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	8,	'TMS加热模式',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(357,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	9,	'TMS自循环模式',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(358,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	10,	'柜门开启',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(359,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	11,	'气溶胶状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(360,	'bms_cluster_{i}_state_bit_0',	6,	'CATL',	'State',	0,	12,	'RACK开关',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(361,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	4,	'单体极限温度故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(362,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	5,	'单体极限电压故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(363,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	7,	'正极主接触器闭合故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(364,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	8,	'负极主接触器闭合故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(365,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	9,	'正极主接触器卡死故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(366,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	10,	'负极主接触器闭合故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(367,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	11,	'辅助电源故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(368,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	13,	'MCU自检故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(369,	'bms_cluster_{i}_fault_bit_0',	6,	'CATL',	'Fault',	0,	15,	'内部通信故障(CCAN)',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(370,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	0,	'内部通信故障(SCAN)',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(371,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	2,	'电流检测异常故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(372,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	3,	'单体电压检测异常故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(373,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	4,	'模组电压检测异常故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(374,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	5,	'高压开路故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(375,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	6,	'MSD故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(376,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	7,	'Rack绝缘开关故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(377,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	8,	'Rack熔丝故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(378,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	9,	'预充电超时',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(379,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	10,	'极限过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(380,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	14,	'CSC电源故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(381,	'bms_cluster_{i}_fault_bit_1',	6,	'CATL',	'Fault',	16,	15,	'电流传感器电源故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(382,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	0,	'TMS通信故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(383,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	1,	'TMS模式冲突',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(384,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	2,	'火警一级',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(385,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	3,	'火警二级',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(386,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	4,	'温度传感器状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(387,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	5,	'烟感状态',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(388,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	7,	'气溶胶关闭',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(389,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	8,	'气溶胶开启',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(390,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	10,	'控制器过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(391,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	11,	'TMS导致BMS停机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(392,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	12,	'风扇1故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(393,	'bms_cluster_{i}_fault_bit_2',	6,	'CATL',	'Fault',	32,	13,	'风扇2故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(394,	'bms_cluster_{i}_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	0,	'单体过压一级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(395,	'bms_cluster_{i}_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	1,	'单体欠压一级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(396,	'bms_cluster_{i}_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	2,	'单体过温一级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(397,	'bms_cluster_{i}_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	3,	'单体低温一级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(398,	'bms_cluster_{i}_alarm_bit_0',	6,	'CATL',	'Alarm',	0,	5,	'TMS一级故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(399,	'bms_cluster_{i}_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	0,	'单体过压二级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(400,	'bms_cluster_{i}_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	1,	'单体欠压二级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(401,	'bms_cluster_{i}_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	2,	'单体过温二级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(402,	'bms_cluster_{i}_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	3,	'单体低温二级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(403,	'bms_cluster_{i}_alarm_bit_1',	6,	'CATL',	'Alarm',	16,	5,	'TMS二级故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(404,	'bms_cluster_{i}_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	0,	'单体过压三级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(405,	'bms_cluster_{i}_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	1,	'单体欠压三级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(406,	'bms_cluster_{i}_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	2,	'单体过温三级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(407,	'bms_cluster_{i}_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	3,	'单体低温三级告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(408,	'bms_cluster_{i}_alarm_bit_2',	6,	'CATL',	'Alarm',	32,	5,	'TMS三级故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(409,	'bms_cluster_{i}_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	0,	'充电过流告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(410,	'bms_cluster_{i}_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	1,	'放电过滤告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(411,	'bms_cluster_{i}_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	2,	'单体电压差异告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(412,	'bms_cluster_{i}_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	3,	'单体温度差异告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(413,	'bms_cluster_{i}_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	6,	'RACK总压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(414,	'bms_cluster_{i}_alarm_bit_3',	6,	'CATL',	'Alarm',	48,	12,	'平衡告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(415,	'bms_cluster_{i}_alarm_bit_4',	6,	'CATL',	'Alarm',	64,	11,	'接触器线圈异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(416,	'bms_cluster_{i}_alarm_bit_4',	6,	'CATL',	'Alarm',	64,	12,	'采样电压与总压异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(417,	'bms_cluster_{i}_alarm_bit_4',	6,	'CATL',	'Alarm',	64,	13,	'MCAN通信告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(418,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	0,	'禁充',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(419,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	1,	'禁放',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(420,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	2,	'告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(421,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	3,	'故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(422,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	4,	'正常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(423,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	5,	'预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(424,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	6,	'接触器：BMS自动控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(425,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	7,	'接触器：EMS强制闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(426,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	8,	'接触器：EMS强制断开',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(427,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	9,	'汇流开关：BMS自动控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(428,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	10,	'汇流开关：EMS强制闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(429,	'bms_state_bit_0',	7,	'Kgooer',	'State',	0,	11,	'汇流开关：EMS强制断开',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(430,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	0,	'电池簇1投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(431,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	1,	'电池簇2投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(432,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	2,	'电池簇3投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(433,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	3,	'电池簇4投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(434,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	4,	'电池簇5投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(435,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	5,	'电池簇6投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(436,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	6,	'电池簇7投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(437,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	7,	'电池簇8投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(438,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	8,	'电池簇9投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(439,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	9,	'电池簇10投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(440,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	10,	'电池簇11投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(441,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	11,	'电池簇12投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(442,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	12,	'电池簇13投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(443,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	13,	'电池簇14投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(444,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	14,	'电池簇15投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(445,	'bms_state_bit_1',	7,	'Kgooer',	'State',	16,	15,	'电池簇16投入运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(446,	'bms_state_bit_2',	7,	'Kgooer',	'State',	32,	0,	'BAMS开关量输入-DI1闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(447,	'bms_state_bit_2',	7,	'Kgooer',	'State',	32,	1,	'BAMS开关量输入-DI2闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(448,	'bms_state_bit_2',	7,	'Kgooer',	'State',	32,	2,	'BAMS开关量输入-DI3闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(449,	'bms_state_bit_2',	7,	'Kgooer',	'State',	32,	3,	'BAMS开关量输入-DI4闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(450,	'bms_state_bit_3',	7,	'Kgooer',	'State',	48,	0,	'ems100初始化电池信息完成',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(451,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	0,	'禁冲',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(452,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	1,	'禁放',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(453,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	2,	'告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(454,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	3,	'故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(455,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	4,	'正常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(456,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	5,	'预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(457,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	6,	'主正接触器：BMS自动控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(458,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	7,	'主正接触器：EMS强制闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(459,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	8,	'主正接触器：EMS强制断开',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(460,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	9,	'主负接触器：BMS自动控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(461,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	10,	'主负接触器：EMS强制闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(462,	'bms_cluster_{i}_state_bit_0',	7,	'Kgooer',	'State',	0,	11,	'主负接触器：EMS强制断开',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(463,	'bms_cluster_{i}_state_bit_1',	7,	'Kgooer',	'State',	16,	0,	'开路',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(464,	'bms_cluster_{i}_state_bit_1',	7,	'Kgooer',	'State',	16,	1,	'待机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(465,	'bms_cluster_{i}_state_bit_1',	7,	'Kgooer',	'State',	16,	2,	'充电',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(466,	'bms_cluster_{i}_state_bit_1',	7,	'Kgooer',	'State',	16,	3,	'放电',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(467,	'bms_cluster_{i}_state_bit_2',	7,	'Kgooer',	'State',	32,	0,	'开关量输入-DI1闭合',	1,	'总正直流接触器反馈状态',	NULL,	NULL,	NULL,	NULL),
(468,	'bms_cluster_{i}_state_bit_2',	7,	'Kgooer',	'State',	32,	1,	'开关量输入-DI2闭合',	1,	'总负直流接触器反馈状态',	NULL,	NULL,	NULL,	NULL),
(469,	'bms_cluster_{i}_state_bit_2',	7,	'Kgooer',	'State',	32,	2,	'开关量输入-DI3闭合',	1,	'消防干接点(闭合告警)接组可配',	NULL,	NULL,	NULL,	NULL),
(470,	'bms_cluster_{i}_state_bit_2',	7,	'Kgooer',	'State',	32,	3,	'开关量输入-DI4闭合',	1,	'水浸干接点(闭合告警)接组可配',	NULL,	NULL,	NULL,	NULL),
(471,	'bms_cluster_{i}_state_bit_2',	7,	'Kgooer',	'State',	32,	4,	'开关量输入-DI5闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(472,	'bms_cluster_{i}_state_bit_2',	7,	'Kgooer',	'State',	32,	5,	'开关量输入-DI6闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(473,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	0,	'组端电压上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(474,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	1,	'组端电压下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(475,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	2,	'充电电流上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(476,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	3,	'放电电流上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(477,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	4,	'单体电压上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(478,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	5,	'单体电压下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(479,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	6,	'充电温度上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(480,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	7,	'充电温度下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(481,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	8,	'放电温度上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(482,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	9,	'放电温度下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(483,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	10,	'单体温度上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(484,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	11,	'单体温度下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(485,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	12,	'环境温度上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(486,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	13,	'环境温度下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(487,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	14,	'动力线温度上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(488,	'bms_cluster_{i}_alarm_bit_0',	7,	'Kgooer',	'Alarm',	0,	15,	'组SOC上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(489,	'bms_cluster_{i}_alarm_bit_1',	7,	'Kgooer',	'Alarm',	16,	0,	'组SOC下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(490,	'bms_cluster_{i}_alarm_bit_1',	7,	'Kgooer',	'Alarm',	16,	1,	'正极绝缘电阻下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(491,	'bms_cluster_{i}_alarm_bit_1',	7,	'Kgooer',	'Alarm',	16,	2,	'负极绝缘电阻下限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(492,	'bms_cluster_{i}_alarm_bit_1',	7,	'Kgooer',	'Alarm',	16,	3,	'箱压差上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(493,	'bms_cluster_{i}_alarm_bit_1',	7,	'Kgooer',	'Alarm',	16,	4,	'箱温差上限预警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(494,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	0,	'组端电压上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(495,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	1,	'组端电压下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(496,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	2,	'充电电流上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(497,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	3,	'放电电流上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(498,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	4,	'单体电压上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(499,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	5,	'单体电压下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(500,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	6,	'充电温度上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(501,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	7,	'充电温度下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(502,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	8,	'放电温度上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(503,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	9,	'放电温度下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(504,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	10,	'单体温度上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(505,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	11,	'单体温度下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(506,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	12,	'环境温度上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(507,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	13,	'环境温度下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(508,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	14,	'动力线温度上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(509,	'bms_cluster_{i}_alarm_bit_2',	7,	'Kgooer',	'Alarm',	32,	15,	'组SOC上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(510,	'bms_cluster_{i}_alarm_bit_3',	7,	'Kgooer',	'Alarm',	48,	0,	'组SOC下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(511,	'bms_cluster_{i}_alarm_bit_3',	7,	'Kgooer',	'Alarm',	48,	1,	'正极绝缘电阻下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(512,	'bms_cluster_{i}_alarm_bit_3',	7,	'Kgooer',	'Alarm',	48,	2,	'负极绝缘电阻下限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(513,	'bms_cluster_{i}_alarm_bit_3',	7,	'Kgooer',	'Alarm',	48,	3,	'箱压差上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(514,	'bms_cluster_{i}_alarm_bit_3',	7,	'Kgooer',	'Alarm',	48,	4,	'箱温差上限告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(515,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	0,	'组端电压上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(516,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	1,	'组端电压下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(517,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	2,	'充电电流上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(518,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	3,	'放电电流上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(519,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	4,	'单体电压上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(520,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	5,	'单体电压下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(521,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	6,	'充电温度上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(522,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	7,	'充电温度下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(523,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	8,	'放电温度上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(524,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	9,	'放电温度下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(525,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	10,	'单体温度上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(526,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	11,	'单体温度下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(527,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	12,	'环境温度上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(528,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	13,	'环境温度下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(529,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	14,	'动力线温度上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(530,	'bms_cluster_{i}_fault_bit_0',	7,	'Kgooer',	'Fault',	0,	15,	'组SOC上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(531,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	0,	'组SOC下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(532,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	1,	'正极绝缘电阻下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(533,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	2,	'负极绝缘电阻下限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(534,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	3,	'箱压差上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(535,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	4,	'箱温差上限保护',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(536,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	5,	'电池温度温升过快',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(537,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	6,	'动力线温度温升过快',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(538,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	7,	'BMU通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(539,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	8,	'BCMU通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(540,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	9,	'单体电压采集线故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(541,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	10,	'总压采集线故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(542,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	11,	'电流采集线故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(543,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	12,	'温度采集断线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(544,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	13,	'温度采集短路',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(545,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	14,	'BMS设备故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(546,	'bms_cluster_{i}_fault_bit_1',	7,	'Kgooer',	'Fault',	16,	15,	'单体电压无效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(547,	'bms_cluster_{i}_fault_bit_2',	7,	'Kgooer',	'Fault',	32,	0,	'单体温度无效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(548,	'bms_cluster_{i}_fault_bit_2',	7,	'Kgooer',	'Fault',	32,	1,	'组端电压无效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(549,	'bms_cluster_{i}_fault_bit_2',	7,	'Kgooer',	'Fault',	32,	2,	'组电流异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(550,	'bms_cluster_{i}_fault_bit_2',	7,	'Kgooer',	'Fault',	32,	3,	'消防报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(551,	'bms_cluster_{i}_fault_bit_2',	7,	'Kgooer',	'Fault',	32,	4,	'水浸报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(552,	'bms_cluster_{i}_fault_bit_2',	7,	'Kgooer',	'Fault',	32,	5,	'缓存告警待获取',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(553,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	0,	'停机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(554,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	1,	'待机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(555,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	2,	'放电运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(556,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	3,	'充电运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(557,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	4,	'PCS降额运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(558,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	5,	'交流断路器状态闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(559,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	6,	'交流断路器脱扣器状态闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(560,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	7,	'直流断路器状态闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(561,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	8,	'直流断路器脱扣器状态闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(562,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	9,	'交流缓冲接触器状态闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(563,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	10,	'直流缓冲接触器状态闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(564,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	11,	'绝缘检测接触器状态闭合',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(565,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	12,	'急停按钮状态：急停',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(566,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	13,	'IGBT_A风机状态停转',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(567,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	14,	'IGBT_B风机状态停转',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(568,	'pcs_{i}_state_bit_0',	9,	'为恒1688kWPCS',	'State',	0,	15,	'IGBT_C风机状态停转',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(569,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	0,	'电抗器风机状态停转',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(570,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	1,	'防雷器状态故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(571,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	2,	'熔丝熔断',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(572,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	3,	'水泵运行',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(573,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	4,	'水泵空转',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(574,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	5,	'水泵无调速信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(575,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	6,	'水泵堵转/过流/停机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(576,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	7,	'三相交流风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(577,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	8,	'水冷液压故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(578,	'pcs_{i}_state_bit_1',	9,	'为恒1688kWPCS',	'State',	16,	9,	'交流风机电源断开',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(579,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	0,	'命令来源：HMI',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(580,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	1,	'命令来源：EMS',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(581,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	2,	'命令来源：后台',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(582,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	3,	'运行模式：并网',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(583,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	4,	'运行模式：离网',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(584,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	5,	'运行模式：并离网切换',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(585,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	6,	'工作方式：恒交流功率',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(586,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	7,	'工作方式：恒压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(587,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	8,	'工作方式：恒流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(588,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	9,	'工作方式：三段式充电',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(589,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	10,	'工作方式：恒直流功率',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(590,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	11,	'离网频率选择：50Hz',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(591,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	12,	'离网频率选择：60Hz',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(592,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	13,	'无功功率设定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(593,	'pcs_{i}_state_bit_2',	9,	'为恒1688kWPCS',	'State',	32,	14,	'功率因数设定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(594,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	0,	'功率指令源：通信输入',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(595,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	1,	'功率指令源：AI输入',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(596,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	2,	'电网频率选择：50Hz',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(597,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	3,	'电网频率选择：60Hz',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(598,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	4,	'绝缘阻抗检测开启',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(599,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	5,	'电网频率自适应',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(600,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	6,	'中点平衡控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(601,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	7,	'功率变化速率控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(602,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	8,	'高低压穿越控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(603,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	9,	'滤波电容电流补偿',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(604,	'pcs_{i}_state_bit_3',	9,	'为恒1688kWPCS',	'State',	48,	10,	'主动孤岛检测控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(605,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	0,	'储能电池极性反接故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(606,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	1,	'储能电池电压过高',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(607,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	2,	'直流侧半母线硬件过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(608,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	3,	'交流硬件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(609,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	4,	'IGBT A相故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(610,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	5,	'IGBT B相故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(611,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	6,	'IGBT C相故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(612,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	7,	'交流侧A相霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(613,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	8,	'交流侧A相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(614,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	9,	'交流侧A相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(615,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	10,	'交流侧A相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(616,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	11,	'交流侧B相电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(617,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	12,	'交流侧B相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(618,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	13,	'交流侧B相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(619,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	14,	'交流侧B相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(620,	'pcs_{i}_fault_bit_0',	9,	'为恒1688kWPCS',	'Fault',	0,	15,	'交流侧C相电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(621,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	0,	'交流侧C相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(622,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	1,	'交流侧C相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(623,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	2,	'交流侧C相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(624,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	3,	'直流侧电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(625,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	4,	'直流侧霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(626,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	5,	'直流侧霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(627,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	6,	'直流侧霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(628,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	7,	'直流熔断器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(629,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	8,	'直流侧脱扣器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(630,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	9,	'直流开关故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(631,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	10,	'IGBT风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(632,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	11,	'电抗器风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(633,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	12,	'直流侧全母线软件过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(634,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	13,	'直流侧全母线软件欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(635,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	14,	'半母线电压偏差过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(636,	'pcs_{i}_fault_bit_1',	9,	'为恒1688kWPCS',	'Fault',	16,	15,	'直流侧软件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(637,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	0,	'电网交流过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(638,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	1,	'电网交流欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(639,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	2,	'离网交流过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(640,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	3,	'离网交流欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(641,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	4,	'电网交流过频',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(642,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	5,	'电网交流欠频',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(643,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	6,	'交流软件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(644,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	7,	'输出缺相故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(645,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	8,	'防雷器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(646,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	9,	'孤岛故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(647,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	10,	'电流控制偏差过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(648,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	11,	'电网电压不平衡',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(649,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	12,	'交流侧电流直流分量超限',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(650,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	13,	'环境过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(651,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	14,	'环境低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(652,	'pcs_{i}_fault_bit_2',	9,	'为恒1688kWPCS',	'Fault',	32,	15,	'IGBT模块A相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(653,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	0,	'IGBT模块B相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(654,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	1,	'IGBT模块C相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(655,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	2,	'IGBT模块A相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(656,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	3,	'IGBT模块B相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(657,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	4,	'IGBT模块C相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(658,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	5,	'交流开关合闸电压不匹配',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(659,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	6,	'电抗器过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(660,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	7,	'AD零漂过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(661,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	8,	'绝缘电阻偏低',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(662,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	9,	'绝缘检测失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(663,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	10,	'HMI通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(664,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	11,	'BMS通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(665,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	12,	'EMS连接超时 1（外部总线）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(666,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	13,	'并机通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(667,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	14,	'I2C总线故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(668,	'pcs_{i}_fault_bit_3',	9,	'为恒1688kWPCS',	'Fault',	48,	15,	'SPI故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(669,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	0,	'物联网通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(670,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	1,	'客户后台通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(671,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	2,	'直流侧预充接触器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(672,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	3,	'交流侧预充接触器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(673,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	4,	'CBC过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(674,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	5,	'设备过载',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(675,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	6,	'电网电压反序',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(676,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	7,	'母线软启动失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(677,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	8,	'交流侧脱扣器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(678,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	9,	'交流侧开关故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(679,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	10,	'载波同步故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(680,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	11,	'EMS连接超时 1（内部通讯）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(681,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	12,	'EMS连接超时 2（外部总线）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(682,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	13,	'EMS连接超时 2（内部通讯）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(683,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	14,	'EMS通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(684,	'pcs_{i}_fault_bit_4',	9,	'为恒1688kWPCS',	'Fault',	64,	15,	'产品版本号不匹配',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(685,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	0,	'机型码不匹配',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(686,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	1,	'三相风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(687,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	2,	'水冷液压故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(688,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	3,	'水泵堵转/过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(689,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	4,	'水泵无调速输入',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(690,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	5,	'水泵空转',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(691,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	6,	'板上电池电压故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(692,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	7,	'辅助电源相序错误',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(693,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	8,	'柜门故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(694,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	9,	'内腔过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(695,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	10,	'内腔低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(696,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	11,	'滤波电容过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(697,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	12,	'滤波电容低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(698,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	13,	'辅助变压器过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(699,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	14,	'辅助变压器低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(700,	'pcs_{i}_fault_bit_5',	9,	'为恒1688kWPCS',	'Fault',	80,	15,	'电池电压过高',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(701,	'pcs_{i}_fault_bit_6',	9,	'为恒1688kWPCS',	'Fault',	96,	0,	'电池电压过低',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(702,	'pcs_{i}_fault_bit_6',	9,	'为恒1688kWPCS',	'Fault',	96,	1,	'电抗器低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(703,	'pcs_{i}_fault_bit_6',	9,	'为恒1688kWPCS',	'Fault',	96,	2,	'黑匣子存储故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(704,	'pcs_{i}_fault_bit_6',	9,	'为恒1688kWPCS',	'Fault',	96,	3,	'220V掉电故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(705,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	0,	'储能电池极性反接告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(706,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	1,	'储能电池电压过高',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(707,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	2,	'直流侧半母线硬件过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(708,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	3,	'交流硬件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(709,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	4,	'IGBT A相告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(710,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	5,	'IGBT B相告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(711,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	6,	'IGBT C相告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(712,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	7,	'交流侧A相霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(713,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	8,	'交流侧A相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(714,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	9,	'交流侧A相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(715,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	10,	'交流侧A相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(716,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	11,	'交流侧B相电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(717,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	12,	'交流侧B相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(718,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	13,	'交流侧B相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(719,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	14,	'交流侧B相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(720,	'pcs_{i}_alarm_bit_0',	9,	'为恒1688kWPCS',	'Alarm',	0,	15,	'交流侧C相电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(721,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	0,	'交流侧C相霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(722,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	1,	'交流侧C相霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(723,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	2,	'交流侧C相霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(724,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	3,	'直流侧电流霍尔断线（+15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(725,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	4,	'直流侧霍尔断线（-15V）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(726,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	5,	'直流侧霍尔断线（IR）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(727,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	6,	'直流侧霍尔断线（GND）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(728,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	7,	'直流熔断器告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(729,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	8,	'直流侧脱扣器告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(730,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	9,	'直流开关告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(731,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	10,	'IGBT风机告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(732,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	11,	'电抗器风机告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(733,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	12,	'直流侧全母线软件过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(734,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	13,	'直流侧全母线软件欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(735,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	14,	'半母线电压偏差过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(736,	'pcs_{i}_alarm_bit_1',	9,	'为恒1688kWPCS',	'Alarm',	16,	15,	'直流侧软件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(737,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	0,	'电网交流过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(738,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	1,	'电网交流欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(739,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	2,	'离网交流过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(740,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	3,	'离网交流欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(741,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	4,	'电网交流过频',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(742,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	5,	'电网交流欠频',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(743,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	6,	'交流软件过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(744,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	7,	'输出缺相告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(745,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	8,	'防雷器告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(746,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	9,	'孤岛告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(747,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	10,	'电流控制偏差过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(748,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	11,	'电网电压不平衡',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(749,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	12,	'交流侧电流直流分量超限',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(750,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	13,	'环境过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(751,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	14,	'环境低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(752,	'pcs_{i}_alarm_bit_2',	9,	'为恒1688kWPCS',	'Alarm',	32,	15,	'IGBT模块A相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(753,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	0,	'IGBT模块B相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(754,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	1,	'IGBT模块C相过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(755,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	2,	'IGBT模块A相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(756,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	3,	'IGBT模块B相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(757,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	4,	'IGBT模块C相低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(758,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	5,	'交流开关合闸电压不匹配',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(759,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	6,	'电抗器过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(760,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	7,	'AD零漂过大',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(761,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	8,	'绝缘电阻偏低',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(762,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	9,	'绝缘检测失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(763,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	10,	'HMI通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(764,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	11,	'BMS通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(765,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	12,	'EMS连接超时 1（外部总线）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(766,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	13,	'并机通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(767,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	14,	'I2C总线故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(768,	'pcs_{i}_alarm_bit_3',	9,	'为恒1688kWPCS',	'Alarm',	48,	15,	'SPI故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(769,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	0,	'物联网通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(770,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	1,	'客户后台通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(771,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	2,	'直流侧预充接触器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(772,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	3,	'交流侧预充接触器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(773,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	4,	'CBC过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(774,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	5,	'设备过载',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(775,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	6,	'电网电压反序',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(776,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	7,	'母线软启动失败',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(777,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	8,	'交流侧脱扣器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(778,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	9,	'交流侧开关故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(779,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	10,	'载波同步故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(780,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	11,	'EMS连接超时 1（内部通讯）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(781,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	12,	'EMS连接超时 2（外部总线）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(782,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	13,	'EMS连接超时 2（内部通讯）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(783,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	14,	'EMS通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(784,	'pcs_{i}_alarm_bit_4',	9,	'为恒1688kWPCS',	'Alarm',	64,	15,	'产品版本号不匹配',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(785,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	0,	'机型码不匹配',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(786,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	1,	'三相风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(787,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	2,	'水冷液压故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(788,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	3,	'水泵堵转/过流',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(789,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	4,	'水泵无调速输入',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(790,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	5,	'水泵空转',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(791,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	6,	'板上电池电压故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(792,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	7,	'辅助电源相序错误',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(793,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	8,	'柜门故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(794,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	9,	'内腔过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(795,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	10,	'内腔低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(796,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	11,	'滤波电容过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(797,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	12,	'滤波电容低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(798,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	13,	'辅助变压器过温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(799,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	14,	'辅助变压器低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(800,	'pcs_{i}_alarm_bit_5',	9,	'为恒1688kWPCS',	'Alarm',	80,	15,	'电池电压过高',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(801,	'pcs_{i}_alarm_bit_6',	9,	'为恒1688kWPCS',	'Alarm',	96,	0,	'电池电压过低',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(802,	'pcs_{i}_alarm_bit_6',	9,	'为恒1688kWPCS',	'Alarm',	96,	1,	'电抗器低温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(803,	'pcs_{i}_alarm_bit_6',	9,	'为恒1688kWPCS',	'Alarm',	96,	2,	'黑匣子存储故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(804,	'pcs_{i}_alarm_bit_6',	9,	'为恒1688kWPCS',	'Alarm',	96,	3,	'220V掉电故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(805,	'firefighting_{i}_fault_bit_0',	10,	'CW131099A',	'Fault',	0,	0,	'报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(806,	'firefighting_{i}_fault_bit_0',	10,	'CW131099A',	'Fault',	0,	1,	'故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(807,	'firefighting_{i}_fault_bit_0',	10,	'CW131099A',	'Fault',	0,	2,	'延时',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(808,	'firefighting_{i}_fault_bit_0',	10,	'CW131099A',	'Fault',	0,	3,	'启动喷洒',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(809,	'firefighting_{i}_fault_bit_0',	10,	'CW131099A',	'Fault',	0,	4,	'喷洒',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(810,	'firefighting_{i}_fault_bit_0',	10,	'CW131099A',	'Fault',	0,	5,	'声光报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(811,	'firefighting_{i}_fault_bit_0',	10,	'CW131099A',	'Fault',	0,	6,	'放气勿入',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(812,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	0,	'消音',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(813,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	1,	'自检',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(814,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	2,	'主电',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(815,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	3,	'备电',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(816,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	4,	'备电欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(817,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	5,	'自动模式',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(818,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	6,	'手动模式',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(819,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	7,	'手动启动',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(820,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	8,	'手动急停',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(821,	'firefighting_{i}_state_bit_0',	10,	'CW131099A',	'State',	0,	9,	'启动控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(822,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	0,	'报警动作',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(823,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	1,	'2级接点信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(824,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	2,	'喷洒动作',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(825,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	3,	'1级接点信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(826,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	5,	'故障输出信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(827,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	7,	'进/排风格栅驱动信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(828,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	8,	'紧急启动',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(829,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	9,	'紧急停止',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(830,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	10,	'自动模式',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(831,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	11,	'外加手报（开关信号）',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(832,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	12,	'灭火器压力反馈信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(833,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	13,	'外部烟感动作',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(834,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	14,	'外部温感动作',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(835,	'firefighting_{i}_state_bit_0',	8,	'NQN214',	'State',	0,	15,	'主电',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(836,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	0,	'消音',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(837,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	1,	'自检',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(838,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	2,	'手动模式',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(839,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	3,	'备电',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(840,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	4,	'一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(841,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	5,	'二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(842,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	8,	'进/排风格栅驱动信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(843,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	10,	'进（排）风机信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(844,	'firefighting_{i}_state_bit_1',	8,	'NQN214',	'State',	16,	15,	'启动灭火信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(845,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	0,	'探测器1号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(846,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	1,	'探测器2号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(847,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	2,	'探测器3号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(848,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	3,	'探测器4号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(849,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	4,	'探测器5号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(850,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	5,	'探测器6号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(851,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	6,	'探测器7号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(852,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	7,	'探测器8号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(853,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	8,	'探测器9号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(854,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	9,	'探测器10号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(855,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	10,	'探测器11号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(856,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	11,	'探测器12号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(857,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	12,	'探测器13号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(858,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	13,	'探测器14号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(859,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	14,	'探测器15号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(860,	'firefighting_{i}_fault_bit_0',	8,	'NQN214',	'Fault',	0,	15,	'探测器16号掉线',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(861,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	0,	'探测器1号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(862,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	1,	'探测器2号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(863,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	2,	'探测器3号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(864,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	3,	'探测器4号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(865,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	4,	'探测器5号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(866,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	5,	'探测器6号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(867,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	6,	'探测器7号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(868,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	7,	'探测器8号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(869,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	8,	'探测器9号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(870,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	9,	'探测器10号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(871,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	10,	'探测器11号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(872,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	11,	'探测器12号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(873,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	12,	'探测器13号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(874,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	13,	'探测器14号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(875,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	14,	'探测器15号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(876,	'firefighting_{i}_fault_bit_1',	8,	'NQN214',	'Fault',	16,	15,	'探测器16号一级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(877,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	0,	'探测器1号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(878,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	1,	'探测器2号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(879,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	2,	'探测器3号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(880,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	3,	'探测器4号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(881,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	4,	'探测器5号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(882,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	5,	'探测器6号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(883,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	6,	'探测器7号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(884,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	7,	'探测器8号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(885,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	8,	'探测器9号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(886,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	9,	'探测器10号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(887,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	10,	'探测器11号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(888,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	11,	'探测器12号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(889,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	12,	'探测器13号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(890,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	13,	'探测器14号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(891,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	14,	'探测器15号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(892,	'firefighting_{i}_fault_bit_2',	8,	'NQN214',	'Fault',	32,	15,	'探测器16号二级报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(893,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	0,	'探测器1号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(894,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	1,	'探测器2号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(895,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	2,	'探测器3号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(896,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	3,	'探测器4号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(897,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	4,	'探测器5号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(898,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	5,	'探测器6号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(899,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	6,	'探测器7号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(900,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	7,	'探测器8号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(901,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	8,	'探测器9号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(902,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	9,	'探测器10号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(903,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	10,	'探测器11号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(904,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	11,	'探测器12号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(905,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	12,	'探测器13号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(906,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	13,	'探测器14号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(907,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	14,	'探测器15号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(908,	'firefighting_{i}_fault_bit_3',	8,	'NQN214',	'Fault',	48,	15,	'探测器16号排风信号',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(909,	'air_conditioner_{i}_state_bit_0',	12,	'Envicool MC75',	'State',	0,	0,	'开机指令',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(910,	'air_conditioner_{i}_state_bit_0',	12,	'Envicool MC75',	'State',	0,	1,	'系统待机',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(911,	'air_conditioner_{i}_state_bit_0',	12,	'Envicool MC75',	'State',	0,	2,	'系统运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(912,	'air_conditioner_{i}_state_bit_0',	12,	'Envicool MC75',	'State',	0,	3,	'电加热运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(913,	'air_conditioner_{i}_state_bit_0',	12,	'Envicool MC75',	'State',	0,	4,	'柜内风扇运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(914,	'air_conditioner_{i}_state_bit_0',	12,	'Envicool MC75',	'State',	0,	5,	'柜外风扇运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(915,	'air_conditioner_{i}_state_bit_0',	12,	'Envicool MC75',	'State',	0,	6,	'压缩机运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(916,	'air_conditioner_{i}_state_bit_0',	12,	'Envicool MC75',	'State',	0,	7,	'水泵运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(917,	'air_conditioner_{i}_fault_bit_0',	12,	'Envicool MC75',	'Fault',	0,	0,	'柜内风扇故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(918,	'air_conditioner_{i}_fault_bit_0',	12,	'Envicool MC75',	'Fault',	0,	1,	'柜外风扇故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(919,	'air_conditioner_{i}_fault_bit_0',	12,	'Envicool MC75',	'Fault',	0,	2,	'压缩机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(920,	'air_conditioner_{i}_fault_bit_0',	12,	'Envicool MC75',	'Fault',	0,	3,	'水泵故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(921,	'air_conditioner_{i}_fault_bit_0',	12,	'Envicool MC75',	'Fault',	0,	4,	'系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(922,	'air_conditioner_{i}_fault_bit_0',	12,	'Envicool MC75',	'Fault',	0,	5,	'电加热故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(923,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	0,	'回风高温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(924,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	1,	'内风机故障告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(925,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	2,	'外风机故障告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(926,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	3,	'压缩机故障告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(927,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	4,	'柜内回风温度温度探头故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(928,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	5,	'系统高压力告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(929,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	6,	'回风低温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(930,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	7,	'交流过压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(931,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	8,	'交流欠压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(932,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	9,	'交流掉电告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(933,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	10,	'蒸发器温度传感器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(934,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	11,	'冷凝器温度传感器故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(935,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	12,	'蒸发器冻结报警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(936,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	13,	'频繁高压力告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(937,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	14,	'系统低压力告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(938,	'air_conditioner_{i}_fault_bit_1',	12,	'Envicool MC75',	'Fault',	16,	15,	'频繁低压力告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(939,	'air_conditioner_{i}_state_bit_0',	11,	'Envicool MC30',	'State',	0,	0,	'开机指令',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(940,	'air_conditioner_{i}_state_bit_0',	11,	'Envicool MC30',	'State',	0,	1,	'系统运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(941,	'air_conditioner_{i}_state_bit_0',	11,	'Envicool MC30',	'State',	0,	2,	'应急风机运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(942,	'air_conditioner_{i}_state_bit_0',	11,	'Envicool MC30',	'State',	0,	3,	'电加热运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(943,	'air_conditioner_{i}_state_bit_0',	11,	'Envicool MC30',	'State',	0,	4,	'柜内风扇运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(944,	'air_conditioner_{i}_state_bit_0',	11,	'Envicool MC30',	'State',	0,	5,	'柜外风扇运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(945,	'air_conditioner_{i}_state_bit_0',	11,	'Envicool MC30',	'State',	0,	6,	'压缩机运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(946,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	0,	'高温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(947,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	1,	'低温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(948,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	2,	'高湿告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(949,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	3,	'低湿告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(950,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	4,	'盘管防冻',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(951,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	5,	'排气高温',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(952,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	6,	'盘管温感失效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(953,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	7,	'室外温感失效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(954,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	8,	'冷凝温感失效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(955,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	9,	'内温感失效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(956,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	10,	'排气温感失效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(957,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	11,	'湿感失效',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(958,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	12,	'内风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(959,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	13,	'外风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(960,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	14,	'压缩机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(961,	'air_conditioner_{i}_fault_bit_1',	11,	'Envicool MC30',	'Fault',	16,	15,	'电加热故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(962,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	0,	'应急风机故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(963,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	1,	'高压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(964,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	2,	'低压告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(965,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	3,	'水浸告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(966,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	4,	'烟感告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(967,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	5,	'门禁告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(968,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	6,	'高压锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(969,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	7,	'低压锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(970,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	8,	'排气锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(971,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	9,	'交流过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(972,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	10,	'交流欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(973,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	11,	'交流掉电',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(974,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	12,	'缺相',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(975,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	13,	'频率异常',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(976,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	14,	'逆相',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(977,	'air_conditioner_{i}_fault_bit_2',	11,	'Envicool MC30',	'Fault',	32,	15,	'直流过压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(978,	'air_conditioner_{i}_fault_bit_3',	11,	'Envicool MC30',	'Fault',	48,	0,	'直流欠压',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(979,	'water_cooler_{i}_state_bit_0',	13,	'Envicool EMW150',	'State',	0,	0,	'开机指令',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(980,	'water_cooler_{i}_state_bit_0',	13,	'Envicool EMW150',	'State',	0,	1,	'水泵运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(981,	'water_cooler_{i}_state_bit_0',	13,	'Envicool EMW150',	'State',	0,	2,	'1号压缩机运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(982,	'water_cooler_{i}_state_bit_0',	13,	'Envicool EMW150',	'State',	0,	3,	'2号压缩机运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(983,	'water_cooler_{i}_state_bit_0',	13,	'Envicool EMW150',	'State',	0,	4,	'电加热运行中',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(984,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	0,	'1号系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(985,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	1,	'1号系统告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(986,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	2,	'2号系统故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(987,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	3,	'2号系统告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(988,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	4,	'1号冷凝压力过高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(989,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	5,	'2号冷凝压力过高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(990,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	6,	'系统缺水告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(991,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	7,	'出水压力过高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(992,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	8,	'进水压力过低告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(993,	'water_cooler_{i}_fault_bit_0',	13,	'Envicool EMW150',	'Fault',	0,	9,	'电源故障告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(994,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	0,	'停止',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(995,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	1,	'内循环',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(996,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	2,	'制冷',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(997,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	3,	'加热',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(998,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	4,	'全自动',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(999,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	5,	'电芯温度控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1000,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	6,	'出水温度控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1001,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	7,	'回水温度控制',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1002,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	8,	'水泵关闭',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1003,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	9,	'水泵开启',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1004,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	10,	'压缩机关闭',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1005,	'water_cooler_{i}_state_bit_0',	14,	'Envicool EMW100',	'State',	0,	11,	'压缩机开启',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1006,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	0,	'严重告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1007,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	1,	'中等告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1008,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	2,	'一般告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1009,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	3,	'出水高温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1010,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	4,	'出水低温告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1011,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	5,	'出水温感故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1012,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	6,	'回水温感故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1013,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	7,	'变频器通讯故障',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1014,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	8,	'系统高压锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1015,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	9,	'系统低压锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1016,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	10,	'排气温度过高锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1017,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	11,	'变频器过流锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1018,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	12,	'变频器过温锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1019,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	13,	'变频器过压锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1020,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	14,	'变频器欠压锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1021,	'water_cooler_{i}_fault_bit_0',	14,	'Envicool EMW100',	'Fault',	0,	15,	'变频器缺相锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1022,	'water_cooler_{i}_fault_bit_1',	14,	'Envicool EMW100',	'Fault',	1,	0,	'变频器其他故障锁定',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1023,	'water_cooler_{i}_fault_bit_1',	14,	'Envicool EMW100',	'Fault',	1,	1,	'补水告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1024,	'water_cooler_{i}_fault_bit_1',	14,	'Envicool EMW100',	'Fault',	1,	2,	'系统压力过高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL),
(1025,	'water_cooler_{i}_fault_bit_1',	14,	'Envicool EMW100',	'Fault',	1,	3,	'出水压力过高告警',	1,	NULL,	NULL,	NULL,	NULL,	NULL);

DROP TABLE IF EXISTS `t_event_message`;
CREATE TABLE `t_event_message` (
                                   `id` int(32) NOT NULL AUTO_INCREMENT,
                                   `device_id` varchar(128) CHARACTER SET utf8mb4 NOT NULL COMMENT '设备id',
                                   `equip_type` varchar(255) DEFAULT NULL COMMENT '装备类型（汇川630kWPCS、派能高压电池箱）',
                                   `equip_name` varchar(255) DEFAULT NULL COMMENT '装备名称（pcs1，bms1）',
                                   `event_type` varchar(15) DEFAULT NULL COMMENT '事件类型（state、alarm、fault）',
                                   `event_code` int(11) DEFAULT NULL COMMENT '事件编码（事件bit偏移量+bit地址）',
                                   `status` int(1) DEFAULT '1' COMMENT '状态（1已确认，0未确认）',
                                   `event_description` varchar(255) DEFAULT NULL COMMENT '事件描述（某某事件触发、某某事件消失）',
                                   `event_on_off` varchar(10) DEFAULT NULL COMMENT '事件触发、消失描述',
                                   `project_id` varchar(128) DEFAULT NULL,
                                   `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                   `create_by` varchar(32) DEFAULT NULL COMMENT '创建者',
                                   `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                   `update_by` varchar(32) DEFAULT NULL COMMENT '更新者',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='事件表';


DROP TABLE IF EXISTS `t_group`;
CREATE TABLE `t_group` (
                           `id` varchar(100) NOT NULL COMMENT '分组标识uuid',
                           `name` varchar(128) DEFAULT NULL COMMENT '分组名称',
                           `external_controller` tinyint(1) DEFAULT NULL COMMENT '外部控制器（0关闭）（1打不开）',
                           `calc_earnings_controller` tinyint(1) DEFAULT NULL COMMENT '计算收益开关(0关闭)(1打开)',
                           `demand_controller` tinyint(1) DEFAULT NULL COMMENT '控制需量开关(0关闭)(1打开)',
                           `demand_controller_model` int(1) DEFAULT NULL COMMENT '需量控制模式 1负载开环控制模式 2电网闭环控制模式 3混合控制模式',
                           `photovoltaic_controller` tinyint(1) DEFAULT NULL COMMENT 'PV(光伏)模式开关(false关闭)(true打开）',
                           `photovoltaic_model` int(11) DEFAULT NULL COMMENT '光伏发电模式 1全额上网 2自发自用余量上网',
                           `whether_system` tinyint(1) DEFAULT '0' COMMENT '是否系统开关(true 是)(false否)',
                           `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
                           `priority` int(4) DEFAULT NULL COMMENT '优先级',
                           `enable_stop_soc` tinyint(1) DEFAULT '0' COMMENT '是否设置soc界限(true 是)(false否)',
                           `create_by` varchar(32) DEFAULT NULL COMMENT '创建者',
                           `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                           `update_by` varchar(32) DEFAULT NULL COMMENT '更新者',
                           `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                           `anti_reflux` tinyint(1) DEFAULT '1' COMMENT '是否防逆流',
                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分组';

INSERT INTO `t_group` (`id`, `name`, `external_controller`, `calc_earnings_controller`, `demand_controller`, `demand_controller_model`, `photovoltaic_controller`, `photovoltaic_model`, `whether_system`, `project_id`, `priority`, `enable_stop_soc`, `create_by`, `create_time`, `update_by`, `update_time`, `anti_reflux`) VALUES
('3e7fbde8830a440392f1b3cb17b52380',	'系统分组',	0,	1,	1,	3,	1,	2,	1,	'4f537620d37d40e19dd25be5ca6ad941',	4,	0,	'118',	2022,	'whadmin',	1660543117,	1);

DROP TABLE IF EXISTS `t_group_ammeter`;
CREATE TABLE `t_group_ammeter` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关系id',
                                   `groupId` varchar(128) NOT NULL COMMENT '分组id',
                                   `ammeterId` varchar(128) NOT NULL COMMENT '电表id',
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `t_group_ammeter_pk` (`groupId`,`ammeterId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分组与电表关联表';


DROP TABLE IF EXISTS `t_group_device`;
CREATE TABLE `t_group_device` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `groupId` varchar(128) NOT NULL COMMENT '分组id',
                                  `deviceId` varchar(128) NOT NULL COMMENT '设备id',
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `t_group_device_pk` (`groupId`,`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分组和设备关联表';


DROP TABLE IF EXISTS `t_log`;
CREATE TABLE `t_log` (
                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                         `module` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模块',
                         `method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '方法',
                         `ip` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求IP',
                         `request_time` bigint(20) DEFAULT NULL COMMENT '请求时间',
                         `user_id` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '操作人',
                         `cost_time` bigint(20) DEFAULT NULL COMMENT '耗时',
                         `result` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结果（success/failure）',
                         `project_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `t_point_data`;
CREATE TABLE `t_point_data` (
                                `point_id` int(11) NOT NULL AUTO_INCREMENT,
                                `point_offset` tinytext COMMENT '偏移量',
                                `point_address` int(11) DEFAULT NULL COMMENT '地址',
                                `point_name` tinytext COMMENT '点位名',
                                `point_column` tinytext COMMENT '字段名',
                                `point_mul` int(11) DEFAULT NULL COMMENT '数值倍率',
                                `point_type` varchar(10) NOT NULL COMMENT '数据类型',
                                `point_level` varchar(5) DEFAULT NULL COMMENT '采样等级',
                                `point_len` tinytext COMMENT '单个采样长度',
                                `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                `create_by` varchar(32) DEFAULT NULL COMMENT '创建者',
                                `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                `update_by` varchar(32) DEFAULT NULL COMMENT '更新者',
                                PRIMARY KEY (`point_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='点位数据对象';

INSERT INTO `t_point_data` (`point_id`, `point_offset`, `point_address`, `point_name`, `point_column`, `point_mul`, `point_type`, `point_level`, `point_len`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
(1,	'0',	0,	'系统类型码',	'system_type_code',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(2,	'0',	1,	'PCS数量(PCS_COUNT <= 5)',	'pcs_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(3,	'0',	2,	'PCS类型码',	'pcs_type_code',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(4,	'0',	3,	'BMS数量(BMS_COUNT <= 1)',	'bms_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(5,	'0',	4,	'BMS类型码',	'bms_type_code',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(6,	'0',	5,	'空调数量(AIR_COUNT <= 4)',	'air_conditioner_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(7,	'0',	6,	'空调类型码',	'air_conditioner_type_code',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(8,	'0',	7,	'消防设备数量(FIRE_COUNT <= 4)',	'firefighting_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(9,	'0',	8,	'消防类型码',	'firefighting_type_code',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(10,	'0',	9,	'PV电表数量( <= 1)',	'pv_meter_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(11,	'0',	10,	'并网点电表数量( <= 1)',	'grid_meter_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(12,	'0',	11,	'负载电表数量(<=1)',	'load_meter_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(13,	'0',	12,	'EMS电表数量(<=1)，仅在>=1时73 75有效',	'ems_meter_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(14,	'0',	13,	'系统开关状态',	'system_run_status',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(15,	'0',	14,	'水冷机数量(WATER_COUNT <= 4)',	'water_cooler_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(16,	'0',	15,	'水冷机类型码',	'water_cooler_type_code',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(17,	'20',	0,	'EMS序列号',	'ems_serial',	1,	'ASCII',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(18,	'40',	0,	'EMS软件版本号',	'ems_software_version',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(19,	'40',	1,	'EMS硬件版本号',	'ems_hardware_version',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(20,	'40',	2,	'EMS通信协议版本号',	'ems_protocol_version',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(21,	'40',	3,	'EMS总充电能力(kW)',	'ems_design_charge_power',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(22,	'40',	4,	'EMS总放电能力(kW)',	'ems_design_discharge_power',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(23,	'40',	5,	'EMS设计存储能力(kWh)',	'ems_design_storage_energy',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(24,	'40',	6,	'EMS设计功率(kW)',	'ems_design_power',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(25,	'40',	7,	'EMS充电能力(kW) 随状态变化',	'ems_charge_power_limit',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(26,	'40',	8,	'EMS放电能力(kW) 随状态变化',	'ems_discharge_power_limit',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(27,	'60',	0,	'EMS状态(0:正常,1:告警,2:故障)',	'ems_state',	1,	'Uint16',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(28,	'60',	1,	'SOC(0~100)',	'ems_soc',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(29,	'60',	2,	'SOH(0~100)',	'ems_soh',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(30,	'60',	3,	'直流侧功率(kW) 正为放电',	'ems_dc_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(31,	'60',	4,	'直流侧电压(V)',	'ems_dc_voltage',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(32,	'60',	5,	'直流侧电流(A) 正为放电',	'ems_dc_current',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(33,	'60',	6,	'电网侧功率(kW) 正为放电',	'ems_ac_active_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(34,	'60',	7,	'电网侧无功功率(kVA) 正为输出',	'ems_ac_reactive_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(35,	'60',	8,	'电网频率(Hz)',	'ems_ac_frequency',	100,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(36,	'60',	9,	'电网侧功率(kW) 正部分',	'ems_ac_active_power_pos',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(37,	'60',	10,	'电网侧功率(kW) 负部分',	'ems_ac_active_power_neg',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(38,	'60',	11,	'直流侧功率(kW) 正部分',	'ems_dc_power_pos',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(39,	'60',	12,	'直流侧功率(kW) 负部分',	'ems_dc_power_neg',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(40,	'60',	13,	'EMS历史输出能量(kWh)',	'ems_history_output_energy',	10,	'Uint32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(41,	'60',	15,	'EMS历史输入能量(kWh)',	'ems_history_input_energy',	10,	'Uint32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(42,	'80',	0,	'SYSTEM状态BIT位0',	'system_state_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(43,	'80',	1,	'SYSTEM状态BIT位1',	'system_state_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(44,	'80',	2,	'SYSTEM状态BIT位2',	'system_state_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(45,	'80',	3,	'SYSTEM状态BIT位3',	'system_state_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(46,	'80',	4,	'SYSTEM告警BIT位0',	'system_alarm_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(47,	'80',	5,	'SYSTEM告警BIT位1',	'system_alarm_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(48,	'80',	6,	'SYSTEM故障BIT位0',	'system_fault_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(49,	'80',	7,	'SYSTEM故障BIT位1',	'system_fault_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(50,	'90',	0,	'32位UTC时间的高16位',	'ems_utc_high',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(51,	'90',	1,	'32位UTC时间的低16位',	'ems_utc_low',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(52,	'90',	2,	'32位启动毫秒数的高16位',	'ems_uptime_high',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(53,	'90',	3,	'32位启动毫秒数的低16位',	'ems_uptime_low',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(54,	'90',	4,	'是否配备热备(1有效)',	'ems_hot-backup_valid',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(55,	'90',	5,	'热备运行中机器编号',	'ems_hot-backup_running_code',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(56,	'90',	6,	'热备当前等级(0: 无法切换, 1: 可实时切换)',	'ems_hot-backup_level',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(57,	'90',	7,	'CPU负载(0-100)',	'ems_cpu_load',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(58,	'90',	8,	'内存负载(0-100)',	'ems_memory_load',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(59,	'90',	9,	'磁盘负载(0-100)',	'ems_disk_load',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(60,	'100+AIRi*10',	0,	'温度(℃)',	'air_conditioner_{i}_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(61,	'100+AIRi*10',	1,	'湿度(%)',	'air_conditioner_{i}_humidity',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(62,	'100+AIRi*10',	2,	'状态BIT位0',	'air_conditioner_{i}_state_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(63,	'100+AIRi*10',	3,	'状态BIT位1',	'air_conditioner_{i}_state_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(64,	'100+AIRi*10',	4,	'故障BIT位0',	'air_conditioner_{i}_fault_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(65,	'100+AIRi*10',	5,	'故障BIT位1',	'air_conditioner_{i}_fault_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(66,	'100+AIRi*10',	6,	'故障BIT位2',	'air_conditioner_{i}_fault_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(67,	'100+AIRi*10',	7,	'故障BIT位3',	'air_conditioner_{i}_fault_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(68,	'150+FIREi*10',	0,	'状态BIT位0',	'firefighting_{i}_state_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(69,	'150+FIREi*10',	1,	'状态BIT位1',	'firefighting_{i}_state_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(70,	'150+FIREi*10',	2,	'故障BIT位0',	'firefighting_{i}_fault_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(71,	'150+FIREi*10',	3,	'故障BIT位1',	'firefighting_{i}_fault_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(72,	'150+FIREi*10',	4,	'故障BIT位2',	'firefighting_{i}_fault_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(73,	'150+FIREi*10',	5,	'故障BIT位3',	'firefighting_{i}_fault_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(74,	'200',	0,	'PV功率(kW)',	'pv_power',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(75,	'200',	1,	'PV历史能量(kWh)',	'pv_history_energy',	10,	'Uint32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(76,	'210',	0,	'并网点功率(kW) 正为向电网送能量',	'grid_active_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(77,	'210',	1,	'并网点无功功率(kVA) 正为向电网发无功',	'grid_reactive_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(78,	'210',	2,	'并网点功率(kW) 正部分',	'grid_active_power_pos',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(79,	'210',	3,	'并网点功率(kw) 负部分',	'grid_active_power_neg',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(80,	'210',	4,	'并网点历史能量(kWh) 正部分',	'grid_history_energy_pos',	10,	'Uint32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(81,	'210',	6,	'并网点历史能量(kwh) 负部分',	'grid_history_energy_neg',	10,	'Uint32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(82,	'220',	0,	'负载有功功率(kW) ',	'load_active_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(83,	'220',	1,	'负载无功功率(kVA)',	'load_reactive_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(84,	'220',	2,	'负载功率(kW) 正部分',	'load_active_power_pos',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(85,	'220',	3,	'负载功率(kW) 负部分',	'load_active_power_neg',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(86,	'220',	4,	'负载历史能量(kWh) 正部分',	'load_history_energy_pos',	10,	'Uint32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(87,	'220',	6,	'负载历史能量(kwh) 负部分',	'load_history_energy_neg',	10,	'Uint32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(88,	'250+WATERi*10',	0,	'出水温度(℃)',	'water_cooler_{i}_outlet_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(89,	'250+WATERi*10',	1,	'回水温度(℃)',	'water_cooler_{i}_return_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(90,	'250+WATERi*10',	2,	'出水压力(Bar)',	'water_cooler_{i}_outlet_pressure',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(91,	'250+WATERi*10',	3,	'回水压力(Bar)',	'water_cooler_{i}_return_pressure',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(92,	'250+WATERi*10',	4,	'状态BIT位0',	'water_cooler_{i}_state_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(93,	'250+WATERi*10',	5,	'状态BIT位1',	'water_cooler_{i}_state_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(94,	'250+WATERi*10',	6,	'故障BIT位0',	'water_cooler_{i}_fault_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(95,	'250+WATERi*10',	7,	'故障BIT位1',	'water_cooler_{i}_fault_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(96,	'250+WATERi*10',	8,	'故障BIT位2',	'water_cooler_{i}_fault_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(97,	'250+WATERi*10',	9,	'故障BIT位3',	'water_cooler_{i}_fault_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(98,	'500+PCSi*100',	0,	'设计最大容量(kVA)',	'pcs_{i}_design_capacity',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(99,	'500+PCSi*100',	1,	'设计最高直流电压(V)',	'pcs_{i}_design_max_dc_voltage',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(100,	'500+PCSi*100',	2,	'设计最低直流电压(V)',	'pcs_{i}_design_min_dc_voltage',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(101,	'500+PCSi*100',	3,	'设计交流电压(V)',	'pcs_{i}_design_ac_voltage',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(102,	'500+PCSi*100',	4,	'设计最大直流电流(A)',	'pcs_{i}_design_max_dc_current',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(103,	'500+PCSi*100',	10,	'PCSi序列号',	'pcs_{i}_serial',	1,	'ASCII',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(104,	'500+PCSi*100',	30,	'功率(kW) 正为放电',	'pcs_{i}_active_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(105,	'500+PCSi*100',	31,	'无功功率(kVA) 正为输出',	'pcs_{i}_reactive_power',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(106,	'500+PCSi*100',	32,	'频率(Hz)',	'pcs_{i}_frequency',	100,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(107,	'500+PCSi*100',	33,	'三相电压(V)',	'pcs_{i}_voltage',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(108,	'500+PCSi*100',	34,	'三相电流(A)',	'pcs_{i}_current',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(109,	'500+PCSi*100',	35,	'A相电压(V)',	'pcs_{i}_voltage_1',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(110,	'500+PCSi*100',	36,	'A相电流(A)',	'pcs_{i}_current_1',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(111,	'500+PCSi*100',	37,	'A相有功功率(kW) 正为放电',	'pcs_{i}_active_power_1',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(112,	'500+PCSi*100',	38,	'A相无功功率(kVA) 正为输出',	'pcs_{i}_reactive_power_1',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(113,	'500+PCSi*100',	39,	'B相电压(V)',	'pcs_{i}_voltage_2',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(114,	'500+PCSi*100',	40,	'B相电流(A)',	'pcs_{i}_current_2',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(115,	'500+PCSi*100',	41,	'B相有功功率(kW) 正为放电',	'pcs_{i}_active_power_2',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(116,	'500+PCSi*100',	42,	'B相无功功率(kVA) 正为输出',	'pcs_{i}_reactive_power_2',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(117,	'500+PCSi*100',	43,	'C相电压(V)',	'pcs_{i}_voltage_3',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(118,	'500+PCSi*100',	44,	'C相电流(A)',	'pcs_{i}_current_3',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(119,	'500+PCSi*100',	45,	'C相有功功率(kW) 正为放电',	'pcs_{i}_active_power_3',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(120,	'500+PCSi*100',	46,	'C相无功功率(kVA) 正为输出',	'pcs_{i}_reactive_power_3',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(121,	'500+PCSi*100',	47,	'空气温度(deg)',	'pcs_{i}_air_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(122,	'500+PCSi*100',	48,	'IGBT温度(deg)',	'pcs_{i}_IGBT_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(123,	'500+PCSi*100',	49,	'功率因数(-1~1)',	'pcs_{i}_power_factor',	100,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(124,	'500+PCSi*100',	50,	'直流侧电压(V)',	'pcs_{i}_dc_voltage',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(125,	'500+PCSi*100',	51,	'直流侧电流(A) 正为输出',	'pcs_{i}_dc_current',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(126,	'500+PCSi*100',	70,	'PCSi状态(0:正常,1:告警,2:故障)',	'pcs_{i}_state',	1,	'Uint16',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(127,	'500+PCSi*100',	71,	'PCSi状态BIT位0',	'pcs_{i}_state_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(128,	'500+PCSi*100',	72,	'PCSi状态BIT位1',	'pcs_{i}_state_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(129,	'500+PCSi*100',	73,	'PCSi状态BIT位2',	'pcs_{i}_state_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(130,	'500+PCSi*100',	74,	'PCSi状态BIT位3',	'pcs_{i}_state_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(131,	'500+PCSi*100',	75,	'PCSi告警BIT位0',	'pcs_{i}_alarm_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(132,	'500+PCSi*100',	76,	'PCSi告警BIT位1',	'pcs_{i}_alarm_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(133,	'500+PCSi*100',	77,	'PCSi告警BIT位2',	'pcs_{i}_alarm_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(134,	'500+PCSi*100',	78,	'PCSi告警BIT位3',	'pcs_{i}_alarm_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(135,	'500+PCSi*100',	79,	'PCSi告警BIT位4',	'pcs_{i}_alarm_bit_4',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(136,	'500+PCSi*100',	80,	'PCSi告警BIT位5',	'pcs_{i}_alarm_bit_5',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(137,	'500+PCSi*100',	81,	'PCSi告警BIT位6',	'pcs_{i}_alarm_bit_6',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(138,	'500+PCSi*100',	82,	'PCSi告警BIT位7',	'pcs_{i}_alarm_bit_7',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(139,	'500+PCSi*100',	83,	'PCSi故障BIT位0',	'pcs_{i}_fault_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(140,	'500+PCSi*100',	84,	'PCSi故障BIT位1',	'pcs_{i}_fault_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(141,	'500+PCSi*100',	85,	'PCSi故障BIT位2',	'pcs_{i}_fault_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(142,	'500+PCSi*100',	86,	'PCSi故障BIT位3',	'pcs_{i}_fault_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(143,	'500+PCSi*100',	87,	'PCSi故障BIT位4',	'pcs_{i}_fault_bit_4',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(144,	'500+PCSi*100',	88,	'PCSi故障BIT位5',	'pcs_{i}_fault_bit_5',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(145,	'500+PCSi*100',	89,	'PCSi故障BIT位6',	'pcs_{i}_fault_bit_6',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(146,	'500+PCSi*100',	90,	'PCSi故障BIT位7',	'pcs_{i}_fault_bit_7',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(147,	'1000',	0,	'设计存储能力(kWh)',	'bms_design_energy',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(148,	'1000',	1,	'最高设计电压(V)',	'bms_design_max_voltage',	10,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(149,	'1000',	2,	'最低设计电压(V)',	'bms_design_min_voltage',	10,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(150,	'1000',	3,	'最高设计放电电流(A)',	'bms_design_max_discharge_current',	10,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(151,	'1000',	4,	'最高设计充电电流(A)',	'bms_design_max_charge_current',	10,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(152,	'1000',	5,	'电池簇数量(CLUSTER <= 50)',	'bms_cluster_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(153,	'1000',	6,	'每簇电池组数量(STACK <= 30)',	'bms_stack_per_cluster_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(154,	'1000',	7,	'每组电芯数量(CELL <= 32)',	'bms_cell_per_stack_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(155,	'1000',	8,	'每组电芯温度传感器数量(CELL_T <= 32)',	'bms_temperature_per_stack_count',	1,	'Uint16',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(156,	'1000',	10,	'序列号',	'bms_serial',	1,	'ASCII',	'T0',	NULL,	NULL,	NULL,	NULL,	NULL),
(157,	'1000',	30,	'SOC(0~100)',	'bms_soc',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(158,	'1000',	31,	'SOH(0~100)',	'bms_soh',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(159,	'1000',	32,	'可放电能量(kWh)',	'bms_dischargeable_energy',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(160,	'1000',	33,	'可充电能量(kWh)',	'bms_chargeable_energy',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(161,	'1000',	34,	'当前电压(V)',	'bms_voltage',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(162,	'1000',	35,	'当前电流(A) 正为放电',	'bms_current',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(163,	'1000',	36,	'环境温度(deg)',	'bms_air_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(164,	'1000',	37,	'最高单体电压(V)',	'bms_cell_high_voltage',	1000,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(165,	'1000',	38,	'最低单体电压(V)',	'bms_cell_low_voltage',	1000,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(166,	'1000',	39,	'最高单体电压充号',	'bms_cell_high_voltage_positioin',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(167,	'1000',	40,	'最低单体电压序号',	'bms_cell_low_voltage_position',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(168,	'1000',	41,	'最高单体温度(deg)',	'bms_cell_high_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(169,	'1000',	42,	'最低单体温度(deg)',	'bms_cell_low_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(170,	'1000',	43,	'最高单体温度充号',	'bms_cell_high_temperature_position',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(171,	'1000',	44,	'最低单体温度序号',	'bms_cell_low_temperature_position',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(172,	'1000',	45,	'单体平均电压(V)',	'bms_cell_average_voltage',	1000,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(173,	'1000',	46,	'单体平均温度(deg)',	'bms_cell_average_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(174,	'1070',	0,	'BMS状态(0:正常,1:告警,2:故障)',	'bms_state',	1,	'Uint16',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(175,	'1070',	1,	'BMS状态BIT位0',	'bms_state_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(176,	'1070',	2,	'BMS状态BIT位1',	'bms_state_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(177,	'1070',	3,	'BMS状态BIT位2',	'bms_state_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(178,	'1070',	4,	'BMS状态BIT位3',	'bms_state_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(179,	'1070',	5,	'BMS告警BIT位0',	'bms_alarm_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(180,	'1070',	6,	'BMS告警BIT位1',	'bms_alarm_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(181,	'1070',	7,	'BMS告警BIT位2',	'bms_alarm_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(182,	'1070',	8,	'BMS告警BIT位3',	'bms_alarm_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(183,	'1070',	9,	'BMS告警BIT位4',	'bms_alarm_bit_4',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(184,	'1070',	10,	'BMS故障BIT位0',	'bms_fault_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(185,	'1070',	11,	'BMS故障BIT位1',	'bms_fault_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(186,	'1070',	12,	'BMS故障BIT位2',	'bms_fault_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(187,	'1070',	13,	'BMS故障BIT位3',	'bms_fault_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(188,	'1070',	14,	'BMS故障BIT位4',	'bms_fault_bit_4',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(189,	'1070',	15,	'BMS故障BIT位5',	'bms_fault_bit_5',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(190,	'1070',	16,	'BMS故障BIT位6',	'bms_fault_bit_6',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(191,	'1100+CLUSTERi*50',	0,	'SOC(0~100)',	'bms_cluster_{i}_soc',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(192,	'1100+CLUSTERi*50',	1,	'SOH(0~100)',	'bms_cluster_{i}_soh',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(193,	'1100+CLUSTERi*50',	2,	'可放电能量(kWh)',	'bms_cluster_{i}_dischargeable_energy',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(194,	'1100+CLUSTERi*50',	3,	'可充电能量(kWh)',	'bms_cluster_{i}_chargeable_energy',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(195,	'1100+CLUSTERi*50',	4,	'当前电压(V)',	'bms_cluster_{i}_voltage',	10,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(196,	'1100+CLUSTERi*50',	5,	'当前电流(A) 正为放电',	'bms_cluster_{i}_current',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(197,	'1100+CLUSTERi*50',	6,	'环境温度(deg)',	'bms_cluster_{i}_air_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(198,	'1100+CLUSTERi*50',	7,	'最高单体电压(V)',	'bms_cluster_{i}_cell_high_voltage',	1000,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(199,	'1100+CLUSTERi*50',	8,	'最低单体电压(V)',	'bms_cluster_{i}_cell_low_voltage',	1000,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(200,	'1100+CLUSTERi*50',	9,	'最高单体电压充号',	'bms_cluster_{i}_cell_high_voltage_positioin',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(201,	'1100+CLUSTERi*50',	10,	'最低单体电压序号',	'bms_cluster_{i}_cell_low_voltage_position',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(202,	'1100+CLUSTERi*50',	11,	'最高单体温度(deg)',	'bms_cluster_{i}_cell_high_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(203,	'1100+CLUSTERi*50',	12,	'最低单体温度(deg)',	'bms_cluster_{i}_cell_low_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(204,	'1100+CLUSTERi*50',	13,	'最高单体温度充号',	'bms_cluster_{i}_cell_high_temperature_position',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(205,	'1100+CLUSTERi*50',	14,	'最低单体温度序号',	'bms_cluster_{i}_cell_low_temperature_position',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(206,	'1100+CLUSTERi*50',	15,	'单体平均电压(V)',	'bms_cluster_{i}_cell_average_voltage',	1000,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(207,	'1100+CLUSTERi*50',	16,	'单体平均温度(deg)',	'bms_cluster_{i}_cell_average_temperature',	10,	'Int16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(208,	'1100+CLUSTERi*50',	20,	'CLUSTERi状态(0:正常,1:告警,2:故障)',	'bms_cluster_{i}_state',	1,	'Uint16',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(209,	'1100+CLUSTERi*50',	21,	'CLUSTERi状态BIT位0',	'bms_cluster_{i}_state_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(210,	'1100+CLUSTERi*50',	22,	'CLUSTERi状态BIT位1',	'bms_cluster_{i}_state_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(211,	'1100+CLUSTERi*50',	23,	'CLUSTERi状态BIT位2',	'bms_cluster_{i}_state_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(212,	'1100+CLUSTERi*50',	24,	'CLUSTERi状态BIT位3',	'bms_cluster_{i}_state_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(213,	'1100+CLUSTERi*50',	25,	'CLUSTERi告警BIT位0',	'bms_cluster_{i}_alarm_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(214,	'1100+CLUSTERi*50',	26,	'CLUSTERi告警BIT位1',	'bms_cluster_{i}_alarm_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(215,	'1100+CLUSTERi*50',	27,	'CLUSTERi告警BIT位2',	'bms_cluster_{i}_alarm_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(216,	'1100+CLUSTERi*50',	28,	'CLUSTERi告警BIT位3',	'bms_cluster_{i}_alarm_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(217,	'1100+CLUSTERi*50',	29,	'CLUSTERi告警BIT位4',	'bms_cluster_{i}_alarm_bit_4',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(218,	'1100+CLUSTERi*50',	30,	'CLUSTERi故障BIT位0',	'bms_cluster_{i}_fault_bit_0',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(219,	'1100+CLUSTERi*50',	31,	'CLUSTERi故障BIT位1',	'bms_cluster_{i}_fault_bit_1',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(220,	'1100+CLUSTERi*50',	32,	'CLUSTERi故障BIT位2',	'bms_cluster_{i}_fault_bit_2',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(221,	'1100+CLUSTERi*50',	33,	'CLUSTERi故障BIT位3',	'bms_cluster_{i}_fault_bit_3',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(222,	'1100+CLUSTERi*50',	34,	'CLUSTERi故障BIT位4',	'bms_cluster_{i}_fault_bit_4',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(223,	'1100+CLUSTERi*50',	35,	'CLUSTERi故障BIT位5',	'bms_cluster_{i}_fault_bit_5',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(224,	'1100+CLUSTERi*50',	36,	'CLUSTERi故障BIT位6',	'bms_cluster_{i}_fault_bit_6',	1,	'BIT',	'T1+',	NULL,	NULL,	NULL,	NULL,	NULL),
(225,	'4000~CLUSTER*STACK*CELL',	0,	'单体电芯电压(V)',	'bms_cell_voltage_{i}',	1000,	'Uint16',	'T2',	NULL,	NULL,	NULL,	NULL,	NULL),
(226,	'4000+CLUSTER*STACK*CELL~CLUSTER*STACK*CELL_T',	0,	'单体电芯温度(deg)',	'bms_cell_temperature_{i}',	10,	'Int16',	'T2',	NULL,	NULL,	NULL,	NULL,	NULL),
(227,	'60000',	0,	'功率只读模式',	'power_read_only',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(228,	'60000',	1,	'设定状态',	'set_state',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(229,	'60000',	2,	'设定功率',	'set_power',	1,	'Int32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(230,	'60000',	4,	'功率右限',	'right_limit_power',	1,	'Int32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(231,	'60000',	6,	'功率左限',	'left_limit_power',	1,	'Int32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(232,	'60000',	8,	'当前功率',	'current_power',	1,	'Int32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(233,	'60000',	10,	'故障标识',	'fault_flag',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(234,	'60000',	11,	'状态机',	'state',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(235,	'60000',	12,	'状态机TICK',	'state_tick',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(236,	'60000',	13,	'状态机子TICK',	'state_sub_tick',	1,	'Uint16',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(237,	'60000',	14,	'防逆流限制值',	'anti_backflow_power_limit',	1,	'Int32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(238,	'60000',	16,	'需量限制值',	'required_power_limit',	1,	'Int32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL),
(239,	'60000',	18,	'电表控制功率',	'meter_control_power',	1,	'Int32',	'T1',	NULL,	NULL,	NULL,	NULL,	NULL);

DROP TABLE IF EXISTS `t_power_level`;
CREATE TABLE `t_power_level` (
                                 `uuid` varchar(128) NOT NULL,
                                 `power_level` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_power_level` (`uuid`, `power_level`) VALUES
('1',	'<1kv'),
('2',	'1kv~10kv'),
('3',	'10kv'),
('4',	'10kv~20kv'),
('5',	'20kv'),
('6',	'20kv~35kv'),
('7',	'35kv及以下'),
('8',	'35kv'),
('9',	'35kv及以上'),
('10',	'35kv~110kv'),
('11',	'110kv'),
('12',	'110kv~220kv'),
('13',	'220kv'),
('14',	'220kv及以上');

DROP TABLE IF EXISTS `t_power_type`;
CREATE TABLE `t_power_type` (
                                `uuid` varchar(128) NOT NULL,
                                `power_type` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_power_type` (`uuid`, `power_type`) VALUES
('1',	'城区一般工商业'),
('2',	'郊区一般工商业'),
('3',	'经开区一般工商业'),
('4',	'城区大工业'),
('5',	'郊区大工业'),
('6',	'大工业'),
('7',	'100千伏安及以上普通工业用电'),
('8',	'工商业(非夏季)'),
('9',	'工商业(夏季)'),
('10',	'一般工商业'),
('11',	'工商业及其他用电');

DROP TABLE IF EXISTS `t_price_area`;
CREATE TABLE `t_price_area` (
                                `id` varchar(128) NOT NULL COMMENT '电价配置id',
                                `area` varchar(128) NOT NULL COMMENT '项目地区',
                                `power_type` varchar(128) NOT NULL COMMENT '用电类型',
                                `power_level` varchar(128) NOT NULL COMMENT '电压等级'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `t_project`;
CREATE TABLE `t_project` (
                             `id` varchar(128) NOT NULL COMMENT 'modbus 存储在tsdb的uuid',
                             `project_name` varchar(100) DEFAULT NULL COMMENT '系统名',
                             `project_host` varchar(255) DEFAULT NULL COMMENT 'modbus host地址',
                             `project_port` int(11) DEFAULT NULL COMMENT 'modbus 地址',
                             `project_model` varchar(128) DEFAULT '1' COMMENT '项目光伏模式，全额上网2，1余量上网',
                             `project_device_alias` varchar(50) DEFAULT NULL COMMENT '项目电站名',
                             `version` varchar(20) DEFAULT NULL COMMENT '版本号',
                             `price_proxy` tinyint(1) NOT NULL DEFAULT '0' COMMENT '电价托管 默认false',
                             `fault` int(10) NOT NULL DEFAULT '0' COMMENT '项目故障',
                             `alarm` int(10) NOT NULL DEFAULT '0' COMMENT '项目告警',
                             `status` int(10) NOT NULL DEFAULT '1' COMMENT '项目状态',
                             `duration` int(10) NOT NULL DEFAULT '8' COMMENT '时区东正西负',
                             `whether_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
                             `create_by` varchar(20) DEFAULT NULL COMMENT '创建者',
                             `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                             `update_by` varchar(20) DEFAULT NULL COMMENT '修改者',
                             `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_project` (`id`, `project_name`, `project_host`, `project_port`, `project_model`, `project_device_alias`, `version`, `price_proxy`, `fault`, `alarm`, `status`, `duration`, `whether_delete`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('4f537620d37d40e19dd25be5ca6ad941',	' 无锡为恒500kW/1MWh储能系统',	'*************',	502,	'1',	'EMS',	'v2.0.2',	0,	0,	0,	1,	8,	0,	NULL,	1657036800,	NULL,	NULL);

DROP TABLE IF EXISTS `t_project_ext`;
CREATE TABLE `t_project_ext` (
                                 `id` varchar(128) NOT NULL COMMENT '项目扩展id',
                                 `area` varchar(128) DEFAULT NULL COMMENT '项目地点',
                                 `size` varchar(128) DEFAULT NULL COMMENT '项目规模',
                                 `address` varchar(1280) DEFAULT NULL COMMENT '项目详细地址',
                                 `power_type` int(10) DEFAULT NULL COMMENT '用电类型',
                                 `power_level` int(10) DEFAULT NULL COMMENT '电压等级',
                                 `longitude` double(22,0) DEFAULT NULL COMMENT '项目经度',
                                 `latitude` double(22,0) DEFAULT NULL COMMENT '项目维度',
                                 `remark` varchar(1280) DEFAULT NULL COMMENT '项目备注',
                                 `ems` int(10) NOT NULL DEFAULT '0' COMMENT 'ems数量',
                                 `pcs` int(10) NOT NULL DEFAULT '0' COMMENT 'pcs数量',
                                 `bms` int(10) NOT NULL DEFAULT '0' COMMENT 'bms数量',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `t_project_url`;
CREATE TABLE `t_project_url` (
                                 `id` varchar(128) NOT NULL COMMENT '项目扩展id',
                                 `url` varchar(256) DEFAULT NULL COMMENT '扩展url',
                                 `order` int(10) DEFAULT NULL COMMENT '序号',
                                 `description` varchar(512) DEFAULT NULL COMMENT 'url描述',
                                 `remark` varchar(256) DEFAULT NULL COMMENT '项目备注',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `t_role`;
CREATE TABLE `t_role` (
                          `id` varchar(128) CHARACTER SET utf8 NOT NULL,
                          `role_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色名称',
                          `project_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目id',
                          `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色备注',
                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `t_role` (`id`, `role_name`, `project_id`, `remark`) VALUES
('1305f0bbed5be61cc39f44d56e88a8b0',	'超级管理员',	'4f537620d37d40e19dd25be5ca6ad941',	NULL),
('814b0b6ecbc18363ed614e2c0670b868',	'xuyuan',	'common',	'');

DROP TABLE IF EXISTS `t_role_authority`;
CREATE TABLE `t_role_authority` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                    `role_id` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '角色ID',
                                    `authority_id` bigint(20) DEFAULT NULL COMMENT '接入点ID',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `t_role_authority` (`id`, `role_id`, `authority_id`) VALUES
(27283,	'45',	1),
(27284,	'45',	11),
(27285,	'45',	12),
(27286,	'45',	13),
(27287,	'45',	14),
(27288,	'45',	15),
(27289,	'45',	2),
(27290,	'45',	21),
(27291,	'45',	211),
(27292,	'45',	212),
(27293,	'45',	213),
(27294,	'45',	214),
(27295,	'45',	22),
(27296,	'45',	221),
(27297,	'45',	222),
(27298,	'45',	223),
(27299,	'45',	3),
(27300,	'45',	31),
(27301,	'45',	311),
(27302,	'45',	312),
(27303,	'45',	313),
(27304,	'45',	32),
(27305,	'45',	321),
(27306,	'45',	322),
(27307,	'45',	323),
(27308,	'45',	33),
(27309,	'45',	331),
(27310,	'45',	332),
(27311,	'45',	333),
(27312,	'45',	34),
(27313,	'45',	341),
(27314,	'45',	342),
(27315,	'45',	4),
(27316,	'45',	41),
(27317,	'45',	5),
(27318,	'45',	51),
(27319,	'45',	52),
(27320,	'45',	6),
(27321,	'45',	61),
(27322,	'45',	7),
(27323,	'45',	71),
(27324,	'45',	711),
(27325,	'45',	712),
(27326,	'45',	713),
(27327,	'45',	8),
(27328,	'45',	81),
(27329,	'45',	82),
(27330,	'45',	83),
(27331,	'45',	9),
(27332,	'45',	91),
(27333,	'45',	92),
(27334,	'45',	93),
(27335,	'45',	94),
(27336,	'45',	10),
(27337,	'45',	101),
(27338,	'45',	19),
(27339,	'45',	191),
(27340,	'45',	192),
(27341,	'45',	193),
(27342,	'45',	194),
(27343,	'45',	195),
(27344,	'45',	196),
(27345,	'45',	197),
(27563,	'49',	1),
(27564,	'49',	11),
(27565,	'49',	12),
(27566,	'49',	13),
(27567,	'49',	14),
(27568,	'49',	15),
(27569,	'49',	2),
(27570,	'49',	21),
(27571,	'49',	211),
(27572,	'49',	212),
(27573,	'49',	213),
(27574,	'49',	214),
(27575,	'49',	22),
(27576,	'49',	221),
(27577,	'49',	222),
(27578,	'49',	223),
(27579,	'49',	3),
(27580,	'49',	31),
(27581,	'49',	311),
(27582,	'49',	312),
(27583,	'49',	313),
(27584,	'49',	32),
(27585,	'49',	321),
(27586,	'49',	322),
(27587,	'49',	323),
(27588,	'49',	33),
(27589,	'49',	331),
(27590,	'49',	332),
(27591,	'49',	333),
(27592,	'49',	34),
(27593,	'49',	341),
(27594,	'49',	342),
(27595,	'49',	4),
(27596,	'49',	41),
(27597,	'49',	5),
(27598,	'49',	51),
(27599,	'49',	52),
(27600,	'49',	6),
(27601,	'49',	61),
(27602,	'49',	7),
(27603,	'49',	71),
(27604,	'49',	711),
(27605,	'49',	712),
(27606,	'49',	713),
(27607,	'49',	8),
(27608,	'49',	81),
(27609,	'49',	82),
(27610,	'49',	83),
(27611,	'49',	9),
(27612,	'49',	91),
(27613,	'49',	92),
(27614,	'49',	93),
(27615,	'49',	94),
(27616,	'49',	10),
(27617,	'49',	101),
(27618,	'49',	19),
(27619,	'49',	191),
(27620,	'49',	192),
(27621,	'49',	193),
(27622,	'49',	194),
(27623,	'49',	195),
(27624,	'49',	196),
(27625,	'49',	197),
(33409,	'10',	1),
(33410,	'10',	2),
(33411,	'10',	3),
(33591,	'1305f0bbed5be61cc39f44d56e88a8b0',	1),
(33592,	'1305f0bbed5be61cc39f44d56e88a8b0',	2),
(33593,	'1305f0bbed5be61cc39f44d56e88a8b0',	4),
(33594,	'1305f0bbed5be61cc39f44d56e88a8b0',	5),
(33595,	'1305f0bbed5be61cc39f44d56e88a8b0',	51),
(33596,	'1305f0bbed5be61cc39f44d56e88a8b0',	52),
(33597,	'1305f0bbed5be61cc39f44d56e88a8b0',	6),
(33598,	'1305f0bbed5be61cc39f44d56e88a8b0',	61),
(33599,	'1305f0bbed5be61cc39f44d56e88a8b0',	62),
(33600,	'1305f0bbed5be61cc39f44d56e88a8b0',	63),
(33601,	'1305f0bbed5be61cc39f44d56e88a8b0',	64),
(33602,	'1305f0bbed5be61cc39f44d56e88a8b0',	65),
(33603,	'1305f0bbed5be61cc39f44d56e88a8b0',	66),
(33604,	'1305f0bbed5be61cc39f44d56e88a8b0',	67),
(33605,	'1305f0bbed5be61cc39f44d56e88a8b0',	68),
(33606,	'1305f0bbed5be61cc39f44d56e88a8b0',	7),
(33607,	'1305f0bbed5be61cc39f44d56e88a8b0',	71),
(33608,	'1305f0bbed5be61cc39f44d56e88a8b0',	72),
(33609,	'1305f0bbed5be61cc39f44d56e88a8b0',	73),
(33610,	'1305f0bbed5be61cc39f44d56e88a8b0',	74),
(33611,	'1305f0bbed5be61cc39f44d56e88a8b0',	8),
(33612,	'1305f0bbed5be61cc39f44d56e88a8b0',	91),
(33613,	'1305f0bbed5be61cc39f44d56e88a8b0',	92),
(33614,	'1305f0bbed5be61cc39f44d56e88a8b0',	93),
(33615,	'1305f0bbed5be61cc39f44d56e88a8b0',	101),
(33616,	'1305f0bbed5be61cc39f44d56e88a8b0',	103),
(33617,	'1305f0bbed5be61cc39f44d56e88a8b0',	104),
(33618,	'1305f0bbed5be61cc39f44d56e88a8b0',	105),
(33619,	'1305f0bbed5be61cc39f44d56e88a8b0',	106),
(33620,	'1305f0bbed5be61cc39f44d56e88a8b0',	107),
(33621,	'1305f0bbed5be61cc39f44d56e88a8b0',	108),
(33622,	'1305f0bbed5be61cc39f44d56e88a8b0',	11),
(33623,	'1305f0bbed5be61cc39f44d56e88a8b0',	111),
(33624,	'1305f0bbed5be61cc39f44d56e88a8b0',	112),
(33625,	'1305f0bbed5be61cc39f44d56e88a8b0',	12),
(33626,	'1305f0bbed5be61cc39f44d56e88a8b0',	121),
(33627,	'1305f0bbed5be61cc39f44d56e88a8b0',	122),
(33628,	'1305f0bbed5be61cc39f44d56e88a8b0',	9),
(33629,	'1305f0bbed5be61cc39f44d56e88a8b0',	10);

DROP TABLE IF EXISTS `t_strategy`;
CREATE TABLE `t_strategy` (
                              `id` int(11) NOT NULL AUTO_INCREMENT,
                              `type` int(11) DEFAULT NULL,
                              `week_day` int(11) DEFAULT NULL,
                              `power` double(22,0) DEFAULT NULL,
                              `start_time` time DEFAULT NULL,
                              `end_time` time DEFAULT NULL,
                              `demand_power` double(22,0) DEFAULT NULL,
                              `control_power` double(22,0) DEFAULT NULL,
                              `soc` double(22,0) DEFAULT NULL,
                              `anti_reflux` int(11) DEFAULT NULL,
                              `pcc_demand_power` double(22,0) DEFAULT NULL,
                              `pcc_control` double(22,0) DEFAULT NULL,
                              `group_id` varchar(128) DEFAULT NULL,
                              `project_id` varchar(128) DEFAULT NULL,
                              `create_time` bigint(20) DEFAULT NULL,
                              `create_by` text,
                              `update_time` bigint(20) DEFAULT NULL,
                              `update_by` text,
                              `priority` int(11) DEFAULT NULL,
                              `back_flow_limit_power` double(22,0) DEFAULT NULL COMMENT '防逆流功率值',
                              `charge_in_appoint_time` int(1) NOT NULL DEFAULT '0' COMMENT '仅在设定时间内充电',
                              `power_factor_control` tinyint(1) DEFAULT NULL COMMENT '功率因数控制模式开关',
                              `power_factor_first` tinyint(1) DEFAULT NULL COMMENT '功率因数优先模式，关闭时为有功功率优先',
                              `power_factor_control_value` double DEFAULT NULL COMMENT '功率因数控制值，0.9 - 1',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_strategy` (`id`, `type`, `week_day`, `power`, `start_time`, `end_time`, `demand_power`, `control_power`, `soc`, `anti_reflux`, `pcc_demand_power`, `pcc_control`, `group_id`, `project_id`, `create_time`, `create_by`, `update_time`, `update_by`, `priority`, `back_flow_limit_power`, `charge_in_appoint_time`, `power_factor_control`, `power_factor_first`, `power_factor_control_value`) VALUES
(187,	NULL,	0,	NULL,	NULL,	NULL,	400,	350,	0,	1,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1654046952,	'admin',	1662706147,	'whadmin',	4,	10,	1,	1,	1,	0.968),
(438,	0,	5,	100,	'00:00:00',	'07:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657092715,	'whadmin',	1657862199,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(439,	1,	5,	300,	'08:00:00',	'10:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657092715,	'whadmin',	1657862207,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(440,	1,	5,	350,	'14:00:00',	'14:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657092715,	'whadmin',	1657862217,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(441,	1,	5,	300,	'18:00:00',	'21:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657092715,	'whadmin',	1657862227,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(474,	0,	6,	100,	'00:00:00',	'07:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862233,	'whadmin',	1657862233,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(475,	1,	6,	300,	'08:00:00',	'10:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862233,	'whadmin',	1657862233,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(476,	1,	6,	350,	'14:00:00',	'14:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862233,	'whadmin',	1657862233,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(477,	1,	6,	300,	'18:00:00',	'21:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862233,	'whadmin',	1657862233,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(478,	0,	7,	100,	'00:00:00',	'07:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862237,	'whadmin',	1657862237,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(479,	1,	7,	300,	'08:00:00',	'10:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862237,	'whadmin',	1657862237,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(480,	1,	7,	350,	'14:00:00',	'14:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862237,	'whadmin',	1657862237,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(481,	1,	7,	300,	'18:00:00',	'21:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862237,	'whadmin',	1657862237,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(482,	0,	5,	50,	'15:00:00',	'17:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862258,	'whadmin',	1657868015,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(483,	0,	4,	100,	'00:00:00',	'07:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862269,	'whadmin',	1657862269,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(484,	1,	4,	300,	'08:00:00',	'10:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862269,	'whadmin',	1657862269,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(485,	1,	4,	350,	'14:00:00',	'14:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862269,	'whadmin',	1657862269,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(486,	0,	4,	100,	'15:00:00',	'17:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862269,	'whadmin',	1657862269,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(487,	1,	4,	300,	'18:00:00',	'21:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862269,	'whadmin',	1657862269,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(488,	0,	3,	100,	'00:00:00',	'07:59:00',	NULL,	NULL,	100,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862272,	'whadmin',	1659518539,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(489,	1,	3,	300,	'08:00:00',	'10:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862272,	'whadmin',	1657862272,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(490,	1,	3,	350,	'14:00:00',	'14:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862272,	'whadmin',	1657862272,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(491,	0,	3,	100,	'15:00:00',	'17:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862272,	'whadmin',	1657862272,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(492,	1,	3,	300,	'18:00:00',	'21:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862272,	'whadmin',	1657862272,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(493,	0,	2,	100,	'00:00:00',	'07:59:00',	NULL,	NULL,	99,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862274,	'whadmin',	1660032478,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(494,	1,	2,	300,	'08:00:00',	'10:59:00',	NULL,	NULL,	0,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862274,	'whadmin',	1660032449,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(495,	1,	2,	350,	'14:00:00',	'14:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862274,	'whadmin',	1657862274,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(496,	0,	2,	100,	'15:00:00',	'17:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862274,	'whadmin',	1657862274,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(497,	1,	2,	300,	'18:00:00',	'21:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862274,	'whadmin',	1657862274,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(498,	0,	1,	100,	'00:00:00',	'07:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862277,	'whadmin',	1657862277,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(499,	1,	1,	300,	'08:00:00',	'10:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862277,	'whadmin',	1657862277,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(500,	1,	1,	350,	'14:00:00',	'14:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862277,	'whadmin',	1657862277,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(501,	0,	1,	100,	'15:00:00',	'17:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862277,	'whadmin',	1657862277,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL),
(502,	1,	1,	300,	'18:00:00',	'21:59:00',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	'3e7fbde8830a440392f1b3cb17b52380',	'4f537620d37d40e19dd25be5ca6ad941',	1657862277,	'whadmin',	1657862277,	'whadmin',	4,	NULL,	0,	NULL,	NULL,	NULL);

DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user` (
                          `id` varchar(128) NOT NULL,
                          `user_name` varchar(256) NOT NULL,
                          `password` varchar(128) DEFAULT NULL,
                          `phone` varchar(128) DEFAULT NULL,
                          `email` varchar(20) DEFAULT NULL,
                          `wechat` varchar(128) DEFAULT NULL,
                          `role` tinytext,
                          `is_delete` bit(1) DEFAULT NULL,
                          `create_by` varchar(20) DEFAULT NULL,
                          `create_time` bigint(20) DEFAULT NULL,
                          `update_by` varchar(20) DEFAULT NULL,
                          `update_time` bigint(20) DEFAULT NULL,
                          `role_id` varchar(128) DEFAULT NULL,
                          `role_name` tinytext,
                          `all_project` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否拥有所有项目',
                          PRIMARY KEY (`id`),
                          UNIQUE KEY `t_user_user_name_uindex` (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户信息表';

INSERT INTO `t_user` (`id`, `user_name`, `password`, `phone`, `email`, `wechat`, `role`, `is_delete`, `create_by`, `create_time`, `update_by`, `update_time`, `role_id`, `role_name`, `all_project`) VALUES
('1',	'whadmin',	'$2a$10$1Z8inm81zfGzqtcxdvl/1ebFjat6E5uJk8Ey4p1MPdgWSp.31uOY2',	NULL,	NULL,	NULL,	'client',	CONV('0', 2, 10) + 0,	NULL,	NULL,	'whadmin',	1654668369,	'1',	'超级管理员',	0),
('1612db34a412d19418659eb5b289e095',	'jacob.sun',	'$2a$10$venF64zvN3LMPhc18zhR3OFnDbK.Yn.TfIbLIyEWhH7MmHURBU9.C',	NULL,	NULL,	NULL,	'client',	CONV('0', 2, 10) + 0,	NULL,	1659689926,	NULL,	1659689926,	NULL,	NULL,	0),
('1fe5b5bea264a8c5b99d047d96ee0ca5',	'测试',	'$2a$10$sOTwqLfKhV1hFrqJAttcCOH06YnDAzqqcMVGxR3wMqq6eYggNg8ru',	NULL,	NULL,	NULL,	'admin',	CONV('0', 2, 10) + 0,	'whadmin',	1663295222,	'whadmin',	1663664009,	'1305f0bbed5be61cc39f44d56e88a8b0',	'超级管理员',	1),
('f4289e8d91b86598108f62e2ca3d0ec9',	'admin',	'$2a$10$JyGIczCsic7yYT.28T6RueYyd5yqqxQbA1DTw5xRfEFHiEFb.dcDy',	NULL,	NULL,	NULL,	'client',	CONV('0', 2, 10) + 0,	'whadmin',	1657588503,	'whadmin',	1657588503,	'1305f0bbed5be61cc39f44d56e88a8b0',	'超级管理员',	0);

DROP TABLE IF EXISTS `t_user_project`;
CREATE TABLE `t_user_project` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `project_id` varchar(128) DEFAULT NULL,
                                  `user_id` varchar(128) DEFAULT NULL,
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `t_user_project` (`id`, `project_id`, `user_id`) VALUES
(2,	'4f537620d37d40e19dd25be5ca6ad941',	'f4289e8d91b86598108f62e2ca3d0ec9');

DROP TABLE IF EXISTS `t_user_role`;
CREATE TABLE `t_user_role` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT,
                               `user_id` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '用户ID',
                               `role_id` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '角色ID',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `t_user_role` (`id`, `user_id`, `role_id`) VALUES
(2,	'f4289e8d91b86598108f62e2ca3d0ec9',	'1305f0bbed5be61cc39f44d56e88a8b0'),
(3,	'1fe5b5bea264a8c5b99d047d96ee0ca5',	'1305f0bbed5be61cc39f44d56e88a8b0');

-- 2022-09-29 07:52:18