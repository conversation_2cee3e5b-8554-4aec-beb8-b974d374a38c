POST http://localhost:7050/weather/test/post
Content-Type: application/json

{"name": "jacob", "age": 19}

###


###

### 添加索引
PUT http://**************:9200/shopping
Accept: application/json

### 查询索引

GET http://**************:9200/shopping
Accept: application/json

### 查询所有索引

GET http://**************:9200/_cat/indices?v
Accept: application/json

### 创建文档数据 不指定id
POST http://**************:9200/shopping/_doc
Content-Type: application/json

{"name":"周昆","age": 18}

### 创建文档数据 指定id
POST http://**************:9200/shopping/_doc/1000
Content-Type: application/json

{"name":"孙勇","age": 20}

### 查询 指定id的数据
GET http://**************:9200/shopping/_doc/1000
Accept: application/json

### 查询 指定索引下所有是数据
GET http://**************:9200/shopping/_search
Accept: application/json

### 全量更新数据

PUT http://**************:9200/shopping/_doc/1000
Content-Type: application/json

{"name":"jacob","age": 18}

### 更新数据的某个部分
POST http://**************:9200/shopping/_update/1000
Content-Type: application/json

{"doc":{"age":"20"}}

###按条件查询数据
GET http://**************:9200/shopping/_search?q=name:jacob
Accept: application/json

###按条件查询数据
POST http://**************:9200/shopping/_search
Content-Type: application/json

{"query": {
  "match": {
    "name": "孙昆"
  }
}}

###按条件查询数据分页查询
POST http://**************:9200/shopping/_search
Content-Type: application/json

{
  "query": {
    "match_all":{
    }
  },
  "from": 0,
  "size": 2
}

###按条件查询数据分页查询 字段筛选 且按照字段排序 (默认不支持多排)
POST http://**************:9200/shopping/_search
Content-Type: application/json

{
  "query": {
    "match_all":{
    }
  },
  "from": 0,
  "size": 2,
  "_source": ["name"],
  "sort": {
    "age": {
      "order": "desc"
    }
  }
}

###多条件查询 and = must or = should  filter:ranage:column:gt:280
POST http://**************:9200/shopping/_search
Content-Type: application/json

{
  "query": {
    "bool": {
      "must": [{
        "match": {
          "name": "jacob"
        }},
        {"match": {
          "age": 18
        }}]
    }
  }
}


### 实时天气
GET https://devapi.qweather.com/v7/weather/now?location=101190205&key=1e5fb7deb9d242fb87a1c020d867db93
Accept: application/json

###

### 72小时天气
GET https://api.qweather.com/v7/weather/72h?location=101190205&key=3ec50552d5e44e20b9d18088456f9c03
Accept: application/json
###

### 天气时光机
GET https://api.qweather.com/v7/historical/weather?location=101210502&date=20221115&key=3ec50552d5e44e20b9d18088456f9c03
Accept: application/json

###

### 光伏天气 24h 72h
GET https://api.qweather.com/v7/solar-radiation/72h?location=120.21,31.62&key=3ec50552d5e44e20b9d18088456f9c03
Accept: application/json

###


###
POST http://nginx.dev.weiheng-tech.com:30231/api/v1/load/hourly
Content-Type: application/json

{
  "projectId": "4c9db86abb8141adb71e8f7d544d950b",
  "y": {
    "time_idx": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22,
      23
    ],
    "series": [
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1"
    ]
  }
}

###
###功率预测
POST  http://nginx.dev.weiheng-tech.com:30231/api/v1/tft/hourly
Content-Type: application/json

{
  "projectId": "4c9db86abb8141adb71e8f7d544d950b",
  "y": {
    "time_idx": [0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22,
      23
    ],
    "series": [
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1"
    ],
    "tempure": [12.709677419354838,
      12.625,
      11.666666666666666,
      11.941176470588236,
      11.314285714285715,
      11.88888888888889,
      11.864864864864865,
      12.842105263157896,
      14.102564102564102,
      15.75,
      17.0,
      18.585365853658537,
      19.0,
      19.902439024390244,
      20.902439024390244,
      19.658536585365855,
      18.902439024390244,
      17.48780487804878,
      15.902439024390244,
      14.585365853658537,
      14.195121951219512,
      13.829268292682928,
      12.829268292682928,
      12.0
    ],
    "windSpeed": [10.67741935483871,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      11.0,
      11.0,
      11.0,
      11.731707317073171,
      11.731707317073171,
      11.731707317073171,
      11.731707317073171,
      13.0,
      13.0,
      13.0,
      13.0,
      14.0,
      13.365853658536585,
      13.0,
      11.0,
      11.0
    ],
    "humidity": [80.41935483870968,
      79.6875,
      81.36363636363636,
      80.88235294117646,
      83.2,
      85.05555555555556,
      89.45945945945945,
      88.15789473684211,
      79.58974358974359,
      68.4,
      57.292682926829265,
      48.5609756097561,
      43.24390243902439,
      41.51219512195122,
      41.853658536585364,
      45.19512195121951,
      50.146341463414636,
      57.073170731707314,
      65.2439024390244,
      74.46341463414635,
      78.97560975609755,
      84.6829268292683,
      90.21951219512195,
      88.8780487804878
    ],
    "pressure": [1022.741935483871,
      1022.84375,
      1022.3333333333334,
      1022.0,
      1021.0,
      1020.0,
      1019.4594594594595,
      1019.0,
      1019.0,
      1019.0,
      1019.4146341463414,
      1019.9756097560976,
      1020.1219512195122,
      1020.2682926829268,
      1020.2926829268292,
      1020.0,
      1019.8536585365854,
      1019.0,
      1019.0,
      1018.560975609756,
      1018.560975609756,
      1019.439024390244,
      1019.6829268292682,
      1020.0
    ]
  }
}
###


# curl -L -X GET --compressed 'https://geoapi.qweather.com/v2/city/lookup?location=beij&key=YOUR_KEY'
GET https://geoapi.qweather.com/v2/city/lookup?location=浙江&adm=宁波&key=3ec50552d5e44e20b9d18088456f9c03
#      浙江省绍兴市诸暨市
#      "id": "101210502",
#      "lat": "29.71366",
#      "lon": "120.24432",

      "id": "101210404",
      "lat": "30.04540",
      "lon": "121.15630",
      "adm2": "宁波",
      "adm1": "浙江省",
###

###



###
POST http://nginx.dev.weiheng-tech.com:30231/api/v1/load/hourly
Content-Type: application/json

{
  "projectId": "4c9db86abb8141adb71e8f7d544d950b",
  "y": {
    "time_idx": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22,
      23
    ],
    "series": [
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1"
    ]
  }
}

###
###功率预测
POST  http://nginx.dev.weiheng-tech.com:30231/api/v1/tft/hourly
Content-Type: application/json

{
  "projectId": "4f537620d37d40e19dd25be5ca6ad941",
  "y": {
    "time_idx": [0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22,
      23
    ],
    "series": [
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1"
    ],
    "tempure": [12.709677419354838,
      12.625,
      11.666666666666666,
      11.941176470588236,
      11.314285714285715,
      11.88888888888889,
      11.864864864864865,
      12.842105263157896,
      14.102564102564102,
      15.75,
      17.0,
      18.585365853658537,
      19.0,
      19.902439024390244,
      20.902439024390244,
      19.658536585365855,
      18.902439024390244,
      17.48780487804878,
      15.902439024390244,
      14.585365853658537,
      14.195121951219512,
      13.829268292682928,
      12.829268292682928,
      12.0
    ],
    "windSpeed": [10.67741935483871,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      11.0,
      11.0,
      11.0,
      11.731707317073171,
      11.731707317073171,
      11.731707317073171,
      11.731707317073171,
      13.0,
      13.0,
      13.0,
      13.0,
      14.0,
      13.365853658536585,
      13.0,
      11.0,
      11.0
    ],
    "humidity": [80.41935483870968,
      79.6875,
      81.36363636363636,
      80.88235294117646,
      83.2,
      85.05555555555556,
      89.45945945945945,
      88.15789473684211,
      79.58974358974359,
      68.4,
      57.292682926829265,
      48.5609756097561,
      43.24390243902439,
      41.51219512195122,
      41.853658536585364,
      45.19512195121951,
      50.146341463414636,
      57.073170731707314,
      65.2439024390244,
      74.46341463414635,
      78.97560975609755,
      84.6829268292683,
      90.21951219512195,
      88.8780487804878
    ],
    "pressure": [1022.741935483871,
      1022.84375,
      1022.3333333333334,
      1022.0,
      1021.0,
      1020.0,
      1019.4594594594595,
      1019.0,
      1019.0,
      1019.0,
      1019.4146341463414,
      1019.9756097560976,
      1020.1219512195122,
      1020.2682926829268,
      1020.2926829268292,
      1020.0,
      1019.8536585365854,
      1019.0,
      1019.0,
      1018.560975609756,
      1018.560975609756,
      1019.439024390244,
      1019.6829268292682,
      1020.0
    ]
  }
}
###
###
#http://pangu-forecast.prod.weiheng-tech.com/docs#/default/easy_predict_api_v1_load_hourly_post
POST http://pangu-forecast.prod.weiheng-tech.com/api/v1/load/hourly
Content-Type: application/json

{
  "projectId": "f4477a16be694375b2e9778d2862c217",
  "y": {
    "time_idx": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22,
      23
    ],
    "series": [
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1"
    ]
  }
}
###

###
#http://pangu-forecast.prod.weiheng-tech.com/docs#/default/easy_predict_api_v1_load_hourly_post
POST http://pangu-forecast.prod.weiheng-tech.com/api/v1/load/2d
Content-Type: application/json

{
  "projectId": "f4477a16be694375b2e9778d2862c217",
  "y": {
    "time_idx": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22,
      23,
      24,
      25,
      26,
      27,
      28,
      29,
      30,
      31,
      32,
      33,
      34,
      35,
      36,
      37,
      38,
      39,
      40,
      41,
      42,
      43,
      44,
      45,
      46,
      47
    ],
    "series": [
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1"
    ]
  }
}
###



###
###功率预测
POST  http://pangu-forecast.prod.weiheng-tech.com/api/v1/tft/hourly
Content-Type: application/json

{
  "projectId": "f4477a16be694375b2e9778d2862c217",
  "y": {
    "time_idx": [0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22,
      23
    ],
    "series": [
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1",
      "1"
    ],
    "tempure": [12.709677419354838,
      12.625,
      11.666666666666666,
      11.941176470588236,
      11.314285714285715,
      11.88888888888889,
      11.864864864864865,
      12.842105263157896,
      14.102564102564102,
      15.75,
      17.0,
      18.585365853658537,
      19.0,
      19.902439024390244,
      20.902439024390244,
      19.658536585365855,
      18.902439024390244,
      17.48780487804878,
      15.902439024390244,
      14.585365853658537,
      14.195121951219512,
      13.829268292682928,
      12.829268292682928,
      12.0
    ],
    "windSpeed": [10.67741935483871,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      9.0,
      11.0,
      11.0,
      11.0,
      11.731707317073171,
      11.731707317073171,
      11.731707317073171,
      11.731707317073171,
      13.0,
      13.0,
      13.0,
      13.0,
      14.0,
      13.365853658536585,
      13.0,
      11.0,
      11.0
    ],
    "humidity": [80.41935483870968,
      79.6875,
      81.36363636363636,
      80.88235294117646,
      83.2,
      85.05555555555556,
      89.45945945945945,
      88.15789473684211,
      79.58974358974359,
      68.4,
      57.292682926829265,
      48.5609756097561,
      43.24390243902439,
      41.51219512195122,
      41.853658536585364,
      45.19512195121951,
      50.146341463414636,
      57.073170731707314,
      65.2439024390244,
      74.46341463414635,
      78.97560975609755,
      84.6829268292683,
      90.21951219512195,
      88.8780487804878
    ],
    "pressure": [1022.741935483871,
      1022.84375,
      1022.3333333333334,
      1022.0,
      1021.0,
      1020.0,
      1019.4594594594595,
      1019.0,
      1019.0,
      1019.0,
      1019.4146341463414,
      1019.9756097560976,
      1020.1219512195122,
      1020.2682926829268,
      1020.2926829268292,
      1020.0,
      1019.8536585365854,
      1019.0,
      1019.0,
      1018.560975609756,
      1018.560975609756,
      1019.439024390244,
      1019.6829268292682,
      1020.0
    ]
  }
}
###

###

### 光伏天气 24h 72h
GET https://api.qweather.com/v7/solar-radiation/72h?location=120.21,31.62&key=3ec50552d5e44e20b9d18088456f9c03
Accept: application/json

###
POST http://pangu-forecast.prod.weiheng-tech.com/api/v1/pv/hourly
Content-Type: application/json

{"y":{"tempure":[21.0,21.0,21.0,21.0,21.0,21.0,21.0,22.0,24.77777777777778,26.0,28.0,29.583333333333332,30.53846153846154,31.0,31.533333333333335,31.4375,30.41176470588235,29.38888888888889,27.94736842105263,26.36842105263158,24.105263157894736,23.57894736842105,22.210526315789473,21.57894736842105],"series":["1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1"],"direct":[0.0,0.0,0.0,0.0,0.0,0.0,0.0,11.923333333333332,46.71,94.59499999999998,249.675,329.275,373.83,492.87199999999996,251.07399999999998,162.13333333333335,114.65666666666668,58.05666666666667,8.506666666666666,0.0,0.0,0.0,0.0,0.0],"time_idx":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],"humidity":[68.0,67.0,66.0,66.0,66.0,68.0,66.0,63.875,57.55555555555556,51.5,46.81818181818182,43.5,40.76923076923077,40.0,39.6,40.8125,44.11764705882353,48.27777777777778,54.05263157894737,61.63157894736842,79.26315789473684,82.57894736842105,86.05263157894737,86.63157894736842],"pressure":[1009.0,1009.0,1009.0,1009.0,1008.0,1007.8333333333334,1007.0,1006.625,1006.5555555555555,1006.5,1007.0,1008.0,1008.8461538461538,1008.7857142857143,1008.3333333333334,1008.3125,1006.6470588235294,1006.0,1005.3157894736842,1005.0,1005.578947368421,1005.2631578947369,1005.2631578947369,1005.6842105263158],"diffuse":[0.0,0.0,0.0,0.0,0.0,0.0,7.62,87.39,188.15,272.67499999999995,310.1,346.31,367.932,319.104,368.95799999999997,301.65666666666664,225.26333333333335,151.05666666666667,75.89333333333333,5.66,0.0,0.0,0.0,0.0],"windSpeed":[9.0,9.0,9.0,9.0,9.0,9.0,9.0,9.0,9.0,9.0,11.0,11.0,9.0,7.0,5.0,9.0,11.0,13.0,13.0,13.0,13.0,11.0,11.0,11.0],"net":[0.0,0.0,0.0,0.0,0.0,0.0,6.650000000000001,85.96666666666665,203.58666666666667,318.54999999999995,487.835,590.025,649.552,712.116,540.78,404.17333333333335,296.28000000000003,181.1966666666667,73.12666666666667,4.94,0.0,0.0,0.0,0.0]},"projectId":"4f537620d37d40e19dd25be5ca6ad941"}

###
