CREATE TABLE t_group_demand_month_income (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id VARCHAR(255) NOT NULL COMMENT '分组id',
    project_id VARCHAR(255) NOT NULL COMMENT '项目id',
    demand_max_power DOUBLE COMMENT '最大需量月度',
    demand_income DOUBLE COMMENT '需量收益',
    demand_price DOUBLE COMMENT '需量价格',
    `month_time` bigint(20) DEFAULT NULL COMMENT '月度时间',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `create_by`   varchar(32)  DEFAULT NULL COMMENT '创建者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
    `update_by`   varchar(32)  DEFAULT NULL COMMENT '更新者'
) COMMENT='分组需量月度收益';

