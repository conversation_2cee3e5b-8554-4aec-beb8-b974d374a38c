---------------------------------------------------------------------------------------------------------------------
---------------------------------------------- 需量控制收益的需量--------------------------------------------------------
import "influxdata/influxdb/tasks"

option task = {
    name: "control_task",
    every: 30m,
}

control = from(bucket: "forever")
    |> range(start: tasks.lastSuccess(orTime: -task.every))
    |> filter(fn: (r) => r._measurement == "T3_5s")
    |> filter(fn: (r) => r["_field"] == "control_power" and r.type == "2")
    |> aggregateWindow(every: 15m, fn: last)
    |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
    |> group(columns: ["_time"], mode: "by")
    |> unique(column: "control_power")

original = () => {
    meter = from(bucket: "forever")
        |> range(start: tasks.lastSuccess(orTime: -task.every))
        |> filter(fn: (r) => r._measurement == "T3_5s")
        |> filter(fn: (r) => r["_field"] == "ac_history_negative_power_in_kwh" and r.type == "2")
        |> aggregateWindow(every: 15m, fn: last)
        |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> difference(columns: ["ac_history_negative_power_in_kwh"])
        |> group(columns: ["_time"], mode: "by")
        |> map(fn: (r) => ({r with ac_history_negative_power_in_kwh: r.ac_history_negative_power_in_kwh * 4.0}))
        |> sum(column: "ac_history_negative_power_in_kwh")

    ems = from(bucket: "forever")
        |> range(start: tasks.lastSuccess(orTime: -task.every))
        |> filter(fn: (r) => r._measurement == "T1_5s")
        |> filter(fn: (r) => r["_field"] == "ems_history_output_energy")
        |> aggregateWindow(every: 15m, fn: last)
        |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> difference(columns: ["ems_history_output_energy"])
        |> group(columns: ["_time"], mode: "by")
        |> map(fn: (r) => ({r with ems_history_output_energy: r.ems_history_output_energy * 4.0}))
        |> sum(column: "ems_history_output_energy")

    return join(tables: {meter, ems}, on: ["_time"])
        |> group(columns: ["_time"], mode: "by")
        |> map(fn: (r) => ({r with originalDemand: r.ems_history_output_energy + r.ac_history_negative_power_in_kwh}))
}

originalDemand = original()

join(tables: {originalDemand, control}, on: ["_time"])
    |> map(fn: (r) => ({r with max: if r.control_power > r.ac_history_negative_power_in_kwh then r.originalDemand - r.control_power else r.originalDemand - r.ac_history_negative_power_in_kwh}))
    |> map(fn: (r) => ({r with _value: if r.max > 0 then r.max else 0.0}))
    |> map(fn: (r) => ({r with _field: "real"}))
    |> drop(
        columns: [
            "ammeterId",
            "type",
            "originalDemand",
            "uuid",
            "ac_history_negative_power_in_kwh",
            "control_power",
            "ems_history_output_energy",
            "max",
        ],
    )
    |> to(bucket: "control")


---------------------------------------------------------------------------------------------------------------------
---------------------------------------------- 充放电功率1分钟平均值 ----------------------------------------------------
import "influxdata/influxdb/tasks"

option task = {
    name: "datagram_rate_mean_1m",
    every: 5m,
}

from(bucket: "ems")
    |> range(start: tasks.lastSuccess(orTime: -task.every))
    |> filter(fn: (r) => r._measurement == "T1_5s" or r._measurement == "T3_5s")
    |> filter(fn: (r) => r["_field"] == "ems_ac_active_power_pos" or r["_field"] == "ac_active_power" or r["_field"] == "ems_ac_active_power_neg" or r["_field"] == "ems_ac_active_power")
    |> aggregateWindow(every: 1m, fn: mean)
    |> to(bucket: "mean")


---------------------------------------------------------------------------------------------------------------------
---------------------------------------------- 数据存储5分钟采样 T0_1m T2_1m --------------------------------------------
import "influxdata/influxdb/tasks"

option task = {
    name: "ems_T0_T2_5m",
    every: 10m,
}

from(bucket: "ems")
    |> range(start: tasks.lastSuccess(orTime: -task.every))
    |> filter(fn: (r) => r._measurement == "T0_1m" or r._measurement == "T0_1m_asc" or r._measurement == "T2_1m")
    |> aggregateWindow(every: 5m, fn: last)
    |> to(bucket: "forever")


---------------------------------------------------------------------------------------------------------------------
---------------------------------------------- 数据存储1分钟采样 T1_5s与T3_5s-------------------------------------------
import "influxdata/influxdb/tasks"

option task = {
    name: "ems_T1_T3_1m",
    every: 5m,
}

from(bucket: "ems")
    |> range(start: tasks.lastSuccess(orTime: -task.every))
    |> filter(fn: (r) => r._measurement == "T1_5s" or r._measurement == "T3_5s")
    |> aggregateWindow(every: 1m, fn: last)
    |> to(bucket: "forever")