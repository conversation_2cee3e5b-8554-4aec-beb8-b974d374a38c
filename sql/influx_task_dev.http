
### 创建bucket ems_forever_dev
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/buckets
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "name": "ems_forever_dev",
  "retentionRules": [],
  "ruleType": null,
  "readableRetention": "forever",
  "orgID": "weiheng",
  "type": "user",
  "schemaType": "implicit"
}


### 创建bucket ems_mean_dev
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/buckets
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "name": "ems_mean_dev",
  "retentionRules": [],
  "ruleType": null,
  "readableRetention": "forever",
  "orgID": "weiheng",
  "type": "user",
  "schemaType": "implicit"
}

### 创建task  ems_meter_forever_dev
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/tasks
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "description": "",
  "flux": "import \"influxdata/influxdb/tasks\"\n\noption task = {\n    name: \"ems_meter_forever_dev\",\n    every: 5m,\n    offset: 1m,\n}\n\nfrom(bucket: \"ems_cloud_dev\")\n    |> range(start: tasks.lastSuccess(orTime: -30m))\n    |> filter(fn: (r) => r._measurement =~ /^ems100@.*/ or r._measurement =~ /^meter@.*/)\n    |> aggregateWindow(every: 1m, fn: last)\n    |> to(bucket: \"ems_forever_dev\")",
  "orgID": "weiheng",
  "status": "active"
}

### 创建task  non_ems_meter_forever_dev
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/tasks
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "description": "",
  "flux": "import \"influxdata/influxdb/tasks\"\n\noption task = {\n    name: \"non_ems_meter_forever_dev\",\n    every: 5m,\n    offset: 1m,\n}\n\nfrom(bucket: \"ems_cloud_dev\")\n    |> range(start: tasks.lastSuccess(orTime: -30m))\n    |> filter(fn: (r) => r._measurement !~ /^ems100@.*/ and r._measurement !~ /^meter@.*/)\n    |> aggregateWindow(every: 5m, fn: last)\n    |> to(bucket: \"ems_forever_dev\")",
  "orgID": "weiheng",
  "status": "active"
}

### 创建task  功率平均task power_mean_1m_dev
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/tasks
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "description": "",
  "flux": "import \"influxdata/influxdb/tasks\"\n\noption task = {\n    name: \"power_mean_1m_dev\",\n    every: 5m,\n    offset: 1m,\n}\n\nfrom(bucket: \"ems_cloud_dev\")\n    |> range(start: tasks.lastSuccess(orTime: -30m))\n    |> filter(fn: (r) => r._measurement =~ /^ems100@.*/ or r._measurement =~ /^meter@.*/)\n    |> filter(fn: (r) => r[\"_field\"] == \"ems_ac_active_power_pos\" or r[\"_field\"] == \"ems_ac_reactive_power\" or r[\"_field\"] == \"ac_active_power\" or r[\"_field\"] == \"ac_reactive_power\" or r[\"_field\"] == \"ems_ac_active_power_neg\" or r[\"_field\"] == \"ems_ac_active_power\" or r[\"_field\"] == \"ac_current\" or r[\"_field\"] == \"ac_currents_0\" or r[\"_field\"] == \"ac_currents_1\" or r[\"_field\"] == \"ac_currents_2\" or r[\"_field\"] == \"ac_active_powers_0\" or r[\"_field\"] == \"ac_active_powers_1\" or r[\"_field\"] == \"ac_active_powers_2\" or r[\"_field\"] == \"ac_reactive_powers_0\" or r[\"_field\"] == \"ac_reactive_powers_1\" or r[\"_field\"] == \"ac_reactive_powers_2\" or r[\"_field\"] == \"dc_voltage\" or r[\"_field\"] == \"dc_power\" or r[\"_field\"] == \"frequency\" or r[\"_field\"] == \"ac_voltage\" or r[\"_field\"] == \"ac_voltages_0\" or r[\"_field\"] == \"ac_voltages_1\" or r[\"_field\"] == \"ac_voltages_2\")\n    |> aggregateWindow(every: 1m, fn: mean)\n    |> to(bucket: \"ems_mean_dev\")",
  "orgID": "weiheng",
  "status": "active"
}

### 创建bucket ems_group_fvcc
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/buckets
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "name": "ems_group_fvcc",
  "retentionRules": [],
  "ruleType": null,
  "readableRetention": "forever",
  "orgID": "weiheng",
  "type": "user",
  "schemaType": "implicit"
}

### 创建task  ems_group_fvcc
POST http://influx-proxy.dev.weiheng-tech.com/api/v2/tasks
Content-Type: application/json
Authorization: Token 8ydw+mbBxkpzsrxy

{
  "description": "",
  "flux": "import \"influxdata/influxdb/tasks\"\n\noption task = {\n    name: \"ems_group_fvcc\",\n    every: 5m,\n    offset: 1m,\n}\n\nfrom(bucket: \"ems_cloud\")\n    |> range(start: tasks.lastSuccess(orTime: -10m))\n    |> filter(fn: (r) => r._measurement =~ /^group@.*/)\n    |> filter(fn: (r) => r._field == \"power_result_udp_ems_active\" or r._field == \"power_result_udp_ems_reactive\" or r._field == \"power_result_udp_active_power_command\" or r._field == \"power_result_udp_reactive_power_command\")\n    |> to(bucket: \"ems_group_fvcc\")",
  "orgID": "weiheng",
  "status": "active"
}


