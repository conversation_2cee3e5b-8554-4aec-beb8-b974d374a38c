------------------------------------------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------报表数据------------------------------------------------------------------------
       from(bucket: "forever")
        |> range(start: 2022-04-30T16:00:00Z, stop: 2022-05-31T06:05:28Z )
        |> filter(fn: (r) => r._measurement == "T1_5s")
        |> filter(fn: (r) => r["_field"] == "ems_history_output_energy"  )
        |> timeShift(duration: 8h, columns: ["_start", "_stop", "_time"])
        |> aggregateWindow(every: 1d, fn: last)
        |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
        |> fill(column: "ems_history_output_energy", value: 0.0)
        |> difference(columns:["ems_history_output_energy"], nonNegative: true)
        |> fill(column: "ems_history_output_energy", value: 0.0)
        |> group(columns: ["_time"], mode:"by")
        |> sum(column:"ems_history_output_energy")
        |> timeShift(duration: -8h, columns: ["_start", "_stop", "_time"])
        |> timeShift(duration: -1s, columns: ["_start", "_stop", "_time"])

------------------------------------------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------运营收益------------------------------------------------------------------------

import "date"
subrange = from(bucket: "forever")
  |> range(start: 2022-05-26T16:00:00Z, stop: 2022-05-27T15:59:59Z)
  |> filter(fn: (r) => r["_measurement"] == "T1_5s")
  |> filter(fn: (r) => r["_field"] == "ems_history_output_energy" or r["_field"] == "ems_history_input_energy" )
  |> timeShift(duration: 8h, columns: ["_start", "_stop", "_time"])
  |> filter(fn: (r) => date.hour(t: r["_time"]) >= 17 and date.hour(t: r["_time"]) <= 23)
  |> filter(fn: (r) => date.minute(t: r["_time"]) >= 0 and date.minute(t: r["_time"]) <= 59)

subrange
  |> window(every: 24h)
  |> difference()
  |> sum()
  |> group(columns: ["_field", "_start"], mode:"by")
  |> sum()
  |> pivot(rowKey: ["_start"], columnKey: ["_field"], valueColumn: "_value")
  |> fill(column: "ems_history_output_energy", value: 0.0 )
  |> fill(column: "ems_history_input_energy", value: 0.0)
  |> timeShift(duration: -8h, columns: ["_start", "_stop", "_time"])

------------------------------------------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------原需量曲线----------------------------------------------------------------------

  meter = from(bucket: "mean")
      |> range(start: 2022-06-01T16:00:00Z, stop: 2022-06-02T15:07:48Z)
      |> filter(fn: (r) => r._measurement == "T3_5s" )
      |> filter(fn: (r) => r["_field"] == "ac_active_power" )
      |> aggregateWindow(every: 1m, fn: last)
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> group(columns: ["_time"], mode:"by")
      |> sum(column: "ac_active_power")
      |> map(fn: (r) => ({r with ac_active_power: r.ac_active_power / 1000.0}))

   ems =  from(bucket: "forever")
      |> range(start: 2022-06-01T16:00:00Z, stop: 2022-06-02T15:07:48Z)
      |> filter(fn: (r) => r._measurement == "T1_5s")
      |> filter(fn: (r) => r["_field"] == "ems_ac_active_power_pos")
      |> aggregateWindow(every: 1m, fn: last)
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> group(columns: ["_time"], mode:"by")
      |> sum(column: "ems_ac_active_power_pos")
  	  join(
      tables: {meter, ems},
      on: ["_time"],
  )
   |> group(columns: ["_time"], mode:"by")
   |> map(fn: (r) => ({ r with originalDemand: r.ems_ac_active_power_pos -r.ac_active_power}))

------------------------------------------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------原需量曲线----------------------------------------------------------------------

  meter = from(bucket: "mean")
      |> range(start: 2022-06-01T16:00:00Z, stop: 2022-06-02T15:07:48Z)
      |> filter(fn: (r) => r._measurement == "T3_5s" )
      |> filter(fn: (r) => r["_field"] == "ac_active_power" )
      |> aggregateWindow(every: 1m, fn: last)
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> group(columns: ["_time"], mode:"by")
      |> sum(column: "ac_active_power")
      |> map(fn: (r) => ({r with ac_active_power: r.ac_active_power / 1000.0}))

   ems =  from(bucket: "forever")
      |> range(start: 2022-06-01T16:00:00Z, stop: 2022-06-02T15:07:48Z)
      |> filter(fn: (r) => r._measurement == "T1_5s")
      |> filter(fn: (r) => r["_field"] == "ems_ac_active_power_pos")
      |> aggregateWindow(every: 1m, fn: last)
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> group(columns: ["_time"], mode:"by")
      |> sum(column: "ems_ac_active_power_pos")
  	  join(
      tables: {meter, ems},
      on: ["_time"],
  )
   |> group(columns: ["_time"], mode:"by")
   |> map(fn: (r) => ({ r with originalDemand: r.ems_ac_active_power_pos -r.ac_active_power}))

------------------------------------------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------功率平均task--------------------------------------------------------------------

    import "influxdata/influxdb/tasks"

    option task = {
        name: "datagram_rate_mean_1m",
        every: 5m,
    }

    from(bucket: "ems")
        |> range(start: tasks.lastSuccess(orTime: -task.every))
        |> filter(fn: (r) => r._measurement == "T1_5s" or r._measurement == "T3_5s")
        |> filter(fn: (r) => r["_field"] == "ems_ac_active_power_pos" or r["_field"] == "ac_active_power" or r["_field"] == "ems_ac_active_power_neg" or r["_field"] == "ems_ac_active_power")
        |> aggregateWindow(every: 1m, fn: mean)
        |> to(bucket: "mean")

------------------------------------------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------需量控制task--------------------------------------------------------------------

     import "influxdata/influxdb/tasks"

     option task = {
         name: "control_task",
         every: 60m,
     }

     control = from(bucket: "forever")
         |> range(start: tasks.lastSuccess(orTime: -task.every))
         |> filter(fn: (r) => r._measurement == "T3_5s")
         |> filter(fn: (r) => r["_field"] == "control_power" and r.system == "1" and r.type == "2")
         |> aggregateWindow(every: 15m, fn: last)
         |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
         |> group(columns: ["_time"], mode: "by")
         |> unique(column: "control_power")

     original = () => {
         meter = from(bucket: "forever")
             |> range(start: tasks.lastSuccess(orTime: -task.every))
             |> filter(fn: (r) => r._measurement == "T3_5s")
             |> filter(fn: (r) => r["_field"] == "ac_history_negative_power_in_kwh" and r.system == "1" and r.type == "2")
             |> aggregateWindow(every: 15m, fn: last)
             |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
             |> difference(columns: ["ac_history_negative_power_in_kwh"])
             |> group(columns: ["_time"], mode: "by")
             |> map(fn: (r) => ({r with ac_history_negative_power_in_kwh: r.ac_history_negative_power_in_kwh * 4.0}))
             |> sum(column: "ac_history_negative_power_in_kwh")

         ems = from(bucket: "forever")
             |> range(start: tasks.lastSuccess(orTime: -task.every))
             |> filter(fn: (r) => r._measurement == "T1_5s")
             |> filter(fn: (r) => r["_field"] == "ems_history_output_energy")
             |> aggregateWindow(every: 15m, fn: last)
             |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
             |> difference(columns: ["ems_history_output_energy"])
             |> group(columns: ["_time"], mode: "by")
             |> map(fn: (r) => ({r with ems_history_output_energy: r.ems_history_output_energy * 4.0}))
             |> sum(column: "ems_history_output_energy")

         return join(tables: {meter, ems}, on: ["_time"])
             |> group(columns: ["_time"], mode: "by")
             |> map(fn: (r) => ({r with originalDemand: r.ems_history_output_energy + r.ac_history_negative_power_in_kwh}))
     }

     originalDemand = original()

     join(tables: {originalDemand, control}, on: ["_time"])
         |> map(fn: (r) => ({r with max: if r.control_power > r.ac_history_negative_power_in_kwh then r.originalDemand - r.control_power else r.originalDemand - r.ac_history_negative_power_in_kwh}))
         |> map(fn: (r) => ({r with _value: if r.max > 0 then r.max else 0.0}))
         |> map(fn: (r) => ({r with _field: "real"}))
         |> drop(
             columns: [
                 "ammeterId",
                 "type",
                 "originalDemand",
                 "uuid",
                 "ac_history_negative_power_in_kwh",
                 "control_power",
                 "ems_history_output_energy",
                 "max",
             ],
         )
         |> to(bucket: "control")


------------------------------------------------------------------------------------------------------------------------------------------------
--------------------------------------------------------------------删除数据----------------------------------------------------------------------
 influx delete --org wifo --bucket ems --token sLbiSBACzk058f1TFlJJeYKbQQmLjlsozyn9IGCOJfmb9RtA-XYx1VrrVNjnKWgBR6vX65Y7yNasnkaQWcbh6g== --host http://127.0.0.1:8086 -p 'deviceId="f3be02ec6d51432c8c8f798db6f300d1"' --start '2022-08-09T23:00:00Z' --stop '2022-08-25T23:00:00Z' --http-debug
 influx delete --org wifo --bucket forever --token sLbiSBACzk058f1TFlJJeYKbQQmLjlsozyn9IGCOJfmb9RtA-XYx1VrrVNjnKWgBR6vX65Y7yNasnkaQWcbh6g== --host http://127.0.0.1:8086 -p 'deviceId="f3be02ec6d51432c8c8f798db6f300d1"' --start '2022-08-09T23:00:00Z' --stop '2022-08-25T23:00:00Z' --http-debug
