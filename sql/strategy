    {
	//防逆流功率 默认传10
    "back_flow_limit_power": 0,
    //防逆流开      默认
	"back_flow_preventing": true,
	//120
    "direct_power_beat": 0,
	//默认true 超时停机
    "direct_power_beat_protect_enable": true,
	//外部控制器模式开关
    "direct_power_control": true,
	//外部控制器模式开关
    "direct_power_readonly": true,
	//默认0
    "direct_required_active_power": 0,
	//默认0
    "direct_required_reactive_power": 0,
	//放电soc下限
    "discharge_soc_low_limit": 0,
	//需量控制
    "required_power": 0,
	//开关
    "start": true,
	//
    "strategies": [
      {
        "end_minute": 0,
		规则 0 充电 1放电 2自发自用
        "function": 0,
        "power": 0,
        "start_minute": 0,
        "week": 0
      }
    ],
	策略优先级
    "strategy_priority": 0, 【43210】

    "total_strategies": strategies.size
	}
	"uuid"