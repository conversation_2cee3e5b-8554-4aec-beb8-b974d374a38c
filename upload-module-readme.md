# 文件上传模块实现说明

## 功能概述

本模块实现了基于阿里云OSS的文件上传功能，支持图片文件上传和项目文件管理。

## 实现的功能

### 1. 图片上传接口
- **接口路径**: `POST /upload/image`
- **功能**: 上传图片文件到阿里云OSS，并将文件信息保存到数据库
- **支持格式**: 所有图片格式（image/*）
- **文件大小限制**: 10MB
- **返回**: 文件访问URL和基本信息

### 2. 文件列表查询接口
- **接口路径**: `GET /upload/files`
- **功能**: 查询当前项目下的所有文件列表
- **返回**: 文件列表，包含文件URL、名称、大小等信息

## 技术实现

### 数据库设计
创建了 `t_project_file` 表，包含以下字段：
- `id`: 主键ID
- `project_id`: 项目ID
- `file_path`: 文件在OSS中的路径
- `file_name`: 原始文件名
- `file_size`: 文件大小
- `file_type`: 文件MIME类型
- `file_extension`: 文件扩展名
- 继承BaseEntity的审计字段

### 代码结构
```
ems-common/src/main/java/com/wifochina/modules/upload/
├── entity/
│   └── ProjectFileEntity.java          # 数据库实体类
├── mapper/
│   └── ProjectFileMapper.java          # MyBatis Mapper接口
├── service/
│   ├── ProjectFileService.java         # 服务接口
│   └── impl/
│       └── ProjectFileServiceImpl.java # 服务实现类
└── vo/
    ├── FileUploadVO.java               # 上传响应VO
    └── ProjectFileVO.java              # 文件信息VO

ems-site/src/main/java/com/wifochina/modules/upload/
└── controller/
    └── UploadController.java           # REST控制器
```

### 核心逻辑

#### 文件上传流程
1. 验证文件类型（仅支持图片）
2. 验证文件大小（限制10MB）
3. 生成唯一文件名（UUID + 原扩展名）
4. 上传到阿里云OSS（使用现有的OssMapper）
5. 保存文件信息到数据库
6. 返回文件访问URL

#### 文件路径规则
```
project/{projectId}/images/{uuid}{extension}
```

#### 项目ID获取
使用 `WebUtils.projectId.get()` 获取当前请求的项目ID

## 配置要求

### 数据库
执行 `sql/t_project_file.sql` 创建数据表

### 应用配置
确保以下配置正确：
```yaml
custom:
  ali:
    oss:
      bucket: your-bucket-name
      domain: https://your-oss-domain.com  # 可选，用于构建完整URL
```

## 使用示例

### 上传图片
```bash
curl -X POST \
  http://localhost:8080/upload/image \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@/path/to/image.jpg'
```

### 查询文件列表
```bash
curl -X GET http://localhost:8080/upload/files
```

## 安全考虑

1. **文件类型验证**: 仅允许图片文件上传
2. **文件大小限制**: 限制单个文件最大10MB
3. **项目隔离**: 文件按项目ID进行隔离存储
4. **唯一文件名**: 使用UUID避免文件名冲突

## 扩展建议

1. **文件删除功能**: 可以添加文件删除接口
2. **更多文件类型**: 根据需要支持更多文件类型
3. **文件预览**: 添加文件预览功能
4. **批量上传**: 支持批量文件上传
5. **权限控制**: 添加文件访问权限控制

## 测试

使用 `upload-api-test.http` 文件进行接口测试。

## 注意事项

1. 确保阿里云OSS配置正确
2. 确保数据库表已创建
3. 确保WebUtils.projectId能正确获取项目ID
4. 文件URL的构建依赖于OSS域名配置
