### 告警配置管理 API 测试文件

### 变量定义
@baseUrl = http://localhost:8808/api
@token = H4sIAAAAAAAAABWPyZKCMABEv4gpNMTlqCwxjECJWQwXi7AMCYhOOQjk60evr6tfV1dz2EhUqESFZ2rwIlbh9quaQ1Ny/IZ4SjzhxESA2POdhBRzQugYk2jGalTlJe6KFjaS048A5ChoJe+GT1bcts+Mdi3WdxWR3fRhgsdaAjaUQQwLkHaSvfsgfsglDDMXr3DLGmGynvlTR9Dfe8WGJcETCRpYtdBNUGdy0nDKQn1ZxijzFzrvUxiZ5nZiLMkujzFv0xXxMlfaP05FAyG4rerT17GntFijOgRqAyr5d3r0R34dpEqKTcGWSM6+5XI+wGjYf3sr/zes69rvzSv9yTOlX3qs3TsThivEsk1twTyjLepmzbcH+H66s+630b2amr3EEQSB9XL0FOnco0tqAfw4MJH67kR9MHSLu7PYn4YntGk/6D0or+v8eBvN84x2h7P9D1WUf3GTAQAA
@projectId = 4f537620d37d40e19dd25be5ca6ad941
@alarmId = 11
@notificationId = 13

### =========== AlarmConfigController 测试用例 ===========

### 1. 查询告警配置列表
GET {{baseUrl}}/alarm/config/list
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

###

### 2. 新增或更新告警配置 - 新增场景
POST {{baseUrl}}/alarm/config/switch
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "alarmContent": 0,
  "isEnabled": true,
  "profitDeviationCoefficient": 0.1,
  "downtimeThreshold": 30,
  "offlineTimeThreshold": 60,
  "efficiencyReminderThreshold": 0.8
}

###

### 3. 新增或更新告警配置 - 更新场景
POST {{baseUrl}}/alarm/config/switch
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "id": 11,
  "alarmContent": 0,
  "isEnabled": true,
  "profitDeviationCoefficient": 0.15,
  "downtimeThreshold": 45,
  "offlineTimeThreshold": 90,
  "efficiencyReminderThreshold": 0.85
}

###

### 4. 批量新增告警通知配置
POST {{baseUrl}}/alarm/notification/batch/add
Authorization: {{token}}
Content-Type: application/json

{
  "projectIds": ["4f537620d37d40e19dd25be5ca6ad941","3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "alarmContent": 0,
  "isEnabled": true,
  "profitDeviationCoefficient": 0.1,
  "downtimeThreshold": 30,
  "offlineTimeThreshold": 60,
  "efficiencyReminderThreshold": 0.8,
  "userName": "申潞杰",
  "notificationType": "0",
  "email": "<EMAIL>"
}

###

### 5. 批量编辑告警通知配置 - 编辑告警内容
POST {{baseUrl}}/alarm/notification/batch/edit
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "alarmContent": 3,
  "isEnabled": true,
  "profitDeviationCoefficient": 0.15,
  "downtimeThreshold": 45,
  "offlineTimeThreshold": 90,
  "efficiencyReminderThreshold": 0.85
}

###

### 6. 批量编辑告警通知配置 - 替换通知人员
POST {{baseUrl}}/alarm/notification/batch/edit
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "originalUserName": "李四",
  "newUserName": "李四",
  "originalPhone": "13800138000",
  "newPhone": "13900139000",
  "originalEmail": "<EMAIL>",
  "newEmail": "<EMAIL>"
}

###

### 7. 批量编辑告警通知配置 - 同时编辑告警内容和替换人员
POST {{baseUrl}}/alarm/notification/batch/edit
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "alarmContent": 3,
  "isEnabled": false,
  "profitDeviationCoefficient": 0.2,
  "downtimeThreshold": 60,
  "offlineTimeThreshold": 120,
  "efficiencyReminderThreshold": 0.9,
  "originalUserName": "李四",
  "newUserName": "王五",
  "originalPhone": "13900139000",
  "newPhone": "13700137000",
  "originalEmail": "<EMAIL>",
  "newEmail": "<EMAIL>"
}

###

### 8. 批量删除告警通知配置 - 按用户名删除
POST {{baseUrl}}/alarm/notification/batch/delete
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "userName": "王五"
}

###

### 9. 批量删除告警通知配置 - 按手机号删除
POST {{baseUrl}}/alarm/notification/batch/delete
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "phone": "13800138000"
}

###

### 10. 批量删除告警通知配置 - 按邮箱删除
POST {{baseUrl}}/alarm/notification/batch/delete
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["3931643d564f47fdb1c8548576df3108", "9d68ecb8873f4f5d84d7eb158c4e2149"],
  "email": "<EMAIL>"
}

###

### 11. 批量删除告警通知配置 - 多条件删除
POST {{baseUrl}}/alarm/notification/batch/delete
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "projectIds": ["project-1", "project-2"],
  "userName": "张三",
  "phone": "13800138000",
  "email": "<EMAIL>"
}

###

### =========== AlarmNotificationController 测试用例 ===========

### 12. 根据告警ID查询通知配置列表
GET {{baseUrl}}/alarm/notification/list?alarmId={{alarmId}}
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

###

### 14. 新增告警通知配置
POST {{baseUrl}}/alarm/notification/add
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "alarmId": {{alarmId}},
  "userName": "申潞杰",
  "notificationType": "0",
  "email": "<EMAIL>"
}

###

### 18. 更新告警通知配置
POST {{baseUrl}}/alarm/notification/update
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json

{
  "id": {{notificationId}},
  "alarmId": {{alarmId}},
  "userName": "申潞杰",
  "notificationType": "0",
  "phone": "13600136001",
  "email": "<EMAIL>"
}

###

### 20. 删除告警通知配置
POST {{baseUrl}}/alarm/notification/delete/15
Authorization: {{token}}
Projectid: {{projectId}}
Content-Type: application/json