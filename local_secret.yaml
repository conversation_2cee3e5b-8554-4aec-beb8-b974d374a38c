apiVersion: v1
items:
  - apiVersion: v1
    data:
      .dockerconfigjson: ****************************************************************************************************************************************************************************************
    kind: Secret
    metadata:
      annotations:
        kubectl.kubernetes.io/last-applied-configuration: |
          {"apiVersion":"v1","data":{".dockerconfigjson":"****************************************************************************************************************************************************************************************"},"kind":"Secret","metadata":{"annotations":{"kubesphere.io/creator":"admin","kubesphere.io/description":"镜像仓库"},"creationTimestamp":"2024-11-14T03:02:57Z","name":"harbor","namespace":"ems-cloud","resourceVersion":"103233","uid":"869e9296-dfb8-46b9-99f1-1a3d8d541002"},"type":"kubernetes.io/dockerconfigjson"}
        kubesphere.io/creator: admin
        kubesphere.io/description: 镜像仓库
      creationTimestamp: "2024-11-14T03:02:57Z"
      name: harbor
      namespace: ems-cloud-local
      resourceVersion: "604595"
      uid: 869e9296-dfb8-46b9-99f1-1a3d8d541002
    type: kubernetes.io/dockerconfigjson
kind: List
metadata:
  resourceVersion: ""
