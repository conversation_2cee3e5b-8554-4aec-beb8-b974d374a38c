package com.wifochina.realtimemodel.common

/**
 * Created on 2025/2/26 14:24.
 * <AUTHOR>
 */
data class DeviceRealTimeData(
    val ts: Long, val err: String, val data: IntArray?
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (other !is DeviceRealTimeData) return false
        return ts == other.ts && err == other.err && data.contentEquals(other.data)
    }

    override fun hashCode(): Int {
        var result = ts.hashCode()
        result = 31 * result + err.hashCode()
        result = 31 * result + data.contentHashCode()
        return result
    }
}
typealias RealTimeData = Map<String, DeviceRealTimeData>