package com.wifochina.realtimemodel.common;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2025/7/2 14:43.
 *
 * <AUTHOR>
 */
public class ReflectionUtils {

    public static Map<String, Object> meterFieldToCamelMap(Object obj) {
        Map<String, Object> result = new HashMap<>();
        Class<?> clazz = obj.getClass();

        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    String camelKey = underlineToCamel(field.getName());
                    result.put(camelKey, value);
                } catch (IllegalAccessException e) {
                    System.out.println("无法访问字段: " + field.getName());
                }
            }
            clazz = clazz.getSuperclass(); // 继续向上找父类字段
        }

        return result;
    }

    public static Object iMethodInvoke(int i, IProtocolData data, String pointName) {
        try {
            // 提取index
            String[] parts = pointName.split("_");
            if (parts.length < 3) {
                return null;
            }
            // 组装方法名，例如：getPcsIActivePower
            StringBuilder methodNameBuilder = new StringBuilder("get");
            // 拼接前缀部分：getPcs_I_
            methodNameBuilder.append(capitalize(parts[0]));
            // 拼接字段部分：DesignAcVoltage
            for (int j = 1; j < parts.length; j++) {
                if ("{i}".equalsIgnoreCase(parts[j])) {
                    methodNameBuilder.append("_I_");
                } else {
                    methodNameBuilder.append(capitalize(parts[j]));
                }
                // Pcs
            }
            Method method = data.getClass().getMethod(methodNameBuilder.toString(), int.class);
            return method.invoke(data, i);
        } catch (Exception e) {
            System.out.println("反射调用失败: " + e.getMessage());
            return null;
        }
    }

    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /** 把下划线字段转驼峰 例如 bms_state -> bmsState */
    private static String underlineToCamel(String field) {
        StringBuilder sb = new StringBuilder();
        boolean nextUpper = false;
        for (char c : field.toCharArray()) {
            if (c == '_') {
                nextUpper = true;
            } else {
                if (nextUpper) {
                    sb.append(Character.toUpperCase(c));
                    nextUpper = false;
                } else {
                    sb.append(c);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 根据下划线字段名列表，从对象中通过反射获取对应属性值
     *
     * @param obj 目标对象
     * @param pointColumns 下划线字段名列表
     * @return key为下划线字段名，value为对应属性值（找不到属性值为null）
     */
    public static Map<String, Object> fixedFieldInvoke(
            Object obj, List<String> pointColumns, boolean camel) {
        Map<String, Object> result = new HashMap<>();
        Class<?> clazz = obj.getClass();

        for (String pointColumn : pointColumns) {
            String fieldName = underlineToCamel(pointColumn);
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(obj);
                if (camel) {
                    result.put(fieldName, value);
                } else {
                    result.put(pointColumn, value);
                }
            } catch (NoSuchFieldException e) {
                // 找不到字段，放null或打印警告
                if (camel) {
                    result.put(fieldName, null);
                } else {
                    result.put(pointColumn, null);
                }
                System.out.println(
                        "Warning: No field '" + fieldName + "' in class " + clazz.getSimpleName());
            } catch (IllegalAccessException e) {
                result.put(pointColumn, null);
                System.out.println(
                        "Warning: Cannot access field '"
                                + fieldName
                                + "' in class "
                                + clazz.getSimpleName());
            }
        }
        return result;
    }

    public static Object getFieldValueByPointColumn(Object obj, String pointColumn) {
        if (obj == null || pointColumn == null) {
            return null;
        }

        String fieldName = underlineToCamel(pointColumn);
        Class<?> clazz = obj.getClass();

        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (NoSuchFieldException e) {
            System.out.println(
                    "Warning: No field '" + fieldName + "' in class " + clazz.getSimpleName());
        } catch (IllegalAccessException e) {
            System.out.println(
                    "Warning: Cannot access field '"
                            + fieldName
                            + "' in class "
                            + clazz.getSimpleName());
        }

        return null;
    }
}
