package com.wifochina.realtimemodel.common;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.Override;
import java.lang.String;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR> 2025-07-11 14:38:43
 */
@Getter
@Slf4j
public class WeihengEms100 implements IProtocolData {
    @JsonSerialize(using = VendorDataSerializer.class)
    public int[] data;

    private final int systemTypeCode;

    private final int pcsCount;

    private final int pcsTypeCode;

    private final int bmsCount;

    private final int bmsTypeCode;

    private final int airConditionerCount;

    private final int airConditionerTypeCode;

    private final int firefightingCount;

    private final int firefightingTypeCode;

    private final int pvMeterCount;

    private final int gridMeterCount;

    private final int loadMeterCount;

    private final int emsMeterCount;

    private final int systemRunStatus;

    private final int waterCoolerCount;

    private final int waterCoolerTypeCode;

    private final int systemOffGrid;

    private final int systemAutoGridOnoff;

    private final int dcdcMeterCount;

    private final String emsSerial;

    private final int emsSoftwareVersion;

    private final int emsHardwareVersion;

    private final int emsProtocolVersion;

    private final int emsDesignChargePower;

    private final int emsDesignDischargePower;

    private final int emsDesignStorageEnergy;

    private final int emsDesignPower;

    private final int emsChargePowerLimit;

    private final int emsDischargePowerLimit;

    private final int emsCapacityLimit;

    private final int gpsOnline;

    private final double gpsLatitude;

    private final double gpsLongitude;

    private final int emsState;

    private final double emsSoc;

    private final double emsSoh;

    private final float emsDcPower;

    private final float emsDcVoltage;

    private final float emsDcCurrent;

    private final float emsAcActivePower;

    private final float emsAcReactivePower;

    private final double emsAcFrequency;

    private final float emsAcActivePowerPos;

    private final float emsAcActivePowerNeg;

    private final float emsDcPowerPos;

    private final float emsDcPowerNeg;

    private final double emsHistoryOutputEnergy;

    private final double emsHistoryInputEnergy;

    private final int systemStartingOrStopping;

    private final int directPowerControlCounter;

    private final int systemStateBit0;

    private final int systemStateBit1;

    private final int systemStateBit2;

    private final int systemStateBit3;

    private final int systemAlarmBit0;

    private final int systemAlarmBit1;

    private final int systemFaultBit0;

    private final int systemFaultBit1;

    private final int systemFaultBit2;

    private final int systemFaultBit3;

    private final int emsUtcHigh;

    private final int emsUtcLow;

    private final int emsUptimeHigh;

    private final int emsUptimeLow;

    private final int emsHotBackupValid;

    private final int emsHotBackupRunningCode;

    private final int emsHotBackupLevel;

    private final double emsCpuLoad;

    private final double emsMemoryLoad;

    private final double emsDiskLoad;

    private final double pvPower;

    private final double pvHistoryEnergy;

    private final float gridActivePower;

    private final float gridReactivePower;

    private final float gridActivePowerPos;

    private final float gridActivePowerNeg;

    private final double gridHistoryEnergyPos;

    private final double gridHistoryEnergyNeg;

    private final float loadActivePower;

    private final float loadReactivePower;

    private final float loadActivePowerPos;

    private final float loadActivePowerNeg;

    private final double loadHistoryEnergyPos;

    private final double loadHistoryEnergyNeg;

    private final float dcdcMeterVoltage;

    private final float dcdcMeterCurrent;

    private final float dcdcMeterPower;

    private final double dcdcMeterHistoryEnergyPos;

    private final double dcdcMeterHistoryEnergyNeg;

    private final int bmsDesignEnergy;

    private final double bmsDesignMaxVoltage;

    private final double bmsDesignMinVoltage;

    private final double bmsDesignMaxDischargeCurrent;

    private final double bmsDesignMaxChargeCurrent;

    private final int bmsClusterCount;

    private final int bmsStackPerClusterCount;

    private final int bmsCellPerStackCount;

    private final int bmsTemperaturePerStackCount;

    private final String bmsVersion;

    private final double bmsSoc;

    private final double bmsSoh;

    private final int bmsDischargeableEnergy;

    private final int bmsChargeableEnergy;

    private final float bmsVoltage;

    private final float bmsCurrent;

    private final float bmsAirTemperature;

    private final double bmsCellHighVoltage;

    private final double bmsCellLowVoltage;

    private final int bmsCellHighVoltagePositioin;

    private final int bmsCellLowVoltagePosition;

    private final float bmsCellHighTemperature;

    private final float bmsCellLowTemperature;

    private final int bmsCellHighTemperaturePosition;

    private final int bmsCellLowTemperaturePosition;

    private final double bmsCellAverageVoltage;

    private final float bmsCellAverageTemperature;

    private final float bmsDischargeCurrentLimit;

    private final float bmsChargeCurrentLimit;

    private final double bmsHistoryChargeEnergy;

    private final double bmsHistoryDischargeEnergy;

    private final int bmsState;

    private final int bmsStateBit0;

    private final int bmsStateBit1;

    private final int bmsStateBit2;

    private final int bmsStateBit3;

    private final int bmsAlarmBit0;

    private final int bmsAlarmBit1;

    private final int bmsAlarmBit2;

    private final int bmsAlarmBit3;

    private final int bmsAlarmBit4;

    private final int bmsFaultBit0;

    private final int bmsFaultBit1;

    private final int bmsFaultBit2;

    private final int bmsFaultBit3;

    private final int bmsFaultBit4;

    private final int bmsFaultBit5;

    private final int bmsFaultBit6;

    private final int powerReadOnly;

    private final int setState;

    private final double setPower;

    private final double rightLimitPower;

    private final double leftLimitPower;

    private final double currentPower;

    private final int faultFlag;

    private final int state;

    private final int stateTick;

    private final int stateSubTick;

    private final double dcdcLimitPower;

    private final double dcdcCurrentPower;

    private final double meterControlPower;

    private final double setReactivePower;

    private final String ems100Version;

    private final float emsAcCurrentA;

    private final float emsAcCurrentB;

    private final float emsAcCurrentC;

    private final float emsAcVoltageAB;

    private final float emsAcVoltageBC;

    private final float emsAcVoltageCA;

    private final float emsAcVoltageA;

    private final float emsAcVoltageB;

    private final float emsAcVoltageC;

    private final float emsAcPowerFactor;

    private final float emsAcActivePowerA;

    private final float emsAcActivePowerB;

    private final float emsAcActivePowerC;

    private final float emsAcReactivePowerA;

    private final float emsAcReactivePowerB;

    private final float emsAcReactivePowerC;

    private final float emsAcPowerFactorA;

    private final float emsAcPowerFactorB;

    private final float emsAcPowerFactorC;

    private final int dcdcCount;

    private final int dcdcTypeCode;

    private final int statesCount;

    private final double gpsTriggerFailedCount;

    private final double gpsLoopFailedCount;

    private final double gpsLoopSuccessCount;

    private final double bmsTriggerFailedCount;

    private final double bmsLoopFailedCount;

    private final double bmsLoopSuccessCount;

    private final double emsMeterTriggerFailedCount;

    private final double emsMeterLoopFailedCount;

    private final double emsMeterLoopSuccessCount;

    private final double DCMeterTriggerFailedCount;

    private final double DCMeterLoopFailedCount;

    private final double DCMeterLoopSuccessCount;

    public WeihengEms100(int[] data) {
        this.data = data;
        this.systemTypeCode = systemTypeCode();
        this.pcsCount = pcsCount();
        this.pcsTypeCode = pcsTypeCode();
        this.bmsCount = bmsCount();
        this.bmsTypeCode = bmsTypeCode();
        this.airConditionerCount = airConditionerCount();
        this.airConditionerTypeCode = airConditionerTypeCode();
        this.firefightingCount = firefightingCount();
        this.firefightingTypeCode = firefightingTypeCode();
        this.pvMeterCount = pvMeterCount();
        this.gridMeterCount = gridMeterCount();
        this.loadMeterCount = loadMeterCount();
        this.emsMeterCount = emsMeterCount();
        this.systemRunStatus = systemRunStatus();
        this.waterCoolerCount = waterCoolerCount();
        this.waterCoolerTypeCode = waterCoolerTypeCode();
        this.systemOffGrid = systemOffGrid();
        this.systemAutoGridOnoff = systemAutoGridOnoff();
        this.dcdcMeterCount = dcdcMeterCount();
        this.emsSerial = emsSerial();
        this.emsSoftwareVersion = emsSoftwareVersion();
        this.emsHardwareVersion = emsHardwareVersion();
        this.emsProtocolVersion = emsProtocolVersion();
        this.emsDesignChargePower = emsDesignChargePower();
        this.emsDesignDischargePower = emsDesignDischargePower();
        this.emsDesignStorageEnergy = emsDesignStorageEnergy();
        this.emsDesignPower = emsDesignPower();
        this.emsChargePowerLimit = emsChargePowerLimit();
        this.emsDischargePowerLimit = emsDischargePowerLimit();
        this.emsCapacityLimit = emsCapacityLimit();
        this.gpsOnline = gpsOnline();
        this.gpsLatitude = gpsLatitude();
        this.gpsLongitude = gpsLongitude();
        this.emsState = emsState();
        this.emsSoc = emsSoc();
        this.emsSoh = emsSoh();
        this.emsDcPower = emsDcPower();
        this.emsDcVoltage = emsDcVoltage();
        this.emsDcCurrent = emsDcCurrent();
        this.emsAcActivePower = emsAcActivePower();
        this.emsAcReactivePower = emsAcReactivePower();
        this.emsAcFrequency = emsAcFrequency();
        this.emsAcActivePowerPos = emsAcActivePowerPos();
        this.emsAcActivePowerNeg = emsAcActivePowerNeg();
        this.emsDcPowerPos = emsDcPowerPos();
        this.emsDcPowerNeg = emsDcPowerNeg();
        this.emsHistoryOutputEnergy = emsHistoryOutputEnergy();
        this.emsHistoryInputEnergy = emsHistoryInputEnergy();
        this.systemStartingOrStopping = systemStartingOrStopping();
        this.directPowerControlCounter = directPowerControlCounter();
        this.systemStateBit0 = systemStateBit0();
        this.systemStateBit1 = systemStateBit1();
        this.systemStateBit2 = systemStateBit2();
        this.systemStateBit3 = systemStateBit3();
        this.systemAlarmBit0 = systemAlarmBit0();
        this.systemAlarmBit1 = systemAlarmBit1();
        this.systemFaultBit0 = systemFaultBit0();
        this.systemFaultBit1 = systemFaultBit1();
        this.systemFaultBit2 = systemFaultBit2();
        this.systemFaultBit3 = systemFaultBit3();
        this.emsUtcHigh = emsUtcHigh();
        this.emsUtcLow = emsUtcLow();
        this.emsUptimeHigh = emsUptimeHigh();
        this.emsUptimeLow = emsUptimeLow();
        this.emsHotBackupValid = emsHotBackupValid();
        this.emsHotBackupRunningCode = emsHotBackupRunningCode();
        this.emsHotBackupLevel = emsHotBackupLevel();
        this.emsCpuLoad = emsCpuLoad();
        this.emsMemoryLoad = emsMemoryLoad();
        this.emsDiskLoad = emsDiskLoad();
        this.pvPower = pvPower();
        this.pvHistoryEnergy = pvHistoryEnergy();
        this.gridActivePower = gridActivePower();
        this.gridReactivePower = gridReactivePower();
        this.gridActivePowerPos = gridActivePowerPos();
        this.gridActivePowerNeg = gridActivePowerNeg();
        this.gridHistoryEnergyPos = gridHistoryEnergyPos();
        this.gridHistoryEnergyNeg = gridHistoryEnergyNeg();
        this.loadActivePower = loadActivePower();
        this.loadReactivePower = loadReactivePower();
        this.loadActivePowerPos = loadActivePowerPos();
        this.loadActivePowerNeg = loadActivePowerNeg();
        this.loadHistoryEnergyPos = loadHistoryEnergyPos();
        this.loadHistoryEnergyNeg = loadHistoryEnergyNeg();
        this.dcdcMeterVoltage = dcdcMeterVoltage();
        this.dcdcMeterCurrent = dcdcMeterCurrent();
        this.dcdcMeterPower = dcdcMeterPower();
        this.dcdcMeterHistoryEnergyPos = dcdcMeterHistoryEnergyPos();
        this.dcdcMeterHistoryEnergyNeg = dcdcMeterHistoryEnergyNeg();
        this.bmsDesignEnergy = bmsDesignEnergy();
        this.bmsDesignMaxVoltage = bmsDesignMaxVoltage();
        this.bmsDesignMinVoltage = bmsDesignMinVoltage();
        this.bmsDesignMaxDischargeCurrent = bmsDesignMaxDischargeCurrent();
        this.bmsDesignMaxChargeCurrent = bmsDesignMaxChargeCurrent();
        this.bmsClusterCount = bmsClusterCount();
        this.bmsStackPerClusterCount = bmsStackPerClusterCount();
        this.bmsCellPerStackCount = bmsCellPerStackCount();
        this.bmsTemperaturePerStackCount = bmsTemperaturePerStackCount();
        this.bmsVersion = bmsVersion();
        this.bmsSoc = bmsSoc();
        this.bmsSoh = bmsSoh();
        this.bmsDischargeableEnergy = bmsDischargeableEnergy();
        this.bmsChargeableEnergy = bmsChargeableEnergy();
        this.bmsVoltage = bmsVoltage();
        this.bmsCurrent = bmsCurrent();
        this.bmsAirTemperature = bmsAirTemperature();
        this.bmsCellHighVoltage = bmsCellHighVoltage();
        this.bmsCellLowVoltage = bmsCellLowVoltage();
        this.bmsCellHighVoltagePositioin = bmsCellHighVoltagePositioin();
        this.bmsCellLowVoltagePosition = bmsCellLowVoltagePosition();
        this.bmsCellHighTemperature = bmsCellHighTemperature();
        this.bmsCellLowTemperature = bmsCellLowTemperature();
        this.bmsCellHighTemperaturePosition = bmsCellHighTemperaturePosition();
        this.bmsCellLowTemperaturePosition = bmsCellLowTemperaturePosition();
        this.bmsCellAverageVoltage = bmsCellAverageVoltage();
        this.bmsCellAverageTemperature = bmsCellAverageTemperature();
        this.bmsDischargeCurrentLimit = bmsDischargeCurrentLimit();
        this.bmsChargeCurrentLimit = bmsChargeCurrentLimit();
        this.bmsHistoryChargeEnergy = bmsHistoryChargeEnergy();
        this.bmsHistoryDischargeEnergy = bmsHistoryDischargeEnergy();
        this.bmsState = bmsState();
        this.bmsStateBit0 = bmsStateBit0();
        this.bmsStateBit1 = bmsStateBit1();
        this.bmsStateBit2 = bmsStateBit2();
        this.bmsStateBit3 = bmsStateBit3();
        this.bmsAlarmBit0 = bmsAlarmBit0();
        this.bmsAlarmBit1 = bmsAlarmBit1();
        this.bmsAlarmBit2 = bmsAlarmBit2();
        this.bmsAlarmBit3 = bmsAlarmBit3();
        this.bmsAlarmBit4 = bmsAlarmBit4();
        this.bmsFaultBit0 = bmsFaultBit0();
        this.bmsFaultBit1 = bmsFaultBit1();
        this.bmsFaultBit2 = bmsFaultBit2();
        this.bmsFaultBit3 = bmsFaultBit3();
        this.bmsFaultBit4 = bmsFaultBit4();
        this.bmsFaultBit5 = bmsFaultBit5();
        this.bmsFaultBit6 = bmsFaultBit6();
        this.powerReadOnly = powerReadOnly();
        this.setState = setState();
        this.setPower = setPower();
        this.rightLimitPower = rightLimitPower();
        this.leftLimitPower = leftLimitPower();
        this.currentPower = currentPower();
        this.faultFlag = faultFlag();
        this.state = state();
        this.stateTick = stateTick();
        this.stateSubTick = stateSubTick();
        this.dcdcLimitPower = dcdcLimitPower();
        this.dcdcCurrentPower = dcdcCurrentPower();
        this.meterControlPower = meterControlPower();
        this.setReactivePower = setReactivePower();
        this.ems100Version = ems100Version();
        this.emsAcCurrentA = emsAcCurrentA();
        this.emsAcCurrentB = emsAcCurrentB();
        this.emsAcCurrentC = emsAcCurrentC();
        this.emsAcVoltageAB = emsAcVoltageAB();
        this.emsAcVoltageBC = emsAcVoltageBC();
        this.emsAcVoltageCA = emsAcVoltageCA();
        this.emsAcVoltageA = emsAcVoltageA();
        this.emsAcVoltageB = emsAcVoltageB();
        this.emsAcVoltageC = emsAcVoltageC();
        this.emsAcPowerFactor = emsAcPowerFactor();
        this.emsAcActivePowerA = emsAcActivePowerA();
        this.emsAcActivePowerB = emsAcActivePowerB();
        this.emsAcActivePowerC = emsAcActivePowerC();
        this.emsAcReactivePowerA = emsAcReactivePowerA();
        this.emsAcReactivePowerB = emsAcReactivePowerB();
        this.emsAcReactivePowerC = emsAcReactivePowerC();
        this.emsAcPowerFactorA = emsAcPowerFactorA();
        this.emsAcPowerFactorB = emsAcPowerFactorB();
        this.emsAcPowerFactorC = emsAcPowerFactorC();
        this.dcdcCount = dcdcCount();
        this.dcdcTypeCode = dcdcTypeCode();
        this.statesCount = statesCount();
        this.gpsTriggerFailedCount = gpsTriggerFailedCount();
        this.gpsLoopFailedCount = gpsLoopFailedCount();
        this.gpsLoopSuccessCount = gpsLoopSuccessCount();
        this.bmsTriggerFailedCount = bmsTriggerFailedCount();
        this.bmsLoopFailedCount = bmsLoopFailedCount();
        this.bmsLoopSuccessCount = bmsLoopSuccessCount();
        this.emsMeterTriggerFailedCount = emsMeterTriggerFailedCount();
        this.emsMeterLoopFailedCount = emsMeterLoopFailedCount();
        this.emsMeterLoopSuccessCount = emsMeterLoopSuccessCount();
        this.DCMeterTriggerFailedCount = DCMeterTriggerFailedCount();
        this.DCMeterLoopFailedCount = DCMeterLoopFailedCount();
        this.DCMeterLoopSuccessCount = DCMeterLoopSuccessCount();
    }

    @Override
    @NotNull
    public String vendorType() {
        return "WeihengEms100";
    }

    @Override
    @NotNull
    public int[] data() {
        return data;
    }

    /** 系统类型码 */
    private int systemTypeCode() {
        if (data != null) {
            return data[0];
        }
        return 0;
    }

    /** PCS数量(PCS_COUNT <= 5) */
    private int pcsCount() {
        if (data != null) {
            return data[1];
        }
        return 0;
    }

    /** PCS类型码 */
    private int pcsTypeCode() {
        if (data != null) {
            return data[2];
        }
        return 0;
    }

    /** BMS数量(BMS_COUNT <= 1) */
    private int bmsCount() {
        if (data != null) {
            return data[3];
        }
        return 0;
    }

    /** BMS类型码 */
    private int bmsTypeCode() {
        if (data != null) {
            return data[4];
        }
        return 0;
    }

    /** 空调数量(AIR_COUNT <= 5) */
    private int airConditionerCount() {
        if (data != null) {
            return data[5];
        }
        return 0;
    }

    /** 空调类型码 */
    private int airConditionerTypeCode() {
        if (data != null) {
            return data[6];
        }
        return 0;
    }

    /** 消防设备数量(FIRE_COUNT <= 5) */
    private int firefightingCount() {
        if (data != null) {
            return data[7];
        }
        return 0;
    }

    /** 消防类型码 */
    private int firefightingTypeCode() {
        if (data != null) {
            return data[8];
        }
        return 0;
    }

    /** PV电表数量( <= 1) */
    private int pvMeterCount() {
        if (data != null) {
            return data[9];
        }
        return 0;
    }

    /** 并网点电表数量( <= 1) */
    private int gridMeterCount() {
        if (data != null) {
            return data[10];
        }
        return 0;
    }

    /** 负载电表数量(<=1) */
    private int loadMeterCount() {
        if (data != null) {
            return data[11];
        }
        return 0;
    }

    /** EMS电表数量 */
    private int emsMeterCount() {
        if (data != null) {
            return data[12];
        }
        return 0;
    }

    /** 系统开关状态 */
    private int systemRunStatus() {
        if (data != null) {
            return data[13];
        }
        return 0;
    }

    /** 水冷机数量(WATER_COUNT <= 5) */
    private int waterCoolerCount() {
        if (data != null) {
            return data[14];
        }
        return 0;
    }

    /** 水冷机类型码 */
    private int waterCoolerTypeCode() {
        if (data != null) {
            return data[15];
        }
        return 0;
    }

    /** 系统并离网状态 */
    private int systemOffGrid() {
        if (data != null) {
            return data[16];
        }
        return 0;
    }

    /** 系统自动并离网 */
    private int systemAutoGridOnoff() {
        if (data != null) {
            return data[17];
        }
        return 0;
    }

    /** DCDC光伏侧直流电表数量 */
    private int dcdcMeterCount() {
        if (data != null) {
            return data[18];
        }
        return 0;
    }

    /** EMS序列号 */
    public String emsSerial() {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 18; i++) {
                if (data[+i] == 0) {
                    continue;
                }
                value.append((char) (data[+i] >> 8));
                value.append((char) (data[+i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** EMS软件版本号 */
    private int emsSoftwareVersion() {
        if (data != null) {
            return data[40];
        }
        return 0;
    }

    /** EMS硬件版本号 */
    private int emsHardwareVersion() {
        if (data != null) {
            return data[41];
        }
        return 0;
    }

    /** EMS通信协议版本号 */
    private int emsProtocolVersion() {
        if (data != null) {
            return data[42];
        }
        return 0;
    }

    /** EMS总充电能力(kW) */
    private int emsDesignChargePower() {
        if (data != null) {
            return data[43];
        }
        return 0;
    }

    /** EMS总放电能力(kW) */
    private int emsDesignDischargePower() {
        if (data != null) {
            return data[44];
        }
        return 0;
    }

    /** EMS设计存储能力(kWh) */
    private int emsDesignStorageEnergy() {
        if (data != null) {
            return data[45];
        }
        return 0;
    }

    /** EMS设计功率(kW) */
    private int emsDesignPower() {
        if (data != null) {
            return data[46];
        }
        return 0;
    }

    /** EMS充电能力(kW) 随状态变化 */
    private int emsChargePowerLimit() {
        if (data != null) {
            return data[47];
        }
        return 0;
    }

    /** EMS放电能力(kW) 随状态变化 */
    private int emsDischargePowerLimit() {
        if (data != null) {
            return data[48];
        }
        return 0;
    }

    /** EMS当前可用容量(kVA)随状态变化 */
    private int emsCapacityLimit() {
        if (data != null) {
            return data[49];
        }
        return 0;
    }

    /** GPS在线状态 */
    private int gpsOnline() {
        if (data != null) {
            return data[50];
        }
        return 0;
    }

    /** GPS纬度 */
    private double gpsLatitude() {
        if (data != null && data.length > (51 + 1)) {
            int high = data[51];
            int low = data[51 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1000000;
        }
        return 0;
    }

    /** GPS经度 */
    private double gpsLongitude() {
        if (data != null && data.length > (53 + 1)) {
            int high = data[53];
            int low = data[53 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1000000;
        }
        return 0;
    }

    /** EMS状态(0:正常,1:告警,2:故障) */
    private int emsState() {
        if (data != null) {
            return data[60];
        }
        return 0;
    }

    /** SOC(0~100) */
    private double emsSoc() {
        if (data != null) {
            return (double) data[61] / 10;
        }
        return 0;
    }

    /** SOH(0~100) */
    private double emsSoh() {
        if (data != null) {
            return (double) data[62] / 10;
        }
        return 0;
    }

    /** 直流侧功率(kW) 正为放电 */
    private float emsDcPower() {
        if (data != null) {
            return (float) ((short) data[63]) / 10;
        }
        return (short) 0;
    }

    /** 直流侧电压(V) */
    private float emsDcVoltage() {
        if (data != null) {
            return (float) ((short) data[64]) / 10;
        }
        return (short) 0;
    }

    /** 直流侧电流(A) 正为放电 */
    private float emsDcCurrent() {
        if (data != null) {
            return (float) ((short) data[65]) / 10;
        }
        return (short) 0;
    }

    /** 电网侧功率(kW) 正为放电 */
    private float emsAcActivePower() {
        if (data != null) {
            return (float) ((short) data[66]) / 10;
        }
        return (short) 0;
    }

    /** 电网侧无功功率(kVA) 正为输出 */
    private float emsAcReactivePower() {
        if (data != null) {
            return (float) ((short) data[67]) / 10;
        }
        return (short) 0;
    }

    /** 电网频率(Hz) */
    private double emsAcFrequency() {
        if (data != null) {
            return (double) data[68] / 100;
        }
        return 0;
    }

    /** 电网侧功率(kW) 正部分 */
    private float emsAcActivePowerPos() {
        if (data != null) {
            return (float) ((short) data[69]) / 10;
        }
        return (short) 0;
    }

    /** 电网侧功率(kW) 负部分 */
    private float emsAcActivePowerNeg() {
        if (data != null) {
            return (float) ((short) data[70]) / 10;
        }
        return (short) 0;
    }

    /** 直流侧功率(kW) 正部分 */
    private float emsDcPowerPos() {
        if (data != null) {
            return (float) ((short) data[71]) / 10;
        }
        return (short) 0;
    }

    /** 直流侧功率(kW) 负部分 */
    private float emsDcPowerNeg() {
        if (data != null) {
            return (float) ((short) data[72]) / 10;
        }
        return (short) 0;
    }

    /** EMS历史输出能量(kWh) */
    private double emsHistoryOutputEnergy() {
        if (data != null && data.length > (73 + 1)) {
            int high = data[73];
            int low = data[73 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** EMS历史输入能量(kWh) */
    private double emsHistoryInputEnergy() {
        if (data != null && data.length > (75 + 1)) {
            int high = data[75];
            int low = data[75 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 系统启动或者关闭中标志位 */
    private int systemStartingOrStopping() {
        if (data != null) {
            return data[77];
        }
        return 0;
    }

    /** 直接控制接收计数器(大于0时说明有数据在接收) */
    private int directPowerControlCounter() {
        if (data != null) {
            return data[78];
        }
        return 0;
    }

    /** SYSTEM状态BIT位0 */
    private int systemStateBit0() {
        if (data != null) {
            return data[80];
        }
        return 0;
    }

    /** SYSTEM状态BIT位1 */
    private int systemStateBit1() {
        if (data != null) {
            return data[81];
        }
        return 0;
    }

    /** SYSTEM状态BIT位2 */
    private int systemStateBit2() {
        if (data != null) {
            return data[82];
        }
        return 0;
    }

    /** SYSTEM状态BIT位3 */
    private int systemStateBit3() {
        if (data != null) {
            return data[83];
        }
        return 0;
    }

    /** SYSTEM告警BIT位0 */
    private int systemAlarmBit0() {
        if (data != null) {
            return data[84];
        }
        return 0;
    }

    /** SYSTEM告警BIT位1 */
    private int systemAlarmBit1() {
        if (data != null) {
            return data[85];
        }
        return 0;
    }

    /** SYSTEM故障BIT位0 */
    private int systemFaultBit0() {
        if (data != null) {
            return data[86];
        }
        return 0;
    }

    /** SYSTEM故障BIT位1 */
    private int systemFaultBit1() {
        if (data != null) {
            return data[87];
        }
        return 0;
    }

    /** SYSTEM故障BIT位2 */
    private int systemFaultBit2() {
        if (data != null) {
            return data[88];
        }
        return 0;
    }

    /** SYSTEM故障BIT位3 */
    private int systemFaultBit3() {
        if (data != null) {
            return data[89];
        }
        return 0;
    }

    /** 32位UTC时间的高16位 */
    private int emsUtcHigh() {
        if (data != null) {
            return data[90];
        }
        return 0;
    }

    /** 32位UTC时间的低16位 */
    private int emsUtcLow() {
        if (data != null) {
            return data[91];
        }
        return 0;
    }

    /** 32位启动毫秒数的高16位 */
    private int emsUptimeHigh() {
        if (data != null) {
            return data[92];
        }
        return 0;
    }

    /** 32位启动毫秒数的低16位 */
    private int emsUptimeLow() {
        if (data != null) {
            return data[93];
        }
        return 0;
    }

    /** 是否配备热备(1有效) */
    private int emsHotBackupValid() {
        if (data != null) {
            return data[94];
        }
        return 0;
    }

    /** 热备运行中机器编号 */
    private int emsHotBackupRunningCode() {
        if (data != null) {
            return data[95];
        }
        return 0;
    }

    /** 热备当前等级(0: 无法切换, 1: 可实时切换) */
    private int emsHotBackupLevel() {
        if (data != null) {
            return data[96];
        }
        return 0;
    }

    /** CPU负载(0-100) */
    private double emsCpuLoad() {
        if (data != null) {
            return (double) data[97] / 10;
        }
        return 0;
    }

    /** 内存负载(0-100) */
    private double emsMemoryLoad() {
        if (data != null) {
            return (double) data[98] / 10;
        }
        return 0;
    }

    /** 磁盘负载(0-100) */
    private double emsDiskLoad() {
        if (data != null) {
            return (double) data[99] / 10;
        }
        return 0;
    }

    /** 温度(℃) */
    public float getAirConditioner_I_Temperature(int index) {
        if (data != null) {
            return (float) ((short) data[100 + 10 * index + 0]) / 10;
        }
        return (short) 0;
    }

    /** 湿度(%) */
    public double getAirConditioner_I_Humidity(int index) {
        if (data != null) {
            return (double) data[100 + 10 * index + 1] / 10;
        }
        return 0;
    }

    /** 状态BIT位0 */
    public int getAirConditioner_I_StateBit0(int index) {
        if (data != null) {
            return data[100 + 10 * index + 2];
        }
        return 0;
    }

    /** 状态BIT位1 */
    public int getAirConditioner_I_StateBit1(int index) {
        if (data != null) {
            return data[100 + 10 * index + 3];
        }
        return 0;
    }

    /** 故障BIT位0 */
    public int getAirConditioner_I_FaultBit0(int index) {
        if (data != null) {
            return data[100 + 10 * index + 4];
        }
        return 0;
    }

    /** 故障BIT位1 */
    public int getAirConditioner_I_FaultBit1(int index) {
        if (data != null) {
            return data[100 + 10 * index + 5];
        }
        return 0;
    }

    /** 故障BIT位2 */
    public int getAirConditioner_I_FaultBit2(int index) {
        if (data != null) {
            return data[100 + 10 * index + 6];
        }
        return 0;
    }

    /** 故障BIT位3 */
    public int getAirConditioner_I_FaultBit3(int index) {
        if (data != null) {
            return data[100 + 10 * index + 7];
        }
        return 0;
    }

    /** 状态BIT位0 */
    public int getFirefighting_I_StateBit0(int index) {
        if (data != null) {
            return data[150 + 10 * index + 0];
        }
        return 0;
    }

    /** 状态BIT位1 */
    public int getFirefighting_I_StateBit1(int index) {
        if (data != null) {
            return data[150 + 10 * index + 1];
        }
        return 0;
    }

    /** 故障BIT位0 */
    public int getFirefighting_I_FaultBit0(int index) {
        if (data != null) {
            return data[150 + 10 * index + 2];
        }
        return 0;
    }

    /** 故障BIT位1 */
    public int getFirefighting_I_FaultBit1(int index) {
        if (data != null) {
            return data[150 + 10 * index + 3];
        }
        return 0;
    }

    /** 故障BIT位2 */
    public int getFirefighting_I_FaultBit2(int index) {
        if (data != null) {
            return data[150 + 10 * index + 4];
        }
        return 0;
    }

    /** 故障BIT位3 */
    public int getFirefighting_I_FaultBit3(int index) {
        if (data != null) {
            return data[150 + 10 * index + 5];
        }
        return 0;
    }

    /** 环境温度(℃) */
    public float getFirefighting_I_Temperature(int index) {
        if (data != null) {
            return (float) ((short) data[150 + 10 * index + 6]) / 10;
        }
        return (short) 0;
    }

    /** 一氧化碳浓度(ppm) */
    public double getFirefighting_I_CarbonMonoxideConcentration(int index) {
        if (data != null) {
            return (double) data[150 + 10 * index + 7] / 10;
        }
        return 0;
    }

    /** VOC(V) */
    public double getFirefighting_I_Voc(int index) {
        if (data != null) {
            return (double) data[150 + 10 * index + 8] / 1000;
        }
        return 0;
    }

    /** PV功率(kW) */
    private double pvPower() {
        if (data != null) {
            return (double) data[200] / 10;
        }
        return 0;
    }

    /** PV历史能量(kWh) */
    private double pvHistoryEnergy() {
        if (data != null && data.length > (201 + 1)) {
            int high = data[201];
            int low = data[201 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 并网点功率(kW) 正为向电网送能量 */
    private float gridActivePower() {
        if (data != null) {
            return (float) ((short) data[210]) / 10;
        }
        return (short) 0;
    }

    /** 并网点无功功率(kVA) 正为向电网发无功 */
    private float gridReactivePower() {
        if (data != null) {
            return (float) ((short) data[211]) / 10;
        }
        return (short) 0;
    }

    /** 并网点功率(kW) 正部分 */
    private float gridActivePowerPos() {
        if (data != null) {
            return (float) ((short) data[212]) / 10;
        }
        return (short) 0;
    }

    /** 并网点功率(kw) 负部分 */
    private float gridActivePowerNeg() {
        if (data != null) {
            return (float) ((short) data[213]) / 10;
        }
        return (short) 0;
    }

    /** 并网点历史能量(kWh) 正部分 */
    private double gridHistoryEnergyPos() {
        if (data != null && data.length > (214 + 1)) {
            int high = data[214];
            int low = data[214 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 并网点历史能量(kwh) 负部分 */
    private double gridHistoryEnergyNeg() {
        if (data != null && data.length > (216 + 1)) {
            int high = data[216];
            int low = data[216 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 负载有功功率(kW) */
    private float loadActivePower() {
        if (data != null) {
            return (float) ((short) data[220]) / 10;
        }
        return (short) 0;
    }

    /** 负载无功功率(kVA) */
    private float loadReactivePower() {
        if (data != null) {
            return (float) ((short) data[221]) / 10;
        }
        return (short) 0;
    }

    /** 负载功率(kW) 正部分 */
    private float loadActivePowerPos() {
        if (data != null) {
            return (float) ((short) data[222]) / 10;
        }
        return (short) 0;
    }

    /** 负载功率(kW) 负部分 */
    private float loadActivePowerNeg() {
        if (data != null) {
            return (float) ((short) data[223]) / 10;
        }
        return (short) 0;
    }

    /** 负载历史能量(kWh) 正部分 */
    private double loadHistoryEnergyPos() {
        if (data != null && data.length > (224 + 1)) {
            int high = data[224];
            int low = data[224 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 负载历史能量(kwh) 负部分 */
    private double loadHistoryEnergyNeg() {
        if (data != null && data.length > (226 + 1)) {
            int high = data[226];
            int low = data[226 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** DCDC光伏侧电压(V) */
    private float dcdcMeterVoltage() {
        if (data != null) {
            return (float) ((short) data[230]) / 10;
        }
        return (short) 0;
    }

    /** DCDC光伏侧电流(A) */
    private float dcdcMeterCurrent() {
        if (data != null) {
            return (float) ((short) data[231]) / 10;
        }
        return (short) 0;
    }

    /** DCDC光伏侧功率(kW) */
    private float dcdcMeterPower() {
        if (data != null) {
            return (float) ((short) data[232]) / 10;
        }
        return (short) 0;
    }

    /** DCDC光伏侧历史输出能量(kWh) */
    private double dcdcMeterHistoryEnergyPos() {
        if (data != null && data.length > (233 + 1)) {
            int high = data[233];
            int low = data[233 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** DCDC光伏侧历史输入能量(kWh) */
    private double dcdcMeterHistoryEnergyNeg() {
        if (data != null && data.length > (235 + 1)) {
            int high = data[235];
            int low = data[235 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 出水温度(℃) */
    public float getWaterCooler_I_OutletTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[250 + 10 * index + 0]) / 10;
        }
        return (short) 0;
    }

    /** 回水温度(℃) */
    public float getWaterCooler_I_ReturnTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[250 + 10 * index + 1]) / 10;
        }
        return (short) 0;
    }

    /** 出水压力(Bar) */
    public float getWaterCooler_I_OutletPressure(int index) {
        if (data != null) {
            return (float) ((short) data[250 + 10 * index + 2]) / 10;
        }
        return (short) 0;
    }

    /** 回水压力(Bar) */
    public float getWaterCooler_I_ReturnPressure(int index) {
        if (data != null) {
            return (float) ((short) data[250 + 10 * index + 3]) / 10;
        }
        return (short) 0;
    }

    /** 状态BIT位0 */
    public int getWaterCooler_I_StateBit0(int index) {
        if (data != null) {
            return data[250 + 10 * index + 4];
        }
        return 0;
    }

    /** 状态BIT位1 */
    public int getWaterCooler_I_StateBit1(int index) {
        if (data != null) {
            return data[250 + 10 * index + 5];
        }
        return 0;
    }

    /** 故障BIT位0 */
    public int getWaterCooler_I_FaultBit0(int index) {
        if (data != null) {
            return data[250 + 10 * index + 6];
        }
        return 0;
    }

    /** 故障BIT位1 */
    public int getWaterCooler_I_FaultBit1(int index) {
        if (data != null) {
            return data[250 + 10 * index + 7];
        }
        return 0;
    }

    /** 故障BIT位2 */
    public int getWaterCooler_I_FaultBit2(int index) {
        if (data != null) {
            return data[250 + 10 * index + 8];
        }
        return 0;
    }

    /** 故障BIT位3 */
    public int getWaterCooler_I_FaultBit3(int index) {
        if (data != null) {
            return data[250 + 10 * index + 9];
        }
        return 0;
    }

    /** 环境温度(℃) */
    public float getWaterCooler_I_AirTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[300 + 10 * index + 0]) / 10;
        }
        return (short) 0;
    }

    /** 设计最大容量(kVA) */
    public int getPcs_I_DesignCapacity(int index) {
        if (data != null) {
            return data[500 + 100 * index + 0];
        }
        return 0;
    }

    /** 设计最高直流电压(V) */
    public int getPcs_I_DesignMaxDcVoltage(int index) {
        if (data != null) {
            return data[500 + 100 * index + 1];
        }
        return 0;
    }

    /** 设计最低直流电压(V) */
    public int getPcs_I_DesignMinDcVoltage(int index) {
        if (data != null) {
            return data[500 + 100 * index + 2];
        }
        return 0;
    }

    /** 设计交流电压(V) */
    public int getPcs_I_DesignAcVoltage(int index) {
        if (data != null) {
            return data[500 + 100 * index + 3];
        }
        return 0;
    }

    /** 设计最大直流电流(A) */
    public int getPcs_I_DesignMaxDcCurrent(int index) {
        if (data != null) {
            return data[500 + 100 * index + 4];
        }
        return 0;
    }

    /** PCSi版本号 */
    public String getPcs_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 16; i++) {
                if (data[500 + 100 * index + 10 + i] == 0) {
                    continue;
                }
                value.append((char) (data[500 + 100 * index + 10 + i] >> 8));
                value.append((char) (data[500 + 100 * index + 10 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** 功率(kW) 正为放电 */
    public float getPcs_I_ActivePower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 30]) / 10;
        }
        return (short) 0;
    }

    /** 无功功率(kVA) 正为输出 */
    public float getPcs_I_ReactivePower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 31]) / 10;
        }
        return (short) 0;
    }

    /** 频率(Hz) */
    public double getPcs_I_Frequency(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 32] / 100;
        }
        return 0;
    }

    /** 三相电压(V) */
    public double getPcs_I_Voltage(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 33] / 10;
        }
        return 0;
    }

    /** 三相电流(A) */
    public double getPcs_I_Current(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 34] / 10;
        }
        return 0;
    }

    /** A相电压(V) */
    public double getPcs_I_Voltage1(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 35] / 10;
        }
        return 0;
    }

    /** A相电流(A) */
    public double getPcs_I_Current1(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 36] / 10;
        }
        return 0;
    }

    /** A相有功功率(kW) 正为放电 */
    public float getPcs_I_ActivePower1(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 37]) / 10;
        }
        return (short) 0;
    }

    /** A相无功功率(kVA) 正为输出 */
    public float getPcs_I_ReactivePower1(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 38]) / 10;
        }
        return (short) 0;
    }

    /** B相电压(V) */
    public double getPcs_I_Voltage2(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 39] / 10;
        }
        return 0;
    }

    /** B相电流(A) */
    public double getPcs_I_Current2(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 40] / 10;
        }
        return 0;
    }

    /** B相有功功率(kW) 正为放电 */
    public float getPcs_I_ActivePower2(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 41]) / 10;
        }
        return (short) 0;
    }

    /** B相无功功率(kVA) 正为输出 */
    public float getPcs_I_ReactivePower2(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 42]) / 10;
        }
        return (short) 0;
    }

    /** C相电压(V) */
    public double getPcs_I_Voltage3(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 43] / 10;
        }
        return 0;
    }

    /** C相电流(A) */
    public double getPcs_I_Current3(int index) {
        if (data != null) {
            return (double) data[500 + 100 * index + 44] / 10;
        }
        return 0;
    }

    /** C相有功功率(kW) 正为放电 */
    public float getPcs_I_ActivePower3(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 45]) / 10;
        }
        return (short) 0;
    }

    /** C相无功功率(kVA) 正为输出 */
    public float getPcs_I_ReactivePower3(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 46]) / 10;
        }
        return (short) 0;
    }

    /** 空气温度(deg) */
    public float getPcs_I_AirTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 47]) / 10;
        }
        return (short) 0;
    }

    /** IGBT温度(deg) */
    public float getPcs_I_IGBTTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 48]) / 10;
        }
        return (short) 0;
    }

    /** 功率因数(-1~1) */
    public float getPcs_I_PowerFactor(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 49]) / 100;
        }
        return (short) 0;
    }

    /** 直流侧电压(V) */
    public float getPcs_I_DcVoltage(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 50]) / 10;
        }
        return (short) 0;
    }

    /** 直流侧电流(A) 正为输出 */
    public float getPcs_I_DcCurrent(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 51]) / 10;
        }
        return (short) 0;
    }

    /** 电表功率(kW) 正为放电（用于单个PCS功率控制） */
    public float getPcs_I_MeterActivePower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 52]) / 10;
        }
        return (short) 0;
    }

    /** 电表无功功率(kVA) 正为输出（用于单个PCS功率控制） */
    public float getPcs_I_MeterReactivePower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 53]) / 10;
        }
        return (short) 0;
    }

    /** PCSi状态(0:正常,1:告警,2:故障) */
    public int getPcs_I_State(int index) {
        if (data != null) {
            return data[500 + 100 * index + 70];
        }
        return 0;
    }

    /** PCSi状态BIT位0 */
    public int getPcs_I_StateBit0(int index) {
        if (data != null) {
            return data[500 + 100 * index + 71];
        }
        return 0;
    }

    /** PCSi状态BIT位1 */
    public int getPcs_I_StateBit1(int index) {
        if (data != null) {
            return data[500 + 100 * index + 72];
        }
        return 0;
    }

    /** PCSi状态BIT位2 */
    public int getPcs_I_StateBit2(int index) {
        if (data != null) {
            return data[500 + 100 * index + 73];
        }
        return 0;
    }

    /** PCSi状态BIT位3 */
    public int getPcs_I_StateBit3(int index) {
        if (data != null) {
            return data[500 + 100 * index + 74];
        }
        return 0;
    }

    /** PCSi告警BIT位0 */
    public int getPcs_I_AlarmBit0(int index) {
        if (data != null) {
            return data[500 + 100 * index + 75];
        }
        return 0;
    }

    /** PCSi告警BIT位1 */
    public int getPcs_I_AlarmBit1(int index) {
        if (data != null) {
            return data[500 + 100 * index + 76];
        }
        return 0;
    }

    /** PCSi告警BIT位2 */
    public int getPcs_I_AlarmBit2(int index) {
        if (data != null) {
            return data[500 + 100 * index + 77];
        }
        return 0;
    }

    /** PCSi告警BIT位3 */
    public int getPcs_I_AlarmBit3(int index) {
        if (data != null) {
            return data[500 + 100 * index + 78];
        }
        return 0;
    }

    /** PCSi告警BIT位4 */
    public int getPcs_I_AlarmBit4(int index) {
        if (data != null) {
            return data[500 + 100 * index + 79];
        }
        return 0;
    }

    /** PCSi告警BIT位5 */
    public int getPcs_I_AlarmBit5(int index) {
        if (data != null) {
            return data[500 + 100 * index + 80];
        }
        return 0;
    }

    /** PCSi告警BIT位6 */
    public int getPcs_I_AlarmBit6(int index) {
        if (data != null) {
            return data[500 + 100 * index + 81];
        }
        return 0;
    }

    /** PCSi告警BIT位7 */
    public int getPcs_I_AlarmBit7(int index) {
        if (data != null) {
            return data[500 + 100 * index + 82];
        }
        return 0;
    }

    /** PCSi故障BIT位0 */
    public int getPcs_I_FaultBit0(int index) {
        if (data != null) {
            return data[500 + 100 * index + 83];
        }
        return 0;
    }

    /** PCSi故障BIT位1 */
    public int getPcs_I_FaultBit1(int index) {
        if (data != null) {
            return data[500 + 100 * index + 84];
        }
        return 0;
    }

    /** PCSi故障BIT位2 */
    public int getPcs_I_FaultBit2(int index) {
        if (data != null) {
            return data[500 + 100 * index + 85];
        }
        return 0;
    }

    /** PCSi故障BIT位3 */
    public int getPcs_I_FaultBit3(int index) {
        if (data != null) {
            return data[500 + 100 * index + 86];
        }
        return 0;
    }

    /** PCSi故障BIT位4 */
    public int getPcs_I_FaultBit4(int index) {
        if (data != null) {
            return data[500 + 100 * index + 87];
        }
        return 0;
    }

    /** PCSi故障BIT位5 */
    public int getPcs_I_FaultBit5(int index) {
        if (data != null) {
            return data[500 + 100 * index + 88];
        }
        return 0;
    }

    /** PCSi故障BIT位6 */
    public int getPcs_I_FaultBit6(int index) {
        if (data != null) {
            return data[500 + 100 * index + 89];
        }
        return 0;
    }

    /** PCSi故障BIT位7 */
    public int getPcs_I_FaultBit7(int index) {
        if (data != null) {
            return data[500 + 100 * index + 90];
        }
        return 0;
    }

    /** PCSi是否在线 */
    public int getPcs_I_Online(int index) {
        if (data != null) {
            return data[500 + 100 * index + 91];
        }
        return 0;
    }

    /** PCSi是否开机 */
    public int getPcs_I_PowerOn(int index) {
        if (data != null) {
            return data[500 + 100 * index + 92];
        }
        return 0;
    }

    /** PCSi直流侧功率(kW) */
    public float getPcs_I_DcPower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 100 * index + 93]) / 10;
        }
        return (short) 0;
    }

    /** 设计存储能力(kWh) */
    private int bmsDesignEnergy() {
        if (data != null) {
            return data[1000];
        }
        return 0;
    }

    /** 最高设计电压(V) */
    private double bmsDesignMaxVoltage() {
        if (data != null) {
            return (double) data[1001] / 10;
        }
        return 0;
    }

    /** 最低设计电压(V) */
    private double bmsDesignMinVoltage() {
        if (data != null) {
            return (double) data[1002] / 10;
        }
        return 0;
    }

    /** 最高设计放电电流(A) */
    private double bmsDesignMaxDischargeCurrent() {
        if (data != null) {
            return (double) data[1003] / 10;
        }
        return 0;
    }

    /** 最高设计充电电流(A) */
    private double bmsDesignMaxChargeCurrent() {
        if (data != null) {
            return (double) data[1004] / 10;
        }
        return 0;
    }

    /** 电池簇数量(CLUSTER <= 50) */
    private int bmsClusterCount() {
        if (data != null) {
            return data[1005];
        }
        return 0;
    }

    /** 每簇电池组数量(STACK <= 30) */
    private int bmsStackPerClusterCount() {
        if (data != null) {
            return data[1006];
        }
        return 0;
    }

    /** 每组电芯数量(CELL <= 32) */
    private int bmsCellPerStackCount() {
        if (data != null) {
            return data[1007];
        }
        return 0;
    }

    /** 每组电芯温度传感器数量(CELL_T <= 32) */
    private int bmsTemperaturePerStackCount() {
        if (data != null) {
            return data[1008];
        }
        return 0;
    }

    /** BMS版本号 */
    public String bmsVersion() {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 16; i++) {
                if (data[+i] == 0) {
                    continue;
                }
                value.append((char) (data[+i] >> 8));
                value.append((char) (data[+i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** SOC(0~100) */
    private double bmsSoc() {
        if (data != null) {
            return (double) data[1030] / 10;
        }
        return 0;
    }

    /** SOH(0~100) */
    private double bmsSoh() {
        if (data != null) {
            return (double) data[1031] / 10;
        }
        return 0;
    }

    /** 可放电能量(kWh) */
    private int bmsDischargeableEnergy() {
        if (data != null) {
            return data[1032];
        }
        return 0;
    }

    /** 可充电能量(kWh) */
    private int bmsChargeableEnergy() {
        if (data != null) {
            return data[1033];
        }
        return 0;
    }

    /** 当前电压(V) */
    private float bmsVoltage() {
        if (data != null) {
            return (float) ((short) data[1034]) / 10;
        }
        return (short) 0;
    }

    /** 当前电流(A) 正为放电 */
    private float bmsCurrent() {
        if (data != null) {
            return (float) ((short) data[1035]) / 10;
        }
        return (short) 0;
    }

    /** 环境温度(deg) */
    private float bmsAirTemperature() {
        if (data != null) {
            return (float) ((short) data[1036]) / 10;
        }
        return (short) 0;
    }

    /** 最高单体电压(V) */
    private double bmsCellHighVoltage() {
        if (data != null) {
            return (double) data[1037] / 1000;
        }
        return 0;
    }

    /** 最低单体电压(V) */
    private double bmsCellLowVoltage() {
        if (data != null) {
            return (double) data[1038] / 1000;
        }
        return 0;
    }

    /** 最高单体电压序号 */
    private int bmsCellHighVoltagePositioin() {
        if (data != null) {
            return data[1039];
        }
        return 0;
    }

    /** 最低单体电压序号 */
    private int bmsCellLowVoltagePosition() {
        if (data != null) {
            return data[1040];
        }
        return 0;
    }

    /** 最高单体温度(deg) */
    private float bmsCellHighTemperature() {
        if (data != null) {
            return (float) ((short) data[1041]) / 10;
        }
        return (short) 0;
    }

    /** 最低单体温度(deg) */
    private float bmsCellLowTemperature() {
        if (data != null) {
            return (float) ((short) data[1042]) / 10;
        }
        return (short) 0;
    }

    /** 最高单体温度充号 */
    private int bmsCellHighTemperaturePosition() {
        if (data != null) {
            return data[1043];
        }
        return 0;
    }

    /** 最低单体温度序号 */
    private int bmsCellLowTemperaturePosition() {
        if (data != null) {
            return data[1044];
        }
        return 0;
    }

    /** 单体平均电压(V) */
    private double bmsCellAverageVoltage() {
        if (data != null) {
            return (double) data[1045] / 1000;
        }
        return 0;
    }

    /** 单体平均温度(deg) */
    private float bmsCellAverageTemperature() {
        if (data != null) {
            return (float) ((short) data[1046]) / 10;
        }
        return (short) 0;
    }

    /** 最大放电电流(A) */
    private float bmsDischargeCurrentLimit() {
        if (data != null) {
            return (float) ((short) data[1047]) / 10;
        }
        return (short) 0;
    }

    /** 最大充电电流(A) */
    private float bmsChargeCurrentLimit() {
        if (data != null) {
            return (float) ((short) data[1048]) / 10;
        }
        return (short) 0;
    }

    /** 历史充电量(kWh) */
    private double bmsHistoryChargeEnergy() {
        if (data != null && data.length > (1049 + 1)) {
            int high = data[1049];
            int low = data[1049 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 历史放电量(kWh) */
    private double bmsHistoryDischargeEnergy() {
        if (data != null && data.length > (1051 + 1)) {
            int high = data[1051];
            int low = data[1051 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** BMS状态(0:正常,1:告警,2:故障) */
    private int bmsState() {
        if (data != null) {
            return data[1070];
        }
        return 0;
    }

    /** BMS状态BIT位0 */
    private int bmsStateBit0() {
        if (data != null) {
            return data[1071];
        }
        return 0;
    }

    /** BMS状态BIT位1 */
    private int bmsStateBit1() {
        if (data != null) {
            return data[1072];
        }
        return 0;
    }

    /** BMS状态BIT位2 */
    private int bmsStateBit2() {
        if (data != null) {
            return data[1073];
        }
        return 0;
    }

    /** BMS状态BIT位3 */
    private int bmsStateBit3() {
        if (data != null) {
            return data[1074];
        }
        return 0;
    }

    /** BMS告警BIT位0 */
    private int bmsAlarmBit0() {
        if (data != null) {
            return data[1075];
        }
        return 0;
    }

    /** BMS告警BIT位1 */
    private int bmsAlarmBit1() {
        if (data != null) {
            return data[1076];
        }
        return 0;
    }

    /** BMS告警BIT位2 */
    private int bmsAlarmBit2() {
        if (data != null) {
            return data[1077];
        }
        return 0;
    }

    /** BMS告警BIT位3 */
    private int bmsAlarmBit3() {
        if (data != null) {
            return data[1078];
        }
        return 0;
    }

    /** BMS告警BIT位4 */
    private int bmsAlarmBit4() {
        if (data != null) {
            return data[1079];
        }
        return 0;
    }

    /** BMS故障BIT位0 */
    private int bmsFaultBit0() {
        if (data != null) {
            return data[1080];
        }
        return 0;
    }

    /** BMS故障BIT位1 */
    private int bmsFaultBit1() {
        if (data != null) {
            return data[1081];
        }
        return 0;
    }

    /** BMS故障BIT位2 */
    private int bmsFaultBit2() {
        if (data != null) {
            return data[1082];
        }
        return 0;
    }

    /** BMS故障BIT位3 */
    private int bmsFaultBit3() {
        if (data != null) {
            return data[1083];
        }
        return 0;
    }

    /** BMS故障BIT位4 */
    private int bmsFaultBit4() {
        if (data != null) {
            return data[1084];
        }
        return 0;
    }

    /** BMS故障BIT位5 */
    private int bmsFaultBit5() {
        if (data != null) {
            return data[1085];
        }
        return 0;
    }

    /** BMS故障BIT位6 */
    private int bmsFaultBit6() {
        if (data != null) {
            return data[1086];
        }
        return 0;
    }

    /** SOC(0~100) */
    public double getBmsCluster_I_Soc(int index) {
        if (data != null) {
            return (double) data[1100 + 50 * index + 0] / 10;
        }
        return 0;
    }

    /** SOH(0~100) */
    public double getBmsCluster_I_Soh(int index) {
        if (data != null) {
            return (double) data[1100 + 50 * index + 1] / 10;
        }
        return 0;
    }

    /** 可放电能量(kWh) */
    public int getBmsCluster_I_DischargeableEnergy(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 2];
        }
        return 0;
    }

    /** 可充电能量(kWh) */
    public int getBmsCluster_I_ChargeableEnergy(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 3];
        }
        return 0;
    }

    /** 当前电压(V) */
    public double getBmsCluster_I_Voltage(int index) {
        if (data != null) {
            return (double) data[1100 + 50 * index + 4] / 10;
        }
        return 0;
    }

    /** 当前电流(A) 正为放电 */
    public float getBmsCluster_I_Current(int index) {
        if (data != null) {
            return (float) ((short) data[1100 + 50 * index + 5]) / 10;
        }
        return (short) 0;
    }

    /** 环境温度(deg) */
    public float getBmsCluster_I_AirTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[1100 + 50 * index + 6]) / 10;
        }
        return (short) 0;
    }

    /** 最高单体电压(V) */
    public double getBmsCluster_I_CellHighVoltage(int index) {
        if (data != null) {
            return (double) data[1100 + 50 * index + 7] / 1000;
        }
        return 0;
    }

    /** 最低单体电压(V) */
    public double getBmsCluster_I_CellLowVoltage(int index) {
        if (data != null) {
            return (double) data[1100 + 50 * index + 8] / 1000;
        }
        return 0;
    }

    /** 最高单体电压充号 */
    public int getBmsCluster_I_CellHighVoltagePositioin(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 9];
        }
        return 0;
    }

    /** 最低单体电压序号 */
    public int getBmsCluster_I_CellLowVoltagePosition(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 10];
        }
        return 0;
    }

    /** 最高单体温度(deg) */
    public float getBmsCluster_I_CellHighTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[1100 + 50 * index + 11]) / 10;
        }
        return (short) 0;
    }

    /** 最低单体温度(deg) */
    public float getBmsCluster_I_CellLowTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[1100 + 50 * index + 12]) / 10;
        }
        return (short) 0;
    }

    /** 最高单体温度充号 */
    public int getBmsCluster_I_CellHighTemperaturePosition(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 13];
        }
        return 0;
    }

    /** 最低单体温度序号 */
    public int getBmsCluster_I_CellLowTemperaturePosition(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 14];
        }
        return 0;
    }

    /** 单体平均电压(V) */
    public double getBmsCluster_I_CellAverageVoltage(int index) {
        if (data != null) {
            return (double) data[1100 + 50 * index + 15] / 1000;
        }
        return 0;
    }

    /** 单体平均温度(deg) */
    public float getBmsCluster_I_CellAverageTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[1100 + 50 * index + 16]) / 10;
        }
        return (short) 0;
    }

    /** 最大放电电流(A) */
    public float getBmsCluster_I_DischargeCurrentLimit(int index) {
        if (data != null) {
            return (float) ((short) data[1100 + 50 * index + 17]) / 10;
        }
        return (short) 0;
    }

    /** 最大充电电流(A) */
    public float getBmsCluster_I_ChargeCurrentLimit(int index) {
        if (data != null) {
            return (float) ((short) data[1100 + 50 * index + 18]) / 10;
        }
        return (short) 0;
    }

    /** CLUSTERi状态(0:正常,1:告警,2:故障) */
    public int getBmsCluster_I_State(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 20];
        }
        return 0;
    }

    /** CLUSTERi状态BIT位0 */
    public int getBmsCluster_I_StateBit0(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 21];
        }
        return 0;
    }

    /** CLUSTERi状态BIT位1 */
    public int getBmsCluster_I_StateBit1(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 22];
        }
        return 0;
    }

    /** CLUSTERi状态BIT位2 */
    public int getBmsCluster_I_StateBit2(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 23];
        }
        return 0;
    }

    /** CLUSTERi状态BIT位3 */
    public int getBmsCluster_I_StateBit3(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 24];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位0 */
    public int getBmsCluster_I_AlarmBit0(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 25];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位1 */
    public int getBmsCluster_I_AlarmBit1(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 26];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位2 */
    public int getBmsCluster_I_AlarmBit2(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 27];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位3 */
    public int getBmsCluster_I_AlarmBit3(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 28];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位4 */
    public int getBmsCluster_I_AlarmBit4(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 29];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位0 */
    public int getBmsCluster_I_FaultBit0(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 30];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位1 */
    public int getBmsCluster_I_FaultBit1(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 31];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位2 */
    public int getBmsCluster_I_FaultBit2(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 32];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位3 */
    public int getBmsCluster_I_FaultBit3(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 33];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位4 */
    public int getBmsCluster_I_FaultBit4(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 34];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位5 */
    public int getBmsCluster_I_FaultBit5(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 35];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位6 */
    public int getBmsCluster_I_FaultBit6(int index) {
        if (data != null) {
            return data[1100 + 50 * index + 36];
        }
        return 0;
    }

    /** CLUSTERi版本号 */
    public String getBmsCluster_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 10; i++) {
                if (data[1100 + 50 * index + 40 + i] == 0) {
                    continue;
                }
                value.append((char) (data[1100 + 50 * index + 40 + i] >> 8));
                value.append((char) (data[1100 + 50 * index + 40 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** 功率只读模式 */
    private int powerReadOnly() {
        if (data != null) {
            return data[60000];
        }
        return 0;
    }

    /** 设定状态 */
    private int setState() {
        if (data != null) {
            return data[60001];
        }
        return 0;
    }

    /** 设定功率 */
    private double setPower() {
        if (data != null && data.length > (60002 + 1)) {
            int high = data[60002];
            int low = data[60002 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 功率右限 */
    private double rightLimitPower() {
        if (data != null && data.length > (60004 + 1)) {
            int high = data[60004];
            int low = data[60004 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 功率左限 */
    private double leftLimitPower() {
        if (data != null && data.length > (60006 + 1)) {
            int high = data[60006];
            int low = data[60006 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 当前功率 */
    private double currentPower() {
        if (data != null && data.length > (60008 + 1)) {
            int high = data[60008];
            int low = data[60008 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 故障标识 */
    private int faultFlag() {
        if (data != null) {
            return data[60010];
        }
        return 0;
    }

    /** 状态机 */
    private int state() {
        if (data != null) {
            return data[60011];
        }
        return 0;
    }

    /** 状态机TICK */
    private int stateTick() {
        if (data != null) {
            return data[60012];
        }
        return 0;
    }

    /** 状态机子TICK */
    private int stateSubTick() {
        if (data != null) {
            return data[60013];
        }
        return 0;
    }

    /** DCDC功率限制值 */
    private double dcdcLimitPower() {
        if (data != null && data.length > (60014 + 1)) {
            int high = data[60014];
            int low = data[60014 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** DCDC功率当前值 */
    private double dcdcCurrentPower() {
        if (data != null && data.length > (60016 + 1)) {
            int high = data[60016];
            int low = data[60016 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 电表控制功率 */
    private double meterControlPower() {
        if (data != null && data.length > (60018 + 1)) {
            int high = data[60018];
            int low = data[60018 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 设定无功功率 */
    private double setReactivePower() {
        if (data != null && data.length > (60020 + 1)) {
            int high = data[60020];
            int low = data[60020 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** EMS100软件版本号 */
    public String ems100Version() {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 40; i++) {
                if (data[+i] == 0) {
                    continue;
                }
                value.append((char) (data[+i] >> 8));
                value.append((char) (data[+i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** 相电流A */
    private float emsAcCurrentA() {
        if (data != null) {
            return (float) ((short) data[60070]) / 10;
        }
        return (short) 0;
    }

    /** 相电流B */
    private float emsAcCurrentB() {
        if (data != null) {
            return (float) ((short) data[60071]) / 10;
        }
        return (short) 0;
    }

    /** 相电流C */
    private float emsAcCurrentC() {
        if (data != null) {
            return (float) ((short) data[60072]) / 10;
        }
        return (short) 0;
    }

    /** 线电压AB */
    private float emsAcVoltageAB() {
        if (data != null) {
            return (float) ((short) data[60073]) / 10;
        }
        return (short) 0;
    }

    /** 线电压BC */
    private float emsAcVoltageBC() {
        if (data != null) {
            return (float) ((short) data[60074]) / 10;
        }
        return (short) 0;
    }

    /** 线电压CA */
    private float emsAcVoltageCA() {
        if (data != null) {
            return (float) ((short) data[60075]) / 10;
        }
        return (short) 0;
    }

    /** 相电压A */
    private float emsAcVoltageA() {
        if (data != null) {
            return (float) ((short) data[60076]) / 10;
        }
        return (short) 0;
    }

    /** 相电压B */
    private float emsAcVoltageB() {
        if (data != null) {
            return (float) ((short) data[60077]) / 10;
        }
        return (short) 0;
    }

    /** 相电压C */
    private float emsAcVoltageC() {
        if (data != null) {
            return (float) ((short) data[60078]) / 10;
        }
        return (short) 0;
    }

    /** 功率因数 */
    private float emsAcPowerFactor() {
        if (data != null) {
            return (float) ((short) data[60079]) / 100;
        }
        return (short) 0;
    }

    /** 有功功率A */
    private float emsAcActivePowerA() {
        if (data != null) {
            return (float) ((short) data[60080]) / 10;
        }
        return (short) 0;
    }

    /** 有功功率B */
    private float emsAcActivePowerB() {
        if (data != null) {
            return (float) ((short) data[60081]) / 10;
        }
        return (short) 0;
    }

    /** 有功功率C */
    private float emsAcActivePowerC() {
        if (data != null) {
            return (float) ((short) data[60082]) / 10;
        }
        return (short) 0;
    }

    /** 无功功率A */
    private float emsAcReactivePowerA() {
        if (data != null) {
            return (float) ((short) data[60083]) / 10;
        }
        return (short) 0;
    }

    /** 无功功率B */
    private float emsAcReactivePowerB() {
        if (data != null) {
            return (float) ((short) data[60084]) / 10;
        }
        return (short) 0;
    }

    /** 无功功率C */
    private float emsAcReactivePowerC() {
        if (data != null) {
            return (float) ((short) data[60085]) / 10;
        }
        return (short) 0;
    }

    /** 功率因数A */
    private float emsAcPowerFactorA() {
        if (data != null) {
            return (float) ((short) data[60086]) / 100;
        }
        return (short) 0;
    }

    /** 功率因数B */
    private float emsAcPowerFactorB() {
        if (data != null) {
            return (float) ((short) data[60087]) / 100;
        }
        return (short) 0;
    }

    /** 功率因数C */
    private float emsAcPowerFactorC() {
        if (data != null) {
            return (float) ((short) data[60088]) / 100;
        }
        return (short) 0;
    }

    /** DCDC数量 */
    private int dcdcCount() {
        if (data != null) {
            return data[20000];
        }
        return 0;
    }

    /** DCDC设备类型码 */
    private int dcdcTypeCode() {
        if (data != null) {
            return data[20001];
        }
        return 0;
    }

    /** STATES数量 */
    private int statesCount() {
        if (data != null) {
            return data[20002];
        }
        return 0;
    }

    /** 开关状态 */
    public int getDcdc_I_Onoff(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 0];
        }
        return 0;
    }

    /** DCDC设计功率(kW) */
    public double getDcdc_I_DesignPower(int index) {
        if (data != null) {
            return (double) data[20100 + 100 * index + 1] / 10;
        }
        return 0;
    }

    /** 光伏设计功率(kW) */
    public double getDcdc_I_PvDesignPower(int index) {
        if (data != null) {
            return (double) data[20100 + 100 * index + 2] / 10;
        }
        return 0;
    }

    /** 光伏侧当前功率(kW) */
    public float getDcdc_I_PvCurrentPower(int index) {
        if (data != null) {
            return (float) ((short) data[20100 + 100 * index + 20]) / 10;
        }
        return (short) 0;
    }

    /** 限制功率(kW) */
    public double getDcdc_I_LimitPower(int index) {
        if (data != null) {
            return (double) data[20100 + 100 * index + 21] / 10;
        }
        return 0;
    }

    /** 光伏侧电压(V) */
    public double getDcdc_I_PvVoltage(int index) {
        if (data != null) {
            return (double) data[20100 + 100 * index + 22] / 10;
        }
        return 0;
    }

    /** 光伏侧电流(A) */
    public float getDcdc_I_PvCurrent(int index) {
        if (data != null) {
            return (float) ((short) data[20100 + 100 * index + 23]) / 10;
        }
        return (short) 0;
    }

    /** 电池侧当前功率(kW) */
    public float getDcdc_I_BatCurrentPower(int index) {
        if (data != null) {
            return (float) ((short) data[20100 + 100 * index + 24]) / 10;
        }
        return (short) 0;
    }

    /** 电池侧电压(V) */
    public double getDcdc_I_BatVoltage(int index) {
        if (data != null) {
            return (double) data[20100 + 100 * index + 25] / 10;
        }
        return 0;
    }

    /** 电池侧电流(A) */
    public float getDcdc_I_BatCurrent(int index) {
        if (data != null) {
            return (float) ((short) data[20100 + 100 * index + 26]) / 10;
        }
        return (short) 0;
    }

    /** 历史充电量(kWh) */
    public double getDcdc_I_HistoryChargeEnergy(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 历史放电量(kWh) */
    public double getDcdc_I_HistoryDischargeEnergy(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** DCDCi状态(0:正常,1:告警,2:故障) */
    public int getDcdc_I_State(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 40];
        }
        return 0;
    }

    /** DCDCi状态BIT位0 */
    public int getDcdc_I_StateBit0(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 41];
        }
        return 0;
    }

    /** DCDCi状态BIT位1 */
    public int getDcdc_I_StateBit1(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 42];
        }
        return 0;
    }

    /** DCDCi状态BIT位2 */
    public int getDcdc_I_StateBit2(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 43];
        }
        return 0;
    }

    /** DCDCi状态BIT位3 */
    public int getDcdc_I_StateBit3(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 44];
        }
        return 0;
    }

    /** DCDCi告警BIT位0 */
    public int getDcdc_I_AlarmBit0(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 45];
        }
        return 0;
    }

    /** DCDCi告警BIT位1 */
    public int getDcdc_I_AlarmBit1(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 46];
        }
        return 0;
    }

    /** DCDCi告警BIT位2 */
    public int getDcdc_I_AlarmBit2(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 47];
        }
        return 0;
    }

    /** DCDCi告警BIT位3 */
    public int getDcdc_I_AlarmBit3(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 48];
        }
        return 0;
    }

    /** DCDCi告警BIT位4 */
    public int getDcdc_I_AlarmBit4(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 49];
        }
        return 0;
    }

    /** DCDCi告警BIT位5 */
    public int getDcdc_I_AlarmBit5(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 50];
        }
        return 0;
    }

    /** DCDCi告警BIT位6 */
    public int getDcdc_I_AlarmBit6(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 51];
        }
        return 0;
    }

    /** DCDCi告警BIT位7 */
    public int getDcdc_I_AlarmBit7(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 52];
        }
        return 0;
    }

    /** DCDCi故障BIT位0 */
    public int getDcdc_I_FaultBit0(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 53];
        }
        return 0;
    }

    /** DCDCi故障BIT位1 */
    public int getDcdc_I_FaultBit1(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 54];
        }
        return 0;
    }

    /** DCDCi故障BIT位2 */
    public int getDcdc_I_FaultBit2(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 55];
        }
        return 0;
    }

    /** DCDCi故障BIT位3 */
    public int getDcdc_I_FaultBit3(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 56];
        }
        return 0;
    }

    /** DCDCi故障BIT位4 */
    public int getDcdc_I_FaultBit4(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 57];
        }
        return 0;
    }

    /** DCDCi故障BIT位5 */
    public int getDcdc_I_FaultBit5(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 58];
        }
        return 0;
    }

    /** DCDCi故障BIT位6 */
    public int getDcdc_I_FaultBit6(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 59];
        }
        return 0;
    }

    /** DCDCi故障BIT位7 */
    public int getDcdc_I_FaultBit7(int index) {
        if (data != null) {
            return data[20100 + 100 * index + 60];
        }
        return 0;
    }

    /** STS类型码 */
    public int getStates_I_TypeCode(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 0];
        }
        return 0;
    }

    /** STATESi状态(0:正常,1:告警,2:故障) */
    public int getStates_I_State(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 10];
        }
        return 0;
    }

    /** STATESi状态BIT位0 */
    public int getStates_I_StateBit0(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 11];
        }
        return 0;
    }

    /** STATESi状态BIT位1 */
    public int getStates_I_StateBit1(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 12];
        }
        return 0;
    }

    /** STATESi状态BIT位2 */
    public int getStates_I_StateBit2(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 13];
        }
        return 0;
    }

    /** STATESi状态BIT位3 */
    public int getStates_I_StateBit3(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 14];
        }
        return 0;
    }

    /** STATESi告警BIT位0 */
    public int getStates_I_AlarmBit0(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 15];
        }
        return 0;
    }

    /** STATESi告警BIT位1 */
    public int getStates_I_AlarmBit1(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 16];
        }
        return 0;
    }

    /** STATESi告警BIT位2 */
    public int getStates_I_AlarmBit2(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 17];
        }
        return 0;
    }

    /** STATESi告警BIT位3 */
    public int getStates_I_AlarmBit3(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 18];
        }
        return 0;
    }

    /** STATESi告警BIT位4 */
    public int getStates_I_AlarmBit4(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 19];
        }
        return 0;
    }

    /** STATESi告警BIT位5 */
    public int getStates_I_AlarmBit5(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 20];
        }
        return 0;
    }

    /** STATESi告警BIT位6 */
    public int getStates_I_AlarmBit6(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 21];
        }
        return 0;
    }

    /** STATESi告警BIT位7 */
    public int getStates_I_AlarmBit7(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 22];
        }
        return 0;
    }

    /** STATESi故障BIT位0 */
    public int getStates_I_FaultBit0(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 23];
        }
        return 0;
    }

    /** STATESi故障BIT位1 */
    public int getStates_I_FaultBit1(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 24];
        }
        return 0;
    }

    /** STATESi故障BIT位2 */
    public int getStates_I_FaultBit2(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 25];
        }
        return 0;
    }

    /** STATESi故障BIT位3 */
    public int getStates_I_FaultBit3(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 26];
        }
        return 0;
    }

    /** STATESi故障BIT位4 */
    public int getStates_I_FaultBit4(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 27];
        }
        return 0;
    }

    /** STATESi故障BIT位5 */
    public int getStates_I_FaultBit5(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 28];
        }
        return 0;
    }

    /** STATESi故障BIT位6 */
    public int getStates_I_FaultBit6(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 29];
        }
        return 0;
    }

    /** STATESi故障BIT位7 */
    public int getStates_I_FaultBit7(int index) {
        if (data != null) {
            return data[21100 + 50 * index + 30];
        }
        return 0;
    }

    /** GPS触发失败次数统计值 */
    private double gpsTriggerFailedCount() {
        if (data != null && data.length > (60100 + 1)) {
            int high = data[60100];
            int low = data[60100 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** GPS轮询失败次数统计值 */
    private double gpsLoopFailedCount() {
        if (data != null && data.length > (60102 + 1)) {
            int high = data[60102];
            int low = data[60102 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** GPS轮询成功次数统计值 */
    private double gpsLoopSuccessCount() {
        if (data != null && data.length > (60104 + 1)) {
            int high = data[60104];
            int low = data[60104 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** BMS触发失败次数统计值 */
    private double bmsTriggerFailedCount() {
        if (data != null && data.length > (60120 + 1)) {
            int high = data[60120];
            int low = data[60120 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** BMS轮询失败次数统计值 */
    private double bmsLoopFailedCount() {
        if (data != null && data.length > (60122 + 1)) {
            int high = data[60122];
            int low = data[60122 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** BMS轮询成功次数统计值 */
    private double bmsLoopSuccessCount() {
        if (data != null && data.length > (60124 + 1)) {
            int high = data[60124];
            int low = data[60124 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** EMS电表触发失败次数统计值 */
    private double emsMeterTriggerFailedCount() {
        if (data != null && data.length > (60140 + 1)) {
            int high = data[60140];
            int low = data[60140 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** EMS电表轮询失败次数统计值 */
    private double emsMeterLoopFailedCount() {
        if (data != null && data.length > (60142 + 1)) {
            int high = data[60142];
            int low = data[60142 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** EMS电表轮询成功次数统计值 */
    private double emsMeterLoopSuccessCount() {
        if (data != null && data.length > (60144 + 1)) {
            int high = data[60144];
            int low = data[60144 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC电表触发失败次数统计值 */
    private double DCMeterTriggerFailedCount() {
        if (data != null && data.length > (60160 + 1)) {
            int high = data[60160];
            int low = data[60160 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC电表轮询失败次数统计值 */
    private double DCMeterLoopFailedCount() {
        if (data != null && data.length > (60162 + 1)) {
            int high = data[60162];
            int low = data[60162 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC电表轮询成功次数统计值 */
    private double DCMeterLoopSuccessCount() {
        if (data != null && data.length > (60164 + 1)) {
            int high = data[60164];
            int low = data[60164 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** PCS触发失败次数统计值 */
    public double getPcs_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** PCS轮询失败次数统计值 */
    public double getPcs_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** PCS轮询成功次数统计值 */
    public double getPcs_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC触发失败次数统计值 */
    public double getDcdc_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC轮询失败次数统计值 */
    public double getDcdc_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC轮询成功次数统计值 */
    public double getDcdc_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 空调触发失败次数统计值 */
    public double getAirConditioner_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 空调轮询失败次数统计值 */
    public double getAirConditioner_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 空调轮询成功次数统计值 */
    public double getAirConditioner_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 水冷触发失败次数统计值 */
    public double getWaterCooler_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 水冷轮询失败次数统计值 */
    public double getWaterCooler_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 水冷轮询成功次数统计值 */
    public double getWaterCooler_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 消防触发失败次数统计值 */
    public double getFirefighting_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 消防轮询失败次数统计值 */
    public double getFirefighting_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 消防轮询成功次数统计值 */
    public double getFirefighting_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** STATES触发失败次数统计值 */
    public double getStates_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** STATES轮询失败次数统计值 */
    public double getStates_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** STATES轮询成功次数统计值 */
    public double getStates_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }
}
