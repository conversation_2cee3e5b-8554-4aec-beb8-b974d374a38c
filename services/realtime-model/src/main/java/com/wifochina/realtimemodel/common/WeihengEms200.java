package com.wifochina.realtimemodel.common;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.Override;
import java.lang.String;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR> 2025-07-11 14:38:44
 */
@Getter
@Slf4j
public class WeihengEms200 implements IProtocolData {
    @JsonSerialize(using = VendorDataSerializer.class)
    public int[] data;

    private final String emsSerial;

    private final String ems100Version;

    private final int emsSoftwareVersion;

    private final int emsHardwareVersion;

    private final int emsProtocolVersion;

    private final double emsUtc;

    private final double emsUptime;

    private final double emsCpuLoad;

    private final double emsMemoryLoad;

    private final double emsDiskLoad;

    private final int emsHotBackupValid;

    private final int emsHotBackupRunningCode;

    private final int emsHotBackupLevel;

    private final int gpsOnline;

    private final double gpsLatitude;

    private final double gpsLongitude;

    private final int pcsCount;

    private final int dcdcCount;

    private final int bmsCount;

    private final int airConditionerCount;

    private final int waterCoolerCount;

    private final int firefightingCount;

    private final int statesCount;

    private final int emsMeterCount;

    private final int dcdcMeterCount;

    private final int gpsCount;

    private final int dcBusMeterCount;

    private final int systemTypeCode;

    private final int emsDesignPower;

    private final int emsDesignStorageEnergy;

    private final int emsDesignChargePower;

    private final int emsDesignDischargePower;

    private final int emsState;

    private final int systemRunStatus;

    private final int systemStartingOrStopping;

    private final int systemOffGrid;

    private final int systemAutoGridOnoff;

    private final int directPowerControlCounter;

    private final double emsSoc;

    private final double emsSoh;

    private final float emsAcActivePower;

    private final float emsAcReactivePower;

    private final float emsAcPowerFactor;

    private final double emsAcFrequency;

    private final float emsAcActivePowerPos;

    private final float emsAcActivePowerNeg;

    private final int emsChargePowerLimit;

    private final int emsDischargePowerLimit;

    private final int emsCapacityLimit;

    private final double emsHistoryOutputEnergy;

    private final double emsHistoryInputEnergy;

    private final float emsDcVoltage;

    private final float emsDcCurrent;

    private final float emsDcPower;

    private final float emsDcPowerPos;

    private final float emsDcPowerNeg;

    private final float emsAcCurrentA;

    private final float emsAcCurrentB;

    private final float emsAcCurrentC;

    private final float emsAcVoltageAB;

    private final float emsAcVoltageBC;

    private final float emsAcVoltageCA;

    private final float emsAcVoltageA;

    private final float emsAcVoltageB;

    private final float emsAcVoltageC;

    private final float emsAcActivePowerA;

    private final float emsAcActivePowerB;

    private final float emsAcActivePowerC;

    private final float emsAcReactivePowerA;

    private final float emsAcReactivePowerB;

    private final float emsAcReactivePowerC;

    private final float emsAcPowerFactorA;

    private final float emsAcPowerFactorB;

    private final float emsAcPowerFactorC;

    private final float dcdcMeterVoltage;

    private final float dcdcMeterCurrent;

    private final float dcdcMeterPower;

    private final double dcdcMeterHistoryEnergyPos;

    private final double dcdcMeterHistoryEnergyNeg;

    private final float dcBusVoltage;

    private final float dcBusCurrent;

    private final float dcBusPower;

    private final double dcBusHistoryEnergyPos;

    private final double dcBusHistoryEnergyNeg;

    private final int dcBusChargeLimit;

    private final int dcBusDischargeLimit;

    private final int systemStateBit0;

    private final int systemStateBit1;

    private final int systemStateBit2;

    private final int systemStateBit3;

    private final int systemAlarmBit0;

    private final int systemAlarmBit1;

    private final int systemFaultBit0;

    private final int systemFaultBit1;

    private final int systemFaultBit2;

    private final int systemFaultBit3;

    private final int bmsTypeCode;

    private final int bmsClusterCount;

    private final int bmsStackPerClusterCount;

    private final int bmsCellPerStackCount;

    private final int bmsTemperaturePerStackCount;

    private final int bmsDesignEnergy;

    private final double bmsDesignMaxVoltage;

    private final double bmsDesignMinVoltage;

    private final double bmsDesignMaxDischargeCurrent;

    private final double bmsDesignMaxChargeCurrent;

    private final double bmsDesignCellMaxVoltage;

    private final double bmsDesignCellMinVoltage;

    private final double bmsDesignMaxDischargePower;

    private final double bmsDesignMaxChargePower;

    private final String bmsVersion;

    private final int bmsState;

    private final int bmsOnline;

    private final int bmsOnoff;

    private final double bmsSoc;

    private final double bmsSoh;

    private final int bmsDischargeableEnergy;

    private final int bmsChargeableEnergy;

    private final float bmsVoltage;

    private final float bmsCurrent;

    private final float bmsAirTemperature;

    private final double bmsCellHighVoltage;

    private final double bmsCellLowVoltage;

    private final int bmsCellHighVoltagePositioin;

    private final int bmsCellLowVoltagePosition;

    private final float bmsCellHighTemperature;

    private final float bmsCellLowTemperature;

    private final int bmsCellHighTemperaturePosition;

    private final int bmsCellLowTemperaturePosition;

    private final double bmsCellAverageVoltage;

    private final float bmsCellAverageTemperature;

    private final float bmsDischargeCurrentLimit;

    private final float bmsChargeCurrentLimit;

    private final double bmsHistoryDischargeEnergy;

    private final double bmsHistoryChargeEnergy;

    private final int bmsStateBit0;

    private final int bmsStateBit1;

    private final int bmsStateBit2;

    private final int bmsStateBit3;

    private final int bmsAlarmBit0;

    private final int bmsAlarmBit1;

    private final int bmsAlarmBit2;

    private final int bmsAlarmBit3;

    private final int bmsAlarmBit4;

    private final int bmsAlarmBit5;

    private final int bmsAlarmBit6;

    private final int bmsAlarmBit7;

    private final int bmsFaultBit0;

    private final int bmsFaultBit1;

    private final int bmsFaultBit2;

    private final int bmsFaultBit3;

    private final int bmsFaultBit4;

    private final int bmsFaultBit5;

    private final int bmsFaultBit6;

    private final int bmsFaultBit7;

    private final int setState;

    private final int state;

    private final int stateTick;

    private final int stateSubTick;

    private final int faultFlag;

    private final double setPower;

    private final double setReactivePower;

    private final double rightLimitPower;

    private final double leftLimitPower;

    private final double currentPower;

    private final double currentReactivePower;

    private final double dcdcLimitPower;

    private final double dcdcCurrentPower;

    private final double meterControlPower;

    private final int powerByUdp;

    private final int powerByPlugin;

    private final double gpsTriggerFailedCount;

    private final double gpsLoopFailedCount;

    private final double gpsLoopSuccessCount;

    private final double bmsTriggerFailedCount;

    private final double bmsLoopFailedCount;

    private final double bmsLoopSuccessCount;

    private final double emsMeterTriggerFailedCount;

    private final double emsMeterLoopFailedCount;

    private final double emsMeterLoopSuccessCount;

    private final double DCMeterTriggerFailedCount;

    private final double DCMeterLoopFailedCount;

    private final double DCMeterLoopSuccessCount;

    public WeihengEms200(int[] data) {
        this.data = data;
        this.emsSerial = emsSerial();
        this.ems100Version = ems100Version();
        this.emsSoftwareVersion = emsSoftwareVersion();
        this.emsHardwareVersion = emsHardwareVersion();
        this.emsProtocolVersion = emsProtocolVersion();
        this.emsUtc = emsUtc();
        this.emsUptime = emsUptime();
        this.emsCpuLoad = emsCpuLoad();
        this.emsMemoryLoad = emsMemoryLoad();
        this.emsDiskLoad = emsDiskLoad();
        this.emsHotBackupValid = emsHotBackupValid();
        this.emsHotBackupRunningCode = emsHotBackupRunningCode();
        this.emsHotBackupLevel = emsHotBackupLevel();
        this.gpsOnline = gpsOnline();
        this.gpsLatitude = gpsLatitude();
        this.gpsLongitude = gpsLongitude();
        this.pcsCount = pcsCount();
        this.dcdcCount = dcdcCount();
        this.bmsCount = bmsCount();
        this.airConditionerCount = airConditionerCount();
        this.waterCoolerCount = waterCoolerCount();
        this.firefightingCount = firefightingCount();
        this.statesCount = statesCount();
        this.emsMeterCount = emsMeterCount();
        this.dcdcMeterCount = dcdcMeterCount();
        this.gpsCount = gpsCount();
        this.dcBusMeterCount = dcBusMeterCount();
        this.systemTypeCode = systemTypeCode();
        this.emsDesignPower = emsDesignPower();
        this.emsDesignStorageEnergy = emsDesignStorageEnergy();
        this.emsDesignChargePower = emsDesignChargePower();
        this.emsDesignDischargePower = emsDesignDischargePower();
        this.emsState = emsState();
        this.systemRunStatus = systemRunStatus();
        this.systemStartingOrStopping = systemStartingOrStopping();
        this.systemOffGrid = systemOffGrid();
        this.systemAutoGridOnoff = systemAutoGridOnoff();
        this.directPowerControlCounter = directPowerControlCounter();
        this.emsSoc = emsSoc();
        this.emsSoh = emsSoh();
        this.emsAcActivePower = emsAcActivePower();
        this.emsAcReactivePower = emsAcReactivePower();
        this.emsAcPowerFactor = emsAcPowerFactor();
        this.emsAcFrequency = emsAcFrequency();
        this.emsAcActivePowerPos = emsAcActivePowerPos();
        this.emsAcActivePowerNeg = emsAcActivePowerNeg();
        this.emsChargePowerLimit = emsChargePowerLimit();
        this.emsDischargePowerLimit = emsDischargePowerLimit();
        this.emsCapacityLimit = emsCapacityLimit();
        this.emsHistoryOutputEnergy = emsHistoryOutputEnergy();
        this.emsHistoryInputEnergy = emsHistoryInputEnergy();
        this.emsDcVoltage = emsDcVoltage();
        this.emsDcCurrent = emsDcCurrent();
        this.emsDcPower = emsDcPower();
        this.emsDcPowerPos = emsDcPowerPos();
        this.emsDcPowerNeg = emsDcPowerNeg();
        this.emsAcCurrentA = emsAcCurrentA();
        this.emsAcCurrentB = emsAcCurrentB();
        this.emsAcCurrentC = emsAcCurrentC();
        this.emsAcVoltageAB = emsAcVoltageAB();
        this.emsAcVoltageBC = emsAcVoltageBC();
        this.emsAcVoltageCA = emsAcVoltageCA();
        this.emsAcVoltageA = emsAcVoltageA();
        this.emsAcVoltageB = emsAcVoltageB();
        this.emsAcVoltageC = emsAcVoltageC();
        this.emsAcActivePowerA = emsAcActivePowerA();
        this.emsAcActivePowerB = emsAcActivePowerB();
        this.emsAcActivePowerC = emsAcActivePowerC();
        this.emsAcReactivePowerA = emsAcReactivePowerA();
        this.emsAcReactivePowerB = emsAcReactivePowerB();
        this.emsAcReactivePowerC = emsAcReactivePowerC();
        this.emsAcPowerFactorA = emsAcPowerFactorA();
        this.emsAcPowerFactorB = emsAcPowerFactorB();
        this.emsAcPowerFactorC = emsAcPowerFactorC();
        this.dcdcMeterVoltage = dcdcMeterVoltage();
        this.dcdcMeterCurrent = dcdcMeterCurrent();
        this.dcdcMeterPower = dcdcMeterPower();
        this.dcdcMeterHistoryEnergyPos = dcdcMeterHistoryEnergyPos();
        this.dcdcMeterHistoryEnergyNeg = dcdcMeterHistoryEnergyNeg();
        this.dcBusVoltage = dcBusVoltage();
        this.dcBusCurrent = dcBusCurrent();
        this.dcBusPower = dcBusPower();
        this.dcBusHistoryEnergyPos = dcBusHistoryEnergyPos();
        this.dcBusHistoryEnergyNeg = dcBusHistoryEnergyNeg();
        this.dcBusChargeLimit = dcBusChargeLimit();
        this.dcBusDischargeLimit = dcBusDischargeLimit();
        this.systemStateBit0 = systemStateBit0();
        this.systemStateBit1 = systemStateBit1();
        this.systemStateBit2 = systemStateBit2();
        this.systemStateBit3 = systemStateBit3();
        this.systemAlarmBit0 = systemAlarmBit0();
        this.systemAlarmBit1 = systemAlarmBit1();
        this.systemFaultBit0 = systemFaultBit0();
        this.systemFaultBit1 = systemFaultBit1();
        this.systemFaultBit2 = systemFaultBit2();
        this.systemFaultBit3 = systemFaultBit3();
        this.bmsTypeCode = bmsTypeCode();
        this.bmsClusterCount = bmsClusterCount();
        this.bmsStackPerClusterCount = bmsStackPerClusterCount();
        this.bmsCellPerStackCount = bmsCellPerStackCount();
        this.bmsTemperaturePerStackCount = bmsTemperaturePerStackCount();
        this.bmsDesignEnergy = bmsDesignEnergy();
        this.bmsDesignMaxVoltage = bmsDesignMaxVoltage();
        this.bmsDesignMinVoltage = bmsDesignMinVoltage();
        this.bmsDesignMaxDischargeCurrent = bmsDesignMaxDischargeCurrent();
        this.bmsDesignMaxChargeCurrent = bmsDesignMaxChargeCurrent();
        this.bmsDesignCellMaxVoltage = bmsDesignCellMaxVoltage();
        this.bmsDesignCellMinVoltage = bmsDesignCellMinVoltage();
        this.bmsDesignMaxDischargePower = bmsDesignMaxDischargePower();
        this.bmsDesignMaxChargePower = bmsDesignMaxChargePower();
        this.bmsVersion = bmsVersion();
        this.bmsState = bmsState();
        this.bmsOnline = bmsOnline();
        this.bmsOnoff = bmsOnoff();
        this.bmsSoc = bmsSoc();
        this.bmsSoh = bmsSoh();
        this.bmsDischargeableEnergy = bmsDischargeableEnergy();
        this.bmsChargeableEnergy = bmsChargeableEnergy();
        this.bmsVoltage = bmsVoltage();
        this.bmsCurrent = bmsCurrent();
        this.bmsAirTemperature = bmsAirTemperature();
        this.bmsCellHighVoltage = bmsCellHighVoltage();
        this.bmsCellLowVoltage = bmsCellLowVoltage();
        this.bmsCellHighVoltagePositioin = bmsCellHighVoltagePositioin();
        this.bmsCellLowVoltagePosition = bmsCellLowVoltagePosition();
        this.bmsCellHighTemperature = bmsCellHighTemperature();
        this.bmsCellLowTemperature = bmsCellLowTemperature();
        this.bmsCellHighTemperaturePosition = bmsCellHighTemperaturePosition();
        this.bmsCellLowTemperaturePosition = bmsCellLowTemperaturePosition();
        this.bmsCellAverageVoltage = bmsCellAverageVoltage();
        this.bmsCellAverageTemperature = bmsCellAverageTemperature();
        this.bmsDischargeCurrentLimit = bmsDischargeCurrentLimit();
        this.bmsChargeCurrentLimit = bmsChargeCurrentLimit();
        this.bmsHistoryDischargeEnergy = bmsHistoryDischargeEnergy();
        this.bmsHistoryChargeEnergy = bmsHistoryChargeEnergy();
        this.bmsStateBit0 = bmsStateBit0();
        this.bmsStateBit1 = bmsStateBit1();
        this.bmsStateBit2 = bmsStateBit2();
        this.bmsStateBit3 = bmsStateBit3();
        this.bmsAlarmBit0 = bmsAlarmBit0();
        this.bmsAlarmBit1 = bmsAlarmBit1();
        this.bmsAlarmBit2 = bmsAlarmBit2();
        this.bmsAlarmBit3 = bmsAlarmBit3();
        this.bmsAlarmBit4 = bmsAlarmBit4();
        this.bmsAlarmBit5 = bmsAlarmBit5();
        this.bmsAlarmBit6 = bmsAlarmBit6();
        this.bmsAlarmBit7 = bmsAlarmBit7();
        this.bmsFaultBit0 = bmsFaultBit0();
        this.bmsFaultBit1 = bmsFaultBit1();
        this.bmsFaultBit2 = bmsFaultBit2();
        this.bmsFaultBit3 = bmsFaultBit3();
        this.bmsFaultBit4 = bmsFaultBit4();
        this.bmsFaultBit5 = bmsFaultBit5();
        this.bmsFaultBit6 = bmsFaultBit6();
        this.bmsFaultBit7 = bmsFaultBit7();
        this.setState = setState();
        this.state = state();
        this.stateTick = stateTick();
        this.stateSubTick = stateSubTick();
        this.faultFlag = faultFlag();
        this.setPower = setPower();
        this.setReactivePower = setReactivePower();
        this.rightLimitPower = rightLimitPower();
        this.leftLimitPower = leftLimitPower();
        this.currentPower = currentPower();
        this.currentReactivePower = currentReactivePower();
        this.dcdcLimitPower = dcdcLimitPower();
        this.dcdcCurrentPower = dcdcCurrentPower();
        this.meterControlPower = meterControlPower();
        this.powerByUdp = powerByUdp();
        this.powerByPlugin = powerByPlugin();
        this.gpsTriggerFailedCount = gpsTriggerFailedCount();
        this.gpsLoopFailedCount = gpsLoopFailedCount();
        this.gpsLoopSuccessCount = gpsLoopSuccessCount();
        this.bmsTriggerFailedCount = bmsTriggerFailedCount();
        this.bmsLoopFailedCount = bmsLoopFailedCount();
        this.bmsLoopSuccessCount = bmsLoopSuccessCount();
        this.emsMeterTriggerFailedCount = emsMeterTriggerFailedCount();
        this.emsMeterLoopFailedCount = emsMeterLoopFailedCount();
        this.emsMeterLoopSuccessCount = emsMeterLoopSuccessCount();
        this.DCMeterTriggerFailedCount = DCMeterTriggerFailedCount();
        this.DCMeterLoopFailedCount = DCMeterLoopFailedCount();
        this.DCMeterLoopSuccessCount = DCMeterLoopSuccessCount();
    }

    @Override
    @NotNull
    public String vendorType() {
        return "WeihengEms200";
    }

    @Override
    @NotNull
    public int[] data() {
        return data;
    }

    /** EMS序列号 */
    public String emsSerial() {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 15; i++) {
                if (data[+i] == 0) {
                    continue;
                }
                value.append((char) (data[+i] >> 8));
                value.append((char) (data[+i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** EMS100软件版本号 */
    public String ems100Version() {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[+i] == 0) {
                    continue;
                }
                value.append((char) (data[+i] >> 8));
                value.append((char) (data[+i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** EMS软件版本号 */
    private int emsSoftwareVersion() {
        if (data != null) {
            return data[40];
        }
        return 0;
    }

    /** EMS硬件版本号 */
    private int emsHardwareVersion() {
        if (data != null) {
            return data[41];
        }
        return 0;
    }

    /** EMS通信协议版本号 */
    private int emsProtocolVersion() {
        if (data != null) {
            return data[42];
        }
        return 0;
    }

    /** UTC时间(s) */
    private double emsUtc() {
        if (data != null && data.length > (50 + 1)) {
            int high = data[50];
            int low = data[50 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 运行时间(ms) */
    private double emsUptime() {
        if (data != null && data.length > (52 + 1)) {
            int high = data[52];
            int low = data[52 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** CPU负载(0-100) */
    private double emsCpuLoad() {
        if (data != null) {
            return (double) data[54] / 10;
        }
        return 0;
    }

    /** 内存负载(0-100) */
    private double emsMemoryLoad() {
        if (data != null) {
            return (double) data[55] / 10;
        }
        return 0;
    }

    /** 磁盘负载(0-100) */
    private double emsDiskLoad() {
        if (data != null) {
            return (double) data[56] / 10;
        }
        return 0;
    }

    /** 是否配备热备(1有效) */
    private int emsHotBackupValid() {
        if (data != null) {
            return data[57];
        }
        return 0;
    }

    /** 热备运行中机器编号 */
    private int emsHotBackupRunningCode() {
        if (data != null) {
            return data[58];
        }
        return 0;
    }

    /** 热备当前等级(0: 无法切换, 1: 可实时切换) */
    private int emsHotBackupLevel() {
        if (data != null) {
            return data[59];
        }
        return 0;
    }

    /** GPS在线状态 */
    private int gpsOnline() {
        if (data != null) {
            return data[60];
        }
        return 0;
    }

    /** GPS纬度 */
    private double gpsLatitude() {
        if (data != null && data.length > (61 + 1)) {
            int high = data[61];
            int low = data[61 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1000000;
        }
        return 0;
    }

    /** GPS经度 */
    private double gpsLongitude() {
        if (data != null && data.length > (63 + 1)) {
            int high = data[63];
            int low = data[63 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1000000;
        }
        return 0;
    }

    /** PCS数量(PCS_COUNT <= 20) */
    private int pcsCount() {
        if (data != null) {
            return data[80];
        }
        return 0;
    }

    /** DCDC数量(<=20) */
    private int dcdcCount() {
        if (data != null) {
            return data[81];
        }
        return 0;
    }

    /** BMS数量(BMS_COUNT <= 1) */
    private int bmsCount() {
        if (data != null) {
            return data[82];
        }
        return 0;
    }

    /** 空调数量(AIR_COUNT <= 20) */
    private int airConditionerCount() {
        if (data != null) {
            return data[83];
        }
        return 0;
    }

    /** 水冷机数量(WATER_COUNT <= 20) */
    private int waterCoolerCount() {
        if (data != null) {
            return data[84];
        }
        return 0;
    }

    /** 消防设备数量(FIRE_COUNT <= 20) */
    private int firefightingCount() {
        if (data != null) {
            return data[85];
        }
        return 0;
    }

    /** STATES数量(<=20) */
    private int statesCount() {
        if (data != null) {
            return data[86];
        }
        return 0;
    }

    /** EMS电表数量(<=1,计量PCS交流测) */
    private int emsMeterCount() {
        if (data != null) {
            return data[87];
        }
        return 0;
    }

    /** DCDC电表数量(<=1,计量光伏侧直流) */
    private int dcdcMeterCount() {
        if (data != null) {
            return data[88];
        }
        return 0;
    }

    /** GPS数量(<=1) */
    private int gpsCount() {
        if (data != null) {
            return data[89];
        }
        return 0;
    }

    /** DC_BUS电表数量(<=20,外接直流母线计量电表) */
    private int dcBusMeterCount() {
        if (data != null) {
            return data[90];
        }
        return 0;
    }

    /** EMS系统类型码 */
    private int systemTypeCode() {
        if (data != null) {
            return data[100];
        }
        return 0;
    }

    /** EMS设计功率(kW) */
    private int emsDesignPower() {
        if (data != null) {
            return data[101];
        }
        return 0;
    }

    /** EMS设计存储能力(kWh) */
    private int emsDesignStorageEnergy() {
        if (data != null) {
            return data[102];
        }
        return 0;
    }

    /** EMS总充电能力(kW) */
    private int emsDesignChargePower() {
        if (data != null) {
            return data[103];
        }
        return 0;
    }

    /** EMS总放电能力(kW) */
    private int emsDesignDischargePower() {
        if (data != null) {
            return data[104];
        }
        return 0;
    }

    /** EMS运行状态(0:正常,1:告警,2:故障) */
    private int emsState() {
        if (data != null) {
            return data[110];
        }
        return 0;
    }

    /** EMS开机状态(0:关,1:开) */
    private int systemRunStatus() {
        if (data != null) {
            return data[111];
        }
        return 0;
    }

    /** EMS启动中或停机(0:否,1:是) */
    private int systemStartingOrStopping() {
        if (data != null) {
            return data[112];
        }
        return 0;
    }

    /** EMS并离网状态(0:并,1:离) */
    private int systemOffGrid() {
        if (data != null) {
            return data[113];
        }
        return 0;
    }

    /** EMS自动并离网功能开启(0:否,1:是) */
    private int systemAutoGridOnoff() {
        if (data != null) {
            return data[114];
        }
        return 0;
    }

    /** UDP指令直接控制中(0:否,1:是) */
    private int directPowerControlCounter() {
        if (data != null) {
            return data[115];
        }
        return 0;
    }

    /** SOC(0~100) */
    private double emsSoc() {
        if (data != null) {
            return (double) data[120] / 10;
        }
        return 0;
    }

    /** SOH(0~100) */
    private double emsSoh() {
        if (data != null) {
            return (double) data[121] / 10;
        }
        return 0;
    }

    /** EMS交流侧功率(kW,正为放电) */
    private float emsAcActivePower() {
        if (data != null) {
            return (float) ((short) data[122]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧无功功率(kVA,正为输出) */
    private float emsAcReactivePower() {
        if (data != null) {
            return (float) ((short) data[123]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧功率因数(-1~1) */
    private float emsAcPowerFactor() {
        if (data != null) {
            return (float) ((short) data[124]) / 100;
        }
        return (short) 0;
    }

    /** EMS交流侧频率(Hz) */
    private double emsAcFrequency() {
        if (data != null) {
            return (double) data[125] / 100;
        }
        return 0;
    }

    /** EMS交流侧功率正部分(kW) */
    private float emsAcActivePowerPos() {
        if (data != null) {
            return (float) ((short) data[126]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧功率负部分(kW) */
    private float emsAcActivePowerNeg() {
        if (data != null) {
            return (float) ((short) data[127]) / 10;
        }
        return (short) 0;
    }

    /** EMS当前充电功率上限(kW,变化值) */
    private int emsChargePowerLimit() {
        if (data != null) {
            return data[128];
        }
        return 0;
    }

    /** EMS当前放电功率上限(kW,变化值) */
    private int emsDischargePowerLimit() {
        if (data != null) {
            return data[129];
        }
        return 0;
    }

    /** EMS当前可用容量(kVA,变化值) */
    private int emsCapacityLimit() {
        if (data != null) {
            return data[130];
        }
        return 0;
    }

    /** EMS历史放电量(kWh) */
    private double emsHistoryOutputEnergy() {
        if (data != null && data.length > (131 + 1)) {
            int high = data[131];
            int low = data[131 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** EMS历史充电量(kWh) */
    private double emsHistoryInputEnergy() {
        if (data != null && data.length > (133 + 1)) {
            int high = data[133];
            int low = data[133 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** EMS直流侧电压(V) */
    private float emsDcVoltage() {
        if (data != null) {
            return (float) ((short) data[135]) / 10;
        }
        return (short) 0;
    }

    /** EMS直流侧电流(A,正为放电) */
    private float emsDcCurrent() {
        if (data != null) {
            return (float) ((short) data[136]) / 10;
        }
        return (short) 0;
    }

    /** EMS直流侧功率(kW,正为放电) */
    private float emsDcPower() {
        if (data != null) {
            return (float) ((short) data[137]) / 10;
        }
        return (short) 0;
    }

    /** EMS直流侧功率正部分(kW) */
    private float emsDcPowerPos() {
        if (data != null) {
            return (float) ((short) data[138]) / 10;
        }
        return (short) 0;
    }

    /** EMS直流侧功率负部分(kW) */
    private float emsDcPowerNeg() {
        if (data != null) {
            return (float) ((short) data[139]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧A相电流(A) */
    private float emsAcCurrentA() {
        if (data != null) {
            return (float) ((short) data[140]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧B相电流(A) */
    private float emsAcCurrentB() {
        if (data != null) {
            return (float) ((short) data[141]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧C相电流(A) */
    private float emsAcCurrentC() {
        if (data != null) {
            return (float) ((short) data[142]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧AB线电压(V) */
    private float emsAcVoltageAB() {
        if (data != null) {
            return (float) ((short) data[143]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧BC线电压(V) */
    private float emsAcVoltageBC() {
        if (data != null) {
            return (float) ((short) data[144]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧CA线电压(V) */
    private float emsAcVoltageCA() {
        if (data != null) {
            return (float) ((short) data[145]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧A相电压(V) */
    private float emsAcVoltageA() {
        if (data != null) {
            return (float) ((short) data[146]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧B相电压(V) */
    private float emsAcVoltageB() {
        if (data != null) {
            return (float) ((short) data[147]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧C相电压(V) */
    private float emsAcVoltageC() {
        if (data != null) {
            return (float) ((short) data[148]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧A相有功功率(kW) */
    private float emsAcActivePowerA() {
        if (data != null) {
            return (float) ((short) data[149]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧B相有功功率(kW) */
    private float emsAcActivePowerB() {
        if (data != null) {
            return (float) ((short) data[150]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧C相有功功率(kW) */
    private float emsAcActivePowerC() {
        if (data != null) {
            return (float) ((short) data[151]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧A相无功功率(kVA) */
    private float emsAcReactivePowerA() {
        if (data != null) {
            return (float) ((short) data[152]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧B相无功功率(kVA) */
    private float emsAcReactivePowerB() {
        if (data != null) {
            return (float) ((short) data[153]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧C相无功功率(kVA) */
    private float emsAcReactivePowerC() {
        if (data != null) {
            return (float) ((short) data[154]) / 10;
        }
        return (short) 0;
    }

    /** EMS交流侧A相功率因数(-1~1) */
    private float emsAcPowerFactorA() {
        if (data != null) {
            return (float) ((short) data[155]) / 100;
        }
        return (short) 0;
    }

    /** EMS交流侧B相功率因数(-1~1) */
    private float emsAcPowerFactorB() {
        if (data != null) {
            return (float) ((short) data[156]) / 100;
        }
        return (short) 0;
    }

    /** EMS交流侧C相功率因数(-1~1) */
    private float emsAcPowerFactorC() {
        if (data != null) {
            return (float) ((short) data[157]) / 100;
        }
        return (short) 0;
    }

    /** EMS光伏侧直流电压(V) */
    private float dcdcMeterVoltage() {
        if (data != null) {
            return (float) ((short) data[158]) / 10;
        }
        return (short) 0;
    }

    /** EMS光伏侧直流电流(A,正为向EMS放电) */
    private float dcdcMeterCurrent() {
        if (data != null) {
            return (float) ((short) data[159]) / 10;
        }
        return (short) 0;
    }

    /** EMS光伏侧直流功率(kW,正为向EMS放电) */
    private float dcdcMeterPower() {
        if (data != null) {
            return (float) ((short) data[160]) / 10;
        }
        return (short) 0;
    }

    /** EMS光伏侧直流历史输出能量(kWh,向EMS放电) */
    private double dcdcMeterHistoryEnergyPos() {
        if (data != null && data.length > (161 + 1)) {
            int high = data[161];
            int low = data[161 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** EMS光伏侧直流历史输入能量(kWh,从EMS充电) */
    private double dcdcMeterHistoryEnergyNeg() {
        if (data != null && data.length > (163 + 1)) {
            int high = data[163];
            int low = data[163 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** EMS外接直流母线直流电压(V) */
    private float dcBusVoltage() {
        if (data != null) {
            return (float) ((short) data[165]) / 10;
        }
        return (short) 0;
    }

    /** EMS外接直流母线直流电流(A,正为向EMS放电) */
    private float dcBusCurrent() {
        if (data != null) {
            return (float) ((short) data[166]) / 10;
        }
        return (short) 0;
    }

    /** EMS外接直流母线直流功率(kW,正为向EMS放电) */
    private float dcBusPower() {
        if (data != null) {
            return (float) ((short) data[167]) / 10;
        }
        return (short) 0;
    }

    /** EMS外接直流母线历史输出能量(kWh,向EMS放电) */
    private double dcBusHistoryEnergyPos() {
        if (data != null && data.length > (168 + 1)) {
            int high = data[168];
            int low = data[168 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** EMS外接直流母线历史输入能量(kWh,从EMS充电) */
    private double dcBusHistoryEnergyNeg() {
        if (data != null && data.length > (170 + 1)) {
            int high = data[170];
            int low = data[170 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** EMS外接直流母线当前充电功率上限(kW,给EMS充电) */
    private int dcBusChargeLimit() {
        if (data != null) {
            return data[172];
        }
        return 0;
    }

    /** EMS外接直流母线当前放电功率上限(kW,从EMS放电) */
    private int dcBusDischargeLimit() {
        if (data != null) {
            return data[173];
        }
        return 0;
    }

    /** EMS系统状态BIT位0 */
    private int systemStateBit0() {
        if (data != null) {
            return data[180];
        }
        return 0;
    }

    /** EMS系统状态BIT位1 */
    private int systemStateBit1() {
        if (data != null) {
            return data[181];
        }
        return 0;
    }

    /** EMS系统状态BIT位2 */
    private int systemStateBit2() {
        if (data != null) {
            return data[182];
        }
        return 0;
    }

    /** EMS系统状态BIT位3 */
    private int systemStateBit3() {
        if (data != null) {
            return data[183];
        }
        return 0;
    }

    /** EMS告警状态BIT位0 */
    private int systemAlarmBit0() {
        if (data != null) {
            return data[184];
        }
        return 0;
    }

    /** EMS告警状态BIT位1 */
    private int systemAlarmBit1() {
        if (data != null) {
            return data[185];
        }
        return 0;
    }

    /** EMS故障状态BIT位0 */
    private int systemFaultBit0() {
        if (data != null) {
            return data[186];
        }
        return 0;
    }

    /** EMS故障状态BIT位1 */
    private int systemFaultBit1() {
        if (data != null) {
            return data[187];
        }
        return 0;
    }

    /** EMS故障状态BIT位2 */
    private int systemFaultBit2() {
        if (data != null) {
            return data[188];
        }
        return 0;
    }

    /** EMS故障状态BIT位3 */
    private int systemFaultBit3() {
        if (data != null) {
            return data[189];
        }
        return 0;
    }

    /** PCSi类型码 */
    public int getPcs_I_TypeCode(int index) {
        if (data != null) {
            return data[500 + 150 * index + 0];
        }
        return 0;
    }

    /** 设计最大容量(kVA) */
    public int getPcs_I_DesignCapacity(int index) {
        if (data != null) {
            return data[500 + 150 * index + 1];
        }
        return 0;
    }

    /** 设计最高直流电压(V) */
    public int getPcs_I_DesignMaxDcVoltage(int index) {
        if (data != null) {
            return data[500 + 150 * index + 2];
        }
        return 0;
    }

    /** 设计最低直流电压(V) */
    public int getPcs_I_DesignMinDcVoltage(int index) {
        if (data != null) {
            return data[500 + 150 * index + 3];
        }
        return 0;
    }

    /** 设计交流电压(V) */
    public int getPcs_I_DesignAcVoltage(int index) {
        if (data != null) {
            return data[500 + 150 * index + 4];
        }
        return 0;
    }

    /** 设计最大直流电流(A) */
    public int getPcs_I_DesignMaxDcCurrent(int index) {
        if (data != null) {
            return data[500 + 150 * index + 5];
        }
        return 0;
    }

    /** PCSi版本号 */
    public String getPcs_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[500 + 150 * index + 10 + i] == 0) {
                    continue;
                }
                value.append((char) (data[500 + 150 * index + 10 + i] >> 8));
                value.append((char) (data[500 + 150 * index + 10 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** PCSi运行状态(0:正常,1:告警,2:故障) */
    public int getPcs_I_State(int index) {
        if (data != null) {
            return data[500 + 150 * index + 30];
        }
        return 0;
    }

    /** PCSi在线状态(0:掉线,1:在线) */
    public int getPcs_I_Online(int index) {
        if (data != null) {
            return data[500 + 150 * index + 31];
        }
        return 0;
    }

    /** PCSi开机状态(0:关,1:开) */
    public int getPcs_I_Onoff(int index) {
        if (data != null) {
            return data[500 + 150 * index + 32];
        }
        return 0;
    }

    /** 功率(kW,正为放电) */
    public float getPcs_I_ActivePower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 40]) / 10;
        }
        return (short) 0;
    }

    /** 无功功率(kVA,正为输出) */
    public float getPcs_I_ReactivePower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 41]) / 10;
        }
        return (short) 0;
    }

    /** 频率(Hz) */
    public double getPcs_I_Frequency(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 42] / 100;
        }
        return 0;
    }

    /** 电网线电压(V) */
    public double getPcs_I_Voltage(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 43] / 10;
        }
        return 0;
    }

    /** 交流电流(A) */
    public double getPcs_I_Current(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 44] / 10;
        }
        return 0;
    }

    /** A相电压(V) */
    public double getPcs_I_Voltage1(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 45] / 10;
        }
        return 0;
    }

    /** A相电流(A) */
    public double getPcs_I_Current1(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 46] / 10;
        }
        return 0;
    }

    /** A相有功功率(kW,正为放电) */
    public float getPcs_I_ActivePower1(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 47]) / 10;
        }
        return (short) 0;
    }

    /** A相无功功率(kVA,正为输出) */
    public float getPcs_I_ReactivePower1(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 48]) / 10;
        }
        return (short) 0;
    }

    /** B相电压(V) */
    public double getPcs_I_Voltage2(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 49] / 10;
        }
        return 0;
    }

    /** B相电流(A) */
    public double getPcs_I_Current2(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 50] / 10;
        }
        return 0;
    }

    /** B相有功功率(kW,正为放电) */
    public float getPcs_I_ActivePower2(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 51]) / 10;
        }
        return (short) 0;
    }

    /** B相无功功率(kVA,正为输出) */
    public float getPcs_I_ReactivePower2(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 52]) / 10;
        }
        return (short) 0;
    }

    /** C相电压(V) */
    public double getPcs_I_Voltage3(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 53] / 10;
        }
        return 0;
    }

    /** C相电流(A) */
    public double getPcs_I_Current3(int index) {
        if (data != null) {
            return (double) data[500 + 150 * index + 54] / 10;
        }
        return 0;
    }

    /** C相有功功率(kW,正为放电) */
    public float getPcs_I_ActivePower3(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 55]) / 10;
        }
        return (short) 0;
    }

    /** C相无功功率(kVA,正为输出) */
    public float getPcs_I_ReactivePower3(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 56]) / 10;
        }
        return (short) 0;
    }

    /** 空气温度(deg) */
    public float getPcs_I_AirTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 57]) / 10;
        }
        return (short) 0;
    }

    /** IGBT温度(deg) */
    public float getPcs_I_IGBTTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 58]) / 10;
        }
        return (short) 0;
    }

    /** 功率因数(-1~1) */
    public float getPcs_I_PowerFactor(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 59]) / 100;
        }
        return (short) 0;
    }

    /** 直流侧电压(V) */
    public float getPcs_I_DcVoltage(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 60]) / 10;
        }
        return (short) 0;
    }

    /** 直流侧电流(A,正为输出) */
    public float getPcs_I_DcCurrent(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 61]) / 10;
        }
        return (short) 0;
    }

    /** 直流侧功率(kW) */
    public float getPcs_I_DcPower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 62]) / 10;
        }
        return (short) 0;
    }

    /** PCS计量电表功率(kW,正为放电,若无电表时等于PCS功率，用于单个PCS功率控制) */
    public float getPcs_I_MeterActivePower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 63]) / 10;
        }
        return (short) 0;
    }

    /** PCS计量电表无功功率(kVA,正为输出,若无电表时等于PCS无功功率,用于单个PCS功率控制) */
    public float getPcs_I_MeterReactivePower(int index) {
        if (data != null) {
            return (float) ((short) data[500 + 150 * index + 64]) / 10;
        }
        return (short) 0;
    }

    /** PCSi状态BIT位0 */
    public int getPcs_I_StateBit0(int index) {
        if (data != null) {
            return data[500 + 150 * index + 80];
        }
        return 0;
    }

    /** PCSi状态BIT位1 */
    public int getPcs_I_StateBit1(int index) {
        if (data != null) {
            return data[500 + 150 * index + 81];
        }
        return 0;
    }

    /** PCSi状态BIT位2 */
    public int getPcs_I_StateBit2(int index) {
        if (data != null) {
            return data[500 + 150 * index + 82];
        }
        return 0;
    }

    /** PCSi状态BIT位3 */
    public int getPcs_I_StateBit3(int index) {
        if (data != null) {
            return data[500 + 150 * index + 83];
        }
        return 0;
    }

    /** PCSi告警BIT位0 */
    public int getPcs_I_AlarmBit0(int index) {
        if (data != null) {
            return data[500 + 150 * index + 84];
        }
        return 0;
    }

    /** PCSi告警BIT位1 */
    public int getPcs_I_AlarmBit1(int index) {
        if (data != null) {
            return data[500 + 150 * index + 85];
        }
        return 0;
    }

    /** PCSi告警BIT位2 */
    public int getPcs_I_AlarmBit2(int index) {
        if (data != null) {
            return data[500 + 150 * index + 86];
        }
        return 0;
    }

    /** PCSi告警BIT位3 */
    public int getPcs_I_AlarmBit3(int index) {
        if (data != null) {
            return data[500 + 150 * index + 87];
        }
        return 0;
    }

    /** PCSi告警BIT位4 */
    public int getPcs_I_AlarmBit4(int index) {
        if (data != null) {
            return data[500 + 150 * index + 88];
        }
        return 0;
    }

    /** PCSi告警BIT位5 */
    public int getPcs_I_AlarmBit5(int index) {
        if (data != null) {
            return data[500 + 150 * index + 89];
        }
        return 0;
    }

    /** PCSi告警BIT位6 */
    public int getPcs_I_AlarmBit6(int index) {
        if (data != null) {
            return data[500 + 150 * index + 90];
        }
        return 0;
    }

    /** PCSi告警BIT位7 */
    public int getPcs_I_AlarmBit7(int index) {
        if (data != null) {
            return data[500 + 150 * index + 91];
        }
        return 0;
    }

    /** PCSi故障BIT位0 */
    public int getPcs_I_FaultBit0(int index) {
        if (data != null) {
            return data[500 + 150 * index + 92];
        }
        return 0;
    }

    /** PCSi故障BIT位1 */
    public int getPcs_I_FaultBit1(int index) {
        if (data != null) {
            return data[500 + 150 * index + 93];
        }
        return 0;
    }

    /** PCSi故障BIT位2 */
    public int getPcs_I_FaultBit2(int index) {
        if (data != null) {
            return data[500 + 150 * index + 94];
        }
        return 0;
    }

    /** PCSi故障BIT位3 */
    public int getPcs_I_FaultBit3(int index) {
        if (data != null) {
            return data[500 + 150 * index + 95];
        }
        return 0;
    }

    /** PCSi故障BIT位4 */
    public int getPcs_I_FaultBit4(int index) {
        if (data != null) {
            return data[500 + 150 * index + 96];
        }
        return 0;
    }

    /** PCSi故障BIT位5 */
    public int getPcs_I_FaultBit5(int index) {
        if (data != null) {
            return data[500 + 150 * index + 97];
        }
        return 0;
    }

    /** PCSi故障BIT位6 */
    public int getPcs_I_FaultBit6(int index) {
        if (data != null) {
            return data[500 + 150 * index + 98];
        }
        return 0;
    }

    /** PCSi故障BIT位7 */
    public int getPcs_I_FaultBit7(int index) {
        if (data != null) {
            return data[500 + 150 * index + 99];
        }
        return 0;
    }

    /** DCDC设备类型码 */
    public int getDcdc_I_TypeCode(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 0];
        }
        return 0;
    }

    /** DCDC设计功率(kW) */
    public double getDcdc_I_DesignPower(int index) {
        if (data != null) {
            return (double) data[3500 + 150 * index + 1] / 10;
        }
        return 0;
    }

    /** DCDC光伏侧设计功率(kW) */
    public double getDcdc_I_PvDesignPower(int index) {
        if (data != null) {
            return (double) data[3500 + 150 * index + 2] / 10;
        }
        return 0;
    }

    /** DCDCi版本号 */
    public String getDcdc_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[3500 + 150 * index + 10 + i] == 0) {
                    continue;
                }
                value.append((char) (data[3500 + 150 * index + 10 + i] >> 8));
                value.append((char) (data[3500 + 150 * index + 10 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** DCDCi运行状态(0:正常,1:告警,2:故障) */
    public int getDcdc_I_State(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 30];
        }
        return 0;
    }

    /** DCDCi在线状态(0:掉线,1:在线) */
    public int getDcdc_I_Online(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 31];
        }
        return 0;
    }

    /** DCDCi开机状态(0:关,1:开) */
    public int getDcdc_I_Onoff(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 32];
        }
        return 0;
    }

    /** 限制功率(kW) */
    public double getDcdc_I_LimitPower(int index) {
        if (data != null) {
            return (double) data[3500 + 150 * index + 40] / 10;
        }
        return 0;
    }

    /** 光伏侧当前功率(kW,正为向EMS放电) */
    public float getDcdc_I_PvCurrentPower(int index) {
        if (data != null) {
            return (float) ((short) data[3500 + 150 * index + 41]) / 10;
        }
        return (short) 0;
    }

    /** 光伏侧电压(V) */
    public double getDcdc_I_PvVoltage(int index) {
        if (data != null) {
            return (double) data[3500 + 150 * index + 42] / 10;
        }
        return 0;
    }

    /** 光伏侧电流(A,正为向EMS放电) */
    public float getDcdc_I_PvCurrent(int index) {
        if (data != null) {
            return (float) ((short) data[3500 + 150 * index + 43]) / 10;
        }
        return (short) 0;
    }

    /** 电池侧当前功率(kW,正为向EMS放电) */
    public float getDcdc_I_BatCurrentPower(int index) {
        if (data != null) {
            return (float) ((short) data[3500 + 150 * index + 44]) / 10;
        }
        return (short) 0;
    }

    /** 电池侧电压(V) */
    public double getDcdc_I_BatVoltage(int index) {
        if (data != null) {
            return (double) data[3500 + 150 * index + 45] / 10;
        }
        return 0;
    }

    /** 电池侧电流(A,正为向EMS放电) */
    public float getDcdc_I_BatCurrent(int index) {
        if (data != null) {
            return (float) ((short) data[3500 + 150 * index + 46]) / 10;
        }
        return (short) 0;
    }

    /** 历史放电量(kWh,向EMS放电) */
    public double getDcdc_I_HistoryDischargeEnergy(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 历史充电量(kWh,从EMS充电) */
    public double getDcdc_I_HistoryChargeEnergy(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** DCDCi状态BIT位0 */
    public int getDcdc_I_StateBit0(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 80];
        }
        return 0;
    }

    /** DCDCi状态BIT位1 */
    public int getDcdc_I_StateBit1(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 81];
        }
        return 0;
    }

    /** DCDCi状态BIT位2 */
    public int getDcdc_I_StateBit2(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 82];
        }
        return 0;
    }

    /** DCDCi状态BIT位3 */
    public int getDcdc_I_StateBit3(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 83];
        }
        return 0;
    }

    /** DCDCi告警BIT位0 */
    public int getDcdc_I_AlarmBit0(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 84];
        }
        return 0;
    }

    /** DCDCi告警BIT位1 */
    public int getDcdc_I_AlarmBit1(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 85];
        }
        return 0;
    }

    /** DCDCi告警BIT位2 */
    public int getDcdc_I_AlarmBit2(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 86];
        }
        return 0;
    }

    /** DCDCi告警BIT位3 */
    public int getDcdc_I_AlarmBit3(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 87];
        }
        return 0;
    }

    /** DCDCi告警BIT位4 */
    public int getDcdc_I_AlarmBit4(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 88];
        }
        return 0;
    }

    /** DCDCi告警BIT位5 */
    public int getDcdc_I_AlarmBit5(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 89];
        }
        return 0;
    }

    /** DCDCi告警BIT位6 */
    public int getDcdc_I_AlarmBit6(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 90];
        }
        return 0;
    }

    /** DCDCi告警BIT位7 */
    public int getDcdc_I_AlarmBit7(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 91];
        }
        return 0;
    }

    /** DCDCi故障BIT位0 */
    public int getDcdc_I_FaultBit0(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 92];
        }
        return 0;
    }

    /** DCDCi故障BIT位1 */
    public int getDcdc_I_FaultBit1(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 93];
        }
        return 0;
    }

    /** DCDCi故障BIT位2 */
    public int getDcdc_I_FaultBit2(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 94];
        }
        return 0;
    }

    /** DCDCi故障BIT位3 */
    public int getDcdc_I_FaultBit3(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 95];
        }
        return 0;
    }

    /** DCDCi故障BIT位4 */
    public int getDcdc_I_FaultBit4(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 96];
        }
        return 0;
    }

    /** DCDCi故障BIT位5 */
    public int getDcdc_I_FaultBit5(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 97];
        }
        return 0;
    }

    /** DCDCi故障BIT位6 */
    public int getDcdc_I_FaultBit6(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 98];
        }
        return 0;
    }

    /** DCDCi故障BIT位7 */
    public int getDcdc_I_FaultBit7(int index) {
        if (data != null) {
            return data[3500 + 150 * index + 99];
        }
        return 0;
    }

    /** BMS类型码 */
    private int bmsTypeCode() {
        if (data != null) {
            return data[6500];
        }
        return 0;
    }

    /** 电池簇数量(<=30) */
    private int bmsClusterCount() {
        if (data != null) {
            return data[6501];
        }
        return 0;
    }

    /** 每簇电池组数量(<=30) */
    private int bmsStackPerClusterCount() {
        if (data != null) {
            return data[6502];
        }
        return 0;
    }

    /** 每组电芯数量(<=100) */
    private int bmsCellPerStackCount() {
        if (data != null) {
            return data[6503];
        }
        return 0;
    }

    /** 每组电芯温度传感器数量(<=100) */
    private int bmsTemperaturePerStackCount() {
        if (data != null) {
            return data[6504];
        }
        return 0;
    }

    /** 设计存储能力(kWh) */
    private int bmsDesignEnergy() {
        if (data != null) {
            return data[6505];
        }
        return 0;
    }

    /** 最高设计电压(V) */
    private double bmsDesignMaxVoltage() {
        if (data != null) {
            return (double) data[6506] / 10;
        }
        return 0;
    }

    /** 最低设计电压(V) */
    private double bmsDesignMinVoltage() {
        if (data != null) {
            return (double) data[6507] / 10;
        }
        return 0;
    }

    /** 最高设计放电电流(A) */
    private double bmsDesignMaxDischargeCurrent() {
        if (data != null) {
            return (double) data[6508] / 10;
        }
        return 0;
    }

    /** 最高设计充电电流(A) */
    private double bmsDesignMaxChargeCurrent() {
        if (data != null) {
            return (double) data[6509] / 10;
        }
        return 0;
    }

    /** 最高设计单体电芯电压(V) */
    private double bmsDesignCellMaxVoltage() {
        if (data != null) {
            return (double) data[6510] / 1000;
        }
        return 0;
    }

    /** 最低设计单体电芯电压(V) */
    private double bmsDesignCellMinVoltage() {
        if (data != null) {
            return (double) data[6511] / 1000;
        }
        return 0;
    }

    /** 最高设计放电功率(kW) */
    private double bmsDesignMaxDischargePower() {
        if (data != null) {
            return (double) data[6512] / 10;
        }
        return 0;
    }

    /** 最高设计充电功率(kW) */
    private double bmsDesignMaxChargePower() {
        if (data != null) {
            return (double) data[6513] / 10;
        }
        return 0;
    }

    /** BMS版本号 */
    public String bmsVersion() {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[+i] == 0) {
                    continue;
                }
                value.append((char) (data[+i] >> 8));
                value.append((char) (data[+i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** BMS运行状态(0:正常,1:告警,2:故障) */
    private int bmsState() {
        if (data != null) {
            return data[6540];
        }
        return 0;
    }

    /** BMS在线状态(0:掉线,1:在线) */
    private int bmsOnline() {
        if (data != null) {
            return data[6541];
        }
        return 0;
    }

    /** BMS开机状态(0:关,1:开) */
    private int bmsOnoff() {
        if (data != null) {
            return data[6542];
        }
        return 0;
    }

    /** SOC(0~100) */
    private double bmsSoc() {
        if (data != null) {
            return (double) data[6550] / 10;
        }
        return 0;
    }

    /** SOH(0~100) */
    private double bmsSoh() {
        if (data != null) {
            return (double) data[6551] / 10;
        }
        return 0;
    }

    /** 可放电能量(kWh) */
    private int bmsDischargeableEnergy() {
        if (data != null) {
            return data[6552];
        }
        return 0;
    }

    /** 可充电能量(kWh) */
    private int bmsChargeableEnergy() {
        if (data != null) {
            return data[6553];
        }
        return 0;
    }

    /** 当前电压(V) */
    private float bmsVoltage() {
        if (data != null) {
            return (float) ((short) data[6554]) / 10;
        }
        return (short) 0;
    }

    /** 当前电流(A) 正为放电 */
    private float bmsCurrent() {
        if (data != null) {
            return (float) ((short) data[6555]) / 10;
        }
        return (short) 0;
    }

    /** 环境温度(deg) */
    private float bmsAirTemperature() {
        if (data != null) {
            return (float) ((short) data[6556]) / 10;
        }
        return (short) 0;
    }

    /** 最高单体电压(V) */
    private double bmsCellHighVoltage() {
        if (data != null) {
            return (double) data[6557] / 1000;
        }
        return 0;
    }

    /** 最低单体电压(V) */
    private double bmsCellLowVoltage() {
        if (data != null) {
            return (double) data[6558] / 1000;
        }
        return 0;
    }

    /** 最高单体电压序号 */
    private int bmsCellHighVoltagePositioin() {
        if (data != null) {
            return data[6559];
        }
        return 0;
    }

    /** 最低单体电压序号 */
    private int bmsCellLowVoltagePosition() {
        if (data != null) {
            return data[6560];
        }
        return 0;
    }

    /** 最高单体温度(deg) */
    private float bmsCellHighTemperature() {
        if (data != null) {
            return (float) ((short) data[6561]) / 10;
        }
        return (short) 0;
    }

    /** 最低单体温度(deg) */
    private float bmsCellLowTemperature() {
        if (data != null) {
            return (float) ((short) data[6562]) / 10;
        }
        return (short) 0;
    }

    /** 最高单体温度充号 */
    private int bmsCellHighTemperaturePosition() {
        if (data != null) {
            return data[6563];
        }
        return 0;
    }

    /** 最低单体温度序号 */
    private int bmsCellLowTemperaturePosition() {
        if (data != null) {
            return data[6564];
        }
        return 0;
    }

    /** 单体平均电压(V) */
    private double bmsCellAverageVoltage() {
        if (data != null) {
            return (double) data[6565] / 1000;
        }
        return 0;
    }

    /** 单体平均温度(deg) */
    private float bmsCellAverageTemperature() {
        if (data != null) {
            return (float) ((short) data[6566]) / 10;
        }
        return (short) 0;
    }

    /** 最大放电电流(A) */
    private float bmsDischargeCurrentLimit() {
        if (data != null) {
            return (float) ((short) data[6567]) / 10;
        }
        return (short) 0;
    }

    /** 最大充电电流(A) */
    private float bmsChargeCurrentLimit() {
        if (data != null) {
            return (float) ((short) data[6568]) / 10;
        }
        return (short) 0;
    }

    /** 历史放电量(kWh) */
    private double bmsHistoryDischargeEnergy() {
        if (data != null && data.length > (6569 + 1)) {
            int high = data[6569];
            int low = data[6569 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 历史充电量(kWh) */
    private double bmsHistoryChargeEnergy() {
        if (data != null && data.length > (6571 + 1)) {
            int high = data[6571];
            int low = data[6571 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** BMS状态BIT位0 */
    private int bmsStateBit0() {
        if (data != null) {
            return data[6580];
        }
        return 0;
    }

    /** BMS状态BIT位1 */
    private int bmsStateBit1() {
        if (data != null) {
            return data[6581];
        }
        return 0;
    }

    /** BMS状态BIT位2 */
    private int bmsStateBit2() {
        if (data != null) {
            return data[6582];
        }
        return 0;
    }

    /** BMS状态BIT位3 */
    private int bmsStateBit3() {
        if (data != null) {
            return data[6583];
        }
        return 0;
    }

    /** BMS告警BIT位0 */
    private int bmsAlarmBit0() {
        if (data != null) {
            return data[6584];
        }
        return 0;
    }

    /** BMS告警BIT位1 */
    private int bmsAlarmBit1() {
        if (data != null) {
            return data[6585];
        }
        return 0;
    }

    /** BMS告警BIT位2 */
    private int bmsAlarmBit2() {
        if (data != null) {
            return data[6586];
        }
        return 0;
    }

    /** BMS告警BIT位3 */
    private int bmsAlarmBit3() {
        if (data != null) {
            return data[6587];
        }
        return 0;
    }

    /** BMS告警BIT位4 */
    private int bmsAlarmBit4() {
        if (data != null) {
            return data[6588];
        }
        return 0;
    }

    /** BMS告警BIT位5 */
    private int bmsAlarmBit5() {
        if (data != null) {
            return data[6589];
        }
        return 0;
    }

    /** BMS告警BIT位6 */
    private int bmsAlarmBit6() {
        if (data != null) {
            return data[6590];
        }
        return 0;
    }

    /** BMS告警BIT位7 */
    private int bmsAlarmBit7() {
        if (data != null) {
            return data[6591];
        }
        return 0;
    }

    /** BMS故障BIT位0 */
    private int bmsFaultBit0() {
        if (data != null) {
            return data[6592];
        }
        return 0;
    }

    /** BMS故障BIT位1 */
    private int bmsFaultBit1() {
        if (data != null) {
            return data[6593];
        }
        return 0;
    }

    /** BMS故障BIT位2 */
    private int bmsFaultBit2() {
        if (data != null) {
            return data[6594];
        }
        return 0;
    }

    /** BMS故障BIT位3 */
    private int bmsFaultBit3() {
        if (data != null) {
            return data[6595];
        }
        return 0;
    }

    /** BMS故障BIT位4 */
    private int bmsFaultBit4() {
        if (data != null) {
            return data[6596];
        }
        return 0;
    }

    /** BMS故障BIT位5 */
    private int bmsFaultBit5() {
        if (data != null) {
            return data[6597];
        }
        return 0;
    }

    /** BMS故障BIT位6 */
    private int bmsFaultBit6() {
        if (data != null) {
            return data[6598];
        }
        return 0;
    }

    /** BMS故障BIT位7 */
    private int bmsFaultBit7() {
        if (data != null) {
            return data[6599];
        }
        return 0;
    }

    /** CLUSTERi版本号 */
    public String getBmsCluster_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[7500 + 150 * index + 10 + i] == 0) {
                    continue;
                }
                value.append((char) (data[7500 + 150 * index + 10 + i] >> 8));
                value.append((char) (data[7500 + 150 * index + 10 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** CLUSTERi状态(0:正常,1:告警,2:故障) */
    public int getBmsCluster_I_State(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 30];
        }
        return 0;
    }

    /** SOC(0~100) */
    public double getBmsCluster_I_Soc(int index) {
        if (data != null) {
            return (double) data[7500 + 150 * index + 40] / 10;
        }
        return 0;
    }

    /** SOH(0~100) */
    public double getBmsCluster_I_Soh(int index) {
        if (data != null) {
            return (double) data[7500 + 150 * index + 41] / 10;
        }
        return 0;
    }

    /** 可放电能量(kWh) */
    public int getBmsCluster_I_DischargeableEnergy(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 42];
        }
        return 0;
    }

    /** 可充电能量(kWh) */
    public int getBmsCluster_I_ChargeableEnergy(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 43];
        }
        return 0;
    }

    /** 当前电压(V) */
    public double getBmsCluster_I_Voltage(int index) {
        if (data != null) {
            return (double) data[7500 + 150 * index + 44] / 10;
        }
        return 0;
    }

    /** 当前电流(A) 正为放电 */
    public float getBmsCluster_I_Current(int index) {
        if (data != null) {
            return (float) ((short) data[7500 + 150 * index + 45]) / 10;
        }
        return (short) 0;
    }

    /** 环境温度(deg) */
    public float getBmsCluster_I_AirTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[7500 + 150 * index + 46]) / 10;
        }
        return (short) 0;
    }

    /** 最高单体电压(V) */
    public double getBmsCluster_I_CellHighVoltage(int index) {
        if (data != null) {
            return (double) data[7500 + 150 * index + 47] / 1000;
        }
        return 0;
    }

    /** 最低单体电压(V) */
    public double getBmsCluster_I_CellLowVoltage(int index) {
        if (data != null) {
            return (double) data[7500 + 150 * index + 48] / 1000;
        }
        return 0;
    }

    /** 最高单体电压序号 */
    public int getBmsCluster_I_CellHighVoltagePositioin(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 49];
        }
        return 0;
    }

    /** 最低单体电压序号 */
    public int getBmsCluster_I_CellLowVoltagePosition(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 50];
        }
        return 0;
    }

    /** 最高单体温度(deg) */
    public float getBmsCluster_I_CellHighTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[7500 + 150 * index + 51]) / 10;
        }
        return (short) 0;
    }

    /** 最低单体温度(deg) */
    public float getBmsCluster_I_CellLowTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[7500 + 150 * index + 52]) / 10;
        }
        return (short) 0;
    }

    /** 最高单体温度序号 */
    public int getBmsCluster_I_CellHighTemperaturePosition(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 53];
        }
        return 0;
    }

    /** 最低单体温度序号 */
    public int getBmsCluster_I_CellLowTemperaturePosition(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 54];
        }
        return 0;
    }

    /** 单体平均电压(V) */
    public double getBmsCluster_I_CellAverageVoltage(int index) {
        if (data != null) {
            return (double) data[7500 + 150 * index + 55] / 1000;
        }
        return 0;
    }

    /** 单体平均温度(deg) */
    public float getBmsCluster_I_CellAverageTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[7500 + 150 * index + 56]) / 10;
        }
        return (short) 0;
    }

    /** 最大放电电流(A) */
    public float getBmsCluster_I_DischargeCurrentLimit(int index) {
        if (data != null) {
            return (float) ((short) data[7500 + 150 * index + 57]) / 10;
        }
        return (short) 0;
    }

    /** 最大充电电流(A) */
    public float getBmsCluster_I_ChargeCurrentLimit(int index) {
        if (data != null) {
            return (float) ((short) data[7500 + 150 * index + 58]) / 10;
        }
        return (short) 0;
    }

    /** CLUSTERi状态BIT位0 */
    public int getBmsCluster_I_StateBit0(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 80];
        }
        return 0;
    }

    /** CLUSTERi状态BIT位1 */
    public int getBmsCluster_I_StateBit1(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 81];
        }
        return 0;
    }

    /** CLUSTERi状态BIT位2 */
    public int getBmsCluster_I_StateBit2(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 82];
        }
        return 0;
    }

    /** CLUSTERi状态BIT位3 */
    public int getBmsCluster_I_StateBit3(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 83];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位0 */
    public int getBmsCluster_I_AlarmBit0(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 84];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位1 */
    public int getBmsCluster_I_AlarmBit1(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 85];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位2 */
    public int getBmsCluster_I_AlarmBit2(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 86];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位3 */
    public int getBmsCluster_I_AlarmBit3(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 87];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位4 */
    public int getBmsCluster_I_AlarmBit4(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 88];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位5 */
    public int getBmsCluster_I_AlarmBit5(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 89];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位6 */
    public int getBmsCluster_I_AlarmBit6(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 90];
        }
        return 0;
    }

    /** CLUSTERi告警BIT位7 */
    public int getBmsCluster_I_AlarmBit7(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 91];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位0 */
    public int getBmsCluster_I_FaultBit0(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 92];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位1 */
    public int getBmsCluster_I_FaultBit1(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 93];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位2 */
    public int getBmsCluster_I_FaultBit2(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 94];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位3 */
    public int getBmsCluster_I_FaultBit3(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 95];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位4 */
    public int getBmsCluster_I_FaultBit4(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 96];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位5 */
    public int getBmsCluster_I_FaultBit5(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 97];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位6 */
    public int getBmsCluster_I_FaultBit6(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 98];
        }
        return 0;
    }

    /** CLUSTERi故障BIT位7 */
    public int getBmsCluster_I_FaultBit7(int index) {
        if (data != null) {
            return data[7500 + 150 * index + 99];
        }
        return 0;
    }

    /** 空调类型码 */
    public int getAirConditioner_I_TypeCode(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 0];
        }
        return 0;
    }

    /** 空调版本号 */
    public String getAirConditioner_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[12000 + 100 * index + 10 + i] == 0) {
                    continue;
                }
                value.append((char) (data[12000 + 100 * index + 10 + i] >> 8));
                value.append((char) (data[12000 + 100 * index + 10 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** 空调运行状态(0:正常,1:告警,2:故障) */
    public int getAirConditioner_I_State(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 30];
        }
        return 0;
    }

    /** 空调在线状态(0:掉线,1:在线) */
    public int getAirConditioner_I_Online(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 31];
        }
        return 0;
    }

    /** 空调开机状态(0:关,1:开) */
    public int getAirConditioner_I_Onoff(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 32];
        }
        return 0;
    }

    /** 温度(℃) */
    public float getAirConditioner_I_Temperature(int index) {
        if (data != null) {
            return (float) ((short) data[12000 + 100 * index + 40]) / 10;
        }
        return (short) 0;
    }

    /** 湿度(%) */
    public double getAirConditioner_I_Humidity(int index) {
        if (data != null) {
            return (double) data[12000 + 100 * index + 41] / 10;
        }
        return 0;
    }

    /** 设定制冷点(℃) */
    public float getAirConditioner_I_SetCool(int index) {
        if (data != null) {
            return (float) ((short) data[12000 + 100 * index + 42]) / 10;
        }
        return (short) 0;
    }

    /** 设定制热点(℃) */
    public float getAirConditioner_I_SetHot(int index) {
        if (data != null) {
            return (float) ((short) data[12000 + 100 * index + 43]) / 10;
        }
        return (short) 0;
    }

    /** 设定湿度(%) */
    public double getAirConditioner_I_SetHumidity(int index) {
        if (data != null) {
            return (double) data[12000 + 100 * index + 44] / 10;
        }
        return 0;
    }

    /** 状态BIT位0 */
    public int getAirConditioner_I_StateBit0(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 70];
        }
        return 0;
    }

    /** 状态BIT位1 */
    public int getAirConditioner_I_StateBit1(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 71];
        }
        return 0;
    }

    /** 故障BIT位0 */
    public int getAirConditioner_I_FaultBit0(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 72];
        }
        return 0;
    }

    /** 故障BIT位1 */
    public int getAirConditioner_I_FaultBit1(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 73];
        }
        return 0;
    }

    /** 故障BIT位2 */
    public int getAirConditioner_I_FaultBit2(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 74];
        }
        return 0;
    }

    /** 故障BIT位3 */
    public int getAirConditioner_I_FaultBit3(int index) {
        if (data != null) {
            return data[12000 + 100 * index + 75];
        }
        return 0;
    }

    /** 水冷机类型码 */
    public int getWaterCooler_I_TypeCode(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 0];
        }
        return 0;
    }

    /** 水冷机版本号 */
    public String getWaterCooler_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[14000 + 100 * index + 10 + i] == 0) {
                    continue;
                }
                value.append((char) (data[14000 + 100 * index + 10 + i] >> 8));
                value.append((char) (data[14000 + 100 * index + 10 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** 水冷机运行状态(0:正常,1:告警,2:故障) */
    public int getWaterCooler_I_State(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 30];
        }
        return 0;
    }

    /** 水冷机在线状态(0:掉线,1:在线) */
    public int getWaterCooler_I_Online(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 31];
        }
        return 0;
    }

    /** 水冷机开机状态(0:关,1:开) */
    public int getWaterCooler_I_Onoff(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 32];
        }
        return 0;
    }

    /** 出水温度(℃) */
    public float getWaterCooler_I_OutletTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[14000 + 100 * index + 40]) / 10;
        }
        return (short) 0;
    }

    /** 回水温度(℃) */
    public float getWaterCooler_I_ReturnTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[14000 + 100 * index + 41]) / 10;
        }
        return (short) 0;
    }

    /** 出水压力(Bar) */
    public float getWaterCooler_I_OutletPressure(int index) {
        if (data != null) {
            return (float) ((short) data[14000 + 100 * index + 42]) / 10;
        }
        return (short) 0;
    }

    /** 回水压力(Bar) */
    public float getWaterCooler_I_ReturnPressure(int index) {
        if (data != null) {
            return (float) ((short) data[14000 + 100 * index + 43]) / 10;
        }
        return (short) 0;
    }

    /** 设定制冷点(℃) */
    public float getWaterCooler_I_SetCool(int index) {
        if (data != null) {
            return (float) ((short) data[14000 + 100 * index + 44]) / 10;
        }
        return (short) 0;
    }

    /** 设定制热点(℃) */
    public float getWaterCooler_I_SetHot(int index) {
        if (data != null) {
            return (float) ((short) data[14000 + 100 * index + 45]) / 10;
        }
        return (short) 0;
    }

    /** 环境温度(℃) */
    public float getWaterCooler_I_AirTemperature(int index) {
        if (data != null) {
            return (float) ((short) data[14000 + 100 * index + 46]) / 10;
        }
        return (short) 0;
    }

    /** 状态BIT位0 */
    public int getWaterCooler_I_StateBit0(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 70];
        }
        return 0;
    }

    /** 状态BIT位1 */
    public int getWaterCooler_I_StateBit1(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 71];
        }
        return 0;
    }

    /** 故障BIT位0 */
    public int getWaterCooler_I_FaultBit0(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 72];
        }
        return 0;
    }

    /** 故障BIT位1 */
    public int getWaterCooler_I_FaultBit1(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 73];
        }
        return 0;
    }

    /** 故障BIT位2 */
    public int getWaterCooler_I_FaultBit2(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 74];
        }
        return 0;
    }

    /** 故障BIT位3 */
    public int getWaterCooler_I_FaultBit3(int index) {
        if (data != null) {
            return data[14000 + 100 * index + 75];
        }
        return 0;
    }

    /** 消防类型码 */
    public int getFirefighting_I_TypeCode(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 0];
        }
        return 0;
    }

    /** 消防版本号 */
    public String getFirefighting_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[16000 + 100 * index + 10 + i] == 0) {
                    continue;
                }
                value.append((char) (data[16000 + 100 * index + 10 + i] >> 8));
                value.append((char) (data[16000 + 100 * index + 10 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** 消防运行状态(0:正常,1:告警,2:故障) */
    public int getFirefighting_I_State(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 30];
        }
        return 0;
    }

    /** 消防在线状态(0:掉线,1:在线) */
    public int getFirefighting_I_Online(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 31];
        }
        return 0;
    }

    /** 消防开机状态(0:关,1:开) */
    public int getFirefighting_I_Onoff(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 32];
        }
        return 0;
    }

    /** 环境温度(℃) */
    public float getFirefighting_I_Temperature(int index) {
        if (data != null) {
            return (float) ((short) data[16000 + 100 * index + 40]) / 10;
        }
        return (short) 0;
    }

    /** 一氧化碳浓度(ppm) */
    public double getFirefighting_I_CarbonMonoxideConcentration(int index) {
        if (data != null) {
            return (double) data[16000 + 100 * index + 41] / 10;
        }
        return 0;
    }

    /** VOC(V) */
    public double getFirefighting_I_Voc(int index) {
        if (data != null) {
            return (double) data[16000 + 100 * index + 42] / 1000;
        }
        return 0;
    }

    /** 状态BIT位0 */
    public int getFirefighting_I_StateBit0(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 70];
        }
        return 0;
    }

    /** 状态BIT位1 */
    public int getFirefighting_I_StateBit1(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 71];
        }
        return 0;
    }

    /** 故障BIT位0 */
    public int getFirefighting_I_FaultBit0(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 72];
        }
        return 0;
    }

    /** 故障BIT位1 */
    public int getFirefighting_I_FaultBit1(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 73];
        }
        return 0;
    }

    /** 故障BIT位2 */
    public int getFirefighting_I_FaultBit2(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 74];
        }
        return 0;
    }

    /** 故障BIT位3 */
    public int getFirefighting_I_FaultBit3(int index) {
        if (data != null) {
            return data[16000 + 100 * index + 75];
        }
        return 0;
    }

    /** STATESi类型码 */
    public int getStates_I_TypeCode(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 0];
        }
        return 0;
    }

    /** STATESi版本号 */
    public String getStates_I_Version(int index) {
        if (data != null) {
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < 20; i++) {
                if (data[18000 + 100 * index + 10 + i] == 0) {
                    continue;
                }
                value.append((char) (data[18000 + 100 * index + 10 + i] >> 8));
                value.append((char) (data[18000 + 100 * index + 10 + i] & 0xFF));
            }
            return value.toString();
        }
        return null;
    }

    /** STATESi运行状态(0:正常,1:告警,2:故障) */
    public int getStates_I_State(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 30];
        }
        return 0;
    }

    /** STATESi在线状态(0:掉线,1:在线) */
    public int getStates_I_Online(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 31];
        }
        return 0;
    }

    /** STATESi开机状态(0:关,1:开) */
    public int getStates_I_Onoff(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 32];
        }
        return 0;
    }

    /** STATESi状态BIT位0 */
    public int getStates_I_StateBit0(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 70];
        }
        return 0;
    }

    /** STATESi状态BIT位1 */
    public int getStates_I_StateBit1(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 71];
        }
        return 0;
    }

    /** STATESi状态BIT位2 */
    public int getStates_I_StateBit2(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 72];
        }
        return 0;
    }

    /** STATESi状态BIT位3 */
    public int getStates_I_StateBit3(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 73];
        }
        return 0;
    }

    /** STATESi告警BIT位0 */
    public int getStates_I_AlarmBit0(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 74];
        }
        return 0;
    }

    /** STATESi告警BIT位1 */
    public int getStates_I_AlarmBit1(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 75];
        }
        return 0;
    }

    /** STATESi告警BIT位2 */
    public int getStates_I_AlarmBit2(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 76];
        }
        return 0;
    }

    /** STATESi告警BIT位3 */
    public int getStates_I_AlarmBit3(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 77];
        }
        return 0;
    }

    /** STATESi告警BIT位4 */
    public int getStates_I_AlarmBit4(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 78];
        }
        return 0;
    }

    /** STATESi告警BIT位5 */
    public int getStates_I_AlarmBit5(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 79];
        }
        return 0;
    }

    /** STATESi告警BIT位6 */
    public int getStates_I_AlarmBit6(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 80];
        }
        return 0;
    }

    /** STATESi告警BIT位7 */
    public int getStates_I_AlarmBit7(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 81];
        }
        return 0;
    }

    /** STATESi故障BIT位0 */
    public int getStates_I_FaultBit0(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 82];
        }
        return 0;
    }

    /** STATESi故障BIT位1 */
    public int getStates_I_FaultBit1(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 83];
        }
        return 0;
    }

    /** STATESi故障BIT位2 */
    public int getStates_I_FaultBit2(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 84];
        }
        return 0;
    }

    /** STATESi故障BIT位3 */
    public int getStates_I_FaultBit3(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 85];
        }
        return 0;
    }

    /** STATESi故障BIT位4 */
    public int getStates_I_FaultBit4(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 86];
        }
        return 0;
    }

    /** STATESi故障BIT位5 */
    public int getStates_I_FaultBit5(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 87];
        }
        return 0;
    }

    /** STATESi故障BIT位6 */
    public int getStates_I_FaultBit6(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 88];
        }
        return 0;
    }

    /** STATESi故障BIT位7 */
    public int getStates_I_FaultBit7(int index) {
        if (data != null) {
            return data[18000 + 100 * index + 89];
        }
        return 0;
    }

    /** DCBUSi直流电压(V) */
    public float getDcBusMeter_I_Voltage(int index) {
        if (data != null) {
            return (float) ((short) data[59000 + 50 * index + 0]) / 10;
        }
        return (short) 0;
    }

    /** DCBUSi直流电流(A,正为向EMS放电) */
    public float getDcBusMeter_I_Current(int index) {
        if (data != null) {
            return (float) ((short) data[59000 + 50 * index + 1]) / 10;
        }
        return (short) 0;
    }

    /** DCBUSi直流功率(kW,正为向EMS放电) */
    public float getDcBusMeter_I_Power(int index) {
        if (data != null) {
            return (float) ((short) data[59000 + 50 * index + 2]) / 10;
        }
        return (short) 0;
    }

    /** DCBUSi历史输出能量(kWh,向EMS放电) */
    public double getDcBusMeter_I_HistoryOutputEnergy(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** DCBUSi历史输入能量(kWh,从EMS充电) */
    public double getDcBusMeter_I_HistoryInputEnergy(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 10;
        }
        return 0.0;
    }

    /** 设定状态(0:停,1:充,2:放,3:离,4:待,15:故障) */
    private int setState() {
        if (data != null) {
            return data[60000];
        }
        return 0;
    }

    /** 当前状态 */
    private int state() {
        if (data != null) {
            return data[60001];
        }
        return 0;
    }

    /** 状态机TICK */
    private int stateTick() {
        if (data != null) {
            return data[60002];
        }
        return 0;
    }

    /** 状态机子TICK */
    private int stateSubTick() {
        if (data != null) {
            return data[60003];
        }
        return 0;
    }

    /** 故障标识 */
    private int faultFlag() {
        if (data != null) {
            return data[60004];
        }
        return 0;
    }

    /** 设定功率(W) */
    private double setPower() {
        if (data != null && data.length > (60005 + 1)) {
            int high = data[60005];
            int low = data[60005 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 设定无功功率(VA) */
    private double setReactivePower() {
        if (data != null && data.length > (60007 + 1)) {
            int high = data[60007];
            int low = data[60007 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 功率右限(W) */
    private double rightLimitPower() {
        if (data != null && data.length > (60009 + 1)) {
            int high = data[60009];
            int low = data[60009 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 功率左限(W) */
    private double leftLimitPower() {
        if (data != null && data.length > (60011 + 1)) {
            int high = data[60011];
            int low = data[60011 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 当前功率(W) */
    private double currentPower() {
        if (data != null && data.length > (60013 + 1)) {
            int high = data[60013];
            int low = data[60013 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 当前无功功率(VA) */
    private double currentReactivePower() {
        if (data != null && data.length > (60015 + 1)) {
            int high = data[60015];
            int low = data[60015 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** DCDC功率限制值 */
    private double dcdcLimitPower() {
        if (data != null && data.length > (60017 + 1)) {
            int high = data[60017];
            int low = data[60017 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** DCDC功率当前值 */
    private double dcdcCurrentPower() {
        if (data != null && data.length > (60019 + 1)) {
            int high = data[60019];
            int low = data[60019 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** 电表控制功率 */
    private double meterControlPower() {
        if (data != null && data.length > (60021 + 1)) {
            int high = data[60021];
            int low = data[60021 + 1];
            int temp = (high << 16) + low;
            return (double) temp / 1;
        }
        return 0;
    }

    /** UDP控制模式 */
    private int powerByUdp() {
        if (data != null) {
            return data[60023];
        }
        return 0;
    }

    /** 插件控制模式 */
    private int powerByPlugin() {
        if (data != null) {
            return data[60024];
        }
        return 0;
    }

    /** GPS触发失败次数统计值 */
    private double gpsTriggerFailedCount() {
        if (data != null && data.length > (60100 + 1)) {
            int high = data[60100];
            int low = data[60100 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** GPS轮询失败次数统计值 */
    private double gpsLoopFailedCount() {
        if (data != null && data.length > (60102 + 1)) {
            int high = data[60102];
            int low = data[60102 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** GPS轮询成功次数统计值 */
    private double gpsLoopSuccessCount() {
        if (data != null && data.length > (60104 + 1)) {
            int high = data[60104];
            int low = data[60104 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** BMS触发失败次数统计值 */
    private double bmsTriggerFailedCount() {
        if (data != null && data.length > (60120 + 1)) {
            int high = data[60120];
            int low = data[60120 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** BMS轮询失败次数统计值 */
    private double bmsLoopFailedCount() {
        if (data != null && data.length > (60122 + 1)) {
            int high = data[60122];
            int low = data[60122 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** BMS轮询成功次数统计值 */
    private double bmsLoopSuccessCount() {
        if (data != null && data.length > (60124 + 1)) {
            int high = data[60124];
            int low = data[60124 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** EMS电表触发失败次数统计值 */
    private double emsMeterTriggerFailedCount() {
        if (data != null && data.length > (60140 + 1)) {
            int high = data[60140];
            int low = data[60140 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** EMS电表轮询失败次数统计值 */
    private double emsMeterLoopFailedCount() {
        if (data != null && data.length > (60142 + 1)) {
            int high = data[60142];
            int low = data[60142 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** EMS电表轮询成功次数统计值 */
    private double emsMeterLoopSuccessCount() {
        if (data != null && data.length > (60144 + 1)) {
            int high = data[60144];
            int low = data[60144 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC电表触发失败次数统计值 */
    private double DCMeterTriggerFailedCount() {
        if (data != null && data.length > (60160 + 1)) {
            int high = data[60160];
            int low = data[60160 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC电表轮询失败次数统计值 */
    private double DCMeterLoopFailedCount() {
        if (data != null && data.length > (60162 + 1)) {
            int high = data[60162];
            int low = data[60162 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC电表轮询成功次数统计值 */
    private double DCMeterLoopSuccessCount() {
        if (data != null && data.length > (60164 + 1)) {
            int high = data[60164];
            int low = data[60164 + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** PCS触发失败次数统计值 */
    public double getPcs_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** PCS轮询失败次数统计值 */
    public double getPcs_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** PCS轮询成功次数统计值 */
    public double getPcs_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC触发失败次数统计值 */
    public double getDcdc_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC轮询失败次数统计值 */
    public double getDcdc_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCDC轮询成功次数统计值 */
    public double getDcdc_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 空调触发失败次数统计值 */
    public double getAirConditioner_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 空调轮询失败次数统计值 */
    public double getAirConditioner_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 空调轮询成功次数统计值 */
    public double getAirConditioner_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 水冷触发失败次数统计值 */
    public double getWaterCooler_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 水冷轮询失败次数统计值 */
    public double getWaterCooler_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 水冷轮询成功次数统计值 */
    public double getWaterCooler_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 消防触发失败次数统计值 */
    public double getFirefighting_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 消防轮询失败次数统计值 */
    public double getFirefighting_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** 消防轮询成功次数统计值 */
    public double getFirefighting_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** STATES触发失败次数统计值 */
    public double getStates_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** STATES轮询失败次数统计值 */
    public double getStates_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** STATES轮询成功次数统计值 */
    public double getStates_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCBUS电表触发失败次数统计值 */
    public double getDcBusMeter_I_TriggerFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCBUS电表轮询失败次数统计值 */
    public double getDcBusMeter_I_LoopFailedCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }

    /** DCBUS电表轮询成功次数统计值 */
    public double getDcBusMeter_I_LoopSuccessCount(int index) {
        if (data != null && data.length > (index + 1)) {
            int high = data[index];
            int low = data[index + 1];
            long temp = ((long) high << 16) + low;
            return (double) temp / 1;
        }
        return 0.0;
    }
}
