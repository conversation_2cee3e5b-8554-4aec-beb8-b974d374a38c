package com.wifochina.realtimemodel.entity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-19
 */
@Data
@EqualsAndHashCode()
@Entity
@Table(name = "t_project")
public class ProjectEntity {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    @ApiModelProperty(value = "项目id")
    private String id;

    /** 系统名 */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /** 项目模式，1场站模式 0云模式 */
    @ApiModelProperty(value = "项目模式 1场站模式 0云模式")
    private Integer projectModel;

    /** 是否删除 true已删除，false未删除 */
    @ApiModelProperty("是否删除 true已删除，false未删除")
    private Boolean whetherDelete;
}
