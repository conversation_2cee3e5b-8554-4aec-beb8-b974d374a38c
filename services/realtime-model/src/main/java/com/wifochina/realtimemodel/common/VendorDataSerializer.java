package com.wifochina.realtimemodel.common;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * Created on 2025/7/1 17:35.
 *
 * <AUTHOR>
 */
public class VendorDataSerializer extends JsonSerializer<int[]> {

    public static final ThreadLocal<Boolean> ignoreThreadLocal = new ThreadLocal<>();

    @Override
    public void serialize(int[] value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        try {
            // 动态条件判断
            if (shouldIgnore()) {
                gen.writeNull(); // 不输出内容
            } else {
                // 正常序列化
                gen.writeObject(value);
            }

        } finally {
            ignoreThreadLocal.remove();
        }
    }

    private boolean shouldIgnore() {
        // 你的动态逻辑，比如某个 flag 或者线程上下文
        System.out.println(ignoreThreadLocal.get());
        return ignoreThreadLocal.get() != null && ignoreThreadLocal.get();
    }
}
