package com.wifochina.realtimemodel.common

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * Created on 2025/3/5 11:30.
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class GoOnlineResponse(
    val ems: List<BaseInfo>, val meter: List<BaseInfo>, val controllable: List<BaseInfo>, val camera: List<BaseInfo>
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BaseInfo(
    val uuid: String, val name: String, val online: Boolean
)
