package com.wifochina.realtimemodel.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 协调控制器
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data
@Table(name = "t_controller")
@Entity
@ApiModel(value = "ControllerEntity对象", description = "协调控制器")
public class ControllerEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "uuid")
    @Id
    @Column(name = "id")
    private String id;

    @ApiModelProperty(value = "ip")
    private String ip;

    @ApiModelProperty(value = "端口")
    private Integer port;

    @ApiModelProperty(value = "序列号")
    private String sn;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private String projectId;
}
