package com.wifochina.realtimemodel.common

import com.wifochina.realtimemodel.enums.ProtocolEnum

/**
 * Created on 2025/7/3 11:09.
 * <AUTHOR>
 */
class RealTimeResolveUtils {
    companion object {
        fun pcsCount(dataAdapter: IProtocolData): Int {
            return when (ProtocolEnum.protocol(dataAdapter.data()[42])) {
                ProtocolEnum.WeihengEms100 -> {
                    (dataAdapter as WeihengEms100).pcsCount
                }

                ProtocolEnum.WeihengEms200 -> {
                    (dataAdapter as WeihengEms200).pcsCount
                }
            }
        }

        fun bmsClusterCount(dataAdapter: IProtocolData): Int {
            return when (ProtocolEnum.protocol(dataAdapter.data()[42])) {
                ProtocolEnum.WeihengEms100 -> {
                    (dataAdapter as WeihengEms100).bmsClusterCount
                }

                ProtocolEnum.WeihengEms200 -> {
                    (dataAdapter as WeihengEms200).bmsClusterCount
                }
            }
        }
    }
}