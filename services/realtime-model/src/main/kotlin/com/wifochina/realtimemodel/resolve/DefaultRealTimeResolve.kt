package com.wifochina.realtimemodel.resolve

import com.wifochina.realtimemodel.common.IProtocolData
import com.wifochina.realtimemodel.common.RealTimeResolveUtils
import com.wifochina.realtimemodel.common.ReflectionUtils
import org.springframework.stereotype.Component

/**
 * Created on 2025/7/3 10:47.
 * <AUTHOR>
 */
@Component
class DefaultRealTimeResolve(
) : RealTimeResolve {

    companion object {
        const val pcsPrefix = "pcs_"
        const val bmsClusterPrefix = "bms_cluster_"

    }

    private fun invokeIResolve(index: Int, data: IProtocolData, columns: List<String>): MutableMap<String, Any?> {
        return mutableMapOf<String, Any?>().let {
            columns.forEach { column ->
                val value: Any? = ReflectionUtils.iMethodInvoke(
                    index, data, column
                )
                it[column] = value
            }
            it
        }
    }


    override fun fixedResolve(data: IProtocolData, columns: List<String>, camel: Boolean): MutableMap<String, Any?> {
        return ReflectionUtils.fixedFieldInvoke(data, columns, camel)
    }

    override fun pcsIResolve(
        data: IProtocolData, columns: List<String>, pcsIndex: Int?
    ): MutableMap<String, MutableMap<String, Any?>> {
        val pcsCount = RealTimeResolveUtils.pcsCount(data)
        val pcsData = mutableMapOf<String, MutableMap<String, Any?>>()
        pcsIndex?.let {
            //如果传了某个index只查询这个
            pcsData["$pcsPrefix$pcsIndex"] = invokeIResolve(pcsIndex, data, columns)
        } ?: run {
            //所有的
            for (i in 0 until pcsCount) {
                pcsData["$pcsPrefix$i"] = invokeIResolve(i, data, columns)
            }
        }
        return pcsData
    }

    override fun bmsIResolve(
        data: IProtocolData,
        columns: List<String>,
        bmsClusterIndex: Int?
    ): MutableMap<String, MutableMap<String, Any?>> {
        val bmsClusterCount = RealTimeResolveUtils.bmsClusterCount(data)
        val bmsData = mutableMapOf<String, MutableMap<String, Any?>>()
        bmsClusterIndex?.let {
            //如果传了某个index只查询这个
            bmsData["$bmsClusterPrefix$bmsClusterIndex"] = invokeIResolve(bmsClusterIndex, data, columns)
        } ?: run {
            //所有的
            for (i in 0 until bmsClusterCount) {
                bmsData["$bmsClusterPrefix$i"] = invokeIResolve(i, data, columns)
            }
        }
        return bmsData
    }
}