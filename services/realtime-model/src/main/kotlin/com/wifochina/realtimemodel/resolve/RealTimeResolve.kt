package com.wifochina.realtimemodel.resolve

import com.wifochina.realtimemodel.common.IProtocolData

/**
 * Created on 2025/7/3 10:43.
 * <AUTHOR>
 */
interface RealTimeResolve {

    fun fixedResolve(
        data: IProtocolData,
        columns: List<String>,
        camel: Boolean
    ):MutableMap<String,Any?>

    fun pcsIResolve(
        data: IProtocolData,
        columns: List<String>,
        pcsIndex: Int?
    ): MutableMap<String, MutableMap<String, Any?>>

    fun bmsIResolve(
        data: IProtocolData,
        columns: List<String>,
        bmsClusterIndex: Int?
    ): MutableMap<String, MutableMap<String, Any?>>
}