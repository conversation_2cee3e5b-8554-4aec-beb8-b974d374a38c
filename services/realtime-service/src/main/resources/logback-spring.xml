<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志保存目录 -->
    <property name="LOG_PATH" value="/opt/log"/>

    <!-- 配置日志输出格式，调用方式：${LOG_PATTERN} -->
    <property name="LOG_PATTERN_COLOR"
              value="%cyan(%d{yyyy-MM-dd HH:mm:ss.SSS,CTT}) [%X{clientIP}] %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{10}) - [%X{traceId}] %msg%n"/>


    <!-- 配置日志输出格式，调用方式：${LOG_PATTERN} -->
    <property name="LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS,CTT} [%X{clientIP}] [%thread] %-5level %logger{10} - [%X{traceId}] %msg%n"/>
    <!-- 配置系统日志输出 -->
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN_COLOR}</pattern>
        </layout>
    </appender>


    <!--根据日志级别分离日志，分别输出到不同的文件-->
    <!-- info日志配置 -->
    <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/info/info.log</file>
        <append>true</append>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
        </filter>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件输出的文件名 -->
            <FileNamePattern>${LOG_PATH}/info/%d{yyyy-MM-dd}-%i.log</FileNamePattern>
            <!-- 单个日志文件最大数据量 -->
            <maxFileSize>50MB</maxFileSize>
            <!-- 日志文件最大数f据量 -->
            <totalSizeCap>1GB</totalSizeCap>
            <!-- 日志文件保留天数 -->
            <MaxHistory>20</MaxHistory>
        </rollingPolicy>
    </appender>

    <!-- warn日志配置 -->
    <appender name="fileWarnLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/warn/warn.log</file>
        <append>true</append>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/warn/%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!-- 单个日志文件最大数据量 -->
            <maxFileSize>50MB</maxFileSize>
            <!-- 日志文件最大数f据量 -->
            <totalSizeCap>1GB</totalSizeCap>
            <!-- 日志文件保留天数 -->
            <MaxHistory>20</MaxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印警告日志 -->
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- error日志配置-->
    <appender name="fileErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error/error.log</file>
        <append>true</append>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error/%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!-- 单个日志文件最大数据量 -->
            <maxFileSize>50MB</maxFileSize>
            <!-- 日志文件最大数f据量 -->
            <totalSizeCap>1GB</totalSizeCap>
            <!-- 日志文件保留天数 -->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印错误日志 -->
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 根据设置日志级别，屏蔽相关的日志：以下包及子包的日志会屏蔽掉-->
<!--    <logger name="org.springframework" level="warn"/>-->
    <logger name="org.apache" level="warn"/>
    <logger name="springfox.documentation" level="warn"/>
    <logger name="com.baomidou.mybatisplus" level="warn"/>
    <logger name="com.alibaba.druid.pool.DruidDataSource" level="INFO"/>
    <logger name="com.alibaba.druid.filter.stat.StatFilter" level="INFO"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver"
            level="ERROR"/>

    <logger name="org.springframework.cloud.alibaba.nacos.discovery"
            level="DEBUG"/>
    <logger name="org.springframework.cloud.nacos.discovery"
            level="DEBUG"/>
    <logger name="org.springframework.cloud.client.serviceregistry"
            level="DEBUG"/>


    <!-- 项目中info级别的日志会输出到以下日志对象中 -->
    <root level="INFO">
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="fileInfoLog"/>
        <appender-ref ref="fileWarnLog"/>
        <appender-ref ref="fileErrorLog"/>
    </root>
</configuration>