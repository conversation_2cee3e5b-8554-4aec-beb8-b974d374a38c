package com.wifochina.realtimeservice.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.kotlin.KotlinModule;

import okhttp3.OkHttpClient;

import org.jetbrains.annotations.NotNull;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * RestTemplateConfig
 *
 * @since 3/29/2022 2:15 PM
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
public class RestTemplateConfig {

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 注册 Kotlin 模块
        objectMapper.registerModule(new KotlinModule());
        // 支持 java 8 时间转换
        objectMapper.registerModule(new JavaTimeModule());
        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder, ObjectMapper objectMapper) {
        OkHttpClient okHttpClient =
                new OkHttpClient.Builder()
                        // 增大超时时间
                        .connectTimeout(5, TimeUnit.SECONDS)
                        // 增大超时时间
                        .readTimeout(10, TimeUnit.SECONDS)
                        // 启用连接失败重试
                        .retryOnConnectionFailure(true)
                        // 增大连接池
                        .connectionPool(new okhttp3.ConnectionPool(50, 5, TimeUnit.MINUTES))
                        .build();
        return createRestTemplate(objectMapper, okHttpClient);
    }

    private static @NotNull RestTemplate createRestTemplate(
            ObjectMapper objectMapper, OkHttpClient okHttpClient) {
        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(factory));
        //        restTemplate.setRequestFactory(factory);
//        restTemplate.setInterceptors(List.of(new LoggingInterceptor()));
        //        restTemplate.setInterceptors(List.of(new RestTemplateRequestInterceptor()));
        //        restTemplate.setMessageConverters(
        //                List.of(new MappingJackson2HttpMessageConverter(objectMapper))); //
        // 避免自定义日志转换器影响
        return restTemplate;
    }
}
