package com.wifochina.realtimeservice.config

import kotlinx.coroutines.ThreadContextElement
import org.slf4j.MDC
import kotlin.coroutines.CoroutineContext

/**
 * Created on 2025/1/8 19:40.
 * <AUTHOR>
 */
class MdcKotlinContext : ThreadContextElement<Map<String, String>?> {
    companion object Key : CoroutineContext.Key<MdcKotlinContext>

    // 保存当前线程的 MDC 上下文
    private val contextMap: Map<String, String>? = MDC.getCopyOfContextMap()

    override val key: CoroutineContext.Key<MdcKotlinContext>
        get() = Key

    // 在协程切换到新的线程时设置 MDC
    override fun updateThreadContext(context: CoroutineContext): Map<String, String>? {
        val previous = MDC.getCopyOfContextMap()
        contextMap?.let { MDC.setContextMap(it) } ?: MDC.clear()
        return previous
    }

    // 协程执行完毕后恢复原始线程的 MDC 上下文
    override fun restoreThreadContext(context: CoroutineContext, oldState: Map<String, String>?) {
        oldState?.let { MDC.setContextMap(it) } ?: MDC.clear()
    }
}