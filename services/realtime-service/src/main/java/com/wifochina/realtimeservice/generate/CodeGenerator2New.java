package com.wifochina.realtimeservice.generate;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.squareup.javapoet.*;

import com.wifochina.realtimemodel.common.IProtocolData;
import com.wifochina.realtimemodel.common.VendorDataSerializer;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import javax.lang.model.element.Modifier;

/**
 * Created on 2025/2/26 16:00.
 *
 * <AUTHOR>
 */
public class CodeGenerator2New {

    private static final String POINT_TYPE_Uint16 = "Uint16";
    private static final String POINT_TYPE_Int16 = "Int16";
    private static final String POINT_TYPE_BIT = "BIT";
    private static final String POINT_TYPE_uInt32 = "uint32";
    private static final String POINT_TYPE_UInt32 = "Uint32";
    private static final String POINT_TYPE_Int32 = "Int32";
    private static final String POINT_TYPE_ASCII = "ASCII";
    private static final String I = "I";
    private static final String _I_ = "_I_";

    private static final String DB_URL =
            "**********************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "123@abcd";

    private static final String CLASS_NAME = "Ems200PointData";

    public static void main(String[] args) throws SQLException, IOException {
        List<String> vendorTypes = getVendorTypes();
        String className;
        List<PointDataEntity> points;
        for (String vendorType : vendorTypes) {
            if (vendorType.isEmpty()) {
                System.out.println("vendorType = " + vendorType + "是空");
                continue;
            }
            className = vendorType;
            points = getPointDataByVendorType(vendorType);
            if (points.isEmpty()) {
                System.out.println("未查询到该vendorType = " + vendorType + "的点表");
                return;
            }
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDate = now.format(formatter);

            FieldSpec dataField =
                    FieldSpec.builder(ArrayTypeName.of(TypeName.INT), "data", Modifier.PUBLIC)
                            .addAnnotation(
                                    AnnotationSpec.builder(JsonSerialize.class)
                                            .addMember(
                                                    "using", "$T.class", VendorDataSerializer.class)
                                            .build())
                            .build();
            //        // 生成 Java 类
            TypeSpec.Builder classBuilder =
                    TypeSpec.classBuilder(className)
                            .addJavadoc("<AUTHOR>
                            .addJavadoc("@date " + formattedDate)
                            .addModifiers(Modifier.PUBLIC)
                            .addAnnotation(Getter.class)
                            .addAnnotation(Slf4j.class)
                            .addSuperinterface(IProtocolData.class)
                            .addField(dataField);
            //                            .addField(ArrayTypeName.of(TypeName.INT), "data",
            // Modifier.PUBLIC);

            classBuilder.addMethod(
                    MethodSpec.methodBuilder("vendorType")
                            .addModifiers(Modifier.PUBLIC)
                            .addAnnotation(Override.class)
                            .addAnnotation(NotNull.class)
                            .returns(String.class)
                            .addStatement("return \"" + vendorType + "\"")
                            .build());
            classBuilder.addMethod(
                    MethodSpec.methodBuilder("data")
                            .addModifiers(Modifier.PUBLIC)
                            .addAnnotation(Override.class)
                            .addAnnotation(NotNull.class)
                            .returns(ArrayTypeName.of(TypeName.INT))
                            .addStatement("return data")
                            .build());

            MethodSpec.Builder constructorBuilder =
                    MethodSpec.constructorBuilder()
                            .addModifiers(Modifier.PUBLIC)
                            .addParameter(ArrayTypeName.of(TypeName.INT), "data");
            String dataAssignLine = String.format("this.%s = %s", "data", "data");
            constructorBuilder.addStatement(dataAssignLine);
            for (PointDataEntity point : points) {
                // 暂时无需求要实时读取 电芯温度和电压 而且这个 电表的offset不规范 解不了
                if (point.getPointColumn().equals("bms_cell_voltage_{i}")
                        || point.getPointColumn().equals("bms_cell_temperature_{i}")) {
                    continue;
                }

                ResolveParams resolveParams = getResolveParams(point);

                MethodSpec.Builder builder =
                        MethodSpec.methodBuilder(resolveParams.getMethodName());
                MethodSpec getter = null;

                if (point.getPointType().equals(POINT_TYPE_Uint16)
                        || point.getPointType().equals(POINT_TYPE_BIT)) {
                    getter = integerResolve(builder, point, resolveParams, classBuilder);
                }
                if (point.getPointType().equals(POINT_TYPE_Int32)) {
                    getter = integer32Resolve(builder, point, resolveParams, classBuilder);
                }
                if (point.getPointType().equals(POINT_TYPE_uInt32)
                        || point.getPointType().equals(POINT_TYPE_UInt32)) {
                    getter = longResolve(builder, point, resolveParams, classBuilder);
                }
                if (point.getPointType().equals(POINT_TYPE_Int16)) {
                    getter = shortResolve(builder, point, resolveParams, classBuilder);
                }
                if (point.getPointType().equals(POINT_TYPE_ASCII)) {
                    getter = asciiResolve(builder, point, resolveParams);
                    if (!resolveParams.dynamicI) {
                        FieldSpec.Builder fieldBuilder =
                                FieldSpec.builder(
                                        ClassName.get(String.class),
                                        resolveParams.methodName,
                                        Modifier.PRIVATE);
                        fieldBuilder.addModifiers(Modifier.FINAL);
                        FieldSpec field = fieldBuilder.build();
                        classBuilder.addField(field);
                    }
                }
                if (getter == null) {
                    System.out.println("getter null");
                }
                classBuilder.addMethod(getter);

                if (!resolveParams.dynamicI) {
                    String assignLine =
                            String.format(
                                    "this.%s = %s()",
                                    resolveParams.getMethodName(), resolveParams.getMethodName());
                    constructorBuilder.addStatement(assignLine);
                }
            }

            classBuilder.addMethod(constructorBuilder.build());

            TypeSpec pointDataClass = classBuilder.build();
            JavaFile javaFile = JavaFile.builder("com.wifochina.common", pointDataClass).build();
            // 输出到 src/main/java/com/example
            File outputDir =
                    new File(
                            "/Users/<USER>/java/newworkspace/ems-server/services/realtime-model/src/main/java/");
            javaFile.writeTo(outputDir);
        }
    }

    private static MethodSpec integer32Resolve(
            MethodSpec.Builder builder,
            PointDataEntity point,
            ResolveParams resolveParams,
            TypeSpec.Builder classBuilder) {
        Object address;
        if (resolveParams.isDynamicI()) {
            builder = builder.addParameter(int.class, "index");
            address = "index";
            builder.addModifiers(Modifier.PUBLIC);
        } else {
            builder.addModifiers(Modifier.PRIVATE);
            address = resolveParams.getResolveAddress();
        }

        builder.returns(double.class);
        FieldSpec.Builder fieldBuilder =
                FieldSpec.builder(TypeName.DOUBLE, resolveParams.methodName, Modifier.PRIVATE);
        if (!resolveParams.dynamicI) {
            fieldBuilder.addModifiers(Modifier.FINAL);
            classBuilder.addField(fieldBuilder.build());
        }

        // 生成代码
        builder.addJavadoc(point.getPointName())
                .beginControlFlow("if (data != null && data.length > ($L + 1))", address)
                .addStatement("int high = data[$L]", address)
                .addStatement("int low = data[$L + 1]", address)
                .addStatement("int temp = (high << 16) + low");

        if (point.getPointMul() != 1) {
            builder.addStatement("return (double) temp / $L", point.getPointMul());
        } else {
            builder.addStatement("return (double) temp / 1");
        }

        builder.endControlFlow().addStatement("return 0");

        return builder.build();
    }

    private static MethodSpec asciiResolve(
            MethodSpec.Builder builder, PointDataEntity point, ResolveParams resolveParams) {
        Object address;
        if (resolveParams.isDynamicI()) {
            // 这里的参数不仅仅是 deviceId
            // 如果包含i 需要额外添加参数
            builder = builder.addParameter(int.class, "index");
            address = resolveParams.getResolveAddressStr();
            builder.addModifiers(Modifier.PUBLIC);
        } else {
            address = resolveParams.getResolveAddressStr();
            builder.addModifiers(Modifier.PUBLIC);
        }
        builder = builder.returns(String.class);
        //        builder.addModifiers(Modifier.PRIVATE);
        return builder.addJavadoc(point.getPointName())
                //                .addStatement(
                //                        "$T deviceRealTimeData = getDeviceRealTimeData(deviceId)",
                //                        ClassName.get("com.wifochina.modules.data.realtime",
                // "DeviceRealTimeData"))
                .beginControlFlow("if (data !=  null)")
                //                .addStatement("int index = " + resolveParams.resolveAddress)
                .addStatement("StringBuilder value = new StringBuilder()")
                .beginControlFlow("for (int i = 0; i < " + point.getPointMul() + "; i++)")
                //                .beginControlFlow("if (data[index + i] == 0)",address)
                .beginControlFlow("if (data[$L + i] == 0)", address)
                .addStatement("continue")
                .endControlFlow()
                //                .addStatement("value.append((char) (data[index + i] >> 8))")
                //                .addStatement("value.append((char) (data[index + i] & 0xFF))")
                .addStatement("value.append((char) (data[$L + i] >> 8))", address)
                .addStatement("value.append((char) (data[$L + i] & 0xFF))", address)
                .endControlFlow()
                .addStatement("return value.toString()")
                .endControlFlow()
                .addStatement("return null")
                .build();
    }

    private static ResolveParams getResolveParams(PointDataEntity point) {
        //        String methodName = getMethodFromPointColumn(point);
        String methodName;
        long resolveAddress = 0;
        String resolveAddressStr = "";
        boolean dynamicI = false;
        if (point.getPointColumn().contains("{i}")) {
            String newPointColumn = point.getPointColumn().replace("{i}_", "I_");
            dynamicI = true;
            methodName = convertToMethodName(dynamicI, newPointColumn);
        } else {
            methodName = convertToMethodName(dynamicI, point.getPointColumn());
        }
        if (!dynamicI) {
            // 正常的这种
            resolveAddress = point.getPointAddress() + Integer.parseInt(point.getPointOffset());
        } else {
            // 动态的
            // 解析 3500 + 150 * index; 这种东西 , 3500+DCDCi*150
            int addressStart = 0;
            try {
                addressStart =
                        Integer.parseInt(
                                point.getPointOffset()
                                        .substring(0, point.getPointOffset().indexOf("+")));
            } catch (Exception e) {
                System.out.println("e:");
            }
            //             500 + 150 * index;
            int addressIndexStep =
                    Integer.parseInt(
                            point.getPointOffset()
                                    .substring(point.getPointOffset().indexOf("*") + 1));
            // 500 + 150 * {index}
            resolveAddressStr =
                    addressStart
                            + " + "
                            + addressIndexStep
                            + " * index"
                            + " + "
                            + point.getPointAddress();
        }
        return new ResolveParams()
                .setMethodName(methodName)
                .setResolveAddress(resolveAddress)
                .setResolveAddressStr(resolveAddressStr)
                .setDynamicI(dynamicI);
    }

    @Accessors(chain = true)
    @Data
    static class ResolveParams {
        String methodName;
        long resolveAddress;
        String resolveAddressStr;
        boolean dynamicI = false;
    }

    private static MethodSpec shortResolve(
            MethodSpec.Builder builder,
            PointDataEntity point,
            ResolveParams resolveParams,
            TypeSpec.Builder classBuilder) {
        //        builder = builder.addParameter(String.class, "deviceId");
        Object address;
        if (resolveParams.isDynamicI()) {
            // 这里的参数不仅仅是 deviceId
            // 如果包含i 需要额外添加参数
            builder = builder.addParameter(int.class, "index");
            address = resolveParams.getResolveAddressStr();
            builder.addModifiers(Modifier.PUBLIC);
        } else {
            address = resolveParams.getResolveAddress();
            builder.addModifiers(Modifier.PRIVATE);
        }
        String returnStr;
        FieldSpec.Builder fieldBuilder;
        if (point.getPointMul() != 1) {
            returnStr = "return (float) ((short) data[$L])";
            returnStr = returnStr + " / " + point.getPointMul();
            builder = builder.returns(float.class);
            fieldBuilder =
                    FieldSpec.builder(TypeName.FLOAT, resolveParams.methodName, Modifier.PRIVATE);

        } else {
            returnStr = "return (float) data[$L]";
            returnStr = returnStr + " / " + point.getPointMul() + " )";
            builder = builder.returns(float.class);
            fieldBuilder =
                    FieldSpec.builder(TypeName.FLOAT, resolveParams.methodName, Modifier.PRIVATE);
        }
        if (!resolveParams.dynamicI) {
            fieldBuilder.addModifiers(Modifier.FINAL);
            FieldSpec field = fieldBuilder.build();
            classBuilder.addField(field);
        }

        return builder.addJavadoc(point.getPointName())
                .beginControlFlow("if (data != null)")
                .addStatement(CodeBlock.of(returnStr, address))
                .endControlFlow()
                .addStatement("return (short) 0")
                .build();
    }

    private static MethodSpec longResolve(
            MethodSpec.Builder builder,
            PointDataEntity point,
            ResolveParams resolveParams,
            TypeSpec.Builder classBuilder) {

        Object address;
        if (resolveParams.isDynamicI()) {
            builder = builder.addParameter(int.class, "index");
            address = "index";
            builder.addModifiers(Modifier.PUBLIC);
        } else {
            address = resolveParams.getResolveAddress();
            builder.addModifiers(Modifier.PRIVATE);
        }
        builder.returns(double.class);
        if (!resolveParams.dynamicI) {
            FieldSpec.Builder fieldBuilder =
                    FieldSpec.builder(
                            TypeName.DOUBLE,
                            resolveParams.methodName,
                            Modifier.PRIVATE,
                            Modifier.FINAL);
            classBuilder.addField(fieldBuilder.build());
        }

        builder.addJavadoc(point.getPointName())
                .beginControlFlow("if (data != null && data.length > ($L + 1))", address)
                .addStatement("int high = data[$L]", address)
                .addStatement("int low = data[$L + 1]", address)
                .addStatement("long temp = ((long) high << 16) + low");

        builder.addStatement("return (double) temp / $L", point.getPointMul());
        builder.endControlFlow().addStatement("return 0.0");
        return builder.build();
    }

    //    private static MethodSpec longResolve(
    //            MethodSpec.Builder builder,
    //            PointDataEntity point,
    //            ResolveParams resolveParams,
    //            TypeSpec.Builder classBuilder) {
    //        builder = builder.returns(long.class);
    //
    //        //        builder = builder.addParameter(String.class, "deviceId");
    //        Object address;
    //        if (resolveParams.isDynamicI()) {
    //            // 这里的参数不仅仅是 deviceId
    //            // 如果包含i 需要额外添加参数
    //            builder = builder.addParameter(int.class, "index");
    //            address = resolveParams.getResolveAddressStr();
    //            builder.addModifiers(Modifier.PUBLIC);
    //        } else {
    //            address = resolveParams.getResolveAddress();
    //            builder.addModifiers(Modifier.PRIVATE);
    //        }
    //        String returnStr = "return data.getData()[$L]";
    //        if (point.getPointMul() != 1) {
    //            returnStr = returnStr + " / " + point.getPointMul();
    //        }
    //        if (!resolveParams.dynamicI) {
    //            FieldSpec.Builder fieldBuilder =
    //                    FieldSpec.builder(TypeName.LONG, resolveParams.methodName,
    // Modifier.PRIVATE);
    //            fieldBuilder.addModifiers(Modifier.FINAL);
    //            FieldSpec field = fieldBuilder.build();
    //            classBuilder.addField(field);
    //        }
    //        return builder.addJavadoc(point.getPointName())
    //                //                .addStatement(
    //                //                        "$T deviceRealTimeData =
    // getDeviceRealTimeData(deviceId)",
    //                //                        ClassName.get("com.wifochina.modules.data.realtime",
    //                // "DeviceRealTimeData"))
    //                .beginControlFlow("if (data != null)")
    //                .addStatement(CodeBlock.of(returnStr, address))
    //                .endControlFlow()
    //                .addStatement("return 0")
    //                .build();
    //    }
    //
    private static MethodSpec integerResolve(
            MethodSpec.Builder builder,
            PointDataEntity point,
            ResolveParams resolveParams,
            TypeSpec.Builder classBuilder) {
        Object address;
        if (resolveParams.isDynamicI()) {
            // 这里的参数不仅仅是 deviceId
            // 如果包含i 需要额外添加参数
            builder = builder.addParameter(int.class, "index");
            address = resolveParams.getResolveAddressStr();
            builder.addModifiers(Modifier.PUBLIC);
        } else {
            builder.addModifiers(Modifier.PRIVATE);
            address = resolveParams.getResolveAddress();
        }
        //        String returnStr = "return data.getData()[$L]";
        String returnStr;
        FieldSpec.Builder fieldBuilder = null;
        if (point.getPointMul() != 1) {
            builder = builder.returns(double.class);
            returnStr = "return (double)data[$L]";
            returnStr = returnStr + " / " + point.getPointMul();
            fieldBuilder =
                    FieldSpec.builder(TypeName.DOUBLE, resolveParams.methodName, Modifier.PRIVATE);
        } else {
            returnStr = "return data[$L]";
            builder = builder.returns(int.class);
            fieldBuilder =
                    FieldSpec.builder(TypeName.INT, resolveParams.methodName, Modifier.PRIVATE);
        }

        if (!resolveParams.dynamicI) {
            fieldBuilder.addModifiers(Modifier.FINAL);
            FieldSpec field = fieldBuilder.build();
            classBuilder.addField(field);
        }

        return builder.addJavadoc(point.getPointName())
                .beginControlFlow("if (data != null)")
                .addStatement(CodeBlock.of(returnStr, address))
                .endControlFlow()
                .addStatement("return 0")
                .build();
    }

    private static List<String> getVendorTypes() throws SQLException {
        List<String> points = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs =
                        stmt.executeQuery(
                                "SELECT vendor_type FROM t_point_data group by vendor_type")) {
            //                                "SELECT point_name, point_column, point_offset,
            // point_address,point_type,point_mul FROM t_point_data where protocol_version = 2 and
            // point_level='T0' and point_offset not like '%+%' ")) {

            while (rs.next()) {
                points.add(rs.getString("vendor_type"));
            }
        }
        return points;
    }

    private static List<PointDataEntity> getPointDataByVendorType(String vendorType)
            throws SQLException {
        List<PointDataEntity> points = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs =
                        stmt.executeQuery(
                                "SELECT point_name, point_column, point_offset, point_address,point_type,point_mul FROM t_point_data where vendor_type = '"
                                        + vendorType
                                        + "' order by point_id asc")) {
            //                                "SELECT point_name, point_column, point_offset,
            // point_address,point_type,point_mul FROM t_point_data where protocol_version = 2 and
            // point_level='T0' and point_offset not like '%+%' ")) {

            while (rs.next()) {
                points.add(
                        new PointDataEntity()
                                .setPointName(rs.getString("point_name"))
                                .setPointColumn(rs.getString("point_column"))
                                .setPointOffset(rs.getString("point_offset"))
                                .setPointAddress(rs.getInt("point_address"))
                                .setPointType(rs.getString("point_type"))
                                .setPointMul(rs.getInt("point_mul")));
            }
        }
        return points;
    }

    private static List<PointDataEntity> getPointData200() throws SQLException {
        List<PointDataEntity> points = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs =
                        stmt.executeQuery(
                                "SELECT point_name, point_column, point_offset, point_address,point_type,point_mul FROM t_point_data where protocol_version = 2 order by point_id asc")) {
            //                                "SELECT point_name, point_column, point_offset,
            // point_address,point_type,point_mul FROM t_point_data where protocol_version = 2 and
            // point_level='T0' and point_offset not like '%+%' ")) {

            while (rs.next()) {
                points.add(
                        new PointDataEntity()
                                .setPointName(rs.getString("point_name"))
                                .setPointColumn(rs.getString("point_column"))
                                .setPointOffset(rs.getString("point_offset"))
                                .setPointAddress(rs.getInt("point_address"))
                                .setPointType(rs.getString("point_type"))
                                .setPointMul(rs.getInt("point_mul")));
            }
        }
        return points;
    }

    private static List<PointDataEntity> getPointData100() throws SQLException {
        List<PointDataEntity> points = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs =
                        stmt.executeQuery(
                                "SELECT point_name, point_column, point_offset, point_address,point_type,point_mul FROM t_point_data where protocol_version = 1 order by point_id asc ")) {

            while (rs.next()) {
                points.add(
                        new PointDataEntity()
                                .setPointName(rs.getString("point_name"))
                                .setPointColumn(rs.getString("point_column"))
                                .setPointOffset(rs.getString("point_offset"))
                                .setPointAddress(rs.getInt("point_address"))
                                .setPointType(rs.getString("point_type"))
                                .setPointMul(rs.getInt("point_mul")));
            }
        }
        return points;
    }

    /**
     * _ 名称转换成 驼峰 并且 在 I 前后加上 -> _I_
     *
     * @param column : column
     * @return : java method Name
     */
    private static String convertToMethodName(boolean dynamicI, String column) {
        // 先替换 - 为 _
        column = column.replace("-", "_");
        String[] parts = column.split("_");
        // 第一个单词保持小写
        StringBuilder methodName = new StringBuilder(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            if (parts[i].equals(I)) {
                parts[i] = _I_;
                methodName.append(parts[i]);
                continue;
            } else if (parts[i].matches("[A-Z]+")) {
                // 检查是否全为大写字母
                methodName.append(parts[i]);
                // 保持全大写
            } else {
                methodName
                        .append(Character.toUpperCase(parts[i].charAt(0)))
                        .append(parts[i].substring(1).toLowerCase());
            }
        }
        if (dynamicI) {
            return "get" + methodName.substring(0, 1).toUpperCase() + methodName.substring(1);
        }
        return methodName.toString();
    }
}
