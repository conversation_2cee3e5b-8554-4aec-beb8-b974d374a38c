package com.wifochina.realtimeservice.generate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点位数据对象
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Data
@ApiModel(value = "PointDataEntity对象", description = "点位数据对象")
@Accessors(chain = true)
public class PointDataEntity implements Cloneable, Serializable {

    private static final long serialVersionUID = 1L;

    private Integer pointId;

    @ApiModelProperty(value = "偏移量 ")
    private String pointOffset;

    @ApiModelProperty(value = "地址")
    private Integer pointAddress;

    @ApiModelProperty(value = "点位名")
    private String pointName;

    @ApiModelProperty(value = "字段名")
    private String pointColumn;

    @ApiModelProperty(value = "数值倍率")
    private Integer pointMul;

    @ApiModelProperty(value = "数据类型")
    private String pointType;

    @ApiModelProperty(value = "采样等级")
    private String pointLevel;

    @ApiModelProperty(value = "单个采样长度")
    private String pointLen;

    @ApiModelProperty(value = "系统值")
    private String value;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
