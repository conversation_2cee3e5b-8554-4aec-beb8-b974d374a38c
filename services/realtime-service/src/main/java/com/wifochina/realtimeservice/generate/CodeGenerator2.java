package com.wifochina.realtimeservice.generate;

import com.squareup.javapoet.*;

import com.wifochina.realtimemodel.common.DeviceRealTimeData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import javax.lang.model.element.Modifier;

/**
 * Created on 2025/2/26 16:00.
 *
 * <AUTHOR>
 */
public class CodeGenerator2 {

    private static final String POINT_TYPE_Uint16 = "Uint16";
    private static final String POINT_TYPE_Int16 = "Int16";
    private static final String POINT_TYPE_BIT = "BIT";
    private static final String POINT_TYPE_uInt32 = "uint32";
    private static final String POINT_TYPE_UInt32 = "Uint32";
    private static final String POINT_TYPE_Int32 = "Int32";
    private static final String POINT_TYPE_ASCII = "ASCII";
    private static final String I = "I";
    private static final String _I_ = "_I_";

    private static final String DB_URL =
            "**********************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "123@abcd";

    private static final String CLASS_NAME = "Ems200PointData";

    public static void main(String[] args) throws SQLException, IOException {
        List<String> vendorTypes = getVendorTypes();
        String className;
        List<PointDataEntity> points;
        for (String vendorType : vendorTypes) {
            if (vendorType.isEmpty()) {
                System.out.println("vendorType = " + vendorType + "是空");
                continue;
            }
            className = vendorType;
            points = getPointDataByVendorType(vendorType);
            if (points.isEmpty()) {
                System.out.println("未查询到该vendorType = " + vendorType + "的点表");
                return;
            }
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDate = now.format(formatter);
            //        // 生成 Java 类
            TypeSpec.Builder classBuilder =
                    TypeSpec.classBuilder(className)
                            .addJavadoc("<AUTHOR>
                            .addJavadoc("@date " + formattedDate)
                            .addModifiers(Modifier.PUBLIC)
                            .addAnnotation(Service.class)
                            .addAnnotation(AllArgsConstructor.class)
                            .addAnnotation(Slf4j.class)
                            .addField(
                                    ClassName.get("com.wifochina.modules.oauth.util", "WebUtils"),
                                    "webUtils",
                                    Modifier.PUBLIC)
                            .addField(
                                    ClassName.get(
                                            "com.wifochina.modules.data.service",
                                            "RealTimeDataService"),
                                    "realTimeDataService",
                                    Modifier.PRIVATE,
                                    Modifier.FINAL);

            MethodSpec build =
                    MethodSpec.methodBuilder("getDeviceRealTimeData")
                            .addModifiers(Modifier.PUBLIC)
                            .returns(DeviceRealTimeData.class)
                            .addParameter(String.class, "deviceId")
                            .addStatement(
                                    "return realTimeDataService.realTimeData(deviceId, WebUtils.projectId.get())")
                            .build();
            classBuilder.addMethod(build);

            // 新增 getDeviceProtocolVersion 方法
            //        MethodSpec getDeviceProtocolVersion =
            //                MethodSpec.methodBuilder("getDeviceProtocolVersion")
            //                        .addModifiers(Modifier.PUBLIC)
            //                        .returns(int.class)
            //                        .addParameter(int[].class, "data")
            //                        .addStatement("return data[42]")
            //                        .build();
            //        classBuilder.addMethod(getDeviceProtocolVersion);

            for (PointDataEntity point : points) {
                // 暂时无需求要实时读取 电芯温度和电压 而且这个 电表的offset不规范 解不了
                if (point.getPointColumn().equals("bms_cell_voltage_{i}")
                        || point.getPointColumn().equals("bms_cell_temperature_{i}")) {
                    continue;
                }
                ResolveParams resolveParams = getResolveParams(point);
                //            String method = getMethodFromPointColumn(point);
                MethodSpec.Builder builder =
                        MethodSpec.methodBuilder(resolveParams.getMethodName())
                                .addModifiers(Modifier.PUBLIC);
                MethodSpec getter = null;

                if (point.getPointType().equals(POINT_TYPE_Uint16)
                        || point.getPointType().equals(POINT_TYPE_Int32)
                        || point.getPointType().equals(POINT_TYPE_BIT)) {
                    getter = integerResolve(builder, point, resolveParams);
                }
                if (point.getPointType().equals(POINT_TYPE_uInt32)
                        || point.getPointType().equals(POINT_TYPE_UInt32)) {
                    getter = longResolve(builder, point, resolveParams);
                }
                if (point.getPointType().equals(POINT_TYPE_Int16)) {
                    getter = shortResolve(builder, point, resolveParams);
                }
                if (point.getPointType().equals(POINT_TYPE_ASCII)) {
                    getter = asciiResolve(builder, point, resolveParams);
                }
                if (getter == null) {
                    System.out.println("getter null");
                }
                classBuilder.addMethod(getter);
            }


            TypeSpec pointDataClass = classBuilder.build();
            JavaFile javaFile = JavaFile.builder("com.wifochina.common", pointDataClass).build();
            // 输出到 src/main/java/com/example
            File outputDir =
                    new File(
                            "/Users/<USER>/java/newworkspace/ems-server/services/realtime-service/src/main/java/");
            javaFile.writeTo(outputDir);
        }
    }

    private static MethodSpec asciiResolve(
            MethodSpec.Builder builder, PointDataEntity point, ResolveParams resolveParams) {
        builder = builder.returns(String.class);
        return builder.addParameter(String.class, "deviceId")
                .addJavadoc(point.getPointName())
                .addStatement(
                        "$T deviceRealTimeData = getDeviceRealTimeData(deviceId)",
                        ClassName.get("com.wifochina.modules.data.realtime", "DeviceRealTimeData"))
                .beginControlFlow("if (deviceRealTimeData !=  null)")
                .addStatement("int index = " + resolveParams.resolveAddress)
                .addStatement("StringBuilder value = new StringBuilder()")
                .beginControlFlow("for (int i = 0; i < " + point.getPointMul() + "; i++)")
                .beginControlFlow("if (deviceRealTimeData.getData()[index + i] == 0)")
                .addStatement("continue")
                .endControlFlow()
                .addStatement("value.append((char) (deviceRealTimeData.getData()[index + i] >> 8))")
                .addStatement(
                        "value.append((char) (deviceRealTimeData.getData()[index + i] & 0xFF))")
                .endControlFlow()
                .addStatement("return value.toString()")
                .endControlFlow()
                .addStatement("return null")
                .build();
    }

    private static ResolveParams getResolveParams(PointDataEntity point) {
        //        String methodName = getMethodFromPointColumn(point);
        String methodName;
        long resolveAddress = 0;
        String resolveAddressStr = "";
        boolean dynamicI = false;
        if (point.getPointColumn().contains("{i}")) {
            String newPointColumn = point.getPointColumn().replace("{i}_", "I_");
            methodName = convertToMethodName(newPointColumn);
            dynamicI = true;
        } else {
            methodName = convertToMethodName(point.getPointColumn());
        }
        if (!dynamicI) {
            // 正常的这种
            resolveAddress = point.getPointAddress() + Integer.parseInt(point.getPointOffset());
        } else {
            // 动态的
            // 解析 3500 + 150 * index; 这种东西 , 3500+DCDCi*150
            int addressStart = 0;
            try {
                addressStart =
                        Integer.parseInt(
                                point.getPointOffset()
                                        .substring(0, point.getPointOffset().indexOf("+")));
            } catch (Exception e) {
                System.out.println("e:");
            }
            //             500 + 150 * index;
            int addressIndexStep =
                    Integer.parseInt(
                            point.getPointOffset()
                                    .substring(
                                            point.getPointOffset().indexOf("*") + 1,
                                            point.getPointOffset().length() - 1));
            // 500 + 15 * {index}
            resolveAddressStr = addressStart + " + " + addressIndexStep + " * index";
        }
        return new ResolveParams()
                .setMethodName(methodName)
                .setResolveAddress(resolveAddress)
                .setResolveAddressStr(resolveAddressStr)
                .setDynamicI(dynamicI);
    }

    @Accessors(chain = true)
    @Data
    static class ResolveParams {
        String methodName;
        long resolveAddress;
        String resolveAddressStr;
        boolean dynamicI = false;
    }

    private static String getMethodFromPointColumn(PointDataEntity point) {
        String methodName;
        if (point.getPointColumn().contains("{i}")) {
            String newPointColumn = point.getPointColumn().replace("{i}_", I);
            methodName = convertToMethodName(newPointColumn);
        } else {
            methodName = convertToMethodName(point.getPointColumn());
        }
        System.out.println(methodName);
        return methodName;
    }

    private static MethodSpec shortResolve(
            MethodSpec.Builder builder, PointDataEntity point, ResolveParams resolveParams) {
        builder = builder.returns(short.class);

        builder = builder.addParameter(String.class, "deviceId");
        Object address;
        if (resolveParams.isDynamicI()) {
            // 这里的参数不仅仅是 deviceId
            // 如果包含i 需要额外添加参数
            builder = builder.addParameter(int.class, "index");
            address = resolveParams.getResolveAddressStr();
        } else {
            address = resolveParams.getResolveAddress();
        }
        String returnStr = "return (short) deviceRealTimeData.getData()[$L]";
        if (point.getPointMul() != 1) {
            returnStr = "return (short) ((short) deviceRealTimeData.getData()[$L]";
            returnStr = returnStr + " / " + point.getPointMul() + " )";
        }
        return builder.addJavadoc(point.getPointName())
                .addStatement(
                        "$T deviceRealTimeData = getDeviceRealTimeData(deviceId)",
                        ClassName.get("com.wifochina.modules.data.realtime", "DeviceRealTimeData"))
                .beginControlFlow("if (deviceRealTimeData != null)")
                .addStatement(CodeBlock.of(returnStr, address))
                //                .addStatement(
                //                        CodeBlock.of(
                //                                "return (short) ((short)
                // deviceRealTimeData.getData()[$L] / "
                //                                        + point.getPointMul()
                //                                        + " )",
                //                                address))
                .endControlFlow()
                .addStatement("return (short) 0")
                .build();
    }

    private static MethodSpec longResolve(
            MethodSpec.Builder builder, PointDataEntity point, ResolveParams resolveParams) {
        builder = builder.returns(long.class);

        builder = builder.addParameter(String.class, "deviceId");
        Object address;
        if (resolveParams.isDynamicI()) {
            // 这里的参数不仅仅是 deviceId
            // 如果包含i 需要额外添加参数
            builder = builder.addParameter(int.class, "index");
            address = resolveParams.getResolveAddressStr();
        } else {
            address = resolveParams.getResolveAddress();
        }
        String returnStr = "return deviceRealTimeData.getData()[$L]";
        if (point.getPointMul() != 1) {
            returnStr = returnStr + " / " + point.getPointMul();
        }
        return builder.addJavadoc(point.getPointName())
                .addStatement(
                        "$T deviceRealTimeData = getDeviceRealTimeData(deviceId)",
                        ClassName.get("com.wifochina.modules.data.realtime", "DeviceRealTimeData"))
                .beginControlFlow("if (deviceRealTimeData != null)")
                .addStatement(CodeBlock.of(returnStr, address))
                .endControlFlow()
                .addStatement("return 0")
                .build();
    }

    private static MethodSpec integerResolve(
            MethodSpec.Builder builder, PointDataEntity point, ResolveParams resolveParams) {
        builder = builder.returns(int.class);

        builder = builder.addParameter(String.class, "deviceId");
        Object address;
        if (resolveParams.isDynamicI()) {
            // 这里的参数不仅仅是 deviceId
            // 如果包含i 需要额外添加参数
            builder = builder.addParameter(int.class, "index");
            address = resolveParams.getResolveAddressStr();
        } else {
            address = resolveParams.getResolveAddress();
        }
        String returnStr = "return deviceRealTimeData.getData()[$L]";
        if (point.getPointMul() != 1) {
            returnStr = returnStr + " / " + point.getPointMul();
        }
        return builder.addJavadoc(point.getPointName())
                .addStatement(
                        "$T deviceRealTimeData = getDeviceRealTimeData(deviceId)",
                        ClassName.get("com.wifochina.modules.data.realtime", "DeviceRealTimeData"))
                .beginControlFlow("if (deviceRealTimeData != null)")
                .addStatement(CodeBlock.of(returnStr, address))
                .endControlFlow()
                .addStatement("return 0")
                .build();
    }

    private static List<String> getVendorTypes() throws SQLException {
        List<String> points = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs =
                        stmt.executeQuery(
                                "SELECT vendor_type FROM t_point_data group by vendor_type")) {
            //                                "SELECT point_name, point_column, point_offset,
            // point_address,point_type,point_mul FROM t_point_data where protocol_version = 2 and
            // point_level='T0' and point_offset not like '%+%' ")) {

            while (rs.next()) {
                points.add(rs.getString("vendor_type"));
            }
        }
        return points;
    }

    private static List<PointDataEntity> getPointDataByVendorType(String vendorType)
            throws SQLException {
        List<PointDataEntity> points = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs =
                        stmt.executeQuery(
                                "SELECT point_name, point_column, point_offset, point_address,point_type,point_mul FROM t_point_data where vendor_type = '"
                                        + vendorType
                                        + "' order by point_id asc")) {
            //                                "SELECT point_name, point_column, point_offset,
            // point_address,point_type,point_mul FROM t_point_data where protocol_version = 2 and
            // point_level='T0' and point_offset not like '%+%' ")) {

            while (rs.next()) {
                points.add(
                        new PointDataEntity()
                                .setPointName(rs.getString("point_name"))
                                .setPointColumn(rs.getString("point_column"))
                                .setPointOffset(rs.getString("point_offset"))
                                .setPointAddress(rs.getInt("point_address"))
                                .setPointType(rs.getString("point_type"))
                                .setPointMul(rs.getInt("point_mul")));
            }
        }
        return points;
    }

    private static List<PointDataEntity> getPointData200() throws SQLException {
        List<PointDataEntity> points = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs =
                        stmt.executeQuery(
                                "SELECT point_name, point_column, point_offset, point_address,point_type,point_mul FROM t_point_data where protocol_version = 2 order by point_id asc")) {
            //                                "SELECT point_name, point_column, point_offset,
            // point_address,point_type,point_mul FROM t_point_data where protocol_version = 2 and
            // point_level='T0' and point_offset not like '%+%' ")) {

            while (rs.next()) {
                points.add(
                        new PointDataEntity()
                                .setPointName(rs.getString("point_name"))
                                .setPointColumn(rs.getString("point_column"))
                                .setPointOffset(rs.getString("point_offset"))
                                .setPointAddress(rs.getInt("point_address"))
                                .setPointType(rs.getString("point_type"))
                                .setPointMul(rs.getInt("point_mul")));
            }
        }
        return points;
    }

    private static List<PointDataEntity> getPointData100() throws SQLException {
        List<PointDataEntity> points = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                Statement stmt = conn.createStatement();
                ResultSet rs =
                        stmt.executeQuery(
                                "SELECT point_name, point_column, point_offset, point_address,point_type,point_mul FROM t_point_data where protocol_version = 1 order by point_id asc ")) {

            while (rs.next()) {
                points.add(
                        new PointDataEntity()
                                .setPointName(rs.getString("point_name"))
                                .setPointColumn(rs.getString("point_column"))
                                .setPointOffset(rs.getString("point_offset"))
                                .setPointAddress(rs.getInt("point_address"))
                                .setPointType(rs.getString("point_type"))
                                .setPointMul(rs.getInt("point_mul")));
            }
        }
        return points;
    }

    /**
     * _ 名称转换成 驼峰 并且 在 I 前后加上 -> _I_
     *
     * @param column : column
     * @return : java method Name
     */
    private static String convertToMethodName(String column) {
        // 先替换 - 为 _
        column = column.replace("-", "_");
        String[] parts = column.split("_");
        // 第一个单词保持小写
        StringBuilder methodName = new StringBuilder(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            if (parts[i].equals(I)) {
                parts[i] = _I_;
                methodName.append(parts[i]);
                continue;
            }
            methodName
                    .append(Character.toUpperCase(parts[i].charAt(0)))
                    .append(parts[i].substring(1).toLowerCase());
        }

        return methodName.toString();
    }
}
