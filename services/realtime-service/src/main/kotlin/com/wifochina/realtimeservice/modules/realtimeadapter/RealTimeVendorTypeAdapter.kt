package com.wifochina.realtimeservice.modules.realtimeadapter

import com.wifochina.realtimemodel.common.IProtocolData
import org.springframework.stereotype.Service

/**
 * Created on 2025/3/27 17:30.
 * <AUTHOR>
 */
@Service
class RealTimeVendorTypeAdapter(
    val protocols: List<IProtocolData>
) {
    fun protocolData(vendorType: String): IProtocolData {
        val protocol = protocols.first { it.vendorType() == vendorType }
        return protocol;
    }
}