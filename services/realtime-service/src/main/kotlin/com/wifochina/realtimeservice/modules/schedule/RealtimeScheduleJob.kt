package com.wifochina.realtimeservice.modules.schedule

import com.alibaba.fastjson.JSON
import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import com.querydsl.jpa.impl.JPAQueryFactory
import com.wifochina.realtimemodel.entity.QProjectEntity
import com.wifochina.realtimeservice.config.MdcKotlinContext
import com.wifochina.realtimeservice.modules.common.ServiceException
import com.wifochina.realtimeservice.modules.realtime.RealTimeDataService
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component
import org.springframework.util.StopWatch
import java.util.concurrent.TimeUnit
import javax.annotation.PostConstruct


private val log = KotlinLogging.logger { }

@Component
class elealtimeScheduleJob(
    val queryFactory: JPAQueryFactory,
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
    val redisTemplate: RedisTemplate<String, String>,
    val realTimeDataService: RealTimeDataService
) {
    @Value("\${ems.badRequest.retryTime}")
    private val retryTime: Long = 1

    lateinit var delayRequestCache: Cache<String, Boolean>

    companion object {
        val qProjectEntity = QProjectEntity.projectEntity!!
        var projects: MutableList<String>? = null
    }

    @PostConstruct
    fun initCache() {
        delayRequestCache =
            Caffeine.newBuilder().expireAfterWrite(retryTime, TimeUnit.MINUTES).maximumSize(1000).build()
        projects = queryFactory.select(
            qProjectEntity.id
        ).from(qProjectEntity).where(
            qProjectEntity.projectModel.eq(0).and(qProjectEntity.whetherDelete.eq(false))
        ).fetch()
    }

    @Scheduled(initialDelay = 1000, fixedDelayString = "\${ems.updateProject.fixedDelay}")
    fun projectUpdate() {
        projects = queryFactory.select(
            qProjectEntity.id
        ).from(qProjectEntity).where(
            qProjectEntity.projectModel.eq(0).and(qProjectEntity.whetherDelete.eq(false))
        ).fetch()
    }


    @Scheduled(initialDelay = 3000, fixedDelayString = "\${spring.task.project.fixedDelay}")
    fun realTimeSchedule() {
        log.info { "realTimeSchedule start..." }
        val stopwatch = StopWatch()
        stopwatch.start()
        runBlocking {
            withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                projects?.forEach { projectId ->
                    launch {
                        if (delayRequestCache.getIfPresent(projectId) == true) {
                            log.debug { "badRequestCache projectId:${projectId} " }
                            return@launch
                        }
                        try {
                            realTimeDataService.realTimeDataNew(null, projectId)?.forEach { (deviceId, deviceData) ->
                                if (deviceData.data != null) {
                                    log.info { "projectId:${projectId} set realtime deviceData  in redis  : $deviceId" }
                                    redisTemplate.opsForValue().set(
                                        "realtime:device:${projectId}:${deviceId}",
                                        JSON.toJSONString(deviceData),
                                        1,
                                        TimeUnit.MINUTES
                                    )
                                } else {
                                    log.debug { "deviceData.data is null , projectId: $projectId, deviceId:$deviceId" }
                                }
                            }
                        } catch (serviceException: ServiceException) {
                            delayRequestCache.put(projectId, true)
                        }
                    }
                }
            }
        }
        stopwatch.stop()
        log.info { "realTimeSchedule end...s :${stopwatch.totalTimeSeconds}" }
    }
}