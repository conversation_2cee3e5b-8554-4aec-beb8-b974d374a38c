package com.wifochina.realtimeservice.modules.utils


import com.querydsl.jpa.impl.JPAQueryFactory
import com.wifochina.realtimemodel.entity.ProjectEntity
import com.wifochina.realtimemodel.entity.QControllerEntity
import com.wifochina.realtimeservice.modules.common.ServiceException
import com.wifochina.realtimeservice.modules.repository.ProjectRepository

import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import kotlin.jvm.optionals.getOrNull

/**
 * Created on 2025/3/5 11:13.
 * <AUTHOR>
 */

private val log = KotlinLogging.logger { }

@Service
class GetGoApiUtils(
    val queryFactory: JPAQueryFactory,
    val projectRepository: ProjectRepository,
) {

    @Value("\${ems.gateway}")
    private val gatewayUrl: String? = null

    companion object {
        val qControllerEntity: QControllerEntity = QControllerEntity.controllerEntity
        const val PROTOCOL_PREFIX = "http://"
    }

    fun getGoApi(projectId: String): String {
        return takeIf {
            projectRepository.existsById(projectId)
        }?.let {
            projectRepository.findById(projectId).getOrNull()?.let {
                getGoApi(it)
            }
        } ?: run {
            throw ServiceException(
                500, "can`t find project by projectId $projectId"
            )
        }
    }

    fun getGoApi(projectEntity: ProjectEntity): String {
        return if (projectEntity.projectModel == 1) {
            queryFactory.select(qControllerEntity.ip, qControllerEntity.port).from(qControllerEntity)
                .where(qControllerEntity.projectId.eq(projectEntity.id)).fetchOne()?.let {
                    "$PROTOCOL_PREFIX${it.get(0, String::class.java)?.trim()}:${it.get(1, Integer::class.java)}"
                } ?: run {
                log.error { "getGoApi can`t find controller by projectId ${projectEntity.id}" }
                throw ServiceException(500, "getGoApi can`t find controller by projectId ${projectEntity.id}")
            }
        } else {
            gatewayUrl ?: run {
                log.error { "gatewayUrl can`t find  by projectId ${projectEntity.id}" }
                throw ServiceException(500, "gatewayUrl can`t find by projectId ${projectEntity.id}")
            }
        }
    }
}