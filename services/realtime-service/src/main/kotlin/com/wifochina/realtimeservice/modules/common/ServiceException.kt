package com.wifochina.realtimeservice.modules.common

/**
 * Created on 2025/6/30 17:03.
 * <AUTHOR>
 */
class ServiceException(
    val code: Int,
    override val message: String
) : RuntimeException() {

    constructor(code: Int) : this(code, "Service exception with code: $code")

    constructor(message: String) : this(-1, message)

    constructor(cause: Throwable) : this(-1, cause.message ?: "Unknown error") {
        initCause(cause)
    }

    override fun toString(): String {
        return "ServiceException(code=$code, message=$message)"
    }
}