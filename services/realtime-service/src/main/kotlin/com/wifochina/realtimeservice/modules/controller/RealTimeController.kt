package com.wifochina.realtimeservice.modules.controller

import com.alibaba.fastjson.JSON
import com.wifochina.realtimemodel.common.DeviceRealTimeData
import com.wifochina.realtimemodel.common.IProtocolData
import com.wifochina.realtimemodel.common.VendorDataSerializer
import com.wifochina.realtimemodel.common.WeihengEms100
import io.github.oshai.kotlinlogging.KotlinLogging
import io.swagger.annotations.Api
import lombok.RequiredArgsConstructor
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

private val log = KotlinLogging.logger { }

/**
 * Created on 2025/4/8 15:25.
 * <AUTHOR>
 */
@RestController
@RequestMapping("/realtime")
@Api(tags = ["RealTimeController"])
@RequiredArgsConstructor
class RealTimeController(
    val redisTemplate: RedisTemplate<String, String>
) {
    @GetMapping("/get/{projectId}/{deviceId}/{detail}")
    fun get(
        @PathVariable projectId: String, @PathVariable deviceId: String, @PathVariable detail: Int?
    ): IProtocolData? {
        if (detail == 1) {
            VendorDataSerializer.ignoreThreadLocal.set(false)
        } else {
            VendorDataSerializer.ignoreThreadLocal.set(true)
        }
        return JSON.parseObject(
            redisTemplate.opsForValue().get("realtime:device:${projectId}:${deviceId}"), DeviceRealTimeData::class.java
        )?.let { deviceRealTimeData ->
            deviceRealTimeData.data?.let {
                val protocolVersion = deviceRealTimeData.data?.get(42)
                var result: IProtocolData? = null
                if (protocolVersion == 1) {
                    result = WeihengEms100(deviceRealTimeData.data)
                } else if (protocolVersion == 2) {
                    result = WeihengEms100(deviceRealTimeData.data)
                }
                result
            }
        } ?: run {
            null
        }
    }

    @GetMapping("/get/{projectId}")
    fun get(@PathVariable projectId: String): IProtocolData? {
//        var realTimeData = realTimeDataService.realTimeDataNew(null, projectId)
        return null
    }
}