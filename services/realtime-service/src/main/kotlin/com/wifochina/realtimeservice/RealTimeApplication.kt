package com.wifochina.realtimeservice

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.runApplication
import org.springframework.context.annotation.EnableAspectJAutoProxy
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.scheduling.annotation.EnableScheduling

/**
 * Created on 2025/3/26 18:38.
 * <AUTHOR>
 */
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication
@EnableJpaRepositories(basePackages = ["com.wifochina.realtimeservice.modules"])
@EntityScan(basePackages = ["com.wifochina.realtimemodel.entity"])
@EnableScheduling
class RealTimeApplication

fun main() {
    runApplication<RealTimeApplication>()
}