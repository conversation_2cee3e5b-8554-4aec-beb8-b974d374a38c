package com.wifochina.realtimeservice.modules.realtime

import com.github.benmanes.caffeine.cache.Caffeine
import com.wifochina.realtimemodel.common.DeviceRealTimeData
import com.wifochina.realtimemodel.common.GoOnlineResponse
import com.wifochina.realtimemodel.common.RealTimeData
import com.wifochina.realtimeservice.modules.common.ServiceException
import com.wifochina.realtimeservice.modules.realtime.RealTimeDataService.Companion.GET_ONLINE_URL
import com.wifochina.realtimeservice.modules.utils.GetGoApiUtils
import com.wifochina.realtimeservice.modules.utils.RestTemplateUtils
import com.wifochina.realtimeservice.utils.AuthUtil
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

/**
 * Created on 2025/2/26 14:28.
 * <AUTHOR>
 */

private val log = KotlinLogging.logger { }

@Service
class RealTimeDataService(
    val goApiUtils: GetGoApiUtils,
    val restTemplateUtils: RestTemplateUtils,

    ) {
    companion object {
        const val GET_ONLINE_URL = "/api/v1/get_online"
        const val GET_EMS_MODBUS = "/api/v1/get_ems_modbus"
    }

    private val cache = Caffeine.newBuilder().expireAfterWrite(5, TimeUnit.SECONDS).maximumSize(1000)
        .build<String, Map<String, DeviceRealTimeData>>()

    /**
     * 按照重构前的代码 是 5s 的缓存 这里暂时不变
     */
    private fun getProjectRealTimeDataFromCache(
        projectId: String, fetchFromApi: (String) -> Map<String, DeviceRealTimeData>
    ): Map<String, DeviceRealTimeData> {
        return cache.get(projectId) {
            //如果这个设备id 在缓存中找不到 则调用这个 lambda 表达式去获取, 这个lambda表达式就是去真正查询go接口获取数据
            fetchFromApi(it)
        }!!
    }

    fun realTimeDataNew(deviceId: String?, projectId: String): RealTimeData? {
        //1.检查 cache 缓存中 是否已经有缓存的数据
        val goApi = goApiUtils.getGoApi(projectId)
        val url = "$goApi$GET_EMS_MODBUS"
        return try {
            getProjectRealTimeDataFromCache(projectId) {
                restTemplateUtils.sendRequestNew(
                    url, HttpMethod.GET, null, mapOf(
                        "ProjectId" to projectId, "W-AUTH" to AuthUtil.getAuth()
                    ), object : ParameterizedTypeReference<RealTimeData>() {}, false
                ) ?: run {
                    log.error { "realTimeDataNew can't get deviceData projectId: $projectId" }
                    mapOf()
                }
            }
        } catch (e: Exception) {
            log.error { "realTimeData projectId:$projectId url:$url error :${e.message} " }
//            if (e.message!!.contains("WsAgent busy")) {
//                // 兼容一下 可能盘古的old 接口也在调用 导致 busy 如果这里抛错 外面放入badRequestCache中 导致不及时更新
//                return null
//            } else {
            throw ServiceException(
                400, "badRequest"
            )
//            }
        }
    }


    /**
     * get_online 接口的调用
     */
    fun getOnline(projectId: String): GoOnlineResponse? {
        val goApi = goApiUtils.getGoApi(projectId)
        val url = "$goApi$GET_ONLINE_URL"
        return try {
            restTemplateUtils.sendRequestNew(
                url, HttpMethod.POST, null, mapOf(), object : ParameterizedTypeReference<GoOnlineResponse>() {}, true
            ) ?: run {
                log.error { "getOnline projectId:$projectId url:$url  can`t get response" }
                throw ServiceException(
                    500, "链接失败"
                )
            }
        } catch (e: Exception) {
            log.error { "getOnline projectId:$projectId url:$url error :${e.message} " }
            throw ServiceException(
                500, "链接失败"
            )
        }
    }

}