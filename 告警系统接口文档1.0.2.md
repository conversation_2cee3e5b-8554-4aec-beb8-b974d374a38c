# 告警系统API接口文档

**文档版本:** v1.0.2  
**更新日期:** 2025-08-12  
**作者:** jiahao.jin

## 简介

本文档描述了告警系统的API接口，用于与告警系统进行交互。告警系统提供了告警发送、告警类型管理、告警规则管理、静默规则管理以及抑制规则管理等功能。

## 注意：

-  不加id是新增，加了id是更新，更新参数要全量传入
-  告警规则的通知人使用 notificationContacts ：

## 基础信息

- 测试服基础URL: `http://wh-alert-manager.dev.weiheng-tech.com`

- 认证方式: 请求头认证
    - `W-sign`: 通过`AuthUtils.generateAuthHeader`生成（名称+时间戳）
    - `Content-Type`: application/json
    
    ```java
    public class AuthUtils {
    
        /**
         * 生成认证头
         *
         * @param appName 应用名称
         * @return Base64编码的认证字符串
         */
        public static String generateAuthHeader(String appName) {
            if (appName == null || appName.trim().isEmpty()) {
                throw new IllegalArgumentException("应用名称不能为空");
            }
    
            // 将冒号替换为短横线
            String name = appName.replaceAll(":", "-");
    
            // 获取当前时间戳（秒）
            long timestamp = new Date().getTime() / 1000;
    
            // 构建签名字符串
            String sign = name + timestamp;
    
            // 生成MD5哈希
            String md5 = getMd5(sign);
    
            // 构建认证字符串：name:timestamp:md5_substring
            String auth = name + ":" + timestamp + ":" + md5.substring(10, 20);
    
            // 返回Base64编码结果
            return Base64.getUrlEncoder().encodeToString(auth.getBytes());
        }
    
        /**
         * 生成字符串的MD5哈希值
         *
         * @param input 输入字符串
         * @return 32位小写MD5哈希字符串
         */
        private static String getMd5(String input) {
            try {
                MessageDigest md = MessageDigest.getInstance("MD5");
                byte[] hashBytes = md.digest(input.getBytes());
    
                StringBuilder sb = new StringBuilder();
                for (byte b : hashBytes) {
                    sb.append(String.format("%02x", b));
                }
                return sb.toString();
    
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException("MD5算法不可用", e);
            }
        }
    
        /**
         * 验证认证头是否有效
         *
         * @param authHeader Base64编码的认证头
         * @param appName 原始应用名称
         * @param timeToleranceSeconds 时间容忍度（秒）
         * @return 是否验证通过
         */
        public static boolean validateAuthHeader(String authHeader, String appName, long timeToleranceSeconds) {
            try {
                // 解码Base64
                String decoded = new String(Base64.getUrlDecoder().decode(authHeader));
                String[] parts = decoded.split(":");
    
                if (parts.length != 3) {
                    return false;
                }
    
                String name = parts[0];
                long timestamp = Long.parseLong(parts[1]);
                String providedMd5Part = parts[2];
    
                // 验证应用名称
                String expectedName = appName.replaceAll(":", "-");
                if (!expectedName.equals(name)) {
                    return false;
                }
    
                // 验证时间戳是否在容忍范围内
                long currentTimestamp = new Date().getTime() / 1000;
                if (Math.abs(currentTimestamp - timestamp) > timeToleranceSeconds) {
                    return false;
                }
    
                // 验证MD5签名
                String sign = name + timestamp;
                String expectedMd5 = getMd5(sign);
                String expectedMd5Part = expectedMd5.substring(10, 20);
    
                return expectedMd5Part.equals(providedMd5Part);
    
            } catch (Exception e) {
                return false;
            }
        }
    
        /**
         * 解析认证头信息
         *
         * @param authHeader Base64编码的认证头
         * @return 认证信息对象，解析失败返回null
         */
        public static AuthInfo parseAuthHeader(String authHeader) {
            try {
                String decoded = new String(Base64.getUrlDecoder().decode(authHeader));
                String[] parts = decoded.split(":");
    
                if (parts.length != 3) {
                    return null;
                }
    
                return new AuthInfo(parts[0], Long.parseLong(parts[1]), parts[2]);
    
            } catch (Exception e) {
                return null;
            }
        }
    
        /**
         * 认证信息数据类
         */
        public static class AuthInfo {
            private final String appName;
            private final long timestamp;
            private final String md5Part;
    
            public AuthInfo(String appName, long timestamp, String md5Part) {
                this.appName = appName;
                this.timestamp = timestamp;
                this.md5Part = md5Part;
            }
    
            public String getAppName() {
                return appName;
            }
    
            public long getTimestamp() {
                return timestamp;
            }
    
            public String getMd5Part() {
                return md5Part;
            }
    
            public Date getTimestampAsDate() {
                return new Date(timestamp * 1000);
            }
    
            @Override
            public String toString() {
                return "AuthInfo{" +
                        "appName='" + appName + '\'' +
                        ", timestamp=" + timestamp +
                        ", md5Part='" + md5Part + '\'' +
                        '}';
            }
        }
    
        public static void main(String[] args) {
            String appName = "Pangu";
            String authHeader = generateAuthHeader(appName);
            System.out.println("Generated Auth Header: " + authHeader);
        }
    }
    ```

## 数据模型

### 通用响应结构

所有API响应统一使用以下格式：

```json
{
  "code": 200,         // HTTP状态码
  "msg": "OK",         // 状态消息
  "data": {...}        // 响应数据，根据接口不同而变化
}
```

### 基础数据类型

#### BaseAlert（告警基础信息）

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| status | String | 告警状态，可选值：`firing`（触发中）或`resolved`（已解决） |
| labels | Map<String, String> | 告警标签，用于标识告警属性 |
| annotations | Map<String, String> | 告警注解，用于提供额外信息 |
| startsAt | String | 告警开始时间，ISO8601格式 |
| endsAt | String | 告警结束时间，ISO8601格式 |
| generatorURL | String | 告警来源URL |

#### AttributeMatchRule（属性匹配规则）

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| key | String | 属性键名 |
| compare | Compare枚举 | 比较操作符，可选值：eq（等于）、gt（大于）、lt（小于）等 |
| value | String | 比较值 |
| operator | Operator枚举 | 逻辑操作符，可选值：and（与）、or（或） |

## API列表

### 1. 发送告警

#### 注意：
- 发送告警前，请在监控告警平台手动新增告警来源分组或者使用接口新增来源分组，否则将无法自动创建告警类型

#### 接口地址

```
POST /alertserver/alert/project/alert-manager
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述               |
| ------ | ---- | ---- |------------------|
| source | String | 是 | 告警来源             |
| subGroup | String | 是 | 告警子分组，默认：default |
| alerts | List\<BaseAlert\> | 是 | 告警列表             |

##### BaseAlert对象结构

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ------ | ---- | ---- | ---- | ---- |
| status | String | 是 | 告警状态，firing(触发中)或resolved(已解决) | firing |
| labels | Map\<String, String\> | 是 | 标签集合，用于标识告警的属性（可以添加更多定制化参数，用于规则和模版配置） |  |
| annotations | Map\<String, String\> | 否 | 注解集合，用于提供告警的额外信息 |  |
| startsAt | String | 否 | 告警开始时间 |  |
| endsAt | String | 否 | 告警结束时间 |  |
| generatorURL | String | 否 | 告警源URL |  |

#### 请求示例

```json
{
  "source": "Pangu", // 必填：告警主分组
  "subGroup": "test-1", // 必填：告警子分组
  "alerts": [
    {
      "status": "firing", // 必填：告警状态
      "labels": {
        "projectName": "盘古服务", // 选填
        "alertname": "场站离线", // 必填：告警名称（若此告警类型会，作为告警类型名称）
        "monitor": "sh_dev", // 选填
        "level": "warning", // 必填：告警等级，(”emergency“、“critical”、“warning”)
        "value": "10086号场站" // 选填
      },
      "annotations": {
        "description": "盘古服务-10086号场站-场站离线" // 选填
      },
      "startsAt": "2025-08-11T08:00:00Z", // 必填，告警发生时间
      "endsAt": "2025-08-11T08:00:00Z" // 选填：告警结束时间
    }
  ]
}
```

#### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": null
}
```

### 2. 告警类型分组管理

#### 2.1 批量创建或更新告警类型分组

##### 接口地址

```
POST /alertserver/alert-type-group/batch
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| request | List\<AlertTypeGroupDTO\> | 告警类型分组列表 |

##### AlertTypeGroupDTO对象结构

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 否 | 分组ID，更新时必填 |
| name | String | 是 | 分组名称 |
| subGroupName | String | 是 | 子分组名称 |
| showCollect | boolean | 否 | 是否在汇聚中显示 |

##### 请求示例

```json
[
  {
    "name": "Pangu",
    "subGroupName": "test-1"
  },
  {
    "name": "Pangu",
    "subGroupName": "test-2"
  }
]
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": [
    {
      "id": "6801c87c9da8150cfaae1e58",
      "createTime": 1744947324614,
      "updateTime": 1744947324614,
      "creatorId": null,
      "modifier": null,
      "deleteFlag": 0,
      "sortFields": null,
      "name": "Pangu",
      "subGroupName": "test-1",
      "showCollect": false,
      "alertNum": 0,
      "preAlertNum": 0
    },
    {
      "id": "6801c87c9da8150cfaae1e59",
      "createTime": 1744947324614,
      "updateTime": 1744947324614,
      "creatorId": null,
      "modifier": null,
      "deleteFlag": 0,
      "sortFields": null,
      "name": "Pangu",
      "subGroupName": "test-2",
      "showCollect": false,
      "alertNum": 0,
      "preAlertNum": 0
    }
  ]
}
```

#### 2.2 批量删除告警类型子分组

##### 接口地址

```
DELETE /alertserver/alert-type-group/deleteAlertTypeSubGroups
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| groupName | String | 告警类型分组名称 |
| subGroupNames | List<String> | 告警类型子分组名称列表 |

##### 请求示例

```json
groupName=Pangu&subGroupNames=test-11,test-12,test-13
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": null
}
```

#### 2.3 查询所有告警类型分组

##### 接口地址

```
GET /alertserver/alert-type-group/findAlertSourceAndSubGroupTree
```

##### 请求参数

| 参数名    | 类型   | 描述             | 必填 |
| --------- | ------ | ---------------- | ---- |
| groupName | String | 告警类型分组名称 | 否   |

##### 请求示例

```json
groupName=Pangu
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": [
    {
      "name": "Pangu",
      "subGroups": [
        {
          "subGroupName": "default",
          "groupId": "6800c0298a127210e2e2992f",
          "alertTypeList": []
        },
        {
          "subGroupName": "test-1",
          "groupId": "6801c87c9da8150cfaae1e58",
          "alertTypeList": []
        },
        {
          "subGroupName": "test-2",
          "groupId": "6801c87c9da8150cfaae1e59",
          "alertTypeList": []
        }
      ]
    }
  ]
}
```

### 3. 告警类型管理

#### 3.1 批量创建或更新告警类型

##### 接口地址

```
POST /alertserver/alert-type/batch
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| request | List\<AlertTypeDTO\> | 告警类型列表 |

##### AlertTypeDTO对象结构

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 否 | 类型ID，更新时必填 |
| groupId | String | 是 | 所属分组ID |
| name | String | 是 | 类型名称 |
| chineseName | String | 是 | 中文名称 |
| source | String | 是 | 告警来源 |
| subSource | String | 是 | 告警来源子名称 |
| sourceType | String | 否 | 源告警信息类型 |
| props | List\<Prop\> | 否 | 属性列表（对应推送告警的labels） |
| templates | String | 否 | 模板 |
| description | String | 否 | 描述 |
| showCollect | boolean | 否 | 是否在汇聚中显示 |

##### Prop对象结构

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| name | String | 是 | 属性名称 |
| chineseName | String | 是 | 属性中文名称 |
| dataType | String | 是 | 数据类型 |

##### 请求示例

```json
[
  {
    "name": "场站测试0812", // 必填：告警类型名称
    "chineseName": "场站测试0812", // 必填：告警类型中文名称
    "source": "Pangu", // 必填：告警来源
    "description": "描述2", // 非必填
    "templates": "", // 非必填：告警模版
    "groupId": "6801c87c9da8150cfaae1e58", // 必填：告警子分组id
    "props": [ // 非必填：属性列表
      {
        "name": "level",
        "chineseName": "level",
        "dataType": "string"
      }
    ]
  }
]
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": [
    {
      "id": "689af642522aa2162698c7c7",
      "createTime": "2025-08-12T16:07:30.263+08:00",
      "updateTime": "2025-08-12T16:56:59.908+08:00",
      "creatorId": 1476812293437263872,
      "modifier": 1476812293437263872,
      "deleteFlag": 0,
      "sortFields": null,
      "group": null,
      "groupId": "6801c87c9da8150cfaae1e58",
      "name": "场站测试0812",
      "chineseName": "场站测试0812",
      "source": "Pangu",
      "subSource": "test-1",
      "sourceType": null,
      "props": [
        {
          "name": "level",
          "chineseName": "level",
          "dataType": "string"
        }
      ],
      "templates": "",
      "description": "描述3",
      "showCollect": false
    }
  ]
}
```

#### 3.2 根据名称删除告警类型

##### 接口地址

```
DELETE /alertserver/alert-type/deleteByName
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| alertTypeName | String | 告警类型名称 |
| sourceName | String | 告警来源名称 |
| subGroupName | String | 告警子分组名称 |

##### 请求示例

```json
alertTypeName=%E5%9C%BA%E7%AB%99%E6%B5%8B%E8%AF%950812&sourceName=Pangu&subGroupName=test-1
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": null
}
```

#### 3.3 根据名称查询告警类型

##### 接口地址

```
GET /alertserver/alert-type/findByName
```

##### 请求参数

| 参数名        | 类型   | 描述           |
| ------------- | ------ | -------------- |
| alertTypeName | String | 告警类型名称   |
| sourceName    | String | 告警来源名称   |
| subGroupName  | String | 告警子分组名称 |

##### 请求示例

```
alertTypeName=%E5%9C%BA%E7%AB%99%E6%B5%8B%E8%AF%950812&sourceName=Pangu&subGroupName=test-1
```

##### 响应示例

```json
{
    "code": 200,
    "msg": "OK",
    "data": {
        "id": "689af642522aa2162698c7c7",
        "createTime": "2025-08-12T16:07:30.263+08:00",
        "updateTime": "2025-08-12T16:56:59.908+08:00",
        "creatorId": 1476812293437263872,
        "modifier": 1476812293437263872,
        "deleteFlag": 0,
        "sortFields": null,
        "group": null,
        "groupId": "6801c87c9da8150cfaae1e58",
        "name": "场站测试0812",
        "chineseName": "场站测试0812",
        "source": "Pangu",
        "subSource": "test-1",
        "sourceType": null,
        "props": [
            {
                "name": "level",
                "chineseName": "level",
                "dataType": "string"
            }
        ],
        "templates": "",
        "description": "描述3",
        "showCollect": false
    }
}
```

### 4. 告警规则管理

#### 4.1 批量创建或更新告警规则

##### 接口地址

```
POST /alertserver/alert-rule/batch
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| request | List\<AlertRuleDTO\> | 告警规则列表 |

##### AlertRuleDTO对象结构

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 否 | 规则ID，更新时必填 |
| name | String | 是 | 规则名称 |
| typeIds | List\<String\> | 是 | 关联的告警类型ID列表 |
| priority | int | 是 | 优先级 |
| area | List\<String\> | 否 | 包含的告警区域 |
| notificationContacts | List\<NotificationContact\> | 否 | 通知联系人 |
| extraAlert | List\<Hook\> | 否 | 额外告警的钉钉群 |
| escalation | List\<Escalation\> | 否 | 升级策略 |
| hasEscalation | boolean | 否 | 是否包含升级策略 |
| attrFirstRules | List\<AttributeMatchFirstRule\> | 否 | 标签匹配规则 |
| alerting | boolean | 否 | 是否告警 |
| showCollect | boolean | 否 | 是否在汇聚中显示 |
| template | String | 否 | 描述模板 |
| expires | Long | 否 | 规则过期时间 |
| description | String | 否 | 描述说明 |
| usageStatus | Boolean | 是 | 使用状态 |
| recoverNotify | Boolean | 否 | 是否恢复通知 |
| recoverTemplate | String | 否 | 恢复告警模板 |

##### 请求示例

```json
[
  {
    "name": "场站测试08-12-1", // 必填：告警规则名称（建议使用系统名+场站名+规则名来取名）
    "priority": 2, // 必填：告警优先级
    "typeIds": [ // 必填：告警类型id
      "6899ad42c215ee0ba1761cbf"
    ],
    "attrFirstRules": [ // 必填：告警规则
      {
        "attrRules": [
          {
            "compare": "eq",
            "key": "alertname",
            "operator": "and",
            "value": "场站离线"
          }
        ],
        "operator": "or"
      }
    ],
    "recoverNotify": false, // 必填：是否恢复告警
    "recoverTemplate": "## 恢复告警模版  \n**名称**: ${name!\"未知名称\"}\n**区域**: ${area!\"未知区域\"}\n**时间**: ${startTime!\"未知时间\"}\n**描述**:  \n<#if (description?default([]))?is_sequence><#list description as d>> ${d}  \n</#list><#else>> ${description!\"无描述\"}  \n</#if>", // 非必填：告警恢复模版，如果不使用则使用告警类型模版，或者默认模版，建议填写
    "template": "## 告警模版  \n**名称**: ${name!\"未知名称\"}\n**区域**: ${area!\"未知区域\"}\n**时间**: ${startTime!\"未知时间\"}\n**描述**:  \n<#if (description?default([]))?is_sequence><#list description as d>> ${d}  \n</#list><#else>> ${description!\"无描述\"}  \n</#if>", // 非必填：告警模版
    "description": "", // 非必填：描述
    "extraAlert": [], // 非必填：告警系统的人员维护参数
    "expires": 1756631460000, // 必填：告警规则过期时间戳
    "hasEscalation": false,  // 必填：是否升级告警
    "escalation": [], // 非必填：当升级告警时填写
    "notificationContacts": [  // 必填：通知人员相关信息
      {
        "name": "金佳豪",
        "contactChannels": [
          {
            "type": "EMAIL", // 必填：通知类型：“EMAIL”、“SMS”、“PHONE_CALL”
            "name": "金佳豪",
            "email": "<EMAIL>" // 必填：通知类型为：“EMAIL”时填写
            "phoneNumber": "18888888888" // 必填：通知类型为：“SMS”、“PHONE_CALL”时填写
          }
        ]
      }
    ],
    "usageStatus": true // 必填：是否启用
  }
]
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": [
    {
      "id": "689b05e561e5f91dc9acf803",
      "createTime": "2025-08-12T17:14:13.963+08:00",
      "updateTime": "2025-08-12T17:14:13.963+08:00",
      "creatorId": 1476812293437263872,
      "modifier": null,
      "deleteFlag": 0,
      "sortFields": null,
      "type": null,
      "typeName": null,
      "groupNames": null,
      "typeIds": [
        "6899ad42c215ee0ba1761cbf"
      ],
      "name": "场站测试08-12-2",
      "area": null,
      "priority": 2,
      "extraAlert": [],
      "notificationContacts": [
        {
          "id": null,
          "name": "金佳豪",
          "description": null,
          "contactChannels": [
            {
              "type": "EMAIL",
              "id": null,
              "name": "金佳豪",
              "enabled": true,
              "config": null,
              "email": "<EMAIL>"
            }
          ]
        }
      ],
      "escalation": [],
      "hasEscalation": false,
      "attrFirstRules": [
        {
          "attrRules": [
            {
              "key": "alertname",
              "compare": "eq",
              "operator": "and",
              "value": "场站离线"
            }
          ],
          "operator": "or"
        }
      ],
      "showCollect": false,
      "template": "## 告警模版  \n**名称**: ${name!\"未知名称\"}\n**区域**: ${area!\"未知区域\"}\n**时间**: ${startTime!\"未知时间\"}\n**描述**:  \n<#if (description?default([]))?is_sequence><#list description as d>> ${d}  \n</#list><#else>> ${description!\"无描述\"}  \n</#if>",
      "expires": 1756631460000,
      "description": "",
      "usageStatus": true,
      "recoverNotify": false,
      "recoverTemplate": "## 恢复告警模版  \n**名称**: ${name!\"未知名称\"}\n**区域**: ${area!\"未知区域\"}\n**时间**: ${startTime!\"未知时间\"}\n**描述**:  \n<#if (description?default([]))?is_sequence><#list description as d>> ${d}  \n</#list><#else>> ${description!\"无描述\"}  \n</#if>"
    }
  ]
}
```

#### 4.2 根据告警规则名称删除告警规则

##### 接口地址

```
DELETE /alertserver/alert-rule/deleteByName
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| name | String | 告警规则名称 |

##### 请求示例

```json
name=%E5%9C%BA%E7%AB%99%E6%B5%8B%E8%AF%9508-12-2
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": null
}
```

#### 4.3 根据告警规则名称查询告警规则

##### 接口地址

```
GET /alertserver/alert-rule/findByName
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| name | String | 告警规则名称 |

##### 请求示例

```
name=%E5%9C%BA%E7%AB%99%E6%B5%8B%E8%AF%9508-12-2
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": {
    "id": "689b05e561e5f91dc9acf803",
    "createTime": "2025-08-12T17:14:13.963+08:00",
    "updateTime": "2025-08-12T17:26:57.965+08:00",
    "creatorId": 1476812293437263872,
    "modifier": 1476812293437263872,
    "deleteFlag": 0,
    "sortFields": null,
    "type": null,
    "typeName": null,
    "groupNames": null,
    "typeIds": [
      "6899ad42c215ee0ba1761cbf"
    ],
    "name": "场站测试08-12-2",
    "area": null,
    "priority": 3,
    "extraAlert": [],
    "notificationContacts": [
      {
        "id": null,
        "name": "金佳豪",
        "description": null,
        "contactChannels": [
          {
            "type": "EMAIL",
            "id": null,
            "name": "金佳豪",
            "enabled": true,
            "config": null,
            "email": "<EMAIL>"
          }
        ]
      }
    ],
    "escalation": [],
    "hasEscalation": false,
    "attrFirstRules": [
      {
        "attrRules": [
          {
            "key": "alertname",
            "compare": "eq",
            "operator": "and",
            "value": "场站离线"
          }
        ],
        "operator": "or"
      }
    ],
    "showCollect": false,
    "template": "## 告警模版  \n**名称**: ${name!\"未知名称\"}\n**区域**: ${area!\"未知区域\"}\n**时间**: ${startTime!\"未知时间\"}\n**描述**:  \n<#if (description?default([]))?is_sequence><#list description as d>> ${d}  \n</#list><#else>> ${description!\"无描述\"}  \n</#if>",
    "expires": 1756631460000,
    "description": "",
    "usageStatus": true,
    "recoverNotify": false,
    "recoverTemplate": "## 恢复告警模版  \n**名称**: ${name!\"未知名称\"}\n**区域**: ${area!\"未知区域\"}\n**时间**: ${startTime!\"未知时间\"}\n**描述**:  \n<#if (description?default([]))?is_sequence><#list description as d>> ${d}  \n</#list><#else>> ${description!\"无描述\"}  \n</#if>"
  }
}
```

### 5. 静默规则管理

#### 5.1 批量创建或更新静默规则

##### 接口地址

```
POST /alertserver/silence-rule/batch
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| request | List\<SilenceRuleDTO\> | 静默规则列表 |

##### SilenceRuleDTO对象结构

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 否 | 规则ID，更新时必填 |
| name | String | 是 | 规则名称 |
| typeIds | List\<String\> | 是 | 关联的告警类型ID列表 |
| effectiveType | EffectiveType | 是 | 有效期类型：持续生效、循环生效、自定义时间段生效 |
| customTimePeriods | List<CustomTimePeriod> | 否 | 自定义时间段列表（当effectiveType为CUSTOM时使用） |
| recurringTimeConfig | RecurringTimeConfig | 否 | 循环时间配置（当effectiveType为RECURRING时使用） |
| silenceRules | List\<AttributeMatchFirstRule\> | 是 | 抑制字段 |
| usageStatus | Boolean | 是 | 使用状态 |

##### 请求示例

```json
[
  {
    "name": "场站测试08-12-3", // 必填：静默规则名称
    "typeIds": [ // 必填：关联告警类型id
      "6899ad42c215ee0ba1761cbf"
    ],
    "effectiveType": "CONTINUOUS",  // 必填：有效期类型：CONTINUOUS-持续生效、RECURRING-循环生效、CUSTOM-自定义时间段生效
    "usageStatus": true, // 必填：是否生效
    "silenceRules": [ // 必填：匹配规则
      {
        "attrRules": [
          {
            "compare": "eq",  // 必填：比较符号（gte、gt、eq、lte、lt、in、notIn、all、notEq、isEmpty、notEmpty）
            "key": "alertname", // 必填：字段名
            "operator": "and", // 必填：or 或者 and
            "value": "场站离线2" // 必填：字段值
          }
        ],
        "operator": "or" // 必填：or 或 and
      }
    ]
  }
]

// 自定义时间段生效示例
[
  {
    "name": "场站测试08-12-3",
    "typeIds": [
      "6899ad42c215ee0ba1761cbf"
    ],
    "effectiveType": "CUSTOM",
    "customTimePeriods": [
          {
            "startTime": "2024-12-01T02:00:00",
            "endTime": "2024-12-01T06:00:00"
          },
          {
            "startTime": "2024-12-15T01:30:00",
            "endTime": "2024-12-15T05:30:00"
          },
          {
            "startTime": "2025-01-01T00:00:00",
            "endTime": "2025-01-01T08:00:00"
          }
        ],
    "usageStatus": true,
    "silenceRules": [
      {
        "attrRules": [
          {
            "compare": "eq",
            "key": "alertname",
            "operator": "and",
            "value": "场站离线2"
          }
        ],
        "operator": "or"
      }
    ]
  }
]

// 每日循环生效示例
[
  {
    "name": "场站测试08-12-3",
    "typeIds": [
      "6899ad42c215ee0ba1761cbf"
    ],
    "effectiveType": "RECURRING",
    "recurringTimeConfig": {
          "recurringType": "DAILY",
          "dailyTimePeriods": [
            {
              "startTime": "02:00:00",
              "endTime": "04:00:00"
            },
            {
              "startTime": "23:30:00",
              "endTime": "23:59:59"
            }
          ],
          "weeklyTimePeriods": []
        },
    "usageStatus": true,
    "silenceRules": [
      {
        "attrRules": [
          {
            "compare": "eq",
            "key": "alertname",
            "operator": "and",
            "value": "场站离线2"
          }
        ],
        "operator": "or"
      }
    ]
  }
]
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": [
    {
      "id": "689b0a9e522aa2162698c7d1",
      "createTime": "2025-08-12T17:34:22.442+08:00",
      "updateTime": "2025-08-12T17:48:12.876+08:00",
      "creatorId": 1476812293437263872,
      "modifier": 1476812293437263872,
      "deleteFlag": 0,
      "sortFields": null,
      "name": "场站测试08-12-3",
      "type": null,
      "typeIds": [
        "6899ad42c215ee0ba1761cbf"
      ],
      "typeList": null,
      "effectiveType": "CONTINUOUS",
      "customTimePeriods": [],
      "recurringTimeConfig": {
        "recurringType": "DAILY",
        "dailyTimePeriods": [],
        "weeklyTimePeriods": []
      },
      "timeZone": "Asia/Shanghai",
      "silenceRules": [
        {
          "attrRules": [
            {
              "key": "alertname",
              "compare": "eq",
              "operator": "and",
              "value": "场站离线2"
            }
          ],
          "operator": "or"
        }
      ],
      "usageStatus": true
    }
  ]
}
```

#### 5.2 根据静默规则名称删除静默规则

##### 接口地址

```
DELETE /alertserver/silence-rule/deleteByName
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| name | String | 静默规则名称 |

##### 请求示例

```json
name=%E5%9C%BA%E7%AB%99%E6%B5%8B%E8%AF%9508-12-3
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": null
}
```

#### 5.3 根据静默规则名称查询静默规则

##### 接口地址

```
GET /alertserver/silence-rule/findByName
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| name | String | 静默规则名称 |

##### 请求示例

```
name=%E5%9C%BA%E7%AB%99%E6%B5%8B%E8%AF%9508-12-3
```

##### 响应示例

```json
{
    "code": 200,
    "msg": "OK",
    "data": {
        "id": "689b0a9e522aa2162698c7d1",
        "createTime": "2025-08-12T17:34:22.442+08:00",
        "updateTime": "2025-08-12T17:48:12.876+08:00",
        "creatorId": 1476812293437263872,
        "modifier": 1476812293437263872,
        "deleteFlag": 0,
        "sortFields": null,
        "name": "场站测试08-12-3",
        "type": null,
        "typeIds": [
            "6899ad42c215ee0ba1761cbf"
        ],
        "typeList": null,
        "effectiveType": "CONTINUOUS",
        "customTimePeriods": [],
        "recurringTimeConfig": {
            "recurringType": "DAILY",
            "dailyTimePeriods": [],
            "weeklyTimePeriods": []
        },
        "timeZone": "Asia/Shanghai",
        "silenceRules": [
            {
                "attrRules": [
                    {
                        "key": "alertname",
                        "compare": "eq",
                        "operator": "and",
                        "value": "场站离线2"
                    }
                ],
                "operator": "or"
            }
        ],
        "usageStatus": true
    }
}
```

### 6. 抑制规则管理

#### 6.1 批量创建或更新抑制规则

##### 接口地址

```
POST /alertserver/restraint-rule/batch
```

##### 请求参数

| 参数名 | 类型 | 描述     |
| ------ | ---- |--------|
| request | List\<RestrainRuleDTO\> | 抑制规则列表 |

##### RestrainRuleDTO对象结构

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 否 | 规则ID，更新时必填 |
| name | String | 是 | 规则名称 |
| alertName | String | 否 | 告警名称 |
| typeIds | List\<String\> | 是 | 关联的告警类型ID列表 |
| effectiveType       | EffectiveType                   | 是   | 有效期类型：持续生效、循环生效、自定义时间段生效  |
| customTimePeriods | List<CustomTimePeriod> | 否 | 自定义时间段列表（当effectiveType为CUSTOM时使用） |
| recurringTimeConfig | RecurringTimeConfig | 否 | 循环时间配置（当effectiveType为RECURRING时使用） |
| fields | List\<String\> | 否 | 分组字段 |
| compType | CompareTypeEnum | 是 | 比较类型 |
| compFields | String | 否 | 比较字段 |
| fluctuation | Integer | 否 | 波动幅度 |
| thresholdTimes | long | 否 | 阈值告警次数 |
| time | int | 否 | 缓冲时间 |
| usageStatus | Boolean | 是 | 使用状态 |
| restrainRules | List\<AttributeMatchFirstRule\> | 是 | 字段匹配 |

##### 请求示例

```json
// 阈值告警次数
[
  {
    "name": "测试场站08-12-4",
    "typeIds": [
      "6899ad42c215ee0ba1761cbf"
    ],
    "compType": "times", // 必填：比较类型（趋势：trend、按次数：times、按持续时间：duration）
    "thresholdTimes": 5, // 必填：阈值告警次数
    "time": 9, // 必填：缓冲时间
    "effectiveType": "CONTINUOUS",
    "usageStatus": true,
    "restrainRules": [
      {
        "attrRules": [
          {
            "compare": "eq",
            "key": "alertname",
            "operator": "and",
            "value": "场站离线"
          }
        ],
        "operator": "or"
      }
    ]
  }
]

// 波动幅度
[
  {
    "name": "测试场站08-12-4",
    "typeIds": [
      "6899ad42c215ee0ba1761cbf"
    ],
  "compType": "trend",  // 必填：比较类型（趋势：trend、按次数：times、按持续时间：duration）
  "compFields": "value", // 必填：比较字段
  "fluctuation": 10, // 波动幅度
    "time": 9, // 必填：缓冲时间
    "effectiveType": "CONTINUOUS",
    "usageStatus": true,
    "restrainRules": [
      {
        "attrRules": [
          {
            "compare": "eq",
            "key": "alertname",
            "operator": "and",
            "value": "场站离线"
          }
        ],
        "operator": "or"
      }
    ]
  }
]

// 缓冲时间   "compType": "duration",
[
  {
    "name": "测试场站08-12-4",
    "typeIds": [
      "6899ad42c215ee0ba1761cbf"
    ],
  "compType": "duration",  // 必填：比较类型（趋势：trend、按次数：times、按持续时间：duration）
    "time": 9, // 必填：缓冲时间
    "effectiveType": "CONTINUOUS",
    "usageStatus": true,
    "restrainRules": [
      {
        "attrRules": [
          {
            "compare": "eq",
            "key": "alertname",
            "operator": "and",
            "value": "场站离线"
          }
        ],
        "operator": "or"
      }
    ]
  }
]
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": [
    {
      "id": "689b1457522aa2162698c7d4",
      "createTime": "2025-08-12T18:15:51.946+08:00",
      "updateTime": "2025-08-12T18:19:09.382+08:00",
      "creatorId": 1476812293437263872,
      "modifier": 1476812293437263872,
      "deleteFlag": 0,
      "sortFields": null,
      "name": "测试场站08-12-4",
      "alertName": null,
      "type": null,
      "typeIds": [
        "6899ad42c215ee0ba1761cbf"
      ],
      "typeList": null,
      "effectiveType": "CONTINUOUS",
      "customTimePeriods": [],
      "recurringTimeConfig": {
        "recurringType": "DAILY",
        "dailyTimePeriods": [],
        "weeklyTimePeriods": []
      },
      "timeZone": "Asia/Shanghai",
      "compType": "times",
      "compFields": null,
      "fluctuation": null,
      "thresholdTimes": 5,
      "time": 9,
      "usageStatus": true,
      "restrainRules": [
        {
          "attrRules": [
            {
              "key": "alertname",
              "compare": "eq",
              "operator": "and",
              "value": "场站离线"
            }
          ],
          "operator": "or"
        }
      ]
    }
  ]
}
```

#### 6.2 根据抑制规则名称删除抑制规则

##### 接口地址

```
DELETE /alertserver/restraint-rule/deleteByName
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| name | String | 抑制规则名称 |

##### 请求示例

```json
name=%E6%B5%8B%E8%AF%95%E5%9C%BA%E7%AB%9908-12-4
```

##### 响应示例

```json
{
  "code": 200,
  "msg": "OK",
  "data": null
}
```

#### 6.3 根据名称查询抑制规则

##### 接口地址

```
GET /alertserver/restraint-rule/findByName
```

##### 请求参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| name | String | 抑制规则名称 |

##### 请求示例

```json
name=%E6%B5%8B%E8%AF%95%E5%9C%BA%E7%AB%9908-12-4
```

##### 响应示例

```json
{
    "code": 200,
    "msg": "OK",
    "data": {
        "id": "689b1457522aa2162698c7d4",
        "createTime": "2025-08-12T18:15:51.946+08:00",
        "updateTime": "2025-08-12T18:30:59.925+08:00",
        "creatorId": 1476812293437264000,
        "modifier": 1476812293437263872,
        "deleteFlag": 0,
        "sortFields": null,
        "name": "测试场站08-12-4",
        "alertName": null,
        "type": null,
        "typeIds": [
            "6899ad42c215ee0ba1761cbf"
        ],
        "typeList": null,
        "effectiveType": "CONTINUOUS",
        "customTimePeriods": [],
        "recurringTimeConfig": {
            "recurringType": "DAILY",
            "dailyTimePeriods": [],
            "weeklyTimePeriods": []
        },
        "timeZone": "Asia/Shanghai",
        "compType": "duration",
        "compFields": null,
        "fluctuation": null,
        "thresholdTimes": 0,
        "time": 10,
        "usageStatus": true,
        "restrainRules": [
            {
                "attrRules": [
                    {
                        "key": "alertname",
                        "compare": "eq",
                        "operator": "and",
                        "value": "场站离线"
                    }
                ],
                "operator": "or"
            }
        ]
    }
}
```

## 错误码说明

| 状态码 | 说明 |
| ------ | ---- |
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 错误处理

告警系统API客户端通过Fallback机制处理服务不可用的情况。所有接口在服务不可用时都会抛出RuntimeException异常，并包含对应的错误信息。


## Java 客户端使用说明

### Maven依赖

```xml
<dependency>
    <groupId>com.weihengtech.alert</groupId>
    <artifactId>alert-api-client</artifactId>
    <version>1.0.2-SNAPSHOT</version>
</dependency>
```

### gradle
```kotlin
implementation("com.weihengtech.alert:alert-api-client:1.0.2-SNAPSHOT")
```

### 导入配置

首先，需要在Spring Boot应用的主类上添加 `@EnableFeignClients` 注解，或确保项目的配置类中已经包含该注解。如果已存在AlertFeignConfig类，则无需额外配置。

```java
package com.weihengtech.test;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(
        basePackages = {"com.weihengtech.alert", "com.weihengtech.test"}
)
public class AlertTestApplication {

  public static void main(String[] args) {
    SpringApplication.run(AlertTestApplication.class, args);
  }

}
```

### 配置项

在application.yml或application.properties中配置以下参数：

```properties
alert.api.base-url=http://127.0.0.1:8080
alert.api.app-name=Pangu

# 可选的Feign客户端配置
feign.okhttp.readTimeout: 15                             # 读取超时时间（秒）
feign.okhttp.connectTimeout: 10                          # 连接超时时间（秒）
feign.okhttp.writeTimeout: 15                            # 写入超时时间（秒）
feign.pool.maxConnections: 200                         # 最大连接数
feign.pool.keepAliveDuration: 5                        # 连接存活时间（分钟）
```

### 框架依赖说明

告警API客户端基于Spring Cloud OpenFeign实现，在Maven项目中需要确保以下依赖已添加：

```kotlin
plugins {
  java
  id("org.springframework.boot") version "3.4.4"
  id("io.spring.dependency-management") version "1.1.7"
}

apply {
  from("auth.gradle")  // 注意路径，如果在根目录，不需要../
}

group = "com.weihengtech"
version = "0.0.1-SNAPSHOT"

java {
  toolchain {
    languageVersion = JavaLanguageVersion.of(17)
  }
}


// 定义仓库
repositories {
  maven {
    credentials {
      username = project.ext.properties["repo_username"].toString()
      password = project.ext.properties["repo_password"].toString()
    }
    isAllowInsecureProtocol = true
    url = uri("http://nexus.dev.weiheng-tech.com/repository/maven-snapshots/")
  }
  maven {
    credentials {
      username = project.ext.properties["repo_username"].toString()
      password = project.ext.properties["repo_password"].toString()
    }
    isAllowInsecureProtocol = true
    url = uri("http://nexus.dev.weiheng-tech.com/repository/maven-releases/")
  }
  maven { url = uri("https://maven.aliyun.com/repository/public/") }
  mavenLocal()
  mavenCentral()
}


dependencies {
  implementation("org.springframework.boot:spring-boot-starter")
  implementation("org.springframework.cloud:spring-cloud-starter-openfeign:4.2.1")
  implementation("com.weihengtech.alert:alert-api-client:1.0-SNAPSHOT")

  // Lombok
  compileOnly("org.projectlombok:lombok:1.18.24")
  annotationProcessor("org.projectlombok:lombok:1.18.24")
}

```

auth.gradle
```kotlin
ext {
    repo_username = ""
    repo_password = ""
}
```