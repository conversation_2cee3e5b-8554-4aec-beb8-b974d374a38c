package com.wifochina.modules.carbon.service.impl;

import static java.util.Comparator.comparing;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.InfluxTimeCalcEnum;
import com.wifochina.modules.carbon.VO.Carbon;
import com.wifochina.modules.carbon.VO.CarbonTotal;
import com.wifochina.modules.carbon.request.CarbonRequest;
import com.wifochina.modules.carbon.service.CarbonService;
import com.wifochina.modules.client.InfluxClient;
import com.wifochina.modules.data.entity.MeterContentData;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupAmmeterEntity;
import com.wifochina.modules.group.entity.GroupDeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupAmmeterService;
import com.wifochina.modules.group.service.GroupDeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Todo 待删除的类
 *
 * <AUTHOR>
 * @since 4/14/2022 11:25 AM
 */
// @Service
@Slf4j
@RequiredArgsConstructor
@Deprecated
public class InfluxCarbonServiceImpl implements CarbonService {

    private final AmmeterService ammeterService;

    private final DeviceService deviceService;

    private final GroupService groupService;

    private final GroupAmmeterService groupAmmeterService;

    private final GroupDeviceService groupDeviceService;

    private final ProjectService projectService;

    private final DataService dataService;

    private final InfluxClient influxClient;
    
    private final PointListHolder pointListHolder;

    @Value("${carbon.load.emission}")
    private double carbonLoadEmission;

    @Value("${carbon.pv.reduction}")
    private double carbonPvReduction;

    @Value("${carbon.pv.power}")
    private double carbonPvPower;

    @Value("${carbon.ems.reduction}")
    private double carbonEmsReduction;

    @Value("${carbon.ems.power}")
    private double carbonEmsPower;

    /**
     * 能量
     *
     * @param type type代表实时。daily 日报；month 月报；年报year
     * @param reportRequest request
     * @return 总收益情况
     */
    @Override
    public Map<String, Object> getCarbon(CarbonRequest reportRequest, String type) {

        List<Carbon> carbons = new ArrayList<>();

        double totalPv = 0d;
        double totalWind = 0d;
        double totalEmsOut = 0d;
        double totalGridOut = 0d;
        Map<Long, Double> pvMap;
        Map<Long, Double> gridOutMap;
        Map<Long, Double> emsInMap;
        Map<Long, Double> emsOutMap;
        Map<Long, Double> windMap;
        Map<Long, Double> dcdcMap;
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        long start = reportRequest.getStartDate();
        long end = reportRequest.getEndDate() + 1;
        String timeZoneCode = projectEntity.getTimezone();
        if (Objects.equals(type, EmsConstants.DAILY)) {
            start = start - ChronoUnit.HOURS.getDuration().getSeconds();
        } else if (Objects.equals(type, EmsConstants.MONTH)) {
            start = start - ChronoUnit.DAYS.getDuration().getSeconds();
        } else if (Objects.equals(type, EmsConstants.YEAR)) {
            start = start - 31 * ChronoUnit.DAYS.getDuration().getSeconds();
        }
        pvMap =
                getPower(
                        start,
                        end,
                        timeZoneCode,
                        influxClient.getMeterTable(projectId),
                        "ac_history_positive_power_in_kwh",
                        InfluxTimeCalcEnum.getTimeFromType(type),
                        "1");

        gridOutMap =
                getPower(
                        start,
                        end,
                        timeZoneCode,
                        influxClient.getMeterTable(projectId),
                        "ac_history_negative_power_in_kwh",
                        InfluxTimeCalcEnum.getTimeFromType(type),
                        "2");
        windMap =
                getPower(
                        start,
                        end,
                        timeZoneCode,
                        influxClient.getMeterTable(projectId),
                        "ac_history_positive_power_in_kwh",
                        InfluxTimeCalcEnum.getTimeFromType(type),
                        "4");
        emsInMap =
                getPower(
                        start,
                        end,
                        timeZoneCode,
                        influxClient.getEmsTable(projectId),
                        "ems_history_input_energy",
                        InfluxTimeCalcEnum.getTimeFromType(type),
                        null);
        emsOutMap =
                getPower(
                        start,
                        end,
                        timeZoneCode,
                        influxClient.getEmsTable(projectId),
                        "ems_history_output_energy",
                        InfluxTimeCalcEnum.getTimeFromType(type),
                        null);

        dcdcMap =
                getPower(
                        start,
                        end,
                        timeZoneCode,
                        influxClient.getEmsTable(projectId),
                        "dcdc_meter_history_energy_pos",
                        InfluxTimeCalcEnum.getTimeFromType(type),
                        null);
        // EMS净出 电池放电电量 - 电池充电电量
        // 电网净出 电网取电 - 馈网电量
        // 总消耗 = 电网馈 + pv发电 +EMS放 - ems充- grid馈
        Set<Long> keySet = null;
        assert emsOutMap != null;
        Optional<Map<Long, Double>> nonEmptyMap =
                Optional.of(emsOutMap)
                        .filter(e -> !e.isEmpty())
                        .or(() -> Optional.ofNullable(emsInMap).filter(e -> !e.isEmpty()))
                        .or(() -> Optional.ofNullable(gridOutMap).filter(e -> !e.isEmpty()))
                        .or(() -> Optional.ofNullable(windMap).filter(e -> !e.isEmpty()))
                        .or(() -> Optional.ofNullable(dcdcMap));
        if (nonEmptyMap.isPresent()) {
            keySet = nonEmptyMap.get().keySet();
        }
        if (keySet != null) {
            long todayZero = MyTimeUtil.getTodayZeroTime(timeZoneCode);
            for (Long time : keySet) {
                Carbon carbon = new Carbon();
                carbon.setTime(time);
                double emsOut = 0;
                if (!emsOutMap.isEmpty()) {
                    emsOut = emsOutMap.get(time);
                    if (time >= todayZero) {
                        totalEmsOut += emsOutMap.get(time);
                    }
                }
                double pv = 0;
                if (!pvMap.isEmpty()) {
                    pv = pvMap.get(time);
                    if (time >= todayZero) {
                        totalPv += pv;
                    }
                }
                double dcdcPower = 0;
                if (!dcdcMap.isEmpty() && time != null) {
                    dcdcPower = dcdcMap.get(time) == null ? 0 : dcdcMap.get(time);
                    // 把dcdc的pv 添加到 总光伏上
                    if (time >= todayZero) {
                        totalPv += dcdcPower;
                    }
                    // 把dcdc的pv 添加到 光伏上, 后期可能会拆开
                    pv += dcdcPower;
                }
                double gridOut = 0;
                if (!gridOutMap.isEmpty()) {
                    gridOut = gridOutMap.get(time);
                    if (time >= todayZero) {
                        totalGridOut += gridOut;
                    }
                }
                double wind = 0;
                if (!windMap.isEmpty()) {
                    wind = windMap.get(time) == null ? 0 : windMap.get(time);
                    if (time >= todayZero) {
                        totalWind += wind;
                    }
                }
                // 碳排放量：负载系数（0.6）
                // 碳减排量：储能放电量0.35+PV放电量*0.997
                carbon.setCarbonEmission(gridOut * carbonLoadEmission);
                carbon.setCarbonReduction(
                        emsOut * carbonEmsReduction + (pv + wind) * carbonPvReduction);
                carbon.setTime(time);
                carbons.add(carbon);
            }
        }
        carbons.sort(comparing(Carbon::getTime));
        Map<String, Object> map = new HashMap<>(6);
        map.put("carbons", carbons);
        if (Objects.equals(type, EmsConstants.DAILY)) {
            map.put("daily_emission", totalGridOut * carbonLoadEmission);
            map.put(
                    "daily_reduction",
                    totalEmsOut * carbonEmsReduction + (totalWind + totalPv) * carbonPvReduction);
            CarbonTotal carbonTotal = getSite();
            map.put("total_reduction", carbonTotal.getCarbonReduction());
            map.put("total_emission", carbonTotal.getCarbonEmission());
            map.put("reduction_power", carbonTotal.getCarbonReductionPower());
        }
        return map;
    }

    Map<Long, Double> getPower(
            long start,
            long end,
            String timeZoneCode,
            String tableName,
            String column,
            String period,
            String type) {
        String condition = "";
        if (type != null) {
            condition = "and r.system == \"1\" and r.type ==\"" + type + "\"";
        }
        String queryString =
                "import \"timezone\"\n"
                        + "option location = timezone.fixed(offset: "
                        + MyTimeUtil.getOffsetSecondsFromZoneCode(timeZoneCode)
                        + "s)\n"
                        + "       from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "        |> range(start: {start}, stop: {end} )\n"
                        + "        |> filter(fn: (r) => r._measurement == \"{tableName}\")\n"
                        + "        |> filter(fn: (r) => r.projectId == \""
                        + WebUtils.projectId.get()
                        + "\")\n"
                        + "        |> filter(fn: (r) => r[\"_field\"] == \"{column}\" {condition} )\n"
                        + "        |> aggregateWindow(every: {period}, fn: max)\n"
                        + "        |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n";
        if (EmsConstants.PERIOD_1_MO.equals(period)) {
            queryString = queryString + "        |> fill(column: \"{column}\", value: 0.0)\n";
        } else {
            queryString =
                    queryString + "        |> fill(column: \"{column}\", usePrevious: true)\n";
        }
        queryString =
                queryString
                        + "        |> difference(columns:[\"{column}\"], nonNegative: true)\n"
                        + "        |> fill(column: \"{column}\", value: 0.0)\n"
                        + "        |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "        |> sum(column:\"{column}\")\n"
                        + "        |> timeShift(duration: -1s, columns: [\"_start\", \"_stop\", \"_time\"])\n";
        // 将query进行兑换
        queryString =
                queryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        queryString = queryString.replace("{column}", column);
        queryString = queryString.replace("{condition}", condition);
        queryString = queryString.replace("{tableName}", tableName);
        queryString = queryString.replace("{period}", period);
        log.debug(queryString);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        Map<Long, Double> map = new HashMap<>();
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                Double value = (Double) fluxRecord.getValueByKey(column);
                Instant time = (Instant) fluxRecord.getValueByKey("_time");
                assert time != null;
                map.put(time.getEpochSecond(), Objects.requireNonNullElse(value, 0d));
            }
        }
        return map;
    }

    public CarbonTotal getSite() {
        List<AmmeterEntity> ammeterEntities =
                ammeterService.list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get()));
        GroupEntity systemGroupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                                .eq(GroupEntity::getWhetherSystem, true));
        List<GroupAmmeterEntity> groupAmmeterEntities =
                groupAmmeterService.list(
                        Wrappers.lambdaQuery(GroupAmmeterEntity.class)
                                .eq(GroupAmmeterEntity::getGroupId, systemGroupEntity.getId()));
        List<String> sysAmmeterIds =
                groupAmmeterEntities.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        List<GroupDeviceEntity> groupDeviceEntities =
                groupDeviceService.list(
                        Wrappers.lambdaQuery(GroupDeviceEntity.class)
                                .eq(GroupDeviceEntity::getGroupId, systemGroupEntity.getId()));
        List<DeviceEntity> unRealDeviceEntities =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, WebUtils.projectId.get())
                        .eq(DeviceEntity::getUnreal, true)
                        .list();
        List<String> unRealDeviceIds =
                unRealDeviceEntities.stream().map(DeviceEntity::getId).collect(Collectors.toList());
        List<String> systemDeviceIds =
                groupDeviceEntities.stream()
                        .map(GroupDeviceEntity::getDeviceId)
                        .collect(Collectors.toList());
        List<String> sysDeviceIds =
                systemDeviceIds.stream()
                        .filter(item -> !unRealDeviceIds.contains(item))
                        .collect(Collectors.toList());
        double pvPower = 0;
        double pvTotal = 0;
        double gridOutTotal = 0;
        double windPower = 0;
        double windTotal = 0;
        for (AmmeterEntity ammeterEntity : ammeterEntities) {
            if (!sysAmmeterIds.contains(ammeterEntity.getId())) {
                continue;
            }
            MeterContentData meterContentData =
                    dataService
                            .collectMeterDataPoint(WebUtils.projectId.get())
                            .get(ammeterEntity.getId());
            if (meterContentData != null) {
                if (MeterTypeEnum.PV.meterType().equals(ammeterEntity.getType())) {
                    // Pv发电
                    pvTotal += meterContentData.getAc_history_positive_power_in_kwh();
                    pvPower += meterContentData.getAc_active_power();
                } else if (MeterTypeEnum.GRID.meterType().equals(ammeterEntity.getType())) {
                    // 电网取点
                    gridOutTotal += meterContentData.getAc_history_negative_power_in_kwh();
                } else if (MeterTypeEnum.WIND.meterType().equals(ammeterEntity.getType())) {
                    windTotal += meterContentData.getAc_history_positive_power_in_kwh();
                    windPower += meterContentData.getAc_active_power();
                }
            }
        }
        double emsOutTotal = 0;
        double emsOutPower = 0;
        double dcdcOutTotal = 0;
        double dcdcOutPower = 0;
        List<DeviceEntity> deviceEntities =
                deviceService.list(
                        Wrappers.lambdaQuery(DeviceEntity.class)
                                .eq(DeviceEntity::getProjectId, WebUtils.projectId.get()));
        for (DeviceEntity deviceEntity : deviceEntities) {
            if (!sysDeviceIds.contains(deviceEntity.getId())) {
                continue;
            }
            int[] data = dataService.get(deviceEntity.getId());
            if (data == null) {
                continue;
            }
            int emsType = data[42];
            int highOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType)];
            int lowOut = data[pointListHolder.getEmsHistoryOutputEnergy(emsType) + 1];
            long out = ((long) highOut << 16) + lowOut;
            emsOutTotal += (out * 1.0 / 10);
            double point =
                    ((double) (short) data[pointListHolder.getEmsAcActivePower(emsType)]) / 10;
            if (point > 0) {
                // 放电
                emsOutPower += point;
            }
            // 2024-01-17 14:20:17 add dcdc
            // 这个要去累计到 pv电量上
            int high = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType)];
            int low = data[pointListHolder.getDcdcMeterHistoryEnergyPos(emsType) + 1];
            dcdcOutTotal += (high << 16) + low;
            dcdcOutTotal = Double.parseDouble(String.format("%.2f", dcdcOutTotal / 10));
            dcdcOutPower += (short) data[pointListHolder.getDcdcMeterPower(emsType)];
            dcdcOutPower = Double.parseDouble(String.format("%.2f", dcdcOutPower / 10));
        }
        // 把dcdc的添加到 pv上
        // 电量
        pvTotal += dcdcOutTotal;
        // 功率
        pvPower += dcdcOutPower;
        /*
             碳排放量：日用电量（从电网）*区域排放因子
             碳减排量： PV发电量*（区域排放因子-0.05）+储能放电量*0.2*（区域排放因子*0.5）
             碳减排强度（kg/h）：PV功率（kW）*（区域排放因子-0.05）+储能放电功率（kW）0.2*（区域排放因子*0.5）
             碳排放量：电网（0.6435）
             碳减排量：储能放电量0.06435+PV放电量*0.5935
             碳减排强度（kg/h）：PV实时功率（kW）*0.5935+储能放电功率（kW）*0.06435
        */
        CarbonTotal carbonTotal = new CarbonTotal();
        carbonTotal.setCarbonEmission(gridOutTotal * carbonLoadEmission);
        carbonTotal.setCarbonReduction(
                emsOutTotal * carbonEmsReduction + (pvTotal + windTotal) * carbonPvReduction);
        carbonTotal.setCarbonReductionPower(
                (pvPower + windPower) * carbonPvPower / 1000 + emsOutPower * carbonEmsPower);
        return carbonTotal;
    }
}
