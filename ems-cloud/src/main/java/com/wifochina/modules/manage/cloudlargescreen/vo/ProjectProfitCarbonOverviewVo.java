package com.wifochina.modules.manage.cloudlargescreen.vo;

import com.wifochina.modules.carbon.VO.CarbonTotal;
import com.wifochina.modules.operation.VO.TotalProfitVO;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2023/10/12 16:35.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProjectProfitCarbonOverviewVo {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "总收益")
    private TotalProfitVO totalProfitVO;

    @ApiModelProperty(value = "碳减排")
    private CarbonTotal carbonTotal;

    @ApiModelProperty(value = "项目币种")
    private String currency;
}
