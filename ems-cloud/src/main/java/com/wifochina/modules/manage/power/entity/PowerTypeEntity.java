package com.wifochina.modules.manage.power.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@TableName("t_power_type")
@ApiModel(value = "PowerTypeEntity对象")
public class PowerTypeEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "uuid", type = IdType.ASSIGN_UUID)
    private String uuid;

    private String powerType;

    @ApiModelProperty(value = "用电类型英文")
    private String powerTypeEn;

}
