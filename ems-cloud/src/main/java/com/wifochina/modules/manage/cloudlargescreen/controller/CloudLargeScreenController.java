package com.wifochina.modules.manage.cloudlargescreen.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.income.timer.TaskOperationTimer;
import com.wifochina.modules.manage.cloudlargescreen.request.CloudLargeScreenProjectSearch;
import com.wifochina.modules.manage.cloudlargescreen.request.CloudLargeScreenSearch;
import com.wifochina.modules.manage.cloudlargescreen.service.CloudLargeScreenService;
import com.wifochina.modules.manage.cloudlargescreen.vo.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Created on 2023/10/11 10:30. 云端 大屏 controller
 *
 * <AUTHOR>
 */
@Api(tags = "28-云端大屏接口")
@RestController
@RequestMapping("/cloudLargeScreen")
@RequiredArgsConstructor
@Slf4j
public class CloudLargeScreenController {

    private final CloudLargeScreenService cloudLargeScreenService;

    /** 查询项目的状态总览接口 */
    @PostMapping("/projectStatus")
    @ApiOperation("查询项目的状态总览接口")
    @PreAuthorize("hasAuthority('/cloudlargeScreen')")
    public Result<ProjectStatusOverviewVo> projectStatus(
            @RequestBody CloudLargeScreenSearch search) {
        ProjectStatusOverviewVo result = cloudLargeScreenService.projectStatus(search);
        return Result.success(result);
    }

    /**
     * 查询所有项目的收益和碳减排数据总览接口
     *
     * @param timePointKey : 时间点的key
     * @return : List<ProjectProfitCarbonOverviewVo>
     */
    @PostMapping("/projectProfitCarbon/{timePoint}")
    @ApiOperation("查询所有项目的收益和碳减排数据总览接口")
    @PreAuthorize("hasAuthority('/cloudlargeScreen')")
    public Result<List<ProjectProfitCarbonOverviewVo>> projectProfitCarbon(
            @RequestBody CloudLargeScreenSearch search,
            @PathVariable(value = "timePoint", required = false) String timePointKey) {
        List<ProjectProfitCarbonOverviewVo> results =
                cloudLargeScreenService.projectProfitCarbon(search, timePointKey);
        return Result.success(results);
    }

    @PostMapping("/projectChargeOneRate")
    @ApiOperation("查询所有项目的充放电功率曲线(传递当日的时间, 可选的项目是开启云大平的项目)")
    @PreAuthorize("hasAuthority('/cloudlargeScreen')")
    public Result<Map<String, List<ValueVO>>> projectChargeOneRate(
            @RequestBody RequestWithDeviceId requestWithDeviceId)
            throws CloneNotSupportedException {
        Map<String, List<ValueVO>> chargeRateMap =
                cloudLargeScreenService.projectChargeOneRate(requestWithDeviceId);
        return Result.success(chargeRateMap);
    }

    /**
     * 近7日的 所有项目的充放电 电量收益等信息
     *
     * @return: list
     */
    @PostMapping("/projectRecentWeekCharge")
    @ApiOperation("查询所有项目的近7日的充放电量概况")
    public Result<List<ProjectRecentWeekChargeOverviewVo>> projectRecentWeekCharge(
            @RequestBody CloudLargeScreenProjectSearch search) {
        List<ProjectRecentWeekChargeOverviewVo> result =
                cloudLargeScreenService.projectRecentWeekCharge(search);
        return Result.success(result);
    }

    /** 查询所有项目的 日收益,月收益和总计收益 + 总计碳减排 */
    @PostMapping("/projectAllProfitCarbon")
    @ApiOperation("查询所有项目的日收益,月收益和总计收益 + 总计碳减排")
    public Result<ProjectAllProfitCarbonOverviewVo> projectAllProfitCarbon(
            @RequestBody CloudLargeScreenSearch search) {
        ProjectAllProfitCarbonOverviewVo result =
                cloudLargeScreenService.projectAllProfitCarbon(search);
        return Result.success(result);
    }

    @PostMapping("/projectAllElectric")
    @ApiOperation("(欧洲/澳洲数据中心)查询所有项目的日冲放,月冲放和总计冲放 + 总计碳减排")
    public Result<ProjectAllElectricOverviewVo> projectAllElectric(
            @RequestBody CloudLargeScreenSearch search) {
        ProjectAllElectricOverviewVo result = cloudLargeScreenService.projectAllElectric(search);
        return Result.success(result);
    }

    @PostMapping("/projectAllCapacity")
    @ApiOperation("查询所有项目的容量(装机,功率,电站数等..)")
    public Result<ProjectCapacityOverviewVo> projectAllCapacity(
            @RequestBody CloudLargeScreenSearch search) {
        ProjectCapacityOverviewVo result = cloudLargeScreenService.projectAllCapacity(search);
        return Result.success(result);
    }

    @PostMapping("/testCloud/{key}")
    @ApiOperation("查询所有项目的容量(装机,功率,电站数等..)")
    public Result<Void> testCloud(@PathVariable("key") String searchKey) {
        if (searchKey.equals("electric")) {
            TaskOperationTimer.monthElectricMap.forEach(
                    (key, value) ->
                            log.info(
                                    "projectId:{} , month Electric totalBenefit :{}",
                                    key,
                                    value.getTotalBenefit()));
            log.error("--------------------------------------------------------------");
            TaskOperationTimer.totalElectricMap.forEach(
                    (key, value) ->
                            log.info(
                                    "projectId:{} , total Electric totalBenefit :{}",
                                    key,
                                    value.getTotalBenefit()));
        }
        if (searchKey.equals("pv")) {
            TaskOperationTimer.monthPvMap.forEach(
                    (key, value) ->
                            log.info(
                                    "projectId:{} , pv month getTotal_benefit_extend :{}, getTotal_discharge_benefit : {} ",
                                    key,
                                    value.getTotal_benefit_extend(),
                                    value.getTotal_discharge_benefit()));
            log.error("--------------------------------------------------------------");
            TaskOperationTimer.totalPvMap.forEach(
                    (key, value) ->
                            log.info(
                                    "projectId:{} , pv total getTotal_benefit_extend :{}, getTotal_discharge_benefit : {} ",
                                    key,
                                    value.getTotal_benefit_extend(),
                                    value.getTotal_discharge_benefit()));
        }
        if (searchKey.equals("wind")) {
            TaskOperationTimer.monthWindMap.forEach(
                    (key, value) ->
                            log.info(
                                    "projectId:{} , wind month getTotal_benefit_extend :{}, getTotal_discharge_benefit : {} ",
                                    key,
                                    value.getTotal_benefit_extend(),
                                    value.getTotal_discharge_benefit()));
            log.error("--------------------------------------------------------------");
            TaskOperationTimer.totalWindMap.forEach(
                    (key, value) ->
                            log.info(
                                    "projectId:{} , wind total getTotal_benefit_extend :{}, getTotal_discharge_benefit : {} ",
                                    key,
                                    value.getTotal_benefit_extend(),
                                    value.getTotal_discharge_benefit()));
        }
        return Result.success();
    }
}
