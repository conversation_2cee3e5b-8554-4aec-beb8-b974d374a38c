package com.wifochina.modules.manage.cloudlargescreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2023/10/13 16:52.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProjectRecentWeekChargeOverviewVo {

    @ApiModelProperty(value = "所有项目的总充电电量KWH")
    private String totalChargeQuantity = "0.0";

    @ApiModelProperty(value = "所有项目的总放电电量")
    private String totalDischargeQuantity = "0.0";

}
