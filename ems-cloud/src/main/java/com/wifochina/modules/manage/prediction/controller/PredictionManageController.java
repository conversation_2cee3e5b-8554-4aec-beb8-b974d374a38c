package com.wifochina.modules.manage.prediction.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.page.Result;
import com.wifochina.modules.prediction.entity.PredictionConfigEntity;
import com.wifochina.modules.prediction.entity.PredictionManageEntity;
import com.wifochina.modules.prediction.request.PredictionManageRequest;
import com.wifochina.modules.prediction.service.PredictionConfigService;
import com.wifochina.modules.prediction.service.PredictionManageService;
import com.wifochina.modules.prediction.vo.PredictionManageVo;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-06-25
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "manage-07-预测设置")
@RequestMapping("/manage/prediction")
public class PredictionManageController {

    private final PredictionManageService predictionManageService;

    private final PredictionConfigService predictionConfigService;

    private final ProjectService projectService;

    @PostMapping("/addConfig")
    @ApiOperation("增加项目预测配置")
    @PreAuthorize("hasAuthority('/manage/prediction/addConfig')")
    public Result<Object> addConfig(@RequestBody PredictionManageRequest predictionManageRequest) {
        PredictionManageEntity predictionManageEntity = predictionManageService.lambdaQuery()
            .eq(PredictionManageEntity::getProjectId, predictionManageRequest.getProjectId())
            .eq(PredictionManageEntity::getType, predictionManageRequest.getType()).one();
        Optional<PredictionManageEntity> optionalPredictionManage = Optional.ofNullable(predictionManageEntity);
        optionalPredictionManage.ifPresentOrElse(e -> {
            throw new ServiceException(ErrorResultCode.PREDICTION_CONFIG_EXISTS.value());
        }, () -> {
            PredictionManageEntity predictionManageEntityTemp = new PredictionManageEntity();
            BeanUtils.copyProperties(predictionManageRequest, predictionManageEntityTemp);
            predictionManageService.save(predictionManageEntityTemp);
        });
        return Result.success();
    }

    @PostMapping("/deleteConfig")
    @ApiOperation("删除项目预测配置")
    @PreAuthorize("hasAuthority('/manage/prediction/deleteConfig')")
    public Result<Object> deleteConfig(@RequestBody PredictionManageRequest predictionManageRequest) {
        predictionManageService.remove(Wrappers.lambdaQuery(PredictionManageEntity.class)
            .eq(PredictionManageEntity::getProjectId, predictionManageRequest.getProjectId())
            .eq(PredictionManageEntity::getType, predictionManageRequest.getType()));
        predictionConfigService.remove(Wrappers.lambdaQuery(PredictionConfigEntity.class)
            .eq(PredictionConfigEntity::getProjectId, predictionManageRequest.getProjectId())
            .eq(PredictionConfigEntity::getType, predictionManageRequest.getType()));
        return Result.success();
    }

    @PostMapping("/queryConfig")
    @ApiOperation("查询项目预测配置")
    @PreAuthorize("hasAuthority('/manage/prediction/queryConfig')")
    public Result<IPage<PredictionManageEntity>> query(@RequestBody PageBean pageBean) {
        Map<String, String> projectMap = projectService.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list()
            .stream().collect(Collectors.toMap(ProjectEntity::getId, ProjectEntity::getProjectName));
        IPage<PredictionManageEntity> predictionManagePage = new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        IPage<PredictionManageEntity> page = predictionManageService.page(predictionManagePage);
        List<PredictionManageVo> list = new ArrayList<>();
        page.getRecords().forEach(e -> {
            PredictionManageVo predictionManageVo = new PredictionManageVo();
            BeanUtils.copyProperties(e, predictionManageVo);
            predictionManageVo.setProjectName(projectMap.get(predictionManageVo.getProjectId()));
            list.add(predictionManageVo);
        });
        page.getRecords().clear();
        page.getRecords().addAll(list);
        return Result.success(page);
    }
}
