package com.wifochina.modules.manage.project.controller;

import cn.hutool.core.collection.CollectionUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.config.AccountSystemConfiguration;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.StrategyTypeEnum;
import com.wifochina.common.constants.TimePointEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.page.Result;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.HashUtil;
import com.wifochina.common.util.RestElectricPriceSystemUtils;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.configuration.enums.DataCenterEnum;
import com.wifochina.modules.configuration.service.ConfigurationService;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.income.job.CustomTimezoneQuartzService;
import com.wifochina.modules.initelectric.InitElectricService;
import com.wifochina.modules.investor.entity.InvestorEntity;
import com.wifochina.modules.investor.service.InvestorProjectService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.ProjectManageLogDetailService;
import com.wifochina.modules.monitor.service.MonitorService;
import com.wifochina.modules.oauth.AuthUser;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.operation.ManageProjectOperation;
import com.wifochina.modules.operation.job.OperationMonthReportJob;
import com.wifochina.modules.operation.job.OperationYearReportJob;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.operation.service.impl.OperationMonthQuartzServiceImpl;
import com.wifochina.modules.operation.service.impl.OperationYearQuartzServiceImpl;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.entity.ProjectUrlEntity;
import com.wifochina.modules.project.entity.ProjectUserRemarkEntity;
import com.wifochina.modules.project.request.ProjectManageListRequest;
import com.wifochina.modules.project.request.ProjectManageRequest;
import com.wifochina.modules.project.request.ProjectUserRequest;
import com.wifochina.modules.project.service.*;
import com.wifochina.modules.project.vo.ProjectManageVo;
import com.wifochina.modules.remedies.timer.service.CheckDataQuartzService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;
import com.wifochina.modules.strategytemplate.request.StrategyTemplateCopyRequest;
import com.wifochina.modules.strategytemplate.service.StrategyTemplateService;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.entity.UserProjectEntity;
import com.wifochina.modules.user.service.LogService;
import com.wifochina.modules.user.service.UserProjectService;
import com.wifochina.modules.user.service.UserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Slf4j
@RequestMapping("/manage/project")
@RestController
@Api(tags = "manage-01-项目管理")
@RequiredArgsConstructor
public class ManageProjectController {

    private final ProjectService projectService;

    private final GroupService groupService;

    private final StrategyService strategyService;
    private final StrategyTemplateService strategyTemplateService;

    private final UserProjectService userProjectService;

    private final ProjectExtService projectExtService;

    private final UserService userService;

    private final ProjectUrlService projectUrlService;

    private final RestTemplate restTemplate;

    private final DeviceService deviceService;

    private final AmmeterService ammeterService;

    private final EventMessageService eventMessageService;

    private final MonitorService monitorService;

    private final OperationMonthQuartzServiceImpl operationMonthQuartzServiceImpl;
    private final OperationYearQuartzServiceImpl operationYearQuartzServiceImpl;
    private final CheckDataQuartzService checkDataQuartzService;

    private final List<CustomTimezoneQuartzService> customTimezoneQuartzServiceList;

    private final ProjectUserRemarkService projectUserRemarkService;

    private final InvestorProjectService investorProjectService;

    private final ElectricPriceService electricPriceService;

    private final ProjectServiceKt projectServiceKt;

    private final ConfigurationService configurationService;

    private final InitElectricService initElectricService;

    private final AccountSystemConfiguration accountSystemConfiguration;

    @Value("${ems.version}")
    private String version;

    @Value("${local.refresh}")
    private String refreshUrl;

    @GetMapping("/getRegionsIntervalData")
    @ApiOperation("manage-根据国家查询实时电价系统支持的区域和间隔")
    // @PreAuthorize("hasAuthority('/manage/project/create')")
    public Result<RestElectricPriceSystemUtils.RegionsAndIntervalData> getRegionsIntervalData(
            @RequestParam("countryId") Integer countryId) {
        RestElectricPriceSystemUtils.RegionsAndIntervalData regionsAndIntervalData =
                electricPriceService.getRegionsAndIntervalData(countryId);
        return Result.success(regionsAndIntervalData);
    }

    /** init project */
    @PostMapping("/create")
    @ApiOperation("manage-项目创建")
    @Log(module = "MANAGE_PROJECT", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/manage/project/create')")
    public Result<Object> create(@RequestBody ProjectManageRequest projectRequest) {
        if (projectRequest.getPriceProxy() != null) {
            if (!projectRequest.getPriceProxy()) {
                // 校验一下 实时电价需要的参数
                checkDynamicPriceParmas(projectRequest);
            } else {
                checkProxyDynamicPriceParams(projectRequest);
            }
        }
        // 创建项目
        ProjectEntity projectEntity = new ProjectEntity();
        BeanUtils.copyProperties(projectRequest, projectEntity);
        String uuid = StringUtil.uuid();
        projectServiceKt.createProject(
                uuid, projectRequest.getProjectName(), projectRequest.getCountry());
        projectEntity.setId(uuid);
        projectEntity.setAlarm(0);
        projectEntity.setFault(0);
        projectEntity.setStatus(0);
        projectEntity.setCreateTime(MyTimeUtil.getTodayZeroTime(projectRequest.getTimezone()));
        projectEntity.setHash(HashUtil.elfHash(uuid));
        projectService.save(projectEntity);
        // 创建系统分组
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setWhetherSystem(true);
        groupEntity.setName(
                DataCenterEnum.isCN(accountSystemConfiguration.dataCenter)
                        ? EmsConstants.SYSTEM_GROUP_NAME
                        : EmsConstants.SYSTEM_GROUP_NAME_EN);
        groupEntity.setProjectId(projectEntity.getId());
        groupEntity.setCapacityController(false);
        groupService.save(groupEntity);
        // 创建系统分组策略  2025-04-28 13:45:49 check strategy
        StrategyEntity strategyEntity = new StrategyEntity();
        strategyEntity.setProjectId(projectEntity.getId());
        strategyEntity.setGroupId(groupEntity.getId());
        strategyEntity.setWeekDay(0);
        strategyEntity.setSoc(EmsConstants.SOC_DEFAULT);
        strategyEntity.setStrategyType(
                StrategyTypeEnum.getStrategyType(projectEntity.getElectricPriceType()));
        strategyService.save(strategyEntity);
        // 创建系统扩展属性
        ProjectExtEntity projectExtEntity = new ProjectExtEntity();
        BeanUtils.copyProperties(projectRequest, projectExtEntity);
        projectExtEntity.setId(projectEntity.getId());
        projectExtEntity.setPredictionPath(projectRequest.getAlias());
        projectExtService.save(projectExtEntity);
        if (projectRequest.getProjectModel() != 0
                && Boolean.TRUE.equals(projectRequest.getWhetherAccessible())) {
            return generateForward();
        }
        Optional.ofNullable(projectRequest.getTimezone())
                .ifPresent(
                        timezone -> {
                            operationYearQuartzServiceImpl.addOperationJob(
                                    timezone, OperationYearReportJob.class);
                            operationMonthQuartzServiceImpl.addOperationJob(
                                    timezone, OperationMonthReportJob.class);
                            checkDataQuartzService.addJob(timezone);
                            for (CustomTimezoneQuartzService customTimezoneQuartzService :
                                    customTimezoneQuartzServiceList) {
                                customTimezoneQuartzService.addJob(timezone);
                            }
                        });
        Optional.ofNullable(projectRequest.getInvestorIds())
                .ifPresent(
                        e ->
                                investorProjectService.saveInvestorForProject(
                                        e, projectEntity.getId()));
        // 默认创建整站、单机模式两个项目配置
        configurationService.initProjectConfiguration(
                DataCenterEnum.isCN(accountSystemConfiguration.dataCenter), uuid);

        // 1.4.4 support strategyTemplate import
        Long strategyTemplateId = projectRequest.getStrategyTemplateId();
        if (strategyTemplateId != null && strategyTemplateId != 0L) {
            strategyTemplateService.copyTemplate(
                    new StrategyTemplateCopyRequest(
                            strategyTemplateId, null, projectEntity.getId(), null, null));
        }
        return Result.success();
    }

    private void checkProxyDynamicPriceParams(ProjectManageRequest projectRequest) {
        // 如果是托管的 电价 则 需要有电价区域配置
        if (StringUtil.isEmpty(projectRequest.getArea())) {
            throw new ServiceException(ErrorResultCode.DYNAMIC_PRICE_NEED_PARAMS.value());
        }
    }

    private static void checkDynamicPriceParmas(ProjectManageRequest projectRequest) {
        if (ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD
                .name()
                .equals(projectRequest.getElectricPriceType())) {
            // 如果是实时电价 需要配置 电价跨度和电价区域
            String electricPriceArea = projectRequest.getElectricPriceArea();
            String electricPriceSpan = projectRequest.getElectricPriceSpan();
            Integer countryId = projectRequest.getCountry();
            if (StringUtil.isEmpty(electricPriceArea)
                    || StringUtil.isEmpty(electricPriceSpan)
                    || countryId == null) {
                throw new ServiceException(ErrorResultCode.DYNAMIC_PRICE_NEED_PARAMS.value());
            }
        }
    }

    /** init project */
    @PostMapping("/update")
    @ApiOperation("manage-项目更新")
    @Transactional(rollbackFor = Exception.class)
    @Log(module = "MANAGE_PROJECT", type = OperationType.UPDATE)
    @PreAuthorize("hasAuthority('/manage/project/update')")
    public Result<Object> update(@RequestBody ProjectManageRequest projectRequest) {
        // 创建项目
        ProjectEntity projectEntity = new ProjectEntity();
        ProjectEntity oldProjectEntity = projectService.getById(projectRequest.getId());
        if (!oldProjectEntity.getProjectName().equals(projectRequest.getProjectName())
                || !Objects.equals(oldProjectEntity.getCountry(), projectRequest.getCountry())) {
            // 更新项目名称或者国家
            projectServiceKt.updateProject(
                    projectRequest.getId(),
                    oldProjectEntity.getProjectName().equals(projectRequest.getProjectName())
                            ? null
                            : projectRequest.getProjectName(),
                    projectRequest.getCountry() == null ? null : projectRequest.getCountry());
        }

        BeanUtils.copyProperties(projectRequest, projectEntity);
        projectService.updateById(projectEntity);
        // 创建系统扩展属性
        ProjectExtEntity projectExtEntity = new ProjectExtEntity();
        BeanUtils.copyProperties(projectRequest, projectExtEntity);
        Optional.ofNullable(projectRequest.getAlias())
                .ifPresent(projectExtEntity::setPredictionPath);

        AuthUser authUser = SecurityUtil.getPrincipal();
        assert authUser != null;
        String role = authUser.getUserEntity().getRole();
        // 2代表内部用户
        boolean unitRemarkFlag = "2".equals(role);
        // 备注处理
        if (!unitRemarkFlag) {
            // 如果不是 统一的备注权限操作
            ProjectUserRemarkEntity projectUserRemarkEntity =
                    projectUserRemarkService.getOne(
                            new LambdaQueryWrapper<ProjectUserRemarkEntity>()
                                    .eq(
                                            ProjectUserRemarkEntity::getUserId,
                                            authUser.getUserEntity().getId())
                                    .eq(
                                            ProjectUserRemarkEntity::getProjectId,
                                            projectEntity.getId()));

            if (projectUserRemarkEntity != null) {
                // 更新备注
                projectUserRemarkEntity.setRemark(projectExtEntity.getRemark());
                projectUserRemarkService.updateById(projectUserRemarkEntity);
            } else {
                ProjectUserRemarkEntity projectUserRemark = new ProjectUserRemarkEntity();
                projectUserRemark.setUserId(authUser.getUserEntity().getId());
                projectUserRemark.setUserName(authUser.getUserEntity().getUserName());
                projectUserRemark.setProjectId(projectEntity.getId());
                projectUserRemark.setRole(authUser.getUserEntity().getRole());
                // 存入这个用户绑定的备注
                projectUserRemark.setRemark(projectExtEntity.getRemark());
                projectUserRemarkService.save(projectUserRemark);
            }

            ProjectExtEntity existProjectExtEntity =
                    projectExtService.getById(projectRequest.getId());
            if (existProjectExtEntity != null) {
                // 相当于不更新remark
                projectExtEntity.setRemark(existProjectExtEntity.getRemark());
            }
        }
        projectExtService.updateById(projectExtEntity);
        if (!Objects.isNull(projectRequest.getProjectModel())
                && !Objects.equals(
                        projectRequest.getProjectModel(), oldProjectEntity.getProjectModel())) {
            return generateForward();
        }
        if (!Objects.isNull(projectRequest.getWhetherAccessible())
                && !Objects.equals(
                        projectRequest.getWhetherAccessible(),
                        oldProjectEntity.getWhetherAccessible())) {
            return generateForward();
        }
        if (!Objects.isNull(projectRequest.getProjectHost())
                && !Objects.equals(
                        projectRequest.getProjectHost(), oldProjectEntity.getProjectHost())) {
            return generateForward();
        }
        if (!Objects.isNull(projectRequest.getProjectPort())
                && !Objects.equals(
                        projectRequest.getProjectPort(), oldProjectEntity.getProjectPort())) {
            return generateForward();
        }
        if (!Objects.isNull(projectRequest.getLocalProjectId())
                && !Objects.equals(
                        projectRequest.getLocalProjectId(), oldProjectEntity.getLocalProjectId())) {
            return generateForward();
        }
        if (!Objects.isNull(projectRequest.getProjectDeviceAlias())
                && !Objects.equals(
                        projectRequest.getProjectDeviceAlias(),
                        oldProjectEntity.getProjectDeviceAlias())) {
            return generateForward();
        }
        if (!oldProjectEntity.getTimezone().equals(projectRequest.getTimezone())) {
            Optional.ofNullable(projectRequest.getTimezone())
                    .ifPresent(
                            timezone -> {
                                operationYearQuartzServiceImpl.addOperationJob(
                                        timezone, OperationYearReportJob.class);
                                operationMonthQuartzServiceImpl.addOperationJob(
                                        timezone, OperationMonthReportJob.class);
                                checkDataQuartzService.addJob(timezone);
                                for (CustomTimezoneQuartzService customTimezoneQuartzService :
                                        customTimezoneQuartzServiceList) {
                                    customTimezoneQuartzService.addJob(timezone);
                                }
                            });
        }
        // 空当删除
        List<String> investorIds =
                investorProjectService.queryInvestIdsByProjectId(projectRequest.getId());
        if (!investorIds.equals(projectRequest.getInvestorIds())) {
            Optional.ofNullable(projectRequest.getInvestorIds())
                    .ifPresent(
                            e ->
                                    investorProjectService.saveInvestorForProject(
                                            e, projectRequest.getId()));
        }
        // 1.4.3 2025-05-19 10:34:44 added update device or meter init electric in/out data
        initElectricService.updateProjectAll(projectEntity);
        return Result.success();
    }

    /** init project */
    @PostMapping("/assign")
    @ApiOperation("manage-项目分配用户")
    @Log(
            module = "MANAGE_PROJECT",
            methods = "MANAGE_PROJECT_ASSIGN",
            type = OperationType.ADD_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/manage/project/assgin')")
    public Result<Object> assign(@RequestBody ProjectUserRequest projectUserRequest) {
        // 删除旧的项目关联
        userProjectService.remove(
                Wrappers.lambdaQuery(UserProjectEntity.class)
                        .eq(UserProjectEntity::getUserId, projectUserRequest.getUserId()));
        // 创建新的项目关联
        List<UserProjectEntity> list = new ArrayList<>();
        for (String projectId : projectUserRequest.getProjectId()) {
            UserProjectEntity userProjectEntity = new UserProjectEntity();
            userProjectEntity.setUserId(projectUserRequest.getUserId());
            userProjectEntity.setProjectId(projectId);
            list.add(userProjectEntity);
        }
        userProjectService.saveBatch(list);
        return Result.success();
    }

    /** get project */
    @PostMapping("/list/current/project")
    @ApiOperation("manage-获取当前用户项目列表")
    public Result<IPage<ProjectManageVo>> getCurrentUserProject(
            @RequestBody ProjectManageListRequest projectManageListRequest) {
        IPage<ProjectManageVo> projectPageBean =
                new Page<>(
                        projectManageListRequest.getPageNum(),
                        projectManageListRequest.getPageSize());
        IPage<ProjectManageVo> entityPage;
        String province = projectManageListRequest.getProvince();
        String[] provinces = Optional.ofNullable(province).orElse("").split(",");
        Set<String> projectIds = getProjectIdByProvince(province, provinces);
        if (StringUtils.hasLength(projectManageListRequest.getInvestor())) {
            List<String> investorProjectIds =
                    investorProjectService.queryProjectIdsByInvestorIds(
                            List.of(projectManageListRequest.getInvestor()));
            projectIds =
                    projectIds.stream()
                            .filter(investorProjectIds::contains)
                            .collect(Collectors.toSet());
        }
        // 获取到 当前的用户信息
        AuthUser authUser = SecurityUtil.getPrincipal();
        assert authUser != null;
        Map<String, String> currentProjects =
                projectServiceKt.getCurrentProjectList(
                        Objects.requireNonNull(SecurityUtil.getLoginRoleId()),
                        authUser.getAccountSystemSessionId());
        // 查找分配给该项目的用户
        Set<String> finalProjectIds = projectIds;
        if (currentProjects == null) {
            return Result.success(null);
        }
        currentProjects =
                currentProjects.entrySet().stream()
                        .filter(entry -> finalProjectIds.contains(entry.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (currentProjects.isEmpty()) {
            return Result.success(null);
        }
        projectIds = currentProjects.keySet();
        String role = authUser.getUserEntity().getRole();
        // 2代表内部用户
        boolean unitRemarkFlag = "2".equals(role);
        List<DeviceEntity> deviceEntities =
                deviceService.lambdaQuery().eq(DeviceEntity::getMaintain, true).list();
        List<AmmeterEntity> ammeterEntities =
                ammeterService.lambdaQuery().eq(AmmeterEntity::getMaintain, true).list();
        Set<String> deviceProjectIds =
                deviceEntities.stream().map(DeviceEntity::getProjectId).collect(Collectors.toSet());
        Set<String> meterProjectIds =
                ammeterEntities.stream()
                        .map(AmmeterEntity::getProjectId)
                        .collect(Collectors.toSet());
        if (projectManageListRequest.getMaintain() != null) {
            if (Boolean.TRUE.equals(projectManageListRequest.getMaintain())) {
                deviceProjectIds.addAll(meterProjectIds);
                projectIds.retainAll(deviceProjectIds);
            } else {
                deviceProjectIds.addAll(meterProjectIds);
                projectIds.removeAll(deviceProjectIds);
            }
        }
        if (!projectIds.isEmpty()) {
            projectManageListRequest.setProjectIds(projectIds);
        } else {
            Set<String> noRecordSet = new HashSet<>(1);
            noRecordSet.add("0");
            projectManageListRequest.setProjectIds(noRecordSet);
        }
        boolean hasMaintainAuthority = SecurityUtil.hasAuthority("/system/maintain");
        projectManageListRequest.setOrderShield(
                SecurityUtil.hasAuthority(EmsConstants.MESSAGE_SHOW_HIDE));
        entityPage =
                projectExtService.queryProjectManageList(projectPageBean, projectManageListRequest);
        Map<String, ProjectUserRemarkEntity> userRemarkMap;
        if (!unitRemarkFlag) {
            // 如果不是统一的备注 则需要去查询出
            userRemarkMap =
                    projectUserRemarkService.findUserRemarkBy(
                            authUser.getUserEntity().getId(),
                            entityPage.getRecords().stream()
                                    .map(ProjectManageVo::getId)
                                    .collect(Collectors.toList()));
        } else {
            userRemarkMap = new HashMap<>(1);
        }
        Set<String> demandProjectIds = groupService.queryEnableDemandProjectIds(projectIds);
        Set<String> capacityProjectIds = groupService.queryEnableCapacityProjectIds(projectIds);
        entityPageResolve(
                entityPage,
                unitRemarkFlag,
                deviceProjectIds,
                meterProjectIds,
                demandProjectIds,
                capacityProjectIds,
                hasMaintainAuthority,
                userRemarkMap,
                currentProjects);
        return Result.success(entityPage);
    }

    private void entityPageResolve(
            IPage<ProjectManageVo> entityPage,
            boolean unitRemarkFlag,
            Set<String> deviceProjectIds,
            Set<String> meterProjectIds,
            Set<String> demandProjectIds,
            Set<String> capacityProjectIds,
            boolean hasMaintainAuthority,
            Map<String, ProjectUserRemarkEntity> userRemarkMap,
            Map<String, String> currentProjects) {
        Set<String> vppProjectIds =
                groupService.lambdaQuery().eq(GroupEntity::getOpenVpp, true).list().stream()
                        .map(GroupEntity::getProjectId)
                        .collect(Collectors.toSet());
        Set<String> frProjectIds =
                ammeterService
                        .lambdaQuery()
                        .ne(AmmeterEntity::getFrequencyRegulation, "nil")
                        .list()
                        .stream()
                        .map(AmmeterEntity::getProjectId)
                        .collect(Collectors.toSet());
        long end = Instant.now().toEpochMilli();
        long start = end - 30 * 1000 * MyTimeUtil.ONE_DAY_SECONDS;
        List<String> list = new ArrayList<>(2);
        list.add("Fault");
        list.add("Alarm");
        List<String> projectIds =
                entityPage.getRecords().stream()
                        .map(ProjectManageVo::getId)
                        .collect(Collectors.toList());

        Map<String, ManageProjectOperation> manageProjectOperationMap = new HashMap<>(1);
        // 1.4.1 新增 把相关 缓存的 今日和昨日相关收益查询出来, 内部使用的是 三项页接口getTotalOperationProfit
        if (SecurityUtil.getPrincipal() != null
                && CollectionUtil.isNotEmpty(SecurityUtil.getPrincipal().getAuthorityList())
                && SecurityUtil.getPrincipal()
                        .getAuthorityList()
                        .contains("/manage/project/operation")) {
            Map<String, GroupEntity> projectSystemGroupMap =
                    groupService.systemGroupEntitys(projectIds).stream()
                            .collect(Collectors.toMap(GroupEntity::getProjectId, v -> v));
            manageProjectOperationMap =
                    projectServiceKt.fillProjectsProfit(
                            projectIds,
                            projectSystemGroupMap,
                            TimePointEnum.TODAY,
                            TimePointEnum.YESTERDAY);
            log.debug("has manage/project/operation :{}", manageProjectOperationMap);
        }
        Map<String, ManageProjectOperation> finalManageProjectOperationMap =
                manageProjectOperationMap;
        entityPage
                .getRecords()
                .forEach(
                        e -> {
                            ManageProjectOperation manageProjectOperation =
                                    finalManageProjectOperationMap.get(e.getId());
                            if (manageProjectOperation != null) {
                                // 1.4.1 把 缓存的收益填充到 vo里面
                                e.setManageProjectOperation(manageProjectOperation);
                            }
                            e.setVersion(version);
                            e.setMaintain(
                                    deviceProjectIds.contains(e.getId())
                                            || meterProjectIds.contains(e.getId()));
                            // 使用账户系统的项目名称
                            e.setProjectName(currentProjects.get(e.getId()));
                            if (vppProjectIds.contains(e.getId())) {
                                e.setHasVpp(true);
                            }
                            if (frProjectIds.contains(e.getId())) {
                                e.setHasFr(true);
                            }
                            if (!SecurityUtil.hasAuthority(EmsConstants.MESSAGE_SHOW_HIDE)) {
                                e.setFault(e.getNonHideFault());
                                e.setAlarm(e.getNonHideAlarm());
                            }
                            if (!SecurityUtil.hasAuthority(EmsConstants.MANAGE_SHOW_INVESTOR)) {
                                e.setInvestor(null);
                            } else {
                                List<InvestorEntity> investorEntities =
                                        investorProjectService.queryInvestorsByProjectId(e.getId());
                                e.setInvestor(
                                        investorEntities.isEmpty()
                                                ? null
                                                : investorEntities.get(0));
                            }
                            long num =
                                    eventMessageService
                                            .lambdaQuery()
                                            .eq(EventMessageEntity::getProjectId, e.getId())
                                            .eq(EventMessageEntity::getStatus, 0)
                                            .in(EventMessageEntity::getEventType, list)
                                            .eq(
                                                    !hasMaintainAuthority,
                                                    EventMessageEntity::getMaintain,
                                                    false)
                                            .eq(EventMessageEntity::getWhetherDelete, false)
                                            .ge(EventMessageEntity::getCreateTime, start)
                                            .le(EventMessageEntity::getCreateTime, end)
                                            .list()
                                            .size();
                            e.setUnConfirmError(num);
                            if (e.getProjectModel() == 1) {
                                if (hasMaintainAuthority) {
                                    e.setUnConfirmError(Long.valueOf(e.getUnConfirmAll()));
                                } else {
                                    e.setUnConfirmError(Long.valueOf(e.getUnConfirmNotMaintain()));
                                }
                            }

                            if (!unitRemarkFlag) {
                                // 如果是 用户绑定的备注 则直接先给备注设置空 防止看到 统一备注
                                e.setRemark("");
                                // 如果不是统一的备注
                                // 这里要去查询 当前登录账号的特定的项目备注
                                ProjectUserRemarkEntity projectUserRemarkEntity =
                                        userRemarkMap.get(e.getId());
                                if (projectUserRemarkEntity != null) {
                                    e.setRemark(projectUserRemarkEntity.getRemark());
                                }
                            }
                            // 控容、控需类型
                            List<Integer> controlType = new ArrayList<>();
                            if (capacityProjectIds.contains(e.getId())) {
                                controlType.add(1);
                            }
                            if (demandProjectIds.contains(e.getId())) {
                                controlType.add(2);
                            }
                            e.setControlType(controlType);
                        });
    }

    private @NotNull Set<String> getProjectIdByProvince(String province, String[] provinces) {
        return projectService
                .lambdaQuery()
                .eq(ProjectEntity::getWhetherDelete, false)
                .in(
                        StringUtils.hasLength(province),
                        ProjectEntity::getProvince,
                        Arrays.asList(provinces))
                .list()
                .stream()
                .map(ProjectEntity::getId)
                .collect(Collectors.toSet());
    }

    @PostMapping("/list/current/status")
    @ApiOperation("manage-获取当前用户所有项目状态")
    @PreAuthorize("hasAuthority('/manage/project/query')")
    public Result<Object> getCurrentUserProjectStatus() throws ExecutionException {
        // 当前登录用户
        UserEntity userEntity = Objects.requireNonNull(SecurityUtil.getPrincipal()).getUserEntity();
        Set<String> projectIds;
        if (!EmsConstants.WH_ADMIN_USERNAME.equals(SecurityUtil.getUsername())
                && Boolean.TRUE.equals(!userEntity.getAllProject())) {
            // 查找分配给该项目的用户
            List<UserProjectEntity> userProjectEntityList =
                    userProjectService.list(
                            Wrappers.lambdaQuery(UserProjectEntity.class)
                                    .eq(UserProjectEntity::getUserId, SecurityUtil.getUserId()));
            // 用户id
            projectIds =
                    userProjectEntityList.stream()
                            .map(UserProjectEntity::getProjectId)
                            .collect(Collectors.toSet());
        } else {
            List<ProjectEntity> list = projectService.list();
            projectIds = list.stream().map(ProjectEntity::getId).collect(Collectors.toSet());
        }
        // 去除所有的逻辑删除项目
        projectService
                .list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, true))
                .stream()
                .map(ProjectEntity::getId)
                .collect(Collectors.toList())
                .forEach(projectIds::remove);
        Map<String, Map<String, Map<String, Object>>> map = new HashMap<>(projectIds.size());
        for (String id : projectIds) {
            Map<String, Map<String, Object>> resultMap =
                    monitorService.getOneEmsRunStatus(
                            id,
                            null,
                            SecurityUtil.hasAuthority(EmsConstants.MESSAGE_SHOW_HIDE),
                            SecurityUtil.hasAuthority(EmsConstants.DEVICE_MAINTAIN_AUTHORITY));
            map.put(id, resultMap);
        }
        return Result.success(map);
    }

    /** get project info */
    @PostMapping("/info")
    @ApiOperation("manage-获取项目详情")
    @PreAuthorize("hasAuthority('/manage/project/query')")
    public Result<ProjectManageVo> getProjectInfo(String projectId) {
        ProjectManageVo projectManageVo = projectExtService.queryProjectManageInfo(projectId);
        projectManageVo.setVersion(version);
        List<UserProjectEntity> userProjectEntityList =
                userProjectService.list(
                        Wrappers.lambdaQuery(UserProjectEntity.class)
                                .eq(UserProjectEntity::getProjectId, projectId));
        Set<String> userIds =
                userProjectEntityList.stream()
                        .map(UserProjectEntity::getUserId)
                        .collect(Collectors.toSet());
        List<UserEntity> allUser =
                userService.list(
                        Wrappers.lambdaQuery(UserEntity.class)
                                .eq(UserEntity::getAllProject, true)
                                .or()
                                .in(!userIds.isEmpty(), UserEntity::getId, userIds));
        projectManageVo.setUserList(allUser);
        return Result.success(projectManageVo);
    }

    /** delete */
    @PostMapping("/delete/{id}")
    @ApiOperation("manage-删除项目以及项目用户关系")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/manage/project/delete')")
    @Log(
            module = "MANAGE_PROJECT",
            methods = "MANAGE_PROJECT_DEL",
            type = OperationType.DEL,
            logDetailServiceClass = ProjectManageLogDetailService.class)
    public Result<Object> delete(@PathVariable("id") String id) {
        projectServiceKt.delResource(id);
        projectService.update(
                Wrappers.lambdaUpdate(ProjectEntity.class)
                        .set(ProjectEntity::getWhetherDelete, true)
                        .eq(ProjectEntity::getId, id));
        return Result.success();
    }

    /** get project */
    @PostMapping("/list/all")
    @ApiOperation("manage-获取所有项目列表分页")
    public Result<IPage<ProjectEntity>> getProjectList(@RequestBody PageBean pageBean) {
        IPage<ProjectEntity> projectPageBean =
                new Page<>(pageBean.getPageNum(), pageBean.getPageSize());
        IPage<ProjectEntity> entityPage = projectService.page(projectPageBean);
        entityPage.getRecords().forEach(e -> e.setVersion(version));
        return Result.success(entityPage);
    }

    public Result<Object> generateForward() {
        // 生成场站版项目链接
        List<Map<String, Object>> list = new ArrayList<>();
        List<ProjectEntity> projectEntities =
                projectService
                        .lambdaQuery()
                        .ne(ProjectEntity::getProjectModel, 0)
                        .eq(ProjectEntity::getWhetherAccessible, true)
                        .eq(ProjectEntity::getWhetherDelete, false)
                        .list();
        for (ProjectEntity tempProjectEntity : projectEntities) {
            Map<String, Object> tempParaMap = new HashMap<>(3);
            String ip = tempProjectEntity.getProjectHost();
            if (tempProjectEntity.getProjectPort() != null
                    && tempProjectEntity.getProjectPort() != 0) {
                ip = tempProjectEntity.getProjectHost() + ":" + tempProjectEntity.getProjectPort();
            }
            tempParaMap.put("ip", ip);
            tempParaMap.put("uuid", tempProjectEntity.getProjectDeviceAlias());
            List<ProjectUrlEntity> urlList =
                    projectUrlService.list(
                            Wrappers.lambdaQuery(ProjectUrlEntity.class)
                                    .eq(ProjectUrlEntity::getProjectId, tempProjectEntity.getId()));
            tempParaMap.put(
                    "pages", urlList.stream().map(ProjectUrlEntity::getDescription).toArray());
            list.add(tempParaMap);
        }
        Map<String, Object> paraMap2 = new HashMap<>(1);
        paraMap2.put("sites", list.toArray());
        log.info(JSON.toJSONString(paraMap2));
        JSONObject jsonObject = restTemplate.postForObject(refreshUrl, paraMap2, JSONObject.class);
        String key = "success";
        assert jsonObject != null;
        boolean add = (boolean) jsonObject.get(key);
        if (!add) {
            return Result.success("forward need refresh");
        } else {
            return Result.success();
        }
    }
}
