package com.wifochina.modules.manage.user.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.handler.MessageSourceHandler;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.oauth.dto.AccountInnerResult;
import com.wifochina.modules.oauth.dto.AccountListPage;
import com.wifochina.modules.oauth.dto.Data;
import com.wifochina.modules.oauth.service.AccountSystemService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.utils.AccountAuthUtils;
import com.wifochina.modules.project.service.ProjectServiceKt;
import com.wifochina.modules.user.entity.LogEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.request.LogNamePageRequest;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.context.NoSuchMessageException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Api(tags = "manage-05-用户中心")
@RestController
@RequestMapping("/manage/user")
@Slf4j
public class ManageOperationLogController {

    @Resource private LogService logService;

    @Resource private AccountSystemService accountSystemService;

    @Resource private ProjectServiceKt projectServiceKt;

    @Resource private AccountAuthUtils accountAuthUtils;

    @PostMapping("/getOnePageLogs")
    @ApiOperation("manage-查询一页操作记录")
    @PreAuthorize("hasAuthority('/manage/user/log')")
    public Result<IPage<LogEntity>> getOnePageLogs(
            @RequestBody LogNamePageRequest logNamePageRequest) {
        IPage<LogEntity> resultPage = queryLogs(logNamePageRequest);
        if (resultPage != null && CollUtil.isNotEmpty(resultPage.getRecords())) {
            resultPage.getRecords().forEach(i -> i.setDetail(null));
        }
        return Result.success(resultPage);
    }

    @PostMapping("/getOnePageLogs/detail")
    @ApiOperation("manage-查询一页操作记录(明细)")
    @PreAuthorize("hasAuthority('/manage/user/log/detail')")
    public Result<IPage<LogEntity>> getOnePageLogsDetail(
            @RequestBody LogNamePageRequest logNamePageRequest) {
        IPage<LogEntity> resultPage = queryLogs(logNamePageRequest);
        return Result.success(resultPage);
    }

    private IPage<LogEntity> queryLogs(LogNamePageRequest logNamePageRequest) {
        if (SecurityUtil.getPrincipal() == null) {
            return null;
        }

        UserEntity user = SecurityUtil.getPrincipal().getUserEntity();
        if (user == null) {
            return null;
        }

        IPage<LogEntity> resultPage;
        Map<String, String> conditionUserNameMap = new HashMap<>();

        Map<String, String> currentProjectMap = getMatchedProjects(logNamePageRequest);

        if (user.getType() != null && 2 == user.getType()) {
            resultPage =
                    handleAdUser(logNamePageRequest, conditionUserNameMap, user, currentProjectMap);
        } else if (EmsConstants.WH_ADMIN_USERNAME.equals(user.getUserName())) {
            resultPage =
                    handleAdUser(logNamePageRequest, conditionUserNameMap, user, currentProjectMap);
        } else {
            resultPage =
                    handleOtherUser(
                            logNamePageRequest, conditionUserNameMap, user, currentProjectMap);
        }

        if (resultPage == null || CollectionUtils.isEmpty(resultPage.getRecords())) {
            return null;
        }
        processLogEntities(resultPage, conditionUserNameMap, currentProjectMap);
        return resultPage;
    }

    private Map<String, String> getMatchedProjects(LogNamePageRequest logNamePageRequest) {
        Map<String, String> currentProjects =
                projectServiceKt.getCurrentProjectList(
                        Objects.requireNonNull(SecurityUtil.getLoginRoleId()),
                        accountAuthUtils.getSessionId());
        if (currentProjects == null) {
            currentProjects = Map.of(EmsConstants.COMMON, EmsConstants.COMMON);
        }
        if (StringUtils.hasLength(logNamePageRequest.getProjectName())) {
            currentProjects =
                    currentProjects.entrySet().stream()
                            .filter(
                                    entry ->
                                            logNamePageRequest
                                                    .getProjectName()
                                                    .equals(entry.getValue()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        } else {
            currentProjects.put(EmsConstants.COMMON, EmsConstants.COMMON);
        }
        return currentProjects;
    }

    private IPage<LogEntity> handleAdUser(
            LogNamePageRequest logNamePageRequest,
            Map<String, String> conditionNameMap,
            UserEntity user,
            Map<String, String> currentPorjectMap) {
        if (StringUtils.hasLength(logNamePageRequest.getName())) {
            AccountInnerResult accountListPage =
                    accountSystemService.getUserByUsername(logNamePageRequest.getName());
            if (accountListPage != null && !CollectionUtils.isEmpty(accountListPage.getData())) {
                conditionNameMap.put(
                        accountListPage.getData().get(0).getId(),
                        accountListPage.getData().get(0).getNickName());
                return queryUserLogs(
                        logNamePageRequest,
                        new ArrayList<>(currentPorjectMap.keySet()),
                        List.of(accountListPage.getData().get(0).getId()));
            } else {
                return null;
            }
        } else {
            IPage<LogEntity> page =
                    queryUserLogs(
                            logNamePageRequest, new ArrayList<>(currentPorjectMap.keySet()), null);
            if (CollectionUtils.isEmpty(page.getRecords())) {
                return null;
            }
            AccountInnerResult accountListPage =
                    accountSystemService.getUserListByUserIds(
                            page.getRecords().stream()
                                    .map(LogEntity::getUserId)
                                    .collect(Collectors.toList()));

            if (accountListPage != null && !CollectionUtils.isEmpty(accountListPage.getData())) {
                accountListPage
                        .getData()
                        .forEach(
                                e -> {
                                    conditionNameMap.put(e.getId(), e.getNickName());
                                });
            }
            return page;
        }
    }

    private IPage<LogEntity> handleOtherUser(
            LogNamePageRequest logNamePageRequest,
            Map<String, String> conditionNameMap,
            UserEntity user,
            Map<String, String> currentPorjectMap) {
        AccountListPage accountListPage = accountSystemService.subUserList();
        List<String> conditionUserIds = new ArrayList<>();
        if (accountListPage != null && accountListPage.getData() != null) {
            if (StringUtils.hasLength(logNamePageRequest.getName())) {
                conditionUserIds =
                        accountListPage.getData().stream()
                                .filter(
                                        data ->
                                                data.getUserName()
                                                        .contains(logNamePageRequest.getName()))
                                .map(Data::getUserId)
                                .collect(Collectors.toList());
            } else {
                conditionUserIds.addAll(
                        accountListPage.getData().stream()
                                .map(Data::getUserId)
                                .collect(Collectors.toList()));
            }
            accountListPage
                    .getData()
                    .forEach(
                            e -> {
                                conditionNameMap.put(e.getUserId(), e.getUserNickName());
                            });
        }
        if (!StringUtils.hasLength(logNamePageRequest.getName())
                || logNamePageRequest.getName().equals(user.getUserName())) {
            conditionUserIds.add(user.getId());
            conditionNameMap.put(user.getId(), user.getName());
        }
        if (CollectionUtils.isEmpty(conditionUserIds)) {
            return null;
        }
        return queryUserLogs(
                logNamePageRequest, new ArrayList<>(currentPorjectMap.keySet()), conditionUserIds);
    }

    private IPage<LogEntity> queryUserLogs(
            LogNamePageRequest logNamePageRequest, List<String> projectIds, List<String> userIds) {
        IPage<LogEntity> logPage =
                new Page<>(logNamePageRequest.getPageNum(), logNamePageRequest.getPageSize());
        return logService.page(
                logPage,
                new QueryWrapper<LogEntity>()
                        .lambda()
                        .ge(
                                logNamePageRequest.getStart() != null,
                                LogEntity::getRequestTime,
                                logNamePageRequest.getStart())
                        .le(
                                logNamePageRequest.getEnd() != null,
                                LogEntity::getRequestTime,
                                logNamePageRequest.getEnd())
                        .in(
                                !CollectionUtils.isEmpty(projectIds),
                                LogEntity::getProjectId,
                                projectIds)
                        .ne(LogEntity::getUserId, EmsConstants.LOG_USER_SYSTEM_ID)
                        .in(!CollectionUtils.isEmpty(userIds), LogEntity::getUserId, userIds)
                        .orderByDesc(LogEntity::getRequestTime));
    }

    private void processLogEntities(
            IPage<LogEntity> logEntityIPage,
            Map<String, String> conditionNameMap,
            Map<String, String> currentProjects) {
        logEntityIPage
                .getRecords()
                .forEach(
                        logEntity -> {
                            try {
                                logEntity.setModule(
                                        MessageSourceHandler.getMessage(logEntity.getModule()));
                                logEntity.setMethod(
                                        MessageSourceHandler.getMessage(logEntity.getMethod()));
                                logEntity.setName(conditionNameMap.get(logEntity.getUserId()));
                                if (!Objects.equals(logEntity.getProjectId(), EmsConstants.COMMON)
                                        && currentProjects != null) {
                                    logEntity.setProjectName(
                                            currentProjects.get(logEntity.getProjectId()));
                                } else {
                                    logEntity.setProjectName(EmsConstants.COMMON);
                                }
                            } catch (NoSuchMessageException e) {
                                log.info(
                                        "[language error]----------------------------->"
                                                + e.getMessage());
                            }
                        });
    }
}
