package com.wifochina.modules.manage.cloudlargescreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2023/10/16 13:28.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProjectAllProfitCarbonOverviewVo {

    @ApiModelProperty(value = "今日收益")
    private String todayBenefit = "0.0";
    @ApiModelProperty(value = "月收益")
    private String monthBenefit = "0.0";
    @ApiModelProperty(value = "合计收益")
    private String totalBenefit = "0.0";

    @ApiModelProperty(value = "碳减排")
    private String carbonReduction = "0.0";
}
