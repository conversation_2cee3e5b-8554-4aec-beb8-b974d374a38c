package com.wifochina.modules.manage.project.timer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.config.RemoteRestTemplateRequestInterceptor;
import com.wifochina.common.constants.EquipNumberEnum;
import com.wifochina.common.constants.InstallationEnum;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsStatusEnum;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.demand.service.DemandAlarmService;
import com.wifochina.modules.demand.vo.GroupDemandAlarmVo;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.request.EventMessageRequest;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.monitor.service.MonitorService;
import com.wifochina.modules.oauth.AuthUser;
import com.wifochina.modules.oauth.util.JwtHelper;
import com.wifochina.modules.oauth.util.JwtUserInfo;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.request.SiteDailyRequest;
import com.wifochina.modules.project.request.ZeroPowerContext;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.user.entity.AuthorityEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.service.AuthorityService;
import com.wifochina.modules.user.service.UserService;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2022-03-03 15:37:04
 *     <p>致敬大师，彼岸无岸，当下即是
 */
@Slf4j
@Component
public class TaskProjectTimer {

    @Resource private DeviceService deviceService;
    @Resource private ProjectService projectService;
    @Resource private GroupService groupService;
    @Resource private DataService dataService;
    @Resource private ProjectExtService projectExtService;
    @Resource private RestTemplateBuilder restTemplateBuilder;
    @Resource private JwtHelper jwtHelper;
    @Resource private UserService userService;
    @Resource private AuthorityService authorityService;
    @Resource private MonitorService monitorService;
    @Resource private HideEventCodeService hideEventCodeService;
    @Resource private DemandAlarmService demandAlarmService;
    @Resource private RestTemplate restTemplate;
    @Resource private EventMessageService eventMessageService;
    @Resource private PointListHolder pointListHolder;

    private final int threadCount = 20;
    // 创建一个固定大小的线程池
    @Getter ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

    @Value("${local.forward}")
    private String forwardUrl;

    @Value("${ems.gateway}")
    private String gatewayUrl;

    @PreDestroy
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.error("Interrupted while waiting for scheduled tasks to complete.", e);
                executorService.shutdownNow();
            }
        }
    }

    /** 定时更新项目状态和告警信息 间隔时间在配置文件中 initialDelay设为3000ms，等待服务起好后执行 */
    @Scheduled(initialDelay = 1000, fixedRateString = "${spring.task.project.fixedDelay}")
    @Async("asyncServiceExecutor")
    public void collectProjectPoint() {
        log.info("{} -->正在执行", Thread.currentThread().getName() + " collectProjectData");
        List<ProjectEntity> projectEntities = projectService.getCloudProjectList();
        for (ProjectEntity projectEntity : projectEntities) {
            // 提交任务到线程池
            executorService.submit(() -> updateProjectStatus(projectEntity));
        }
    }

    private void updateProjectStatus(ProjectEntity projectEntity) {
        List<DeviceEntity> deviceEntities = deviceService.getDevicesByPid(projectEntity.getId());
        try {
            WebUtils.projectId.set(projectEntity.getId());
            ResponseEntity<String> response =
                    restTemplate.execute(
                            gatewayUrl + "/api/v1/get_online",
                            HttpMethod.POST,
                            null,
                            clientHttpResponse ->
                                    new ResponseEntity<>(clientHttpResponse.getStatusCode()));
            assert response != null;
            HttpStatus statusCode = response.getStatusCode();
            if (statusCode != HttpStatus.OK) {
                // 设置项目状态为 未安装(待调试) or 断网
                setProjectStatusUnknownOrInstall(projectEntity, deviceEntities);
                return;
            }
        } catch (Exception ignored) {
            setProjectStatusUnknownOrInstall(projectEntity, deviceEntities);
            return;
        }
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectEntity.getId());
        List<String> sysDeviceIds =
                deviceService.getGroupDeviceIdList(
                        systemGroupEntity.getId(), EmsConstants.ALL, false);
        try {
            int status;
            if (deviceEntities.isEmpty()) {
                status = EmsStatusEnum.UNINSTALL.getStatus();
            } else {
                status = updateEquipNum(deviceEntities, projectEntity, sysDeviceIds);
            }
            Map<String, Map<String, Object>> runStatus =
                    monitorService.getRunStatus(projectEntity.getId(), true, true);
            // 将Map转换为JSON字符串
            String jsonString = JSON.toJSONString(runStatus);
            // 将JSON字符串转换为JSONObject
            JSONObject runStatusJsonObject = JSON.parseObject(jsonString);
//            hideEventCodeService.getHideEventCode(projectEntity.getId());
            List<String> hideCodeList =
                    hideEventCodeService.getHideAllTypeEventCodeList(projectEntity.getId());
            Pair<Pair<Integer, Integer>, Pair<Integer, Integer>> errorNumPairAndErrorHideNumPair =
                    getFaultAndAlarm(runStatusJsonObject, hideCodeList);
            errorNumPairAndErrorHideNumPair = addDemandAlarmNum(errorNumPairAndErrorHideNumPair);
            updateProjectInfo(
                    errorNumPairAndErrorHideNumPair.getFirst().getFirst(),
                    errorNumPairAndErrorHideNumPair.getFirst().getSecond(),
                    status,
                    projectEntity.getId(),
                    projectEntity.getProjectModel() == 1,
                    -1,
                    -1,
                    errorNumPairAndErrorHideNumPair.getSecond().getFirst(),
                    errorNumPairAndErrorHideNumPair.getSecond().getSecond());
        } catch (Exception e) {
            log.error(
                    "project id: {}, project name: {} update status error, current status is {}",
                    projectEntity.getId(),
                    projectEntity.getProjectName(),
                    deviceEntities.isEmpty()
                            ? EmsStatusEnum.UNINSTALL.getStatus()
                            : EmsStatusEnum.UNKNOWN.getStatus());
            setProjectStatusUnknownOrInstall(projectEntity, deviceEntities);
        }
    }

    /**
     * 设置项目状态为 未安装(待调试) or 断网
     *
     * @param projectEntity
     * @param deviceEntities
     */
    private void setProjectStatusUnknownOrInstall(
            ProjectEntity projectEntity, List<DeviceEntity> deviceEntities) {
        updateProjectInfo(
                0,
                0,
                deviceEntities.isEmpty()
                        ? EmsStatusEnum.UNINSTALL.getStatus()
                        : EmsStatusEnum.UNKNOWN.getStatus(),
                projectEntity.getId(),
                projectEntity.getProjectModel() == 1,
                -1,
                -1,
                0,
                0);
    }

    private @NotNull Pair<Pair<Integer, Integer>, Pair<Integer, Integer>> addDemandAlarmNum(
            Pair<Pair<Integer, Integer>, Pair<Integer, Integer>> errorNumPairAndErrorHideNumPair) {
        List<GroupDemandAlarmVo> demandAlarmList =
                demandAlarmService.getDemandAlarm(WebUtils.projectId.get());
        errorNumPairAndErrorHideNumPair =
                Pair.of(
                        Pair.of(
                                errorNumPairAndErrorHideNumPair.getFirst().getFirst(),
                                errorNumPairAndErrorHideNumPair.getFirst().getSecond()
                                        + demandAlarmList.size()),
                        Pair.of(
                                errorNumPairAndErrorHideNumPair.getSecond().getFirst(),
                                errorNumPairAndErrorHideNumPair.getSecond().getSecond()
                                        + demandAlarmList.size()));
        return errorNumPairAndErrorHideNumPair;
    }

    private int updateEquipNum(
            List<DeviceEntity> deviceEntities,
            ProjectEntity projectEntity,
            List<String> sysDeviceIds) {
        int ems = 0;
        int pcs = 0;
        int bms = 0;
        boolean updateEquipNum = true;
        boolean isOff = true;
        int status = 0;
        double designPower = 0;
        double emsInPower = 0;
        double emsOutPower = 0;
        double emsReactiveInPower = 0;
        double emsReactiveOutPower = 0;
        boolean hasEmsRun = false;
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectEntity.getId());

        for (DeviceEntity deviceEntity : deviceEntities) {
            if (!sysDeviceIds.contains(deviceEntity.getId())) {
                continue;
            }
            int[] data = dataService.get(deviceEntity.getId(), projectEntity.getId());
            if (data == null) {
                updateEquipNum = false;
                continue;
            } else {
                isOff = false;
            }
            pcs += getPcsNum(data);
            bms += getBmsNum(data);
            ems += 1;
            double point =
                    ((double)
                                    (short)
                                            data[
                                                    pointListHolder.getEmsAcActivePower(
                                                            data[
                                                                    pointListHolder
                                                                            .getEmsTypeIndex()])])
                            / 10;
            //  2025-06-06 14:08:43 new added for support ac reactive power
            if (Boolean.TRUE.equals(systemGroupEntity.getHomePageReactivePowerController())) {
                double reActivePoint =
                        ((double)
                                        (short)
                                                data[
                                                        pointListHolder.getEmsAcReactivePower(
                                                                data[
                                                                        pointListHolder
                                                                                .getEmsTypeIndex()])])
                                / 10;
                if (reActivePoint > 0) {
                    // 放电
                    emsReactiveOutPower += reActivePoint;
                } else {
                    // 充电
                    emsReactiveInPower += Math.abs(reActivePoint);
                }
            }
            if (point > 0) {
                // 放电
                emsOutPower += point;
            } else {
                // 充电
                emsInPower += Math.abs(point);
            }
            designPower +=
                    data[
                            pointListHolder.getEmsDesignPower(
                                    data[pointListHolder.getEmsTypeIndex()])];
            // 是否有机器开机
            if (data[pointListHolder.getSystemRunStatus(data[pointListHolder.getEmsTypeIndex()])]
                    > 0) {
                hasEmsRun = true;
            }
        }

        ZeroPowerContext zeroPowerContext = new ZeroPowerContext();
        // 默认离线
        zeroPowerContext.setOpenZeroController(systemGroupEntity.getHomePageZeroPowerController());
        if (!isOff) {
            if (Boolean.TRUE.equals(systemGroupEntity.getHomePageReactivePowerController())) {
                status =
                        EmsUtil.getEmsStatusSupportReactivePower(
                                designPower,
                                emsInPower,
                                emsOutPower,
                                emsReactiveInPower,
                                emsReactiveOutPower,
                                isOff,
                                hasEmsRun,
                                zeroPowerContext);
            } else {
                status =
                        EmsUtil.getEmsStatus(
                                designPower,
                                emsInPower,
                                emsOutPower,
                                isOff,
                                hasEmsRun,
                                zeroPowerContext);
            }
            //            status = EmsUtil.getEmsStatus(designPower, emsInPower, emsOutPower, isOff,
            // hasEmsRun);
        }

        if (updateEquipNum) {
            ProjectExtEntity projectExtEntity = projectExtService.getById(projectEntity.getId());
            if (projectExtEntity.getBms() != bms
                    || projectExtEntity.getPcs() != pcs
                    || projectExtEntity.getEms() != ems) {
                updateProjectExtInfo(ems, pcs, bms, projectEntity.getId());
            }
        }
        return status;
    }

    private int getBmsNum(int[] data) {
        return data[pointListHolder.getBmsNum(data[pointListHolder.getEmsTypeIndex()])];
    }

    private int getPcsNum(int[] data) {
        return data[pointListHolder.getPcsNum(data[pointListHolder.getEmsTypeIndex()])];
    }

    @Scheduled(initialDelay = 2000, fixedRateString = "${spring.task.project.fixedDelay}")
    @Async("asyncServiceExecutor")
    public void getLocalStatus() {
        List<ProjectEntity> projectEntities = getLocalProjectList();
        RestTemplate remoteRestTemplate =
                restTemplateBuilder
                        .setConnectTimeout(Duration.ofSeconds(5))
                        .setReadTimeout((Duration.ofSeconds(10)))
                        .build();
        AuthUser loginUser = getSuperUser();
        String token =
                jwtHelper.generateRemoteUserToken(
                        JwtUserInfo.builder().userId("1").authUser(loginUser).build(), false);
        for (ProjectEntity projectEntity : projectEntities) {
            remoteRestTemplate.setInterceptors(
                    List.of(
                            new RemoteRestTemplateRequestInterceptor(
                                    projectEntity.getLocalProjectId(), token)));
            String url = forwardUrl + projectEntity.getProjectDeviceAlias() + "/api/project/site";
            long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            SiteDailyRequest siteDailyRequest = new SiteDailyRequest();
            siteDailyRequest.setStartDate(todayZero);
            String runStatusUrl =
                    forwardUrl
                            + projectEntity.getProjectDeviceAlias()
                            + "/api/monitor/getRunStatus";
            String eventMessageUrl =
                    forwardUrl
                            + projectEntity.getProjectDeviceAlias()
                            + "/api/event/getEventMessage";
            String hideCodeUrl =
                    forwardUrl + projectEntity.getProjectDeviceAlias() + "/api/event/getHideCode";
            EventMessageRequest eventMessageRequest =
                    createEventMessageRequest(Instant.now().toEpochMilli());
            try {
                JSONObject jsonObject =
                        remoteRestTemplate.postForObject(url, siteDailyRequest, JSONObject.class);
                assert jsonObject != null;
                String status = String.valueOf(jsonObject.getJSONObject("data").get("emsStatus"));
                jsonObject = remoteRestTemplate.getForObject(runStatusUrl, JSONObject.class);
                assert jsonObject != null;
                JSONObject runStatus = jsonObject.getJSONObject("data");
                List<HideEventCodeEntity> hideEventCodeList = new ArrayList<>();
                try {
                    JSONObject hideCodeJsonObject =
                            remoteRestTemplate.postForObject(hideCodeUrl, null, JSONObject.class);
                    if (hideCodeJsonObject != null) {
                        hideEventCodeList =
                                hideCodeJsonObject
                                        .getJSONArray("data")
                                        .toJavaList(HideEventCodeEntity.class);
                    }
                } catch (Exception e) {
                    log.error(
                            "{} 没有获取 hide code的方法 需要更新 {}",
                            projectEntity.getProjectName(),
                            e.getMessage());
                }
                Pair<Pair<Integer, Integer>, Pair<Integer, Integer>>
                        errorNumPairAndErrorHideNumPair =
                                getFaultAndAlarm(
                                        runStatus,
                                        hideEventCodeList.stream()
                                                .map(HideEventCodeEntity::getEventCode)
                                                .collect(Collectors.toList()));
                Pair<Integer, Integer> errorNumPair = errorNumPairAndErrorHideNumPair.getFirst();
                Pair<Integer, Integer> errorHideNumPair =
                        errorNumPairAndErrorHideNumPair.getSecond();
                int unConfirmAll = 0;
                int unConfirmNotMaintain = 0;
                eventMessageRequest.setMaintain(null);
                jsonObject =
                        remoteRestTemplate.postForObject(
                                eventMessageUrl, eventMessageRequest, JSONObject.class);
                if (jsonObject != null) {
                    unConfirmAll = (int) jsonObject.getJSONObject("data").get("total");
                }
                eventMessageRequest.setMaintain(false);
                jsonObject =
                        remoteRestTemplate.postForObject(
                                eventMessageUrl, eventMessageRequest, JSONObject.class);
                if (jsonObject != null) {
                    unConfirmNotMaintain = (int) jsonObject.getJSONObject("data").get("total");
                }
                updateProjectInfo(
                        errorNumPair.getFirst(),
                        errorNumPair.getSecond(),
                        Integer.parseInt(status),
                        projectEntity.getId(),
                        projectEntity.getProjectModel() == 1,
                        unConfirmNotMaintain,
                        unConfirmAll,
                        errorHideNumPair.getFirst(),
                        errorHideNumPair.getSecond());
            } catch (Exception e) {
                log.error("can get local status for {}", projectEntity.getProjectName());
                updateProjectInfo(
                        0,
                        0,
                        EmsStatusEnum.UNKNOWN.getStatus(),
                        projectEntity.getId(),
                        projectEntity.getProjectModel() == 1,
                        0,
                        0,
                        0,
                        0);
            }
        }
    }

    public Pair<Pair<Integer, Integer>, Pair<Integer, Integer>> getFaultAndAlarm(
            JSONObject runStatus, List<String> hideEventCodeList) {
        Pair<Integer, Integer> errorNumPair = Pair.of(0, 0);
        Pair<Integer, Integer> errorHideNumPair = Pair.of(0, 0);
        for (String outKey : runStatus.keySet()) {
            if (EmsConstants.DEMAND_NOTICE.equals(outKey)) {
                JSONArray demandList = runStatus.getJSONArray(EmsConstants.DEMAND_NOTICE);
                if (!CollectionUtils.isEmpty(demandList)) {
                    errorNumPair =
                            Pair.of(
                                    errorNumPair.getFirst(),
                                    errorNumPair.getSecond() + demandList.size());
                }
                continue;
            }
            JSONObject equip = runStatus.getJSONObject(outKey);
            String type = (String) equip.get("type");
            int offline = equip.get("offline") == null ? 1 : (int) equip.get("offline");
            if (offline == 1) {
                errorNumPair = Pair.of(errorNumPair.getFirst() + 1, errorNumPair.getSecond());
                continue;
            }
            if (EmsConstants.CONTROLLABLE.equals(equip.get("type"))) {
                JSONObject controllableStatus = equip.getJSONObject("status");
                if (controllableStatus == null) {
                    continue;
                }
                JSONArray alarmList = controllableStatus.getJSONArray("AlarmEvent");
                JSONArray errorList = controllableStatus.getJSONArray("FaultEvent");
                if (alarmList != null) {
                    errorNumPair =
                            Pair.of(
                                    errorNumPair.getFirst(),
                                    errorNumPair.getSecond() + alarmList.size());
                    JSONArray alarmCodeList = controllableStatus.getJSONArray("AlarmEventCode");
                    if (alarmCodeList != null) {
                        for (Object alarmCode : alarmCodeList) {
                            if (hideEventCodeList.stream()
                                    .anyMatch(hideEventCode -> hideEventCode.equals(alarmCode))) {
                                errorHideNumPair =
                                        Pair.of(
                                                errorHideNumPair.getFirst(),
                                                errorHideNumPair.getSecond() + 1);
                            }
                        }
                    }
                }
                if (errorList != null) {
                    errorNumPair =
                            Pair.of(
                                    errorNumPair.getFirst() + errorList.size(),
                                    errorNumPair.getSecond());
                    JSONArray errorCodeList = controllableStatus.getJSONArray("FaultEventCode");
                    if (errorCodeList != null) {
                        for (Object errorCode : errorCodeList) {
                            if (hideEventCodeList.stream()
                                    .anyMatch(hideEventCode -> hideEventCode.equals(errorCode))) {
                                errorHideNumPair =
                                        Pair.of(
                                                errorHideNumPair.getFirst() + 1,
                                                errorHideNumPair.getSecond());
                            }
                        }
                    }
                }
            }

            if (InstallationEnum.EMS.getName().equals(type) && offline == 0) {
                errorNumPair = setEmsPair(errorNumPair, equip);
                errorHideNumPair = setEmsHidePair(errorHideNumPair, equip, hideEventCodeList);
            }
            if ("meter".equals(type) && offline == 0) {
                errorNumPair = setFaultAndAlarm(errorNumPair, equip, "meter");
                errorHideNumPair =
                        setHideFaultAndAlarm(errorHideNumPair, equip, "meter", hideEventCodeList);
            }
        }
        return Pair.of(errorNumPair, errorHideNumPair);
    }

    private Pair<Integer, Integer> setEmsPair(
            Pair<Integer, Integer> errorNumPair, JSONObject equip) {
        errorNumPair = setFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.PCS.getCode());
        errorNumPair = setFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.BMS.getCode());
        errorNumPair = setFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.AIR.getCode());
        errorNumPair = setFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.FIRE.getCode());
        errorNumPair =
                setFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.WATER_COOLER.getCode());
        errorNumPair = setFaultAndAlarm(errorNumPair, equip, "system");
        errorNumPair = setFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.DCDC.getCode());
        errorNumPair = setFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.STATES.getCode());
        return errorNumPair;
    }

    private Pair<Integer, Integer> setEmsHidePair(
            Pair<Integer, Integer> errorNumPair, JSONObject equip, List<String> list) {
        errorNumPair =
                setHideFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.PCS.getCode(), list);
        errorNumPair =
                setHideFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.BMS.getCode(), list);
        errorNumPair =
                setHideFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.AIR.getCode(), list);
        errorNumPair =
                setHideFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.FIRE.getCode(), list);
        errorNumPair =
                setHideFaultAndAlarm(
                        errorNumPair, equip, EquipNumberEnum.WATER_COOLER.getCode(), list);
        errorNumPair = setHideFaultAndAlarm(errorNumPair, equip, "system", list);
        errorNumPair =
                setHideFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.DCDC.getCode(), list);
        errorNumPair =
                setHideFaultAndAlarm(errorNumPair, equip, EquipNumberEnum.STATES.getCode(), list);
        return errorNumPair;
    }

    @NotNull
    private static EventMessageRequest createEventMessageRequest(long now) {
        EventMessageRequest eventMessageRequest = new EventMessageRequest();
        eventMessageRequest.setPageNum(1);
        eventMessageRequest.setPageSize(1);
        eventMessageRequest.setStatus(List.of("0"));
        eventMessageRequest.setEventType(List.of("Fault", "Alarm"));
        eventMessageRequest.setStartDate(now - 30 * MyTimeUtil.ONE_DAY_SECONDS * 1000);
        eventMessageRequest.setEndDate(now);
        return eventMessageRequest;
    }

    @NotNull
    private AuthUser getSuperUser() {
        UserEntity userEntity =
                userService
                        .lambdaQuery()
                        .eq(UserEntity::getUserName, EmsConstants.WH_ADMIN_USERNAME)
                        .one();
        userEntity.setId("1");
        return new AuthUser(
                userEntity,
                new LinkedHashSet<>(
                        authorityService.list().stream()
                                .map(AuthorityEntity::getApKey)
                                .collect(Collectors.toSet())));
    }

    private List<ProjectEntity> getLocalProjectList() {
        return projectService
                .lambdaQuery()
                .eq(ProjectEntity::getWhetherDelete, false)
                .eq(ProjectEntity::getProjectModel, 1)
                .list();
    }

    public Pair<Integer, Integer> setFaultAndAlarm(
            Pair<Integer, Integer> pair, JSONObject equip, String type) {
        JSONObject status = equip.getJSONObject(type);
        int fault = pair.getFirst();
        int alarm = pair.getSecond();
        if (status == null) {
            return Pair.of(pair.getFirst(), pair.getSecond());
        }
        for (String key : status.keySet()) {
            fault += (int) status.getJSONObject(key).get("fault_count");
            alarm += (int) status.getJSONObject(key).get("alarm_count");
        }
        return Pair.of(fault, alarm);
    }

    public Pair<Integer, Integer> setHideFaultAndAlarm(
            Pair<Integer, Integer> pair, JSONObject equip, String type, List<String> hideCodelist) {
        JSONObject status = equip.getJSONObject(type);
        int fault = pair.getFirst();
        int alarm = pair.getSecond();
        if (status == null) {
            return Pair.of(pair.getFirst(), pair.getSecond());
        }
        for (String key : status.keySet()) {
            List<EventCodeEntity> faultList;
            JSONArray jsonArray = status.getJSONObject(key).getJSONArray("fault");
            if (jsonArray != null) {
                faultList = jsonArray.toJavaList(EventCodeEntity.class);
            } else {
                // 处理"fault"不存在或不是数组的情况
                faultList = new ArrayList<>();
            }
            for (EventCodeEntity eventCodeEntity : faultList) {
                if (hideCodelist.stream()
                        .anyMatch(
                                hideEventCode ->
                                        hideEventCode.equals(eventCodeEntity.getEventCode()))) {
                    fault++;
                }
            }
            List<EventCodeEntity> alarmList;
            JSONArray jsonAlarmArray = status.getJSONObject(key).getJSONArray("alarm");
            if (jsonAlarmArray != null) {
                alarmList = jsonAlarmArray.toJavaList(EventCodeEntity.class);
            } else {
                // 处理"fault"不存在或不是数组的情况
                alarmList = new ArrayList<>();
            }
            for (EventCodeEntity eventCodeEntity : alarmList) {
                if (hideCodelist.stream()
                        .anyMatch(
                                hideEventCode ->
                                        hideEventCode.equals(eventCodeEntity.getEventCode()))) {
                    alarm++;
                }
            }
        }
        return Pair.of(fault, alarm);
    }

    public void updateProjectExtInfo(int ems, int pcs, int bms, String projectId) {
        projectExtService.update(
                Wrappers.lambdaUpdate(ProjectExtEntity.class)
                        .set(ProjectExtEntity::getEms, ems)
                        .set(ProjectExtEntity::getPcs, pcs)
                        .set(ProjectExtEntity::getBms, bms)
                        .eq(ProjectExtEntity::getId, projectId));
    }

    public void updateProjectInfo(
            int fault,
            int alarm,
            int status,
            String projectId,
            boolean whetherLocal,
            int unConfirmNotMaintain,
            int unConfirmAll,
            int hideFault,
            int hideAlarm) {
        if (!whetherLocal) {
            long end = Instant.now().toEpochMilli();
            long start = end - 30 * 1000 * MyTimeUtil.ONE_DAY_SECONDS;
            List<EventMessageEntity> messageEntities =
                    eventMessageService
                            .lambdaQuery()
                            .eq(EventMessageEntity::getProjectId, projectId)
                            .eq(EventMessageEntity::getStatus, 0)
                            .in(EventMessageEntity::getEventType, List.of("Fault", "Alarm"))
                            .eq(EventMessageEntity::getWhetherDelete, false)
                            .ge(EventMessageEntity::getCreateTime, start)
                            .le(EventMessageEntity::getCreateTime, end)
                            .list();
            unConfirmAll = messageEntities.size();
            unConfirmNotMaintain =
                    (int)
                            messageEntities.stream()
                                    .filter(e -> !Boolean.TRUE.equals(e.getMaintain()))
                                    .count();
        }
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setId(projectId);
        projectEntity.setFault(fault);
        projectEntity.setAlarm(alarm);
        projectEntity.setStatus(status);
        projectEntity.setOrder(
                getProjectOrderByStatus(fault, alarm, status, unConfirmAll > 0, whetherLocal));
        projectEntity.setOrderOne(
                getProjectOrderOneByStatus(fault, alarm, status, unConfirmAll > 0, whetherLocal));
        projectEntity.setUnConfirmAll(unConfirmAll);
        projectEntity.setUnConfirmNotMaintain(unConfirmNotMaintain);
        projectEntity.setNonHideFault(fault - hideFault);
        projectEntity.setNonHideAlarm(alarm - hideAlarm);
        projectEntity.setOrderShield(
                getProjectOrderByStatus(
                        fault - hideFault,
                        alarm - hideAlarm,
                        status,
                        unConfirmAll > 0,
                        whetherLocal));
        projectEntity.setOrderOneShield(
                getProjectOrderOneByStatus(
                        fault - hideFault,
                        alarm - hideAlarm,
                        status,
                        unConfirmAll > 0,
                        whetherLocal));
        //        log.error(
        //                "updateProjectInfo: projectId {}, order {}, fault{}, alarm{},unconfirmall
        // {},unconfirmMaintian {}",
        //                projectId,
        //                getProjectOrderByStatus(fault, alarm, status, unConfirmAll > 0,
        // whetherLocal),
        //                fault,
        //                alarm,
        //                unConfirmAll,
        //                unConfirmNotMaintain);
        projectService.updateById(projectEntity);
    }

    public int getProjectOrderByStatus(
            int fault, int alarm, int status, boolean hasUnConfirm, boolean whetherLocal) {
        // 故障1/告警2 >断网项目3>离线项目4>停机项目5>充电/放电/待机6（按时间倒序）>待调7
        // 页签页面排序：断网（云）、故障（云/本地）、部件离线（云/本地）、告警（云/本地）、停机（云/本地）、正常(有未确认)（云/本地）、正常(无未确认)（云/本地）、断网（本地）、待调（云/本地）
        // 云端 页签页面排序：断网（云）、故障（云）、部件离线（云）、告警（云）、停机（云）、正常(有未确认)（云）、正常(无未确认)（云）、待调（云）
        // 本地 页签页面排序：故障（本地）、部件离线（本地）、告警（本地）、停机（本地）、正常(有未确认)（本地）、正常(无未确认)（本地）、断网（本地）、待调（本地）

        // 断网（1，） 故障（2，3） 离线（4，5） 告警（6，7） 停机（8，9） 正常(有未确认)（10，11） 正常(无未确认)(12,13) 断网本地 14 待调（15，16

        // OFFLINE(0)离线, STANDBY(1), CHARGING(2), DISCHARGING(3), STOP(4)停机, Fault(5),
        // UNINSTALL(8)待调, UNKNOWN(9)断网;

        // 正常（无未确认） 正常（有确认） 断网（云） 故障 部件离线 告警 停机 断网（本地） 待调
        // 正常（无未确认）[1,2] 正常（有确认）[3,4]  断网（云）[5,]  故障[6,7]  部件离线[8,9]  告警[10,11]  停机[12,13]
        // 断网（本地）[,14] 待调[15,16]

        int order;
        switch (status) {
                // 断网
            case 9:
                if (!whetherLocal) {
                    order = 1;
                } else {
                    order = 14;
                }
                break;
                // 离线
            case 0:
                if (!whetherLocal) {
                    order = 4;
                } else {
                    order = 5;
                }
                break;
                // 停机
            case 4:
                if (!whetherLocal) {
                    order = 8;
                } else {
                    order = 9;
                }
                break;
                // 待调
            case 8:
                if (!whetherLocal) {
                    order = 15;
                } else {
                    order = 16;
                }
                break;
            default:
                // 正常有未确认
                if (hasUnConfirm) {
                    if (!whetherLocal) {
                        order = 10;
                    } else {
                        order = 11;
                    }
                } else {
                    // 正常无未确认
                    if (!whetherLocal) {
                        order = 12;
                    } else {
                        order = 13;
                    }
                }
        }
        // 告警
        if (alarm > 0) {
            if (!whetherLocal) {
                order = 6;
            } else {
                order = 7;
            }
        }
        // 故障
        if (fault > 0) {
            if (!whetherLocal) {
                order = 2;
            } else {
                order = 3;
            }
        }
        return order;
    }

    public int getProjectOrderOneByStatus(
            int fault, int alarm, int status, boolean hasUnConfirm, boolean whetherLocal) {

        // 正常（无未确认） 正常（有确认） 断网（云） 故障 部件离线 告警 停机 断网（本地） 待调
        // 正常（无未确认）[1,2] 正常（有确认）[3,4]  断网（云）[5,]  故障[6,7]  部件离线[8,9]  告警[10,11]  停机[12,13]
        // 断网（本地）[,14] 待调[15,16]

        int order;
        switch (status) {
                // 断网
            case 9:
                if (!whetherLocal) {
                    order = 5;
                } else {
                    order = 14;
                }
                break;
                // 离线
            case 0:
                if (!whetherLocal) {
                    order = 8;
                } else {
                    order = 9;
                }
                break;
                // 停机
            case 4:
                if (!whetherLocal) {
                    order = 12;
                } else {
                    order = 13;
                }
                break;
                // 待调
            case 8:
                if (!whetherLocal) {
                    order = 15;
                } else {
                    order = 16;
                }
                break;
            default:
                // 正常有未确认
                if (hasUnConfirm) {
                    if (!whetherLocal) {
                        order = 3;
                    } else {
                        order = 4;
                    }
                } else {
                    // 正常无未确认
                    if (!whetherLocal) {
                        order = 1;
                    } else {
                        order = 2;
                    }
                }
        }
        // 告警
        if (alarm > 0) {
            if (!whetherLocal) {
                order = 10;
            } else {
                order = 11;
            }
        }
        // 故障
        if (fault > 0) {
            if (!whetherLocal) {
                order = 6;
            } else {
                order = 7;
            }
        }
        return order;
    }
}
