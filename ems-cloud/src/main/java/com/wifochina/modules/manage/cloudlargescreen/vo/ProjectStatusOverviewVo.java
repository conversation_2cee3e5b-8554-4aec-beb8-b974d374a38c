package com.wifochina.modules.manage.cloudlargescreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2023/10/11 16:14.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProjectStatusOverviewVo {

    @ApiModelProperty(value = "离线数量")
    private Long offLineCount;
    @ApiModelProperty(value = "运行数量")
    private Long runningCount;
    @ApiModelProperty(value = "告警中的项目 & 故障中的项目")
    private Long alarmFaultCount;

}
