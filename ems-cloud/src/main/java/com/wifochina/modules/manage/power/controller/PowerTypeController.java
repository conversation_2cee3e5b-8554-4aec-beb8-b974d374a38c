package com.wifochina.modules.manage.power.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.page.Result;
import com.wifochina.modules.manage.power.entity.PowerTypeEntity;
import com.wifochina.modules.manage.power.request.PowerRequest;
import com.wifochina.modules.manage.power.service.PowerTypeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@RequestMapping("/manage/type")
@RestController
@Api(tags = "manage-03-项目电力类型")
public class PowerTypeController {

    @Autowired
    private PowerTypeService powerTypeService;

    @PostMapping("/list")
    @ApiOperation("manage-项目电力类型列表")
    public Result<List<PowerTypeEntity>> list() {
        List<PowerTypeEntity> list = powerTypeService.list();
        return Result.success(list);
    }

    @PostMapping("/query")
    @ApiOperation("manage-根据id查询电力类型")
    public Result<PowerTypeEntity> query(@RequestBody PowerRequest powerRequest) {
        return Result.success(powerTypeService.getById(powerRequest.getUuid()));
    }
}
