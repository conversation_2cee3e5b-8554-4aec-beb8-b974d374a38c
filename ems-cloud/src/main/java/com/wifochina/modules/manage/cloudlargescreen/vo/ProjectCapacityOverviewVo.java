package com.wifochina.modules.manage.cloudlargescreen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2023/10/17 13:56.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProjectCapacityOverviewVo {

    /**
     * 所有项目的 电站额定容量
     */
    @ApiModelProperty(value = "所有项目的 电站额定容量")
    private Long sumEmsCapacity;
    /**
     * 今年新增的 所有项目的 电站额定容量
     */
    @ApiModelProperty(value = "今年新增项目的 电站额定容量")
    private Long currentYearEmsCapacity;

    /**
     * 所有项目的 电站额定功率
     */
    @ApiModelProperty(value = "所有项目的 电站额定功率")
    private Long sumEmsDesignPower;
    /**
     * 今年新增的 所有项目的 电站额定功率
     */
    @ApiModelProperty(value = "今年新增项目的 电站额定功率")
    private Long currentYearEmsDesignPower;

    /**
     * 电站数 当前账户绑定的
     */
    @ApiModelProperty(value = "电站数 当前账户绑定的")
    private Integer powerStationNum;

    /**
     * 今年新增的 电站数 当前账户绑定的
     */
    @ApiModelProperty(value = "今年新增的项目的电站数 当前账户绑定的")
    private Integer currentYearPowerStationNum;

    /**
     * 合计 充电量
     */
    @ApiModelProperty(value = "所有项目的合计 充电量")
    private Double sumChargeQuantity;
    /**
     * 合计 放电量
     */
    @ApiModelProperty(value = "所有项目的合计 放电量")
    private Double sumDischargeQuantity;
    /**
     * 当前年份的 充电量
     */
    @ApiModelProperty(value = "今年新增项目的合计 充电量")
    private Double currentYearChargeQuantity;
    /**
     * 当前年份的 放电量
     */
    @ApiModelProperty(value = "今年新增项目的合计 放电量")
    private Double currentYearDischargeQuantity;

}
