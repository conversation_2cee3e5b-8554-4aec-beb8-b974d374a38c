package com.wifochina.modules.manage.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.handler.MessageSourceHandler;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.req.UserProjectReq;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.project.service.ProjectServiceKt;
import com.wifochina.modules.user.entity.LogEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.entity.UserProjectEntity;
import com.wifochina.modules.user.request.*;
import com.wifochina.modules.user.service.LogService;
import com.wifochina.modules.user.service.UserProjectService;
import com.wifochina.modules.user.service.UserService;
import com.wifochina.modules.user.vo.UserInfoVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.context.NoSuchMessageException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = "manage-05-用户中心")
@RestController
@RequestMapping("/manage/user")
@Slf4j
public class ManageUserController {

    @Resource private UserService userService;

    @Resource private UserProjectService userProjectService;

    @Resource private LogService logService;

    @Resource private ProjectService projectService;

    @Resource private ProjectServiceKt projectServiceKt;

    @PostMapping("/update/password")
    @ApiOperation("manage-修改当前用户密码")
    @Log(module = "MANAGE_USER", methods = "MANAGE_USER_CH_PWD")
    public Result<Void> updatePassword(
            @RequestBody UserUpdatePasswordRequest updatePasswordRequest) {
        userService.updatePassword(updatePasswordRequest, true);
        return Result.success();
    }

    @PostMapping("/update/other/password")
    @ApiOperation("manage-修改其它用户密码")
    @PreAuthorize("hasAuthority('/manage/user/account')")
    @Log(module = "MANAGE_USER", methods = "MANAGE_USER_CH_OTHER_PWD")
    public Result<Void> updateOtherPassword(
            @RequestBody UserUpdatePasswordRequest updatePasswordRequest) {
        userService.updatePassword(updatePasswordRequest, false);
        return Result.success();
    }

    @PostMapping("/update")
    @ApiOperation("manage-修改用户")
    @PreAuthorize("hasAuthority('/manage/user/account')")
    @Log(module = "MANAGE_USER", methods = "MANAGE_USER_UPDATE", type = OperationType.UPDATE_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> update(@RequestBody UserUpdateRoleRequest userUpdateRoleRequest) {
        userService.updateUserManage(userUpdateRoleRequest);
        return Result.success();
    }

    @PostMapping("/add")
    @ApiOperation("manage-添加用户")
    @PreAuthorize("hasAuthority('/manage/user/account')")
    @Log(module = "MANAGE_USER", methods = "MANAGE_USER_ADD", type = OperationType.ADD_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> register(@RequestBody UserRegisterRequest userRegisterRequest) {
        userRegisterRequest.setType(EmsConstants.USER_ROLE_ADMIN);
        UserEntity userEntity =
                userService.getOne(
                        Wrappers.lambdaQuery(UserEntity.class)
                                .eq(UserEntity::getUserName, userRegisterRequest.getUserName()));
        if (userEntity != null) {
            throw new ServiceException(ErrorResultCode.USER_EXISTS.value());
        }
        userService.registerManage(userRegisterRequest);

        return Result.success();
    }

    /**
     * 获取登录用户详情
     *
     * @return Result<UserInfoVo>
     */
    @GetMapping("/getUserInfo")
    @ApiOperation("manage-获取登录用户详情")
    public Result<UserInfoVo> getUserInfo() {
        String userId = SecurityUtil.getUserId();
        return Result.success(userService.getUserInfo(userId));
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("manage-删除用户")
    @PreAuthorize("hasAuthority('/manage/user/account')")
    @Log(module = "MANAGE_USER", methods = "MANAGE_USER_DEL", type = OperationType.DEL_SIMPLE)
    public Result<Object> delete(@PathVariable("id") String id) {
        userService.removeUser(id);
        return Result.success();
    }

    @PostMapping("/getPagesUsers")
    @ApiOperation("manage-分页查询用户")
    @PreAuthorize("hasAuthority('/manage/user/account')")
    public Result<IPage<UserEntity>> getPagesUsers(@RequestBody UserPageRequest userRequestPage) {
        IPage<UserEntity> userPageBean =
                new Page<>(userRequestPage.getPageNum(), userRequestPage.getPageSize());
        IPage<UserEntity> userEntityPage;
        Set<String> userIds = null;
        if (userRequestPage.getProjectName() != null
                && !userRequestPage.getProjectName().trim().isEmpty()) {
            List<ProjectEntity> projectList =
                    projectService.list(
                            Wrappers.lambdaQuery(ProjectEntity.class)
                                    .eq(ProjectEntity::getWhetherDelete, false)
                                    .like(
                                            ProjectEntity::getProjectName,
                                            userRequestPage.getProjectName().trim()));
            if (projectList.isEmpty()) {
                return Result.success(null);
            } else {
                List<String> projectIds =
                        projectList.stream().map(ProjectEntity::getId).collect(Collectors.toList());
                List<UserProjectEntity> userProjectEntityList =
                        userProjectService.list(
                                Wrappers.lambdaQuery(UserProjectEntity.class)
                                        .in(UserProjectEntity::getProjectId, projectIds));
                userIds =
                        userProjectEntityList.stream()
                                .map(UserProjectEntity::getUserId)
                                .collect(Collectors.toSet());
                // 用户合并(查询所有项目的用户+分配给这个用户)
                List<UserEntity> allUserList =
                        userService.list(
                                Wrappers.lambdaQuery(UserEntity.class)
                                        .eq(UserEntity::getAllProject, true));
                Set<String> allUserIds =
                        allUserList.stream().map(UserEntity::getId).collect(Collectors.toSet());
                userIds.addAll(allUserIds);
            }
        }
        LambdaQueryWrapper<UserEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.ne(UserEntity::getRole, EmsConstants.USER_ROLE_CLIENT);
        if (StringUtils.hasLength(userRequestPage.getName())) {
            lambdaQueryWrapper.and(
                    i ->
                            i.like(UserEntity::getUserName, userRequestPage.getName())
                                    .or()
                                    .like(UserEntity::getName, userRequestPage.getName()));
        }
        lambdaQueryWrapper.eq(
                StringUtils.hasLength(userRequestPage.getRole()),
                UserEntity::getRoleName,
                userRequestPage.getRole());
        lambdaQueryWrapper.in(userIds != null, UserEntity::getId, userIds);
        if (!EmsConstants.WH_ADMIN_USERNAME.equals(SecurityUtil.getUsername())) {
            lambdaQueryWrapper.ne(UserEntity::getRole, EmsConstants.USER_ROLE_SUPER);
        }
        userEntityPage =
                userService.page(
                        userPageBean, lambdaQueryWrapper.orderByDesc(UserEntity::getCreateTime));
        for (UserEntity userEntity : userEntityPage.getRecords()) {
            if (!userEntity.getAllProject()) {
                List<UserProjectEntity> userProjectEntities =
                        userProjectService.list(
                                Wrappers.lambdaQuery(UserProjectEntity.class)
                                        .eq(UserProjectEntity::getUserId, userEntity.getId()));
                List<String> projectIds =
                        userProjectEntities.stream()
                                .map(UserProjectEntity::getProjectId)
                                .collect(Collectors.toList());
                if (!projectIds.isEmpty()) {
                    List<ProjectEntity> projectEntities =
                            projectService.list(
                                    Wrappers.lambdaQuery(ProjectEntity.class)
                                            .eq(ProjectEntity::getWhetherDelete, false)
                                            .in(ProjectEntity::getId, projectIds));
                    Map<String, String> projectMap = new HashMap<>(projectEntities.size());
                    for (ProjectEntity projectEntity : projectEntities) {
                        projectMap.put(projectEntity.getId(), projectEntity.getProjectName());
                    }
                    userEntity.setProjectMap(projectMap);
                }
            }
        }
        return Result.success(userEntityPage);
    }

    @PostMapping("/getLogs")
    @ApiOperation("manage-查询操作记录")
    @PreAuthorize("hasAuthority('/manage/user/getLogs')")
    public Result<IPage<LogEntity>> getLogs(@RequestBody LogPageRequest logPageRequest) {
        IPage<LogEntity> logPage =
                new Page<>(logPageRequest.getPageNum(), logPageRequest.getPageSize());
        IPage<LogEntity> logList =
                logService.page(
                        logService.page(
                                logPage,
                                new QueryWrapper<LogEntity>()
                                        .lambda()
                                        .eq(LogEntity::getUserId, logPageRequest.getUserId())
                                        .orderByDesc(LogEntity::getRequestTime)));
        logList.getRecords()
                .forEach(
                        logEntity -> {
                            try {
                                logEntity.setModule(
                                        MessageSourceHandler.getMessage(logEntity.getModule()));
                                logEntity.setMethod(
                                        MessageSourceHandler.getMessage(logEntity.getMethod()));
                            } catch (NoSuchMessageException e) {
                                log.info(
                                        "[language error]----------------------------->"
                                                + e.getMessage());
                            }
                        });
        return Result.success(logList);
    }

    @PostMapping("/updatePhone")
    @ApiOperation("manage-修改其它用户手机")
    @PreAuthorize("hasAuthority('/manage/user/account')")
    @Log(
            module = "MANAGE_USER",
            methods = "MANAGE_USER_CH_OTHER_PHONE",
            type = OperationType.UPDATE_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updatePhone(@RequestBody UserOtherPhoneRequest userOtherPhoneRequest) {
        userService.updateOtherPhone(userOtherPhoneRequest);
        return Result.success();
    }

    @PostMapping("/updateEmail")
    @ApiOperation("manage-修改其它用户邮箱")
    @PreAuthorize("hasAuthority('/manage/user/account')")
    @Log(
            module = "MANAGE_USER",
            methods = "MANAGE_USER_CH_OTHER_EMAIL",
            type = OperationType.UPDATE_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateEmail(
            @Validated @RequestBody UserOtherEmailRequest userOtherEmailRequest) {
        userService.updateOtherEmail(userOtherEmailRequest);
        return Result.success();
    }

    /** init project */
    @PostMapping("/getSpecificUserProjects")
    @ApiOperation("获取指定用户所有项目")
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> getSpecificUserProjects(@RequestBody UserProjectReq userProjectReq) {
        if (SecurityUtil.getAuthStat() == null || SecurityUtil.getAuthStat() != 1) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ACCESS.value());
        }
        if (!StringUtils.hasLength(userProjectReq.getUserId())
                || !StringUtils.hasLength(userProjectReq.getRoleId())) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        Map<String, String> map =
                projectServiceKt.getProjectListByUserIdAndRoleId(
                        userProjectReq.getUserId(), userProjectReq.getRoleId());
        removeDeleteProject(map);
        return Result.success(map);
    }

    private void removeDeleteProject(Map<String, String> map) {
        if (map != null && !map.isEmpty()) {
            List<String> deletedProjectId =
                    projectService
                            .lambdaQuery()
                            .eq(ProjectEntity::getWhetherDelete, true)
                            .list()
                            .stream()
                            .map(ProjectEntity::getId)
                            .collect(Collectors.toList());
            deletedProjectId.forEach(map::remove);
        }
    }

    /** init project */
    @PostMapping("/getCurrentUserProjects")
    @ApiOperation("获取当前用户所有项目")
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> getCurrentUserProjects() {
        if (SecurityUtil.getAuthStat() == null || SecurityUtil.getAuthStat() != 1) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ACCESS.value());
        }
        Map<String, String> map =
                projectServiceKt.getProjectListByUserIdAndRoleId(
                        Objects.requireNonNull(SecurityUtil.getUserId()),
                        Objects.requireNonNull(SecurityUtil.getLoginRoleId()));
        removeDeleteProject(map);
        return Result.success(map);
    }
}
