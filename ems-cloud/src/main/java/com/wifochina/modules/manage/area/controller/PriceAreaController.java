package com.wifochina.modules.manage.area.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.area.entity.PriceAreaEntity;
import com.wifochina.modules.area.request.PriceAreaPageRequest;
import com.wifochina.modules.area.request.PriceAreaRequest;
import com.wifochina.modules.area.service.PriceAreaService;
import com.wifochina.modules.area.vo.PriceAreaVo;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.PriceTemplateLogDetailService;
import com.wifochina.modules.manage.area.entity.AreaEntity;
import com.wifochina.modules.manage.area.service.AreaService;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@RequestMapping("/manage/project")
@RestController
@Api(tags = "manage-04-项目区域管理")
public class PriceAreaController {

    @Resource private PriceAreaService priceAreaService;

    @Resource private AreaService areaService;

    @Resource private LogService logService;

    @PostMapping("/area/create")
    @ApiOperation("manage-区域创建")
    @PreAuthorize("hasAuthority('/manage/area/create')")
    @Log(module = "MANAGE_AREA", type = OperationType.ADD)
    public Result<PriceAreaEntity> create(@RequestBody PriceAreaRequest priceAreaRequest) {
        // 实时电价区域的 需要去配置 国家 区域 以及 span
        if (priceAreaRequest.getPriceType() != null) {
            if (priceAreaRequest
                    .getPriceType()
                    .equals(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name())) {
                String electricPriceArea = priceAreaRequest.getElectricPriceArea();
                String electricPriceSpan = priceAreaRequest.getElectricPriceSpan();
                Integer countryId = priceAreaRequest.getCountryId();
                if (StringUtil.isEmpty(electricPriceArea)
                        || StringUtil.isEmpty(electricPriceSpan)
                        || countryId == null) {
                    throw new ServiceException(ErrorResultCode.DYNAMIC_PRICE_NEED_PARAMS.value());
                }
            }
        }
        PriceAreaEntity priceAreaEntity =
                priceAreaService
                        .lambdaQuery()
                        .eq(PriceAreaEntity::getArea, priceAreaRequest.getArea())
                        .one();
        ServiceAssert.isTrue(priceAreaEntity == null, ErrorResultCode.AREA_EXISTS.value());
        PriceAreaEntity areaEntity = new PriceAreaEntity();
        BeanUtils.copyProperties(priceAreaRequest, areaEntity);
        areaEntity.setId(StringUtil.uuid());
        priceAreaService.save(areaEntity);
        //        logService.logAddDetail(
        //                LogInfo.builder()
        //                        .module("MANAGE_AREA")
        //                        .method("MANAGE_AREA_ADD")
        //                        .object(priceAreaRequest)
        //                        .traceId(String.format(LogService.AREA_TRACE_FORMAT,
        // areaEntity.getId()))
        //                        .build());
        return Result.success(areaEntity);
    }

    @PostMapping("/area/update")
    @ApiOperation("manage-区域更新")
    @PreAuthorize("hasAuthority('/manage/area/update')")
    @Log(module = "MANAGE_AREA", type = OperationType.UPDATE)
    public Result<PriceAreaEntity> update(@RequestBody PriceAreaRequest priceAreaRequest) {
        PriceAreaEntity priceAreaEntity =
                priceAreaService
                        .lambdaQuery()
                        .eq(PriceAreaEntity::getArea, priceAreaRequest.getArea())
                        .one();
        if (priceAreaEntity != null && !priceAreaEntity.getId().equals(priceAreaRequest.getId())) {
            throw new ServiceException(ErrorResultCode.AREA_EXISTS.value());
        }
        PriceAreaEntity areaEntity = new PriceAreaEntity();
        BeanUtils.copyProperties(priceAreaRequest, areaEntity);
        priceAreaService.updateById(areaEntity);
        //        logService.logUpdateDetail(
        //                LogInfo.builder()
        //                        .module("MANAGE_AREA")
        //                        .method("MANAGE_AREA_UPDATE")
        //                        .object(priceAreaRequest)
        //                        .traceId(
        //                                String.format(
        //                                        LogService.AREA_TRACE_FORMAT,
        // priceAreaRequest.getId()))
        //                        .build());
        return Result.success(areaEntity);
    }

    @PostMapping("/area/delete")
    @ApiOperation("manage-区域删除")
    @PreAuthorize("hasAuthority('/manage/area/delete')")
    @Log(
            module = "MANAGE_AREA",
            type = OperationType.DEL,
            logDetailServiceClass = PriceTemplateLogDetailService.class)
    public Result<Object> delete(
            @RequestParam @ApiParam(required = true, name = "id", value = "区域类型id") String id) {
        PriceAreaEntity priceAreaEntity = priceAreaService.getById(id);
        priceAreaService.removeById(id);
        //        logService.logDeleteDetail(
        //                LogInfo.builder()
        //                        .module("MANAGE_AREA")
        //                        .method("MANAGE_AREA_DEL")
        //                        .object(id)
        //                        .delDetail(Map.of(id, priceAreaEntity.getArea()))
        //                        .traceId(String.format(LogService.AREA_TRACE_FORMAT, id))
        //                        .build());
        return Result.success();
    }

    @PostMapping("/area/query")
    @ApiOperation("manage-区域查询")
    @PreAuthorize("hasAuthority('/manage/area/query') or hasAuthority('/manage/project')")
    public Result<IPage<PriceAreaVo>> query(
            @RequestBody PriceAreaPageRequest priceAreaPageRequest) {
        IPage<PriceAreaVo> priceAreaPage =
                new Page<>(priceAreaPageRequest.getPageNum(), priceAreaPageRequest.getPageSize());
        return Result.success(priceAreaService.queryPriceArea(priceAreaPage));
    }

    @GetMapping("/province/query")
    @ApiOperation("manage-省份查询")
    public Result<Map<Integer, String>> listProvince() {
        List<AreaEntity> list =
                areaService
                        .lambdaQuery()
                        .eq(AreaEntity::getType, 0)
                        .or()
                        .eq(AreaEntity::getType, 1)
                        .list();
        Map<Integer, String> map =
                list.stream().collect(Collectors.toMap(AreaEntity::getId, AreaEntity::getCityName));
        return Result.success(map);
    }

    @GetMapping("/province/queryAllColumn")
    @ApiOperation("manage-省份全字段查询")
    public Result<List<AreaEntity>> listProvinceEntity() {
        List<AreaEntity> list =
                areaService
                        .lambdaQuery()
                        .eq(AreaEntity::getType, 0)
                        .or()
                        .eq(AreaEntity::getType, 1)
                        .list();
        return Result.success(list);
    }
}
