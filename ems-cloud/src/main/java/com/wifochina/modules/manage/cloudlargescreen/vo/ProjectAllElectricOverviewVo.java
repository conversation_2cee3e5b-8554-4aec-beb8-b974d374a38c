package com.wifochina.modules.manage.cloudlargescreen.vo;

import com.wifochina.modules.collect.CacheElectricCollectVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2025/2/28 15:11.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProjectAllElectricOverviewVo {
    @ApiModelProperty(value = "电缓存")
    private CacheElectricCollectVo electricCollectVo = new CacheElectricCollectVo();

    @ApiModelProperty(value = "碳减排")
    private String carbonReduction = "0.0";
}
