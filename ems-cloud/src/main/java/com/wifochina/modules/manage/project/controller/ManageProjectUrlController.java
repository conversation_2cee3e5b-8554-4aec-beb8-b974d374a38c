package com.wifochina.modules.manage.project.controller;

import java.util.List;

import javax.annotation.Resource;

import com.wifochina.modules.log.OperationType;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.project.entity.ProjectUrlEntity;
import com.wifochina.modules.project.request.ProjectUrlRequest;
import com.wifochina.modules.project.service.ProjectUrlService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-19
 */

@RequestMapping("/manage/project/url")
@RestController
@Api(tags = "manage-01-项目管理")
@Slf4j
public class ManageProjectUrlController {

    @Resource
    private ProjectUrlService projectUrlService;

    /**
     * 创建项目url
     */
    @PostMapping("/create")
    @ApiOperation("manage-项目url创建")
    @Log(module = "MANAGE_PROJECT", methods = "MANAGE_PROJECT_URL_ADD", type = OperationType.ADD)
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/manage/project/create')")
    public Result<Object> create(@RequestBody ProjectUrlRequest projectUrlRequest) {
        ProjectUrlEntity projectUrlEntity = new ProjectUrlEntity();
        BeanUtils.copyProperties(projectUrlRequest, projectUrlEntity);
        projectUrlEntity.setId(StringUtil.uuid());
        projectUrlService.save(projectUrlEntity);
        return Result.success();
    }

    /**
     * 修改项目url
     */
    @PostMapping("/update")
    @ApiOperation("manage-项目url更新")
    @Log(module = "MANAGE_PROJECT", methods = "MANAGE_PROJECT_URL_UPDATE", type = OperationType.UPDATE)
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/manage/project/update')")
    public Result<Object> update(@RequestBody ProjectUrlRequest projectUrlRequest) {
        ProjectUrlEntity projectUrlEntity = new ProjectUrlEntity();
        BeanUtils.copyProperties(projectUrlRequest, projectUrlEntity);
        projectUrlService.updateById(projectUrlEntity);
        return Result.success();
    }

    /**
     * 修改项目url
     */
    @PostMapping("/delete")
    @ApiOperation("manage-项目url删除")
    @Log(module = "MANAGE_PROJECT", methods = "MANAGE_PROJECT_URL_DELETE", type = OperationType.DEL_SIMPLE)
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/manage/project/update')")
    public Result<Object> delete(@RequestBody ProjectUrlRequest projectUrlRequest) {
        projectUrlService.removeById(projectUrlRequest.getId());
        return Result.success();
    }

    /**
     * 查询项目url
     */
    @PostMapping("/list")
    @ApiOperation("manage-项目url列表")
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("hasAuthority('/manage/project/query')")
    public Result<List<ProjectUrlEntity>> list(@RequestBody ProjectUrlRequest projectUrlRequest) {
        List<ProjectUrlEntity> list =
            projectUrlService.lambdaQuery().eq(ProjectUrlEntity::getProjectId, projectUrlRequest.getProjectId())
                .orderByAsc(ProjectUrlEntity::getOrderNum).list();
        return Result.success(list);
    }

}
