package com.wifochina.modules.manage.user.controller;

import java.util.List;

import com.wifochina.modules.log.OperationType;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.PageBean;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.user.entity.RoleEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.request.RoleRequest;
import com.wifochina.modules.user.service.RoleService;
import com.wifochina.modules.user.service.UserService;
import com.wifochina.modules.user.vo.RoleVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * RoleController
 *
 * <AUTHOR>
 * @version 1.0
 * @date 3/25/2022 5:29 PM
 */
@RestController
@RequestMapping("/manage/role")
@Api(tags = "manage-05-角色管理")
@RequiredArgsConstructor
public class ManageRoleController {

    private final RoleService roleService;

    private final UserService userService;

    private final RedisTemplate<String, String> redisTemplate;

    @PostMapping("/create")
    @ApiOperation("manage-新增角色")
    @PreAuthorize("hasAuthority('/manage/user/role')")
    @Log(module = "MANAGE_ROLE", methods = "MANAGE_ROLE_ADD", type = OperationType.ADD)
    public Result<Object> create(@RequestBody RoleRequest roleRequest) {
        roleService.createRole(roleRequest, EmsConstants.COMMON);
        return Result.success();
    }

    @PostMapping("/update")
    @ApiOperation("manage-修改角色")
    @PreAuthorize("hasAuthority('/manage/user/role')")
    @Log(module = "MANAGE_ROLE", methods = "MANAGE_ROLE_UPDATE", type = OperationType.UPDATE)
    public Result<Object> update(@RequestBody RoleRequest roleRequest) {
        roleService.updateRole(roleRequest, EmsConstants.COMMON);
        logout(roleRequest.getId());
        return Result.success();
    }

    @GetMapping("/delete/{roleId}")
    @ApiOperation("manage-删除角色")
    @PreAuthorize("hasAuthority('/manage/user/role')")
    @Log(module = "MANAGE_ROLE", methods = "MANAGE_ROLE_DEL", type = OperationType.DEL_SIMPLE)
    public Result<Object> delete(@PathVariable("roleId") String roleId) {
        roleService.deleteRole(roleId);
        logout(roleId);
        return Result.success();
    }

    public void logout(String roleId) {
        List<UserEntity> userEntities =
            userService.lambdaQuery().eq(UserEntity::getRoleId, roleId).eq(UserEntity::getIsDelete, false).list();
        for (UserEntity userEntity : userEntities) {
            redisTemplate.delete(userEntity.getId());
        }
    }

    @PostMapping("/getPagesRoles")
    @ApiOperation("manage-分页查询角色")
    @PreAuthorize("hasAuthority('/manage/user/role')")
    public Result<IPage<RoleVO>> getPagesRoles(@RequestBody PageBean pageBean) {
        IPage<RoleVO> roleVoPage = roleService.getPagesRoles(pageBean, EmsConstants.COMMON);
        return Result.success(roleVoPage);
    }

    @PostMapping("/getAllRoles")
    @ApiOperation("manage-查询所有角色")
    @PreAuthorize("hasAuthority('/manage/user/role')")
    public Result<List<RoleEntity>> getAllRoles() {
        return Result.success(
            roleService.list(Wrappers.lambdaQuery(RoleEntity.class).eq(RoleEntity::getProjectId, EmsConstants.COMMON)));
    }

}
