package com.wifochina.modules.manage.price.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.modules.manage.price.service.ElectricPriceCloudService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.mapper.ElectricPriceMapper;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Service
public class ElectricPriceCloudServiceImpl
        extends ServiceImpl<ElectricPriceMapper, ElectricPriceEntity>
        implements ElectricPriceCloudService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePrice(List<ElectricPriceEntity> electricPriceCloudList) {
        // 删除旧的价格配置
        getBaseMapper()
                .delete(
                        Wrappers.lambdaQuery(ElectricPriceEntity.class)
                                .isNull(ElectricPriceEntity::getPriceTemplateId)
                                .eq(
                                        ElectricPriceEntity::getProjectId,
                                        electricPriceCloudList.get(0).getProjectId()));
        if (!electricPriceCloudList.isEmpty()) {
            // 如果日期不符合规则，直接抛出异常
            checkDate(electricPriceCloudList);

            // 先检查时间是否排满，再进行间隔配置的插入
            for (ElectricPriceEntity electricityPrice : electricPriceCloudList) {
                List<ElectricPriceEntity> list = electricityPrice.getPeriodPriceList();
                if (!list.isEmpty()) {
                    // 取第一个节点作为前一个节点
                    // 将时间段按照开始时间进行排序
                    list =
                            list.stream()
                                    .sorted(
                                            Comparator.comparing(ElectricPriceEntity::getStartTime)
                                                    .thenComparing(ElectricPriceEntity::getEndTime))
                                    .collect(Collectors.toList());
                    ElectricPriceEntity electricityPrice1 = list.get(0);
                    for (int i = 1; i < list.size(); i++) {
                        // 当前节点
                        ElectricPriceEntity electricityPrice2 = list.get(i);
                        ServiceAssert.isTrue(
                                electricityPrice1
                                                .getEndTime()
                                                .compareTo(electricityPrice2.getStartTime())
                                        <= 0,
                                ErrorResultCode.TIME_OVERLAPPED.value());
                        ServiceAssert.isTrue(
                                electricityPrice1
                                                .getEndTime()
                                                .compareTo(electricityPrice2.getStartTime())
                                        >= 0,
                                ErrorResultCode.TIME_IS_NOT_FULL.value());
                        if (list.size() - 1 == i) {
                            //// 最后一个如果前后相同，证明传入的都是00:00:00，那肯定没有排满24小时
                            ServiceAssert.isTrue(
                                    electricityPrice2.getEndTime() == list.get(0).getStartTime(),
                                    ErrorResultCode.TIME_IS_NOT_FULL.value());
                            break;
                        }
                        electricityPrice1 = electricityPrice2;
                    }
                } else {
                    // 1.3.7 空时段 只有一种情况 就是 这个是实时电价的情况
                }

                boolean success = this.save(electricityPrice);
                if (!success) {
                    throw new ServiceException(ErrorResultCode.DISTRIBUTION_FAILED.value());
                }
                for (ElectricPriceEntity timePrice : electricityPrice.getPeriodPriceList()) {
                    if (timePrice.getStartTime() == timePrice.getEndTime()) {
                        continue;
                    }
                    timePrice.setId(null);
                    timePrice.setPid(electricityPrice.getId());
                    timePrice.setProjectId(electricityPrice.getProjectId());
                    timePrice.setStartDate(electricityPrice.getStartDate());
                    timePrice.setEndDate(electricityPrice.getEndDate());
                    if (!this.save(timePrice)) {
                        throw new ServiceException(ErrorResultCode.DISTRIBUTION_FAILED.value());
                    }
                }
            }
        }
    }

    /**
     * 检查日期是否排满以及是否有重叠
     *
     * @param electricPriceCloudList 电价配置列表
     */
    public void checkDate(List<ElectricPriceEntity> electricPriceCloudList) {
        // 得到第一个电价配置
        ElectricPriceEntity electricPriceCloudEntity = electricPriceCloudList.get(0);
        if (electricPriceCloudEntity.getEndDate() == null
                && electricPriceCloudEntity.getStartDate() == null) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value());
        }
        // 如果不是至今的话。判断下结束日期是否大于开始日期
        if (electricPriceCloudEntity.getWhetherCurrent() == 0) {
            ServiceAssert.isTrue(
                    electricPriceCloudEntity
                                    .getEndDate()
                                    .compareTo(electricPriceCloudEntity.getStartDate())
                            >= 0,
                    ErrorResultCode.START_GE_END_TIME.value());
        }
        // 比较剩下的时间配置，防止有未覆盖的时间
        for (int i = 1; i < electricPriceCloudList.size(); i++) {
            // 当前的储能价格
            ElectricPriceEntity electricityPrice = electricPriceCloudList.get(i);
            if (electricityPrice.getEndDate() == null && electricityPrice.getStartDate() == null) {
                throw new ServiceException(ErrorResultCode.ILLEGAL_DATA.value());
            }

            // 如果是至今，跳过
            if (electricityPrice.getWhetherCurrent() == 1) {
                ServiceAssert.isTrue(
                        electricPriceCloudEntity
                                        .getEndDate()
                                        .compareTo(electricityPrice.getStartDate())
                                <= 0,
                        ErrorResultCode.DATE_OVERLAPPED.value());
                continue;
            }
            // 结束时间>开始时间 报错
            ServiceAssert.isTrue(
                    electricityPrice.getEndDate().compareTo(electricityPrice.getStartDate()) >= 0,
                    ErrorResultCode.START_GE_END_TIME.value());

            // 上一个节点的结束日期+1天，应该等于当前节点的开始日期，否则就是漏了或者重叠了 to
            // to1 如果上一个结束时间+1后 比当前的大，证明有重叠
            ServiceAssert.isTrue(
                    (electricityPrice.getStartDate() - electricPriceCloudEntity.getEndDate())
                            <= MyTimeUtil.ONE_DAY_SECONDS,
                    ErrorResultCode.DATE_IS_NOT_FULL.value());
            // to2 如果上一个结束时间+1后 比当前的小，证明有漏
            ServiceAssert.isTrue(
                    (electricityPrice.getStartDate() - electricPriceCloudEntity.getEndDate())
                            >= MyTimeUtil.ONE_DAY_SECONDS,
                    ErrorResultCode.DATE_OVERLAPPED.value());
            // to3
            // 不重复且不漏，继续下一个
            electricPriceCloudEntity = electricityPrice;
        }
    }

    @Override
    public List<ElectricPriceEntity> getPriceList(String id) {
        List<ElectricPriceEntity> list =
                this.getBaseMapper()
                        .selectList(
                                Wrappers.<ElectricPriceEntity>lambdaQuery()
                                        .isNull(ElectricPriceEntity::getPriceTemplateId)
                                        .isNull(ElectricPriceEntity::getPid)
                                        .eq(ElectricPriceEntity::getProjectId, id)
                                        .orderByAsc(ElectricPriceEntity::getStartDate));
        for (ElectricPriceEntity electricPriceCloudEntity : list) {
            List<ElectricPriceEntity> priceList =
                    this.getBaseMapper()
                            .selectList(
                                    Wrappers.<ElectricPriceEntity>lambdaQuery()
                                            .eq(
                                                    ElectricPriceEntity::getPid,
                                                    electricPriceCloudEntity.getId())
                                            .orderByAsc(ElectricPriceEntity::getStartTime));
            electricPriceCloudEntity.setPeriodPriceList(priceList);
        }
        return list;
    }
}
