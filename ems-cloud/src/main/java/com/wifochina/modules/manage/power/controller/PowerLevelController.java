package com.wifochina.modules.manage.power.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.page.Result;
import com.wifochina.modules.manage.power.entity.PowerLevelEntity;
import com.wifochina.modules.manage.power.request.PowerRequest;
import com.wifochina.modules.manage.power.service.PowerLevelService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@RequestMapping("/manage/power")
@RestController
@Api(tags = "manage-03-项目电力级别")
public class PowerLevelController {

    @Autowired
    private PowerLevelService powerLevelService;

    @PostMapping("/list")
    @ApiOperation("manage-电力级别列表")
    public Result<List<PowerLevelEntity>> list() {
        List<PowerLevelEntity> list = powerLevelService.list();
        return Result.success(list);
    }

    @PostMapping("/query")
    @ApiOperation("manage-根据id查询电力级别")
    public Result<PowerLevelEntity> query(@RequestBody PowerRequest powerRequest) {
        return Result.success(powerLevelService.getById(powerRequest.getUuid()));
    }
}
