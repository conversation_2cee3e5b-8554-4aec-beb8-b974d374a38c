package com.wifochina.modules.manage.favourite.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.page.Result;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.favourite.entity.FavouriteProjectEntity;
import com.wifochina.modules.favourite.request.FavouriteProjectDeleteRequest;
import com.wifochina.modules.favourite.request.FavouriteProjectPageRequest;
import com.wifochina.modules.favourite.request.FavouriteProjectRequest;
import com.wifochina.modules.favourite.request.FavouriteProjectUpdateRequest;
import com.wifochina.modules.favourite.service.FavouriteProjectService;
import com.wifochina.modules.favourite.vo.FavouriteProjectVo;
import com.wifochina.modules.oauth.util.SecurityUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @since 2023-01-04
 */
@RestController
@RequestMapping("/manage/favourite")
@Api(tags = "manage-06-重点项目")
public class FavouriteProjectController {

    @Resource
    private FavouriteProjectService favouriteProjectService;

    /**
     * 查询当前的所有重点关注项目
     */
    @PostMapping("/list")
    @ApiOperation("重点项目列表")
    @PreAuthorize("hasAuthority('/manage/favourite/list')")
    public Result<IPage<FavouriteProjectVo>>
    list(@RequestBody FavouriteProjectPageRequest favouriteProjectPageRequest) {

        IPage<FavouriteProjectVo> favouriteProjectPage =
                new Page<>(favouriteProjectPageRequest.getPageNum(), favouriteProjectPageRequest.getPageSize());
        IPage<FavouriteProjectVo> list = favouriteProjectService.queryFavouriteProjectVo(favouriteProjectPage,
                favouriteProjectPageRequest.getProjectName(), SecurityUtil.getUserId());
        return Result.success(list);
    }

    /**
     * 添加重点关注项目
     */
    @PostMapping("/add")
    @ApiOperation("重点项目添加")
    @PreAuthorize("hasAuthority('/manage/favourite/add')")
    public Result<Object> add(@RequestBody FavouriteProjectRequest favouriteProjectRequest) {
        long count = favouriteProjectService.lambdaQuery().eq(FavouriteProjectEntity::getProjectId, favouriteProjectRequest.getProjectId())
                .eq(FavouriteProjectEntity::getUserId, SecurityUtil.getUserId()).count();
        if (count > 0) {
            throw new ServiceException(ErrorResultCode.FAVOURITE_PROJECT_ADDED.value());
        }
        FavouriteProjectEntity favouriteProjectEntity = new FavouriteProjectEntity();
        BeanUtils.copyProperties(favouriteProjectRequest, favouriteProjectEntity);
        favouriteProjectEntity.setUserId(SecurityUtil.getUserId());
        favouriteProjectService.save(favouriteProjectEntity);
        return Result.success();
    }

    /**
     * 删除重点关注项目
     */
    @PostMapping("/delete")
    @ApiOperation("重点项目删除")
    @PreAuthorize("hasAuthority('/manage/favourite/delete')")
    public Result<Map<String, Map<String, List<ValueVO>>>>
    delete(@RequestBody FavouriteProjectDeleteRequest favouriteProjectDeleteRequest) {
        favouriteProjectService.removeById(favouriteProjectDeleteRequest.getId());
        return Result.success();
    }

    /**
     * 修改重点关注项目
     */
    @PostMapping("/edit")
    @ApiOperation("重点项目修改")
    @PreAuthorize("hasAuthority('/manage/favourite/edit')")
    public Result<Object> edit(@RequestBody FavouriteProjectUpdateRequest favouriteProjectUpdateRequest) {
        FavouriteProjectEntity favouriteProjectEntity = new FavouriteProjectEntity();
        BeanUtils.copyProperties(favouriteProjectUpdateRequest, favouriteProjectEntity);
        favouriteProjectService.updateById(favouriteProjectEntity);
        return Result.success();
    }
}
