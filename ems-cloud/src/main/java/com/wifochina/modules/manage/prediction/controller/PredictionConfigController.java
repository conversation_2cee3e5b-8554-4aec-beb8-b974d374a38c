package com.wifochina.modules.manage.prediction.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.page.Result;
import com.wifochina.modules.prediction.entity.PredictionConfigEntity;
import com.wifochina.modules.prediction.request.PredictionConfigRequest;
import com.wifochina.modules.prediction.request.PredictionManageRequest;
import com.wifochina.modules.prediction.service.PredictionConfigService;
import com.wifochina.modules.prediction.vo.PredictionConfigDetailVo;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-06-25 10:56 AM
 */
@RequestMapping("/manage/prediction")
@RestController
@Api(tags = "manage-07-预测设置")
@RequiredArgsConstructor
public class PredictionConfigController {

    private final PredictionConfigService predictionConfigService;

    private final ProjectService projectService;

    @PostMapping("/addConfigDetail")
    @ApiOperation("增加项目预测细节")
    @PreAuthorize("hasAuthority('/manage/prediction/addConfig')")
    public Result<Object> addConfigDetail(@RequestBody List<PredictionConfigRequest> list) {
        PredictionConfigEntity predictionConfigEntity = predictionConfigService.lambdaQuery()
            .eq(PredictionConfigEntity::getProjectId, list.get(0).getProjectId())
            .eq(PredictionConfigEntity::getType, list.get(0).getType()).eq(PredictionConfigEntity::getWeek, 1).one();
        Optional<PredictionConfigEntity> optionalPredictionConfig = Optional.ofNullable(predictionConfigEntity);
        optionalPredictionConfig.ifPresentOrElse(e -> {
            throw new ServiceException(ErrorResultCode.PREDICTION_CONFIG_EXISTS.value());
        }, () -> {
            predictionConfigService.saveBatch(getPredictionConfigEntities(list));
        });
        return Result.success();
    }

    @NotNull
    private List<PredictionConfigEntity> getPredictionConfigEntities(List<PredictionConfigRequest> list) {
        List<PredictionConfigEntity> predictionConfigEntities = new ArrayList<>();
        list.forEach(e -> {
            PredictionConfigEntity predictionConfigEntity = new PredictionConfigEntity();
            BeanUtils.copyProperties(e, predictionConfigEntity);
            predictionConfigEntities.add(predictionConfigEntity);
        });
        return predictionConfigEntities;
    }

    @PostMapping("/editConfigDetail")
    @ApiOperation("修改项目预测细节")
    @PreAuthorize("hasAuthority('/manage/prediction/editConfig')")
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> editConfigDetail(@RequestBody List<PredictionConfigRequest> list) {
        predictionConfigService.remove(Wrappers.lambdaQuery(PredictionConfigEntity.class)
            .eq(PredictionConfigEntity::getProjectId, list.get(0).getProjectId())
            .eq(PredictionConfigEntity::getType, list.get(0).getType()));
        predictionConfigService.saveBatch(getPredictionConfigEntities(list));
        return Result.success();
    }

    @PostMapping("queryConfigDetail")
    @ApiOperation("查询项目预测细节")
    @PreAuthorize("hasAuthority('/manage/prediction/queryConfig')")
    @Transactional(rollbackFor = Exception.class)
    public Result<PredictionConfigDetailVo>
        queryConfigDetail(@RequestBody PredictionManageRequest predictionManageRequest) {
        ProjectEntity projectEntity = projectService.getById(predictionManageRequest.getProjectId());
        PredictionConfigDetailVo predictionConfigDetailVo = new PredictionConfigDetailVo();
        List<PredictionConfigEntity> list = predictionConfigService.lambdaQuery()
            .eq(PredictionConfigEntity::getProjectId, predictionManageRequest.getProjectId())
            .eq(PredictionConfigEntity::getType, predictionManageRequest.getType()).list();
        predictionConfigDetailVo.setProjectId(projectEntity.getId());
        predictionConfigDetailVo.setProjectName(projectEntity.getProjectName());
        predictionConfigDetailVo.setType(predictionManageRequest.getType());
        predictionConfigDetailVo.setPredictionConfigEntities(list);
        return Result.success(predictionConfigDetailVo);
    }
}
