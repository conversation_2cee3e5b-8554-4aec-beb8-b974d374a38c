package com.wifochina.modules.manage.cloudlargescreen.service;

import com.wifochina.common.constants.TimePointEnum;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.manage.cloudlargescreen.request.CloudLargeScreenProjectSearch;
import com.wifochina.modules.manage.cloudlargescreen.request.CloudLargeScreenSearch;
import com.wifochina.modules.manage.cloudlargescreen.vo.*;
import com.wifochina.modules.project.entity.ProjectEntity;

import java.util.List;
import java.util.Map;

/**
 * Created on 2023/10/12 16:17.
 *
 * <AUTHOR>
 */
public interface CloudLargeScreenService {

    /**
     * 项目收益和碳足迹概览
     *
     * @param timePointEnum : 时间点
     * @param projectEntity : 项目
     * @return : ProjectProfitCarbonOverviewVo : 项目收益和碳足迹概览
     */
    ProjectProfitCarbonOverviewVo projectProfitAndCarbon(
            TimePointEnum timePointEnum, ProjectEntity projectEntity);

    /**
     * 查询所有项目的容量(装机,功率,电站数等..)
     *
     * @return : ProjectCapacityOverviewVo
     */
    ProjectCapacityOverviewVo projectAllCapacity(CloudLargeScreenSearch search);

    /**
     * 查询所有项目的日收益,月收益和总计收益 + 总计碳减排
     *
     * @return : ProjectAllProfitCarbonOverviewVo
     */
    ProjectAllProfitCarbonOverviewVo projectAllProfitCarbon(CloudLargeScreenSearch search);

    /**
     * 查询所有项目的近7日的充放电量概况
     *
     * @return : List<ProjectRecentWeekChargeOverviewVo>
     */
    List<ProjectRecentWeekChargeOverviewVo> projectRecentWeekCharge(
            CloudLargeScreenProjectSearch search);

    /**
     * 查询所有项目的总收益和碳减排数据总览接口
     *
     * @param timePointKey : 时间点key
     * @return : List<ProjectProfitCarbonOverviewVo> 每个项目列表里面的收益和碳减排对象
     */
    List<ProjectProfitCarbonOverviewVo> projectProfitCarbon(
            CloudLargeScreenSearch search, String timePointKey);

    /**
     * 查询项目的状态总览接口
     *
     * @return : ProjectStatusOverviewVo
     */
    ProjectStatusOverviewVo projectStatus(CloudLargeScreenSearch search);

    Map<String, List<ValueVO>> projectChargeOneRate(RequestWithDeviceId requestWithDeviceId)
            throws CloneNotSupportedException;

    ProjectAllElectricOverviewVo projectAllElectric(CloudLargeScreenSearch search);
}
