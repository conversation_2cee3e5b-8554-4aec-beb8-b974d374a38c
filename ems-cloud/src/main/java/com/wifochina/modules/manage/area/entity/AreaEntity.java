package com.wifochina.modules.manage.area.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Getter
@Setter
@TableName("t_area")
@ApiModel(value = "AreaEntity对象")
public class AreaEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("父id")
    private Integer pid;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("城市名称英文")
    private String cityNameEn;

    @ApiModelProperty("1省2市3县")
    private Integer type;
}
