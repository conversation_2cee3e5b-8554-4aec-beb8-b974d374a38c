package com.wifochina.modules.remedies.timer.service.impl;

import com.wifochina.modules.remedies.timer.job.CheckDataJob;
import com.wifochina.modules.remedies.timer.service.CheckDataQuartzService;

import lombok.extern.slf4j.Slf4j;

import org.quartz.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

import javax.annotation.Resource;

/**
 * @since 2024-03-14 3:59 PM
 * <AUTHOR>
 */
@Component
@Slf4j
public class CheckDataQuartzServiceImpl implements CheckDataQuartzService {

    @Resource protected Scheduler scheduler;

    public static final String JOB_GROUP = "check-data-group";

    @Value("${ems.standPointCount}")
    private String standPointCount;

    @Override
    public void addJob(String timezone) {
        // 构建JobDetail (表示一个具体的可执行的调度程序，Job是这个可执行调度程序所要执行的内容)
        JobDetail jobDetail = getJobDetail(timezone);
        // 构建出发去Trigger （调度参数的配置，代表何时出发该任务)
        Trigger trigger = getTrigger(timezone, getJobCrontab());
        if (trigger != null) {
            try {
                scheduler.scheduleJob(jobDetail, trigger);
                if (!scheduler.isStarted()) {
                    scheduler.start();
                }
            } catch (SchedulerException ignored) {
                log.error("");
            }
        }
    }

    public JobDetail getJobDetail(String timezone) {
        Map<String, String> jobData = new HashMap<>(1);
        String jobName = String.format("check-data-job:%s", timezone);
        jobData.put("timezone", timezone);
        jobData.put("standPointCount", standPointCount);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.putAll(jobData);
        return JobBuilder.newJob(CheckDataJob.class)
                .withIdentity(jobName, JOB_GROUP)
                .usingJobData(jobDataMap)
                .storeDurably()
                .build();
    }

    public Trigger getTrigger(String timezone, String jobCron) {
        String triggerName = String.format("check-data-trigger:%s", timezone);
        return TriggerBuilder.newTrigger()
                .withIdentity(triggerName, JOB_GROUP)
                // .startAt(DateBuilder.futureDate(1, DateBuilder.IntervalUnit.SECOND))
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(jobCron)
                                .inTimeZone(TimeZone.getTimeZone(timezone)))
                .build();
    }

    public String getJobCrontab() {
        return "0 20 0 * * ?";
    }
}
