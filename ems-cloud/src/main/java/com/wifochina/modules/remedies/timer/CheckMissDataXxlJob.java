package com.wifochina.modules.remedies.timer;

import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.entity.EmsDataCountEntity;
import com.wifochina.modules.remedies.service.EmsDataCountService;
import com.wifochina.modules.remedies.timer.service.CheckDataService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-08-10 4:38 PM
 */
//@Component
@Slf4j
public class CheckMissDataXxlJob {

    @Resource private ProjectService projectService;

    @Resource private EmsDataCountService emsDataCountService;

    @Resource private RedisTemplate<String, Long> redisTemplate;

    @Resource private CheckDataService checkDataService;

    @Value("${ems.standPointCount}")
    private String standPointCount;

    @Scheduled(cron = "0 45 * * * ?")
    @Async
    public void checkData() {
        List<ProjectEntity> projectEntities = projectService.getCloudProjectList();
        for (ProjectEntity projectEntity : projectEntities) {
            long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            String redisKey = "checkDataDaily:";
            Long lastCheckTime = redisTemplate.opsForValue().get(redisKey + projectEntity.getId());
            if (lastCheckTime != null && todayZero == lastCheckTime) {
                continue;
            }
            long count =
                    emsDataCountService
                            .lambdaQuery()
                            .eq(
                                    EmsDataCountEntity::getTime,
                                    todayZero - EmsConstants.ONE_DAY_SECOND)
                            .eq(EmsDataCountEntity::getProjectId, projectEntity.getId())
                            .count();
            if (count <= 0) {
                checkDataService.addOneProjectData(
                        Integer.parseInt(standPointCount), todayZero, projectEntity);
                redisTemplate
                        .opsForValue()
                        .set(redisKey + projectEntity.getId(), todayZero, 2, TimeUnit.DAYS);
            }
            redisTemplate
                    .opsForValue()
                    .set("checkDataDaily:" + projectEntity.getId(), todayZero, 2, TimeUnit.DAYS);
        }
    }
}
