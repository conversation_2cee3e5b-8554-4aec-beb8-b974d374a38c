package com.wifochina.modules.remedies.timer.job.init;

import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.timer.service.CheckDataQuartzService;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * @since 2024-03-14 2:59 PM
 * <AUTHOR>
 */
@Component
public class CheckDataJobStart {

    @Resource private ProjectService projectService;

    @Resource private CheckDataQuartzService checkDataQuartzService;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        List<String> timezoneList = projectService.getAllTimeZone();
        for (String timezone : timezoneList) {
            checkDataQuartzService.addJob(timezone);
        }
    }
}
