package com.wifochina.modules.remedies.timer.job;

import cn.hutool.extra.spring.SpringUtil;

import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.timer.service.CheckDataService;

import lombok.extern.slf4j.Slf4j;

import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.List;

/**
 * @since 2024-03-14 4:23 PM
 * <AUTHOR>
 */
@Slf4j
public class CheckDataJob extends QuartzJobBean {
    @Override
    protected void executeInternal(JobExecutionContext context) {
        ProjectService projectService = SpringUtil.getBean(ProjectService.class);
        CheckDataService checkDataService = SpringUtil.getBean(CheckDataService.class);
        String timezone = (String) context.getJobDetail().getJobDataMap().get("timezone");
        String standPointCount =
                (String) context.getJobDetail().getJobDataMap().get("standPointCount");
        List<ProjectEntity> projectEntities = projectService.getProjectsByTimeZone(timezone);
        for (ProjectEntity projectEntity : projectEntities) {
            long todayZero = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
            try {
                checkDataService.addOneProjectData(
                        Integer.parseInt(standPointCount), todayZero, projectEntity);
            } catch (Exception e) {
                log.error("项目 {} 检查昨日数据完整时发生异常 {}", projectEntity.getProjectName(), e.getMessage());
            }
        }
    }
}
