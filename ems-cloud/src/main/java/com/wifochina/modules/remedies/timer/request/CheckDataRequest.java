package com.wifochina.modules.remedies.timer.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * profitRequest
 *
 * @date 4/14/2022 11:17 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "按项目和数据阈值来恢复检查")
public class CheckDataRequest {

    @ApiModelProperty(value = "开始时间", required = true)
    private Long time;

    @ApiModelProperty(value = "检查数据的阈值")
    private Integer standCount;

    @ApiModelProperty(value = "项目id", required = true)
    private String projectId;
}
