package com.wifochina.modules.remedies.timer.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.remedies.timer.request.CheckDataRequest;
import com.wifochina.modules.remedies.timer.service.CheckDataService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-08-25 4:16 PM
 */
@Api(tags = "24-补充数据")
@RequestMapping("/remedies")
@RestController
public class CheckDataController {

    @Resource private CheckDataService checkDataService;

    @PostMapping("/checkDataRecover")
    @ApiOperation("恢复检查数据任务")
    public Result<Object> checkDataRecover(@RequestBody CheckDataRequest checkDataRequest) {
        checkDataService.checkDataRecover(checkDataRequest);
        return Result.success();
    }
}
