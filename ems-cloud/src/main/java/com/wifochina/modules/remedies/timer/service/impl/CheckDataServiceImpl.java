package com.wifochina.modules.remedies.timer.service.impl;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.remedies.entity.EmsDataCountEntity;
import com.wifochina.modules.remedies.service.EmsDataCountService;
import com.wifochina.modules.remedies.timer.request.CheckDataRequest;
import com.wifochina.modules.remedies.timer.service.CheckDataService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

/**
 * @since 2024-03-14 11:51 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class CheckDataServiceImpl implements CheckDataService {

    @Value("${ems.standPointCount}")
    private String standPointCount;

    @Resource private ProjectService projectService;
    @Resource private InfluxClientService influxClientService;
    @Resource private DeviceService deviceService;
    @Resource private EmsDataCountService emsDataCountService;

    @Override
    public void checkDataRecover(CheckDataRequest checkDataRequest) {
        int stand =
                checkDataRequest.getStandCount() == null
                        ? Integer.parseInt(standPointCount)
                        : checkDataRequest.getStandCount();
        String projectId = checkDataRequest.getProjectId();
        Long time = checkDataRequest.getTime();
        Optional.ofNullable(time)
                .ifPresent(
                        t -> {
                            t = t + EmsConstants.ONE_DAY_SECOND;
                            if (projectId != null) {
                                ProjectEntity projectEntity =
                                        projectService
                                                .lambdaQuery()
                                                .eq(ProjectEntity::getId, projectId)
                                                .one();
                                Long finalT = t;
                                Optional.ofNullable(projectEntity)
                                        .ifPresent(p -> addOneProjectData(stand, finalT, p));
                            } else {
                                List<ProjectEntity> projectEntities =
                                        projectService
                                                .lambdaQuery()
                                                .eq(ProjectEntity::getWhetherDelete, false)
                                                .ne(ProjectEntity::getProjectModel, 1)
                                                .list();
                                for (ProjectEntity projectEntity : projectEntities) {
                                    addOneProjectData(stand, t, projectEntity);
                                }
                            }
                        });
    }

    @Override
    public void addOneProjectData(int stand, long end, ProjectEntity projectEntity) {
        long start = end - EmsConstants.ONE_DAY_SECOND;
        EmsDataCountEntity emsDataCountEntityExist =
                emsDataCountService
                        .lambdaQuery()
                        .eq(EmsDataCountEntity::getTime, start)
                        .eq(EmsDataCountEntity::getProjectId, projectEntity.getId())
                        .one();
        if (emsDataCountEntityExist == null) {
            return;
        }
        String realQueryString =
                " from(bucket: \""
                        + influxClientService.getBucketRealtime()
                        + "\")\n"
                        + "        |> range(start: {start}, stop: {end} )\n"
                        + "        |> filter(fn: (r) => r._measurement == \""
                        + influxClientService.getEmsTable(projectEntity.getId())
                        + "\")\n"
                        + "        |> filter(fn: (r) => r[\"_field\"] == \"ems_history_output_energy\"  )\n"
                        + "        |> count()\n"
                        + "        |> group()\n"
                        + "        |> sum()";
        Long deviceCount =
                deviceService
                        .lambdaQuery()
                        .eq(DeviceEntity::getProjectId, projectEntity.getId())
                        .eq(DeviceEntity::getUnreal, false)
                        .count();
        realQueryString =
                realQueryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        List<FluxTable> tables = influxClientService.getQueryApi().query(realQueryString);
        double dataCount = 0;
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                Double value = (Double) fluxRecord.getValueByKey("_value");
                if (value != null) {
                    dataCount = value;
                }
            }
        }
        // insert data status
        if (stand * deviceCount > dataCount) {
            EmsDataCountEntity emsDataCountEntity = new EmsDataCountEntity();
            emsDataCountEntity.setDataCount((int) dataCount);
            emsDataCountEntity.setState(false);
            emsDataCountEntity.setTime(start);
            emsDataCountEntity.setProjectId(projectEntity.getId());
            emsDataCountEntity.setEmsCount(Math.toIntExact(deviceCount));
            try {
                emsDataCountService.save(emsDataCountEntity);
            } catch (Exception e) {
                log.error("already insert");
            }
        }
    }
}
