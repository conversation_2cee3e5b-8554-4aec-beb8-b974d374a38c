package com.wifochina.modules.project.service.impl;

import com.google.common.collect.Lists;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectDailyService;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class InfluxProjectDailyServiceImpl implements ProjectDailyService {

    private final InfluxClientService influxClient;

    private final ProjectService projectService;

    @Override
    public Map<String, Double> getEmsPower(Long start, Long end, String column) {
        Map<String, Double> map = new HashMap<>(16);
        String queryString =
                "  from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "    |> range(start: {start}, stop: {end} )\n"
                        + "    |> filter(fn: (r) => r._measurement == \""
                        + influxClient.getEmsTable(WebUtils.projectId.get())
                        + "\")\n"
                        + "    |> filter(fn: (r) => r.projectId == \""
                        + WebUtils.projectId.get()
                        + "\")\n"
                        + "    |> filter(fn: (r) => r[\"_field\"] == \"{column}\" )\n"
                        + "    |> difference()\n"
                        + "    |> sum()\n";
        // 将query进行兑换
        queryString =
                queryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        queryString = queryString.replace("{column}", column);
        log.debug(queryString);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                String id = (String) fluxRecord.getValueByKey("deviceId");
                Double value = (Double) fluxRecord.getValueByKey("_value");
                map.put(id, value);
            }
        }
        return map;
    }

    @Override
    public Double getDailyPower(
            Long start, Long end, String column, String type, List<String> itemIds) {
        String condition = "";
        String system = "";
        String tableName = influxClient.getEmsTable(WebUtils.projectId.get());
        if (type != null) {
            tableName = influxClient.getMeterTable(WebUtils.projectId.get());
            if (itemIds != null && !itemIds.isEmpty()) {
                condition = "and" + EmsUtil.createMeterSqlForCloud(itemIds);
            } else {
                condition = "and r.type ==\"" + type + "\"";
                system = "|> filter(fn: (r) => r[\"system\"] == \"1\")\n";
            }
        } else {
            if (itemIds != null && !itemIds.isEmpty()) {
                condition = "and" + EmsUtil.createDeviceSqlForInfluxDb(itemIds);
            }
        }
        String queryString =
                "from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "    |> range(start: {start}, stop: {end} )\n"
                        + "    |> filter(fn: (r) => r._measurement == \"{tableName}\")\n"
                        + "{system}"
                        + "    |> filter(fn: (r) => r.projectId == \""
                        + WebUtils.projectId.get()
                        + "\")\n"
                        + "    |> filter(fn: (r) => r[\"_field\"] == \"{column}\" {condition})\n"
                        + "    |> difference()\n"
                        + "    |> group()\n"
                        + "    |> sum()\n";
        // 将query进行兑换
        queryString =
                queryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        queryString = queryString.replace("{column}", column);
        queryString = queryString.replace("{condition}", condition);
        queryString = queryString.replace("{system}", system);
        queryString = queryString.replace("{tableName}", tableName);
        log.debug(queryString);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);

        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                Double value = (Double) fluxRecord.getValueByKey("_value");
                if (value == null) {
                    value = 0d;
                }
                return value;
            }
        }
        return 0d;
    }

    @Override
    public Double getDailyPowerProject(
            Long start,
            Long end,
            String column,
            String type,
            List<String> itemIds,
            String projectId) {
        String condition = "";
        String system = "";
        String tableName = influxClient.getEmsTable(projectId);
        if (type != null) {
            tableName = influxClient.getMeterTable(projectId);
            if (itemIds != null && !itemIds.isEmpty()) {
                condition = "and" + EmsUtil.createMeterSqlForCloud(itemIds);
            } else {
                condition = "and r.type ==\"" + type + "\"";
                system = "|> filter(fn: (r) => r[\"system\"] == \"1\")\n";
            }
        } else {
            if (itemIds != null && !itemIds.isEmpty()) {
                condition = "and" + EmsUtil.createDeviceSqlForInfluxDb(itemIds);
            }
        }
        String queryString =
                "from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "    |> range(start: {start}, stop: {end} )\n"
                        + "    |> filter(fn: (r) => r._measurement == \"{tableName}\")\n"
                        + "{system}"
                        + "    |> filter(fn: (r) => r.projectId == \""
                        + projectId
                        + "\")\n"
                        + "    |> filter(fn: (r) => r[\"_field\"] == \"{column}\" {condition})\n"
                        + "    |> difference()\n"
                        + "    |> group()\n"
                        + "    |> sum()\n";
        // 将query进行兑换
        queryString =
                queryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        queryString = queryString.replace("{column}", column);
        queryString = queryString.replace("{condition}", condition);
        queryString = queryString.replace("{system}", system);
        queryString = queryString.replace("{tableName}", tableName);
        log.debug(queryString);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        Double value = null;
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                value = (Double) fluxRecord.getValueByKey("_value");
            }
        }
        return value != null ? value : 0d;
    }

    /**
     * 查询start end 时间范围内的 按照 1d 进行窗口后 取 last数据
     *
     * @param start : 开始时间
     * @param end : 结束时间
     * @param column : 查询的列
     * @param type : 表名称
     * @param itemIds : 查询的设备
     * @param projectId : 项目id
     * @return : 返回每天的最后一条数据的Map
     */
    @Override
    public Map<Long, Double> getDaysPowerForEffeciency(
            Long start,
            Long end,
            String column,
            String type,
            List<String> itemIds,
            String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        String fieldColumn;
        String tableName;
        if (type != null) {
            tableName = influxClient.getMeterTable(projectId);
            fieldColumn = "meterId";
        } else {
            tableName = influxClient.getEmsTable(projectId);
            fieldColumn = "deviceId";
        }
        Restrictions fieldRestriction = null;
        if (itemIds != null && !itemIds.isEmpty()) {
            List<Restrictions> deviceRestrictions = new ArrayList<>();
            for (String itemId : itemIds) {
                Restrictions deviceRestriction = Restrictions.tag(fieldColumn).equal(itemId);
                deviceRestrictions.add(deviceRestriction);
            }
            fieldRestriction = Restrictions.or(deviceRestrictions.toArray(new Restrictions[0]));
        }
        List<Restrictions> filtersList =
                Lists.newArrayList(
                        Restrictions.measurement().equal(tableName),
                        Restrictions.field().equal(column),
                        Restrictions.tag("projectId").equal(projectId));
        if (fieldRestriction != null) {
            filtersList.add(fieldRestriction);
        }
        Restrictions filters = Restrictions.and(filtersList.toArray(new Restrictions[0]));
        // 构造flux查询语句 使用 flux dsl
        Flux flux =
                Flux.from(influxClient.getBucketForever())
                        .withLocationFixed(
                                MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone())
                                        + "s")
                        .range(start, end)
                        .filter(filters)
                        .aggregateWindow(1L, ChronoUnit.DAYS, "last")
                        .groupBy("_time")
                        .sum();
        String fluxQuery = flux.toString();
        log.info("getDaysPowerForEffeciency: \n" + fluxQuery);
        List<FluxTable> tables = influxClient.getQueryApi().query(fluxQuery);
        Map<Long, Double> map = new HashMap<>(16);
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                Double value = (Double) fluxRecord.getValueByKey("_value");
                Instant time = (Instant) fluxRecord.getValueByKey("_time");
                assert time != null;
                map.put(time.getEpochSecond(), Objects.requireNonNullElse(value, 0d));
            }
        }
        return map;
    }
}
