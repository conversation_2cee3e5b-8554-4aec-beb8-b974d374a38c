package com.wifochina.modules.heatmap.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.data.service.PointListHolder;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.group.entity.*;
import com.wifochina.modules.group.service.*;
import com.wifochina.modules.heatmap.request.HeatMapRequestWithGroupId;
import com.wifochina.modules.heatmap.service.HeatMapService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 4/14/2022 11:25 AM
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class InfluxHeatMapServiceImpl implements HeatMapService {
    private static final int MAP_DEFAULT_SIZE = 16;

    private final DeviceService deviceService;

    private final DataService dataService;

    private final InfluxClientService influxClient;

    private final GroupDeviceService groupDeviceService;

    private final GroupAmmeterService groupAmmeterService;

    private final AmmeterService ammeterService;

    private final GroupService groupService;

    private final ProjectService projectService;
    private final PointListHolder pointListHolder;

    /**
     * 热力图，
     *
     * @param heatMapRequestWithGroupId 代表类型。week 7天；month 近30天；year近1年
     * @return 热力图查询
     */
    @Override
    public Map<Long, Double> getHeatMap(
            HeatMapRequestWithGroupId heatMapRequestWithGroupId,
            String column,
            String meterType,
            String deviceId) {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        ZoneOffset zoneOffset = MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone());
        LocalDateTime endDateTime = LocalDateTime.now(zoneOffset.normalized());
        // 不是电表，则是查询ems，表为T15s
        String tableName;
        if (meterType == null) {
            tableName = influxClient.getEmsTable(projectId);
        } else {
            tableName = influxClient.getMeterTable(projectId);
        }
        long start = 0;
        LocalDateTime startDateTime = null;
        if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.WEEK)) {
            startDateTime = endDateTime.minusDays(7);
        } else if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.MONTH)) {
            startDateTime = endDateTime.minusMonths(1);
        } else if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.YEAR)) {
            startDateTime = endDateTime.minusYears(1);
        }
        assert startDateTime != null;
        start =
                startDateTime.toEpochSecond(
                        MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
        return getPower(
                heatMapRequestWithGroupId,
                start,
                Instant.now().getEpochSecond(),
                tableName,
                column,
                meterType);
    }

    Map<Long, Double> getPower(
            HeatMapRequestWithGroupId heatMapRequestWithGroupId,
            long start,
            long end,
            String tableName,
            String column,
            String type) {
        String projectId = WebUtils.projectId.get();
        StringBuilder condition;
        if (type != null) {
            // 根据组查询到电表id
            List<String> ammeterIds = new ArrayList<>(1);
            if (com.google.common.base.Objects.equal(
                            heatMapRequestWithGroupId.getItemId(), EmsConstants.ALL)
                    || !StringUtils.hasLength(heatMapRequestWithGroupId.getItemId())) {
                List<GroupAmmeterEntity> list;
                if (com.google.common.base.Objects.equal(
                                heatMapRequestWithGroupId.getGroupId(), EmsConstants.ALL)
                        || !StringUtils.hasLength(heatMapRequestWithGroupId.getGroupId())) {
                    ammeterIds =
                            ammeterService
                                    .list(
                                            Wrappers.lambdaQuery(AmmeterEntity.class)
                                                    .eq(AmmeterEntity::getProjectId, projectId))
                                    .stream()
                                    .map(AmmeterEntity::getId)
                                    .map(String::valueOf)
                                    .collect(Collectors.toList());
                } else {
                    list =
                            groupAmmeterService
                                    .lambdaQuery()
                                    .eq(
                                            GroupAmmeterEntity::getGroupId,
                                            heatMapRequestWithGroupId.getGroupId())
                                    .list();
                    List<String> ammeterIdsTemp =
                            list.stream()
                                    .map(GroupAmmeterEntity::getAmmeterId)
                                    .map(String::valueOf)
                                    .collect(Collectors.toList());
                    if (!ammeterIdsTemp.isEmpty()) {
                        ammeterIds =
                                ammeterService
                                        .list(
                                                Wrappers.lambdaQuery(AmmeterEntity.class)
                                                        .in(AmmeterEntity::getId, ammeterIdsTemp)
                                                        .eq(AmmeterEntity::getType, type))
                                        .stream()
                                        .map(AmmeterEntity::getId)
                                        .map(String::valueOf)
                                        .collect(Collectors.toList());
                    }
                }
            } else {
                ammeterIds.add(heatMapRequestWithGroupId.getItemId());
            }
            condition = getQueryMeterSql(ammeterIds);

        } else {
            List<String> deviceIds = new ArrayList<>();
            if (EmsConstants.ALL.equals(heatMapRequestWithGroupId.getItemId())
                    || !StringUtils.hasLength(heatMapRequestWithGroupId.getItemId())) {
                if (EmsConstants.ALL.equals(heatMapRequestWithGroupId.getGroupId())
                        || !StringUtils.hasLength(heatMapRequestWithGroupId.getGroupId())) {
                    deviceIds =
                            deviceService
                                    .lambdaQuery()
                                    .eq(DeviceEntity::getProjectId, projectId)
                                    .eq(DeviceEntity::getUnreal, false)
                                    .list()
                                    .stream()
                                    .map(DeviceEntity::getId)
                                    .collect(Collectors.toList());
                } else {
                    List<GroupDeviceEntity> deviceList =
                            groupDeviceService.list(
                                    Wrappers.lambdaQuery(GroupDeviceEntity.class)
                                            .eq(
                                                    GroupDeviceEntity::getGroupId,
                                                    heatMapRequestWithGroupId.getGroupId()));
                    deviceIds =
                            deviceList.stream()
                                    .map(GroupDeviceEntity::getDeviceId)
                                    .map(String::valueOf)
                                    .collect(Collectors.toList());
                }
            } else {
                deviceIds.add(heatMapRequestWithGroupId.getItemId());
            }
            condition = getQueryEmsSql(deviceIds);
            // 处理一下 分组没有设备的情况 不去查询 influx 否则下面的 condition 是空 查询语句报错
            if (deviceIds.isEmpty()) {
                return new HashMap<>(0);
            }
        }
        String queryString = getPowerSql(projectId);
        // 将query进行兑换
        queryString =
                queryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        queryString = queryString.replace("{column}", column);
        queryString = queryString.replace("{condition}", condition);
        queryString = queryString.replace("{tableName}", tableName);
        queryString = queryString.replace("{period}", "1h");
        queryString = queryString.replace("{rate}", "1.0");
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        Map<Long, Double> map = new HashMap<>(MAP_DEFAULT_SIZE);
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                Double value = (Double) record.getValueByKey(column);
                Instant time = (Instant) record.getValueByKey("_time");
                assert time != null;
                value = Double.parseDouble(String.format("%.2f", value == null ? 0d : value));
                map.put(time.getEpochSecond(), value);
            }
        }
        return map;
    }

    private String getPowerSql(String projectId) {
        String queryString =
                " from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "        |> range(start: {start}, stop: {end} )\n"
                        + "        |> filter(fn: (r) => r._measurement == \"{tableName}\")\n"
                        + "        |> filter(fn: (r) => r[\"_field\"] == \"{column}\" {condition} )\n"
                        + "        |> filter(fn: (r) => r.projectId == \""
                        + projectId
                        + "\")\n"
                        + "        |> aggregateWindow(every: {period}, fn: last)\n"
                        + "        |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n"
                        + "        |> fill(column: \"{column}\", usePrevious: true)\n"
                        + "        |> difference(columns:[\"{column}\"])\n"
                        + "        |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "        |> sum(column:\"{column}\")\n"
                        + "        |> map(fn: (r) => ({ r with {column}: r.{column} / {rate} }))";
        return queryString;
    }

    private StringBuilder getQueryMeterSql(List<String> ammeterIds) {
        StringBuilder ammeterIdSql = new StringBuilder("and (");
        for (int i = 0; i < ammeterIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("r.meterId ==\"").append(ammeterIds.get(i)).append("\"");
            } else {
                ammeterIdSql.append(" or r.meterId == \"").append(ammeterIds.get(i)).append("\"");
            }
        }
        return ammeterIdSql.append(")");
    }

    private StringBuilder getQueryEmsSql(List<String> deviceIds) {
        StringBuilder emsIdSql = new StringBuilder("and (");
        for (int i = 0; i < deviceIds.size(); i++) {
            if (i == 0) {
                emsIdSql.append("r.deviceId ==\"").append(deviceIds.get(i)).append("\"");
            } else {
                emsIdSql.append(" or r.deviceId == \"").append(deviceIds.get(i)).append("\"");
            }
        }
        return emsIdSql.append(")");
    }

    /**
     * 获取集装箱温度状态
     *
     * @param deviceId 设备
     * @return Map<String, Map < String, Map < String, Map < String, Double>>>> map
     */
    @Override
    public Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryTemperature(
            String deviceId, Long time) {
        String projectId = WebUtils.projectId.get();
        return getStringMapMap(deviceId, time, projectId, "temperature");
    }

    private @NotNull Map<String, Map<String, Map<String, Map<String, Double>>>> getStringMapMap(
            String deviceId, Long time, String projectId, String key) {
        Map<String, Map<String, Map<String, Map<String, Double>>>> resultMap =
                new HashMap<>(MAP_DEFAULT_SIZE);
        List<String> deviceIds = new ArrayList<>();

        if (EmsConstants.ALL.equals(deviceId)) {
            deviceIds =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getProjectId, projectId)
                            .eq(DeviceEntity::getUnreal, false)
                            .list()
                            .stream()
                            .map(DeviceEntity::getId)
                            .collect(Collectors.toList());
        } else {
            deviceIds.add(deviceId);
        }
        for (String id : deviceIds) {
            int[] data = dataService.get(id);

            if (data == null) {
                continue;
            }
            int emsType = data[42];
            // 电池簇数量
            int cluster = data[pointListHolder.getBmsClusterNum(emsType)];
            // 每簇电池组数量
            int stack = data[pointListHolder.getBmsStackNum(emsType)];
            // 每组电芯数量
            int cell = data[pointListHolder.getBmsCellNum(emsType)];
            // 每组电芯温度传感器数量
            int cellT = data[pointListHolder.getBmsCellTNum(emsType)];
            Map<String, Map<String, Map<String, Double>>> clusterTemperatureMap =
                    new HashMap<>(cluster * stack * cell);
            // fix bug
            int cellInitIndex = pointListHolder.getCellInitIndex(emsType);
            //            int cellIndex = 4000 + cluster * stack * cell;
            int cellIndex = cellInitIndex + cluster * stack * cell;
            for (int i = 0; i < cluster; i++) {
                Map<String, Map<String, Double>> stackTemperatureMap = new HashMap<>(stack * cellT);
                for (int j = 0; j < stack; j++) {
                    Map<String, Double> bmuTemperatureMap = new HashMap<>(cellT);
                    for (int k = 0; k < cellT; k++) {
                        double value =
                                (double) data[cellIndex + k + (j) * cellT + cellT * stack * i] / 10;
                        bmuTemperatureMap.put("cell_" + k, value);
                    }
                    stackTemperatureMap.put("stack_" + j, bmuTemperatureMap);
                }
                clusterTemperatureMap.put("cluster_" + i, stackTemperatureMap);
            }
            if (time != null) {
                List<FluxTable> tables = getPower(time - MyTimeUtil.ONE_DAY_SECONDS, time, id);
                for (FluxTable table : tables) {
                    for (FluxRecord fluxRecord : table.getRecords()) {
                        Double value = (Double) fluxRecord.getValueByKey("_value");
                        String filed = (String) fluxRecord.getValueByKey("_field");
                        String index = (String) fluxRecord.getValueByKey("cellIndex");
                        String cellCluster = (String) fluxRecord.getValueByKey("cellCluster");
                        String cellStack = (String) fluxRecord.getValueByKey("cellStack");
                        assert filed != null;
                        if (filed.contains(key)) {
                            clusterTemperatureMap
                                    .get("cluster_" + cellCluster)
                                    .get("stack_" + cellStack)
                                    .put("cell_" + index, value);
                        }
                    }
                }
            }
            resultMap.put(String.valueOf(id), clusterTemperatureMap);
        }
        return resultMap;
    }

    /**
     * 获取集装箱电压状态
     *
     * @param deviceId ems id
     * @return Map<String, Map>
     */
    @Override
    public Map<String, Map<String, Map<String, Map<String, Double>>>> getBatteryVoltage(
            String deviceId, Long time) {
        String projectId = WebUtils.projectId.get();
        return getStringMapMap(deviceId, time, projectId, "voltage");
    }

    List<FluxTable> getPower(long start, long end, String deviceId) {
        String projectId = WebUtils.projectId.get();
        String queryString =
                " from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "  |> range(start: {start},  stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getCellTable(projectId)
                        + "\")\n"
                        + "  |> filter(fn: (r) => r[\"deviceId\"] == \"{deviceId}\")"
                        + "  |> filter(fn: (r) => r.projectId == \""
                        + projectId
                        + "\")\n"
                        + "  |> aggregateWindow(every: 1m, fn: last, createEmpty: false)\n"
                        + " |> sort(columns: [\"_time\"], desc: true)"
                        + "  |> unique(column: \"_field\")";
        // 将query进行兑换
        queryString =
                queryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        queryString = queryString.replace("{deviceId}", deviceId);
        log.debug(queryString);
        return influxClient.getQueryApi().query(queryString);
    }

    @Override
    public Map<Long, Double> getLoad(HeatMapRequestWithGroupId heatMapRequestWithGroupId) {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        ZoneOffset zoneOffset = MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone());
        LocalDateTime endDateTime = LocalDateTime.now(zoneOffset.normalized());
        Map<Long, Double> loadMap = new HashMap<>(MAP_DEFAULT_SIZE);
        LocalDateTime startDateTime;
        if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.WEEK)) {
            startDateTime = endDateTime.minusDays(7);
        } else if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.MONTH)) {
            startDateTime = endDateTime.minusMonths(1);
        } else if (Objects.equals(heatMapRequestWithGroupId.getType(), EmsConstants.YEAR)) {
            startDateTime = endDateTime.minusYears(1);
            // 1week = 7*24 hour = 168h
        } else {
            return loadMap;
        }
        GroupEntity systemGroupEntity =
                groupService
                        .lambdaQuery()
                        .eq(GroupEntity::getProjectId, projectId)
                        .eq(GroupEntity::getWhetherSystem, true)
                        .one();
        Map<Long, Double> pvMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> gridInMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> gridOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> emsInMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> emsOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> windOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> dieselOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> pileOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        Map<Long, Double> gasOutMap = new HashMap<>(MAP_DEFAULT_SIZE);
        // 光伏日量
        long start =
                startDateTime.toEpochSecond(
                        MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone()));
        long end = Instant.now().getEpochSecond();
        if (Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())) {
            pvMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getMeterTable(projectId),
                            "ac_history_positive_power_in_kwh",
                            "1");
        }
        // 电网日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableElectricGrid())) {
            gridInMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getMeterTable(projectId),
                            "ac_history_positive_power_in_kwh",
                            "2");
            gridOutMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getMeterTable(projectId),
                            "ac_history_negative_power_in_kwh",
                            "2");
        }
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableEms())) {
            emsInMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getEmsTable(projectId),
                            "ems_history_input_energy",
                            null);
            emsOutMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getEmsTable(projectId),
                            "ems_history_output_energy",
                            null);
        }
        // 风电日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())) {
            windOutMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getMeterTable(projectId),
                            "ac_history_positive_power_in_kwh",
                            "4");
        }
        // 柴发日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableWoodPowerGeneration())) {
            dieselOutMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getMeterTable(projectId),
                            "ac_history_positive_power_in_kwh",
                            "5");
        }
        // 充电桩日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableChargingPilePower())) {
            pileOutMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getMeterTable(projectId),
                            "ac_history_positive_power_in_kwh",
                            "6");
        }
        // 燃气日量
        if (Boolean.TRUE.equals(systemGroupEntity.getEnableGasPowerGeneration())) {
            gasOutMap =
                    getPower(
                            heatMapRequestWithGroupId,
                            start,
                            end,
                            influxClient.getMeterTable(projectId),
                            "ac_history_positive_power_in_kwh",
                            "8");
        }
        // EMS净出 电池放电电量 - 电池充电电量
        // 电网净出 电网取电 - 馈网电量
        // 总消耗 = 电网馈 + pv发电 +EMS放 - ems充- grid馈
        Map<Long, Double> keyMap = new HashMap<>(MAP_DEFAULT_SIZE);
        if (!gridOutMap.isEmpty()) {
            keyMap = gridOutMap;
        } else if (!emsInMap.isEmpty()) {
            keyMap = emsInMap;
        } else if (!windOutMap.isEmpty()) {
            keyMap = windOutMap;
        } else if (!dieselOutMap.isEmpty()) {
            keyMap = dieselOutMap;
        } else if (!pileOutMap.isEmpty()) {
            keyMap = pileOutMap;
        } else if (!gasOutMap.isEmpty()) {
            keyMap = gasOutMap;
        }
        for (Long time : keyMap.keySet()) {
            double pv = 0;
            double gridIn = 0;
            double gridOut = 0;
            double emsOut = 0;
            double emsIn = 0;
            double windPower = 0;
            double dieselPower = 0;
            double pilePower = 0;
            double gasPower = 0;
            if (Boolean.TRUE.equals(systemGroupEntity.getPhotovoltaicController())
                    && pvMap.get(time) != null) {
                pv = pvMap.get(time);
            }
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableElectricGrid())) {
                if (gridInMap.get(time) != null) {
                    gridIn = gridInMap.get(time);
                }
                if (gridOutMap.get(time) != null) {
                    gridOut = gridOutMap.get(time);
                }
            }
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableEms())) {
                if (emsOutMap.get(time) != null) {
                    emsOut = emsOutMap.get(time);
                }
                if (emsInMap.get(time) != null) {
                    emsIn = emsInMap.get(time);
                }
            }
            // 风电日量
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableWindPowerGeneration())
                    && windOutMap.get(time) != null) {
                windPower = windOutMap.get(time);
            }
            // 柴发日量
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableWoodPowerGeneration())
                    && dieselOutMap.get(time) != null) {
                dieselPower = dieselOutMap.get(time);
            }
            // 充电桩日量
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableChargingPilePower())
                    && pileOutMap.get(time) != null) {
                pilePower = pileOutMap.get(time);
            }
            // 燃气日量
            if (Boolean.TRUE.equals(systemGroupEntity.getEnableGasPowerGeneration())
                    && gasOutMap.get(time) != null) {
                gasPower = gasOutMap.get(time);
            }
            double load =
                    emsOut
                            + pv
                            + gridOut
                            - gridIn
                            - emsIn
                            + windPower
                            + dieselPower
                            + gasPower
                            - pilePower;
            if (load > 10000) {
                load = 0;
            }
            loadMap.put(time, Double.valueOf(String.format("%.2f", load)));
        }
        return loadMap;
    }
}
