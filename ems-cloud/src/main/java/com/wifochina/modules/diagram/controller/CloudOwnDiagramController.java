package com.wifochina.modules.diagram.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.wifochina.modules.oauth.util.WebUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wifochina.common.page.Result;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.BatteryRequest;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.service.NewDiagramService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * DiagramController
 *
 * <AUTHOR>
 * @version 1.0
 * @date 4/18/2022 6:47 PM
 */
@RequestMapping("/diagram")
@RestController
@Api(tags = "16-历史曲线")
@RequiredArgsConstructor
public class CloudOwnDiagramController {

    private final NewDiagramService newDiagramService;

    /** 单体电池电压温度曲线 */
    @PostMapping("/getBatteryRate")
    @ApiOperation("单体电池电压温度曲线")
    @PreAuthorize("hasAuthority('/diagram/getBatteryRate')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getBatteryRate(
            @RequestBody BatteryRequest batteryRequest) {

        RequestWithDeviceId requestWithDeviceId = new RequestWithDeviceId();
        requestWithDeviceId.setDeviceId(batteryRequest.getDeviceId());
        requestWithDeviceId.setStartDate(batteryRequest.getStartDate());
        requestWithDeviceId.setEndDate(batteryRequest.getEndDate());
        // the number of cluster selected
        int cluster = batteryRequest.getBms_cluster_count();
        // the number of stack selected
        Integer stack = batteryRequest.getBms_stack_per_cluster_count();
        Map<String, Map<String, List<ValueVO>>> socRateMap =
                newDiagramService.getBatteryRate(requestWithDeviceId, null, cluster, stack, -1, -1);
        Map<String, List<ValueVO>> columnMap = socRateMap.get(batteryRequest.getDeviceId());
        Map<String, Map<String, List<ValueVO>>> result = new HashMap<>(columnMap.size());
        result.put("bms_cell_temperature", columnMap);

        return Result.success(result);
    }

    /** 单体电池电压温度曲线 */
    @PostMapping("/getBatteryRateWhoStation")
    @ApiOperation("整站电池电压温度曲线")
    @PreAuthorize("hasAuthority('/diagram/getBatteryRateWhoStation')")
    public Result<Map<String, Map<String, List<ValueVO>>>> getBatteryRateWhoStation(
            @RequestBody BatteryRequest batteryRequest) {
        RequestWithDeviceId requestWithDeviceId = new RequestWithDeviceId();
        requestWithDeviceId.setDeviceId(batteryRequest.getDeviceId());
        requestWithDeviceId.setStartDate(batteryRequest.getStartDate());
        requestWithDeviceId.setEndDate(batteryRequest.getEndDate());
        requestWithDeviceId.setPeriod(batteryRequest.getPeriod());
        requestWithDeviceId.setProjectId(WebUtils.projectId.get());
        Map<String, Map<String, List<ValueVO>>> socRateMap =
                newDiagramService.getBatteryRateWhoStation(requestWithDeviceId, null);
        Map<String, List<ValueVO>> columnMap = socRateMap.get(batteryRequest.getDeviceId());
        Map<String, Map<String, List<ValueVO>>> result = new HashMap<>(columnMap.size());
        result.put("bms_cell_temperature", columnMap);
        return Result.success(result);
    }
}
