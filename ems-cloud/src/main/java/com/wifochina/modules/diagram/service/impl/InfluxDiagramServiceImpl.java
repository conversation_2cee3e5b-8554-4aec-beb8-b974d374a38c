package com.wifochina.modules.diagram.service.impl;

import com.google.common.base.Objects;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.influx.FluxAdapter;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.common.util.ServiceAssert;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.data.util.DataService;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.diagram.request.RequestWithDeviceIdAndPcs;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.AbstractDiagramExtendService;
import com.wifochina.modules.diagram.service.DiagramService;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.entity.GroupAmmeterEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupAmmeterService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.extern.slf4j.Slf4j;

import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 4/18/2022 6:51 PM
 */
// @Service
@Slf4j
@Deprecated
public class InfluxDiagramServiceImpl extends AbstractDiagramExtendService
        implements DiagramService {
    @Resource private GroupAmmeterService groupAmmeterService;
    @Resource private InfluxClientService influxClient;
    @Resource private DataService dataService;

    private static final int DEFAULT_MAP_SIZE = 16;
    protected static final Set<String> NOT_TRANSFORM_FIELDS =
            Set.of(
                    MeterFieldEnum.AC_VOLTAGE.field(),
                    MeterFieldEnum.AC_VOLTAGES_0.field(),
                    MeterFieldEnum.AC_VOLTAGES_1.field(),
                    MeterFieldEnum.AC_VOLTAGES_2.field(),
                    MeterFieldEnum.FREQUENCY.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field(),
                    MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                    MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_1.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_2.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_3.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_4.field(),
                    MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_5.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_1.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_2.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_3.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_4.field(),
                    MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_5.field());

    @Override
    public Map<String, Map<String, List<ValueVO>>> getRate(
            String bucket,
            RequestWithDeviceId requestWithDeviceId,
            List<String> fields,
            String type,
            String measurement) {
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<String> deviceIds = new ArrayList<>();
        // 按照设备id查询influx db ，避免查询到数据需要反复判断deviceId来决定数据归属
        if (EmsConstants.ALL.equals(requestWithDeviceId.getDeviceId())) {
            deviceIds =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getProjectId, projectId)
                            .eq(DeviceEntity::getUnreal, false)
                            .list()
                            .stream()
                            .map(DeviceEntity::getId)
                            .collect(Collectors.toList());
        } else if (requestWithDeviceId.getDeviceId().contains(EmsConstants.SPILT_COMMA)) {
            String[] ids = requestWithDeviceId.getDeviceId().split(EmsConstants.SPILT_COMMA);
            deviceIds = Arrays.asList(ids);
        } else {
            deviceIds.add(requestWithDeviceId.getDeviceId());
        }
        Map<String, Map<String, List<ValueVO>>> valueRateMap = new HashMap<>(deviceIds.size());
        // 根据多个字段来生成查询条件，可以一次多查出几个字段
        String method = "last";
        // 相差的天数
        long periodTime =
                (requestWithDeviceId.getEndDate() - requestWithDeviceId.getStartDate()) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = (periodTime / 2 + 1) * 5;
        if (influxClient.getBucketMean().equals(bucket)
                && (influxClient.getMeterTable(projectId).contains(measurement)
                        || influxClient.getEmsTable(projectId).contains(measurement))) {
            period = (periodTime / 2 + 1);
        }
        if (requestWithDeviceId.getPeriod() != null) {
            period = requestWithDeviceId.getPeriod();
        }
        // 开始设备查询
        for (String deviceId : deviceIds) {
            Map<String, List<ValueVO>> valueVoMap = new HashMap<>();
            String influxSql =
                    FluxAdapter.builder()
                            .project(projectEntity)
                            .from(bucket)
                            .range(
                                    requestWithDeviceId.getStartDate(),
                                    requestWithDeviceId.getEndDate())
                            .measurement(measurement)
                            .equipmentIds(List.of(deviceId))
                            .fields(fields)
                            .toFlux()
                            .aggregateWindow(period, ChronoUnit.MINUTES, method)
                            .withCreateEmpty(false)
                            .toString();
            log.debug("getRate influxSql : {}", influxSql);
            valueVoMap =
                    FluxAdapter.query(influxSql)
                            // 保留2位小数
                            .decimal(2)
                            // 丢弃第一条数据
                            .dropFirst()
                            // 每个 time 整体 偏移
                            .timeOffset(-period * 60)
                            .handleResult()
                            .toMap();
            valueRateMap.put(deviceId, valueVoMap);
        }
        return valueRateMap;
    }

    @Override
    public Map<String, List<ValueVO>> getRateForLargeScreen(
            String bucket,
            RequestWithDeviceId requestWithDeviceId,
            List<String> fields,
            String measurement) {
        String projectId = requestWithDeviceId.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        List<String> deviceIds;
        // 按照设备id查询influx db ，避免查询到数据需要反复判断deviceId来决定数据归属
        if (EmsConstants.ALL.equals(requestWithDeviceId.getDeviceId())) {
            deviceIds =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getProjectId, projectId)
                            .eq(DeviceEntity::getUnreal, false)
                            .list()
                            .stream()
                            .map(DeviceEntity::getId)
                            .collect(Collectors.toList());
        } else {
            deviceIds = new ArrayList<>();
            deviceIds.add(requestWithDeviceId.getDeviceId());
        }
        String condition = "";
        Map<String, List<ValueVO>> valueVoMap = new HashMap<>(fields.size());

        // 根据多个字段来生成查询条件，可以一次多查出几个字段
        //        StringBuilder condition = new StringBuilder("  |> filter(fn: (r) => ");
        //        // 开始设备查询
        //        Map<String, List<ValueVO>> valueVoMap = new HashMap<>(fields.size());
        //        for (int i = 0; i < fields.size(); i++) {
        //            if (i == 0) {
        //                condition.append("r[\"_field\"] == \"").append(fields.get(i));
        //            } else {
        //                condition.append("\" or r[\"_field\"] == \"").append(fields.get(i));
        //            }
        //            valueVoMap.put(fields.get(i), new ArrayList<>());
        //        }
        //        condition.append("\")\n");
        String method = "last";
        // 相差的天数
        long periodTime =
                (requestWithDeviceId.getEndDate() - requestWithDeviceId.getStartDate()) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = (periodTime / 2 + 1) * 5;
        if (influxClient.getBucketMean().equals(bucket)) {
            period = (periodTime / 2 + 1);
        }
        if (requestWithDeviceId.getPeriod() != null) {
            period = requestWithDeviceId.getPeriod();
        }
        String deviceSql = EmsUtil.createDeviceSqlForInfluxDb(deviceIds);
        long finalPeriod = period;
        FluxAdapter.query(
                        builder ->
                                builder.project(projectEntity)
                                        .from(bucket)
                                        .range(
                                                requestWithDeviceId.getStartDate(),
                                                requestWithDeviceId.getEndDate())
                                        .measurement(measurement)
                                        .fields(fields)
                                        .equipmentIds(deviceIds)
                                        .toFlux()
                                        .aggregateWindow(finalPeriod, ChronoUnit.MINUTES, method)
                                        .withCreateEmpty(false)
                                        .toString())
                .decimal(2);
        // 如果是替换字段查询条件
        String queryString =
                "from(bucket: \"{bucket}\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + measurement
                        + "\")\n"
                        + condition
                        + "  |> filter(fn: (r) => r.projectId == \""
                        + projectId
                        + "\" and {deviceSql})\n"
                        + "  |> aggregateWindow(every: {period}m, fn: {method}, createEmpty: false)";
        // 将query进行兑换
        queryString =
                EmsUtil.queryReplaceTime(
                        requestWithDeviceId.getStartDate(),
                        requestWithDeviceId.getEndDate(),
                        queryString);
        queryString = queryString.replace("{deviceSql}", deviceSql);
        queryString = queryString.replace("{bucket}", bucket);
        queryString = queryString.replace("{period}", String.valueOf(period));
        queryString = queryString.replace("{method}", method);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                ValueVO valueVO = new ValueVO();
                Double value = (Double) record.getValueByKey("_value");
                if (value != null) {
                    valueVO.setValue(Double.valueOf(String.format("%.2f", value)));
                    Instant time = (Instant) record.getValueByKey("_time");
                    assert time != null;
                    valueVO.setTime(time.getEpochSecond());
                    String key = (String) record.getValueByKey(EmsConstants.INFLUX_FIELD);
                    valueVoMap.get(key).add(valueVO);
                }
            }
        }
        return valueVoMap;
    }

    /** only for lindorm */
    @Override
    public Map<String, Map<String, List<ValueVO>>> getBatteryRate(
            RequestWithDeviceId requestWithDeviceId,
            List<String> fields,
            int cluster,
            int stack,
            int cell,
            int cellT) {
        // 相差的天数
        long periodTime =
                (requestWithDeviceId.getEndDate() - requestWithDeviceId.getStartDate()) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = (periodTime / 2 + 1) * 5;
        if (requestWithDeviceId.getPeriod() != null) {
            period = requestWithDeviceId.getPeriod();
        }
        Map<String, List<ValueVO>> dataMap = new HashMap<>();
        com.influxdb.query.dsl.Flux flux =
                com.influxdb.query.dsl.Flux.from(influxClient.getBucketForever())
                        .range(requestWithDeviceId.getStartDate(), requestWithDeviceId.getEndDate())
                        .filter(
                                Restrictions.measurement()
                                        .equal(influxClient.getCellTable(WebUtils.projectId.get())))
                        .filter(Restrictions.tag("projectId").equal(WebUtils.projectId.get()))
                        .filter(
                                Restrictions.tag(influxClient.getDeviceKey())
                                        .equal(requestWithDeviceId.getDeviceId()))
                        .filter(Restrictions.tag("cellCluster").equal(String.valueOf(cluster)))
                        .filter(Restrictions.tag("cellStack").equal(String.valueOf(stack)))
                        .aggregateWindow(period, ChronoUnit.MINUTES, "last");
        String queryString = flux.toString();
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable fluxTable : tables) {
            for (FluxRecord record : fluxTable.getRecords()) {
                String field = (String) record.getValueByKey("_field");
                Double value = (Double) record.getValueByKey("_value");
                Instant time = (Instant) record.getValueByKey("_time");
                assert time != null;
                ValueVO valueVO = new ValueVO(time.getEpochSecond(), value);
                String index = (String) record.getValueByKey("cellIndex");
                assert index != null;
                assert field != null;
                if (field.contains("bms_cell_voltage")) {
                    String id = "bms_cell_voltage_" + cluster + "_" + stack + "_" + index;
                    dataMap.computeIfAbsent(id, k -> new ArrayList<>()).add(valueVO);
                } else if (field.contains("bms_cell_temperature")) {
                    String id = "bms_cell_temperature_" + cluster + "_" + stack + "_" + index;
                    dataMap.computeIfAbsent(id, k -> new ArrayList<>()).add(valueVO);
                }
            }
        }
        Map<String, Map<String, List<ValueVO>>> map = new HashMap<>();
        map.put(requestWithDeviceId.getDeviceId(), dataMap);
        return map;
    }

    @Override
    public Map<String, List<ValueVO>> getPcsRate(
            RequestWithDeviceIdAndPcs requestWithDeviceIdAndPcs) {
        String projectId = WebUtils.projectId.get();
        String activeFiled =
                "pcs_{i}_active_power".replace("{i}", requestWithDeviceIdAndPcs.getIndex());
        String reactiveFiled =
                "pcs_{i}_reactive_power".replace("{i}", requestWithDeviceIdAndPcs.getIndex());
        if (EmsConstants.ALL.equals(requestWithDeviceIdAndPcs.getIndex())) {
            activeFiled = "ems_ac_active_power";
            reactiveFiled = "ems_ac_reactive_power";
        }
        long periodTime =
                (requestWithDeviceIdAndPcs.getEndDate() - requestWithDeviceIdAndPcs.getStartDate())
                        / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = periodTime / 2 + 1;
        if (requestWithDeviceIdAndPcs.getPeriod() != null) {
            period = requestWithDeviceIdAndPcs.getPeriod();
        }
        List<String> deviceIds = new ArrayList<>();
        if (EmsConstants.ALL.equals(requestWithDeviceIdAndPcs.getDeviceId())) {
            deviceIds =
                    deviceService
                            .lambdaQuery()
                            .eq(DeviceEntity::getProjectId, projectId)
                            .eq(DeviceEntity::getUnreal, false)
                            .list()
                            .stream()
                            .map(DeviceEntity::getId)
                            .collect(Collectors.toList());
        } else {
            deviceIds.add(requestWithDeviceIdAndPcs.getDeviceId());
        }
        String deviceSql = EmsUtil.createDeviceSqlForInfluxDb(deviceIds);
        String queryString =
                "from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "    |> range(start: {start}, stop: {end} )\n"
                        + "    |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getEmsTable(projectId)
                        + "\")\n"
                        + "    |> filter(fn: (r) => r[\"_field\"] == \"{column}\" )\n"
                        + "    |> filter(fn: (r) =>  {deviceSql})\n"
                        + "    |> filter(fn: (r) => r.projectId == \""
                        + projectId
                        + "\")\n"
                        + "    |> aggregateWindow(every: {period}m, fn: mean, createEmpty: false)\n"
                        + "    |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n"
                        + "    |> group(columns: [\"_time\",], mode:\"by\")\n"
                        + "    |> sum(column: \"{column}\")";
        queryString =
                EmsUtil.queryReplaceTime(
                        requestWithDeviceIdAndPcs.getStartDate(),
                        requestWithDeviceIdAndPcs.getEndDate(),
                        queryString);
        queryString = queryString.replace("{deviceSql}", deviceSql);
        queryString = queryString.replace("{period}", String.valueOf(period));
        String activeQueryString = queryString;
        activeQueryString = activeQueryString.replace("{column}", activeFiled);
        List<FluxTable> activeTables = influxClient.getQueryApi().query(activeQueryString);
        List<ValueVO> activeList = new ArrayList<>();
        for (FluxTable table : activeTables) {
            for (FluxRecord record : table.getRecords()) {
                ValueVO valueVO = new ValueVO();
                Double value = (Double) record.getValueByKey(activeFiled);
                if (value != null) {
                    valueVO.setValue(Double.valueOf(String.format("%.2f", value)));
                    Instant time = (Instant) record.getValueByKey("_time");
                    assert time != null;
                    valueVO.setTime(time.getEpochSecond());
                    activeList.add(valueVO);
                }
            }
        }
        String reactiveQueryString = queryString;
        reactiveQueryString = reactiveQueryString.replace("{column}", reactiveFiled);
        List<FluxTable> reactiveTables = influxClient.getQueryApi().query(reactiveQueryString);
        List<ValueVO> reactiveList = new ArrayList<>();
        for (FluxTable table : reactiveTables) {
            for (FluxRecord record : table.getRecords()) {
                ValueVO valueVO = new ValueVO();
                Double value = (Double) record.getValueByKey(reactiveFiled);
                if (value != null) {
                    valueVO.setValue(Double.valueOf(String.format("%.2f", value)));
                    Instant time = (Instant) record.getValueByKey("_time");
                    assert time != null;
                    valueVO.setTime(time.getEpochSecond());
                    reactiveList.add(valueVO);
                }
            }
        }
        Map<String, List<ValueVO>> rateMap = new HashMap<>(2);
        rateMap.put("active", activeList);
        rateMap.put("reactive", reactiveList);
        return rateMap;
    }

    @Override
    public List<ValueVO> getEmsRate(RequestWithGroupId requestWithDeviceId) {
        String projectId = WebUtils.projectId.get();
        long period =
                EmsUtil.getPeriod(
                        requestWithDeviceId.getStartDate(), requestWithDeviceId.getEndDate(), 1);
        period = requestWithDeviceId.getPeriod() != null ? requestWithDeviceId.getPeriod() : period;
        List<String> deviceIds = deviceService.getGroupDeviceList(requestWithDeviceId);
        if (CollectionUtils.isEmpty(deviceIds)) {
            return Collections.emptyList();
        }
        String condition = " and " + EmsUtil.createDeviceSqlForInfluxDb(deviceIds);
        String queryString =
                "from(bucket: \""
                        + influxClient.getBucketMean()
                        + "\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getEmsTable(projectId)
                        + "\")\n"
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"ems_ac_active_power\" {condition})\n"
                        + "  |> filter(fn: (r) => r.projectId == \""
                        + projectId
                        + "\")\n"
                        + "  |> aggregateWindow(every: {period}m, fn: last, createEmpty: false)\n"
                        + "  |> group(columns: [\"_time\",], mode:\"by\")\n"
                        + "  |> sum()";

        // 将query进行兑换
        queryString =
                EmsUtil.queryReplaceTime(
                        requestWithDeviceId.getStartDate(),
                        requestWithDeviceId.getEndDate(),
                        queryString);
        queryString = queryString.replace("{period}", String.valueOf(period));
        queryString = queryString.replace("{condition}", condition);
        log.debug(queryString);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        List<ValueVO> list = new ArrayList<>();
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                ValueVO valueVO = new ValueVO();
                Double value = (Double) record.getValueByKey("_value");
                if (value != null) {
                    valueVO.setValue(Double.valueOf(String.format("%.2f", value)));
                    Instant time = (Instant) record.getValueByKey("_time");
                    assert time != null;
                    valueVO.setTime(time.getEpochSecond());
                    list.add(valueVO);
                }
            }
        }
        return list;
    }

    @Override
    public Map<String, List<ValueVO>> getLoadRate(RequestWithGroupId loadRateRequest) {
        //        GroupEntity groupEntity =
        //                groupService
        //                        .lambdaQuery()
        //                        .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
        //                        .eq(GroupEntity::getWhetherSystem, true)
        //                        .one();
        //        String activeField = "ac_active_power";
        //        String reactiveField = "ac_reactive_power";
        //        List<ValueVO> activeList = new ArrayList<>();
        //        List<ValueVO> reactiveList = new ArrayList<>();
        //        if (Boolean.TRUE.equals(groupEntity.getEnableLoadGrid())) {
        //            activeList = this.getMeterRate(loadRateRequest, activeField, "3");
        //            reactiveList = this.getMeterRate(loadRateRequest, reactiveField, "3");
        //        } else {
        //            Map<Long, Double> pvMap = new HashMap<>(DEFAULT_MAP_SIZE);
        //            Map<Long, Double> gridMap = new HashMap<>(DEFAULT_MAP_SIZE);
        //            Map<Long, Double> emsMap = new HashMap<>(DEFAULT_MAP_SIZE);
        //            Map<Long, Double> windMap = new HashMap<>(DEFAULT_MAP_SIZE);
        //            Map<Long, Double> dieselMap = new HashMap<>(DEFAULT_MAP_SIZE);
        //            Map<Long, Double> pileMap = new HashMap<>(DEFAULT_MAP_SIZE);
        //            Map<Long, Double> gasMap = new HashMap<>(DEFAULT_MAP_SIZE);
        //            if (Boolean.TRUE.equals(groupEntity.getPhotovoltaicController())) {
        //                List<ValueVO> pvVOList = this.getMeterRate(loadRateRequest, activeField,
        // "1");
        //                pvMap =
        //                        pvVOList.stream()
        //                                .collect(Collectors.toMap(ValueVO::getTime,
        // ValueVO::getValue));
        //            }
        //            // 电网日量
        //            if (Boolean.TRUE.equals(groupEntity.getEnableElectricGrid())) {
        //                List<ValueVO> gridVOList = this.getMeterRate(loadRateRequest, activeField,
        // "2");
        //                gridMap =
        //                        gridVOList.stream()
        //                                .collect(Collectors.toMap(ValueVO::getTime,
        // ValueVO::getValue));
        //            }
        //            if (Boolean.TRUE.equals(groupEntity.getEnableEms())) {
        //                List<ValueVO> emsRateList = this.getEmsRate(loadRateRequest);
        //                emsMap =
        //                        emsRateList.stream()
        //                                .collect(Collectors.toMap(ValueVO::getTime,
        // ValueVO::getValue));
        //            }
        //            // 风电日量
        //            if (Boolean.TRUE.equals(groupEntity.getEnableWindPowerGeneration())) {
        //                List<ValueVO> windVOList = this.getMeterRate(loadRateRequest, activeField,
        // "4");
        //                windMap =
        //                        windVOList.stream()
        //                                .collect(Collectors.toMap(ValueVO::getTime,
        // ValueVO::getValue));
        //            }
        //            // 柴发日量
        //            if (Boolean.TRUE.equals(groupEntity.getEnableWoodPowerGeneration())) {
        //                List<ValueVO> dieselVOList = this.getMeterRate(loadRateRequest,
        // activeField, "5");
        //                dieselMap =
        //                        dieselVOList.stream()
        //                                .collect(Collectors.toMap(ValueVO::getTime,
        // ValueVO::getValue));
        //            }
        //            // 充电桩日量
        //            if (Boolean.TRUE.equals(groupEntity.getEnableChargingPilePower())) {
        //                List<ValueVO> dieselVOList = this.getMeterRate(loadRateRequest,
        // activeField, "6");
        //                pileMap =
        //                        dieselVOList.stream()
        //                                .collect(Collectors.toMap(ValueVO::getTime,
        // ValueVO::getValue));
        //            }
        //            // 燃气日量
        //            if (Boolean.TRUE.equals(groupEntity.getEnableGasPowerGeneration())) {
        //                List<ValueVO> gasVOList = this.getMeterRate(loadRateRequest, activeField,
        // "8");
        //                gasMap =
        //                        gasVOList.stream()
        //                                .collect(Collectors.toMap(ValueVO::getTime,
        // ValueVO::getValue));
        //            }
        //            Map<Long, Double> keyMap = new HashMap<>(DEFAULT_MAP_SIZE);
        //            if (!gridMap.isEmpty()) {
        //                keyMap = gridMap;
        //            } else if (!emsMap.isEmpty()) {
        //                keyMap = emsMap;
        //            } else if (!windMap.isEmpty()) {
        //                keyMap = windMap;
        //            } else if (!dieselMap.isEmpty()) {
        //                keyMap = dieselMap;
        //            } else if (!pileMap.isEmpty()) {
        //                keyMap = pileMap;
        //            } else if (!gasMap.isEmpty()) {
        //                keyMap = gasMap;
        //            }
        //            for (Long time : keyMap.keySet()) {
        //                getLoadList(
        //                        time,
        //                        groupEntity,
        //                        pvMap,
        //                        gridMap,
        //                        emsMap,
        //                        windMap,
        //                        dieselMap,
        //                        pileMap,
        //                        gasMap,
        //                        activeList);
        //            }
        //        }
        //        Map<String, List<ValueVO>> map = new HashMap<>(2);
        //        map.put(
        //                activeField,
        //                activeList.stream()
        //                        .sorted(Comparator.comparing(ValueVO::getTime))
        //                        .collect(Collectors.toList()));
        //        map.put(
        //                reactiveField,
        //                reactiveList.stream()
        //                        .sorted(Comparator.comparing(ValueVO::getTime))
        //                        .collect(Collectors.toList()));
        //        return map;
        return null;
    }

    @Override
    public List<ValueVO> getBatteryDiffRate(RequestWithGroupId requestWithGroupId, String column) {
        String projectId = WebUtils.projectId.get();
        String maxColumn = "bms_cell_high_voltage";
        String minColumn = "bms_cell_low_voltage";
        String mark = "temperature";
        if (column.equals(mark)) {
            maxColumn = "bms_cell_high_temperature";
            minColumn = "bms_cell_low_temperature";
        }
        String queryString =
                "max_column = from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getEmsTable(projectId)
                        + "\")\n"
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"{maxColumn}\" {condition})\n"
                        + "  |> aggregateWindow(every: {period}m, fn: last)\n"
                        + "  |> pivot(rowKey: [\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n"
                        + "  |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "  |> max(column: \"{maxColumn}\")\n"
                        + "\n"
                        + "min_column = from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getEmsTable(projectId)
                        + "\")\n"
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"{minColumn}\" {condition})\n"
                        + "  |> aggregateWindow(every: {period}m, fn: last)\n"
                        + "  |> pivot(rowKey: [\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n"
                        + "  |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "  |> min(column: \"{minColumn}\")\n"
                        + "\n"
                        + "join(tables: {max_column, min_column}, on: [\"_time\"])\n"
                        + "  |> map(fn: (r) => ({ r with difference: r.{maxColumn} - r.{minColumn} }))\n"
                        + "  |> keep(columns: [\"_time\", \"difference\"])";
        long periodTime =
                (requestWithGroupId.getEndDate() - requestWithGroupId.getStartDate()) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = (periodTime / 2 + 1) * 5;
        if (requestWithGroupId.getPeriod() != null) {
            period = requestWithGroupId.getPeriod();
        }
        requestWithGroupId.setItemId(EmsConstants.ALL);
        List<String> deviceIds = deviceService.getGroupDeviceList(requestWithGroupId);
        if (deviceIds.isEmpty()) {
            return Collections.emptyList();
        }
        String condition = " and " + EmsUtil.createDeviceSqlForInfluxDb(deviceIds);
        // 将query进行兑换
        queryString =
                EmsUtil.queryReplaceTime(
                        requestWithGroupId.getStartDate(),
                        requestWithGroupId.getEndDate(),
                        queryString);
        queryString = queryString.replace("{period}", String.valueOf(period));
        queryString = queryString.replace("{maxColumn}", maxColumn);
        queryString = queryString.replace("{minColumn}", minColumn);
        queryString = queryString.replace("{condition}", condition);
        log.debug(queryString);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        List<ValueVO> list = new ArrayList<>();
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                ValueVO valueVO = new ValueVO();
                Double value = (Double) record.getValueByKey("difference");
                if (value != null) {
                    valueVO.setValue(value);
                    Instant time = (Instant) record.getValueByKey("_time");
                    assert time != null;
                    valueVO.setTime(time.getEpochSecond());
                    list.add(valueVO);
                }
            }
        }
        return list;
    }

    @Override
    public List<ValueVO> getRateForDcdc(
            String bucket,
            RequestWithGroupId requestWithGroupId,
            String filed,
            String type,
            String measurement) {
        List<String> deviceIds = deviceService.getGroupDcdcDeviceList(requestWithGroupId);
        // 按照设备id查询influx db ，避免查询到数据需要反复判断deviceId来决定数据归属
        if (deviceIds.isEmpty()) {
            return new ArrayList<>();
        }
        // 根据多个字段来生成查询条件，可以一次多查出几个字段
        List<ValueVO> valueVOList = new ArrayList<>(1);
        String condition = "  |> filter(fn: (r) => " + "r[\"_field\"] == \"" + filed + "\")\n";
        String method = "last";
        // 相差的天数
        long periodTime =
                (requestWithGroupId.getEndDate() - requestWithGroupId.getStartDate()) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = (periodTime / 2 + 1) * 5;
        if (influxClient.getBucketMean().equals(bucket)) {
            period = (periodTime / 2 + 1);
        }
        if (requestWithGroupId.getPeriod() != null) {
            period = requestWithGroupId.getPeriod();
        }
        String deviceSql = EmsUtil.createDeviceSqlForInfluxDb(deviceIds);
        // 如果是替换字段查询条件
        String queryString =
                "from(bucket: \"{bucket}\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + measurement
                        + "\")\n"
                        + condition
                        + "  |> filter(fn: (r) => r.projectId == \""
                        + WebUtils.projectId.get()
                        + "\" and {deviceSql})\n"
                        + "  |> aggregateWindow(every: {period}m, fn: {method}, createEmpty: false)";
        // 将query进行兑换
        queryString =
                EmsUtil.queryReplaceTime(
                        requestWithGroupId.getStartDate(),
                        requestWithGroupId.getEndDate(),
                        queryString);
        queryString = queryString.replace("{deviceSql}", deviceSql);
        queryString = queryString.replace("{bucket}", bucket);
        queryString = queryString.replace("{period}", String.valueOf(period));
        queryString = queryString.replace("{method}", method);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                ValueVO valueVO = new ValueVO();
                Double value = (Double) fluxRecord.getValueByKey("_value");
                if (value != null) {
                    valueVO.setValue(Double.valueOf(String.format("%.2f", value)));
                    Instant time = (Instant) fluxRecord.getValueByKey("_time");
                    assert time != null;
                    valueVO.setTime(time.getEpochSecond());
                    valueVOList.add(valueVO);
                }
            }
        }
        return valueVOList;
    }

    private static void getLoadList(
            Long time,
            GroupEntity groupEntity,
            Map<Long, Double> pvMap,
            Map<Long, Double> gridMap,
            Map<Long, Double> emsMap,
            Map<Long, Double> windMap,
            Map<Long, Double> dieselMap,
            Map<Long, Double> pileMap,
            Map<Long, Double> gasMap,
            List<ValueVO> activeList) {
        double pvPower = 0;
        double gridPower = 0;
        double emsPower = 0;
        double windPower = 0;
        double dieselPower = 0;
        double pilePower = 0;
        double gasPower = 0;
        if (Boolean.TRUE.equals(groupEntity.getPhotovoltaicController())
                && pvMap.get(time) != null) {
            pvPower = pvMap.get(time);
        }
        if (Boolean.TRUE.equals(groupEntity.getEnableElectricGrid())) {
            if (gridMap.get(time) != null) {
                gridPower = gridMap.get(time);
            }
        }
        if (Boolean.TRUE.equals(groupEntity.getEnableEms())) {
            if (emsMap.get(time) != null) {
                emsPower = emsMap.get(time);
            }
        }
        // 风电日量
        if (Boolean.TRUE.equals(groupEntity.getEnableWindPowerGeneration())
                && windMap.get(time) != null) {
            windPower = windMap.get(time);
        }
        // 柴发日量
        if (Boolean.TRUE.equals(groupEntity.getEnableWoodPowerGeneration())
                && dieselMap.get(time) != null) {
            dieselPower = dieselMap.get(time);
        }
        // 充电桩日量
        if (Boolean.TRUE.equals(groupEntity.getEnableChargingPilePower())
                && pileMap.get(time) != null) {
            pilePower = pileMap.get(time);
        }
        // 燃气日量
        if (Boolean.TRUE.equals(groupEntity.getEnableGasPowerGeneration())
                && gasMap.get(time) != null) {
            gasPower = gasMap.get(time);
        }
        double load =
                emsPower + pvPower + gridPower + windPower + dieselPower + gasPower - pilePower;
        ValueVO valueVO = new ValueVO();
        valueVO.setTime(time);
        valueVO.setValue(load);
        activeList.add(valueVO);
    }

    @Override
    public List<ValueVO> getMeterRate(
            RequestWithGroupId requestWithGroupId, String field, String type) {
        LocalDateTime startDate =
                LocalDateTime.ofEpochSecond(requestWithGroupId.getStartDate(), 0, ZoneOffset.UTC);
        LocalDateTime endDate =
                LocalDateTime.ofEpochSecond(
                        requestWithGroupId.getEndDate() - 240, 0, ZoneOffset.UTC);
        // 开始时间不晚于结束时间，佛则返回时间错误异常
        ServiceAssert.isTrue(!endDate.isBefore(startDate), ErrorResultCode.TIME_OVERLAPPED.value());
        // 相差的天数
        long periodTime =
                (requestWithGroupId.getEndDate() - requestWithGroupId.getStartDate()) / 86400;
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        long period = (periodTime / 2 + 1);
        if (requestWithGroupId.getPeriod() != null) {
            period = requestWithGroupId.getPeriod();
        }
        List<String> ammeterIds = new ArrayList<>(1);
        // 根据组查询到电表id
        if (Objects.equal(requestWithGroupId.getItemId(), EmsConstants.ALL)
                || !StringUtils.hasLength(requestWithGroupId.getItemId())) {
            List<GroupAmmeterEntity> list;
            if (Objects.equal(requestWithGroupId.getGroupId(), EmsConstants.ALL)
                    || !StringUtils.hasLength(requestWithGroupId.getGroupId())) {
                list = groupAmmeterService.list();
            } else {
                list =
                        groupAmmeterService
                                .lambdaQuery()
                                .eq(GroupAmmeterEntity::getGroupId, requestWithGroupId.getGroupId())
                                .list();
            }
            ammeterIds = EmsUtil.getMeterIdsFromGroupAmmeterEntity(list, type, ammeterService);
        } else {
            ammeterIds.add(requestWithGroupId.getItemId());
        }
        // 没有电表无法计算
        if (ammeterIds.isEmpty()) {
            return new ArrayList<>();
        }
        return getMeterList(startDate, endDate, ammeterIds, period, type, field);
    }

    @Override
    public List<ValueVO> getMeterFirst(
            RequestWithGroupId requestWithGroupId, String field, String type) {
        return null;
        // return getMeterRate(requestWithGroupId, field, type);
    }

    public List<ValueVO> getMeterList(
            LocalDateTime startDate,
            LocalDateTime endDate,
            List<String> ammeterIds,
            long period,
            String type,
            String field) {
        String projectId = WebUtils.projectId.get();
        List<ValueVO> valueVoList = new ArrayList<>();
        // 查询电表id
        String ammeterIdSql = EmsUtil.createMeterSqlForCloud(ammeterIds);
        String tableName = influxClient.getBucketMean();
        List<String> notFromMeanColumnList =
                List.of(
                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field(),
                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                        MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH.field(),
                        MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
        if (notFromMeanColumnList.contains(field)) {
            tableName = influxClient.getBucketForever();
        }
        String queryString =
                "  from(bucket: \"{tableName}\")\n"
                        + "    |> range(start: {start}, stop: {end}) \n"
                        + "    |> filter(fn: (r) => r._measurement == \""
                        + influxClient.getMeterTable(projectId)
                        + "\")\n";
        queryString =
                queryString
                        + "    |> filter(fn: (r) => r._field== \"{column}\" and {ammeterIds} )\n";
        queryString =
                queryString
                        + "    |> filter(fn: (r) => r.projectId == \""
                        + WebUtils.projectId.get()
                        + "\")\n";
        queryString =
                queryString
                        + "    |> aggregateWindow(every: {period}m, fn: last)\n"
                        + "    |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n"
                        + "    |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "    |> map(fn: (r) => ({r with {column}: r.{column} / 1000.0}))\n"
                        + "    |> sum(column:\"{column}\")";
        // 将query进行兑换
        DateTimeFormatter stdUtcFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        queryString =
                queryString
                        .replace("{start}", stdUtcFormat.format(startDate))
                        .replace("{end}", stdUtcFormat.format(endDate));
        queryString = queryString.replace("{ammeterIds}", ammeterIdSql);
        queryString = queryString.replace("{tableName}", tableName);
        queryString = queryString.replace("{period}", String.valueOf(period));
        queryString = queryString.replace("{column}", field);
        queryString = queryString.replace("{type}", type);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                ValueVO valueVO = new ValueVO();
                Double value = (Double) fluxRecord.getValueByKey(field);
                if (!Objects.equal(null, value)) {
                    if ("2".equals(type) && !NOT_TRANSFORM_FIELDS.contains(field)) {
                        valueVO.setValue(-Double.parseDouble(String.format("%.2f", value)));
                    } else {
                        if (NOT_TRANSFORM_FIELDS.contains(field)) {
                            valueVO.setValue(
                                    Double.parseDouble(String.format("%.2f", value * 1000)));
                        } else {
                            valueVO.setValue(Double.valueOf(String.format("%.2f", value)));
                        }
                    }
                    Instant time = (Instant) fluxRecord.getValueByKey("_time");
                    assert time != null;
                    valueVO.setTime(time.getEpochSecond());
                    valueVoList.add(valueVO);
                }
            }
        }
        return valueVoList;
    }

    @Override
    protected Map<Long, Double> getSocDiffMap(long start, long end) {
        Map<Long, Double> socMap = new HashMap<>(30);
        String projectId = WebUtils.projectId.get();
        ProjectEntity projectEntity = projectService.getById(projectId);
        String queryString =
                "import \"timezone\"\n"
                        + "option location = timezone.fixed(offset: "
                        + MyTimeUtil.getOffsetSecondsFromZoneCode(projectEntity.getTimezone())
                        + "s)\n"
                        + "\n"
                        + "from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getEmsTable(projectId)
                        + "\")\n"
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"ems_soc\")\n"
                        + "  |> filter(fn: (r) => r[\"projectId\"] == \""
                        + projectId
                        + "\")\n"
                        + "  |> aggregateWindow(every: 1d, fn: first, createEmpty: false)\n"
                        + "  |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "  |> mean(column: \"_value\")";
        queryString =
                queryString
                        .replace("{start}", String.valueOf(start))
                        .replace("{end}", String.valueOf(end));
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (int i = tables.size() - 1; i >= 1; i--) {
            FluxTable fluxTable1 = tables.get(i);
            List<FluxRecord> record1 = fluxTable1.getRecords();
            Instant time1 = (Instant) record1.get(0).getValueByKey("_time");
            Double value1 = (Double) record1.get(0).getValueByKey("_value");
            FluxTable fluxTable2 = tables.get(i - 1);
            List<FluxRecord> record2 = fluxTable2.getRecords();
            Instant time2 = (Instant) record2.get(0).getValueByKey("_time");
            Double value2 = (Double) record2.get(0).getValueByKey("_value");
            assert time2 != null;
            assert time1 != null;
            if ((time1.getEpochSecond() - time2.getEpochSecond()) > MyTimeUtil.ONE_DAY_SECONDS) {
                continue;
            }
            if (ObjectUtils.isEmpty(value1) || ObjectUtils.isEmpty(value2)) {
                continue;
            }
            socMap.put(
                    time2.getEpochSecond() - MyTimeUtil.ONE_DAY_SECONDS, (value1 - value2) / 100);
        }
        return socMap;
    }
}
