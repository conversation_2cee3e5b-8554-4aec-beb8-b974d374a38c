package com.wifochina.modules.client;

import com.influxdb.client.*;
import com.wifochina.common.util.HashUtil;
import com.wifochina.modules.group.entity.GroupEntity;

import lombok.Data;

import okhttp3.OkHttpClient;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @since 4/6/2022 3:27 PM
 * <AUTHOR>
 */
@Component
@Data
public class InfluxClient implements InitializingBean, EnvironmentAware, InfluxClientService {

    public WriteApiBlocking writeApi;

    public QueryApi queryApi;

    private Environment environment;

    private String realtimeBucket;

    private String foreverBucket;

    private String meanBucket;

    private String demandBucket;
    private String groupBucket;

    private static final String TABLE_CONTROLLABLE = "controllable";
    private static final String TABLE_EMS = "ems100";
    private static final String TABLE_EMS_GROUP = "group";

    private static final String TABLE_METER = "meter";

    private static final String TABLE_CELL = "ems100_cell";

    private static final String TABLE_DEMAND_1M_SLIDING = "demand_15m_sliding_1m";

    private static final String TABLE_DEMAND_15M_FIXED = "demand_15m_fixed";

    private static final String TABLE_DEMAND_30M_FIXED = "demand_30m_fixed";

    public static final String KEY_DEVICE = "deviceId";

    public static final String KEY_METER = "meterId";
    public static final String TABLE_DEMAND_PREFIX = "demand";
    public static final String TABLE_CAPACITY_PREFIX = "capacity_1d";

    @Override
    public void afterPropertiesSet() {

        String url = environment.getProperty("ems.influx.url");
        String token = environment.getProperty("ems.influx.token");
        String org = environment.getProperty("ems.influx.org");
        this.realtimeBucket = environment.getProperty("ems.influx.bucket.realtime");
        this.foreverBucket = environment.getProperty("ems.influx.bucket.forever");
        this.meanBucket = environment.getProperty("ems.influx.bucket.mean");
        this.demandBucket = environment.getProperty("ems.influx.bucket.demand");
        this.groupBucket = environment.getProperty("ems.influx.bucket.group");
        assert url != null;
        assert token != null;
        InfluxDBClientOptions influxDBClientOptions =
                InfluxDBClientOptions.builder()
                        .url(url)
                        .org(org)
                        .authenticateToken(token.toCharArray())
                        .okHttpClient(
                                new OkHttpClient.Builder()
                                        // .connectTimeout(5, TimeUnit.SECONDS)
                                        .readTimeout(120, TimeUnit.SECONDS))
                        .build();
        InfluxDBClient influxDbClient = InfluxDBClientFactory.create(influxDBClientOptions);
        writeApi = influxDbClient.getWriteApiBlocking();
        queryApi = influxDbClient.getQueryApi();
    }

    @Override
    public String getBucketGroup() {
        return groupBucket;
    }

    @Override
    public String getBucketRealtime() {
        return realtimeBucket;
    }

    @Override
    public String getBucketForever() {
        return foreverBucket;
    }

    @Override
    public String getBucketMean() {
        return meanBucket;
    }

    @Override
    public String getBucketDemand() {
        return demandBucket;
    }

    @Override
    public String getTableEms() {
        return TABLE_EMS;
    }

    @Override
    public String getTableMeter() {
        return TABLE_METER;
    }

    @Override
    public String getTableDemand1mSliding() {
        return TABLE_DEMAND_1M_SLIDING;
    }

    @Override
    public String getTableDemand15mFixed() {
        return TABLE_DEMAND_15M_FIXED;
    }

    @Override
    public String getTableDemand30mFixed() {
        return TABLE_DEMAND_30M_FIXED;
    }

    @Override
    public String getControllableTable(String projectId) {
        return TABLE_CONTROLLABLE + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public String getEmsTable(String projectId) {
        return TABLE_EMS + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public String getEmsGroupTable(String projectId) {
        return TABLE_EMS_GROUP + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public String getEmsT0Table(String projectId) {
        // 这个proxy 版本 不需要区分
        return getEmsTable(projectId);
    }

    @Override
    public String getMeterTable(String projectId) {
        return TABLE_METER + "@" + HashUtil.elfHash(projectId);
    }

    /**
     * control_15m@37943 <br>
     * control_30m_sliding@37943
     *
     * @param projectId : projectId
     * @param systemGroup : 系统分组
     * @return : demand table str
     */
    @Override
    public String getDemandTable(String projectId, GroupEntity systemGroup) {
        return TABLE_DEMAND_PREFIX + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public String getCapacityTable(String projectId) {
        return TABLE_CAPACITY_PREFIX + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public String getCellTable(String projectId) {
        return TABLE_CELL + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public QueryApi getQueryApi() {
        return this.queryApi;
    }

    @Override
    public WriteApiBlocking getWriteApi() {
        return writeApi;
    }

    @Override
    public String getDeviceKey() {
        return KEY_DEVICE;
    }

    @Override
    public String getMeterKey() {
        return KEY_METER;
    }

    @Override
    public String getControllableKey() {
        return "ctrlId";
    }

    @Override
    public String getOrg() {
        return environment.getProperty("ems.influx.org");
    }

    @Override
    public void setEnvironment(@NotNull Environment environment) {
        this.environment = environment;
    }
}
