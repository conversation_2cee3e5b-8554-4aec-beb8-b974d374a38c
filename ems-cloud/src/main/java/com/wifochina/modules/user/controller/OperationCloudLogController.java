package com.wifochina.modules.user.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wifochina.common.handler.MessageSourceHandler;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.oauth.dto.AccountInnerResult;
import com.wifochina.modules.oauth.dto.AccountListPage;
import com.wifochina.modules.oauth.dto.Data;
import com.wifochina.modules.oauth.service.AccountSystemService;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.user.entity.LogEntity;
import com.wifochina.modules.user.entity.UserEntity;
import com.wifochina.modules.user.request.*;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.NoSuchMessageException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(value = "user", tags = "17-用户管理")
@RestController
@RequestMapping("/user")
@Slf4j
@RequiredArgsConstructor
public class OperationCloudLogController {

    private final LogService logService;

    private final AccountSystemService accountSystemService;

    @PostMapping("/getOnePageLogs")
    @ApiOperation("查询一页操作记录")
    @PreAuthorize("hasAuthority('/user/log')")
    public Result<IPage<LogEntity>> getOnePageLogs(@RequestBody LogNamePageRequest request) {
        IPage<LogEntity> resultPage = queryLogs(request);
        if (resultPage != null && CollUtil.isNotEmpty(resultPage.getRecords())) {
            resultPage.getRecords().forEach(i -> i.setDetail(null));
        }
        return Result.success(resultPage);
    }

    @PostMapping("/getOnePageLogs/detail")
    @ApiOperation("查询一页操作记录（明细）")
    @PreAuthorize("hasAuthority('/user/log/detail')")
    public Result<IPage<LogEntity>> getOnePageLogsDetail(@RequestBody LogNamePageRequest request) {
        IPage<LogEntity> resultPage = queryLogs(request);
        return Result.success(resultPage);
    }

    private IPage<LogEntity> queryLogs(LogNamePageRequest request) {
        if (SecurityUtil.getPrincipal() == null) {
            return null;
        }

        UserEntity user = SecurityUtil.getPrincipal().getUserEntity();
        if (user == null) {
            return null;
        }

        IPage<LogEntity> resultPage;
        Map<String, String> conditionNameMap = new HashMap<>();

        if (user.getType() != null && 2 == user.getType()) {
            resultPage = handleAdUser(request, conditionNameMap);
        } else if (EmsConstants.WH_ADMIN_USERNAME.equals(user.getUserName())) {
            resultPage = handleAdUser(request, conditionNameMap);
        } else {
            resultPage = handleOtherUser(request, conditionNameMap, user);
        }

        if (resultPage == null || CollectionUtils.isEmpty(resultPage.getRecords())) {
            return null;
        }

        processLogEntities(resultPage, conditionNameMap);
        return resultPage;
    }

    private IPage<LogEntity> handleAdUser(
            LogNamePageRequest logNamePageRequest, Map<String, String> conditionNameMap) {
        if (StringUtils.hasLength(logNamePageRequest.getName())) {
            AccountInnerResult accountListPage =
                    accountSystemService.getUserByUsername(logNamePageRequest.getName());
            if (accountListPage != null && !CollectionUtils.isEmpty(accountListPage.getData())) {
                conditionNameMap.put(
                        accountListPage.getData().get(0).getId(),
                        accountListPage.getData().get(0).getNickName());
                return queryUserLogs(
                        logNamePageRequest, accountListPage.getData().get(0).getId(), null);
            } else {
                return null;
            }
        } else {
            IPage<LogEntity> page = queryUserLogs(logNamePageRequest, null, null);
            if (CollectionUtils.isEmpty(page.getRecords())) {
                return null;
            }
            AccountInnerResult accountInnerResult =
                    accountSystemService.getUserListByUserIds(
                            page.getRecords().stream()
                                    .map(LogEntity::getUserId)
                                    .collect(Collectors.toList()));

            if (accountInnerResult != null
                    && !CollectionUtils.isEmpty(accountInnerResult.getData())) {
                accountInnerResult
                        .getData()
                        .forEach(
                                e -> {
                                    conditionNameMap.put(e.getId(), e.getNickName());
                                });
            }
            return page;
        }
    }

    private IPage<LogEntity> handleOtherUser(
            LogNamePageRequest logNamePageRequest,
            Map<String, String> conditionNameMap,
            UserEntity user) {
        AccountListPage accountListPage = accountSystemService.subUserList();
        List<String> conditionUserIds = new ArrayList<>();
        if (accountListPage != null && accountListPage.getData() != null) {
            if (StringUtils.hasLength(logNamePageRequest.getName())) {
                conditionUserIds =
                        accountListPage.getData().stream()
                                .filter(
                                        data ->
                                                data.getUserName()
                                                        .contains(logNamePageRequest.getName()))
                                .map(Data::getUserId)
                                .collect(Collectors.toList());
            } else {
                conditionUserIds.addAll(
                        accountListPage.getData().stream()
                                .map(Data::getUserId)
                                .collect(Collectors.toList()));
            }
            accountListPage
                    .getData()
                    .forEach(
                            e -> {
                                conditionNameMap.put(e.getUserId(), e.getUserNickName());
                            });
        }
        if (!StringUtils.hasLength(logNamePageRequest.getName())
                || logNamePageRequest.getName().equals(user.getUserName())) {
            conditionUserIds.add(user.getId());
            conditionNameMap.put(user.getId(), user.getName());
        }
        if (CollectionUtils.isEmpty(conditionUserIds)) {
            return null;
        }
        return queryUserLogs(logNamePageRequest, null, conditionUserIds);
    }

    private IPage<LogEntity> queryUserLogs(
            LogNamePageRequest logNamePageRequest, String userId, List<String> userIds) {
        IPage<LogEntity> logPage =
                new Page<>(logNamePageRequest.getPageNum(), logNamePageRequest.getPageSize());
        return logService.page(
                logPage,
                new QueryWrapper<LogEntity>()
                        .lambda()
                        .eq(LogEntity::getProjectId, WebUtils.projectId.get())
                        .ge(
                                logNamePageRequest.getStart() != null,
                                LogEntity::getRequestTime,
                                logNamePageRequest.getStart())
                        .le(
                                logNamePageRequest.getEnd() != null,
                                LogEntity::getRequestTime,
                                logNamePageRequest.getEnd())
                        .eq(StringUtils.hasLength(userId), LogEntity::getUserId, userId)
                        .ne(LogEntity::getUserId, EmsConstants.LOG_USER_SYSTEM_ID)
                        .in(!CollectionUtils.isEmpty(userIds), LogEntity::getUserId, userIds)
                        .orderByDesc(LogEntity::getRequestTime));
    }

    private void processLogEntities(
            IPage<LogEntity> logEntityIPage, Map<String, String> conditionNameMap) {
        logEntityIPage
                .getRecords()
                .forEach(
                        logEntity -> {
                            try {
                                logEntity.setModule(
                                        MessageSourceHandler.getMessage(logEntity.getModule()));
                                logEntity.setMethod(
                                        MessageSourceHandler.getMessage(logEntity.getMethod()));
                                logEntity.setName(conditionNameMap.get(logEntity.getUserId()));
                            } catch (NoSuchMessageException e) {
                                log.info(
                                        "[language error]----------------------------->"
                                                + e.getMessage());
                            }
                        });
    }
}
