package com.wifochina.modules.notice.service;

import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.notice.service.impl.AbstractNoticeReportServiceImpl;
import com.wifochina.modules.notice.utils.data.month.DetailReport;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-10-08 10:50 AM
 */
// @Component
@Deprecated
public class NoticeReportServiceImpl extends AbstractNoticeReportServiceImpl {

    @Resource private InfluxClientService influxClient;

    @Resource private ProjectService projectService;

    @Override
    public void setWindDischarge(
            long start, long end, String projectId, Map<Long, DetailReport> detailReportMap) {
        setDischargePv(start, end, projectId, detailReportMap, "4");
    }

    @Override
    public void setWasterDischarge(
            long start, long end, String projectId, Map<Long, DetailReport> detailReportMap) {}

    @Override
    public void setPvDischarge(
            long start, long end, String projectId, Map<Long, DetailReport> detailReportMap) {
        setDischargePv(start, end, projectId, detailReportMap, "1");
    }

    private void setDischargePv(
            long start,
            long end,
            String projectId,
            Map<Long, DetailReport> detailReportMap,
            String type) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        String queryString =
                "import \"timezone\"\n"
                        + "option location = timezone.fixed(offset: "
                        + MyTimeUtil.getOffsetSecondsFromZoneCode(projectEntity.getTimezone())
                        + "s)\n"
                        + "from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getMeterTable(projectId)
                        + "\")\n"
                        + "  |> filter(fn: (r) => r[\"type\"] == \"{type}\")\n"
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"ac_history_positive_power_in_kwh\")\n"
                        + "  |> filter(fn: (r) => r[\"system\"] == \"1\")\n"
                        + "  |> aggregateWindow(every: 1d, fn: first, createEmpty: false)\n"
                        + "  |> difference()\n"
                        + "  |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "  |> sum()";
        queryString = queryString.replace("{start}", String.valueOf(start));
        queryString =
                queryString.replace("{end}", String.valueOf(end + MyTimeUtil.ONE_DAY_SECONDS));
        queryString = queryString.replace("{type}", type);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                Double value = (Double) fluxRecord.getValueByKey("_value");
                if (value != null) {
                    Instant time = (Instant) fluxRecord.getValueByKey("_time");
                    assert time != null;
                    DetailReport detailReport =
                            detailReportMap.get(
                                    time.getEpochSecond() - 2 * MyTimeUtil.ONE_DAY_SECONDS);
                    if (detailReport != null) {
                        if ("1".equals(type)) {
                            detailReport.setPvDischarge(String.format("%.5f", value));
                        } else if ("4".equals(type)) {
                            detailReport.setWindDischarge(String.format("%.5f", value));
                        }
                    }
                }
            }
        }
    }

    @Override
    public Map<Long, Double> getPvOrWindPower(
            Long startDate, Long endDate, String type, String projectId) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        String queryString =
                "import \"timezone\"\n"
                        + "option location = timezone.fixed(offset: "
                        + MyTimeUtil.getOffsetSecondsFromZoneCode(projectEntity.getTimezone())
                        + "s)\n"
                        + "from(bucket: \""
                        + influxClient.getBucketForever()
                        + "\")\n"
                        + "  |> range(start: {start}, stop: {end})\n"
                        + "  |> filter(fn: (r) => r[\"_measurement\"] == \""
                        + influxClient.getMeterTable(projectId)
                        + "\")\n"
                        + "  |> filter(fn: (r) => r[\"type\"] == \"{type}\")\n"
                        + "  |> filter(fn: (r) => r[\"_field\"] == \"ac_history_positive_power_in_kwh\")\n"
                        + "  |> filter(fn: (r) => r[\"system\"] == \"1\")\n"
                        + "  |> aggregateWindow(every: 1mo, fn: first, createEmpty: false)\n"
                        + "  |> difference()\n"
                        + " |> group(columns: [\"_time\"], mode:\"by\")\n"
                        + "  |> sum()";
        Map<Long, Double> map = new HashMap<>(12);
        // 开始时间为12月1号零点
        queryString = queryString.replace("{start}", String.valueOf(startDate));
        queryString =
                queryString.replace(
                        "{end}", String.valueOf(endDate + 31 * MyTimeUtil.ONE_DAY_SECONDS));
        queryString = queryString.replace("{type}", type);
        List<FluxTable> tables = influxClient.getQueryApi().query(queryString);
        for (FluxTable table : tables) {
            for (FluxRecord fluxRecord : table.getRecords()) {
                Double value = (Double) fluxRecord.getValueByKey("_value");
                if (value != null) {
                    Instant time = (Instant) fluxRecord.getValueByKey("_time");
                    assert time != null;
                    map.put(
                            getMonthBeforeTwoMonth(
                                    time.getEpochSecond(),
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())),
                            value);
                }
            }
        }
        return map;
    }

    public static Long getMonthBeforeTwoMonth(long start, ZoneOffset zoneOffset) {
        LocalDateTime localDateTime =
                LocalDateTime.ofInstant(Instant.ofEpochSecond(start), zoneOffset);
        localDateTime = localDateTime.minusMonths(2);
        return localDateTime.toInstant(zoneOffset).getEpochSecond();
    }
}
