server:
  port: ${APPLICATION_PORT:8808}
  tomcat:
    uri-encoding: utf-8
    basedir: ~/logs
    access log:
      enabled: true
      pattern: '%h %l %u %t "%r" %s %b %D %{User-Agent}i'
      max-days: 2
    remote ip:
      protocol-header: x-forwarded-proto
      host-header: x-forwarded-for
    threads:
      max: ${TOMCAT_THREAD_MAX:200}
      min-spare: ${TOMCAT_THREAD_MIN:20}
  servlet:
    session:
      timeout: 100000000
    context-path: ${APPLICATION_PATH:/api}

spring:
  output:
    ansi:
      enabled: never
  application:
    name: ${APPLICATION_NAME:wifo-china.ems}
  profiles:
    active: ${PROFILES_ACTIVE:prod}
  messages:
    basename: i18n/message
    encoding: UTF-8
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
  mvc:
    async:
      request-timeout: 600000
    path match:
      matching-strategy: ant_path_matcher
  datasource:
    #配置监控统计拦截的filters，去掉后监控界面sql无法统计，wall用于防火墙
    druid:
      #配置初始化大小、最小、最大
      initial-size: ${DRUID_INITIAL_SIZE:20}
      minIdle: ${DRUID_MIN_IDLE:20}
      max-active: ${DRUID_MAX_ACTIVE:200}
      # 配置获取连接等待超时的时间(单位：毫秒)
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒（最小10分钟，最大15分钟）
      time-between-eviction-runs-millis: 600000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 600000
      max-evictable-idle-time-millis: 1200000
      # 配置来检测并清理被遗弃的连接 10分钟
      remove-abandoned: true
      remove-abandoned-timeout-millis: 600000
      # 用来测试连接是否可用的SQL语句,默认值每种数据库都不相同,这是mysql
      validationQuery: select 1
      # 应用向连接池申请连接，并且testOnBorrow为false时，连接池将会判断连接是否处于空闲状态，如果是，则验证这条连接是否可用
      testWhileIdle: true
      # 如果为true，默认是false，应用向连接池申请连接时，连接池会判断这条连接是否是可用的
      testOnBorrow: true
      # 如果为true（默认false），当应用使用完连接，连接池回收连接的时候会判断该连接是否还可用
      testOnReturn: false
      # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle
      poolPreparedStatements: true
      # 要启用PSCache，必须配置大于0，当大于0时， poolPreparedStatements自动触发修改为true，
      # 在Druid中，不会存在Oracle下PSCache占用内存过多的问题，
      # 可以把这个数值配置大一些，比如说100
      maxOpenPreparedStatements: 100
      # 连接池中的minIdle数量以内的连接，空闲时间超过minEvictableIdleTimeMillis，则会执行keepAlive操作
      keepAlive: true
      keep-alive-between-time-millis: 900000
      #      # Spring 监控，利用aop 对指定接口的执行时间，jdbc数进行记录
      #      aop-patterns: "com.springboot.template.dao.*"
      ########### 启用内置过滤器（第一个 stat必须，否则监控不到SQL）##########
      # stat - SQL监控配置
      # wall - SQL防火墙配置
      # slf4j - Druid日志配置
      filters: stat,wall,slf4j
      filter:
        config:
          #开启密钥加密
          enabled: true
        stat:
          enabled: true
          db-type: mysql
          #开启慢sql监控
          log-slow-sql: true
          slow-sql-millis: 2000
        #日志监控，使用slf4j 进行日志输出
        slf4j:
          enabled: true
          statement-log-error-enabled: true
          statement-create-after-log-enabled: false
          statement-close-after-log-enabled: false
          result-set-open-after-log-enabled: false
          result-set-close-after-log-enabled: false
        wall:
          config:
            comment-allow: true
          enabled: true
      ########## 配置WebStatFilter，用于采集web关联监控的数据 ##########
      web-stat-filter:
        # 配置统计页面过滤
        enabled: true
        # 过滤路径
        url-pattern: /*
        # 排除路径
        exclusions: .js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,*.html,*.woff2,/druid/*
        # 开启session统计
        session-stat-enable: true
        # session统计的最大个数
        session-stat-max-count: 1000
      stat-view-servlet:
        # 访问白名单 如果allow没有配置或者为空，则允许所有访问
        allow:
        # 拒绝访问的地址，deny优先于allow，如果在deny列表中，就算在allow列表中，也会被拒绝
        # deny:
        # 配置统计页面
        enabled: true
        # 访问密码
        login-password: wh123@abcdwh
        # 访问用户名
        login-username: whadmin
        # 不允许重置监控数据
        reset-enable: false
      use-ping-method: false
  mail:
    host: ${MAIL_HOST:smtp.exmail.qq.com}
    port: ${MAIL_PORT:465}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:kveiTQfNJof43Mme}
    properties:
      mail:
        smtp:
          auth: true
          starttls.enable: true
          ssl.enable: true
          ssl.trust: smtp.exmail.qq.com
    #          socketFactory:
    #            class: javax.net.ssl.SSLSocketFactory
    default-encoding: UTF-8
  flyway:
    enabled: true
    # 1.4.2 生产环境 指定这个版本
    baseline-version: 20250228110542 # 基线版本指定为1.4.1 版本, 因为1.4.2 cloud 版本才准备把flyway 打开


#prometheus actuator
management:
  endpoints:
    jmx:
      exposure:
        include: "*"
    web:
      exposure:
        include: "*"
  metrics:
    export:
      datadog:
        application-key: ${spring.application.name}


mybatis-plus:
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:com/wifochina/modules/**/mapper/xml/*Mapper.xml
  global-config:
    db-config:
      #主键类型 AUTO:"数据库ID自增" INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO

  configuration:
    cache-enabled: true
    use-generated-keys: true
    default-executor-type: REUSE
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true

graphic:
  verifyExpire: ${GRIPHIC_EXPIRE:3}
user:
  regret: ${USER_REGRET:7}

qweather:
  official-key: ${QWEATHER_OFFICIAL_KEY:3ec50552d5e44e20b9d18088456f9c03}

knife4j:
  enable: ${KNIFE4J_ENABLE:true}
  basePackage: com.wifochina
  basePath:
  title: 储能EMS
  description: 储能EMS接口文档
  termsOfServiceUrl: http://localhost:8808/api/swagger-ui.html
  version: 2.0
  apiName: Authorization
  apiKeyName: Authorization

carbon:
  load:
    emission: ${CARBON_LOAD:0.6435}
  pv:
    reduction: ${CARBON_PV_REDUCTION:0.5935}
    power: ${CARBON_PV_POWER:0.5935}
  ems:
    reduction: ${CARBON_EMS_REDUCTION:0.06435}
    power: ${CARBON_EMS_POWER:0.06435}

authentication:
  jwt:
    ### jwt过期时间
    expire: ${JWT_EXPIRE:604800}
    ### 公钥
    pubKey: ${AUTH_PUB_KEY:MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCXPkkw5KYbcln2hh0tWXTH9TxdqHIKNYXANGTePk6DE+Cs/e1HzXrUxm8E+YfKQKreFJyIujJEAsfaIcJZy9N/ZwFn4Mk3K7C2orv0YXrn3hXJ/HFqxCCnQqnp+urGHCd+qTOciiNV2pIQG0R+iRb3gBolwDmuFcUABYpAcpN5zQIDAQAB}
    ### 私钥
    priKey: ${AUTH_PRI_KEY:MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJc+STDkphtyWfaGHS1ZdMf1PF2ocgo1hcA0ZN4+ToMT4Kz97UfNetTGbwT5h8pAqt4UnIi6MkQCx9ohwlnL039nAWfgyTcrsLaiu/RheufeFcn8cWrEIKdCqen66sYcJ36pM5yKI1XakhAbRH6JFveAGiXAOa4VxQAFikByk3nNAgMBAAECgYB+eylMSTscovHXN2s5HKGMA2t7S74rCX2UMnzUWzjfZ4UyRpzjulRpkpUPjPphStlaJdSOh2A3/jdSFX9qBwoUKBFNdp67EOhW7NjRc7whBVnO0/zrBaeW3Efn5YEb1o+X3QNzp3NfWXdyJv65RewuUZPZZSfkqh4H3hjBqmBs4QJBAOted9KU8/uReGYwhsMG6CEuM3qKy27NfWbOi6tHnhfk+ypY8b/zvCXMsjMfCkvZ2jxCY7FIuvhnuwZ2mg6b4+UCQQCkgBcNsbFMMngu6oIlJ9Ewlfn7CvPwoXD6phh1wb6jHvrYLf+KKnyWmQEJhoA3luXebAcXEToHX/9Wj3t1lK/JAkBTzEVy5u9awLcSAvLn2ryom49ecK3vHCAqixz09UGXFkJKGHKxubBh8Nf9FW8QBFcLn0NpKhDPQfc3XOCKlPv1AkAES2/OpLf7REoM94RkUfDNMu0u169ctepMMO/six1eBt4HrNPCGK/eAqqbRA6u5Nqlfu6EdKeuL5xr9x0DCdm5AkAHwnvjggnioz3LQidF7FfbGLsfDz8Ab/jGHoFc8ycbtO+ePHRibw4mpofj6yzESI6dkKI+S2967abNL3qhGHSB}

limit:
  ### 限流key
  key: ${LIMIT_KEY:rate_limit}
  ###限流时间,单位秒
  time: ${LIMIT_TIME:60}
  ###限流次数
  count: ${LIMIT_COUNT:10}
  path: ${LIMIT_PATH:com.wifochina.modules.diagram;com.wifochina.modules.event;com.wifochina.modules.monitor;com.wifochina.modules.oauth;com.wifochina.modules.report;com.wifochina.modules.system;com.wifochina.modules.project;com.wifochina.modules.operation;com.wifochina.modules.system;}

local:
  refresh: ${REFRESH_URL:http://pangu-forward-control.dev.weiheng-tech.com/refresh}
  forward: ${FORWARD_URL:http://pangu-forward.weiheng-tech.com/}

xxl:
  job:
    admin:
      addresses: http://${XXL_URL:nginx.dev.weiheng-tech.com:32156}/xxl-job-admin
    accessToken: ${XXL_JOB_TOKEN:default_token}
    executor:
      app name: ${XXL_JOB_APP_NAME:lindorm-job-executor}
      address:
      ip:
      port: 9999
      logpath: /opt/xxl-job/jobhandler
      logretentiondays: 7

ems:
  standPointCount: ${EMS_STAND_POINT_COUNT:17180}
  email:
    notice:
      demandExceed: ${EMS_EMAIL_NOTICE_DEMAND_EXCEED:<EMAIL>}
      demandMonthInit: ${EMS_EMAIL_NOTICE_MONTH_INIT:<EMAIL>}
      strategyDynamicPriceUpload: ${EMS_EMAIL_NOTICE_STRATEGY_DYNAMIC_PRICE_UPLOAD:<EMAIL>}
  # 标注 是 场站 还是 云版本
  cloud: true

# 1.3.7 for 电价系统的 盐和业务系统name
electricpricesystem:
  name: ${ELECTRIC_PRICE_SYSTEM_NAME:pangu}
  salt: ${ELECTRIC_PRICE_SYSTEM_SALT:M3T5vWSoK4B4oZu4}
  electricPriceUrl: "/data/v1/dayAheadPrice"
  electricPriceRegionsIntervalUrl: "/data/v1/country-region-price-interval"
  dataSource: ${ELECTRIC_PRICE_SYSTEM_DATASOURCE:Entsoe}
  endpoint: ${ELECTRIC_PRICE_SYSTEM_ENDPOINT:http://nginx.dev.weiheng-tech.com:31146}


# 系统配置
system:
  # 账号系统相关配置
  account-system:
    accountUrl: ${CUSTOM_URL_ACCOUNT:http://account-api.dev.weiheng-tech.com}
    appId: ${AUTH_CENTER_APP_ID:2}
    dataCenter: ${DATA_CENTER:3}
    salt: ${AUTH_CENTER_SALT:bkrq8uuG3B4rb8GgbLK!}
    adminAccount: <EMAIL>
    adminPassword: admin123
    adminAccountType: 1
    loginRoleId: 101
    auth:
      # 内部系统远程调用key、secret
      accessKey: ${CUSTOM_ACCESS_KEY:Cnk7yuKnarCMC5CQVzK!SpX6&2yRG68D}
      accessSecret: ${CUSTOM_ACCESS_SECRET:Mm3xTaZoevKQ53@30PbjhKrs%hgVYjsq}
  # other 其他配置 后续慢慢重构到这边 保持配置统一 不分散 好管理, 如  electricpricesystem
  demand:
    # 需量新版本上线后 或者 没有最新记录的时候 去查询 influxdb 最新数据的时候 前推多少小时
    demandLastPreHours: ${DEMAND_LAST_PRE_HOURS:3}
    # 控制异常情况  需量后续数据太大 比如中间断了几天, 控制一下 一次最多处理 180条 并且是 最新的 takeLast 180 
    demandExtrusionMaxTake: ${DEMAND_EXTRUSION_MAX_TAKE:180}
    demandJobCalcInterval: ${DEMAND_JOB_CALC_INTERVAL:1}