package com.wofichina

import cn.hutool.core.codec.Base64Encoder
import cn.hutool.core.date.DateUtil
import cn.hutool.crypto.digest.DigestUtil
import com.wifochina.modules.strategytemplate.request.StrategyTemplateItemRequest
import java.time.Instant
import java.time.LocalTime
import java.time.ZoneId


fun main() {
//    val startTimestamp = 1713542400L // 2023-06-14 04:07:56
//    val endTimestamp = Instant.now().epochSecond
//
//    val midnightTimestamps = getMidnightTimestamps(ZoneId.of("Asia/Shanghai"), startTimestamp, endTimestamp)
//    println(midnightTimestamps) // 输出 [1686681600, 1686768000, 1686854400, 1686940800]
//
////    println(WSign().sign())
//    println(WSign().calculateOffsetNew(ZoneId.of("Asia/Shanghai"), 1738802640, 15))
//
    val list = listOf(
        TimeRange(LocalTime.of(8, 0), LocalTime.of(10, 0)),
        TimeRange(LocalTime.of(10, 0), LocalTime.of(12, 59)),
        TimeRange(LocalTime.of(12, 59), LocalTime.MIDNIGHT) // 保留 00:00
    )
    validateNoOverlap(list)
    println(list)
}

data class TimeRange(val startTime: LocalTime, val endTime: LocalTime)

fun validateNoOverlap(items: List<TimeRange>): Boolean {
    // 处理时间：将 endTime == 00:00 替换为 23:59:59
    val fixedItems = items.map {
        val fixedEnd = if (it.endTime == LocalTime.MIDNIGHT) LocalTime.of(23, 59, 59) else it.endTime
        TimeRange(it.startTime, fixedEnd)
    }
    // 按 startTime 排序
    val sorted = fixedItems.sortedBy { it.startTime }

    for (i in 1 until sorted.size) {
        val prev = sorted[i - 1]
        val current = sorted[i]
        if (current.startTime.isBefore(prev.endTime)) {
            // 有交叉
            println("时间冲突: ${prev.startTime}-${prev.endTime} 与 ${current.startTime}-${current.endTime}")
            return false
        }
    }
    return true
}

fun calculateOffsetNew(zoneId: ZoneId, timestamp: Long, period: Int): Long {
    // 将时间戳转换为 ZonedDateTime（假设为 UTC 时间）
    val dateTime = Instant.ofEpochSecond(timestamp).atZone(zoneId)
    val hours = dateTime.hour
    val minutes = dateTime.minute

    // 转换为分钟数
    val currentMinutes = hours * 60 + minutes

    // 计算 15 分钟窗口的基准点
    val base = period
    val nearestBase = (currentMinutes / base) * base

    // 偏移量
    val offset = currentMinutes - nearestBase
    return offset.toLong()
}

fun getMidnightTimestamps(zoneId: ZoneId, startTimestamp: Long, endTimestamp: Long): List<Long> {
    val startDate = Instant.ofEpochSecond(startTimestamp)
        .atZone(zoneId)
        .toLocalDate() // 获取开始日期

    val endDate = Instant.ofEpochSecond(endTimestamp)
        .atZone(zoneId)
        .minusDays(1)
        .toLocalDate() // 获取结束日期

    return generateSequence(startDate) { date ->
        date.plusDays(1).takeIf { it <= endDate } // 每次加一天，直到超过结束日期
    }.map { date ->
        date.atStartOfDay(zoneId).toEpochSecond() // 转换为零点时间戳
    }.toList()
}

class WSign {
    fun sign(): String {
        val curTime = DateUtil.currentSeconds()
        val authStr = "2" + curTime + "bkrq8uuG3B4rb8GgbLK!"
        val shaStr = DigestUtil.sha256Hex(authStr)
        val token = Base64Encoder.encode(
            String.format("%s:%d:%s", 2, curTime, shaStr.substring(10, 20))
        )
        return token
    }

    fun calculateOffsetNew(zoneId: ZoneId, timestamp: Long, period: Int): Long {
        // 将时间戳转换为 ZonedDateTime（假设为 UTC 时间）
        val dateTime = Instant.ofEpochSecond(timestamp).atZone(zoneId)
        val hours = dateTime.hour
        val minutes = dateTime.minute

        // 转换为分钟数
        val currentMinutes = hours * 60 + minutes

        // 计算 15 分钟窗口的基准点
        val base = period
        val nearestBase = (currentMinutes / base) * base

        // 偏移量
        val offset = currentMinutes - nearestBase

        return offset.toLong()
    }
}