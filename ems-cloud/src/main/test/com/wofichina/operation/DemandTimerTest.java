package com.wofichina.operation;

import com.aliyun.lindorm.tsdb.client.model.Record;
import com.wifochina.EmsCloudApplication;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.modules.demand.service.DemandService;
import com.wifochina.modules.demand.vo.OaDemandData;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.service.StrategyService;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-01-31 4:29 PM
 */
@SpringBootTest(classes = EmsCloudApplication.class)
@Slf4j
public class DemandTimerTest {
    @Resource private ProjectService projectService;

    @Resource private StrategyService strategyService;

    @Resource private GroupService groupService;

    @Resource private DemandService demandService;

    @Test
    public void calcDemand() {
        ProjectEntity projectEntity = projectService.getById("");
        Long start = projectEntity.getCreateTime();
        List<GroupEntity> groupEntities =
                groupService.queryEnableDemandControl(projectEntity.getId());
        if (!groupEntities.isEmpty()) {
            for (GroupEntity groupEntity : groupEntities) {
                StrategyEntity strategyEntity =
                        strategyService
                                .lambdaQuery()
                                .eq(StrategyEntity::getGroupId, groupEntity.getId())
                                .eq(StrategyEntity::getWeekDay, 0)
                                .one();
                for (int i = 0; ; i++) {
                    long time = start + (long) EmsConstants.ONE_DAY_SECOND * i;
                    if (time > MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone())) {
                        break;
                    }
                    Map<Long, OaDemandData> controlMap =
                            demandService.getOriginAndActualDemand(
                                    "",
                                    time,
                                    time + EmsConstants.ONE_DAY_SECOND,
                                    projectEntity.getTimezone(),
                                    groupEntity);
                    demandService.saveOriginAndActualDemandToInfluxDb(
                            strategyEntity.getGridControlPower(), controlMap, groupEntity);
                    log.info(
                            "{} calc in {}",
                            groupEntity.getName(),
                            LocalDateTime.ofEpochSecond(
                                    time,
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())));
                }
            }
        }
    }

    /**
     * 4f537620d37d40e19dd25be5ca6ad941 浙江鑫升500kW/1MWh储能系统 6b1939bb30f7427aa09c93755d8f6108
     * 星云电子100kW/233kWh储能系统 2c8a5e961ba9424d8f66cfae73ca549e 广东智道乐100kW/233kWh储能系统
     * 8dca730ac2c641e99fe86f028c620449 无锡为恒100kW/233kWh光储充一体系统 49844578d14941a2821ba6a107950033
     * 常州宏巨电子100kW/233kWh储能系统 e8c3f0faf5ab49908fe6e827f7e7d875 苏州荣威工贸440kW/932kWh储能系统
     * c770a8d8511a4f068b6fb5dcf50e1803 吕梁蔡家崖收费站200kW/466kWh储能系统 f4477a16be694375b2e9778d2862c217
     * 无锡为恒500kW/1MWh储能系统
     */
    @Test
    public void reCalcDemand() {
        // 传 null表示 所有项目的
        List<GroupEntity> groupEntities = groupService.queryEnableDemandControl(null);
        Set<String> projectIdList =
                groupEntities.stream().map(GroupEntity::getProjectId).collect(Collectors.toSet());
        List<ProjectEntity> projectEntities =
                projectService
                        .lambdaQuery()
                        .in(ProjectEntity::getId, new ArrayList<>(projectIdList))
                        .list();
        // 鑫升不需要跑
        List<String> notRun = List.of("4f537620d37d40e19dd25be5ca6ad941");
        for (ProjectEntity projectEntity : projectEntities) {
            if (notRun.contains(projectEntity.getId())) {
                continue;
            }
            List<GroupEntity> demandGroupEntities =
                    groupService.queryEnableDemandControl(projectEntity.getId());
            for (GroupEntity groupEntity : demandGroupEntities) {
                StrategyEntity strategyEntity =
                        strategyService
                                .lambdaQuery()
                                .eq(StrategyEntity::getGroupId, groupEntity.getId())
                                .eq(StrategyEntity::getWeekDay, 0)
                                .one();
                long start =
                        MyTimeUtil.getOneDayZeroTime(
                                projectEntity.getCreateTime(), projectEntity.getTimezone());
                for (int i = 0; ; i++) {
                    long time = start + (long) EmsConstants.ONE_DAY_SECOND * i;
                    log.info(
                            "start {} {} calc in {}",
                            projectEntity.getProjectName(),
                            groupEntity.getName(),
                            LocalDateTime.ofEpochSecond(
                                    time,
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())));
                    if (time > Instant.now().getEpochSecond()) {
                        break;
                    }
                    for (int k = 0; k < 12; k++) {
                        try {

                            Map<Long, OaDemandData> controlMap =
                                    demandService.getOriginAndActualDemand(
                                            "",
                                            time + k * 7200,
                                            time + (k + 1) * 7200,
                                            projectEntity.getTimezone(),
                                            groupEntity);
                            if (CollectionUtils.isEmpty(controlMap)) {
                                log.info(
                                        "control map null {} {} calc in {}",
                                        projectEntity.getProjectName(),
                                        groupEntity.getName(),
                                        LocalDateTime.ofEpochSecond(
                                                time,
                                                0,
                                                MyTimeUtil.getZoneOffsetFromZoneCode(
                                                        projectEntity.getTimezone())));
                                continue;
                            }
                            if (strategyEntity.getGridControlPower() == null) {
                                log.info(
                                        "control_power null {} {} calc in {}",
                                        projectEntity.getProjectName(),
                                        groupEntity.getName(),
                                        LocalDateTime.ofEpochSecond(
                                                time,
                                                0,
                                                MyTimeUtil.getZoneOffsetFromZoneCode(
                                                        projectEntity.getTimezone())));
                                continue;
                            }
                            demandService.saveOriginAndActualDemandToInfluxDb(
                                    strategyEntity.getGridControlPower(), controlMap, groupEntity);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    log.info(
                            "end {} {} calc in {}",
                            projectEntity.getProjectName(),
                            groupEntity.getName(),
                            LocalDateTime.ofEpochSecond(
                                    time,
                                    0,
                                    MyTimeUtil.getZoneOffsetFromZoneCode(
                                            projectEntity.getTimezone())));
                }
            }
        }
    }

    @Test
    public void resetControlPower() {
        List<GroupEntity> groupEntities = groupService.queryEnableDemandControl(null);
        List<ProjectEntity> projectEntities =
                projectService
                        .lambdaQuery()
                        .eq(ProjectEntity::getId, "f4477a16be694375b2e9778d2862c217")
                        .list();
        for (ProjectEntity projectEntity : projectEntities) {
            updateControlPowerOneDay(projectEntity, groupEntities);
        }
    }

    private void updateControlPowerOneDay(
            ProjectEntity projectEntity, List<GroupEntity> groupEntities) {
        List<GroupEntity> demandGroupEntities =
                groupEntities.stream()
                        .filter(e -> e.getProjectId().equals(projectEntity.getId()))
                        .collect(Collectors.toList());
        for (GroupEntity groupEntity : demandGroupEntities) {
            StrategyEntity strategyEntity = getStrategy(groupEntity);
            if (strategyEntity.getControlPower() == null) {
                continue;
            }
            long start = 1705593600;
            for (int i = 0; ; i++) {
                long time = start + (long) EmsConstants.ONE_DAY_SECOND * i;
                if (time > Instant.now().getEpochSecond()) {
                    break;
                }
                for (int j = 0; j < 48; j++) {
                    Map<Long, GroupLindorm> controlMap =
                            getControlPower(
                                    time + j * 1800,
                                    time + (j + 1) * 1800,
                                    groupEntity.getId(),
                                    projectEntity.getId());
                    if (CollectionUtils.isEmpty(controlMap)) {
                        controlMap.put(
                                time,
                                new GroupLindorm(
                                        groupEntity.getId(),
                                        projectEntity.getId(),
                                        projectEntity.getProjectName(),
                                        strategyEntity.getControlPower(),
                                        groupEntity.getWhetherSystem() ? "1" : "0",
                                        time + j * 3600));
                        updateControlPower(controlMap, strategyEntity.getControlPower());
                        continue;
                    }
                    updateControlPower(controlMap, strategyEntity.getControlPower());
                }
                log.info(
                        "{} {} update control_power as {} at {}",
                        projectEntity.getProjectName(),
                        groupEntity.getName(),
                        strategyEntity.getControlPower(),
                        LocalDateTime.ofEpochSecond(
                                time,
                                0,
                                MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone())));
            }
        }
    }

    private Map<Long, GroupLindorm> getControlPower(
            Long start, Long end, String groupId, String projectId) {
        String queryString =
                "select groupId,projectId,projectName,`strategy_grid_control_power`,`system`,`time` from `group` where `time`>={start} and `time`<={end} and groupId ='"
                        + groupId
                        + "' and projectId= '"
                        + projectId
                        + "' ";
        // 将query进行兑换
        queryString = EmsUtil.replaceTime(start, end, queryString);
        Map<Long, GroupLindorm> controlPowerMap = new HashMap<>();
        return controlPowerMap;
    }

    public void updateControlPower(Map<Long, GroupLindorm> groupLindormMap, Double controlPower) {
        if (CollectionUtils.isEmpty(groupLindormMap)) {
            return;
        }
        List<Record> records = new ArrayList<>();
        for (Long time : groupLindormMap.keySet()) {
            GroupLindorm groupLindorm = groupLindormMap.get(time);
            Record record =
                    Record.table("group")
                            .addTag("groupId", groupLindorm.getGroupId())
                            .addTag("projectId", groupLindorm.getProjectId())
                            .addTag("projectName", groupLindorm.getProjectName())
                            .addTag("system", groupLindorm.getSystem())
                            .addField("strategy_grid_control_power", controlPower)
                            .time(time)
                            .build();
            records.add(record);
        }
        // lindormService.write(records);
    }

    private StrategyEntity getStrategy(GroupEntity groupEntity) {
        return strategyService
                .lambdaQuery()
                .eq(StrategyEntity::getGroupId, groupEntity.getId())
                .eq(StrategyEntity::getWeekDay, 0)
                .one();
    }

    @Setter
    @Getter
    public static class GroupLindorm {
        private String groupId;
        private String projectId;
        private String projectName;
        private Double controlPower;
        private String system;

        private Long time;

        public GroupLindorm(
                String groupId,
                String projectId,
                String projectName,
                Double controlPower,
                String system,
                Long time) {
            this.groupId = groupId;
            this.projectId = projectId;
            this.projectName = projectName;
            this.controlPower = controlPower;
            this.system = system;
            this.time = time;
        }

        public GroupLindorm() {}
    }

    public static void main(String[] args) {
        char[] arr =
                new char[] {
                    0, 0, 31, 71, 5, 72, 89, 48, 48, 48, 50, 73, 110, 115, 101, 114, 116, 32, 102,
                    97, 105, 108, 101, 100, 59, 32, 73, 110, 118, 97, 108, 105, 100, 32, 116, 105,
                    109, 101, 115, 116, 97, 109, 112, 58, 32, 49, 55, 48, 53, 53, 48, 55, 50, 48,
                    48, 48, 48, 48, 48, 48, 48
                };
        System.out.println(new String(arr));

        System.out.println(Instant.ofEpochSecond(1705507200).toEpochMilli());
        System.out.println(System.currentTimeMillis());
    }
}
