package com.wofichina.operation;

import com.wifochina.common.time.MyTimeUtil;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 1 建立新表 t_event_message_2024 2 修改旧表 ALTER TABLE t_event_message ADD `event_key` VARCHAR(128)
 * COMMENT '事件编码'; 3 执行 迁移 sql 4 单独迁移今天的数据 5
 */
public class MySqlTestOffset {
    public static void main(String[] args) {
        List<String> insertList = new ArrayList<>();
        List<String> updateList = new ArrayList<>();
        // 2024-01-01
        long beginTime = 1704038400;
        long todayZero = MyTimeUtil.getTodayZeroTime("Asia/Shanghai");
        for (int i = 0; i < 100; i++) {
            long start = beginTime + i * MyTimeUtil.ONE_DAY_SECONDS * 7;
            System.out.println(
                    LocalDate.ofInstant(
                            Instant.ofEpochSecond(start),
                            MyTimeUtil.getZoneOffsetFromZoneCode("Asia/Shanghai")));
            long end = start + MyTimeUtil.ONE_DAY_SECONDS * 7;
            // 2024-07-18
            if (end > todayZero) {
                end = todayZero;
                System.out.println(
                        LocalDate.ofInstant(
                                Instant.ofEpochSecond(end),
                                MyTimeUtil.getZoneOffsetFromZoneCode("Asia/Shanghai")));
                String insertSql =
                        "insert into t_event_message_2024 (select * from t_event_message where create_time >= "
                                + start
                                + " and create_time < "
                                + end
                                + ");";
                insertList.add(insertSql);
                String updateSql =
                        "UPDATE t_event_message_2024 SET `create_time` = `create_time` * 1000, `update_time` = `update_time` * 1000 where where create_time >= "
                                + start
                                + " and create_time < "
                                + end
                                + ";";
                updateList.add(updateSql);
                System.out.println("---------今天的sql--------");
                String todayInsertSql =
                        "insert into t_event_message_2024 (select * from t_event_message where create_time >= "
                                + todayZero
                                + " and create_time < "
                                + (todayZero + MyTimeUtil.ONE_DAY_SECONDS)
                                + ");";
                String todayUpdateSql =
                        "UPDATE t_event_message_2024 SET `create_time` = `create_time` * 1000, `update_time` = `update_time` * 1000 where where create_time >= "
                                + todayZero
                                + " and create_time < "
                                + (todayZero + MyTimeUtil.ONE_DAY_SECONDS)
                                + ";";
                System.out.println(todayInsertSql);
                System.out.println(todayUpdateSql);
                break;
            }
            String insertSql =
                    "insert into t_event_message_2024 (select * from t_event_message where create_time >= "
                            + start
                            + " and create_time < "
                            + end
                            + ");";
            insertList.add(insertSql);
            String updateSql =
                    "UPDATE t_event_message_2024 SET `create_time` = `create_time` * 1000, `update_time` = `update_time` * 1000 where where create_time >= "
                            + start
                            + " and create_time < "
                            + end
                            + ";";

            updateList.add(updateSql);
        }
        System.out.println("insert sql --------------------");
        System.out.println("--------------------------------");
        insertList.forEach(System.out::println);
        System.out.println("update sql --------------------");
        System.out.println("--------------------------------");
        updateList.forEach(System.out::println);
    }
}
