package com.wofichina.notice;

import com.wifochina.EmsCloudApplication;
import com.wifochina.common.util.MessageDigestUtils;
import com.wifochina.modules.electric.vo.ElectricPriceSystemData;
import com.wifochina.modules.notice.service.NoticeEmailService;
import com.wifochina.modules.notice.utils.ProjectMeterReportUtils;
import com.wifochina.modules.notice.utils.data.month.DetailReportData;
import com.wifochina.modules.notice.utils.data.month.DetailReportDatas;
import com.wifochina.modules.notice.utils.data.month.MonthBillReportData;

import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * Created on 2023/9/20 16:59.
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = EmsCloudApplication.class)
@Slf4j
public class NoticeEmailServiceTest {

    @Autowired private NoticeEmailService noticeEmailService;
    @Autowired private RestTemplate restTemplate;

    List<String> receivers = Arrays.asList("<EMAIL>");

    private static final String SALT = "M3T5vWSoK4B4oZu4";
    private static final String NAME = "pangu"; // 请求来源应用名称

    public static void main(String[] args) throws IOException {
        RestTemplate restTemplate1 = new RestTemplate();
        long l = System.currentTimeMillis();
        // 获取当前时间戳（秒级）
        long timestamp = l / 1000;

        // 生成待加密字符串
        String authString = NAME + timestamp + SALT;

        // 使用SHA-256对待加密字符串进行加密
        String sha256Hash = MessageDigestUtils.sha256(authString);

        // 截取加密串的第10到20位
        String authSubString = sha256Hash.substring(10, 20);

        // 生成签名
        String sign = MessageDigestUtils.base64Encode(NAME + ":" + timestamp + ":" + authSubString);

        // 打印生成的签名
        System.out.println("Generated Sign: " + sign);
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Sign", sign);

        // 创建HttpEntity，包含请求头
        HttpEntity<String> entity = new HttpEntity<>(headers);
        // 使用签名进行API调用
        String apiUrl =
                "http://nginx.dev.weiheng-tech.com:31146/data/v1/dayAheadPrice?country=Germany&region=DE&startTime=1716912000&endTime=1716998399&dataSource=Entsoe&_t="
                        + l;
        ResponseEntity<ElectricPriceSystemData> response =
                restTemplate1.exchange(
                        apiUrl, HttpMethod.GET, entity, ElectricPriceSystemData.class);
        ElectricPriceSystemData body = response.getBody();

        System.out.println(body);
        HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection();
        connection.setRequestProperty("Sign", sign);
        connection.setRequestMethod("GET");

        // 处理响应
        int responseCode = connection.getResponseCode();
        System.out.println("Response Code: " + responseCode);
        if (responseCode == HttpURLConnection.HTTP_OK) {
            Scanner scanner = new Scanner(connection.getInputStream());
            String responseBody = scanner.useDelimiter("\\A").next();
            System.out.println("Response Body: " + responseBody);
            scanner.close();
        } else {
            System.out.println("Failed to call API");
        }
    }

    @Test
    public void testDynamicPriceRest() {

        long timestamp = System.currentTimeMillis();
        // restTemplate.getForObject("http://nginx.dev.weiheng-tech.com:31146/data/v1/dayAheadPrice?country=Germany&region=DE&startTime=1716912000&endTime=1716998399&timeZone=CET&dataSource=Entsoe")

    }

    @Test
    public void testSendMonEmail() {

        noticeEmailService.sendMonthReportEmail(
                new NoticeEmailService.MonthDataHolder() {

                    @Override
                    public MonthBillReportData billData() {
                        // 初始化模拟数据 sheet1
                        return ProjectMeterReportUtils.initMonBillData();
                    }

                    @Override
                    public DetailReportDatas detailDatas() {
                        // sheet2 明细数据
                        Map<String, DetailReportData> map = new HashMap<>();
                        map.put(
                                "systemGroupId",
                                new DetailReportData()
                                        .setTitleName("为恒项目7月用电明细")
                                        .setDetailReports(
                                                ProjectMeterReportUtils.getDetailReports()));
                        return new DetailReportDatas().setDetailReportDataMap(map);
                    }
                },
                receivers);
    }

    @Test
    public void testSendYearEmail() {
        //        noticeEmailService.sendYearReportEmail(
        //                ProjectMeterReportUtils::initYearBillData, receivers);
    }
}
