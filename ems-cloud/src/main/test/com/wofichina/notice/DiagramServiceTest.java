package com.wofichina.notice;

import com.wifochina.EmsCloudApplication;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.diagram.request.RequestWithGroupId;
import com.wifochina.modules.diagram.service.AbstractDiagramExtendService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.*;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 2024/3/6 18:37.
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = EmsCloudApplication.class)
@Slf4j
public class DiagramServiceTest {

    @Autowired private AbstractDiagramExtendService diagramExtendService;

    @Autowired private ProjectService projectService;

    @Test
    public void testTxtToExcel() {

        // 读取 所有的.txt文件
    }

    @Test
    public void testGetEfficiencyRate() {
        //
        List<ProjectEntity> projectEntities =
                projectService
                        .lambdaQuery()
                        .eq(ProjectEntity::getWhetherDelete, false)
                        .eq(ProjectEntity::getProjectModel, 0)
                        .list();
        projectEntities.forEach(
                projectEntity -> {
                    log.info("projectEntity: {}", projectEntity);
                    String fileName =
                            projectEntity.getProjectName().replace("/", "_")
                                    + "_"
                                    + projectEntity.getId()
                                    + ".txt";

                    if (new File(fileName).exists()) {
                        log.warn("file exists, skip: {}", fileName);
                        return;
                    }
                    LocalDate startDate = LocalDate.of(2024, 1, 1);
                    LocalDate endDate = LocalDate.of(2024, 3, 6);
                    LocalDateTime startTime = startDate.atStartOfDay(); // 2024-01-01 00:00:00
                    ZoneOffset zoneOffsetFromZoneCode =
                            MyTimeUtil.getZoneOffsetFromZoneCode(projectEntity.getTimezone());
                    long startTimeEpochSecond =
                            startTime.toEpochSecond(zoneOffsetFromZoneCode); // UTC时区的时间戳

                    long endTimeEpochSecond =
                            endDate.atStartOfDay().toEpochSecond(zoneOffsetFromZoneCode);
                    if (projectEntity.getCreateTime() > startTimeEpochSecond) {
                        log.warn(
                                "{} createTime > startDate, use createTime as startTimeEpochSecond",
                                projectEntity.getId());
                        startTimeEpochSecond = projectEntity.getCreateTime();
                    }

                    WebUtils.projectId.set(projectEntity.getId());
                    RequestWithGroupId requestWithGroupId = new RequestWithGroupId();
                    requestWithGroupId.setGroupId("all");
                    requestWithGroupId.setStartDate(startTimeEpochSecond);
                    requestWithGroupId.setEndDate(endTimeEpochSecond);
                    // 将日期时间转换为时间戳
                    Map<Long, Double> efficiencyRate = new HashMap<>();
                    try {
                        efficiencyRate = diagramExtendService.getEfficiencyRate(requestWithGroupId);
                    } catch (Exception e) {
                        log.error(
                                "{} {}error",
                                projectEntity.getProjectName(),
                                projectEntity.getId(),
                                e.getMessage());
                        return;
                    }
                    try (PrintWriter writer = new PrintWriter(new FileWriter(fileName))) {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        for (Map.Entry<Long, Double> entry : efficiencyRate.entrySet()) {
                            LocalDateTime dateTime =
                                    LocalDateTime.ofInstant(
                                            Instant.ofEpochSecond(entry.getKey()),
                                            MyTimeUtil.getZoneOffsetFromZoneCode(
                                                    projectEntity.getTimezone()));
                            String formattedDate = dateTime.format(formatter);
                            writer.println(formattedDate + " " + entry.getValue());
                        }
                        System.out.println("Efficiency rates have been written to " + fileName);
                    } catch (IOException e) {
                        System.err.println("Error writing to file: " + e.getMessage());
                    }
                    log.info(
                            "projectId :{} , efficiencyRate: {}",
                            projectEntity.getId(),
                            efficiencyRate);
                });
    }
}
