package com.wofichina.project;

import com.wifochina.EmsCloudApplication;
import com.wifochina.common.util.HashUtil;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import javax.annotation.Resource;

@SpringBootTest(classes = EmsCloudApplication.class)
@ActiveProfiles("proxy-test")
public class UpdateProjectHashTest {

    @Resource private ProjectService projectService;

    @Test
    public void updateProjectHash() {
        List<ProjectEntity> projectEntities =
                projectService.lambdaQuery().isNull(ProjectEntity::getHash).list();
        projectEntities.forEach(
                projectEntity -> {
                    projectService
                            .lambdaUpdate()
                            .set(ProjectEntity::getHash, HashUtil.elfHash(projectEntity.getId()))
                            .eq(ProjectEntity::getId, projectEntity.getId())
                            .update();
                });
    }
}
