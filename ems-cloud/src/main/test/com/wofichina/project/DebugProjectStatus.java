package com.wofichina.project;

import com.wifochina.EmsCloudApplication;
import com.wifochina.modules.manage.project.timer.TaskProjectTimer;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

@SpringBootTest(classes = EmsCloudApplication.class)
@ActiveProfiles("proxy-dev")
public class DebugProjectStatus {
    @Autowired private TaskProjectTimer taskProjectTimer;

    @Test
    public void test() throws InterruptedException {
        taskProjectTimer.collectProjectPoint();
        // 等待所有任务执行完毕，最长等待 60 秒
        if (!taskProjectTimer.getExecutorService().awaitTermination(5, TimeUnit.MINUTES)) {
            System.out.println("任务未在规定时间内完成");
        }
    }
}
