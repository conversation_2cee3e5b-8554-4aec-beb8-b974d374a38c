package com.wofichina


import com.wifochina.EmsCloudApplication
import com.wifochina.modules.oauth.utils.AccountAuthUtils
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest(classes = [EmsCloudApplication::class])
class MyServiceTest {

    @Autowired
    private lateinit var accountAuthUtils: AccountAuthUtils

    @Test
    fun `test addition`() {
        accountAuthUtils.getAccountAuthHeader().forEach(
            { (k, v) -> println("$k: $v") }
        )
    }
}