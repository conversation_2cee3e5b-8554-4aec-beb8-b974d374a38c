package com.wofichina;

import com.wifochina.common.config.LoggingInterceptor;
import com.wifochina.modules.oauth.dto.AccountResult;
import com.wifochina.modules.oauth.dto.CaptchaCode;

import okhttp3.OkHttpClient;

import org.junit.jupiter.api.Test;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created on 2024/11/7 15:35.
 *
 * <AUTHOR>
 */
public class RestTest {

    @Test
    public void test01() {
        RestTemplate restTemplate = new RestTemplate();
        OkHttpClient okHttpClient =
                new OkHttpClient.Builder()
                        .connectTimeout(3, TimeUnit.SECONDS)
                        .readTimeout(4, TimeUnit.SECONDS)
                        .retryOnConnectionFailure(false)
                        .connectionPool(new okhttp3.ConnectionPool(10, 5, TimeUnit.MINUTES))
                        .build();

        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
        restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(factory));
        restTemplate.setInterceptors(List.of(new LoggingInterceptor()));
        HttpEntity<Object> objectHttpEntity = new HttpEntity(null, null);
        ResponseEntity<AccountResult<CaptchaCode>> exchange =
                restTemplate.exchange(
                        "http://account-api.dev.weiheng-tech.com/api/v1/captcha/code",
                        HttpMethod.GET,
                        objectHttpEntity,
                        new ParameterizedTypeReference<AccountResult<CaptchaCode>>() {});
        System.out.println(exchange.getBody());
    }
}
