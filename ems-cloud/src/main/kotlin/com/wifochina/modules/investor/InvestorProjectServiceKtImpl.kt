package com.wifochina.modules.investor

import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.modules.investor.entity.InvestorEntity
import com.wifochina.modules.investor.entity.InvestorProjectEntity
import com.wifochina.modules.investor.service.impl.InvestorProjectServiceImpl
import com.wifochina.modules.oauth.util.SecurityUtil
import com.wifochina.modules.project.service.ProjectServiceKt
import org.springframework.stereotype.Component
import java.util.Objects

@Component
class InvestorProjectServiceKtImpl(val projectServiceKt: ProjectServiceKt) : InvestorProjectServiceImpl() {
    override fun queryInvestIdsByUserId(userId: String): List<String> {
        return queryInvestorProjectByUserId(userId).map { it.investorId }
    }

    override fun queryInvestorProjectByUserId(userId: String): List<InvestorProjectEntity> {
        val projectListByUserIdAndRoleId = projectServiceKt.getProjectListByUserIdAndRoleId(
            userId, Objects.requireNonNull(SecurityUtil.getLoginRoleId())
        )
        return projectListByUserIdAndRoleId?.takeIf { it.isNotEmpty() }?.let {
            this.lambdaQuery().`in`(
                InvestorProjectEntity::getProjectId, it.keys
            ).list()
        } ?: emptyList()
    }

    override fun queryInvestorsByUserId(userId: String): List<InvestorEntity> {
        return userId.takeIf { userId.isNotEmpty() }?.run {
            queryInvestIdsByUserId(userId).takeIf { it.isNotEmpty() }
                ?.run { investorService.lambdaQuery().`in`(InvestorEntity::getId, this).list() }
        } ?: emptyList()
    }
}