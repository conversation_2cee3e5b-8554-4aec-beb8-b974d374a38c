package com.wifochina.modules.project.service.impl

import com.wifochina.common.config.SystemConfiguration
import com.wifochina.common.constants.TimePointEnum
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.mdc.MdcKotlinContext
import com.wifochina.common.utils.RestTemplateUtils
import com.wifochina.common.utils.dataClassToMutableMap
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.income.NewOperationProfitService
import com.wifochina.modules.income.vo.OperationProfitVo
import com.wifochina.modules.oauth.constants.AuthConstants.AUTHORIZATION
import com.wifochina.modules.oauth.dto.AccountResult
import com.wifochina.modules.oauth.service.AccountSystemReqService
import com.wifochina.modules.oauth.util.SecurityUtil
import com.wifochina.modules.oauth.utils.AccountAuthUtils
import com.wifochina.modules.operation.ManageProjectOperation
import com.wifochina.modules.project.dto.ProjectCreateDto
import com.wifochina.modules.project.dto.ProjectListPage
import com.wifochina.modules.project.service.ProjectServiceKt
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component

@Component
class ProjectServiceKtImpl(
    val accountSystemReqService: AccountSystemReqService,
    val restTemplateUtils: RestTemplateUtils,
    val accountAuthUtils: AccountAuthUtils,
    val systemConfiguration: SystemConfiguration,
    val newOperationProfitService: NewOperationProfitService,
    val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
) : ProjectServiceKt {
    companion object {
        const val PROJECTS_QUERY_API = "/api/v1/user/resource/list"
        const val PROJECT_CREATE_API = "/api/v1/resource"
        const val PROJECT_UPDATE_API = "/api/v1/resource"
        const val PROJECT_DELETE_API = "/api/v1/resource"
    }


    override fun getCurrentProjectList(loginRoleId: String, sessionId: String): Map<String, String>? {
        val paraMap = mutableMapOf<String, Any>()
        val headerMap = accountAuthUtils.getAccountAuthHeader()
        headerMap[AUTHORIZATION] = sessionId
        paraMap["dataCenter"] = systemConfiguration.accountSystem.dataCenter.toInt()
        paraMap["appId"] = systemConfiguration.accountSystem.appId.toInt()
        paraMap["loginRoleId"] = loginRoleId
        paraMap["limit"] = -1
        paraMap["offset"] = -1
        val result = restTemplateUtils.get(
            systemConfiguration.accountSystem.accountUrl + PROJECTS_QUERY_API,
            paraMap,
            object : ParameterizedTypeReference<AccountResult<ProjectListPage>>() {},
            headerMap,
            false
        )

        return result?.run {
            if (status == 200) {
                data?.data?.associate { it.id to it.name }
            } else {
                null
            }
        } ?: run {
            null
        }
    }

    override fun createProject(id: String, name: String, country: Int) {
        val projectCreate = ProjectCreateDto(roleId = SecurityUtil.getLoginRoleId())
        projectCreate.id = id
        projectCreate.code = id
        projectCreate.name = name
        projectCreate.dataCenter = systemConfiguration.accountSystem.dataCenter.toInt()
        projectCreate.installArea = country

        restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + PROJECT_CREATE_API,
            projectCreate.dataClassToMutableMap(),
            object : ParameterizedTypeReference<AccountResult<Any>>() {},
            mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()),
            true
        )?.run {
            if (status == 200) Unit else throw ServiceException(desc)
        } ?: Unit
    }

    override fun updateProject(id: String, name: String?, installArea: Int?) {
        val paraMap = mutableMapOf<String, Any>()
        name?.let { paraMap["name"] = it }
        installArea?.let { paraMap["installArea"] = it }
        return accountSystemReqService.commonReq(
            "$PROJECT_UPDATE_API/$id", HttpMethod.PUT, mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()), paraMap
        )?.run {
            if (status == 200) Unit else throw ServiceException(desc)
        } ?: Unit

    }

    override fun delResource(id: String) {
        val bodyMap = mutableMapOf<String, Any>("appId" to systemConfiguration.accountSystem.appId)
        bodyMap["loginRoleId"] = SecurityUtil.getLoginRoleId()
        bodyMap["ids"] = listOf(id)
        return accountSystemReqService.commonReq(
            PROJECT_DELETE_API, HttpMethod.DELETE, mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()), bodyMap
        )?.run {
            if (status == 200) Unit else throw ServiceException(desc)
        } ?: Unit

    }

    override fun fillProjectsProfit(
        projects: List<String>, projectsSystemGroupMap: Map<String, GroupEntity>, vararg timePointEnum: TimePointEnum
    ): Map<String, ManageProjectOperation?> {
        val map = mutableMapOf<String, ManageProjectOperation>()
        runBlocking {
            withContext(threadPoolTaskExecutor.asCoroutineDispatcher() + MdcKotlinContext()) {
                projects.forEach { projectId ->
                    // 只有项目的系统分组开启了 计算收益模式 (储能收益开关)  才进行关联返回
                    projectsSystemGroupMap[projectId]?.takeIf { it.calcEarningsController != null && it.calcEarningsController }
                        ?.let {
                            launch {
                                //从缓存中查询到 收益 填充到 项目对象里面
                                val result = newOperationProfitService.getTotalOperationProfit(
                                    true, projectId, TimePointEnum.TODAY, TimePointEnum.YESTERDAY
                                )
                                map[projectId] = ManageProjectOperation(
                                    (result[TimePointEnum.TODAY.value] as? OperationProfitVo),
                                    (result[TimePointEnum.YESTERDAY.value] as? OperationProfitVo)
                                )
                            }
                        }
                }
            }
        }
        return map
    }


    override fun getProjectListByUserIdAndRoleId(userId: String, roleId: String): Map<String, String>? {
        val paraMap = mutableMapOf<String, Any>()
        val headerMap = accountAuthUtils.getAccountAuthHeader()
        headerMap[AUTHORIZATION] = accountAuthUtils.getSessionId()
        paraMap["dataCenter"] = systemConfiguration.accountSystem.dataCenter.toInt()
        paraMap["appId"] = systemConfiguration.accountSystem.appId.toInt()
        paraMap["loginRoleId"] = SecurityUtil.getLoginRoleId()
        paraMap["userId"] = userId
        paraMap["roleId"] = roleId
        paraMap["limit"] = -1
        paraMap["offset"] = -1

        val result = restTemplateUtils.get(
            systemConfiguration.accountSystem.accountUrl + PROJECTS_QUERY_API,
            paraMap,
            object : ParameterizedTypeReference<AccountResult<ProjectListPage>>() {},
            headerMap,
            false
        )

        return result?.run {
            if (status == 200) {
                data?.data?.associate { it.id to it.name }
            } else {
                null
            }
        } ?: run {
            null
        }
    }
}