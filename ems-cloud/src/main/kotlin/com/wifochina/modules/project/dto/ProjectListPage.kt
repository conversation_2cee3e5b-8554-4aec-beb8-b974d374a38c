package com.wifochina.modules.project.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class ProjectListPage(
    val count: Int, val offset: Int, val limit: Int, val `data`: List<Project>?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Project(
    val id: String,
    val type: Int,
    val category: Int,
    val categoryName: String,
    val typeName: String,
    val code: String,
    val name: String,
    val remark: String,
    val detail: String,
    val source: Int,
    val sourceType: Int,
    val dataCenter: Int,
    val datacenter: Int,
    val installArea: Int,
    val dataCenterCode: String,
    val dataCenterName: String,
    val deliverArea: Int,
    val deliverAreaName: String,
    val expireAt: Long,
    val expiredAt: Long,
    val ct: Long,
    val ut: Long,
    val userId: String,
    val email: String,
    val roleId: String,
    val subUserId: String,
    val subEmail: String,
    val subRoleId: String,
    val applicationId: String,
    val unbindState: Int
)
