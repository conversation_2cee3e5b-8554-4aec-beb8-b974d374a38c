package com.wifochina.modules.project.service

import com.wifochina.common.constants.TimePointEnum
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.operation.ManageProjectOperation

interface ProjectServiceKt {
    fun getCurrentProjectList(loginRoleId: String, sessionId: String): Map<String, String>?

    fun getProjectListByUserIdAndRoleId(userId: String, roleId: String): Map<String, String>?

    fun createProject(id: String, name: String, country: Int)

    fun updateProject(id: String, name: String?, installArea: Int?)

    fun delResource(id: String)


    /**
     * 填充项目收益 的接口
     */
    fun fillProjectsProfit(
        projects: List<String>,
        projectsSystemGroupMap: Map<String, GroupEntity>,
        vararg timePointEnum: TimePointEnum,
    ): Map<String, ManageProjectOperation?>
}