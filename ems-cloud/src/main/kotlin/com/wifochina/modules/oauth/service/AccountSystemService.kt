package com.wifochina.modules.oauth.service

import com.wifochina.modules.oauth.AccountUser
import com.wifochina.modules.oauth.dto.*
import com.wifochina.modules.oauth.req.AccountAuthenticationReq
import com.wifochina.modules.oauth.req.AccountListPageReq
import com.wifochina.modules.oauth.req.AccountUserInfoReq
import com.wifochina.modules.oauth.req.AuthorizationEmailCheckReq
import com.wifochina.modules.oauth.req.AuthorizationRoleCancelReq
import com.wifochina.modules.oauth.req.AuthorizationRoleReq
import com.wifochina.modules.oauth.req.BindEmailReq
import com.wifochina.modules.oauth.req.BindPhoneReq
import com.wifochina.modules.oauth.req.BingResourceReq
import com.wifochina.modules.oauth.req.CancelUserReq
import com.wifochina.modules.oauth.req.CaptchaCodeCheckReq
import com.wifochina.modules.oauth.req.ChangePasswordReq
import com.wifochina.modules.oauth.req.EmailCodeCheckReq
import com.wifochina.modules.oauth.req.EmailCodeReq
import com.wifochina.modules.oauth.req.EmailUsernameCheckReq
import com.wifochina.modules.oauth.req.ForgetPasswordReq
import com.wifochina.modules.oauth.req.NameFindEmailReq
import com.wifochina.modules.oauth.req.PhoneCodeReq
import com.wifochina.modules.oauth.req.RoleAuthorityConfigReq
import com.wifochina.modules.oauth.req.RoleCreateReq
import com.wifochina.modules.oauth.req.RoleDeleteReq
import com.wifochina.modules.oauth.req.RoleListPageReq
import com.wifochina.modules.oauth.req.RoleUpdateReq
import com.wifochina.modules.oauth.req.UpdateAuthorizationReq
import com.wifochina.modules.oauth.req.UpdateMarkReq
import com.wifochina.modules.oauth.req.UpdateUserReq
import com.wifochina.modules.oauth.req.UserListReq
import com.wifochina.modules.oauth.req.UserRegisterReq

/**
 * Created on 2024/11/5 17:00.
 * <AUTHOR>
 */

interface AccountSystemService {
    fun login(userDto: UserDto): AccountUser?
    fun authentication(accountAuthenticationReq: AccountAuthenticationReq): AccountUser?
    fun captchaCode(): CaptchaCode?
    fun phoneCode(phoneCodeReq: PhoneCodeReq)
    fun emailCode(emailCodeReq: EmailCodeReq, sessionId: String?)
    fun forgetPassword(passwordChangeReq: ForgetPasswordReq)
    fun changePassword(changePasswordReq: ChangePasswordReq, sessionId: String?)
    fun bindPhone(bindPhoneReq: BindPhoneReq, sessionId: String?)
    fun updateUser(updateUserReq: UpdateUserReq, sessionId: String?)
    fun bindEmail(bindEmailReq: BindEmailReq, sessionId: String?)
    fun accountListPage(accountListPageReq: AccountListPageReq): AccountListPage?
    fun roleListPage(roleListPageReq: RoleListPageReq): RoleListPage?
    fun captchaCodeCheck(captchaCodeCheckReq: CaptchaCodeCheckReq)
    fun authorizationEmailCheck(authorizationEmailCheckReq: AuthorizationEmailCheckReq): EmailCheckResult?
    fun userList(userListReq: UserListReq): List<AccountUser>?
    fun authorizationRole(authorizationRoleReq: AuthorizationRoleReq)
    fun roleCreate(roleCreateReq: RoleCreateReq)
    fun roleUpdate(roleUpdateReq: RoleUpdateReq)
    fun roleDelete(roleDeleteReq: RoleDeleteReq)
    fun authorizationRoleCancel(authorizationRoleCancelReq: AuthorizationRoleCancelReq)
    fun roleAuthorityConfig(roleAuthorityConfigReq: RoleAuthorityConfigReq): List<RoleAuthorityConfigResult>?
    fun userRegister(userRegisterReq: UserRegisterReq)
    fun emailUsernameCheck(emailUsernameCheckReq: EmailUsernameCheckReq)
    fun subUserList(): AccountListPage?
    fun cancelUser(cancelUserReq: CancelUserReq, sessionId: String?)
    fun logout(sessionId: String?)
    fun roleList(sessionId: String): List<RoleInfo>?
    fun userInfo(sessionId: String): AccountUser?
    fun bingResource(bingResourceReq: BingResourceReq)
    fun nameFindEmail(nameFindEmailReq: NameFindEmailReq): AccountUser?
    fun emailCodeCheck(emailCodeCheckReq: EmailCodeCheckReq)
    fun updateMark(updateMarkReq: UpdateMarkReq)
    fun updateAuthorization(updateAuthorizationReq: UpdateAuthorizationReq)
    fun accountUserInfo(accountUserInfoReq: AccountUserInfoReq): AccountUserInfo?
    fun getUserByUsername(username: String): AccountInnerResult?
    fun getUserListByUserIds(userIds: List<String>): AccountInnerResult?
}