package com.wifochina.modules.oauth.req

import com.wifochina.common.page.PageBean
import javax.validation.constraints.NotEmpty
import javax.validation.constraints.NotNull

/**
 * Created on 2024/11/12 11:47.
 * <AUTHOR>
 */

data class AccountListPageReq(
    @field:NotNull val loginRoleId: String?,
    @field:NotNull val parentRoleId: String?,
    //要把Int变成Int?  变成可空类型 这样才会触发 NotNull校验, 否则 Int 是非空类型默认是0
    @field:NotEmpty val parentUserId: String?,
    val roleId: String? = null,
    val userId: String? = null,
    val account: String? = null,
    val username: String? = null,
    val nickname: String? = null,
    val resourceName: String? = null,
    val allDescendants: Boolean? = null,
    val offset: Int? = null,
    val limit: Int? = null,
    val withResource: Boolean? = true,
) : PageBean()