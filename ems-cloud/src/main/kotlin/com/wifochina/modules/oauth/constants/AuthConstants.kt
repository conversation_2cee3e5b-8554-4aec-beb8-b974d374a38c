package com.wifochina.modules.oauth.constants

/**
 * Created on 2024/11/6 16:33.
 * <AUTHOR>
 */
object AuthConstants {

    const val MANAGE_AUTHORITY_FLAG = "/manage"
    const val AUTHORIZATION = "Authorization"
    const val SESSION_ID = "SessionId"
    const val ACCOUNT_SYSTEM_SESSION_ID_HEADER = "AccountSystemSessionId"
    const val ACCOUNT_SYSTEM_ROLE_ID = "AccountSystemRoleId"
    const val APP_ID = "appId"
    const val APPLICATION_ID = "applicationId"
    const val BEARER = "bearer"
    const val REDIS_USER_PREFIX = "user:id:"
    const val ACCOUNT_SYSTEM_REQ_REDIS_SESSION_ID_PREFIX = "account_system_req_user:session_id:"
    const val ROLE_ID = "roleId"
    const val LOGIN_ROLE_ID = "loginRoleId"
    const val USER_ID = "userId"
    const val RESOURCE_CODES = "resourceCodes"
    const val ALL_RESOURCE_STAT = "allResourceStat"
    const val INHERIT_RESOURCE_STAT = "inheritResourceStat"
    const val ALL_RESOURCE_STAT_DATACENTER = "allResourceStatDatacenter"

    const val NICK_NAME_DEST = "nickName"
    const val NICK_NAME_ORIGIN = "nickname"

    const val TYPE = "type"
    const val ACCOUNT = "account"
    const val PASSWORD = "password"
    const val PHONE_LOC_CODE = "phoneLocCode"
    const val CODE = "code"
    const val LOC_CHINA_CODE = "86"


    object AuthenticationResultConstants {
        const val AUTHORITY = "authority"
        const val PROJECT = "project"
        const val ACCESS_TOKEN = "access_token"
        const val INFO = "info"
        const val HAS_CONTROLLABLE = "hasControllable"
        const val HAS_CAMERA = "hasCamera"
        const val HAS_METER = "hasMeter"
    }


    object PageConstants {
        const val OFFSET = "offset"
        const val LIMIT = "limit"
    }

}