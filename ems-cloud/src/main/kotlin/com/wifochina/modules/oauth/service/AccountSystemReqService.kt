package com.wifochina.modules.oauth.service

import com.wifochina.modules.oauth.AccountUser
import com.wifochina.modules.oauth.dto.*
import com.wifochina.modules.oauth.req.AccountListPageReq
import com.wifochina.modules.oauth.req.AuthorizationEmailCheckReq
import com.wifochina.modules.oauth.req.AuthorizationRoleCancelReq
import com.wifochina.modules.oauth.req.AuthorizationRoleReq
import com.wifochina.modules.oauth.req.BingResourceReq
import com.wifochina.modules.oauth.req.RoleCreateReq
import com.wifochina.modules.oauth.req.RoleDeleteReq
import com.wifochina.modules.oauth.req.RoleListPageReq
import com.wifochina.modules.oauth.req.RoleUpdateReq
import com.wifochina.modules.oauth.req.UserListReq
import com.wifochina.modules.oauth.req.UserRegisterReq
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod

/**
 * Created on 2024/11/5 15:40.
 * <AUTHOR>
 */
interface AccountSystemReqService {

    fun login(bodyMap: MutableMap<String, Any>): AccountResult<AccountUser>?

    fun authentication(bodyMap: MutableMap<String, Any>): AccountResult<AccountUser>?
    fun captchaCode(): AccountResult<CaptchaCode>?

    fun commonReq(
        api: String, httpMethod: HttpMethod, headerMap: Map<String, String>, bodyMap: MutableMap<String, Any>
    ): AccountNoResult?

    fun <T> commonReqResultNew(
        api: String,
        httpMethod: HttpMethod,
        headerMap: Map<String, String>,
        bodyMap: MutableMap<String, Any>,
        responseType: ParameterizedTypeReference<T>
    ): T?

    fun accountListPage(accountListPageReq: AccountListPageReq): AccountResult<AccountListPage>?
    fun roleListPage(roleListPageReq: RoleListPageReq): AccountResult<RoleListPage>?
    fun emailCheck(authorizationEmailCheckReq: AuthorizationEmailCheckReq): AccountResult<EmailCheckResult>?

    fun userList(userListReq: UserListReq): AccountResult<List<AccountUser>>?
    fun authorizationRole(authorizationRoleReq: AuthorizationRoleReq): AccountResult<Unit>?
    fun roleCreate(roleCreateReq: RoleCreateReq): AccountResult<Unit>?
    fun roleDelete(roleDeleteReq: RoleDeleteReq): AccountResult<Unit>?
    fun roleUpdate(roleUpdateReq: RoleUpdateReq): AccountResult<Unit>?

    fun authorizationRoleCancel(authorizationRoleCancelReq: AuthorizationRoleCancelReq): AccountResult<Unit>?
    fun userRegister(userRegisterReq: UserRegisterReq): AccountResult<Unit>?
    fun bingResource(bingResourceReq: BingResourceReq): AccountNoResult?
    fun getUserByUsername(username: String): AccountResult<AccountInnerResult>?
    fun getUserListByUserIds(userIds: List<String>): AccountResult<AccountInnerResult>?
}