package com.wifochina.modules.oauth.service.impl

import com.wifochina.common.config.SystemConfiguration
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.util.StringUtil
import com.wifochina.common.utils.dataClassToMutableMap
import com.wifochina.modules.oauth.AccountUser
import com.wifochina.modules.oauth.constants.AuthConstants
import com.wifochina.modules.oauth.constants.AuthConstants.ACCOUNT
import com.wifochina.modules.oauth.constants.AuthConstants.APP_ID
import com.wifochina.modules.oauth.constants.AuthConstants.AUTHORIZATION
import com.wifochina.modules.oauth.constants.AuthConstants.CODE
import com.wifochina.modules.oauth.constants.AuthConstants.LOC_CHINA_CODE
import com.wifochina.modules.oauth.constants.AuthConstants.PASSWORD
import com.wifochina.modules.oauth.constants.AuthConstants.PHONE_LOC_CODE
import com.wifochina.modules.oauth.constants.AuthConstants.REDIS_USER_PREFIX
import com.wifochina.modules.oauth.constants.AuthConstants.ROLE_ID
import com.wifochina.modules.oauth.constants.AuthConstants.TYPE
import com.wifochina.modules.oauth.dto.*
import com.wifochina.modules.oauth.req.AccountAuthenticationReq
import com.wifochina.modules.oauth.req.AccountListPageReq
import com.wifochina.modules.oauth.req.AccountUserInfoReq
import com.wifochina.modules.oauth.req.AuthorizationEmailCheckReq
import com.wifochina.modules.oauth.req.AuthorizationRoleCancelReq
import com.wifochina.modules.oauth.req.AuthorizationRoleReq
import com.wifochina.modules.oauth.req.BindEmailReq
import com.wifochina.modules.oauth.req.BindPhoneReq
import com.wifochina.modules.oauth.req.BingResourceReq
import com.wifochina.modules.oauth.req.CancelUserReq
import com.wifochina.modules.oauth.req.CaptchaCodeCheckReq
import com.wifochina.modules.oauth.req.ChangePasswordReq
import com.wifochina.modules.oauth.req.EmailCodeCheckReq
import com.wifochina.modules.oauth.req.EmailCodeReq
import com.wifochina.modules.oauth.req.EmailUsernameCheckReq
import com.wifochina.modules.oauth.req.ForgetPasswordReq
import com.wifochina.modules.oauth.req.NameFindEmailReq
import com.wifochina.modules.oauth.req.PhoneCodeReq
import com.wifochina.modules.oauth.req.RoleAuthorityConfigReq
import com.wifochina.modules.oauth.req.RoleCreateReq
import com.wifochina.modules.oauth.req.RoleDeleteReq
import com.wifochina.modules.oauth.req.RoleListPageReq
import com.wifochina.modules.oauth.req.RoleUpdateReq
import com.wifochina.modules.oauth.req.UpdateAuthorizationReq
import com.wifochina.modules.oauth.req.UpdateMarkReq
import com.wifochina.modules.oauth.req.UpdateUserReq
import com.wifochina.modules.oauth.req.UserListReq
import com.wifochina.modules.oauth.req.UserRegisterReq
import com.wifochina.modules.oauth.service.AccountSystemReqService
import com.wifochina.modules.oauth.service.AccountSystemService
import com.wifochina.modules.oauth.util.SecurityUtil
import com.wifochina.modules.oauth.utils.AccountAuthUtils
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.core.ParameterizedTypeReference
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }

/**
 * Created on 2024/11/5 17:04.
 * <AUTHOR>
 */
@Service
class AccountSystemServiceImpl(
    val accountSystemReqService: AccountSystemReqService,
    val accountAuthUtils: AccountAuthUtils,
    val redisTemplate: RedisTemplate<String, String>,
    val systemConfiguration: SystemConfiguration
) : AccountSystemService {

    /**
     * 调用账号系统, 进行账号登录
     */
    override fun login(userDto: UserDto): AccountUser? {
        val requestMap = mutableMapOf<String, Any>()
        //账号密码登录
        if (userDto.type == 0) {
            requestMap[TYPE] = 1
            requestMap[ACCOUNT] = userDto.username
            requestMap[PASSWORD] = userDto.password
        }
        //手机号登录
        if (userDto.type == 1) {
            //手机号登录 type = 4
            requestMap[TYPE] = 4
            requestMap[ACCOUNT] = userDto.phone
            //手机号前缀码
            requestMap[PHONE_LOC_CODE] = userDto.phoneLocCode ?: LOC_CHINA_CODE
            requestMap[CODE] = userDto.code
        }
        //邮箱登录
        if (userDto.type == 2) {
            requestMap[TYPE] = 3
            requestMap[ACCOUNT] = userDto.email
            requestMap[CODE] = userDto.code
        }
        return accountSystemReqService.login(requestMap)?.run {
            when (status) {
                200 -> data
                //其他错误都算 用户名密码错误
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        }
    }

    /**
     * 调用账号系统 认证接口
     */
    override fun authentication(accountAuthenticationReq: AccountAuthenticationReq): AccountUser? {
        return accountSystemReqService.authentication(mutableMapOf(ROLE_ID to accountAuthenticationReq.roleId))?.run {
            if (status == 200) data else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        }
    }

    /**
     * 调用账号系统 获取滑块图形验证码信息 给前端
     */
    override fun captchaCode(): CaptchaCode? {
        return accountSystemReqService.captchaCode()?.run {
            if (status == 200) data else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        }
    }


    /**
     * 调用账号系统 发送手机验证码
     * @see HttpMethod.POST
     */
    override fun phoneCode(phoneCodeReq: PhoneCodeReq) {
        //发送获取验证码操作
        val bodyMap = phoneCodeReq.dataClassToMutableMap()
        // 2025-04-24 09:43:40 内卷群里说要加这个appId = 2
        bodyMap[APP_ID] = systemConfiguration.accountSystem.appId
        return accountSystemReqService.commonReq(
            AccountSystemReqServiceImpl.PHONE_CODE_API,
            HttpMethod.POST,
            emptyMap(),
            bodyMap
        )?.run {
            if (status == 200) Unit else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        } ?: Unit


    }

    /**
     * 获取邮箱验证码
     * @see HttpMethod.POST
     */
    override fun emailCode(emailCodeReq: EmailCodeReq, sessionId: String?) {
        val map = sessionId?.let {
            mapOf(AUTHORIZATION to it)
        } ?: run {
            try {
                accountAuthUtils.getSessionId()
            } catch (e: Exception) {
                null
            }?.let { tokenSessionId ->
                mapOf(AUTHORIZATION to tokenSessionId)
            } ?: mapOf()
        }
        return accountSystemReqService.commonReq(
            AccountSystemReqServiceImpl.EMAIL_CODE_API, HttpMethod.POST, map, emailCodeReq.dataClassToMutableMap()
        )?.run {
            if (status == 200) Unit else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        } ?: Unit


    }

    /**
     * 忘记密码
     * @see HttpMethod.POST
     */
    override fun forgetPassword(passwordChangeReq: ForgetPasswordReq) {
        return accountSystemReqService.commonReq(
            AccountSystemReqServiceImpl.CHANGE_PASSWORD_API,
            HttpMethod.POST,
            emptyMap(),
            passwordChangeReq.dataClassToMutableMap()
        )?.run {
            if (status == 200) Unit else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        } ?: Unit

    }

    /**
     * 更新密码 (登录状态下)
     * @see HttpMethod.PUT
     */
    override fun changePassword(changePasswordReq: ChangePasswordReq, sessionId: String?) {
        val map = sessionId?.let {
            mapOf(AUTHORIZATION to it)
        } ?: run {
            try {
                accountAuthUtils.getSessionId()
            } catch (e: Exception) {
                null
            }?.let { tokenSessionId ->
                mapOf(AUTHORIZATION to tokenSessionId)
            } ?: mapOf()
        }
        return accountSystemReqService.commonReq(
            AccountSystemReqServiceImpl.CHANGE_PASSWORD_SIGN_STATUS_API,
            HttpMethod.PUT,
            map,
            changePasswordReq.dataClassToMutableMap()
        )?.run {
            if (status == 200) {
                accountAuthUtils.getAuthUser()?.let { authUser ->
                    //把当前的用户的token删除掉
                    redisTemplate.delete("$REDIS_USER_PREFIX${authUser.userEntity.id}")
                    Unit
                }
            } else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        } ?: Unit
    }

    /**
     * 绑定手机号
     * @see HttpMethod.POST
     */
    override fun bindPhone(bindPhoneReq: BindPhoneReq, sessionId: String?) {
        val map = sessionId?.let {
            mapOf(AUTHORIZATION to it)
        } ?: run {
            try {
                accountAuthUtils.getSessionId()
            } catch (e: Exception) {
                null
            }?.let { tokenSessionId ->
                mapOf(AUTHORIZATION to tokenSessionId)
            } ?: mapOf()
        }
        return accountSystemReqService.commonReq(
            AccountSystemReqServiceImpl.BIND_PHONE_API, HttpMethod.POST, map, bindPhoneReq.dataClassToMutableMap()
        )?.run {
            if (status == 200) Unit else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        } ?: Unit

    }

    /**
     * 更新用户信息
     * @see HttpMethod.PUT
     */
    override fun updateUser(updateUserReq: UpdateUserReq, sessionId: String?) {
        val map = sessionId?.let {
            mapOf(AUTHORIZATION to it)
        } ?: run {
            try {
                accountAuthUtils.getSessionId()
            } catch (e: Exception) {
                null
            }?.let { tokenSessionId ->
                mapOf(AUTHORIZATION to tokenSessionId)
            } ?: mapOf()
        }
        val dataClassToMutableMap = updateUserReq.dataClassToMutableMap()
        //特殊处理一下 我们这边 前段还是保持nickname传递过来,  到账号系统这个接口需要转换成 nickName, 这个是账号系统历史遗留问题
        dataClassToMutableMap.contains(AuthConstants.NICK_NAME_ORIGIN).let {
            val nickname = dataClassToMutableMap[AuthConstants.NICK_NAME_ORIGIN] as String
            dataClassToMutableMap[AuthConstants.NICK_NAME_DEST] = nickname
        }
        return accountSystemReqService.commonReq(
            AccountSystemReqServiceImpl.USER_INFO_API, HttpMethod.PUT, map, dataClassToMutableMap
        )?.run {
            if (status == 200) Unit else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        } ?: Unit

    }

    /**
     * 绑定邮箱 支持半登录状态
     */
    override fun bindEmail(bindEmailReq: BindEmailReq, sessionId: String?) {
        val map = sessionId?.let {
            mapOf(AUTHORIZATION to it)
        } ?: run {
            try {
                accountAuthUtils.getSessionId()
            } catch (e: Exception) {
                null
            }?.let { tokenSessionId ->
                mapOf(AUTHORIZATION to tokenSessionId)
            } ?: mapOf()
        }
        return accountSystemReqService.commonReq(
            AccountSystemReqServiceImpl.BIND_EMAIL_API, HttpMethod.POST, map, bindEmailReq.dataClassToMutableMap()
        )?.run {
            if (status == 200) Unit else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        } ?: Unit

    }

    /**
     * 账号列表查询 分页接口
     */
    override fun accountListPage(accountListPageReq: AccountListPageReq): AccountListPage? {
        return accountSystemReqService.accountListPage(accountListPageReq)?.run {
            when (status) {
                200 -> data
                //这个是没有记录 返回一个空列表 不算错误
                400 -> AccountListPage(0, emptyList())
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        }
    }

    /**
     * 子用户列表查询
     */
    override fun subUserList(): AccountListPage? {
        val roleId = SecurityUtil.getLoginRoleId()
        val accountListPageReq =
            AccountListPageReq(roleId, roleId, SecurityUtil.getUserId(), allDescendants = true, offset = -1, limit = -1)
//        accountListPageReq. = 0
//        accountListPageReq.pageSize = 99999
        //依靠accountListPage 这个接口
        return accountListPage(accountListPageReq)
    }

    /**
     * 角色列表查询支持 分页查询
     */
    override fun roleListPage(roleListPageReq: RoleListPageReq): RoleListPage? {
        return accountSystemReqService.roleListPage(roleListPageReq)?.run {
            when (status) {
                200 -> data
                //这个是没有记录 返回一个空列表 不算错误
                400 -> RoleListPage(0, emptyList())
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        }
    }

    /**
     * 滑块验证码校验接口
     */
    override fun captchaCodeCheck(captchaCodeCheckReq: CaptchaCodeCheckReq) {
        accountSystemReqService.commonReq(
            AccountSystemReqServiceImpl.CAPTCHA_CODE_CHECK_API,
            HttpMethod.POST,
            emptyMap(),
            captchaCodeCheckReq.dataClassToMutableMap()
        )?.run {
            if (status == 200) Unit else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        } ?: Unit
    }

    /**
     * 授权邮箱校验接口
     */
    override fun authorizationEmailCheck(authorizationEmailCheckReq: AuthorizationEmailCheckReq): EmailCheckResult? {
        return accountSystemReqService.emailCheck(authorizationEmailCheckReq)?.run {
            if (status == 200) data else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
        }
    }

    /**
     * 用户列表接口
     * 支持ids 查询
     */
    override fun userList(userListReq: UserListReq): List<AccountUser>? {
        val requestParamsMap = accountAuthUtils.transformMap(userListReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.USER_LIST_API,
                                                          HttpMethod.GET,
                                                          mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()),
                                                          requestParamsMap,
                                                          object :
                                                              ParameterizedTypeReference<AccountResult<List<AccountUser>>>() {})
            ?.run {
                if (status == 200) data else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
    }

    /**
     * 取消角色授权接口
     */
    override fun authorizationRoleCancel(authorizationRoleCancelReq: AuthorizationRoleCancelReq) {
        val requestBodyMap = accountAuthUtils.transformMap(authorizationRoleCancelReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.AUTHORIZATION_ROLE_CANCEL_API,
                                                          HttpMethod.POST,
                                                          mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()),
                                                          requestBodyMap,
                                                          object : ParameterizedTypeReference<AccountResult<Unit>>() {})
            ?.run {
                when (status) {
                    200 -> Unit
                    else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
                }
            } ?: Unit
    }

    /**
     * 角色对应的权限 element 树查询
     */
    override fun roleAuthorityConfig(roleAuthorityConfigReq: RoleAuthorityConfigReq): List<RoleAuthorityConfigResult>? {
        val requestBodyMap = accountAuthUtils.transformMap(roleAuthorityConfigReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.ELEMENT_CONFIG_LIST_API,
                                                          HttpMethod.GET,
                                                          mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()),
                                                          requestBodyMap,
                                                          object :
                                                              ParameterizedTypeReference<AccountResult<List<RoleAuthorityConfigResult>>>() {})
            ?.run {
                when (status) {
                    200 -> data
                    else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
                }
            }
    }

    /**
     * 用户注册接口
     */
    override fun userRegister(userRegisterReq: UserRegisterReq) {
        val requestBodyMap = accountAuthUtils.transformMap(userRegisterReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.USER_REGISTER_API,
                                                          HttpMethod.POST,
                                                          emptyMap(),
                                                          requestBodyMap,
                                                          object : ParameterizedTypeReference<AccountResult<Unit>>() {})
            ?.run {
                if (status == 200) Unit else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            } ?: Unit
    }

    /**
     * 邮箱用户名 check 接口
     */
    override fun emailUsernameCheck(emailUsernameCheckReq: EmailUsernameCheckReq) {
        val requestBodyMap = accountAuthUtils.transformMap(emailUsernameCheckReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.EMAIL_USERNAME_CHECK_API,
                                                          HttpMethod.POST,
                                                          emptyMap(),
                                                          requestBodyMap,
                                                          object : ParameterizedTypeReference<AccountResult<Unit>>() {})
            ?.run {
                if (status == 200) Unit
                else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            } ?: Unit
    }

    /**
     * 注销用户接口
     */
    override fun cancelUser(cancelUserReq: CancelUserReq, sessionId: String?) {
        val map = sessionId?.let {
            mapOf(AUTHORIZATION to it)
        } ?: run {
            try {
                accountAuthUtils.getSessionId()
            } catch (e: Exception) {
                null
            }?.let { tokenSessionId ->
                mapOf(AUTHORIZATION to tokenSessionId)
            } ?: mapOf()
        }
        val requestBodyMap = accountAuthUtils.transformMap(cancelUserReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.CANCELLATION_API,
                                                          HttpMethod.POST,
                                                          map,
                                                          requestBodyMap,
                                                          object : ParameterizedTypeReference<AccountResult<Unit>>() {})
            ?.run {
                if (status == 200) Unit
                else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            } ?: Unit
    }

    /**
     * 退出登录接口
     */
    override fun logout(sessionId: String?) {
        val map = sessionId?.let {
            mapOf(AUTHORIZATION to it)
        } ?: run {
            try {
                accountAuthUtils.getSessionId()
            } catch (e: Exception) {
                null
            }?.let { tokenSessionId ->
                mapOf(AUTHORIZATION to tokenSessionId)
            } ?: mapOf()
        }
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.LOGOUT_API,
                                                          HttpMethod.POST,
                                                          map,
                                                          mutableMapOf(),
                                                          object : ParameterizedTypeReference<AccountResult<Unit>>() {})
            ?.run {
                if (status == 200) {
                    SecurityUtil.getPrincipal()
                        //此时可能还没选择角色 我们平台redis可能还没有
                        ?.let {
                            //把我们的redis里的清除了
                            redisTemplate.delete(
                                "$REDIS_USER_PREFIX${it.userEntity.id}"
                            )
                        }
                    Unit
                } else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            } ?: Unit
    }

    /**
     * 角色列表接口
     */
    override fun roleList(sessionId: String): List<RoleInfo>? {
        var accountSessionId = sessionId
        if (StringUtil.isEmpty(accountSessionId)) {
            accountSessionId = accountAuthUtils.getSessionId()
        }
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.ROLE_INFO_LIST_API,
                                                          HttpMethod.GET,
                                                          mapOf(AUTHORIZATION to accountSessionId),
                                                          mutableMapOf(APP_ID to systemConfiguration.accountSystem.appId),
                                                          object :
                                                              ParameterizedTypeReference<AccountResult<PanguRoles>>() {})
            ?.run {
                if (status == 200) {
                    data?.panguOs
                } else if (status == 401) {
                    throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), status, desc)
                } else throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
    }

    /**
     * userinfo接口
     */
    override fun userInfo(sessionId: String): AccountUser? {
        var accountSessionId = sessionId
        if (StringUtil.isEmpty(accountSessionId)) {
            accountSessionId = accountAuthUtils.getSessionId()
        }
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.USER_INFO_API,
                                                          HttpMethod.GET,
                                                          mapOf(AUTHORIZATION to accountSessionId),
                                                          mutableMapOf(),
                                                          object :
                                                              ParameterizedTypeReference<AccountResult<AccountUser>>() {})
            ?.run {
                when (status) {
                    200 -> data
                    else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
                }
            }
    }

    /**
     * name账号查询邮箱接口
     */
    override fun nameFindEmail(nameFindEmailReq: NameFindEmailReq): AccountUser? {
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.CHECK_RESET_PASSWORD_API.replace(
            ":username", nameFindEmailReq.username
        ),
                                                          HttpMethod.POST,
                                                          emptyMap(),
                                                          mutableMapOf(),
                                                          object :
                                                              ParameterizedTypeReference<AccountResult<AccountUser>>() {})
            ?.run {
                when (status) {
                    200 -> data
                    else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
                }
            }
    }

    /**
     * emialCode 校验接口
     */
    override fun emailCodeCheck(emailCodeCheckReq: EmailCodeCheckReq) {
        val requestBodyMap = accountAuthUtils.transformMap(emailCodeCheckReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.EMAIL_CODE_CHECK_API,
                                                          HttpMethod.POST,
                                                          emptyMap(),
                                                          requestBodyMap,
                                                          object : ParameterizedTypeReference<AccountResult<Unit>>() {})
            ?.run {
                when (status) {
                    200 -> Unit
                    else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
                }
            } ?: Unit
    }

    /**
     * 更新备注接口
     */
    override fun updateMark(updateMarkReq: UpdateMarkReq) {
        val requestBodyMap = accountAuthUtils.transformMap(updateMarkReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.UPDATE_MARK_API,
                                                          HttpMethod.PUT,
                                                          mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()),
                                                          requestBodyMap,
                                                          object : ParameterizedTypeReference<AccountResult<Unit>>() {})
            ?.run {
                when (status) {
                    200 -> Unit
                    else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
                }
            } ?: Unit
    }

    /**
     * 更新授权信息接口
     */
    override fun updateAuthorization(updateAuthorizationReq: UpdateAuthorizationReq) {
        val requestBodyMap = accountAuthUtils.transformMap(updateAuthorizationReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.UPDATE_AUTHORIZATION_API,
                                                          HttpMethod.PUT,
                                                          mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()),
                                                          requestBodyMap,
                                                          object : ParameterizedTypeReference<AccountResult<Unit>>() {})
            ?.run {
                when (status) {
                    200 -> Unit
                    else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
                }
            } ?: Unit
    }

    override fun accountUserInfo(accountUserInfoReq: AccountUserInfoReq): AccountUserInfo? {
        val requestParams = accountAuthUtils.transformMap(accountUserInfoReq)
        return accountSystemReqService.commonReqResultNew(AccountSystemReqServiceImpl.ACCOUNT_INFO_API,
                                                          HttpMethod.GET,
                                                          mapOf(AUTHORIZATION to accountAuthUtils.getSessionId()),
                                                          requestParams,
                                                          object :
                                                              ParameterizedTypeReference<AccountResult<AccountUserInfo>>() {})
            ?.run {
                when (status) {
                    200 -> data
                    else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
                }
            }
    }

    override fun getUserByUsername(username: String): AccountInnerResult? {
        return accountSystemReqService.getUserByUsername(username)?.run {
            when (status) {
                200 -> data
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        }
    }

    override fun getUserListByUserIds(userIds: List<String>): AccountInnerResult? {
        return accountSystemReqService.getUserListByUserIds(userIds)?.run {
            when (status) {
                200 -> data
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        }
    }


    /**
     * 授权角色
     */
    override fun authorizationRole(authorizationRoleReq: AuthorizationRoleReq) {
        return accountSystemReqService.authorizationRole(authorizationRoleReq)?.run {
            when (status) {
                200 -> Unit
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        } ?: Unit
    }

    /**
     * 角色创建接口
     */
    override fun roleCreate(roleCreateReq: RoleCreateReq) {
        return accountSystemReqService.roleCreate(roleCreateReq)?.run {
            when (status) {
                200 -> Unit
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        } ?: Unit
    }

    override fun roleUpdate(roleUpdateReq: RoleUpdateReq) {
        return accountSystemReqService.roleUpdate(roleUpdateReq)?.run {
            when (status) {
                200 -> Unit
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        } ?: Unit
    }

    /**
     * 角色删除接口
     */
    override fun roleDelete(roleDeleteReq: RoleDeleteReq) {
        return accountSystemReqService.roleDelete(roleDeleteReq)?.run {
            when (status) {
                200 -> Unit
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        } ?: Unit
    }

    /**
     * 绑定资源接口
     */
    override fun bingResource(bingResourceReq: BingResourceReq) {
        return accountSystemReqService.bingResource(bingResourceReq)?.run {
            when (status) {
                200 -> Unit
                else -> throw ServiceException(ErrorResultCode.CUSTOM_MSG_ERROR.value(), desc)
            }
        } ?: Unit
    }


}
