package com.wifochina.modules.oauth.service.impl

import com.wifochina.common.config.SystemConfiguration
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.utils.RestTemplateUtils
import com.wifochina.modules.oauth.AccountUser
import com.wifochina.modules.oauth.constants.AuthConstants.ALL_RESOURCE_STAT
import com.wifochina.modules.oauth.constants.AuthConstants.ALL_RESOURCE_STAT_DATACENTER
import com.wifochina.modules.oauth.constants.AuthConstants.APPLICATION_ID
import com.wifochina.modules.oauth.constants.AuthConstants.APP_ID
import com.wifochina.modules.oauth.constants.AuthConstants.AUTHORIZATION
import com.wifochina.modules.oauth.constants.AuthConstants.INHERIT_RESOURCE_STAT
import com.wifochina.modules.oauth.constants.AuthConstants.LOGIN_ROLE_ID
import com.wifochina.modules.oauth.constants.AuthConstants.PageConstants.LIMIT
import com.wifochina.modules.oauth.constants.AuthConstants.PageConstants.OFFSET
import com.wifochina.modules.oauth.constants.AuthConstants.RESOURCE_CODES
import com.wifochina.modules.oauth.constants.AuthConstants.ROLE_ID
import com.wifochina.modules.oauth.constants.AuthConstants.SESSION_ID
import com.wifochina.modules.oauth.constants.AuthConstants.USER_ID
import com.wifochina.modules.oauth.dto.*
import com.wifochina.modules.oauth.req.AccountListPageReq
import com.wifochina.modules.oauth.req.AuthorizationEmailCheckReq
import com.wifochina.modules.oauth.req.AuthorizationRoleCancelReq
import com.wifochina.modules.oauth.req.AuthorizationRoleReq
import com.wifochina.modules.oauth.req.BingResourceReq
import com.wifochina.modules.oauth.req.RoleCreateReq
import com.wifochina.modules.oauth.req.RoleDeleteReq
import com.wifochina.modules.oauth.req.RoleListPageReq
import com.wifochina.modules.oauth.req.RoleUpdateReq
import com.wifochina.modules.oauth.req.UserListReq
import com.wifochina.modules.oauth.req.UserRegisterReq
import com.wifochina.modules.oauth.service.AccountSystemReqService
import com.wifochina.modules.oauth.util.SecurityUtil
import com.wifochina.modules.oauth.utils.AccountAuthUtils
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Component
import org.springframework.web.client.ResourceAccessException
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

private val log = KotlinLogging.logger { }

/**
 * <AUTHOR>
 * Created on 2024/12/19 09:39.
 */
@Component
class AccountSystemReqServiceImpl(
    val restTemplateUtils: RestTemplateUtils,
    val accountAuthUtils: AccountAuthUtils,
    val systemConfiguration: SystemConfiguration
) : AccountSystemReqService {
    companion object {
        const val LOGIN_API = "/api/v1/login"
        const val AUTHENTICATION_API = "/api/v1/internal/authentication"
        const val CAPTCHA_CODE_API = "/api/v1/captcha/code"
        const val PHONE_CODE_API = "/api/v1/phone/code"
        const val EMAIL_CODE_API = "/api/v1/email/code"
        const val CHANGE_PASSWORD_API = "/api/v1/user/password/find"
        const val CHANGE_PASSWORD_SIGN_STATUS_API = "/api/v1/user/password"
        const val BIND_PHONE_API = "/api/v1/user/phone/set"
        const val BIND_EMAIL_API = "/api/v1/user/email/set"
        const val ACCOUNT_LIST_PAGE = "/api/v1/user/role/authorization/account/list"
        const val ROLE_LIST_PAGE = "/api/v1/user/role/available/list"
        const val CAPTCHA_CODE_CHECK_API = "/api/v1/captcha/check"
        const val AUTHORIZATION_EMAIL_CHECK_API = "/api/v1/user/role/authorization/email/check"
        const val USER_LIST_API = "/api/v1/user/list"
        const val AUTHORIZATION_ROLE_API = "/api/v1/user/role/authorization/email/set"
        const val AUTHORIZATION_ROLE_CANCEL_API = "/api/v1/user/role/authorization/email/cancel"
        const val ROLE_CREATE_API = "/api/v1/user/role/create"
        const val ROLE_DELETE_API = "/api/v1/user/role/delete"
        const val ROLE_UPDATE_API = "/api/v1/user/role/update"
        const val ELEMENT_CONFIG_LIST_API = "/api/v1/user/role/element/config/list"
        const val USER_REGISTER_API = "/api/v1/user/register"
        const val EMAIL_USERNAME_CHECK_API = "/api/v1/email/check"
        const val CANCELLATION_API = "/api/v1/user/cancellation"
        const val LOGOUT_API = "/api/v1/user/logout"
        const val ROLE_INFO_LIST_API = "/api/v1/user/role/list"
        const val USER_INFO_API = "/api/v1/user/info"
        const val EMAIL_CODE_CHECK_API = "/api/v1/email/code/check"
        const val CHECK_RESET_PASSWORD_API = "/api/v1/internal/user/:username/check-reset-password"
        const val RESOURCE_BIND_API = "/api/v1/user/resource/rel"
        const val UPDATE_MARK_API = "/api/v1/user/role/authorization/mark"
        const val UPDATE_AUTHORIZATION_API = "/api/v1/user/role/authorization"
        const val ACCOUNT_INFO_API = "/api/v1/user/role/account/info"
        const val ACCOUNT_INNER_USER_API = "/api/v1/internal/user/_search"
    }

    /**
     * 调用账号系统的登录接口
     */
    override fun login(bodyMap: MutableMap<String, Any>): AccountResult<AccountUser>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        return try {
            val post = restTemplateUtils.post(
                systemConfiguration.accountSystem.accountUrl + LOGIN_API,
                bodyMap,
                object : ParameterizedTypeReference<AccountResult<AccountUser>>() {},
                accountAuthHeader,
                true
            )
            post
        } catch (e: ResourceAccessException) {
            throw ServiceException(ErrorResultCode.USER_PASSWORD_INVALID.value()) // 包含原始异常信息
        } catch (e: Exception) {
            e.printStackTrace()
            log.error { "what why error in login ${e.message}" }
            throw ServiceException("debug login error")
        }
    }

    /**
     * 认证的时候需要 前面login 返回的 sessionId -> Authorization 这个传递过去
     */
    override fun authentication(bodyMap: MutableMap<String, Any>): AccountResult<AccountUser>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        return RequestContextHolder.getRequestAttributes()?.let {
            (it as ServletRequestAttributes).request.getHeader(SESSION_ID)?.let { authorization ->
                accountAuthHeader[AUTHORIZATION] = authorization
                bodyMap[APP_ID] = systemConfiguration.accountSystem.appId
                restTemplateUtils.post(
                    systemConfiguration.accountSystem.accountUrl + AUTHENTICATION_API,
                    bodyMap,
                    object : ParameterizedTypeReference<AccountResult<AccountUser>>() {},
                    accountAuthHeader,
                    false
                )
            }
        } ?: run {
            throw ServiceException(ErrorResultCode.INTERNAL_SERVER_ERROR.value())
        }

    }

    override fun captchaCode(): AccountResult<CaptchaCode>? {
        return restTemplateUtils.get(
            systemConfiguration.accountSystem.accountUrl + CAPTCHA_CODE_API,
            emptyMap(),
            object : ParameterizedTypeReference<AccountResult<CaptchaCode>>() {},
            accountAuthUtils.getAccountAuthHeader(),
            true
        )
    }

    override fun <T> commonReqResultNew(
        api: String,
        httpMethod: HttpMethod,
        headerMap: Map<String, String>,
        bodyMap: MutableMap<String, Any>,
        responseType: ParameterizedTypeReference<T>
    ): T? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        // 直接免去让前端传sessionId 头部了
        accountAuthHeader.putAll(headerMap)
        bodyMap[APPLICATION_ID] = systemConfiguration.accountSystem.appId
        bodyMap[APP_ID] = systemConfiguration.accountSystem.appId
        return restTemplateUtils.sendRequestNew(
            systemConfiguration.accountSystem.accountUrl + api,
            httpMethod,
            bodyMap,
            accountAuthHeader,
            responseType,
            true
        )
    }


    override fun commonReq(
        api: String, httpMethod: HttpMethod, headerMap: Map<String, String>, bodyMap: MutableMap<String, Any>
    ): AccountNoResult? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        // 直接免去让前端传sessionId 头部了
        accountAuthHeader.putAll(headerMap)
        return restTemplateUtils.sendRequest(
            systemConfiguration.accountSystem.accountUrl + api,
            httpMethod,
            bodyMap,
            AccountNoResult::class.java,
            accountAuthHeader,
            true
        )

    }

    override fun accountListPage(accountListPageReq: AccountListPageReq): AccountResult<AccountListPage>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val transformMap = accountAuthUtils.transformMap(accountListPageReq)
        if (accountListPageReq.offset == -1 || accountListPageReq.limit == -1) {
            transformMap[LIMIT] = -1
            transformMap[OFFSET] = -1
        }
        return restTemplateUtils.get(
            systemConfiguration.accountSystem.accountUrl + ACCOUNT_LIST_PAGE,
            transformMap,
            object : ParameterizedTypeReference<AccountResult<AccountListPage>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun roleListPage(roleListPageReq: RoleListPageReq): AccountResult<RoleListPage>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val transformMap = accountAuthUtils.transformMap(roleListPageReq)
        return restTemplateUtils.get(
            systemConfiguration.accountSystem.accountUrl + ROLE_LIST_PAGE,
            transformMap,
            object : ParameterizedTypeReference<AccountResult<RoleListPage>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun emailCheck(authorizationEmailCheckReq: AuthorizationEmailCheckReq): AccountResult<EmailCheckResult>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val requestBodyMap = accountAuthUtils.transformMap(authorizationEmailCheckReq)
        requestBodyMap[APP_ID] = systemConfiguration.accountSystem.appId
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + AUTHORIZATION_EMAIL_CHECK_API,
            requestBodyMap,
            object : ParameterizedTypeReference<AccountResult<EmailCheckResult>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun userList(userListReq: UserListReq): AccountResult<List<AccountUser>>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val requestBodyMap = accountAuthUtils.transformMap(userListReq)
        return restTemplateUtils.get(
            systemConfiguration.accountSystem.accountUrl + USER_LIST_API,
            requestBodyMap,
            object : ParameterizedTypeReference<AccountResult<List<AccountUser>>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun authorizationRoleCancel(authorizationRoleCancelReq: AuthorizationRoleCancelReq): AccountResult<Unit>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val requestBodyMap = accountAuthUtils.transformMap(authorizationRoleCancelReq)
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + AUTHORIZATION_ROLE_CANCEL_API,
            requestBodyMap,
            object : ParameterizedTypeReference<AccountResult<Unit>>() {},
            accountAuthHeader,
            true
        )
    }


    override fun userRegister(userRegisterReq: UserRegisterReq): AccountResult<Unit>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val requestBodyMap = accountAuthUtils.transformMap(userRegisterReq)
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + "",
            requestBodyMap,
            object : ParameterizedTypeReference<AccountResult<Unit>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun authorizationRole(authorizationRoleReq: AuthorizationRoleReq): AccountResult<Unit>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val requestBodyMap = accountAuthUtils.transformMap(authorizationRoleReq)
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + AUTHORIZATION_ROLE_API,
            requestBodyMap,
            object : ParameterizedTypeReference<AccountResult<Unit>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun roleCreate(roleCreateReq: RoleCreateReq): AccountResult<Unit>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val requestBodyMap = accountAuthUtils.transformMap(roleCreateReq)
        //这个接口为什么要把appId 变成applicationId
        requestBodyMap[APPLICATION_ID] = systemConfiguration.accountSystem.appId
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + ROLE_CREATE_API,
            requestBodyMap,
            object : ParameterizedTypeReference<AccountResult<Unit>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun roleDelete(roleDeleteReq: RoleDeleteReq): AccountResult<Unit>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val requestBodyMap = accountAuthUtils.transformMap(roleDeleteReq)
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + ROLE_DELETE_API,
            requestBodyMap,
            object : ParameterizedTypeReference<AccountResult<Unit>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun roleUpdate(roleUpdateReq: RoleUpdateReq): AccountResult<Unit>? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val requestBodyMap = accountAuthUtils.transformMap(roleUpdateReq)
        //这个接口为什么要把appId 变成applicationId
        requestBodyMap[APPLICATION_ID] = systemConfiguration.accountSystem.appId
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + ROLE_UPDATE_API,
            requestBodyMap,
            object : ParameterizedTypeReference<AccountResult<Unit>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun bingResource(bingResourceReq: BingResourceReq): AccountNoResult? {
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        accountAuthHeader[AUTHORIZATION] = accountAuthUtils.getSessionId()
        val bodyMap = mutableMapOf<String, Any>(APPLICATION_ID to systemConfiguration.accountSystem.appId)
        bodyMap[ROLE_ID] = bingResourceReq.roleId
        bodyMap[LOGIN_ROLE_ID] = SecurityUtil.getLoginRoleId()
        bodyMap[USER_ID] = bingResourceReq.userId
        bodyMap[RESOURCE_CODES] = bingResourceReq.resourceId
        bodyMap["datacenter"] = systemConfiguration.accountSystem.dataCenter.toInt()
        bingResourceReq.allProject?.apply {
            bodyMap[INHERIT_RESOURCE_STAT] = if (bingResourceReq.allProject == false) 1 else 2
        }
        bodyMap[ALL_RESOURCE_STAT_DATACENTER] = systemConfiguration.accountSystem.dataCenter.toInt()
        return restTemplateUtils.sendRequest(
            systemConfiguration.accountSystem.accountUrl + RESOURCE_BIND_API,
            HttpMethod.PUT,
            bodyMap,
            AccountNoResult::class.java,
            accountAuthHeader,
            true
        )
    }

    override fun getUserByUsername(username: String): AccountResult<AccountInnerResult>? {
        if (username.isBlank()) {
            return null
        }
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        val bodyMap = mutableMapOf<String, Any>("username" to username)
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + ACCOUNT_INNER_USER_API,
            bodyMap,
            object : ParameterizedTypeReference<AccountResult<AccountInnerResult>>() {},
            accountAuthHeader,
            true
        )
    }

    override fun getUserListByUserIds(userIds: List<String>): AccountResult<AccountInnerResult>? {
        if (userIds.isEmpty()) {
            return null
        }
        val accountAuthHeader = accountAuthUtils.getAccountAuthHeader()
        val bodyMap = mutableMapOf<String, Any>("userIds" to userIds)
        bodyMap[LIMIT] = -1
        bodyMap[OFFSET] = -1
        return restTemplateUtils.post(
            systemConfiguration.accountSystem.accountUrl + ACCOUNT_INNER_USER_API,
            bodyMap,
            object : ParameterizedTypeReference<AccountResult<AccountInnerResult>>() {},
            accountAuthHeader,
            true
        )
    }


}