package com.wifochina.modules.oauth.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Created on 2024/11/25 15:43.
 * <AUTHOR>
 */
data class RoleInfo(
    val applicationId: String,
    val applicationName: String,
    val authStat: Int,
    val category: Int,
    val creatorRoleId: String,
    val creatorUserId: String,
    val parentRoleId: String,
    val ct: Long,
    val i118nId: Long,
    val isManager: Int,
    val level: Int,
    val parentUserId: String,
    val roleId: String,
    val roleName: String,
    val type: Int,
    val ut: Long
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PanguRoles(
    @JsonProperty("PANGU OS")
    val panguOs: List<RoleInfo>
)