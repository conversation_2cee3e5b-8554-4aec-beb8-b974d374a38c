package com.wifochina.modules.oauth.req

import io.swagger.annotations.ApiModelProperty
import javax.validation.constraints.NotNull

/**
 * Created on 2024/11/19 09:39.
 * <AUTHOR>
 */
data class EmailCodeCheckReq(
    @field:NotNull val email: String,
    @field:NotNull val code: String,
    @field:ApiModelProperty("业务类型，1: 注册；2：重置密码；3：找回密码；4：注销；5：内部用户注册；6：邮箱验证码登录；7：重置邮箱") @field:NotNull val pt: Int,
)