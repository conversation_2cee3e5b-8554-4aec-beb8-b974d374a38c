package com.wifochina.modules.oauth.filter

import com.alibaba.fastjson.JSON
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.page.Result
import com.wifochina.common.servlet.HttpServletRequestWrapper
import com.wifochina.common.util.StringUtil
import com.wifochina.modules.oauth.AuthUser
import com.wifochina.modules.oauth.IJwtAuthenticationTokenFilter
import com.wifochina.modules.oauth.config.MySecurityConfig
import com.wifochina.modules.oauth.constants.AuthConstants.AUTHORIZATION
import com.wifochina.modules.oauth.constants.AuthConstants.BEARER
import com.wifochina.modules.oauth.constants.AuthConstants.REDIS_USER_PREFIX
import com.wifochina.modules.oauth.constants.AuthConstants.SESSION_ID
import com.wifochina.modules.oauth.req.AccountAuthenticationReq
import com.wifochina.modules.oauth.service.AccountSystemService
import com.wifochina.modules.oauth.util.JwtHelper
import com.wifochina.modules.oauth.util.JwtUserInfo
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.oauth.utils.AccountAuthUtils
import com.wifochina.modules.user.entity.UserEntity
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import org.springframework.web.filter.OncePerRequestFilter
import java.io.IOException
import java.time.Duration
import java.time.Instant
import java.util.concurrent.TimeUnit
import javax.servlet.FilterChain
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

private val log = KotlinLogging.logger { }

/**
 * 专门用于 云盘古的 jwt 认证filter
 * Created on 2024/11/6 11:50.
 * <AUTHOR>
 */
@Component
class JwtAuthenticationTokenFilter(
    val jwtHelper: JwtHelper,
    val redisTemplate: RedisTemplate<String, String>,
    val accountAuthUtils: AccountAuthUtils,
    val accountSystemService: AccountSystemService
) : OncePerRequestFilter(), IJwtAuthenticationTokenFilter {
    override fun doFilterInternal(
        request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain
    ) {
        val authentication: Authentication? = SecurityContextHolder.getContext().authentication
        if (authentication != null) {
            //如果已经认证过了 这里就直接通过
            filterChain.doFilter(request, response)
            return
        }
        val sessionId: String? = request.getHeader(SESSION_ID)
        // get jwt token
        var token: String? = request.getHeader(AUTHORIZATION)
        val authHeader = BEARER
        // parse token
        //token 不传 并且 访问的路径是 session_id_urls 并且是白名单 可以直接通过
        // 为了支持 session_id 可以访问的一些接口 , 这些接口即支持 session_id 又要支持 token访问
        if (StringUtil.isEmpty(token) && MySecurityConfig.SESSION_ID_URLS.contains(request.requestURI.substring(4))) {
            if (MySecurityConfig.NO_AUTH_URLS.contains(
                    request.requestURI.substring(4)
                )
            ) {
                filterChain.doFilter(request, response)
                return
            }
            //token 不传 并且 访问的路径不在 session_id_urls 里 , 并且是白名单 可以直接通过, 这大概是以前的逻辑保证 比如swagger路径可以出来
        } else if (StringUtil.isEmpty(token) || MySecurityConfig.NO_AUTH_URLS.contains(
                request.requestURI.substring(4)
            ) && !MySecurityConfig.SESSION_ID_URLS.contains(request.requestURI.substring(4))
        ) {
            filterChain.doFilter(request, response)
            return
        } else if (token!!.startsWith(authHeader)) {
            token = token.substring(6).trim { it <= ' ' }
        }
        if (JwtHelper.illegalTokenSet.contains(token)) {
            sendErrorMessage(response)
            return
        }
        // get user info from redis
        //   需要取出来 userName 和roleId 做场站的用户查询跳转
        val jwtUserInfo: JwtUserInfo? = jwtHelper.getJwtFromToken(token)
        if (jwtUserInfo == null) {
            sendErrorMessage(response)
            return
        }
        val userId = jwtUserInfo.userId
        // init
        var loginUser: AuthUser? =
            JSON.parseObject(redisTemplate.opsForValue().get("$REDIS_USER_PREFIX$userId"), AuthUser::class.java)
        if (loginUser == null) {
            redisTemplate.delete("$REDIS_USER_PREFIX$userId")
//            sendErrorMessage(response)
//            return
//            throw ServiceException("old数据token清除处理")
            //如果 == null 可以去查询一下 账号系统
            val roleId: String? = jwtUserInfo.roleId
            val accountSystemSessionId = jwtUserInfo.accountSystemSessionId
            log.error { "what roleId: $roleId" }
            if (roleId != null && !StringUtil.isEmpty(roleId) && roleId != "null") {
                val accountAuthenticationReq = AccountAuthenticationReq(roleId)
                val requestWrapper = HttpServletRequestWrapper(request)
                //authentication 里面 解析的是我们平台来的 头叫  SessionId 虽然和 账号系统的一个意思 但是分开
                requestWrapper.addHeader(SESSION_ID, accountSystemSessionId)
                RequestContextHolder.setRequestAttributes(ServletRequestAttributes(requestWrapper))
                accountSystemService.authentication(accountAuthenticationReq)?.let { accountUser ->
                    val authoritySets = linkedSetOf<String>()
                    accountUser.currentRole!!.policyAllowed?.forEach {
                        accountAuthUtils.addAuthorityUrls(it, authoritySets)
                    }
                    UserEntity().also { userEntity ->
                        val duration = Duration.between(
                            Instant.now(), Instant.ofEpochSecond(accountUser.token?.expiredAt!!.toLong())
                        )
                        val difference = duration.seconds
                        //把账号系统用户信息 转换成 UserEntity对象
                        accountAuthUtils.transformAccountUserToUserEntity(accountUser, userEntity, authoritySets)
                        loginUser = AuthUser(
                            userEntity, accountSystemSessionId, authoritySets
                        )
                        //用户存入redis
                        redisTemplate.opsForValue().set(
                            "$REDIS_USER_PREFIX${userEntity.id}",
                            JSON.toJSONString(
                                loginUser
                            ),
                            difference, TimeUnit.SECONDS,
                        )
                    }
                } ?: throw ServiceException("")
            } else {
                log.warn { "loginUser is null and roleId is null but has token ..." }
                sendErrorMessage(response)
                return
            }

        }
        val projectId = request.getHeader("ProjectId")
        WebUtils.projectId.set(projectId)

        // store user info to the SecurityContext
        val usernamePasswordAuthenticationToken = UsernamePasswordAuthenticationToken(
            loginUser, null, loginUser!!.authorities
        )
//        var accountSystemSessionId = loginUser.accountSystemSessionId
        SecurityContextHolder.getContext().authentication = usernamePasswordAuthenticationToken
        // go to next filter chain
        filterChain.doFilter(request, response)
    }

    @Throws(IOException::class)
    private fun sendErrorMessage(response: HttpServletResponse) {
        // 在这里定义你的异常处理逻辑，比如返回自定义的 HTTP 响应
        val resultVo = Result.failure<Any>(ErrorResultCode.ILLEGAL_ACCESS.value())
        WebUtils.rendString(response, JSON.toJSONString(resultVo))
    }

}