package com.wifochina.modules.oauth.controller

import com.alibaba.fastjson2.JSON
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.log.Log
import com.wifochina.common.page.Result
import com.wifochina.common.servlet.HttpServletRequestWrapper
import com.wifochina.common.util.EmsConstants
import com.wifochina.common.util.StringUtil
import com.wifochina.modules.group.entity.AmmeterEntity
import com.wifochina.modules.group.entity.CameraEntity
import com.wifochina.modules.group.entity.DeviceEntity
import com.wifochina.modules.group.service.AmmeterService
import com.wifochina.modules.group.service.CameraService
import com.wifochina.modules.group.service.DeviceService
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.log.OperationType
import com.wifochina.modules.oauth.AccountUser
import com.wifochina.modules.oauth.AuthUser
import com.wifochina.modules.oauth.constants.AuthConstants.AuthenticationResultConstants.ACCESS_TOKEN
import com.wifochina.modules.oauth.constants.AuthConstants.AuthenticationResultConstants.AUTHORITY
import com.wifochina.modules.oauth.constants.AuthConstants.AuthenticationResultConstants.HAS_CAMERA
import com.wifochina.modules.oauth.constants.AuthConstants.AuthenticationResultConstants.HAS_CONTROLLABLE
import com.wifochina.modules.oauth.constants.AuthConstants.AuthenticationResultConstants.HAS_METER
import com.wifochina.modules.oauth.constants.AuthConstants.AuthenticationResultConstants.INFO
import com.wifochina.modules.oauth.constants.AuthConstants.AuthenticationResultConstants.PROJECT
import com.wifochina.modules.oauth.constants.AuthConstants.REDIS_USER_PREFIX
import com.wifochina.modules.oauth.constants.AuthConstants.SESSION_ID
import com.wifochina.modules.oauth.dto.*
import com.wifochina.modules.oauth.req.*
import com.wifochina.modules.oauth.service.AccountSystemService
import com.wifochina.modules.oauth.util.JwtHelper
import com.wifochina.modules.oauth.util.JwtUserInfo
import com.wifochina.modules.oauth.util.SecurityUtil
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.oauth.utils.AccountAuthUtils
import com.wifochina.modules.project.entity.ProjectEntity
import com.wifochina.modules.project.service.ProjectService
import com.wifochina.modules.project.service.ProjectServiceKt
import com.wifochina.modules.user.entity.UserEntity
import com.wifochina.modules.user.service.LogService
import io.github.oshai.kotlinlogging.KotlinLogging
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.time.Duration
import java.time.Instant
import java.util.concurrent.TimeUnit
import java.util.stream.Collectors
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid


private val log = KotlinLogging.logger { }

/**
 * Created on 2024/11/5 16:57.
 * <AUTHOR>
 */
@Api(tags = ["认证账号系统"])
@RestController
@RequestMapping("/oauth/account/")
@Validated
class AccountController(
    val accountSystemService: AccountSystemService,
    val jwtHelper: JwtHelper,
    val accountAuthUtils: AccountAuthUtils,
    val projectServiceKt: ProjectServiceKt,
    val projectService: ProjectService,
    val redisTemplate: RedisTemplate<String, String>,
    val ammeterService: AmmeterService,
    val deviceService: DeviceService,
    val cameraService: CameraService,
    val logService: LogService,
    val groupService: GroupService,
) {
    @ApiOperation("更新授权信息")
    @PostMapping("/updateAuthorization")
    fun updateAuthorization(@RequestBody updateAuthorizationReq: UpdateAuthorizationReq): Result<Unit> {
        return Result.success(accountSystemService.updateAuthorization(updateAuthorizationReq))
    }

    @ApiOperation("更新备注")
    @PostMapping("/updateMark")
    fun updateMark(@RequestBody updateMarkReq: UpdateMarkReq): Result<Unit> {
        return Result.success(accountSystemService.updateMark(updateMarkReq))
    }

    @ApiOperation("用户名查询邮箱")
    @PostMapping("/nameFindEmail")
    fun nameFindEmail(
        @RequestBody nameFindEmailReq: NameFindEmailReq
    ): Result<AccountUser> {
        //根据name查询 email
        return Result.success(accountSystemService.nameFindEmail(nameFindEmailReq))
    }

    @ApiOperation("邮箱code校验")
    @PostMapping("/emailCodeCheck")
    fun emailCodeCheck(@RequestBody emailCodeCheckReq: EmailCodeCheckReq): Result<Unit> {
        //根据邮箱code 校验
        return Result.success(accountSystemService.emailCodeCheck(emailCodeCheckReq))
    }

    @ApiOperation("角色列表查询(根据SessionId)")
    @PostMapping("/roleList")
    fun roleList(@RequestHeader("SessionId") sessionId: String): Result<List<RoleInfo>> {
        return Result.success(accountSystemService.roleList(sessionId))
    }

    @ApiOperation("邮箱用户名校验(非登录状态)")
    @PostMapping("/emailUsernameCheck")
    fun emailUsernameCheck(@RequestBody @Valid emailUsernameCheckReq: EmailUsernameCheckReq): Result<Unit> {
        return Result.success(accountSystemService.emailUsernameCheck(emailUsernameCheckReq))
    }

    @ApiOperation("用户register注册")
    @PostMapping("/user/register")
    fun userRegister(@RequestBody @Valid userRegisterReq: UserRegisterReq): Result<Unit> {
        return Result.success(accountSystemService.userRegister(userRegisterReq))
    }

    @ApiOperation("账号系统accountInfo接口")
    @PostMapping("/accountInfo")
    fun accountUserInfo(@RequestBody @Valid accountUserInfoReq: AccountUserInfoReq): Result<AccountUserInfo> {
        return Result.success(accountSystemService.accountUserInfo(accountUserInfoReq))
    }

    @ApiOperation("用户info信息")
    @PostMapping("/user/info")
    fun userInfo(
        @RequestHeader("SessionId", required = false) sessionId: String?, request: HttpServletRequest
    ): Result<Map<String, Any>> {
        if (!StringUtil.isEmpty(sessionId)) {
            val accountUser = sessionId?.let { accountSystemService.userInfo(it) }
            accountUser?.let {
                val userEntity = UserEntity()
                accountAuthUtils.transformAccountUserToUserEntity(accountUser, userEntity, mutableSetOf())
                return Result.success(
                    mapOf(
                        INFO to userEntity
                    )
                )
            } ?: throw ServiceException(ErrorResultCode.SERVICE_EXCEPTION.value())
        } else {
            val authUser = SecurityUtil.getPrincipal()
            val userEntity = authUser.userEntity
            //走认证去查询 保证切换了语言能够支持
            val requestWrapper = HttpServletRequestWrapper(request)
            //authentication 里面 解析的是我们平台来的 头叫  SessionId 虽然和 账号系统的一个意思 但是分开
            requestWrapper.addHeader(SESSION_ID, accountAuthUtils.getSessionId())
            RequestContextHolder.setRequestAttributes(ServletRequestAttributes(requestWrapper))
            //需要我们去进行认证
            return accountSystemService.authentication(
                AccountAuthenticationReq(
                    userEntity.roleId
                )
            )?.let { accountUser ->
                val projectList =
                    projectServiceKt.getCurrentProjectList(userEntity.roleId, accountAuthUtils.getSessionId())
                        ?.takeIf { it.isNotEmpty() && it.keys.isNotEmpty() }?.let { currentProjects ->
                            projectService.lambdaQuery().`in`(ProjectEntity::getId, currentProjects.keys).list()
                                .onEach { project ->
                                    project.projectName = currentProjects[project.id]
                                }
                        } ?: emptyList()
                // maintain 和 pcsCode 处理
                maintainAndPcsCodeQuery(projectList)
                var hasControllable = false
                var hasCamera = false
                var hasMeter = false
                WebUtils.projectId.get()?.let { projectId ->
                    hasControllable = ammeterService.lambdaQuery().eq(AmmeterEntity::getControllable, true)
                        .eq(AmmeterEntity::getProjectId, projectId).count() > 0;
                    hasCamera = cameraService.lambdaQuery().eq(CameraEntity::getProjectId, projectId).count() > 0;
                    hasMeter = ammeterService.lambdaQuery().eq(AmmeterEntity::getProjectId, projectId).count() > 0;
                }
                Result.success(
                    mapOf(
                        AUTHORITY to accountUser.currentRole!!.policyAllowed!!,
                        PROJECT to projectList,
                        INFO to userEntity,
                        HAS_CONTROLLABLE to hasControllable,
                        HAS_CAMERA to hasCamera,
                        HAS_METER to hasMeter
                    )
                )
            } ?: throw ServiceException(ErrorResultCode.INTERNAL_SERVER_ERROR.value())


        }
    }

    private fun maintainAndPcsCodeQuery(projectList: List<ProjectEntity>) {
        // 应虎佳要求 在这里把 系统分组的 pcsCode返回
        val systemGroupsMap = groupService.querySystemGroupPcsCodes(
            projectList.stream().map(ProjectEntity::getId).collect(Collectors.toList())
        )
        // 判断maintain
        val deviceEntities: List<DeviceEntity> =
            deviceService.lambdaQuery()
                .eq(DeviceEntity::getMaintain, true).list()
        val ammeterEntities =
            ammeterService.lambdaQuery()
                .eq(AmmeterEntity::getMaintain, true).list()
        val deviceProjectIds =
            deviceEntities.stream().map { obj: DeviceEntity -> obj.projectId }
                .collect(Collectors.toSet())
        val meterProjectIds =
            ammeterEntities.stream()
                .map { obj: AmmeterEntity -> obj.projectId }
                .collect(Collectors.toSet())
        projectList.forEach {
            systemGroupsMap[it.id]?.let { groupEntity ->
                it.pcsCode = groupEntity.pcsCode
            }
            it.maintain = (deviceProjectIds.contains(it.id)
                    || meterProjectIds.contains(it.id))
        }
    }

    @ApiOperation("角色权限列表配置查询")
    @PostMapping("/roleAuthorityConfig")
    fun roleAuthorityConfig(@RequestBody @Valid roleAuthorityConfigReq: RoleAuthorityConfigReq): Result<List<RoleAuthorityConfigResult>> {
        return Result.success(accountSystemService.roleAuthorityConfig(roleAuthorityConfigReq))
    }

    @ApiOperation("删除角色")
    @PostMapping("/roleDelete")
    @Log(module = "ROLE", methods = "ROLE_DEL", type = OperationType.DEL_SIMPLE)
    fun roleDelete(@RequestBody @Valid roleDeleteReq: RoleDeleteReq): Result<Unit> {
        accountSystemService.roleDelete(roleDeleteReq)
//        logService.logDeleteDetail(
//            LogInfo.builder()
//                .module("ROLE")
//                .method("ROLE_DEL")
//                .`object`(roleDeleteReq)
//                .delDetail(java.util.Map.of(roleDeleteReq.roleId, roleDeleteReq.roleId))
//                .traceId(java.lang.String.format(LogService.ROLE_DELETE_TRACE_FORMAT, roleDeleteReq.roleId))
//                .build()
//        )
        return Result.success(Unit)
    }

    @ApiOperation("新增角色")
    @PostMapping("/roleCreate")
    @Log(module = "ROLE", methods = "ROLE_ADD", type = OperationType.ADD_SIMPLE)
    fun roleCreate(@RequestBody @Valid roleCreateReq: RoleCreateReq): Result<Unit> {
        accountSystemService.roleCreate(roleCreateReq)
//        logService.logAddDetail(
//            LogInfo.builder()
//                .module("ROLE")
//                .method("ROLE_ADD")
//                .`object`(roleCreateReq)
//                .traceId(
//                    java.lang.String.format(
//                        LogService.ROLE_CREATE_TRACE_FORMAT, roleCreateReq.name
//                    )
//                )
//                .build()
//        )
        return Result.success(Unit)
    }

    @ApiOperation("更新角色")
    @PostMapping("/roleUpdate")
    @Log(module = "ROLE", methods = "ROLE_UPDATE", type = OperationType.UPDATE_SIMPLE)
    fun roleUpdate(@RequestBody @Valid roleUpdateReq: RoleUpdateReq): Result<Unit> {
        accountSystemService.roleUpdate(roleUpdateReq)
//        logService.logAddDetail(
//            LogInfo.builder()
//                .module("ROLE")
//                .method("ROLE_UPDATE")
//                .`object`(roleUpdateReq)
//                .traceId(
//                    java.lang.String.format(
//                        LogService.ROLE_UPDATE_TRACE_FORMAT, roleUpdateReq.roleId
//                    )
//                )
//                .build()
//        )
        return Result.success(accountSystemService.roleUpdate(roleUpdateReq))
    }

    @ApiOperation("解除授权角色")
    @PostMapping("/authorizationRoleCancel")
    @Log(module = "USER_MANAGE", methods = "ROLE_AUTHOR_CANCEL", type = OperationType.UPDATE_SIMPLE)
    fun authorizationRoleCancel(@RequestBody @Valid authorizationRoleCancelReq: AuthorizationRoleCancelReq): Result<Unit> {
        accountSystemService.bingResource(
            BingResourceReq(
                authorizationRoleCancelReq.roleId.toString(),
                emptyList(), authorizationRoleCancelReq.userId.toString(), false
            )
        )
        accountSystemService.authorizationRoleCancel(authorizationRoleCancelReq)
//        logService.logAddDetail(
//            LogInfo.builder()
//                .module("USER_MANAGE")
//                .method("ROLE_AUTHOR_CANCEL")
//                .`object`(authorizationRoleCancelReq)
//                .traceId(
//                    java.lang.String.format(
//                        LogService.ROLE_AUTHOR_CANCEL_TRACE_FORMAT,
//                        authorizationRoleCancelReq.userId,
//                        authorizationRoleCancelReq.roleId
//                    )
//                )
//                .build()
//        )
        return Result.success(Unit)
    }

    @ApiOperation("授权角色")
    @PostMapping("/authorizationRole")
    @Log(module = "USER_MANAGE", methods = "ROLE_AUTHOR", type = OperationType.ADD_SIMPLE)
    fun authorizationRole(@RequestBody @Valid authorizationRoleReq: AuthorizationRoleReq): Result<Unit> {
        accountSystemService.authorizationRole(authorizationRoleReq)
//        logService.logAddDetail(
//            LogInfo.builder()
//                .module("USER_MANAGE")
//                .method("ROLE_AUTHOR")
//                .`object`(authorizationRoleReq)
//                .traceId(
//                    java.lang.String.format(
//                        LogService.ROLE_AUTHOR_TRACE_FORMAT,
//                        authorizationRoleReq.authEmail,
//                        authorizationRoleReq.roleId
//                    )
//                )
//                .build()
//        )
        return Result.success(Unit)
    }

    @ApiOperation("查询用户列表(支持模糊查询)")
    @PostMapping("/userList")
    fun userList(@RequestBody @Valid userListReq: UserListReq): Result<List<AccountUser>> {
        return Result.success(accountSystemService.userList(userListReq))
    }

    @ApiOperation("邮箱校验(登录状态授权用户,校验邮箱是否可授权)")
    @PostMapping("/authorization/emailCheck")
    fun authorizationEmailCheck(@RequestBody @Valid authorizationEmailCheckReq: AuthorizationEmailCheckReq): Result<EmailCheckResult> {
        return Result.success(accountSystemService.authorizationEmailCheck(authorizationEmailCheckReq))
    }

    /**
     * 是否需要注销方法 应该是没有 后悔操作了
     */
    @ApiOperation("注销用户(登录状态下)")
    @PostMapping("/cancelUser")
    @Log(module = "USER_MANAGE", methods = "CANCEL_USER")
    fun cancelUser(
        @RequestBody @Valid cancelUserReq: CancelUserReq,
        @RequestHeader("SessionId", required = false) sessionId: String?
    ): Result<Unit> {
        return Result.success(accountSystemService.cancelUser(cancelUserReq, sessionId))
    }

    @ApiOperation("用户登出(登录状态下)")
    @PostMapping("/logout")
    fun logout(@RequestHeader("SessionId", required = false) sessionId: String?): Result<Unit> {
        return Result.success(accountSystemService.logout(sessionId))
    }

    /**
     * 角色管理分页接口
     */
    @ApiOperation("角色管理分页查询接口")
    @PostMapping("/roleListPage")
    fun roleListPage(@RequestBody @Valid roleListPageReq: RoleListPageReq): Result<RoleListPage> {
        val roleListPage: RoleListPage? = accountSystemService.roleListPage(roleListPageReq)
        return Result.success(roleListPage)
    }

    @ApiOperation("账户管理分页查询接口")
    @PostMapping("/accountListPage")
    fun accountListPage(@RequestBody @Valid accountListPageReq: AccountListPageReq): Result<AccountListPage> {
        log.info { accountListPageReq }
        val accountListPage = accountSystemService.accountListPage(accountListPageReq)
        return Result.success(accountListPage)
    }

    /**
     * 更新用户信息
     * 外部用户才能 更新昵称 用户名
     */
    @ApiOperation("更改用户信息 (外部用户才能更新 , 更新昵称和用户名)")
    @PostMapping("/updateUser")
    fun updateUser(
        @RequestBody updateUserReq: UpdateUserReq, @RequestHeader("SessionId", required = false) sessionId: String?
    ): Result<Unit> {
        return Result.success(accountSystemService.updateUser(updateUserReq, sessionId))
    }

    /**
     * 绑定 手机号
     * 需要登录后
     */
    @ApiOperation("更改绑定手机号 (也属于更新用户信息)")
    @PostMapping("/bindPhone")
    @Log(module = "USER_MANAGE", methods = "BIND_PHONE")
    fun bindPhone(
        @RequestBody bindPhoneReq: BindPhoneReq, @RequestHeader("SessionId", required = false) sessionId: String?
    ): Result<Unit> {
        return Result.success(accountSystemService.bindPhone(bindPhoneReq, sessionId))
    }

    /**
     * 绑定 邮箱
     * 需要登录后
     */
    @ApiOperation("更改绑定邮箱 (也属于更新用户信息)")
    @PostMapping("/bindEmail")
    @Log(module = "USER_MANAGE", methods = "BIND_EMAIL")
    fun bindEmail(
        @RequestBody bindEmailReq: BindEmailReq, @RequestHeader("SessionId", required = false) sessionId: String?
    ): Result<Unit> {
        return Result.success(accountSystemService.bindEmail(bindEmailReq, sessionId))
    }

    @ApiOperation("修改密码(登录状态下)")
    @PostMapping("/changePassword")
    fun changePassword(
        @RequestBody changePasswordReq: ChangePasswordReq,
        @RequestHeader("SessionId", required = false) sessionId: String?
    ): Result<Unit> {
        return Result.success(accountSystemService.changePassword(changePasswordReq, sessionId))
    }

    @ApiOperation("忘记密码")
    @PostMapping("/forgetPassword")
    fun forgetPassword(
        @RequestBody passwordChangeReq: ForgetPasswordReq
    ): Result<Unit> {
        return Result.success(accountSystemService.forgetPassword(passwordChangeReq))
    }

    @ApiOperation("发送email验证码")
    @PostMapping("/emailCode")
    fun emailCode(
        @RequestBody emailCodeReq: EmailCodeReq, @RequestHeader("SessionId", required = false) sessionId: String?
    ): Result<Unit> {
        return Result.success(accountSystemService.emailCode(emailCodeReq, sessionId))
    }

    @ApiOperation("发送手机验证码")
    @PostMapping("/phoneCode")
    fun phoneCode(@RequestBody phoneCodeReq: PhoneCodeReq): Result<Unit> {
        return Result.success(accountSystemService.phoneCode(phoneCodeReq))
    }

    @ApiOperation("滑块图像验证码校验")
    @PostMapping("/captchaCodeCheck")
    fun captchaCodeCheck(@RequestBody captchaCodeCheckReq: CaptchaCodeCheckReq): Result<Unit> {
        accountSystemService.captchaCodeCheck(captchaCodeCheckReq)
        return Result.success()
    }

    @ApiOperation("获取滑块图像验证码")
    @GetMapping("/captchaCode")
    fun captchaCode(): Result<CaptchaCode> {
        return Result.success(accountSystemService.captchaCode())
    }

    @ApiOperation("用户登录")
    @PostMapping("/token")
    fun login(@Validated @RequestBody userDto: UserDto): Result<AccountUser?> {
        //调用账号系统的登录接口
        return Result.success(accountSystemService.login(userDto))
    }

    @ApiOperation("用户认证", notes = "header need SessionId, /oauth/account/token 接口返回的")
    @PostMapping("/authentication")
    fun authentication(
        @RequestHeader("SessionId", required = true) sessionId: String,
        @RequestBody authenticationReq: AccountAuthenticationReq
    ): Result<Map<String, Any>> {
        //认证接口
        //返回我们的access_token jwt的
        return accountSystemService.authentication(authenticationReq)?.let { accountUser ->
            log.debug { "账号系统认证  roleId:${authenticationReq.roleId}  结果: $accountUser" }
            // 得到SpringSecurity需要的权限列表
            val securityAuthoritySets = linkedSetOf<String>()
            accountUser.currentRole!!.policyAllowed?.forEach {
                accountAuthUtils.addAuthorityUrls(it, securityAuthoritySets)
            }
            val currentProjects = projectServiceKt.getCurrentProjectList(authenticationReq.roleId, sessionId)
            val projectList = currentProjects?.keys?.takeIf { it.isNotEmpty() }?.let { keys ->
                projectService.lambdaQuery().`in`(ProjectEntity::getId, keys).list().onEach { project ->
                    project.projectName = currentProjects[project.id]
                }
            } ?: emptyList()

            Result.success(mapOf(AUTHORITY to accountUser.currentRole!!.policyAllowed!!,
                //project 还没查询处理
                                 PROJECT to projectList, ACCESS_TOKEN to accountUser.run {
                    // 生成jwt
                    jwtHelper.generateUserTokenNew(
                        JwtUserInfo.builder().userId(id).userName(name).roleId(currentRole!!.roleId)
                            .accountSystemSessionId(sessionId).build(),
                        name!!.contains(EmsConstants.ADMIN_USERNAME),
                        //把 账号系统的token过期时间设置到jwt上保持一致 只好
                        token?.expiredAt
                    )
                }, INFO to UserEntity().also { userEntity ->
                    val duration = Duration.between(
                        Instant.now(), Instant.ofEpochSecond(accountUser.token?.expiredAt!!.toLong())
                    )
                    //把账号系统用户信息 转换成 UserEntity对象
                    accountAuthUtils.transformAccountUserToUserEntity(
                        accountUser, userEntity, securityAuthoritySets
                    )
                    //用户存入redis
                    redisTemplate.opsForValue().set(
                        "$REDIS_USER_PREFIX${userEntity.id}",
                        JSON.toJSONString(
                            AuthUser(
                                userEntity,
                                sessionId,
                                securityAuthoritySets,
                            )
                        ),
                        duration.seconds, TimeUnit.SECONDS,
                    )
                })
            )
        }!!
    }

    @ApiOperation("绑定项目资源")
    @PostMapping("/bingResource")
    @Log(module = "OAUTH", methods = "MANAGE_PROJECT_ASSIGN", type = OperationType.ADD_SIMPLE)
    fun bingResource(@Validated @RequestBody bingResourceReq: BingResourceReq): Result<Unit> {
        if (SecurityUtil.getAuthStat() == null || SecurityUtil.getAuthStat() != 1) {
            throw ServiceException(ErrorResultCode.ILLEGAL_ACCESS.value());
        }
        //调用账号系统的登录接口
        return Result.success(accountSystemService.bingResource(bingResourceReq))
    }

}
