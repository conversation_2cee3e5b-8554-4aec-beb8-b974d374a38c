package com.wifochina.modules.oauth.filter

import com.alibaba.fastjson2.JSON
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.servlet.HttpServletRequestWrapper
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.oauth.AuthUser
import com.wifochina.modules.oauth.IAccountSystemAuthenticationFilter
import com.wifochina.modules.oauth.config.MySecurityConfig
import com.wifochina.modules.oauth.constants.AuthConstants.ACCOUNT_SYSTEM_REQ_REDIS_SESSION_ID_PREFIX
import com.wifochina.modules.oauth.constants.AuthConstants.ACCOUNT_SYSTEM_ROLE_ID
import com.wifochina.modules.oauth.constants.AuthConstants.ACCOUNT_SYSTEM_SESSION_ID_HEADER
import com.wifochina.modules.oauth.constants.AuthConstants.SESSION_ID
import com.wifochina.modules.oauth.req.AccountAuthenticationReq
import com.wifochina.modules.oauth.service.AccountSystemService
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.oauth.utils.AccountAuthUtils
import com.wifochina.modules.user.entity.UserEntity
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import org.springframework.web.filter.OncePerRequestFilter
import java.util.concurrent.TimeUnit
import javax.servlet.FilterChain
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * Created on 2024/11/20 16:13.
 * <AUTHOR>
 */
@Component
class AccountSystemSessionIdAuthenticationFilter(
    val accountSystemService: AccountSystemService,
    val accountAuthUtils: AccountAuthUtils,
    val redisTemplate: RedisTemplate<String, String>
) : OncePerRequestFilter(), IAccountSystemAuthenticationFilter {
    override fun doFilterInternal(
        request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain
    ) {
        //判断是否是账号系统过来的请求
        val accountSystemSessionId: String? = request.getHeader(ACCOUNT_SYSTEM_SESSION_ID_HEADER)
        val accountSystemRoleId: String? = request.getHeader(ACCOUNT_SYSTEM_ROLE_ID)
        if (accountSystemSessionId == null || accountSystemRoleId == null) {
            filterChain.doFilter(request, response)
            return
        }
        if (MySecurityConfig.NO_AUTH_URLS.contains(request.requestURI.substring(4))) {
            filterChain.doFilter(request, response)
            return
        }
        request.getHeader(EmsConstants.PROJECT_ID_O)?.apply {
            WebUtils.projectId.set(this)
        }
        //表示是账号系统的请求
        //1.先判断缓存里面是否有 这个sessionId 对应的 我们平台的 jwt token
        JSON.parseObject(
            redisTemplate.opsForValue().get("$ACCOUNT_SYSTEM_REQ_REDIS_SESSION_ID_PREFIX${accountSystemSessionId}"),
            AuthUser::class.java
        )?.run {
            //如果有 则
            val usernamePasswordAuthenticationToken = UsernamePasswordAuthenticationToken(
                this, null, this.authorities
            )
            SecurityContextHolder.getContext().authentication = usernamePasswordAuthenticationToken
            filterChain.doFilter(request, response)
        } ?: run {
            //如果没有找到.. 则去认证一下
            val requestWrapper = HttpServletRequestWrapper(request)
            //authentication 里面 解析的是我们平台来的 头叫  SessionId 虽然和 账号系统的一个意思 但是分开
            requestWrapper.addHeader(SESSION_ID, accountSystemSessionId)
            RequestContextHolder.setRequestAttributes(ServletRequestAttributes(requestWrapper))
            //需要我们去进行认证
            accountSystemService.authentication(
                AccountAuthenticationReq(
                    accountSystemRoleId
                )
            )?.also { accountUser ->
                val authoritySets = linkedSetOf<String>()
                accountUser.currentRole!!.policyAllowed?.forEach {
                    accountAuthUtils.addAuthorityUrls(it, authoritySets)
                }
                UserEntity().also { userEntity ->
                    //把账号系统用户信息 转换成 UserEntity对象
                    accountAuthUtils.transformAccountUserToUserEntity(accountUser, userEntity, authoritySets)
                    val authUser = AuthUser(
                        userEntity, accountSystemSessionId, authoritySets
                    )
                    val usernamePasswordAuthenticationToken =
                        UsernamePasswordAuthenticationToken(authUser, null, authoritySets.map {
                            SimpleGrantedAuthority(
                                it
                            )
                        })
                    //用户存入redis
                    redisTemplate.opsForValue().set(
                        "$ACCOUNT_SYSTEM_REQ_REDIS_SESSION_ID_PREFIX${accountSystemSessionId}", JSON.toJSONString(
                            authUser
                        ), 3, TimeUnit.MINUTES
                    )
                    SecurityContextHolder.getContext().authentication = usernamePasswordAuthenticationToken
                    filterChain.doFilter(request, response)
                }
            } ?: throw ServiceException("")
        }

    }
}