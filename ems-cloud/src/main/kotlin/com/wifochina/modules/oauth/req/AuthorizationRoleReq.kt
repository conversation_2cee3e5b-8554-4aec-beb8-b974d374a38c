package com.wifochina.modules.oauth.req

import com.wifochina.common.log.LogFieldIgnore
import javax.validation.constraints.NotNull

/**
 * Created on 2024/11/19 09:39.
 * <AUTHOR>
 */
data class AuthorizationRoleReq(
    @field:NotNull val authEmail: String?,
    @field:NotNull val roleId: String?,
    @field:NotNull val parentRoleId: String?,
    @field:NotNull val parentUserId: String?,
    @field:NotNull val creatorRoleId: String?,
    @field:NotNull @LogFieldIgnore val password: String?,
    val passwordType: Int = 0,
    val expireAt: Long?,
    val nickname: String?,
    val countryOrRegionCode: Long?,
    val username: String?
)