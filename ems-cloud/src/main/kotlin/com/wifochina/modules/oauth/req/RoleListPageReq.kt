package com.wifochina.modules.oauth.req

import com.wifochina.common.page.PageBean
import javax.validation.constraints.NotNull

/**
 * Created on 2024/11/12 11:47.
 * <AUTHOR>
 */

data class RoleListPageReq(
    @field:NotNull val loginRoleId: String?,
    @field:NotNull val roleId: String?,
    //要把Int变成Int?  变成可空类型 这样才会触发 NotNull校验, 否则 Int 是非空类型默认是0
    @field:NotNull val userId: String?,
    val category: String?,
    val roleName: String?,
) : PageBean()