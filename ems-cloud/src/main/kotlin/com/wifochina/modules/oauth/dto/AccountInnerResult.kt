package com.wifochina.modules.oauth.dto


data class AccountInnerResult(
    val count: Int,
    val hasMore: Boolean,
    val data: List<User>
)

data class User(
    val id: String,
    val panguId: String,
    val type: Int,
    val isManager: Int,
    val name: String,
    val nickName: String,
    val email: String,
    val phone: String?,
    val phoneLocationCode: String?,
    val countryOrRegionCode: Int,
    val countryOrRegionName: String?,
    val region: String?,
    val address: String?,
    val lastSignInTime: Long,
    val status: Int,
    val source: Int,
    val ct: Long
)

