package com.wifochina.modules.oauth.utils

import cn.hutool.core.codec.Base64Encoder
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.crypto.digest.DigestUtil
import com.wifochina.common.config.SystemConfiguration
import com.wifochina.common.util.EmsConstants
import com.wifochina.common.utils.dataClassToMap
import com.wifochina.modules.oauth.AccountUser
import com.wifochina.modules.oauth.AuthUser
import com.wifochina.modules.oauth.PolicyAllowObj
import com.wifochina.modules.oauth.constants.AuthConstants.APP_ID
import com.wifochina.modules.oauth.constants.AuthConstants.MANAGE_AUTHORITY_FLAG
import com.wifochina.modules.oauth.constants.AuthConstants.PageConstants.LIMIT
import com.wifochina.modules.oauth.constants.AuthConstants.PageConstants.OFFSET
import com.wifochina.modules.user.entity.UserEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

/**
 * Created on 2024/11/5 14:34.
 * <AUTHOR>
 */

@Component
class AccountAuthUtils(
    val systemConfiguration: SystemConfiguration
) {

    fun getSessionId(): String {
        val authUser = SecurityContextHolder.getContext().authentication.principal as AuthUser
        return authUser.accountSystemSessionId
    }

    fun getAuthUser(): AuthUser? {
        return SecurityContextHolder.getContext().authentication?.let {
            it.principal.takeIf { authUser -> authUser is AuthUser } as? AuthUser
        }
    }

    fun getAccountAuthHeader(): MutableMap<String, String> {
        val curTime = DateUtil.currentSeconds()
        val authStr = systemConfiguration.accountSystem.appId + curTime + systemConfiguration.accountSystem.salt
        val shaStr = DigestUtil.sha256Hex(authStr)
        val token = Base64Encoder.encode(
            String.format("%s:%d:%s", 2, curTime, shaStr.substring(10, 20))
        )
        var language: String? = "en_US"
        val requestAttributes = RequestContextHolder.getRequestAttributes()
        if (requestAttributes != null) {
            val request = (requestAttributes as ServletRequestAttributes).request
            //兼容 app端 language 使用的是accept-language
            language = request.getHeader("Accept-Language")
            if (StrUtil.isBlank(language) || language.split("_".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray().size != 2) {
                //都找不到就使用 en_US
                language = "en_US"
            }
        }
        return mutableMapOf(
            "W-Sign" to token, "Language" to (language ?: "")
        )
    }

    fun addAuthorityUrls(it: PolicyAllowObj, authoritySets: MutableSet<String>) {
        // 处理当前的 URL mark
        it.urlMarks.let { urlMarks ->
            if (urlMarks.isNotBlank()) {
                // 检查 URL 是否以 '/' 开头，如果没有，则加上 '/'
                val formattedUrl = if (urlMarks.startsWith("/")) {
                    urlMarks
                } else {
                    "/$urlMarks"
                }
                // 如果 URL 中包含 ":"，则替换为 "/"
                if (formattedUrl.contains(":")) {
                    authoritySets.add(formattedUrl.replace(":", "/"))
                } else {
                    authoritySets.add(formattedUrl)
                }
            }
            // 如果有子元素，则递归处理子元素
            it.children?.forEach { children ->
                // 处理子元素的 URL
                val childrenUrl = if (children.urlMarks.startsWith("/")) {
                    children.urlMarks
                } else {
                    "/${children.urlMarks}"
                }
                if (childrenUrl != "/") {
                    // 如果子元素 URL 中包含 ":"，则替换为 "/"
                    authoritySets.add(childrenUrl.replace(":", "/"))
                }
                // 递归调用
                addAuthorityUrls(children, authoritySets)
            }
        }
    }

    fun transformAccountUserToUserEntity(
        accountUser: AccountUser, userEntity: UserEntity, authoritySets: MutableSet<String>
    ) {
        accountUser.apply {
            userEntity.id = id
            userEntity.email = email
            userEntity.userName = name
            userEntity.name = nickName
            userEntity.password = null
            //记录用户是否内外来源
            userEntity.role = source.toString()
            userEntity.roleId = currentRole?.roleId
            userEntity.roleName = currentRole?.roleName
            userEntity.isDelete = false
            //包含这个权限标识表示有 管理端权限
            userEntity.visitManage = authoritySets.contains(MANAGE_AUTHORITY_FLAG)
            userEntity.phone = phone
            userEntity.authStat = accountUser.currentRole?.authStat
            userEntity.countryOrRegionName = accountUser.countryOrRegionName
            userEntity.type = type
            userEntity.allProject =  accountUser.currentRole?.allResourceStat == 2
        }
    }

    fun <T> transformMap(t: T): MutableMap<String, Any> {
        val transformClassToMap = mutableMapOf<String, Any>(APP_ID to systemConfiguration.accountSystem.appId)
        var pageNum: Int? = null
        var pageSize: Int? = null
        t!!.dataClassToMap().forEach {
            //把 null的过滤了
            it.value?.run {
                when (it.key) {
                    //把系统的pageNum 和 pageSize 转换成 账号系统对应的 offset 和 limit
//                    EmsConstants.PAGE_NUM -> transformClassToMap[OFFSET] = it.value!!
//                    EmsConstants.PAGE_SIZE -> transformClassToMap[LIMIT] = it.value!!
                    EmsConstants.PAGE_NUM -> pageNum = (it.value as? Int)
                    EmsConstants.PAGE_SIZE -> pageSize = (it.value as? Int)
                    else -> transformClassToMap[it.key] = it.value!!
                }
            }
        }
        // 如果 pageNum 和 pageSize 都存在，则计算 offset
        if (pageNum != null && pageSize != null) {
            transformClassToMap[OFFSET] = (pageNum!! - 1) * pageSize!!
            transformClassToMap[LIMIT] = pageSize!!
        }
        return transformClassToMap
    }

}