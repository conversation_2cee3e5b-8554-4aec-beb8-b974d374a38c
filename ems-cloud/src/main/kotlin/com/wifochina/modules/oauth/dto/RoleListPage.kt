package com.wifochina.modules.oauth.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * Created on 2024/11/12 16:04.
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class RoleListPage(
    val count: Int, val `data`: List<RoleData>?
)

data class RoleData(
    val applicationId: String,
    val authStat: Int,
    val category: Int,
    val creatorRoleId: String,
    val creatorUserId: String,
    val parentRoleId: String,
    val ct: Long,
    val i118nId: Long,
    val isManager: Int,
    val level: Int,
    val parentUserId: String,
    val roleId: String,
    val roleName: String,
    val type: Int,
    val ut: Long
)