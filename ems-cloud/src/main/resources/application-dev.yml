spring:
  #need prevent druid filter
  #driver-class-name : com.p6spy.engine.spy.P6SpyDriver
  #url: jdbc:p6spy:${DATABASE_HOST:mysql://nginx.dev.weiheng-tech.com:30175}/${DATABASE_NAME:cloud}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useLegacyDatetimeCode=false&serverTimezone=UTC
  redis:
    host: ${REDIS_HOST:nginx.dev.weiheng-tech.com}
    port: ${REDIS_PORT:31756}
    #password: ${REDIS_PASSWORD:wq2018!}
    database: ${REDIS_DATABASE:8}
    timeout: 10000
    jedis:
      pool:
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 1
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  # 定时任务线程池大小
  task:
    project:
      fixedDelay: ${TASK_PROJECT_COLLECT:60000}
    scheduling:
      pool:
        size: 20
      thread-name-prefix: task-schedule
  flyway:
    baseline-version: 20250228110636 # 基线版本指定为1.4.1 版本, 因为1.4.2 cloud 版本才准备把flyway 打开
    enabled: false

ems:
  cloud: true
  gateway: ${GATEWAY_URL:http://nginx.dev.weiheng-tech.com:32308}
  adUrl: ${AD_URL:http://ldap-login.dev.weiheng-tech.com}/auth/ldap
  version: ${EMS_VERSION:v2.1.6}
  collect:
    data:
      url: ${DATA_URL:http://{ip}:{port}/api/modbus?start={start}&count={count}&type={type}}
      #一次采集数据最大值，默认10000
      count: 65535
    project:
      id: ${PROJECT_ID:4f537620d37d40e19dd25be5ca6ad941}

  influx:
    #influxdb服务器的地址
    url: ${TSDB_URL:http://influx-proxy.dev.weiheng-tech.com}
    token: ${TSDB_TOKEN:8ydw+mbBxkpzsrxy}
    org: weiheng
    bucket:
      realtime: ${BUCKET_REALTIME:ems_cloud_dev}
      forever: ${BUCKET_FOREVER:ems_forever_dev}
      mean: ${BUCKET_MEAN:ems_mean_dev}
      demand: ${BUCKET_DEMAND:ems_forever_dev}
      group: ${BUCKET_GROUP:ems_group_fvcc_dev}