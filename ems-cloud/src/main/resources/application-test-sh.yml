spring:
  datasource:
    url: jdbc:${DATABASE_HOST:mysql://***************:20089}/${DATABASE_NAME:pangu}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useLegacyDatetimeCode=false&serverTimezone=UTC
    username: ${DATABASE_USERNAME:pangu}
    password: ${DATABASE_PASSWORD:PANGU@007}
    type: com.alibaba.druid.pool.DruidDataSource
    #driver-class-name : com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: ${REDIS_HOST:nginx.dev.weiheng-tech.com}
    port: ${REDIS_PORT:31756}
    #password: ${REDIS_PASSWORD:wq2018!}
    database: ${REDIS_DATABASE:8}
    timeout: 10000
    jedis:
      pool:
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 1
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  # 定时任务线程池大小
  task:
    project:
      fixedDelay: ${TASK_PROJECT_COLLECT:60000}
    scheduling:
      pool:
        size: 20
      thread-name-prefix: task-schedule

ems:
  cloud: true
  gateway: ${GATEWAY_URL:http://pangu-cloud-gateway.weiheng-tech.com}
  adUrl: ${AD_URL:http://ldap-login.dev.weiheng-tech.com}/auth/ldap
  version: ${EMS_VERSION:v2.1.6}
  collect:
    data:
      url: ${DATA_URL:http://{ip}:{port}/api/modbus?start={start}&count={count}&type={type}}
      #一次采集数据最大值，默认10000
      count: 65535
    project:
      id: ${PROJECT_ID:4f537620d37d40e19dd25be5ca6ad941}

  influx:
    #influxdb服务器的地址
    url: ${TSDB_URL:http://influx-proxy-sh.prod.weiheng-tech.com}
    token: ${TSDB_TOKEN:8ydw+mbBxkpzsrxy}
    org: weiheng
    bucket:
      realtime: ${BUCKET_REALTIME:ems_cloud}
      forever: ${BUCKET_FOREVER:ems_forever}
      mean: ${BUCKET_MEAN:ems_mean}
      demand: ${BUCKET_DEMAND:ems_forever}
      group: ${BUCKET_GROUP:ems_group_fvcc}