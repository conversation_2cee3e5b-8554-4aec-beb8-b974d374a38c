{"properties": [{"name": "email.from", "type": "java.lang.String", "description": "Description for email.from."}, {"name": "lindorm.database", "type": "java.lang.String", "description": "Description for lindorm.database."}, {"name": "lindorm.url", "type": "java.lang.String", "description": "Description for lindorm.url."}, {"name": "ems.gateway", "type": "java.lang.String", "description": "Description for ems.gateway."}, {"name": "ems.adUrl", "type": "java.lang.String", "description": "Description for ems.adUrl."}, {"name": "ems.version", "type": "java.lang.String", "description": "Description for ems.version."}, {"name": "ems.collect.data.url", "type": "java.lang.String", "description": "Description for ems.collect.data.url."}, {"name": "ems.collect.data.count", "type": "java.lang.String", "description": "Description for ems.collect.data.count."}, {"name": "ems.collect.project.id", "type": "java.lang.String", "description": "Description for ems.collect.project.id."}, {"name": "spring.task.project.fixedDelay", "type": "java.lang.String", "description": "Description for spring.task.project.fixedDelay."}, {"name": "email.to", "type": "java.lang.String", "description": "Description for email.to."}, {"name": "xxl.job.admin.addresses", "type": "java.lang.String", "description": "Description for xxl.job.admin.addresses."}, {"name": "xxl.job.accessToken", "type": "java.lang.String", "description": "Description for xxl.job.accessToken."}, {"name": "xxl.job.executor.app name", "type": "java.lang.String", "description": "Description for xxl.job.executor.app name."}, {"name": "xxl.job.executor.address", "type": "java.lang.String", "description": "Description for xxl.job.executor.address."}, {"name": "xxl.job.executor.ip", "type": "java.lang.String", "description": "Description for xxl.job.executor.ip."}, {"name": "xxl.job.executor.port", "type": "java.lang.String", "description": "Description for xxl.job.executor.port."}, {"name": "xxl.job.executor.logpath", "type": "java.lang.String", "description": "Description for xxl.job.executor.logpath."}, {"name": "xxl.job.executor.logretentiondays", "type": "java.lang.String", "description": "Description for xxl.job.executor.logretentiondays."}, {"name": "limit.key", "type": "java.lang.String", "description": "Description for limit.key."}, {"name": "limit.time", "type": "java.lang.String", "description": "Description for limit.time."}, {"name": "limit.count", "type": "java.lang.String", "description": "Description for limit.count."}, {"name": "limit.path", "type": "java.lang.String", "description": "Description for limit.path."}, {"name": "authentication.jwt.priKey", "type": "java.lang.String", "description": "Description for authentication.jwt.priKey."}, {"name": "authentication.jwt.pubKey", "type": "java.lang.String", "description": "Description for authentication.jwt.pubKey."}, {"name": "authentication.jwt.expire", "type": "java.lang.String", "description": "Description for authentication.jwt.expire."}, {"name": "local.refresh", "type": "java.lang.String", "description": "Description for local.refresh."}, {"name": "local.forward", "type": "java.lang.String", "description": "Description for local.forward."}, {"name": "carbon.load.emission", "type": "java.lang.String", "description": "Description for carbon.load.emission."}, {"name": "carbon.pv.reduction", "type": "java.lang.String", "description": "Description for carbon.pv.reduction."}, {"name": "carbon.pv.power", "type": "java.lang.String", "description": "Description for carbon.pv.power."}, {"name": "carbon.ems.reduction", "type": "java.lang.String", "description": "Description for carbon.ems.reduction."}, {"name": "carbon.ems.power", "type": "java.lang.String", "description": "Description for carbon.ems.power."}, {"name": "qweather.official-key", "type": "java.lang.String", "description": "Description for qweather.official-key."}, {"name": "user.regret", "type": "java.lang.String", "description": "Description for user.regret."}, {"name": "graphic.verifyExpire", "type": "java.lang.String", "description": "Description for graphic.verifyExpire."}, {"name": "sms.codeLength", "type": "java.lang.String", "description": "Description for sms.codeLength."}, {"name": "sms.loginExpire", "type": "java.lang.String", "description": "Description for sms.loginExpire."}, {"name": "sms.registerId", "type": "java.lang.String", "description": "Description for sms.registerId."}, {"name": "sms.verifyId", "type": "java.lang.String", "description": "Description for sms.verifyId."}, {"name": "sms.loginId", "type": "java.lang.String", "description": "Description for sms.loginId."}, {"name": "sms.signName", "type": "java.lang.String", "description": "Description for sms.signName."}, {"name": "sms.accessKeySecret", "type": "java.lang.String", "description": "Description for sms.accessKeySecret."}, {"name": "sms.accessKeyId", "type": "java.lang.String", "description": "Description for sms.accessKeyId."}, {"name": "sms.type", "type": "java.lang.String", "description": "Description for sms.type."}]}