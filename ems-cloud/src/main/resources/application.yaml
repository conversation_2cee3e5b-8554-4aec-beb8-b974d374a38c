spring:
  application:
    name: ems-api
  cloud:
    nacos:
      config:
        # server-addr: ${CONFIG_ADDR:nacos-svc.ems-cloud.svc.cluster.local:8848}
        # server-addr: ${CONFIG_ADDR:192.168.130.229:30848}
        server-addr: ${NACOS_CONFIG_ADDR:192.168.130.228:30225}
        file-extension: yaml
        username: 'nacos'
        password: 'nacos'
        group: ${spring.application.name}
        #namespace: 1c2455dd-f89a-4814-b5ad-64186c525d78
        namespace: ${spring.profiles.active}
        refresh-enabled: true
      discovery:
        username: 'nacos'
        password: 'nacos'
        #server-addr: ${DISCOVERY_ADDR:nacos-svc.ems-cloud.svc.cluster.local:8848}
        #server-addr: ${DISCOVERY_ADDR:192.168.130.229:30848}
        server-addr: ${NACOS_CONFIG_ADDR:192.168.130.228:30225}
        #        namespace: 1c2455dd-f89a-4814-b5ad-64186c525d78
        namespace: ${spring.profiles.active}
        group: ${spring.application.name}
  config:
    import:
      - nacos:${spring.application.name}-common-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
      - nacos:${spring.application.name}-datasource-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
      - nacos:${spring.application.name}-systemconfig-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
      - nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

  profiles:
    active: ${PROFILES_ACTIVE:dev}


logging:
  level:
    com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader: DEBUG
    com.alibaba.nacos.client.config.impl: DEBUG
    com.alibaba.nacos.client.naming: DEBUG
    com.alibaba.nacos.client: DEBUG
    org.springframework.cloud.alibaba.nacos: DEBUG
    org.springframework.cloud.alibaba.nacos.discovery: DEBUG # 开启 Nacos 服务注册的详细日志
    org.springframework.cloud.client.serviceregistry: DEBUG  # 查看 Spring Cloud 服务注册的详细日志
    org.springframework.cloud.nacos.discovery: DEBUG # 确保 Spring Cloud 的 Nacos 服务发现也开启 DEBUG

feign:
  client:
    config:
      alter-manager:
        read-timeout: 10000