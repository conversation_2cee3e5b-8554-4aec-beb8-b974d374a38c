### 文件上传接口测试

### 1. 上传图片文件
POST http://localhost:8808/upload/image
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test-image.jpg"
Content-Type: image/jpeg

< ./test-image.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 2. 查询项目文件列表
GET http://localhost:8080/upload/files

### 响应示例

# 上传成功响应
{
  "success": true,
  "code": "200",
  "message": "success",
  "data": {
    "filePath": "https://your-oss-domain.com/project/projectId/images/uuid.jpg",
    "fileName": "test-image.jpg",
    "fileSize": 102400,
    "uploadTime": 1692345678901
  }
}

# 文件列表响应
{
  "success": true,
  "code": "200",
  "message": "success",
  "data": [
    {
      "id": "file-uuid-1",
      "filePath": "https://your-oss-domain.com/project/projectId/images/uuid1.jpg",
      "fileName": "image1.jpg",
      "fileSize": 102400,
      "fileType": "image/jpeg",
      "fileExtension": ".jpg",
      "createTime": 1692345678901
    },
    {
      "id": "file-uuid-2",
      "filePath": "https://your-oss-domain.com/project/projectId/images/uuid2.png",
      "fileName": "image2.png",
      "fileSize": 204800,
      "fileType": "image/png",
      "fileExtension": ".png",
      "createTime": 1692345678902
    }
  ]
}

# 错误响应
{
  "success": false,
  "code": "400",
  "message": "项目ID不能为空",
  "data": null
}
