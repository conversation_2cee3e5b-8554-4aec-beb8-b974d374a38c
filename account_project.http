###
POST http://account-api.dev.weiheng-tech.com/api/v1/login
Content-Type: application/json
W-Sign: ********************************
Language: en_US

{
  "type": 1,
  "account": "<EMAIL>",
  "password": "admin123"
}

###

### 查询资源
###
GET http://account-api.dev.weiheng-tech.com/api/v1/user/resource/list?dataCenter=3&appId=2&loginRoleId=101
#Content-Type: application/json
Authorization: 6e048338336547b4578c49ac4b475616
Language: en_US

###

### 创建资源
###
POST http://account-api.dev.weiheng-tech.com/api/v1/resource
Content-Type: application/json
Authorization: 6e048338336547b4578c49ac4b475616
Language: en_US

{
  "appId": "2",
  "source": 3,
  "sourceType": 2,
  "type": 4,
  "code": "1605f456bb6743699cfe3982e0d47ca3",
  "id": "1605f456bb6743699cfe3982e0d47ca3",
  "name": "FCAS测试",
  "dataCenter": 3,
  "installArea": 226,
  "roleId": "101"
}

###

### 更新资源
###
PUT http://account-api.dev.weiheng-tech.com/api/v1/resource/1605f456bb6743699cfe3982e0d47ca3
Content-Type: application/json
Authorization: fc22c23acfe35b8a1a6f8c018226a5d5
Language: en_US

{
  "installArea": 226,
  "name": "FCAS"
}

###
### 查询所有用户
###
POST http://account-api.dev.weiheng-tech.com/api/v1/internal/user/_search
Content-Type: application/json
W-Sign: ********************************
Language: en_US

{
  "username": "xu.yuan"
}

###
###
### 查询所有用户
###
POST http://account-api.dev.weiheng-tech.com/api/v1/internal/user/_search
Content-Type: application/json
W-Sign: ********************************
Language: en_US

{
  "userIds": ["1861685209062182912","1679404356468084736"]
}

